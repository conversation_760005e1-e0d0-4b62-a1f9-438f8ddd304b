/**
 * PostgreSQL Data Access Layer for Transactions
 */
const { Pool } = require('pg');
const config = require('../config/postgres');
const assetsDb = require('./assets');

// Get the PostgreSQL pool from the config
const pool = config.getPool();

/**
 * Get all transactions for a user
 * @param {string} userId - The user ID
 * @param {Object} options - Query options (limit, offset, sort)
 * @returns {Promise<Array>} - Array of transactions
 */
async function getUserTransactions(userId, options = {}) {
  const { limit = 10, offset = 0, sort = 'desc' } = options;
  
  const query = `
    SELECT * FROM transactions 
    WHERE user_id = $1 
    ORDER BY transaction_date ${sort === 'asc' ? 'ASC' : 'DESC'}
    LIMIT $2 OFFSET $3
  `;
  
  try {
    const result = await pool.query(query, [userId, limit, offset]);
    return result.rows;
  } catch (error) {
    console.error('Error fetching user transactions:', error);
    throw error;
  }
}

/**
 * Get a specific transaction
 * @param {string} transactionId - The transaction ID
 * @returns {Promise<Object>} - The transaction
 */
async function getTransaction(transactionId) {
  const query = `
    SELECT * FROM transactions 
    WHERE id = $1
  `;
  
  try {
    const result = await pool.query(query, [transactionId]);
    return result.rows[0];
  } catch (error) {
    console.error('Error fetching transaction:', error);
    throw error;
  }
}

/**
 * Create a new transaction
 * @param {Object} transaction - The transaction object
 * @param {boolean} updateAsset - Whether to update the asset balance
 * @returns {Promise<Object>} - The created transaction
 */
async function createTransaction(transaction, updateAsset = true) {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    const { user_id, type, asset, amount, value, fee = 0, status = 'completed' } = transaction;
    
    const query = `
      INSERT INTO transactions 
        (user_id, type, asset, amount, value, fee, status, transaction_date)
      VALUES 
        ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
      RETURNING *
    `;
    
    const result = await client.query(query, [
      user_id, type, asset, amount, value, fee, status
    ]);
    
    // Update asset balance if needed
    if (updateAsset && status === 'completed') {
      // For buy/deposit, add to balance; for sell/withdrawal, subtract
      const balanceChange = ['buy', 'deposit'].includes(type) ? amount : -amount;
      
      await assetsDb.updateAssetBalance(user_id, asset, balanceChange);
    }
    
    await client.query('COMMIT');
    return result.rows[0];
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error creating transaction:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Update a transaction's status
 * @param {string} transactionId - The transaction ID
 * @param {string} status - The new status
 * @param {boolean} updateAsset - Whether to update the asset balance
 * @returns {Promise<Object>} - The updated transaction
 */
async function updateTransactionStatus(transactionId, status, updateAsset = true) {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    // First get the current transaction
    const getQuery = `
      SELECT * FROM transactions 
      WHERE id = $1
      FOR UPDATE
    `;
    
    const currentTx = await client.query(getQuery, [transactionId]);
    
    if (currentTx.rows.length === 0) {
      throw new Error(`Transaction with ID ${transactionId} not found`);
    }
    
    const transaction = currentTx.rows[0];
    
    // Update the transaction status
    const updateQuery = `
      UPDATE transactions 
      SET status = $2,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `;
    
    const result = await client.query(updateQuery, [transactionId, status]);
    
    // Update asset balance if needed
    if (updateAsset && 
        transaction.status !== 'completed' && 
        status === 'completed') {
      // For buy/deposit, add to balance; for sell/withdrawal, subtract
      const balanceChange = ['buy', 'deposit'].includes(transaction.type) 
        ? transaction.amount 
        : -transaction.amount;
      
      await assetsDb.updateAssetBalance(
        transaction.user_id, 
        transaction.asset, 
        balanceChange
      );
    }
    
    // If transaction was completed but now cancelled/failed, reverse the balance change
    if (updateAsset && 
        transaction.status === 'completed' && 
        ['cancelled', 'failed'].includes(status)) {
      // Reverse the previous balance change
      const balanceChange = ['buy', 'deposit'].includes(transaction.type) 
        ? -transaction.amount 
        : transaction.amount;
      
      await assetsDb.updateAssetBalance(
        transaction.user_id, 
        transaction.asset, 
        balanceChange
      );
    }
    
    await client.query('COMMIT');
    return result.rows[0];
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error updating transaction status:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get transaction count for a user
 * @param {string} userId - The user ID
 * @returns {Promise<number>} - The transaction count
 */
async function getTransactionCount(userId) {
  const query = `
    SELECT COUNT(*) as count FROM transactions 
    WHERE user_id = $1
  `;
  
  try {
    const result = await pool.query(query, [userId]);
    return parseInt(result.rows[0].count, 10);
  } catch (error) {
    console.error('Error counting transactions:', error);
    throw error;
  }
}

/**
 * Get transaction statistics for a user
 * @param {string} userId - The user ID
 * @returns {Promise<Object>} - Transaction statistics
 */
async function getTransactionStats(userId) {
  const query = `
    SELECT 
      COUNT(*) as total_count,
      SUM(CASE WHEN type = 'buy' THEN 1 ELSE 0 END) as buy_count,
      SUM(CASE WHEN type = 'sell' THEN 1 ELSE 0 END) as sell_count,
      SUM(CASE WHEN type = 'swap' THEN 1 ELSE 0 END) as swap_count,
      SUM(CASE WHEN type = 'deposit' THEN 1 ELSE 0 END) as deposit_count,
      SUM(CASE WHEN type = 'withdrawal' THEN 1 ELSE 0 END) as withdrawal_count,
      SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
      SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
      SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count,
      SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_count,
      SUM(CASE WHEN type = 'buy' AND status = 'completed' THEN value ELSE 0 END) as total_buy_value,
      SUM(CASE WHEN type = 'sell' AND status = 'completed' THEN value ELSE 0 END) as total_sell_value,
      SUM(CASE WHEN status = 'completed' THEN fee ELSE 0 END) as total_fees
    FROM transactions 
    WHERE user_id = $1
  `;
  
  try {
    const result = await pool.query(query, [userId]);
    return result.rows[0];
  } catch (error) {
    console.error('Error fetching transaction stats:', error);
    throw error;
  }
}

module.exports = {
  getUserTransactions,
  getTransaction,
  createTransaction,
  updateTransactionStatus,
  getTransactionCount,
  getTransactionStats
};
