/**
 * User Balance Model
 * Tracks user balances in the platform's centralized wallet system
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const UserBalanceSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  // SOL balance
  solBalance: {
    type: Number,
    default: 0,
    min: 0
  },
  // Token balances (key-value pairs where key is token symbol or mint address)
  tokenBalances: {
    type: Map,
    of: Number,
    default: () => new Map()
  },
  // Pending deposits
  pendingDeposits: [{
    txId: String,
    amount: Number,
    token: String,
    timestamp: Date,
    status: {
      type: String,
      enum: ['pending', 'confirmed', 'failed'],
      default: 'pending'
    }
  }],
  // Pending withdrawals
  pendingWithdrawals: [{
    txId: String,
    amount: Number,
    token: String,
    destinationAddress: String,
    timestamp: Date,
    status: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed'],
      default: 'pending'
    }
  }],
  // Transaction history
  transactionHistory: [{
    txId: String,
    type: {
      type: String,
      enum: ['deposit', 'withdrawal', 'trade', 'fee', 'reward', 'adjustment'],
      required: true
    },
    token: String,
    amount: Number,
    fee: Number,
    timestamp: {
      type: Date,
      default: Date.now
    },
    description: String,
    relatedTxId: String
  }],
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Create indexes for efficient queries
UserBalanceSchema.index({ userId: 1 });
UserBalanceSchema.index({ 'pendingDeposits.txId': 1 });
UserBalanceSchema.index({ 'pendingWithdrawals.txId': 1 });
UserBalanceSchema.index({ 'transactionHistory.timestamp': -1 });

// Method to add a transaction to history
UserBalanceSchema.methods.addTransaction = function(txData) {
  this.transactionHistory.push({
    txId: txData.txId,
    type: txData.type,
    token: txData.token,
    amount: txData.amount,
    fee: txData.fee || 0,
    description: txData.description,
    relatedTxId: txData.relatedTxId,
    timestamp: txData.timestamp || new Date()
  });
  
  this.updatedAt = new Date();
  return this.save();
};

// Method to update SOL balance
UserBalanceSchema.methods.updateSolBalance = function(amount, txData = null) {
  // Ensure balance doesn't go negative
  if (this.solBalance + amount < 0) {
    throw new Error('Insufficient SOL balance');
  }
  
  this.solBalance += amount;
  this.updatedAt = new Date();
  
  // Add transaction to history if provided
  if (txData) {
    this.addTransaction({
      ...txData,
      token: 'SOL',
      amount: amount
    });
  }
  
  return this.save();
};

// Method to update token balance
UserBalanceSchema.methods.updateTokenBalance = function(token, amount, txData = null) {
  const currentBalance = this.tokenBalances.get(token) || 0;
  
  // Ensure balance doesn't go negative
  if (currentBalance + amount < 0) {
    throw new Error(`Insufficient ${token} balance`);
  }
  
  this.tokenBalances.set(token, currentBalance + amount);
  this.updatedAt = new Date();
  
  // Add transaction to history if provided
  if (txData) {
    this.addTransaction({
      ...txData,
      token: token,
      amount: amount
    });
  }
  
  return this.save();
};

// Method to add pending deposit
UserBalanceSchema.methods.addPendingDeposit = function(depositData) {
  this.pendingDeposits.push({
    txId: depositData.txId,
    amount: depositData.amount,
    token: depositData.token,
    timestamp: depositData.timestamp || new Date(),
    status: 'pending'
  });
  
  this.updatedAt = new Date();
  return this.save();
};

// Method to confirm pending deposit
UserBalanceSchema.methods.confirmDeposit = function(txId) {
  const deposit = this.pendingDeposits.find(d => d.txId === txId);
  
  if (!deposit) {
    throw new Error('Deposit not found');
  }
  
  deposit.status = 'confirmed';
  
  // Update the appropriate balance
  if (deposit.token === 'SOL') {
    this.solBalance += deposit.amount;
  } else {
    const currentBalance = this.tokenBalances.get(deposit.token) || 0;
    this.tokenBalances.set(deposit.token, currentBalance + deposit.amount);
  }
  
  // Add to transaction history
  this.transactionHistory.push({
    txId: deposit.txId,
    type: 'deposit',
    token: deposit.token,
    amount: deposit.amount,
    timestamp: new Date(),
    description: 'Deposit confirmed'
  });
  
  this.updatedAt = new Date();
  return this.save();
};

// Method to add pending withdrawal
UserBalanceSchema.methods.addPendingWithdrawal = function(withdrawalData) {
  // Check if there's enough balance
  if (withdrawalData.token === 'SOL') {
    if (this.solBalance < withdrawalData.amount) {
      throw new Error('Insufficient SOL balance');
    }
    // Reserve the amount by reducing the balance
    this.solBalance -= withdrawalData.amount;
  } else {
    const currentBalance = this.tokenBalances.get(withdrawalData.token) || 0;
    if (currentBalance < withdrawalData.amount) {
      throw new Error(`Insufficient ${withdrawalData.token} balance`);
    }
    // Reserve the amount by reducing the balance
    this.tokenBalances.set(withdrawalData.token, currentBalance - withdrawalData.amount);
  }
  
  // Add to pending withdrawals
  this.pendingWithdrawals.push({
    txId: withdrawalData.txId,
    amount: withdrawalData.amount,
    token: withdrawalData.token,
    destinationAddress: withdrawalData.destinationAddress,
    timestamp: withdrawalData.timestamp || new Date(),
    status: 'pending'
  });
  
  // Add to transaction history
  this.transactionHistory.push({
    txId: withdrawalData.txId,
    type: 'withdrawal',
    token: withdrawalData.token,
    amount: -withdrawalData.amount,
    timestamp: new Date(),
    description: 'Withdrawal requested'
  });
  
  this.updatedAt = new Date();
  return this.save();
};

// Method to update withdrawal status
UserBalanceSchema.methods.updateWithdrawalStatus = function(txId, status) {
  const withdrawal = this.pendingWithdrawals.find(w => w.txId === txId);
  
  if (!withdrawal) {
    throw new Error('Withdrawal not found');
  }
  
  withdrawal.status = status;
  
  // If failed, refund the amount
  if (status === 'failed') {
    if (withdrawal.token === 'SOL') {
      this.solBalance += withdrawal.amount;
    } else {
      const currentBalance = this.tokenBalances.get(withdrawal.token) || 0;
      this.tokenBalances.set(withdrawal.token, currentBalance + withdrawal.amount);
    }
    
    // Add refund to transaction history
    this.transactionHistory.push({
      txId: `refund-${withdrawal.txId}`,
      type: 'adjustment',
      token: withdrawal.token,
      amount: withdrawal.amount,
      timestamp: new Date(),
      description: 'Withdrawal failed - amount refunded',
      relatedTxId: withdrawal.txId
    });
  }
  
  this.updatedAt = new Date();
  return this.save();
};

const UserBalance = mongoose.model('UserBalance', UserBalanceSchema);

module.exports = UserBalance;
