# Development to Production Guide

This guide explains how to use the development environment and transition to production when ready.

## Environment Setup

The application is designed to work in both development and production modes with minimal changes. The key differences are handled through environment variables and feature flags.

### Development Mode

Development mode provides:
- Mock implementations for services that fail to initialize
- Relaxed rate limiting
- Email verification bypass
- Simplified authentication
- Devnet Solana network
- Local database connections

### Production Mode

Production mode provides:
- Strict service initialization (fails if critical services cannot connect)
- Strict rate limiting
- Required email verification
- Enhanced security measures
- Mainnet Solana network
- Cloud-hosted database connections

## Switching Between Environments

### Using npm Scripts

```bash
# Switch to development mode
npm run env:dev

# Switch to production mode
npm run env:prod

# Start in development mode
npm run start:dev

# Start in production mode
npm run start:prod
```

### Manual Switching

You can also switch environments manually:

```bash
# Switch to development mode
node switch-env.js development

# Switch to production mode
node switch-env.js production
```

## Configuration Files

The environment configuration is stored in the following files:

- `.env.development` - Development environment variables
- `.env.production` - Production environment variables
- `.env` - Current active environment (copied from one of the above)

## Feature Flags

Feature flags control which features are enabled in each environment. These are defined in `src/config/environment.js`.

Key feature flags:
- `USE_REAL_SOLANA_NETWORK` - Use mainnet instead of devnet
- `USE_REAL_ORDER_MATCHING` - Use the C++ order matching engine
- `STORE_TRADES_ON_CHAIN` - Record trades on the Solana blockchain
- `USE_HARDWARE_WALLET` - Use hardware wallet for signing transactions
- `REQUIRE_EMAIL_VERIFICATION` - Require email verification for account creation

## Services

The application uses several services that are initialized in a specific order:

1. **Database Service** - Connects to MongoDB and PostgreSQL
2. **Solana Service** - Connects to the Solana blockchain
3. **Order Matching Service** - Connects to the C++ order matching engine

Each service has fallback mechanisms for development mode, but will fail in production mode if critical connections cannot be established.

## Preparing for Production

Before deploying to production, you should:

1. Update `.env.production` with your production credentials
2. Test the application in production mode locally
3. Ensure all services can connect properly
4. Set up monitoring and logging
5. Configure proper security measures

## Solana Smart Contract Integration

The Solana smart contract integration works in both development and production modes:

- In development, it connects to Solana devnet and can use mock implementations
- In production, it connects to Solana mainnet and requires proper wallet setup

### Token Creation

Token creation works through the same API in both environments:

- In development, tokens are created on Solana devnet
- In production, tokens are created on Solana mainnet

## Database Integration

The database service supports both development and production modes:

- In development, it can use local databases or mock implementations
- In production, it requires proper cloud database connections

## Troubleshooting

If you encounter issues:

1. Check the logs for error messages
2. Verify that all required environment variables are set
3. Ensure all services are running (order matching engine, databases, etc.)
4. Try restarting the services in the correct order

## Security Considerations

In production mode, additional security measures are enabled:

- Strict rate limiting
- TLS enforcement
- JWT token expiration
- Email verification requirement
- Hardware wallet integration

Make sure to properly secure your production environment before deployment.
