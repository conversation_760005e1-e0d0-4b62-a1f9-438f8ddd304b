import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const nibiru = /*#__PURE__*/ defineChain({
    id: 6900,
    name: '<PERSON>bir<PERSON>',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON><PERSON>',
        symbol: 'NIB<PERSON>',
    },
    rpcUrls: {
        default: { http: ['https://evm-rpc.nibiru.fi'] },
    },
    blockExplorers: {
        default: {
            name: '<PERSON>biS<PERSON>',
            url: 'https://nibiscan.io',
        },
    },
    contracts: {
        multicall3: {
            address: '0xcA11bde05977b3631167028862bE2a173976CA11',
            blockCreated: 19587573,
        },
    },
});
//# sourceMappingURL=nibiru.js.map