/**
 * User Balance Model (PostgreSQL)
 * 
 * Stores user balance data for the centralized wallet system
 */

const { sequelize, Sequelize } = require('../../config/postgresql');

const UserBalance = sequelize.define('UserBalance', {
  // ID (primary key)
  id: {
    type: Sequelize.UUID,
    defaultValue: Sequelize.UUIDV4,
    primaryKey: true
  },
  
  // User ID (from MongoDB)
  userId: {
    type: Sequelize.STRING,
    allowNull: false,
    unique: true
  },
  
  // SOL balance (as string to preserve precision)
  solBalance: {
    type: Sequelize.STRING,
    defaultValue: '0',
    allowNull: false
  },
  
  // Token balances (JSONB for flexibility)
  tokenBalances: {
    type: Sequelize.JSONB,
    defaultValue: {},
    allowNull: false
  },
  
  // Pending deposits (JSONB array)
  pendingDeposits: {
    type: Sequelize.JSONB,
    defaultValue: [],
    allowNull: false
  },
  
  // Pending withdrawals (JSONB array)
  pendingWithdrawals: {
    type: Sequelize.JSONB,
    defaultValue: [],
    allowNull: false
  },
  
  // Last updated timestamp
  lastUpdated: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW,
    allowNull: false
  }
}, {
  // Table configuration
  tableName: 'user_balances',
  timestamps: true,
  indexes: [
    {
      fields: ['userId'],
      unique: true
    }
  ]
});

module.exports = UserBalance;
