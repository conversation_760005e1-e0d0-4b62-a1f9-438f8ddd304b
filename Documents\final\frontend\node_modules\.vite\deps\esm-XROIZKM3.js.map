{"version": 3, "sources": ["../../eventemitter3/index.js", "../../@project-serum/sol-wallet-adapter/node_modules/base-x/src/index.js", "../../@project-serum/sol-wallet-adapter/node_modules/bs58/index.js", "../../salmon-adapter-sdk/lib/esm/index.js", "../../salmon-adapter-sdk/lib/esm/adapters/base.js", "../../@project-serum/sol-wallet-adapter/src/index.ts", "../../salmon-adapter-sdk/lib/esm/adapters/web.js", "../../salmon-adapter-sdk/lib/esm/adapters/extension.js"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "'use strict'\n// base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\n// @ts-ignore\nvar _Buffer = require('safe-buffer').Buffer\nfunction base (ALPHABET) {\n  if (ALPHABET.length >= 255) { throw new TypeError('Alphabet too long') }\n  var BASE_MAP = new Uint8Array(256)\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i)\n    var xc = x.charCodeAt(0)\n    if (BASE_MAP[xc] !== 255) { throw new TypeError(x + ' is ambiguous') }\n    BASE_MAP[xc] = i\n  }\n  var BASE = ALPHABET.length\n  var LEADER = ALPHABET.charAt(0)\n  var FACTOR = Math.log(BASE) / Math.log(256) // log(BASE) / log(256), rounded up\n  var iFACTOR = Math.log(256) / Math.log(BASE) // log(256) / log(BASE), rounded up\n  function encode (source) {\n    if (Array.isArray(source) || source instanceof Uint8Array) { source = _Buffer.from(source) }\n    if (!_Buffer.isBuffer(source)) { throw new TypeError('Expected Buffer') }\n    if (source.length === 0) { return '' }\n        // Skip & count leading zeroes.\n    var zeroes = 0\n    var length = 0\n    var pbegin = 0\n    var pend = source.length\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++\n      zeroes++\n    }\n        // Allocate enough space in big-endian base58 representation.\n    var size = ((pend - pbegin) * iFACTOR + 1) >>> 0\n    var b58 = new Uint8Array(size)\n        // Process the bytes.\n    while (pbegin !== pend) {\n      var carry = source[pbegin]\n            // Apply \"b58 = b58 * 256 + ch\".\n      var i = 0\n      for (var it1 = size - 1; (carry !== 0 || i < length) && (it1 !== -1); it1--, i++) {\n        carry += (256 * b58[it1]) >>> 0\n        b58[it1] = (carry % BASE) >>> 0\n        carry = (carry / BASE) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      pbegin++\n    }\n        // Skip leading zeroes in base58 result.\n    var it2 = size - length\n    while (it2 !== size && b58[it2] === 0) {\n      it2++\n    }\n        // Translate the result into a string.\n    var str = LEADER.repeat(zeroes)\n    for (; it2 < size; ++it2) { str += ALPHABET.charAt(b58[it2]) }\n    return str\n  }\n  function decodeUnsafe (source) {\n    if (typeof source !== 'string') { throw new TypeError('Expected String') }\n    if (source.length === 0) { return _Buffer.alloc(0) }\n    var psz = 0\n        // Skip and count leading '1's.\n    var zeroes = 0\n    var length = 0\n    while (source[psz] === LEADER) {\n      zeroes++\n      psz++\n    }\n        // Allocate enough space in big-endian base256 representation.\n    var size = (((source.length - psz) * FACTOR) + 1) >>> 0 // log(58) / log(256), rounded up.\n    var b256 = new Uint8Array(size)\n        // Process the characters.\n    while (psz < source.length) {\n            // Find code of next character\n      var charCode = source.charCodeAt(psz)\n            // Base map can not be indexed using char code\n      if (charCode > 255) { return }\n            // Decode character\n      var carry = BASE_MAP[charCode]\n            // Invalid character\n      if (carry === 255) { return }\n      var i = 0\n      for (var it3 = size - 1; (carry !== 0 || i < length) && (it3 !== -1); it3--, i++) {\n        carry += (BASE * b256[it3]) >>> 0\n        b256[it3] = (carry % 256) >>> 0\n        carry = (carry / 256) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      psz++\n    }\n        // Skip leading zeroes in b256.\n    var it4 = size - length\n    while (it4 !== size && b256[it4] === 0) {\n      it4++\n    }\n    var vch = _Buffer.allocUnsafe(zeroes + (size - it4))\n    vch.fill(0x00, 0, zeroes)\n    var j = zeroes\n    while (it4 !== size) {\n      vch[j++] = b256[it4++]\n    }\n    return vch\n  }\n  function decode (string) {\n    var buffer = decodeUnsafe(string)\n    if (buffer) { return buffer }\n    throw new Error('Non-base' + BASE + ' character')\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  }\n}\nmodule.exports = base\n", "var basex = require('base-x')\nvar ALPHABET = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz'\n\nmodule.exports = basex(ALPHABET)\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport EventEmitter from 'eventemitter3';\nimport WebAdapter from './adapters/web';\nimport ExtensionAdapter from './adapters/extension';\nvar Salmon = /** @class */ (function (_super) {\n    __extends(Salmon, _super);\n    function Salmon(config) {\n        var _this = _super.call(this) || this;\n        _this._network = 'mainnet-beta';\n        _this._adapterInstance = null;\n        _this._connectHandler = null;\n        _this._connected = function () {\n            if (_this._connectHandler) {\n                _this._connectHandler.resolve();\n                _this._connectHandler = null;\n            }\n            _this.emit('connect', _this.publicKey);\n        };\n        _this._disconnected = function () {\n            if (_this._connectHandler) {\n                _this._connectHandler.reject();\n                _this._connectHandler = null;\n            }\n            _this._adapterInstance = null;\n            _this.emit('disconnect');\n        };\n        if (config === null || config === void 0 ? void 0 : config.network) {\n            _this._network = config === null || config === void 0 ? void 0 : config.network;\n        }\n        if (config === null || config === void 0 ? void 0 : config.provider) {\n            _this._provider = config === null || config === void 0 ? void 0 : config.provider;\n        }\n        else if (window.salmon) {\n            _this._provider = window.salmon;\n        }\n        else {\n            _this._provider = 'https://app.salmonwallet.io';\n        }\n        return _this;\n    }\n    Object.defineProperty(Salmon.prototype, \"publicKey\", {\n        get: function () {\n            var _a;\n            return ((_a = this._adapterInstance) === null || _a === void 0 ? void 0 : _a.publicKey) || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Salmon.prototype, \"isConnected\", {\n        get: function () {\n            var _a;\n            return !!((_a = this._adapterInstance) === null || _a === void 0 ? void 0 : _a.connected);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Salmon.prototype, \"connected\", {\n        get: function () {\n            return this.isConnected;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Salmon.prototype, \"autoApprove\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Salmon.prototype.connect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (this.connected) {\n                            return [2 /*return*/];\n                        }\n                        if (typeof this._provider === 'string') {\n                            this._adapterInstance = new WebAdapter(this._provider, this._network);\n                        }\n                        else {\n                            this._adapterInstance = new ExtensionAdapter(this._provider, this._network);\n                        }\n                        this._adapterInstance.on('connect', this._connected);\n                        this._adapterInstance.on('disconnect', this._disconnected);\n                        this._adapterInstance.connect();\n                        return [4 /*yield*/, new Promise(function (resolve, reject) {\n                                _this._connectHandler = { resolve: resolve, reject: reject };\n                            })];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Salmon.prototype.disconnect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this._adapterInstance) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, this._adapterInstance.disconnect()];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Salmon.prototype.signTransaction = function (transaction) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._adapterInstance.signTransaction(transaction)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Salmon.prototype.signAllTransactions = function (transactions) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._adapterInstance.signAllTransactions(transactions)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Salmon.prototype.signMessage = function (data, display) {\n        if (display === void 0) { display = 'utf8'; }\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._adapterInstance.signMessage(data, display)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Salmon.prototype.sign = function (data, display) {\n        if (display === void 0) { display = 'utf8'; }\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.signMessage(data, display)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    return Salmon;\n}(EventEmitter));\nexport default Salmon;\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport EventEmitter from 'eventemitter3';\nvar WalletAdapter = /** @class */ (function (_super) {\n    __extends(WalletAdapter, _super);\n    function WalletAdapter() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return WalletAdapter;\n}(EventEmitter));\nexport default WalletAdapter;\n", "import EventEmitter from 'eventemitter3';\nimport { PublicKey, Transaction } from '@solana/web3.js';\nimport bs58 from 'bs58';\n\ntype InjectedProvider = { postMessage: (params: unknown) => void };\n\nexport default class Wallet extends EventEmitter {\n  private _providerUrl: URL | undefined;\n  private _injectedProvider?: InjectedProvider;\n  private _publicKey: PublicKey | null = null;\n  private _popup: Window | null = null;\n  private _handlerAdded = false;\n  private _nextRequestId = 1;\n  private _autoApprove = false;\n  private _responsePromises: Map<\n    number,\n    [(value: string) => void, (reason: Error) => void]\n  > = new Map();\n\n  constructor(provider: unknown, private _network: string) {\n    super();\n    if (isInjectedProvider(provider)) {\n      this._injectedProvider = provider;\n    } else if (isString(provider)) {\n      this._providerUrl = new URL(provider);\n      this._providerUrl.hash = new URLSearchParams({\n        origin: window.location.origin,\n        network: this._network,\n      }).toString();\n    } else {\n      throw new Error(\n        'provider parameter must be an injected provider or a URL string.',\n      );\n    }\n  }\n\n  handleMessage = (\n    e: MessageEvent<{\n      id: number;\n      method: string;\n      params: {\n        autoApprove: boolean;\n        publicKey: string;\n      };\n      result?: string;\n      error?: string;\n    }>,\n  ): void => {\n    if (\n      (this._injectedProvider && e.source === window) ||\n      (e.origin === this._providerUrl?.origin && e.source === this._popup)\n    ) {\n      if (e.data.method === 'connected') {\n        const newPublicKey = new PublicKey(e.data.params.publicKey);\n        if (!this._publicKey || !this._publicKey.equals(newPublicKey)) {\n          if (this._publicKey && !this._publicKey.equals(newPublicKey)) {\n            this.handleDisconnect();\n          }\n          this._publicKey = newPublicKey;\n          this._autoApprove = !!e.data.params.autoApprove;\n          this.emit('connect', this._publicKey);\n        }\n      } else if (e.data.method === 'disconnected') {\n        this.handleDisconnect();\n      } else if (e.data.result || e.data.error) {\n        const promises = this._responsePromises.get(e.data.id);\n        if (promises) {\n          const [resolve, reject] = promises;\n          if (e.data.result) {\n            resolve(e.data.result);\n          } else {\n            reject(new Error(e.data.error));\n          }\n        }\n      }\n    }\n  };\n\n  private handleConnect() {\n    if (!this._handlerAdded) {\n      this._handlerAdded = true;\n      window.addEventListener('message', this.handleMessage);\n      window.addEventListener('beforeunload', this._beforeUnload);\n    }\n    if (this._injectedProvider) {\n      return new Promise<void>((resolve) => {\n        void this.sendRequest('connect', {});\n        resolve();\n      });\n    } else {\n      window.name = 'parent';\n      this._popup = window.open(\n        this._providerUrl?.toString(),\n        '_blank',\n        'location,resizable,width=460,height=675',\n      );\n      return new Promise((resolve) => {\n        this.once('connect', resolve);\n      });\n    }\n  }\n\n  private handleDisconnect() {\n    if (this._handlerAdded) {\n      this._handlerAdded = false;\n      window.removeEventListener('message', this.handleMessage);\n      window.removeEventListener('beforeunload', this._beforeUnload);\n    }\n    if (this._publicKey) {\n      this._publicKey = null;\n      this.emit('disconnect');\n    }\n    this._responsePromises.forEach(([, reject], id) => {\n      this._responsePromises.delete(id);\n      reject(new Error('Wallet disconnected'));\n    });\n  }\n\n  private async sendRequest(method: string, params: Record<string, unknown>) {\n    if (method !== 'connect' && !this.connected) {\n      throw new Error('Wallet not connected');\n    }\n    const requestId = this._nextRequestId;\n    ++this._nextRequestId;\n    return new Promise((resolve, reject) => {\n      this._responsePromises.set(requestId, [resolve, reject]);\n      if (this._injectedProvider) {\n        this._injectedProvider.postMessage({\n          jsonrpc: '2.0',\n          id: requestId,\n          method,\n          params: {\n            network: this._network,\n            ...params,\n          },\n        });\n      } else {\n        this._popup?.postMessage(\n          {\n            jsonrpc: '2.0',\n            id: requestId,\n            method,\n            params,\n          },\n          this._providerUrl?.origin ?? '',\n        );\n\n        if (!this.autoApprove) {\n          this._popup?.focus();\n        }\n      }\n    });\n  }\n\n  get publicKey(): PublicKey | null {\n    return this._publicKey;\n  }\n\n  get connected(): boolean {\n    return this._publicKey !== null;\n  }\n\n  get autoApprove(): boolean {\n    return this._autoApprove;\n  }\n\n  async connect(): Promise<void> {\n    if (this._popup) {\n      this._popup.close();\n    }\n    await this.handleConnect();\n  }\n\n  async disconnect(): Promise<void> {\n    if (this._injectedProvider) {\n      await this.sendRequest('disconnect', {});\n    }\n    if (this._popup) {\n      this._popup.close();\n    }\n    this.handleDisconnect();\n  }\n\n  private _beforeUnload = (): void => {\n    void this.disconnect();\n  };\n\n  async sign(\n    data: Uint8Array,\n    display: unknown,\n  ): Promise<{\n    signature: Buffer;\n    publicKey: PublicKey;\n  }> {\n    if (!(data instanceof Uint8Array)) {\n      throw new Error('Data must be an instance of Uint8Array');\n    }\n\n    const response = (await this.sendRequest('sign', {\n      data,\n      display,\n    })) as { publicKey: string; signature: string };\n    const signature = bs58.decode(response.signature);\n    const publicKey = new PublicKey(response.publicKey);\n    return {\n      signature,\n      publicKey,\n    };\n  }\n\n  async signTransaction(transaction: Transaction): Promise<Transaction> {\n    const response = (await this.sendRequest('signTransaction', {\n      message: bs58.encode(transaction.serializeMessage()),\n    })) as { publicKey: string; signature: string };\n    const signature = bs58.decode(response.signature);\n    const publicKey = new PublicKey(response.publicKey);\n    transaction.addSignature(publicKey, signature);\n    return transaction;\n  }\n\n  async signAllTransactions(\n    transactions: Transaction[],\n  ): Promise<Transaction[]> {\n    const response = (await this.sendRequest('signAllTransactions', {\n      messages: transactions.map((tx) => bs58.encode(tx.serializeMessage())),\n    })) as { publicKey: string; signatures: string[] };\n    const signatures = response.signatures.map((s) => bs58.decode(s));\n    const publicKey = new PublicKey(response.publicKey);\n    transactions = transactions.map((tx, idx) => {\n      tx.addSignature(publicKey, signatures[idx]);\n      return tx;\n    });\n    return transactions;\n  }\n\n  async diffieHellman(\n    publicKey: Uint8Array,\n  ): Promise<{ publicKey: Uint8Array; secretKey: Uint8Array }> {\n    if (!(publicKey instanceof Uint8Array)) {\n      throw new Error('Data must be an instance of Uint8Array');\n    }\n    const response = (await this.sendRequest('diffieHellman', {\n      publicKey,\n    })) as {\n      publicKey: Uint8Array;\n      secretKey: Uint8Array;\n    };\n    return response;\n  }\n}\n\nfunction isString(a: unknown): a is string {\n  return typeof a === 'string';\n}\n\nfunction isInjectedProvider(a: unknown): a is InjectedProvider {\n  return (\n    isObject(a) && 'postMessage' in a && typeof a.postMessage === 'function'\n  );\n}\n\nfunction isObject(a: unknown): a is Record<string, unknown> {\n  return typeof a === 'object' && a !== null;\n}\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport WalletAdapter from './base';\nimport Wallet from '@project-serum/sol-wallet-adapter';\nvar WebAdapter = /** @class */ (function (_super) {\n    __extends(WebAdapter, _super);\n    function WebAdapter(provider, network) {\n        var _this = _super.call(this) || this;\n        _this._instance = null;\n        _this._handleConnect = function () {\n            _this.emit('connect');\n        };\n        _this._handleDisconnect = function () {\n            window.clearInterval(_this._pollTimer);\n            _this.emit('disconnect');\n        };\n        _this._provider = provider;\n        _this._network = network;\n        return _this;\n    }\n    Object.defineProperty(WebAdapter.prototype, \"publicKey\", {\n        get: function () {\n            return this._instance.publicKey || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(WebAdapter.prototype, \"connected\", {\n        get: function () {\n            return this._instance.connected || false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    WebAdapter.prototype.connect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this._instance = new Wallet(this._provider, this._network);\n                        this._instance.on('connect', this._handleConnect);\n                        this._instance.on('disconnect', this._handleDisconnect);\n                        this._pollTimer = window.setInterval(function () {\n                            var _a, _b;\n                            // @ts-ignore\n                            if (((_b = (_a = _this._instance) === null || _a === void 0 ? void 0 : _a._popup) === null || _b === void 0 ? void 0 : _b.closed) !== false) {\n                                _this._handleDisconnect();\n                            }\n                        }, 200);\n                        return [4 /*yield*/, this._instance.connect()];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.disconnect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        this._instance.removeAllListeners('connect');\n                        this._instance.removeAllListeners('disconnect');\n                        return [4 /*yield*/, this._instance.disconnect()];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signTransaction = function (transaction) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._instance.signTransaction(transaction)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signAllTransactions = function (transactions) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._instance.signAllTransactions(transactions)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signMessage = function (data, display) {\n        if (display === void 0) { display = 'hex'; }\n        return __awaiter(this, void 0, void 0, function () {\n            var signature;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._instance.sign(data, display)];\n                    case 1:\n                        signature = (_a.sent()).signature;\n                        return [2 /*return*/, Uint8Array.from(signature)];\n                }\n            });\n        });\n    };\n    return WebAdapter;\n}(WalletAdapter));\nexport default WebAdapter;\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport WalletAdapter from './base';\nvar ExtensionAdapter = /** @class */ (function (_super) {\n    __extends(ExtensionAdapter, _super);\n    function ExtensionAdapter(provider, network) {\n        var _this = _super.call(this) || this;\n        _this._provider = provider;\n        _this._network = network;\n        return _this;\n    }\n    Object.defineProperty(ExtensionAdapter.prototype, \"publicKey\", {\n        get: function () {\n            return this._provider.publicKey;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(ExtensionAdapter.prototype, \"connected\", {\n        get: function () {\n            return this._provider.isConnected;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ExtensionAdapter.prototype.connect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var e_1;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        _a.trys.push([0, 2, , 3]);\n                        if (this.connected) {\n                            throw new Error('Wallet already connected');\n                        }\n                        return [4 /*yield*/, this._provider.connect()];\n                    case 1:\n                        _a.sent();\n                        this.emit('connect');\n                        return [3 /*break*/, 3];\n                    case 2:\n                        e_1 = _a.sent();\n                        this.emit('disconnect');\n                        throw e_1;\n                    case 3: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    ExtensionAdapter.prototype.disconnect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._provider.disconnect()];\n                    case 1:\n                        _a.sent();\n                        this.emit('disconnect');\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    ExtensionAdapter.prototype.signTransaction = function (transaction) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._provider.signTransaction(transaction, this._network)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    ExtensionAdapter.prototype.signAllTransactions = function (transactions) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._provider.signAllTransactions(transactions, this._network)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    ExtensionAdapter.prototype.signMessage = function (data) {\n        return __awaiter(this, void 0, void 0, function () {\n            var signature;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        if (!(data instanceof Uint8Array)) {\n                            throw new Error('Data must be an instance of Uint8Array');\n                        }\n                        return [4 /*yield*/, this._provider.signMessage(data)];\n                    case 1:\n                        signature = (_a.sent()).signature;\n                        return [2 /*return*/, signature];\n                }\n            });\n        });\n    };\n    return ExtensionAdapter;\n}(WalletAdapter));\nexport default ExtensionAdapter;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI,SAAS;AASb,aAAS,SAAS;AAAA,IAAC;AASnB,QAAI,OAAO,QAAQ;AACjB,aAAO,YAAY,uBAAO,OAAO,IAAI;AAMrC,UAAI,CAAC,IAAI,OAAO,EAAE,UAAW,UAAS;AAAA,IACxC;AAWA,aAAS,GAAG,IAAI,SAAS,MAAM;AAC7B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AAAA,IACtB;AAaA,aAAS,YAAY,SAAS,OAAO,IAAI,SAAS,MAAM;AACtD,UAAI,OAAO,OAAO,YAAY;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,WAAW,IAAI,GAAG,IAAI,WAAW,SAAS,IAAI,GAC9C,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,QAAQ,QAAQ,GAAG,EAAG,SAAQ,QAAQ,GAAG,IAAI,UAAU,QAAQ;AAAA,eAC3D,CAAC,QAAQ,QAAQ,GAAG,EAAE,GAAI,SAAQ,QAAQ,GAAG,EAAE,KAAK,QAAQ;AAAA,UAChE,SAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG,QAAQ;AAE3D,aAAO;AAAA,IACT;AASA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,EAAE,QAAQ,iBAAiB,EAAG,SAAQ,UAAU,IAAI,OAAO;AAAA,UAC1D,QAAO,QAAQ,QAAQ,GAAG;AAAA,IACjC;AASA,aAASA,gBAAe;AACtB,WAAK,UAAU,IAAI,OAAO;AAC1B,WAAK,eAAe;AAAA,IACtB;AASA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,UAAI,QAAQ,CAAC,GACT,QACA;AAEJ,UAAI,KAAK,iBAAiB,EAAG,QAAO;AAEpC,WAAK,QAAS,SAAS,KAAK,SAAU;AACpC,YAAI,IAAI,KAAK,QAAQ,IAAI,EAAG,OAAM,KAAK,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAEA,UAAI,OAAO,uBAAuB;AAChC,eAAO,MAAM,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,OAAO;AAC3D,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,WAAW,KAAK,QAAQ,GAAG;AAE/B,UAAI,CAAC,SAAU,QAAO,CAAC;AACvB,UAAI,SAAS,GAAI,QAAO,CAAC,SAAS,EAAE;AAEpC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK;AAClE,WAAG,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,gBAAgB,SAAS,cAAc,OAAO;AACnE,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,CAAC,UAAW,QAAO;AACvB,UAAI,UAAU,GAAI,QAAO;AACzB,aAAO,UAAU;AAAA,IACnB;AASA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AACrE,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG,EAAG,QAAO;AAE/B,UAAI,YAAY,KAAK,QAAQ,GAAG,GAC5B,MAAM,UAAU,QAChB,MACA;AAEJ,UAAI,UAAU,IAAI;AAChB,YAAI,UAAU,KAAM,MAAK,eAAe,OAAO,UAAU,IAAI,QAAW,IAAI;AAE5E,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,OAAO,GAAG;AAAA,UACrD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,EAAE,GAAG;AAAA,UACzD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,EAAE,GAAG;AAAA,UAC7D,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,UACjE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,UACrE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3E;AAEA,aAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAClD,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC3B;AAEA,kBAAU,GAAG,MAAM,UAAU,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,YAAI,SAAS,UAAU,QACnB;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,cAAI,UAAU,CAAC,EAAE,KAAM,MAAK,eAAe,OAAO,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAElF,kBAAQ,KAAK;AAAA,YACX,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,OAAO;AAAG;AAAA,YACpD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,EAAE;AAAG;AAAA,YACxD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,EAAE;AAAG;AAAA,YAC5D,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,IAAI,EAAE;AAAG;AAAA,YAChE;AACE,kBAAI,CAAC,KAAM,MAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAC7D,qBAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,cAC3B;AAEA,wBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC,EAAE,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,IAAAA,cAAa,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS;AAC1D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,KAAK;AAAA,IACpD;AAWA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS;AAC9D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,IACnD;AAYA,IAAAA,cAAa,UAAU,iBAAiB,SAAS,eAAe,OAAO,IAAI,SAAS,MAAM;AACxF,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG,EAAG,QAAO;AAC/B,UAAI,CAAC,IAAI;AACP,mBAAW,MAAM,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,UAAU,IAAI;AAChB,YACE,UAAU,OAAO,OAChB,CAAC,QAAQ,UAAU,UACnB,CAAC,WAAW,UAAU,YAAY,UACnC;AACA,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACvE,cACE,UAAU,CAAC,EAAE,OAAO,MACnB,QAAQ,CAAC,UAAU,CAAC,EAAE,QACtB,WAAW,UAAU,CAAC,EAAE,YAAY,SACrC;AACA,mBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAKA,YAAI,OAAO,OAAQ,MAAK,QAAQ,GAAG,IAAI,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA,YACpE,YAAW,MAAM,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,qBAAqB,SAAS,mBAAmB,OAAO;AAC7E,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,SAAS,SAAS,QAAQ;AAChC,YAAI,KAAK,QAAQ,GAAG,EAAG,YAAW,MAAM,GAAG;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,IAAI,OAAO;AAC1B,aAAK,eAAe;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAKA,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AACpD,IAAAA,cAAa,UAAU,cAAcA,cAAa,UAAU;AAK5D,IAAAA,cAAa,WAAW;AAKxB,IAAAA,cAAa,eAAeA;AAK5B,QAAI,gBAAgB,OAAO,QAAQ;AACjC,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;AC/UA;AAAA;AAAA;AAOA,QAAI,UAAU,sBAAuB;AACrC,aAAS,KAAM,UAAU;AACvB,UAAI,SAAS,UAAU,KAAK;AAAE,cAAM,IAAI,UAAU,mBAAmB;AAAA,MAAE;AACvE,UAAI,WAAW,IAAI,WAAW,GAAG;AACjC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,iBAAS,CAAC,IAAI;AAAA,MAChB;AACA,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,IAAI,SAAS,OAAO,CAAC;AACzB,YAAI,KAAK,EAAE,WAAW,CAAC;AACvB,YAAI,SAAS,EAAE,MAAM,KAAK;AAAE,gBAAM,IAAI,UAAU,IAAI,eAAe;AAAA,QAAE;AACrE,iBAAS,EAAE,IAAI;AAAA,MACjB;AACA,UAAI,OAAO,SAAS;AACpB,UAAI,SAAS,SAAS,OAAO,CAAC;AAC9B,UAAI,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAC1C,UAAI,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAC3C,eAAS,OAAQ,QAAQ;AACvB,YAAI,MAAM,QAAQ,MAAM,KAAK,kBAAkB,YAAY;AAAE,mBAAS,QAAQ,KAAK,MAAM;AAAA,QAAE;AAC3F,YAAI,CAAC,QAAQ,SAAS,MAAM,GAAG;AAAE,gBAAM,IAAI,UAAU,iBAAiB;AAAA,QAAE;AACxE,YAAI,OAAO,WAAW,GAAG;AAAE,iBAAO;AAAA,QAAG;AAErC,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,OAAO,OAAO;AAClB,eAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,GAAG;AAC9C;AACA;AAAA,QACF;AAEA,YAAI,QAAS,OAAO,UAAU,UAAU,MAAO;AAC/C,YAAI,MAAM,IAAI,WAAW,IAAI;AAE7B,eAAO,WAAW,MAAM;AACtB,cAAI,QAAQ,OAAO,MAAM;AAEzB,cAAIC,KAAI;AACR,mBAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAI,WAAY,QAAQ,IAAK,OAAOA,MAAK;AAChF,qBAAU,MAAM,IAAI,GAAG,MAAO;AAC9B,gBAAI,GAAG,IAAK,QAAQ,SAAU;AAC9B,oBAAS,QAAQ,SAAU;AAAA,UAC7B;AACA,cAAI,UAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAAE;AACrD,mBAASA;AACT;AAAA,QACF;AAEA,YAAI,MAAM,OAAO;AACjB,eAAO,QAAQ,QAAQ,IAAI,GAAG,MAAM,GAAG;AACrC;AAAA,QACF;AAEA,YAAI,MAAM,OAAO,OAAO,MAAM;AAC9B,eAAO,MAAM,MAAM,EAAE,KAAK;AAAE,iBAAO,SAAS,OAAO,IAAI,GAAG,CAAC;AAAA,QAAE;AAC7D,eAAO;AAAA,MACT;AACA,eAAS,aAAc,QAAQ;AAC7B,YAAI,OAAO,WAAW,UAAU;AAAE,gBAAM,IAAI,UAAU,iBAAiB;AAAA,QAAE;AACzE,YAAI,OAAO,WAAW,GAAG;AAAE,iBAAO,QAAQ,MAAM,CAAC;AAAA,QAAE;AACnD,YAAI,MAAM;AAEV,YAAI,SAAS;AACb,YAAI,SAAS;AACb,eAAO,OAAO,GAAG,MAAM,QAAQ;AAC7B;AACA;AAAA,QACF;AAEA,YAAI,QAAU,OAAO,SAAS,OAAO,SAAU,MAAO;AACtD,YAAI,OAAO,IAAI,WAAW,IAAI;AAE9B,eAAO,MAAM,OAAO,QAAQ;AAE1B,cAAI,WAAW,OAAO,WAAW,GAAG;AAEpC,cAAI,WAAW,KAAK;AAAE;AAAA,UAAO;AAE7B,cAAI,QAAQ,SAAS,QAAQ;AAE7B,cAAI,UAAU,KAAK;AAAE;AAAA,UAAO;AAC5B,cAAIA,KAAI;AACR,mBAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAI,WAAY,QAAQ,IAAK,OAAOA,MAAK;AAChF,qBAAU,OAAO,KAAK,GAAG,MAAO;AAChC,iBAAK,GAAG,IAAK,QAAQ,QAAS;AAC9B,oBAAS,QAAQ,QAAS;AAAA,UAC5B;AACA,cAAI,UAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAAE;AACrD,mBAASA;AACT;AAAA,QACF;AAEA,YAAI,MAAM,OAAO;AACjB,eAAO,QAAQ,QAAQ,KAAK,GAAG,MAAM,GAAG;AACtC;AAAA,QACF;AACA,YAAI,MAAM,QAAQ,YAAY,UAAU,OAAO,IAAI;AACnD,YAAI,KAAK,GAAM,GAAG,MAAM;AACxB,YAAIC,KAAI;AACR,eAAO,QAAQ,MAAM;AACnB,cAAIA,IAAG,IAAI,KAAK,KAAK;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,eAAS,OAAQ,QAAQ;AACvB,YAAI,SAAS,aAAa,MAAM;AAChC,YAAI,QAAQ;AAAE,iBAAO;AAAA,QAAO;AAC5B,cAAM,IAAI,MAAM,aAAa,OAAO,YAAY;AAAA,MAClD;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;AC1HjB;AAAA;AAAA,QAAI,QAAQ;AACZ,QAAI,WAAW;AAEf,WAAO,UAAU,MAAM,QAAQ;AAAA;AAAA;;;ACgD/B,IAAAC,wBAAyB;;;ACpCzB,2BAAyB;AAfzB,IAAI,YAAyC,2BAAY;AACrD,MAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,IAAG;AACpG,WAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,SAAU,GAAG,GAAG;AACnB,QAAI,OAAO,MAAM,cAAc,MAAM;AACjC,YAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kBAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAG;AACtC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,EACtF;AACJ,EAAG;AAEH,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,cAAUC,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACrB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,WAAOA;AAAA,EACX,EAAE,qBAAAC,OAAY;AAAA;AACd,IAAO,eAAQ;;;ACvBf,IAAAC,wBAAyB;AACzB;AACA,kBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIjB,IAAqB,SAArB,cAAoC,sBAAAC,QAAY;EAa9C,YAAY,UAA2B,UAAgB;AACrD,UAAK;AADgC,SAAA,WAAA;AAV/B,SAAA,aAA+B;AAC/B,SAAA,SAAwB;AACxB,SAAA,gBAAgB;AAChB,SAAA,iBAAiB;AACjB,SAAA,eAAe;AACf,SAAA,oBAGJ,oBAAI,IAAG;AAmBX,SAAA,gBAAgB,CACd,MAUQ;;AACR,UACG,KAAK,qBAAqB,EAAE,WAAW,UACvC,EAAE,aAAW,KAAA,KAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,WAAU,EAAE,WAAW,KAAK,QAC7D;AACA,YAAI,EAAE,KAAK,WAAW,aAAa;AACjC,gBAAM,eAAe,IAAI,UAAU,EAAE,KAAK,OAAO,SAAS;AAC1D,cAAI,CAAC,KAAK,cAAc,CAAC,KAAK,WAAW,OAAO,YAAY,GAAG;AAC7D,gBAAI,KAAK,cAAc,CAAC,KAAK,WAAW,OAAO,YAAY,GAAG;AAC5D,mBAAK,iBAAgB;;AAEvB,iBAAK,aAAa;AAClB,iBAAK,eAAe,CAAC,CAAC,EAAE,KAAK,OAAO;AACpC,iBAAK,KAAK,WAAW,KAAK,UAAU;;mBAE7B,EAAE,KAAK,WAAW,gBAAgB;AAC3C,eAAK,iBAAgB;mBACZ,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;AACxC,gBAAM,WAAW,KAAK,kBAAkB,IAAI,EAAE,KAAK,EAAE;AACrD,cAAI,UAAU;AACZ,kBAAM,CAAC,SAAS,MAAM,IAAI;AAC1B,gBAAI,EAAE,KAAK,QAAQ;AACjB,sBAAQ,EAAE,KAAK,MAAM;mBAChB;AACL,qBAAO,IAAI,MAAM,EAAE,KAAK,KAAK,CAAC;;;;;IAKxC;AA2GQ,SAAA,gBAAgB,MAAW;AACjC,WAAK,KAAK,WAAU;IACtB;AApKE,QAAI,mBAAmB,QAAQ,GAAG;AAChC,WAAK,oBAAoB;eAChB,SAAS,QAAQ,GAAG;AAC7B,WAAK,eAAe,IAAI,IAAI,QAAQ;AACpC,WAAK,aAAa,OAAO,IAAI,gBAAgB;QAC3C,QAAQ,OAAO,SAAS;QACxB,SAAS,KAAK;OACf,EAAE,SAAQ;WACN;AACL,YAAM,IAAI,MACR,kEAAkE;;EAGxE;EA4CQ,gBAAa;;AACnB,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,gBAAgB;AACrB,aAAO,iBAAiB,WAAW,KAAK,aAAa;AACrD,aAAO,iBAAiB,gBAAgB,KAAK,aAAa;;AAE5D,QAAI,KAAK,mBAAmB;AAC1B,aAAO,IAAI,QAAc,CAAC,YAAW;AACnC,aAAK,KAAK,YAAY,WAAW,CAAA,CAAE;AACnC,gBAAO;MACT,CAAC;WACI;AACL,aAAO,OAAO;AACd,WAAK,SAAS,OAAO,MACnB,KAAA,KAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ,GAC3B,UACA,yCAAyC;AAE3C,aAAO,IAAI,QAAQ,CAAC,YAAW;AAC7B,aAAK,KAAK,WAAW,OAAO;MAC9B,CAAC;;EAEL;EAEQ,mBAAgB;AACtB,QAAI,KAAK,eAAe;AACtB,WAAK,gBAAgB;AACrB,aAAO,oBAAoB,WAAW,KAAK,aAAa;AACxD,aAAO,oBAAoB,gBAAgB,KAAK,aAAa;;AAE/D,QAAI,KAAK,YAAY;AACnB,WAAK,aAAa;AAClB,WAAK,KAAK,YAAY;;AAExB,SAAK,kBAAkB,QAAQ,CAAC,CAAC,EAAE,MAAM,GAAG,OAAM;AAChD,WAAK,kBAAkB,OAAO,EAAE;AAChC,aAAO,IAAI,MAAM,qBAAqB,CAAC;IACzC,CAAC;EACH;EAEc,YAAY,QAAgB,QAA+B;;AACvE,UAAI,WAAW,aAAa,CAAC,KAAK,WAAW;AAC3C,cAAM,IAAI,MAAM,sBAAsB;;AAExC,YAAM,YAAY,KAAK;AACvB,QAAE,KAAK;AACP,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;;AACrC,aAAK,kBAAkB,IAAI,WAAW,CAAC,SAAS,MAAM,CAAC;AACvD,YAAI,KAAK,mBAAmB;AAC1B,eAAK,kBAAkB,YAAY;YACjC,SAAS;YACT,IAAI;YACJ;YACA,QAAM,OAAA,OAAA,EACJ,SAAS,KAAK,SAAQ,GACnB,MAAM;WAEZ;eACI;AACL,WAAA,KAAA,KAAK,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,YACX;YACE,SAAS;YACT,IAAI;YACJ;YACA;cAEF,MAAA,KAAA,KAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,KAAI,EAAE;AAGjC,cAAI,CAAC,KAAK,aAAa;AACrB,aAAA,KAAA,KAAK,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,MAAK;;;MAGxB,CAAC;IACH,CAAC;;EAED,IAAI,YAAS;AACX,WAAO,KAAK;EACd;EAEA,IAAI,YAAS;AACX,WAAO,KAAK,eAAe;EAC7B;EAEA,IAAI,cAAW;AACb,WAAO,KAAK;EACd;EAEM,UAAO;;AACX,UAAI,KAAK,QAAQ;AACf,aAAK,OAAO,MAAK;;AAEnB,YAAM,KAAK,cAAa;IAC1B,CAAC;;EAEK,aAAU;;AACd,UAAI,KAAK,mBAAmB;AAC1B,cAAM,KAAK,YAAY,cAAc,CAAA,CAAE;;AAEzC,UAAI,KAAK,QAAQ;AACf,aAAK,OAAO,MAAK;;AAEnB,WAAK,iBAAgB;IACvB,CAAC;;EAMK,KACJ,MACA,SAAgB;;AAKhB,UAAI,EAAE,gBAAgB,aAAa;AACjC,cAAM,IAAI,MAAM,wCAAwC;;AAG1D,YAAM,WAAY,MAAM,KAAK,YAAY,QAAQ;QAC/C;QACA;OACD;AACD,YAAM,YAAY,YAAAC,QAAK,OAAO,SAAS,SAAS;AAChD,YAAM,YAAY,IAAI,UAAU,SAAS,SAAS;AAClD,aAAO;QACL;QACA;;IAEJ,CAAC;;EAEK,gBAAgB,aAAwB;;AAC5C,YAAM,WAAY,MAAM,KAAK,YAAY,mBAAmB;QAC1D,SAAS,YAAAA,QAAK,OAAO,YAAY,iBAAgB,CAAE;OACpD;AACD,YAAM,YAAY,YAAAA,QAAK,OAAO,SAAS,SAAS;AAChD,YAAM,YAAY,IAAI,UAAU,SAAS,SAAS;AAClD,kBAAY,aAAa,WAAW,SAAS;AAC7C,aAAO;IACT,CAAC;;EAEK,oBACJ,cAA2B;;AAE3B,YAAM,WAAY,MAAM,KAAK,YAAY,uBAAuB;QAC9D,UAAU,aAAa,IAAI,CAAC,OAAO,YAAAA,QAAK,OAAO,GAAG,iBAAgB,CAAE,CAAC;OACtE;AACD,YAAM,aAAa,SAAS,WAAW,IAAI,CAAC,MAAM,YAAAA,QAAK,OAAO,CAAC,CAAC;AAChE,YAAM,YAAY,IAAI,UAAU,SAAS,SAAS;AAClD,qBAAe,aAAa,IAAI,CAAC,IAAI,QAAO;AAC1C,WAAG,aAAa,WAAW,WAAW,GAAG,CAAC;AAC1C,eAAO;MACT,CAAC;AACD,aAAO;IACT,CAAC;;EAEK,cACJ,WAAqB;;AAErB,UAAI,EAAE,qBAAqB,aAAa;AACtC,cAAM,IAAI,MAAM,wCAAwC;;AAE1D,YAAM,WAAY,MAAM,KAAK,YAAY,iBAAiB;QACxD;OACD;AAID,aAAO;IACT,CAAC;;;AAGH,SAAS,SAAS,GAAU;AAC1B,SAAO,OAAO,MAAM;AACtB;AAEA,SAAS,mBAAmB,GAAU;AACpC,SACE,SAAS,CAAC,KAAK,iBAAiB,KAAK,OAAO,EAAE,gBAAgB;AAElE;AAEA,SAAS,SAAS,GAAU;AAC1B,SAAO,OAAO,MAAM,YAAY,MAAM;AACxC;;;ACvQA,IAAIC,aAAyC,2BAAY;AACrD,MAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,IAAG;AACpG,WAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,SAAU,GAAG,GAAG;AACnB,QAAI,OAAO,MAAM,cAAc,MAAM;AACjC,YAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kBAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAG;AACtC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,EACtF;AACJ,EAAG;AACH,IAAIC,aAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AACA,IAAI,cAA4C,SAAU,SAAS,MAAM;AACrE,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,EAAG,KAAI;AACV,UAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,UAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,cAAQ,GAAG,CAAC,GAAG;AAAA,QACX,KAAK;AAAA,QAAG,KAAK;AAAG,cAAI;AAAI;AAAA,QACxB,KAAK;AAAG,YAAE;AAAS,iBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,QACtD,KAAK;AAAG,YAAE;AAAS,cAAI,GAAG,CAAC;AAAG,eAAK,CAAC,CAAC;AAAG;AAAA,QACxC,KAAK;AAAG,eAAK,EAAE,IAAI,IAAI;AAAG,YAAE,KAAK,IAAI;AAAG;AAAA,QACxC;AACI,cAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,gBAAI;AAAG;AAAA,UAAU;AAC3G,cAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,cAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,UAAO;AACrF,cAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,gBAAI;AAAI;AAAA,UAAO;AACpE,cAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,cAAE,IAAI,KAAK,EAAE;AAAG;AAAA,UAAO;AAClE,cAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,YAAE,KAAK,IAAI;AAAG;AAAA,MACtB;AACA,WAAK,KAAK,KAAK,SAAS,CAAC;AAAA,IAC7B,SAAS,GAAG;AAAE,WAAK,CAAC,GAAG,CAAC;AAAG,UAAI;AAAA,IAAG,UAAE;AAAU,UAAI,IAAI;AAAA,IAAG;AACzD,QAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAGA,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAC9C,IAAAH,WAAUI,aAAY,MAAM;AAC5B,aAASA,YAAW,UAAU,SAAS;AACnC,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,YAAM,YAAY;AAClB,YAAM,iBAAiB,WAAY;AAC/B,cAAM,KAAK,SAAS;AAAA,MACxB;AACA,YAAM,oBAAoB,WAAY;AAClC,eAAO,cAAc,MAAM,UAAU;AACrC,cAAM,KAAK,YAAY;AAAA,MAC3B;AACA,YAAM,YAAY;AAClB,YAAM,WAAW;AACjB,aAAO;AAAA,IACX;AACA,WAAO,eAAeA,YAAW,WAAW,aAAa;AAAA,MACrD,KAAK,WAAY;AACb,eAAO,KAAK,UAAU,aAAa;AAAA,MACvC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,YAAW,WAAW,aAAa;AAAA,MACrD,KAAK,WAAY;AACb,eAAO,KAAK,UAAU,aAAa;AAAA,MACvC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,aAAOD,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,QAAQ;AACZ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,YAAY,IAAI,OAAO,KAAK,WAAW,KAAK,QAAQ;AACzD,mBAAK,UAAU,GAAG,WAAW,KAAK,cAAc;AAChD,mBAAK,UAAU,GAAG,cAAc,KAAK,iBAAiB;AACtD,mBAAK,aAAa,OAAO,YAAY,WAAY;AAC7C,oBAAIE,KAAI;AAER,sBAAM,MAAMA,MAAK,MAAM,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,OAAO;AACzI,wBAAM,kBAAkB;AAAA,gBAC5B;AAAA,cACJ,GAAG,GAAG;AACN,qBAAO,CAAC,GAAa,KAAK,UAAU,QAAQ,CAAC;AAAA,YACjD,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAD,YAAW,UAAU,aAAa,WAAY;AAC1C,aAAOD,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,mBAAK,UAAU,mBAAmB,SAAS;AAC3C,mBAAK,UAAU,mBAAmB,YAAY;AAC9C,qBAAO,CAAC,GAAa,KAAK,UAAU,WAAW,CAAC;AAAA,YACpD,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAC,YAAW,UAAU,kBAAkB,SAAU,aAAa;AAC1D,aAAOD,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,UAAU,gBAAgB,WAAW,CAAC;AAAA,YACpE,KAAK;AAAG,qBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAC,YAAW,UAAU,sBAAsB,SAAU,cAAc;AAC/D,aAAOD,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,UAAU,oBAAoB,YAAY,CAAC;AAAA,YACzE,KAAK;AAAG,qBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAC,YAAW,UAAU,cAAc,SAAU,MAAM,SAAS;AACxD,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAO;AAC3C,aAAOD,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,UAAU,KAAK,MAAM,OAAO,CAAC;AAAA,YAC3D,KAAK;AACD,0BAAa,GAAG,KAAK,EAAG;AACxB,qBAAO,CAAC,GAAc,WAAW,KAAK,SAAS,CAAC;AAAA,UACxD;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOC;AAAA,EACX,EAAE,YAAa;AAAA;AACf,IAAO,cAAQ;;;AC7Kf,IAAIE,aAAyC,2BAAY;AACrD,MAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,IAAG;AACpG,WAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,SAAU,GAAG,GAAG;AACnB,QAAI,OAAO,MAAM,cAAc,MAAM;AACjC,YAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kBAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAG;AACtC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,EACtF;AACJ,EAAG;AACH,IAAIC,aAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AACA,IAAIC,eAA4C,SAAU,SAAS,MAAM;AACrE,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,EAAG,KAAI;AACV,UAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,UAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,cAAQ,GAAG,CAAC,GAAG;AAAA,QACX,KAAK;AAAA,QAAG,KAAK;AAAG,cAAI;AAAI;AAAA,QACxB,KAAK;AAAG,YAAE;AAAS,iBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,QACtD,KAAK;AAAG,YAAE;AAAS,cAAI,GAAG,CAAC;AAAG,eAAK,CAAC,CAAC;AAAG;AAAA,QACxC,KAAK;AAAG,eAAK,EAAE,IAAI,IAAI;AAAG,YAAE,KAAK,IAAI;AAAG;AAAA,QACxC;AACI,cAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,gBAAI;AAAG;AAAA,UAAU;AAC3G,cAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,cAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,UAAO;AACrF,cAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,gBAAI;AAAI;AAAA,UAAO;AACpE,cAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,cAAE,IAAI,KAAK,EAAE;AAAG;AAAA,UAAO;AAClE,cAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,YAAE,KAAK,IAAI;AAAG;AAAA,MACtB;AACA,WAAK,KAAK,KAAK,SAAS,CAAC;AAAA,IAC7B,SAAS,GAAG;AAAE,WAAK,CAAC,GAAG,CAAC;AAAG,UAAI;AAAA,IAAG,UAAE;AAAU,UAAI,IAAI;AAAA,IAAG;AACzD,QAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAEA,IAAI;AAAA;AAAA,EAAkC,SAAU,QAAQ;AACpD,IAAAJ,WAAUK,mBAAkB,MAAM;AAClC,aAASA,kBAAiB,UAAU,SAAS;AACzC,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,YAAM,YAAY;AAClB,YAAM,WAAW;AACjB,aAAO;AAAA,IACX;AACA,WAAO,eAAeA,kBAAiB,WAAW,aAAa;AAAA,MAC3D,KAAK,WAAY;AACb,eAAO,KAAK,UAAU;AAAA,MAC1B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,kBAAiB,WAAW,aAAa;AAAA,MAC3D,KAAK,WAAY;AACb,eAAO,KAAK,UAAU;AAAA,MAC1B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,kBAAiB,UAAU,UAAU,WAAY;AAC7C,aAAOF,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,iBAAG,KAAK,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AACxB,kBAAI,KAAK,WAAW;AAChB,sBAAM,IAAI,MAAM,0BAA0B;AAAA,cAC9C;AACA,qBAAO,CAAC,GAAa,KAAK,UAAU,QAAQ,CAAC;AAAA,YACjD,KAAK;AACD,iBAAG,KAAK;AACR,mBAAK,KAAK,SAAS;AACnB,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,oBAAM,GAAG,KAAK;AACd,mBAAK,KAAK,YAAY;AACtB,oBAAM;AAAA,YACV,KAAK;AAAG,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAC,kBAAiB,UAAU,aAAa,WAAY;AAChD,aAAOF,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,UAAU,WAAW,CAAC;AAAA,YACpD,KAAK;AACD,iBAAG,KAAK;AACR,mBAAK,KAAK,YAAY;AACtB,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAC,kBAAiB,UAAU,kBAAkB,SAAU,aAAa;AAChE,aAAOF,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,UAAU,gBAAgB,aAAa,KAAK,QAAQ,CAAC;AAAA,YACnF,KAAK;AAAG,qBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAC,kBAAiB,UAAU,sBAAsB,SAAU,cAAc;AACrE,aAAOF,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,UAAU,oBAAoB,cAAc,KAAK,QAAQ,CAAC;AAAA,YACxF,KAAK;AAAG,qBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAC,kBAAiB,UAAU,cAAc,SAAU,MAAM;AACrD,aAAOF,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,kBAAI,EAAE,gBAAgB,aAAa;AAC/B,sBAAM,IAAI,MAAM,wCAAwC;AAAA,cAC5D;AACA,qBAAO,CAAC,GAAa,KAAK,UAAU,YAAY,IAAI,CAAC;AAAA,YACzD,KAAK;AACD,0BAAa,GAAG,KAAK,EAAG;AACxB,qBAAO,CAAC,GAAc,SAAS;AAAA,UACvC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOC;AAAA,EACX,EAAE,YAAa;AAAA;AACf,IAAO,oBAAQ;;;AJrKf,IAAIC,aAAyC,2BAAY;AACrD,MAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,IAAG;AACpG,WAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,SAAU,GAAG,GAAG;AACnB,QAAI,OAAO,MAAM,cAAc,MAAM;AACjC,YAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kBAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAG;AACtC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,EACtF;AACJ,EAAG;AACH,IAAIC,aAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AACA,IAAIC,eAA4C,SAAU,SAAS,MAAM;AACrE,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,EAAG,KAAI;AACV,UAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,UAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,cAAQ,GAAG,CAAC,GAAG;AAAA,QACX,KAAK;AAAA,QAAG,KAAK;AAAG,cAAI;AAAI;AAAA,QACxB,KAAK;AAAG,YAAE;AAAS,iBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,QACtD,KAAK;AAAG,YAAE;AAAS,cAAI,GAAG,CAAC;AAAG,eAAK,CAAC,CAAC;AAAG;AAAA,QACxC,KAAK;AAAG,eAAK,EAAE,IAAI,IAAI;AAAG,YAAE,KAAK,IAAI;AAAG;AAAA,QACxC;AACI,cAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,gBAAI;AAAG;AAAA,UAAU;AAC3G,cAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,cAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,UAAO;AACrF,cAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,gBAAI;AAAI;AAAA,UAAO;AACpE,cAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,cAAE,IAAI,KAAK,EAAE;AAAG;AAAA,UAAO;AAClE,cAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,YAAE,KAAK,IAAI;AAAG;AAAA,MACtB;AACA,WAAK,KAAK,KAAK,SAAS,CAAC;AAAA,IAC7B,SAAS,GAAG;AAAE,WAAK,CAAC,GAAG,CAAC;AAAG,UAAI;AAAA,IAAG,UAAE;AAAU,UAAI,IAAI;AAAA,IAAG;AACzD,QAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAIA,IAAI;AAAA;AAAA,EAAwB,SAAU,QAAQ;AAC1C,IAAAJ,WAAUK,SAAQ,MAAM;AACxB,aAASA,QAAO,QAAQ;AACpB,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,YAAM,WAAW;AACjB,YAAM,mBAAmB;AACzB,YAAM,kBAAkB;AACxB,YAAM,aAAa,WAAY;AAC3B,YAAI,MAAM,iBAAiB;AACvB,gBAAM,gBAAgB,QAAQ;AAC9B,gBAAM,kBAAkB;AAAA,QAC5B;AACA,cAAM,KAAK,WAAW,MAAM,SAAS;AAAA,MACzC;AACA,YAAM,gBAAgB,WAAY;AAC9B,YAAI,MAAM,iBAAiB;AACvB,gBAAM,gBAAgB,OAAO;AAC7B,gBAAM,kBAAkB;AAAA,QAC5B;AACA,cAAM,mBAAmB;AACzB,cAAM,KAAK,YAAY;AAAA,MAC3B;AACA,UAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAChE,cAAM,WAAW,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AAAA,MAC5E;AACA,UAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,UAAU;AACjE,cAAM,YAAY,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AAAA,MAC7E,WACS,OAAO,QAAQ;AACpB,cAAM,YAAY,OAAO;AAAA,MAC7B,OACK;AACD,cAAM,YAAY;AAAA,MACtB;AACA,aAAO;AAAA,IACX;AACA,WAAO,eAAeA,QAAO,WAAW,aAAa;AAAA,MACjD,KAAK,WAAY;AACb,YAAI;AACJ,iBAAS,KAAK,KAAK,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc;AAAA,MAC/F;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,QAAO,WAAW,eAAe;AAAA,MACnD,KAAK,WAAY;AACb,YAAI;AACJ,eAAO,CAAC,GAAG,KAAK,KAAK,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MACnF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,QAAO,WAAW,aAAa;AAAA,MACjD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,QAAO,WAAW,eAAe;AAAA,MACnD,KAAK,WAAY;AACb,eAAO;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,QAAO,UAAU,UAAU,WAAY;AACnC,aAAOF,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,QAAQ;AACZ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,KAAK,WAAW;AAChB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,kBAAI,OAAO,KAAK,cAAc,UAAU;AACpC,qBAAK,mBAAmB,IAAI,YAAW,KAAK,WAAW,KAAK,QAAQ;AAAA,cACxE,OACK;AACD,qBAAK,mBAAmB,IAAI,kBAAiB,KAAK,WAAW,KAAK,QAAQ;AAAA,cAC9E;AACA,mBAAK,iBAAiB,GAAG,WAAW,KAAK,UAAU;AACnD,mBAAK,iBAAiB,GAAG,cAAc,KAAK,aAAa;AACzD,mBAAK,iBAAiB,QAAQ;AAC9B,qBAAO,CAAC,GAAa,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACpD,sBAAM,kBAAkB,EAAE,SAAkB,OAAe;AAAA,cAC/D,CAAC,CAAC;AAAA,YACV,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAC,QAAO,UAAU,aAAa,WAAY;AACtC,aAAOF,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,kBAAkB;AACxB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,KAAK,iBAAiB,WAAW,CAAC;AAAA,YAC3D,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAC,QAAO,UAAU,kBAAkB,SAAU,aAAa;AACtD,aAAOF,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,iBAAiB,gBAAgB,WAAW,CAAC;AAAA,YAC3E,KAAK;AAAG,qBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAC,QAAO,UAAU,sBAAsB,SAAU,cAAc;AAC3D,aAAOF,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,iBAAiB,oBAAoB,YAAY,CAAC;AAAA,YAChF,KAAK;AAAG,qBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAC,QAAO,UAAU,cAAc,SAAU,MAAM,SAAS;AACpD,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAQ;AAC5C,aAAOF,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,iBAAiB,YAAY,MAAM,OAAO,CAAC;AAAA,YACzE,KAAK;AAAG,qBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAC,QAAO,UAAU,OAAO,SAAU,MAAM,SAAS;AAC7C,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAQ;AAC5C,aAAOF,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AAAG,qBAAO,CAAC,GAAa,KAAK,YAAY,MAAM,OAAO,CAAC;AAAA,YAC5D,KAAK;AAAG,qBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOC;AAAA,EACX,EAAE,sBAAAC,OAAY;AAAA;AACd,IAAO,cAAQ;", "names": ["EventEmitter", "i", "j", "import_eventemitter3", "d", "b", "WalletAdapter", "EventEmitter", "import_eventemitter3", "EventEmitter", "bs58", "__extends", "d", "b", "__awaiter", "WebAdapter", "_a", "__extends", "d", "b", "__awaiter", "__generator", "ExtensionAdapter", "__extends", "d", "b", "__awaiter", "__generator", "Salmon", "EventEmitter"]}