const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: false,
    trim: true,
    minlength: 3
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  profilePicture: {
    type: String,
    default: '/images/default-avatar.png'
  },
  phone: {
    type: String,
    sparse: true
  },
  countryCode: {
    type: String,
    default: '+1'
  },
  country: {
    type: String,
    default: ''
  },
  city: {
    type: String,
    default: ''
  },
  phoneNumber: {
    type: String,
    unique: true,
    sparse: true
  },
  walletAddress: {
    type: String,
    unique: true,
    sparse: true
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  lastLogin: {
    type: Date
  },
  testWallet: {
    publicKey: String,
    secretKey: String,
    isTestWallet: Boolean,
    balance: Number
  },
  notificationPreferences: {
    emailNotifications: {
      type: Boolean,
      default: true
    },
    smsNotifications: {
      type: Boolean,
      default: true
    },
    pushNotifications: {
      type: Boolean,
      default: false
    }
  }
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Add static methods with error handling
userSchema.statics.findByEmail = async function(email) {
  try {
    return await this.findOne({ email }).maxTimeMS(15000);
  } catch (error) {
    console.error(`Error finding user by email ${email}:`, error.message);
    return null;
  }
};

userSchema.statics.findByWalletAddress = async function(walletAddress) {
  try {
    return await this.findOne({ walletAddress }).maxTimeMS(15000);
  } catch (error) {
    console.error(`Error finding user by wallet address ${walletAddress}:`, error.message);
    return null;
  }
};

userSchema.statics.findLatestUser = async function() {
  try {
    return await this.findOne().sort({ createdAt: -1 }).maxTimeMS(15000);
  } catch (error) {
    console.error('Error finding latest user:', error.message);
    return null;
  }
};

const User = mongoose.model('User', userSchema);

module.exports = User;