import React, { useRef, useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Navbar from '../components/Navbar.jsx';
import TokenCard from '../components/TokenCard.jsx';
import TrendingTokenCard from '../components/TrendingTokenCard.jsx';
import bitcoinLogo from '../assets/bitcoin-logo.png';
import { API_URL } from '../config/constants';

const HomePage = () => {
  const trendingTokensRef = useRef(null);
  const trendingTrackRef = useRef(null);
  const [trendingTokens, setTrendingTokens] = useState([]);
  const [exploreTokens, setExploreTokens] = useState([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isScrolling, setIsScrolling] = useState(false);

  useEffect(() => {
    fetch(`${API_URL}/tokens/trending`)
      .then(res => res.json())
      .then(data => setTrendingTokens(data))
      .catch(() => setTrendingTokens([
        { id: 101, name: 'MemeRocket', symbol: 'MRKT', price: 0.12, change: '+25%', volume: 120000, holders: 5000, marketCap: 1000000, description: 'A trending meme coin!', volume_24h: 120000, image: bitcoinLogo },
        { id: 102, name: 'SolanaPepe', symbol: 'SPEPE', price: 0.05, change: '+18%', volume: 95000, holders: 3200, marketCap: 800000, description: 'Pepe on Solana!', volume_24h: 95000, image: bitcoinLogo },
        { id: 103, name: 'DogeCoin2', symbol: 'DOGE2', price: 0.08, change: '+12%', volume: 85000, holders: 2800, marketCap: 650000, description: 'The next Doge!', volume_24h: 85000, image: bitcoinLogo },
        { id: 104, name: 'SolanaShib', symbol: 'SSHIB', price: 0.03, change: '+8%', volume: 75000, holders: 2100, marketCap: 450000, description: 'Shiba on Solana!', volume_24h: 75000, image: bitcoinLogo },
        { id: 105, name: 'MoonToken', symbol: 'MOON', price: 0.15, change: '+30%', volume: 150000, holders: 6200, marketCap: 1200000, description: 'To the moon!', volume_24h: 150000, image: bitcoinLogo },
        { id: 106, name: 'RocketFuel', symbol: 'FUEL', price: 0.06, change: '+15%', volume: 65000, holders: 1800, marketCap: 380000, description: 'Fuel for rockets!', volume_24h: 65000, image: bitcoinLogo },
        { id: 107, name: 'SolPepe', symbol: 'SOLPEPE', price: 0.04, change: '+20%', volume: 90000, holders: 3500, marketCap: 720000, description: 'Pepe meets Solana!', volume_24h: 90000, image: bitcoinLogo },
        { id: 108, name: 'DiamondHands', symbol: 'DIAMOND', price: 0.25, change: '+45%', volume: 200000, holders: 8000, marketCap: 1800000, description: 'Diamond hands only!', volume_24h: 200000, image: bitcoinLogo },
        { id: 109, name: 'SolanaInu', symbol: 'SOLINU', price: 0.02, change: '+10%', volume: 55000, holders: 1500, marketCap: 320000, description: 'Inu on Solana!', volume_24h: 55000, image: bitcoinLogo },
        { id: 110, name: 'CryptoKing', symbol: 'KING', price: 0.18, change: '+35%', volume: 180000, holders: 7200, marketCap: 1500000, description: 'King of crypto!', volume_24h: 180000, image: bitcoinLogo },
      ]));
  }, []);

  useEffect(() => {
    fetch(`${API_URL}/tokens/explore`)
      .then(res => res.json())
      .then(data => setExploreTokens(data))
      .catch(() => setExploreTokens([
        { id: 201, name: 'FreshMeme', symbol: 'FMEME', price: 0.01, change: '+2%', volume: 10000, holders: 100, marketCap: 20000, description: 'A new meme coin!', created_at: new Date().toISOString(), image: bitcoinLogo, creator: 'User' },
        { id: 202, name: 'SolanaDog', symbol: 'SDOG', price: 0.03, change: '+5%', volume: 20000, holders: 250, marketCap: 60000, description: 'Dog coin on Solana!', created_at: new Date().toISOString(), image: bitcoinLogo, creator: 'User' },
      ]));
  }, []);

  // Auto-carousel functionality
  useEffect(() => {
    if (!isAutoPlaying || trendingTokens.length <= 3) return;

    const interval = setInterval(() => {
      setCurrentSlide(prev => {
        const maxSlide = Math.max(0, trendingTokens.length - 3);
        const nextSlide = prev >= maxSlide ? 0 : prev + 1;

        if (trendingTrackRef.current) {
          const cardWidth = 320; // Width of each card + gap
          const translateX = -nextSlide * cardWidth;
          // Use transform for hardware-accelerated animation
          trendingTrackRef.current.style.transform = `translateX(${translateX}px)`;
        }
        return nextSlide;
      });
    }, 4000); // Auto-scroll every 4 seconds

    return () => clearInterval(interval);
  }, [isAutoPlaying, trendingTokens.length]);

  const scrollLeft = () => {
    if (isScrolling) return; // Prevent multiple rapid clicks

    setIsScrolling(true);
    setIsAutoPlaying(false);

    if (trendingTrackRef.current) {
      const cardWidth = 320;
      const newSlide = Math.max(0, currentSlide - 1);
      setCurrentSlide(newSlide);

      const translateX = -newSlide * cardWidth;
      trendingTrackRef.current.style.transform = `translateX(${translateX}px)`;
    }

    // Reset scrolling state and resume auto-play
    setTimeout(() => {
      setIsScrolling(false);
      setIsAutoPlaying(true);
    }, 1000);
  };

  const scrollRight = () => {
    if (isScrolling) return; // Prevent multiple rapid clicks

    setIsScrolling(true);
    setIsAutoPlaying(false);

    if (trendingTrackRef.current) {
      const cardWidth = 320;
      const maxSlide = Math.max(0, trendingTokens.length - 3);
      const newSlide = Math.min(maxSlide, currentSlide + 1);
      setCurrentSlide(newSlide);

      const translateX = -newSlide * cardWidth;
      trendingTrackRef.current.style.transform = `translateX(${translateX}px)`;
    }

    // Reset scrolling state and resume auto-play
    setTimeout(() => {
      setIsScrolling(false);
      setIsAutoPlaying(true);
    }, 1000);
  };

  return (
    <div className="home-page">
      <Navbar />

      <div className="home-content">
        <div className="trending-section">
          <div className="trending-header-container">
            <h2 className="trending-header">Now Trending</h2>
            <div className="carousel-controls">
              <button
                className="carousel-btn carousel-btn-prev"
                onClick={scrollLeft}
                aria-label="Previous tokens"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="15,18 9,12 15,6"></polyline>
                </svg>
              </button>
              <div className="carousel-indicators">
                {Array.from({ length: Math.max(1, trendingTokens.length - 2) }).map((_, index) => (
                  <div
                    key={index}
                    className={`carousel-indicator ${index === currentSlide ? 'active' : ''}`}
                  />
                ))}
              </div>
              <button
                className="carousel-btn carousel-btn-next"
                onClick={scrollRight}
                aria-label="Next tokens"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="9,18 15,12 9,6"></polyline>
                </svg>
              </button>
            </div>
          </div>

          <div
            className="trending-carousel"
            ref={trendingTokensRef}
            onMouseEnter={() => setIsAutoPlaying(false)}
            onMouseLeave={() => setIsAutoPlaying(true)}
          >
            <div className="trending-carousel-track" ref={trendingTrackRef}>
              {trendingTokens.map(token => (
                <TrendingTokenCard key={token.id} token={token} />
              ))}
            </div>
          </div>
        </div>

        <div className="action-buttons">
          <Link to="/create-token" className="create-token-btn">Create Token</Link>
          <button className="manage-tokens-btn">Manage Tokens</button>
        </div>

        <div className="explore-section">
          <h2>Explore</h2>
          
          <div className="search-section">
            <div className="search-container">
              <input type="text" placeholder="Search for tokens here" className="search-input" />
            </div>
            <div className="filter-options">
              <button className="filter-option-btn new">New</button>
              <button className="filter-option-btn trending">Trending</button>
            </div>
          </div>
          
          <div className="explore-tokens">
            {exploreTokens.map(token => (
              <TokenCard key={token.id} token={token} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
