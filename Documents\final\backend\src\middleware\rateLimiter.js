const rateLimit = require('express-rate-limit');
const { redisClient } = require('../config/redis');

// Login rate limiter - stricter in production
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'production' ? 5 : 20, // 5 attempts in production, 20 in development
  message: 'Too many login attempts. Please try again after 15 minutes.',
  standardHeaders: true,
  legacyHeaders: false
});

// Password reset rate limiter - 3 attempts per hour
const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 attempts
  message: 'Too many password reset attempts. Please try again after an hour.',
  standardHeaders: true,
  legacyHeaders: false
});

// MFA verification rate limiter - 3 attempts per 5 minutes
const mfaLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 3, // 3 attempts
  message: 'Too many MFA verification attempts. Please try again after 5 minutes.',
  standardHeaders: true,
  legacyHeaders: false
});

// OTP request rate limiter - 5 attempts per 15 minutes
const otpRequestLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts
  message: 'Too many OTP requests. Please try again after 15 minutes.',
  standardHeaders: true,
  legacyHeaders: false
});

module.exports = {
  loginLimiter,
  passwordResetLimiter,
  mfaLimiter,
  otpRequestLimiter
};