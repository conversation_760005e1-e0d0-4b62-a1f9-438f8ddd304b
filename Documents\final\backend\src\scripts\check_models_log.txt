Check Models Log - 2025-04-19T16:39:43.940Z

Starting model check...
Checking MongoDB User model...
User model: function
User schema: defined
User schema paths: username, email, password, profilePicture, phone, countryCode, country, city, phoneNumber, walletAddress, isVerified, role, createdAt, lastLogin, testWallet.publicKey, testWallet.secretKey, testWallet.isTestWallet, testWallet.balance, notificationPreferences.emailNotifications, notificationPreferences.smsNotifications, notificationPreferences.pushNotifications, _id, __v

Checking PostgreSQL models...
Order model: function
Order attributes: id, type, walletAddress, tokenMint, tokenAmount, price, totalValue, status, filledAmount, remainingAmount, expiresAt, metadata, createdAt, updatedAt
UserBalance model: function
UserBalance attributes: id, userId, solBalance, tokenBalances, pendingDeposits, pendingWithdrawals, lastUpdated, createdAt, updatedAt

Model check completed successfully
Log file written to: C:\Users\<USER>\Documents\project V\wb-swap\backend\src\scripts\check_models_log.txt
