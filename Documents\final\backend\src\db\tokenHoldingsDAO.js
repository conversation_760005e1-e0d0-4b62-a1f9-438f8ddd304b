/**
 * Data Access Object for Token Holdings
 * Handles database operations for token holdings
 */
const { pgPool } = require('../config/postgres');

class TokenHoldingsDAO {
  /**
   * Get top token holders for a specific symbol
   * @param {string} symbol - Token symbol
   * @param {number} limit - Maximum number of holders to return
   * @param {number} offset - Offset for pagination
   * @returns {Promise<Array>} - Array of token holders
   */
  static async getTopHoldersBySymbol(symbol, limit = 20, offset = 0) {
    try {
      const query = `
        SELECT 
          user_id, 
          username, 
          symbol, 
          balance, 
          value, 
          percentage,
          last_updated
        FROM 
          token_holdings
        WHERE 
          symbol = $1
        ORDER BY 
          balance DESC
        LIMIT $2 OFFSET $3
      `;
      
      const result = await pgPool.query(query, [symbol, limit, offset]);
      return result.rows;
    } catch (error) {
      console.error('Error getting top holders by symbol:', error);
      throw error;
    }
  }

  /**
   * Get token holdings for a specific user
   * @param {string} userId - User ID
   * @returns {Promise<Array>} - Array of token holdings
   */
  static async getHoldingsByUserId(userId) {
    try {
      const query = `
        SELECT 
          user_id, 
          username, 
          symbol, 
          balance, 
          value, 
          percentage,
          last_updated
        FROM 
          token_holdings
        WHERE 
          user_id = $1
        ORDER BY 
          value DESC
      `;
      
      const result = await pgPool.query(query, [userId]);
      return result.rows;
    } catch (error) {
      console.error('Error getting holdings by user ID:', error);
      throw error;
    }
  }

  /**
   * Get token supply information
   * @param {string} symbol - Token symbol
   * @returns {Promise<Object>} - Token supply information
   */
  static async getTokenSupply(symbol) {
    try {
      const query = `
        SELECT 
          symbol, 
          total_supply, 
          circulating_supply, 
          last_updated
        FROM 
          token_supply
        WHERE 
          symbol = $1
      `;
      
      const result = await pgPool.query(query, [symbol]);
      return result.rows[0];
    } catch (error) {
      console.error('Error getting token supply:', error);
      throw error;
    }
  }

  /**
   * Update or insert token holdings for a user
   * @param {Object} holding - Token holding data
   * @returns {Promise<Object>} - Updated token holding
   */
  static async upsertHolding(holding) {
    const { user_id, username, symbol, balance, value, percentage } = holding;
    
    try {
      const query = `
        INSERT INTO token_holdings 
          (user_id, username, symbol, balance, value, percentage, last_updated)
        VALUES 
          ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, symbol) 
        DO UPDATE SET 
          balance = $4,
          value = $5,
          percentage = $6,
          username = $2,
          last_updated = CURRENT_TIMESTAMP
        RETURNING *
      `;
      
      const result = await pgPool.query(query, [
        user_id, username, symbol, balance, value, percentage
      ]);
      
      return result.rows[0];
    } catch (error) {
      console.error('Error upserting token holding:', error);
      throw error;
    }
  }

  /**
   * Update or insert token supply information
   * @param {Object} supply - Token supply data
   * @returns {Promise<Object>} - Updated token supply
   */
  static async upsertTokenSupply(supply) {
    const { symbol, total_supply, circulating_supply } = supply;
    
    try {
      const query = `
        INSERT INTO token_supply 
          (symbol, total_supply, circulating_supply, last_updated)
        VALUES 
          ($1, $2, $3, CURRENT_TIMESTAMP)
        ON CONFLICT (symbol) 
        DO UPDATE SET 
          total_supply = $2,
          circulating_supply = $3,
          last_updated = CURRENT_TIMESTAMP
        RETURNING *
      `;
      
      const result = await pgPool.query(query, [
        symbol, total_supply, circulating_supply
      ]);
      
      return result.rows[0];
    } catch (error) {
      console.error('Error upserting token supply:', error);
      throw error;
    }
  }

  /**
   * Delete a token holding
   * @param {string} userId - User ID
   * @param {string} symbol - Token symbol
   * @returns {Promise<boolean>} - True if deleted successfully
   */
  static async deleteHolding(userId, symbol) {
    try {
      const query = `
        DELETE FROM token_holdings
        WHERE user_id = $1 AND symbol = $2
        RETURNING id
      `;
      
      const result = await pgPool.query(query, [userId, symbol]);
      return result.rowCount > 0;
    } catch (error) {
      console.error('Error deleting token holding:', error);
      throw error;
    }
  }

  /**
   * Get total number of holders for a token
   * @param {string} symbol - Token symbol
   * @returns {Promise<number>} - Number of holders
   */
  static async getHolderCount(symbol) {
    try {
      const query = `
        SELECT COUNT(*) as holder_count
        FROM token_holdings
        WHERE symbol = $1
      `;
      
      const result = await pgPool.query(query, [symbol]);
      return parseInt(result.rows[0].holder_count, 10);
    } catch (error) {
      console.error('Error getting holder count:', error);
      throw error;
    }
  }

  /**
   * Seed sample data for testing
   * @returns {Promise<void>}
   */
  static async seedSampleData() {
    try {
      // Sample token supply
      await this.upsertTokenSupply({
        symbol: 'SOL',
        total_supply: 555000000,
        circulating_supply: 410000000
      });
      
      await this.upsertTokenSupply({
        symbol: 'ETH',
        total_supply: 120000000,
        circulating_supply: 118000000
      });
      
      await this.upsertTokenSupply({
        symbol: 'USDC',
        total_supply: 45000000000,
        circulating_supply: 45000000000
      });
      
      // Sample holders
      const sampleHolders = [
        {
          user_id: 'user1',
          username: 'whale_investor',
          symbol: 'SOL',
          balance: 1250000,
          value: 134250000,
          percentage: 0.3049
        },
        {
          user_id: 'user2',
          username: 'crypto_king',
          symbol: 'SOL',
          balance: 850000,
          value: 91290000,
          percentage: 0.2073
        },
        {
          user_id: 'user3',
          username: 'hodl_master',
          symbol: 'SOL',
          balance: 425000,
          value: 45645000,
          percentage: 0.1037
        },
        {
          user_id: 'user4',
          username: 'diamond_hands',
          symbol: 'SOL',
          balance: 210000,
          value: 22554000,
          percentage: 0.0512
        },
        {
          user_id: 'user5',
          username: 'satoshi_fan',
          symbol: 'SOL',
          balance: 175000,
          value: 18795000,
          percentage: 0.0427
        },
        {
          user_id: 'user6',
          username: 'crypto_newbie',
          symbol: 'SOL',
          balance: 85000,
          value: 9129000,
          percentage: 0.0207
        },
        {
          user_id: 'user7',
          username: 'blockchain_believer',
          symbol: 'SOL',
          balance: 42500,
          value: 4564500,
          percentage: 0.0104
        },
        {
          user_id: 'user8',
          username: 'token_trader',
          symbol: 'SOL',
          balance: 21000,
          value: 2255400,
          percentage: 0.0051
        },
        {
          user_id: 'user9',
          username: 'crypto_enthusiast',
          symbol: 'SOL',
          balance: 10500,
          value: 1127700,
          percentage: 0.0026
        },
        {
          user_id: 'user10',
          username: 'defi_degen',
          symbol: 'SOL',
          balance: 5250,
          value: 563850,
          percentage: 0.0013
        }
      ];
      
      for (const holder of sampleHolders) {
        await this.upsertHolding(holder);
      }
      
      console.log('Sample token holdings data seeded successfully');
    } catch (error) {
      console.error('Error seeding sample data:', error);
      throw error;
    }
  }
}

module.exports = TokenHoldingsDAO;
