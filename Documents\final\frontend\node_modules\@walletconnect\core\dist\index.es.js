import ki,{EventEmitter as ee}from"events";import{HEARTBEAT_EVENTS as ne,HeartBeat as Ui}from"@walletconnect/heartbeat";import Fi from"@walletconnect/keyvaluestorage";import{generateChildLogger as N,getLoggerContext as U,pino as Mi,getDefaultLoggerOptions as He,generatePlatformLogger as <PERSON>}from"@walletconnect/logger";import{IMessageTracker as Bi,IPublisher as ji,ISubscriber as Vi,<PERSON><PERSON><PERSON><PERSON> as qi,IStore as Gi,IJsonRpcHistory as Hi,IExpirer as Yi,IVerify as Ji,IEchoClient as Xi,IEventClient as Wi,ICore as Zi}from"@walletconnect/types";import{ONE_DAY as te,SIX_HOURS as Qi,THIRTY_DAYS as Ee,FIVE_SECONDS as we,THIRTY_SECONDS as Ie,toMiliseconds as R,ONE_MINUTE as Ye,ONE_SECOND as Z,Watch as es,FIVE_MINUTES as ye,fromMiliseconds as Je}from"@walletconnect/time";import{safeJsonStringify as ts,safeJsonParse as Xe}from"@walletconnect/safe-json";import*as be from"@walletconnect/relay-auth";import{decodeJWT as We}from"@walletconnect/relay-auth";import{mapToObj as Ze,objToMap as Qe,getInternalError as b,generateRandomBytes32 as Te,generateKeyPair as is,deriveSymKey as ss,hashKey as rs,validateEncoding as ns,isTypeTwoEnvelope as et,encodeTypeTwoEnvelope as os,isTypeOneEnvelope as tt,encrypt as as,validateDecoding as cs,decodeTypeTwoEnvelope as hs,decrypt as ls,deserialize as it,decodeTypeByte as us,BASE64 as st,BASE16 as ds,hashMessage as Ce,getRelayProtocolApi as oe,getRelayProtocolName as De,isUndefined as Pe,createExpiringPromise as W,getSdkError as ae,sleep as ps,isAndroid as gs,getAppId as rt,isIos as ys,isOnline as nt,calcExpiry as ce,isNode as ot,formatRelayRpcUrl as bs,subscribeToNetworkChange as Ds,isProposalStruct as ms,isSessionStruct as vs,parseExpirerTarget as fs,TYPE_1 as _s,formatUri as at,parseUri as ct,createDelayedPromise as Es,engineEvent as Se,isExpired as ht,isValidParams as Re,isValidUrl as ws,isValidString as Is,formatTopicTarget as Ts,formatIdTarget as Cs,isTestRun as lt,isBrowser as Ps,verifyP256Jwt as Ss,uuidv4 as ut,formatUA as Rs,getAppMetadata as xs}from"@walletconnect/utils";import{toString as Os}from"uint8arrays";import{JsonRpcProvider as As}from"@walletconnect/jsonrpc-provider";import{getBigIntRpcId as dt,isJsonRpcRequest as pt,isJsonRpcResponse as gt,formatJsonRpcResult as yt,formatJsonRpcRequest as bt,formatJsonRpcError as Ns,isJsonRpcResult as $s,isJsonRpcError as Dt}from"@walletconnect/jsonrpc-utils";import zs from"@walletconnect/jsonrpc-ws-connection";import Ls from"lodash.isequal";import{getDocument as ks}from"@walletconnect/window-getters";const xe="wc",Oe=2,he="core",B=`${xe}@2:${he}:`,mt={name:he,logger:"error"},vt={database:":memory:"},ft="crypto",Ae="client_ed25519_seed",_t=te,Et="keychain",wt="0.3",It="messages",Tt="0.3",Ne=Qi,Ct="publisher",Pt="irn",St="error",$e="wss://relay.walletconnect.org",Rt="relayer",T={message:"relayer_message",message_ack:"relayer_message_ack",connect:"relayer_connect",disconnect:"relayer_disconnect",error:"relayer_error",connection_stalled:"relayer_connection_stalled",transport_closed:"relayer_transport_closed",publish:"relayer_publish"},xt="_subscription",L={payload:"payload",connect:"connect",disconnect:"disconnect",error:"error"},Ot=.1,Us={database:":memory:"},me="2.19.0",Fs=1e4,Q={link_mode:"link_mode",relay:"relay"},At="0.3",Nt="WALLETCONNECT_CLIENT_ID",ze="WALLETCONNECT_LINK_MODE_APPS",$={created:"subscription_created",deleted:"subscription_deleted",expired:"subscription_expired",disabled:"subscription_disabled",sync:"subscription_sync",resubscribed:"subscription_resubscribed"},Ms=Ee,$t="subscription",zt="0.3",Lt=we*1e3,kt="pairing",Ut="0.3",Ks=Ee,ie={wc_pairingDelete:{req:{ttl:te,prompt:!1,tag:1e3},res:{ttl:te,prompt:!1,tag:1001}},wc_pairingPing:{req:{ttl:Ie,prompt:!1,tag:1002},res:{ttl:Ie,prompt:!1,tag:1003}},unregistered_method:{req:{ttl:te,prompt:!1,tag:0},res:{ttl:te,prompt:!1,tag:0}}},se={create:"pairing_create",expire:"pairing_expire",delete:"pairing_delete",ping:"pairing_ping"},F={created:"history_created",updated:"history_updated",deleted:"history_deleted",sync:"history_sync"},Ft="history",Mt="0.3",Kt="expirer",M={created:"expirer_created",deleted:"expirer_deleted",expired:"expirer_expired",sync:"expirer_sync"},Bt="0.3",Bs=te,jt="verify-api",js="https://verify.walletconnect.com",Vt="https://verify.walletconnect.org",le=Vt,qt=`${le}/v3`,Gt=[js,Vt],Ht="echo",Yt="https://echo.walletconnect.com",Vs="event-client",q={pairing_started:"pairing_started",pairing_uri_validation_success:"pairing_uri_validation_success",pairing_uri_not_expired:"pairing_uri_not_expired",store_new_pairing:"store_new_pairing",subscribing_pairing_topic:"subscribing_pairing_topic",subscribe_pairing_topic_success:"subscribe_pairing_topic_success",existing_pairing:"existing_pairing",pairing_not_expired:"pairing_not_expired",emit_inactive_pairing:"emit_inactive_pairing",emit_session_proposal:"emit_session_proposal",subscribing_to_pairing_topic:"subscribing_to_pairing_topic"},J={no_wss_connection:"no_wss_connection",no_internet_connection:"no_internet_connection",malformed_pairing_uri:"malformed_pairing_uri",active_pairing_already_exists:"active_pairing_already_exists",subscribe_pairing_topic_failure:"subscribe_pairing_topic_failure",pairing_expired:"pairing_expired",proposal_expired:"proposal_expired",proposal_listener_not_found:"proposal_listener_not_found"},qs={session_approve_started:"session_approve_started",proposal_not_expired:"proposal_not_expired",session_namespaces_validation_success:"session_namespaces_validation_success",create_session_topic:"create_session_topic",subscribing_session_topic:"subscribing_session_topic",subscribe_session_topic_success:"subscribe_session_topic_success",publishing_session_approve:"publishing_session_approve",session_approve_publish_success:"session_approve_publish_success",store_session:"store_session",publishing_session_settle:"publishing_session_settle",session_settle_publish_success:"session_settle_publish_success"},Gs={no_internet_connection:"no_internet_connection",no_wss_connection:"no_wss_connection",proposal_expired:"proposal_expired",subscribe_session_topic_failure:"subscribe_session_topic_failure",session_approve_publish_failure:"session_approve_publish_failure",session_settle_publish_failure:"session_settle_publish_failure",session_approve_namespace_validation_failure:"session_approve_namespace_validation_failure",proposal_not_found:"proposal_not_found"},Hs={authenticated_session_approve_started:"authenticated_session_approve_started",authenticated_session_not_expired:"authenticated_session_not_expired",chains_caip2_compliant:"chains_caip2_compliant",chains_evm_compliant:"chains_evm_compliant",create_authenticated_session_topic:"create_authenticated_session_topic",cacaos_verified:"cacaos_verified",store_authenticated_session:"store_authenticated_session",subscribing_authenticated_session_topic:"subscribing_authenticated_session_topic",subscribe_authenticated_session_topic_success:"subscribe_authenticated_session_topic_success",publishing_authenticated_session_approve:"publishing_authenticated_session_approve",authenticated_session_approve_publish_success:"authenticated_session_approve_publish_success"},Ys={no_internet_connection:"no_internet_connection",no_wss_connection:"no_wss_connection",missing_session_authenticate_request:"missing_session_authenticate_request",session_authenticate_request_expired:"session_authenticate_request_expired",chains_caip2_compliant_failure:"chains_caip2_compliant_failure",chains_evm_compliant_failure:"chains_evm_compliant_failure",invalid_cacao:"invalid_cacao",subscribe_authenticated_session_topic_failure:"subscribe_authenticated_session_topic_failure",authenticated_session_approve_publish_failure:"authenticated_session_approve_publish_failure",authenticated_session_pending_request_not_found:"authenticated_session_pending_request_not_found"},Jt=.1,Xt="event-client",Wt=86400,Zt="https://pulse.walletconnect.org/batch";function Js(n,e){if(n.length>=255)throw new TypeError("Alphabet too long");for(var t=new Uint8Array(256),s=0;s<t.length;s++)t[s]=255;for(var i=0;i<n.length;i++){var r=n.charAt(i),o=r.charCodeAt(0);if(t[o]!==255)throw new TypeError(r+" is ambiguous");t[o]=i}var a=n.length,c=n.charAt(0),h=Math.log(a)/Math.log(256),u=Math.log(256)/Math.log(a);function g(l){if(l instanceof Uint8Array||(ArrayBuffer.isView(l)?l=new Uint8Array(l.buffer,l.byteOffset,l.byteLength):Array.isArray(l)&&(l=Uint8Array.from(l))),!(l instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(l.length===0)return"";for(var y=0,O=0,w=0,v=l.length;w!==v&&l[w]===0;)w++,y++;for(var k=(v-w)*u+1>>>0,I=new Uint8Array(k);w!==v;){for(var V=l[w],X=0,K=k-1;(V!==0||X<O)&&K!==-1;K--,X++)V+=256*I[K]>>>0,I[K]=V%a>>>0,V=V/a>>>0;if(V!==0)throw new Error("Non-zero carry");O=X,w++}for(var Y=k-O;Y!==k&&I[Y]===0;)Y++;for(var ge=c.repeat(y);Y<k;++Y)ge+=n.charAt(I[Y]);return ge}function m(l){if(typeof l!="string")throw new TypeError("Expected String");if(l.length===0)return new Uint8Array;var y=0;if(l[y]!==" "){for(var O=0,w=0;l[y]===c;)O++,y++;for(var v=(l.length-y)*h+1>>>0,k=new Uint8Array(v);l[y];){var I=t[l.charCodeAt(y)];if(I===255)return;for(var V=0,X=v-1;(I!==0||V<w)&&X!==-1;X--,V++)I+=a*k[X]>>>0,k[X]=I%256>>>0,I=I/256>>>0;if(I!==0)throw new Error("Non-zero carry");w=V,y++}if(l[y]!==" "){for(var K=v-w;K!==v&&k[K]===0;)K++;for(var Y=new Uint8Array(O+(v-K)),ge=O;K!==v;)Y[ge++]=k[K++];return Y}}}function A(l){var y=m(l);if(y)return y;throw new Error(`Non-${e} character`)}return{encode:g,decodeUnsafe:m,decode:A}}var Xs=Js,Ws=Xs;const Qt=n=>{if(n instanceof Uint8Array&&n.constructor.name==="Uint8Array")return n;if(n instanceof ArrayBuffer)return new Uint8Array(n);if(ArrayBuffer.isView(n))return new Uint8Array(n.buffer,n.byteOffset,n.byteLength);throw new Error("Unknown type, must be binary type")},Zs=n=>new TextEncoder().encode(n),Qs=n=>new TextDecoder().decode(n);class er{constructor(e,t,s){this.name=e,this.prefix=t,this.baseEncode=s}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class tr{constructor(e,t,s){if(this.name=e,this.prefix=t,t.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=t.codePointAt(0),this.baseDecode=s}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return ei(this,e)}}class ir{constructor(e){this.decoders=e}or(e){return ei(this,e)}decode(e){const t=e[0],s=this.decoders[t];if(s)return s.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const ei=(n,e)=>new ir({...n.decoders||{[n.prefix]:n},...e.decoders||{[e.prefix]:e}});class sr{constructor(e,t,s,i){this.name=e,this.prefix=t,this.baseEncode=s,this.baseDecode=i,this.encoder=new er(e,t,s),this.decoder=new tr(e,t,i)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const ve=({name:n,prefix:e,encode:t,decode:s})=>new sr(n,e,t,s),ue=({prefix:n,name:e,alphabet:t})=>{const{encode:s,decode:i}=Ws(t,e);return ve({prefix:n,name:e,encode:s,decode:r=>Qt(i(r))})},rr=(n,e,t,s)=>{const i={};for(let u=0;u<e.length;++u)i[e[u]]=u;let r=n.length;for(;n[r-1]==="=";)--r;const o=new Uint8Array(r*t/8|0);let a=0,c=0,h=0;for(let u=0;u<r;++u){const g=i[n[u]];if(g===void 0)throw new SyntaxError(`Non-${s} character`);c=c<<t|g,a+=t,a>=8&&(a-=8,o[h++]=255&c>>a)}if(a>=t||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o},nr=(n,e,t)=>{const s=e[e.length-1]==="=",i=(1<<t)-1;let r="",o=0,a=0;for(let c=0;c<n.length;++c)for(a=a<<8|n[c],o+=8;o>t;)o-=t,r+=e[i&a>>o];if(o&&(r+=e[i&a<<t-o]),s)for(;r.length*t&7;)r+="=";return r},C=({name:n,prefix:e,bitsPerChar:t,alphabet:s})=>ve({prefix:e,name:n,encode(i){return nr(i,s,t)},decode(i){return rr(i,s,t,n)}}),or=ve({prefix:"\0",name:"identity",encode:n=>Qs(n),decode:n=>Zs(n)});var ar=Object.freeze({__proto__:null,identity:or});const cr=C({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var hr=Object.freeze({__proto__:null,base2:cr});const lr=C({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var ur=Object.freeze({__proto__:null,base8:lr});const dr=ue({prefix:"9",name:"base10",alphabet:"0123456789"});var pr=Object.freeze({__proto__:null,base10:dr});const gr=C({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),yr=C({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var br=Object.freeze({__proto__:null,base16:gr,base16upper:yr});const Dr=C({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),mr=C({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),vr=C({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),fr=C({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),_r=C({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),Er=C({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),wr=C({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),Ir=C({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),Tr=C({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var Cr=Object.freeze({__proto__:null,base32:Dr,base32upper:mr,base32pad:vr,base32padupper:fr,base32hex:_r,base32hexupper:Er,base32hexpad:wr,base32hexpadupper:Ir,base32z:Tr});const Pr=ue({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),Sr=ue({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var Rr=Object.freeze({__proto__:null,base36:Pr,base36upper:Sr});const xr=ue({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),Or=ue({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var Ar=Object.freeze({__proto__:null,base58btc:xr,base58flickr:Or});const Nr=C({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),$r=C({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),zr=C({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),Lr=C({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var kr=Object.freeze({__proto__:null,base64:Nr,base64pad:$r,base64url:zr,base64urlpad:Lr});const ti=Array.from("\u{1F680}\u{1FA90}\u2604\u{1F6F0}\u{1F30C}\u{1F311}\u{1F312}\u{1F313}\u{1F314}\u{1F315}\u{1F316}\u{1F317}\u{1F318}\u{1F30D}\u{1F30F}\u{1F30E}\u{1F409}\u2600\u{1F4BB}\u{1F5A5}\u{1F4BE}\u{1F4BF}\u{1F602}\u2764\u{1F60D}\u{1F923}\u{1F60A}\u{1F64F}\u{1F495}\u{1F62D}\u{1F618}\u{1F44D}\u{1F605}\u{1F44F}\u{1F601}\u{1F525}\u{1F970}\u{1F494}\u{1F496}\u{1F499}\u{1F622}\u{1F914}\u{1F606}\u{1F644}\u{1F4AA}\u{1F609}\u263A\u{1F44C}\u{1F917}\u{1F49C}\u{1F614}\u{1F60E}\u{1F607}\u{1F339}\u{1F926}\u{1F389}\u{1F49E}\u270C\u2728\u{1F937}\u{1F631}\u{1F60C}\u{1F338}\u{1F64C}\u{1F60B}\u{1F497}\u{1F49A}\u{1F60F}\u{1F49B}\u{1F642}\u{1F493}\u{1F929}\u{1F604}\u{1F600}\u{1F5A4}\u{1F603}\u{1F4AF}\u{1F648}\u{1F447}\u{1F3B6}\u{1F612}\u{1F92D}\u2763\u{1F61C}\u{1F48B}\u{1F440}\u{1F62A}\u{1F611}\u{1F4A5}\u{1F64B}\u{1F61E}\u{1F629}\u{1F621}\u{1F92A}\u{1F44A}\u{1F973}\u{1F625}\u{1F924}\u{1F449}\u{1F483}\u{1F633}\u270B\u{1F61A}\u{1F61D}\u{1F634}\u{1F31F}\u{1F62C}\u{1F643}\u{1F340}\u{1F337}\u{1F63B}\u{1F613}\u2B50\u2705\u{1F97A}\u{1F308}\u{1F608}\u{1F918}\u{1F4A6}\u2714\u{1F623}\u{1F3C3}\u{1F490}\u2639\u{1F38A}\u{1F498}\u{1F620}\u261D\u{1F615}\u{1F33A}\u{1F382}\u{1F33B}\u{1F610}\u{1F595}\u{1F49D}\u{1F64A}\u{1F639}\u{1F5E3}\u{1F4AB}\u{1F480}\u{1F451}\u{1F3B5}\u{1F91E}\u{1F61B}\u{1F534}\u{1F624}\u{1F33C}\u{1F62B}\u26BD\u{1F919}\u2615\u{1F3C6}\u{1F92B}\u{1F448}\u{1F62E}\u{1F646}\u{1F37B}\u{1F343}\u{1F436}\u{1F481}\u{1F632}\u{1F33F}\u{1F9E1}\u{1F381}\u26A1\u{1F31E}\u{1F388}\u274C\u270A\u{1F44B}\u{1F630}\u{1F928}\u{1F636}\u{1F91D}\u{1F6B6}\u{1F4B0}\u{1F353}\u{1F4A2}\u{1F91F}\u{1F641}\u{1F6A8}\u{1F4A8}\u{1F92C}\u2708\u{1F380}\u{1F37A}\u{1F913}\u{1F619}\u{1F49F}\u{1F331}\u{1F616}\u{1F476}\u{1F974}\u25B6\u27A1\u2753\u{1F48E}\u{1F4B8}\u2B07\u{1F628}\u{1F31A}\u{1F98B}\u{1F637}\u{1F57A}\u26A0\u{1F645}\u{1F61F}\u{1F635}\u{1F44E}\u{1F932}\u{1F920}\u{1F927}\u{1F4CC}\u{1F535}\u{1F485}\u{1F9D0}\u{1F43E}\u{1F352}\u{1F617}\u{1F911}\u{1F30A}\u{1F92F}\u{1F437}\u260E\u{1F4A7}\u{1F62F}\u{1F486}\u{1F446}\u{1F3A4}\u{1F647}\u{1F351}\u2744\u{1F334}\u{1F4A3}\u{1F438}\u{1F48C}\u{1F4CD}\u{1F940}\u{1F922}\u{1F445}\u{1F4A1}\u{1F4A9}\u{1F450}\u{1F4F8}\u{1F47B}\u{1F910}\u{1F92E}\u{1F3BC}\u{1F975}\u{1F6A9}\u{1F34E}\u{1F34A}\u{1F47C}\u{1F48D}\u{1F4E3}\u{1F942}"),Ur=ti.reduce((n,e,t)=>(n[t]=e,n),[]),Fr=ti.reduce((n,e,t)=>(n[e.codePointAt(0)]=t,n),[]);function Mr(n){return n.reduce((e,t)=>(e+=Ur[t],e),"")}function Kr(n){const e=[];for(const t of n){const s=Fr[t.codePointAt(0)];if(s===void 0)throw new Error(`Non-base256emoji character: ${t}`);e.push(s)}return new Uint8Array(e)}const Br=ve({prefix:"\u{1F680}",name:"base256emoji",encode:Mr,decode:Kr});var jr=Object.freeze({__proto__:null,base256emoji:Br}),Vr=si,ii=128,qr=127,Gr=~qr,Hr=Math.pow(2,31);function si(n,e,t){e=e||[],t=t||0;for(var s=t;n>=Hr;)e[t++]=n&255|ii,n/=128;for(;n&Gr;)e[t++]=n&255|ii,n>>>=7;return e[t]=n|0,si.bytes=t-s+1,e}var Yr=Le,Jr=128,ri=127;function Le(n,s){var t=0,s=s||0,i=0,r=s,o,a=n.length;do{if(r>=a)throw Le.bytes=0,new RangeError("Could not decode varint");o=n[r++],t+=i<28?(o&ri)<<i:(o&ri)*Math.pow(2,i),i+=7}while(o>=Jr);return Le.bytes=r-s,t}var Xr=Math.pow(2,7),Wr=Math.pow(2,14),Zr=Math.pow(2,21),Qr=Math.pow(2,28),en=Math.pow(2,35),tn=Math.pow(2,42),sn=Math.pow(2,49),rn=Math.pow(2,56),nn=Math.pow(2,63),on=function(n){return n<Xr?1:n<Wr?2:n<Zr?3:n<Qr?4:n<en?5:n<tn?6:n<sn?7:n<rn?8:n<nn?9:10},an={encode:Vr,decode:Yr,encodingLength:on},ni=an;const oi=(n,e,t=0)=>(ni.encode(n,e,t),e),ai=n=>ni.encodingLength(n),ke=(n,e)=>{const t=e.byteLength,s=ai(n),i=s+ai(t),r=new Uint8Array(i+t);return oi(n,r,0),oi(t,r,s),r.set(e,i),new cn(n,t,e,r)};class cn{constructor(e,t,s,i){this.code=e,this.size=t,this.digest=s,this.bytes=i}}const ci=({name:n,code:e,encode:t})=>new hn(n,e,t);class hn{constructor(e,t,s){this.name=e,this.code=t,this.encode=s}digest(e){if(e instanceof Uint8Array){const t=this.encode(e);return t instanceof Uint8Array?ke(this.code,t):t.then(s=>ke(this.code,s))}else throw Error("Unknown type, must be binary type")}}const hi=n=>async e=>new Uint8Array(await crypto.subtle.digest(n,e)),ln=ci({name:"sha2-256",code:18,encode:hi("SHA-256")}),un=ci({name:"sha2-512",code:19,encode:hi("SHA-512")});var dn=Object.freeze({__proto__:null,sha256:ln,sha512:un});const li=0,pn="identity",ui=Qt,gn=n=>ke(li,ui(n)),yn={code:li,name:pn,encode:ui,digest:gn};var bn=Object.freeze({__proto__:null,identity:yn});new TextEncoder,new TextDecoder;const di={...ar,...hr,...ur,...pr,...br,...Cr,...Rr,...Ar,...kr,...jr};({...dn,...bn});function Dn(n=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?globalThis.Buffer.allocUnsafe(n):new Uint8Array(n)}function pi(n,e,t,s){return{name:n,prefix:e,encoder:{name:n,prefix:e,encode:t},decoder:{decode:s}}}const gi=pi("utf8","u",n=>"u"+new TextDecoder("utf8").decode(n),n=>new TextEncoder().encode(n.substring(1))),Ue=pi("ascii","a",n=>{let e="a";for(let t=0;t<n.length;t++)e+=String.fromCharCode(n[t]);return e},n=>{n=n.substring(1);const e=Dn(n.length);for(let t=0;t<n.length;t++)e[t]=n.charCodeAt(t);return e}),mn={utf8:gi,"utf-8":gi,hex:di.base16,latin1:Ue,ascii:Ue,binary:Ue,...di};function vn(n,e="utf8"){const t=mn[e];if(!t)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(n,"utf8"):t.decoder.decode(`${t.prefix}${n}`)}var fn=Object.defineProperty,_n=(n,e,t)=>e in n?fn(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,G=(n,e,t)=>_n(n,typeof e!="symbol"?e+"":e,t);class yi{constructor(e,t){this.core=e,this.logger=t,G(this,"keychain",new Map),G(this,"name",Et),G(this,"version",wt),G(this,"initialized",!1),G(this,"storagePrefix",B),G(this,"init",async()=>{if(!this.initialized){const s=await this.getKeyChain();typeof s<"u"&&(this.keychain=s),this.initialized=!0}}),G(this,"has",s=>(this.isInitialized(),this.keychain.has(s))),G(this,"set",async(s,i)=>{this.isInitialized(),this.keychain.set(s,i),await this.persist()}),G(this,"get",s=>{this.isInitialized();const i=this.keychain.get(s);if(typeof i>"u"){const{message:r}=b("NO_MATCHING_KEY",`${this.name}: ${s}`);throw new Error(r)}return i}),G(this,"del",async s=>{this.isInitialized(),this.keychain.delete(s),await this.persist()}),this.core=e,this.logger=N(t,this.name)}get context(){return U(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}async setKeyChain(e){await this.core.storage.setItem(this.storageKey,Ze(e))}async getKeyChain(){const e=await this.core.storage.getItem(this.storageKey);return typeof e<"u"?Qe(e):void 0}async persist(){await this.setKeyChain(this.keychain)}isInitialized(){if(!this.initialized){const{message:e}=b("NOT_INITIALIZED",this.name);throw new Error(e)}}}var En=Object.defineProperty,wn=(n,e,t)=>e in n?En(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,P=(n,e,t)=>wn(n,typeof e!="symbol"?e+"":e,t);class bi{constructor(e,t,s){this.core=e,this.logger=t,P(this,"name",ft),P(this,"keychain"),P(this,"randomSessionIdentifier",Te()),P(this,"initialized",!1),P(this,"init",async()=>{this.initialized||(await this.keychain.init(),this.initialized=!0)}),P(this,"hasKeys",i=>(this.isInitialized(),this.keychain.has(i))),P(this,"getClientId",async()=>{this.isInitialized();const i=await this.getClientSeed(),r=be.generateKeyPair(i);return be.encodeIss(r.publicKey)}),P(this,"generateKeyPair",()=>{this.isInitialized();const i=is();return this.setPrivateKey(i.publicKey,i.privateKey)}),P(this,"signJWT",async i=>{this.isInitialized();const r=await this.getClientSeed(),o=be.generateKeyPair(r),a=this.randomSessionIdentifier,c=_t;return await be.signJWT(a,i,c,o)}),P(this,"generateSharedKey",(i,r,o)=>{this.isInitialized();const a=this.getPrivateKey(i),c=ss(a,r);return this.setSymKey(c,o)}),P(this,"setSymKey",async(i,r)=>{this.isInitialized();const o=r||rs(i);return await this.keychain.set(o,i),o}),P(this,"deleteKeyPair",async i=>{this.isInitialized(),await this.keychain.del(i)}),P(this,"deleteSymKey",async i=>{this.isInitialized(),await this.keychain.del(i)}),P(this,"encode",async(i,r,o)=>{this.isInitialized();const a=ns(o),c=ts(r);if(et(a))return os(c,o?.encoding);if(tt(a)){const m=a.senderPublicKey,A=a.receiverPublicKey;i=await this.generateSharedKey(m,A)}const h=this.getSymKey(i),{type:u,senderPublicKey:g}=a;return as({type:u,symKey:h,message:c,senderPublicKey:g,encoding:o?.encoding})}),P(this,"decode",async(i,r,o)=>{this.isInitialized();const a=cs(r,o);if(et(a)){const c=hs(r,o?.encoding);return Xe(c)}if(tt(a)){const c=a.receiverPublicKey,h=a.senderPublicKey;i=await this.generateSharedKey(c,h)}try{const c=this.getSymKey(i),h=ls({symKey:c,encoded:r,encoding:o?.encoding});return Xe(h)}catch(c){this.logger.error(`Failed to decode message from topic: '${i}', clientId: '${await this.getClientId()}'`),this.logger.error(c)}}),P(this,"getPayloadType",(i,r=st)=>{const o=it({encoded:i,encoding:r});return us(o.type)}),P(this,"getPayloadSenderPublicKey",(i,r=st)=>{const o=it({encoded:i,encoding:r});return o.senderPublicKey?Os(o.senderPublicKey,ds):void 0}),this.core=e,this.logger=N(t,this.name),this.keychain=s||new yi(this.core,this.logger)}get context(){return U(this.logger)}async setPrivateKey(e,t){return await this.keychain.set(e,t),e}getPrivateKey(e){return this.keychain.get(e)}async getClientSeed(){let e="";try{e=this.keychain.get(Ae)}catch{e=Te(),await this.keychain.set(Ae,e)}return vn(e,"base16")}getSymKey(e){return this.keychain.get(e)}isInitialized(){if(!this.initialized){const{message:e}=b("NOT_INITIALIZED",this.name);throw new Error(e)}}}var In=Object.defineProperty,Tn=(n,e,t)=>e in n?In(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,H=(n,e,t)=>Tn(n,typeof e!="symbol"?e+"":e,t);class Di extends Bi{constructor(e,t){super(e,t),this.logger=e,this.core=t,H(this,"messages",new Map),H(this,"name",It),H(this,"version",Tt),H(this,"initialized",!1),H(this,"storagePrefix",B),H(this,"init",async()=>{if(!this.initialized){this.logger.trace("Initialized");try{const s=await this.getRelayerMessages();typeof s<"u"&&(this.messages=s),this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",size:this.messages.size})}catch(s){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(s)}finally{this.initialized=!0}}}),H(this,"set",async(s,i)=>{this.isInitialized();const r=Ce(i);let o=this.messages.get(s);return typeof o>"u"&&(o={}),typeof o[r]<"u"||(o[r]=i,this.messages.set(s,o),await this.persist()),r}),H(this,"get",s=>{this.isInitialized();let i=this.messages.get(s);return typeof i>"u"&&(i={}),i}),H(this,"has",(s,i)=>{this.isInitialized();const r=this.get(s),o=Ce(i);return typeof r[o]<"u"}),H(this,"del",async s=>{this.isInitialized(),this.messages.delete(s),await this.persist()}),this.logger=N(e,this.name),this.core=t}get context(){return U(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}async setRelayerMessages(e){await this.core.storage.setItem(this.storageKey,Ze(e))}async getRelayerMessages(){const e=await this.core.storage.getItem(this.storageKey);return typeof e<"u"?Qe(e):void 0}async persist(){await this.setRelayerMessages(this.messages)}isInitialized(){if(!this.initialized){const{message:e}=b("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Cn=Object.defineProperty,Pn=Object.defineProperties,Sn=Object.getOwnPropertyDescriptors,mi=Object.getOwnPropertySymbols,Rn=Object.prototype.hasOwnProperty,xn=Object.prototype.propertyIsEnumerable,Fe=(n,e,t)=>e in n?Cn(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,fe=(n,e)=>{for(var t in e||(e={}))Rn.call(e,t)&&Fe(n,t,e[t]);if(mi)for(var t of mi(e))xn.call(e,t)&&Fe(n,t,e[t]);return n},Me=(n,e)=>Pn(n,Sn(e)),j=(n,e,t)=>Fe(n,typeof e!="symbol"?e+"":e,t);class On extends ji{constructor(e,t){super(e,t),this.relayer=e,this.logger=t,j(this,"events",new ee),j(this,"name",Ct),j(this,"queue",new Map),j(this,"publishTimeout",R(Ye)),j(this,"initialPublishTimeout",R(Z*15)),j(this,"needsTransportRestart",!1),j(this,"publish",async(s,i,r)=>{var o;this.logger.debug("Publishing Payload"),this.logger.trace({type:"method",method:"publish",params:{topic:s,message:i,opts:r}});const a=r?.ttl||Ne,c=De(r),h=r?.prompt||!1,u=r?.tag||0,g=r?.id||dt().toString(),m={topic:s,message:i,opts:{ttl:a,relay:c,prompt:h,tag:u,id:g,attestation:r?.attestation,tvf:r?.tvf}},A=`Failed to publish payload, please try again. id:${g} tag:${u}`;try{const l=new Promise(async y=>{const O=({id:v})=>{m.opts.id===v&&(this.removeRequestFromQueue(v),this.relayer.events.removeListener(T.publish,O),y(m))};this.relayer.events.on(T.publish,O);const w=W(new Promise((v,k)=>{this.rpcPublish({topic:s,message:i,ttl:a,prompt:h,tag:u,id:g,attestation:r?.attestation,tvf:r?.tvf}).then(v).catch(I=>{this.logger.warn(I,I?.message),k(I)})}),this.initialPublishTimeout,`Failed initial publish, retrying.... id:${g} tag:${u}`);try{await w,this.events.removeListener(T.publish,O)}catch(v){this.queue.set(g,Me(fe({},m),{attempt:1})),this.logger.warn(v,v?.message)}});this.logger.trace({type:"method",method:"publish",params:{id:g,topic:s,message:i,opts:r}}),await W(l,this.publishTimeout,A)}catch(l){if(this.logger.debug("Failed to Publish Payload"),this.logger.error(l),(o=r?.internal)!=null&&o.throwOnFailedPublish)throw l}finally{this.queue.delete(g)}}),j(this,"on",(s,i)=>{this.events.on(s,i)}),j(this,"once",(s,i)=>{this.events.once(s,i)}),j(this,"off",(s,i)=>{this.events.off(s,i)}),j(this,"removeListener",(s,i)=>{this.events.removeListener(s,i)}),this.relayer=e,this.logger=N(t,this.name),this.registerEventListeners()}get context(){return U(this.logger)}async rpcPublish(e){var t,s,i,r;const{topic:o,message:a,ttl:c=Ne,prompt:h,tag:u,id:g,attestation:m,tvf:A}=e,l={method:oe(De().protocol).publish,params:fe({topic:o,message:a,ttl:c,prompt:h,tag:u,attestation:m},A),id:g};Pe((t=l.params)==null?void 0:t.prompt)&&((s=l.params)==null||delete s.prompt),Pe((i=l.params)==null?void 0:i.tag)&&((r=l.params)==null||delete r.tag),this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"message",direction:"outgoing",request:l});const y=await this.relayer.request(l);return this.relayer.events.emit(T.publish,e),this.logger.debug("Successfully Published Payload"),y}removeRequestFromQueue(e){this.queue.delete(e)}checkQueue(){this.queue.forEach(async(e,t)=>{const s=e.attempt+1;this.queue.set(t,Me(fe({},e),{attempt:s}));const{topic:i,message:r,opts:o,attestation:a}=e;this.logger.warn({},`Publisher: queue->publishing: ${e.opts.id}, tag: ${e.opts.tag}, attempt: ${s}`),await this.rpcPublish(Me(fe({},e),{topic:i,message:r,ttl:o.ttl,prompt:o.prompt,tag:o.tag,id:o.id,attestation:a,tvf:o.tvf})),this.logger.warn({},`Publisher: queue->published: ${e.opts.id}`)})}registerEventListeners(){this.relayer.core.heartbeat.on(ne.pulse,()=>{if(this.needsTransportRestart){this.needsTransportRestart=!1,this.relayer.events.emit(T.connection_stalled);return}this.checkQueue()}),this.relayer.on(T.message_ack,e=>{this.removeRequestFromQueue(e.id.toString())})}}var An=Object.defineProperty,Nn=(n,e,t)=>e in n?An(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,re=(n,e,t)=>Nn(n,typeof e!="symbol"?e+"":e,t);class $n{constructor(){re(this,"map",new Map),re(this,"set",(e,t)=>{const s=this.get(e);this.exists(e,t)||this.map.set(e,[...s,t])}),re(this,"get",e=>this.map.get(e)||[]),re(this,"exists",(e,t)=>this.get(e).includes(t)),re(this,"delete",(e,t)=>{if(typeof t>"u"){this.map.delete(e);return}if(!this.map.has(e))return;const s=this.get(e);if(!this.exists(e,t))return;const i=s.filter(r=>r!==t);if(!i.length){this.map.delete(e);return}this.map.set(e,i)}),re(this,"clear",()=>{this.map.clear()})}get topics(){return Array.from(this.map.keys())}}var zn=Object.defineProperty,Ln=Object.defineProperties,kn=Object.getOwnPropertyDescriptors,vi=Object.getOwnPropertySymbols,Un=Object.prototype.hasOwnProperty,Fn=Object.prototype.propertyIsEnumerable,Ke=(n,e,t)=>e in n?zn(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,de=(n,e)=>{for(var t in e||(e={}))Un.call(e,t)&&Ke(n,t,e[t]);if(vi)for(var t of vi(e))Fn.call(e,t)&&Ke(n,t,e[t]);return n},Be=(n,e)=>Ln(n,kn(e)),D=(n,e,t)=>Ke(n,typeof e!="symbol"?e+"":e,t);class fi extends Vi{constructor(e,t){super(e,t),this.relayer=e,this.logger=t,D(this,"subscriptions",new Map),D(this,"topicMap",new $n),D(this,"events",new ee),D(this,"name",$t),D(this,"version",zt),D(this,"pending",new Map),D(this,"cached",[]),D(this,"initialized",!1),D(this,"pendingSubscriptionWatchLabel","pending_sub_watch_label"),D(this,"pollingInterval",20),D(this,"storagePrefix",B),D(this,"subscribeTimeout",R(Ye)),D(this,"initialSubscribeTimeout",R(Z*15)),D(this,"clientId"),D(this,"batchSubscribeTopicsLimit",500),D(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),this.registerEventListeners(),await this.restore()),this.initialized=!0}),D(this,"subscribe",async(s,i)=>{this.isInitialized(),this.logger.debug("Subscribing Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:s,opts:i}});try{const r=De(i),o={topic:s,relay:r,transportType:i?.transportType};this.pending.set(s,o);const a=await this.rpcSubscribe(s,r,i);return typeof a=="string"&&(this.onSubscribe(a,o),this.logger.debug("Successfully Subscribed Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:s,opts:i}})),a}catch(r){throw this.logger.debug("Failed to Subscribe Topic"),this.logger.error(r),r}}),D(this,"unsubscribe",async(s,i)=>{this.isInitialized(),typeof i?.id<"u"?await this.unsubscribeById(s,i.id,i):await this.unsubscribeByTopic(s,i)}),D(this,"isSubscribed",async s=>{if(this.topics.includes(s))return!0;const i=`${this.pendingSubscriptionWatchLabel}_${s}`;return await new Promise((r,o)=>{const a=new es;a.start(i);const c=setInterval(()=>{(!this.pending.has(s)&&this.topics.includes(s)||this.cached.some(h=>h.topic===s))&&(clearInterval(c),a.stop(i),r(!0)),a.elapsed(i)>=Lt&&(clearInterval(c),a.stop(i),o(new Error("Subscription resolution timeout")))},this.pollingInterval)}).catch(()=>!1)}),D(this,"on",(s,i)=>{this.events.on(s,i)}),D(this,"once",(s,i)=>{this.events.once(s,i)}),D(this,"off",(s,i)=>{this.events.off(s,i)}),D(this,"removeListener",(s,i)=>{this.events.removeListener(s,i)}),D(this,"start",async()=>{await this.onConnect()}),D(this,"stop",async()=>{await this.onDisconnect()}),D(this,"restart",async()=>{await this.restore(),await this.onRestart()}),D(this,"checkPending",async()=>{if(this.pending.size===0&&(!this.initialized||!this.relayer.connected))return;const s=[];this.pending.forEach(i=>{s.push(i)}),await this.batchSubscribe(s)}),D(this,"registerEventListeners",()=>{this.relayer.core.heartbeat.on(ne.pulse,async()=>{await this.checkPending()}),this.events.on($.created,async s=>{const i=$.created;this.logger.info(`Emitting ${i}`),this.logger.debug({type:"event",event:i,data:s}),await this.persist()}),this.events.on($.deleted,async s=>{const i=$.deleted;this.logger.info(`Emitting ${i}`),this.logger.debug({type:"event",event:i,data:s}),await this.persist()})}),this.relayer=e,this.logger=N(t,this.name),this.clientId=""}get context(){return U(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.relayer.core.customStoragePrefix+"//"+this.name}get length(){return this.subscriptions.size}get ids(){return Array.from(this.subscriptions.keys())}get values(){return Array.from(this.subscriptions.values())}get topics(){return this.topicMap.topics}get hasAnyTopics(){return this.topicMap.topics.length>0||this.pending.size>0||this.cached.length>0||this.subscriptions.size>0}hasSubscription(e,t){let s=!1;try{s=this.getSubscription(e).topic===t}catch{}return s}reset(){this.cached=[],this.initialized=!0}onDisable(){this.cached=this.values,this.subscriptions.clear(),this.topicMap.clear()}async unsubscribeByTopic(e,t){const s=this.topicMap.get(e);await Promise.all(s.map(async i=>await this.unsubscribeById(e,i,t)))}async unsubscribeById(e,t,s){this.logger.debug("Unsubscribing Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:e,id:t,opts:s}});try{const i=De(s);await this.restartToComplete({topic:e,id:t,relay:i}),await this.rpcUnsubscribe(e,t,i);const r=ae("USER_DISCONNECTED",`${this.name}, ${e}`);await this.onUnsubscribe(e,t,r),this.logger.debug("Successfully Unsubscribed Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:e,id:t,opts:s}})}catch(i){throw this.logger.debug("Failed to Unsubscribe Topic"),this.logger.error(i),i}}async rpcSubscribe(e,t,s){var i;(!s||s?.transportType===Q.relay)&&await this.restartToComplete({topic:e,id:e,relay:t});const r={method:oe(t.protocol).subscribe,params:{topic:e}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:r});const o=(i=s?.internal)==null?void 0:i.throwOnFailedPublish;try{const a=await this.getSubscriptionId(e);if(s?.transportType===Q.link_mode)return setTimeout(()=>{(this.relayer.connected||this.relayer.connecting)&&this.relayer.request(r).catch(u=>this.logger.warn(u))},R(Z)),a;const c=new Promise(async u=>{const g=m=>{m.topic===e&&(this.events.removeListener($.created,g),u(m.id))};this.events.on($.created,g);try{const m=await W(new Promise((A,l)=>{this.relayer.request(r).catch(y=>{this.logger.warn(y,y?.message),l(y)}).then(A)}),this.initialSubscribeTimeout,`Subscribing to ${e} failed, please try again`);this.events.removeListener($.created,g),u(m)}catch{}}),h=await W(c,this.subscribeTimeout,`Subscribing to ${e} failed, please try again`);if(!h&&o)throw new Error(`Subscribing to ${e} failed, please try again`);return h?a:null}catch(a){if(this.logger.debug("Outgoing Relay Subscribe Payload stalled"),this.relayer.events.emit(T.connection_stalled),o)throw a}return null}async rpcBatchSubscribe(e){if(!e.length)return;const t=e[0].relay,s={method:oe(t.protocol).batchSubscribe,params:{topics:e.map(i=>i.topic)}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:s});try{await await W(new Promise(i=>{this.relayer.request(s).catch(r=>this.logger.warn(r)).then(i)}),this.subscribeTimeout,"rpcBatchSubscribe failed, please try again")}catch{this.relayer.events.emit(T.connection_stalled)}}async rpcBatchFetchMessages(e){if(!e.length)return;const t=e[0].relay,s={method:oe(t.protocol).batchFetchMessages,params:{topics:e.map(r=>r.topic)}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:s});let i;try{i=await await W(new Promise((r,o)=>{this.relayer.request(s).catch(a=>{this.logger.warn(a),o(a)}).then(r)}),this.subscribeTimeout,"rpcBatchFetchMessages failed, please try again")}catch{this.relayer.events.emit(T.connection_stalled)}return i}rpcUnsubscribe(e,t,s){const i={method:oe(s.protocol).unsubscribe,params:{topic:e,id:t}};return this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:i}),this.relayer.request(i)}onSubscribe(e,t){this.setSubscription(e,Be(de({},t),{id:e})),this.pending.delete(t.topic)}onBatchSubscribe(e){e.length&&e.forEach(t=>{this.setSubscription(t.id,de({},t)),this.pending.delete(t.topic)})}async onUnsubscribe(e,t,s){this.events.removeAllListeners(t),this.hasSubscription(t,e)&&this.deleteSubscription(t,s),await this.relayer.messages.del(e)}async setRelayerSubscriptions(e){await this.relayer.core.storage.setItem(this.storageKey,e)}async getRelayerSubscriptions(){return await this.relayer.core.storage.getItem(this.storageKey)}setSubscription(e,t){this.logger.debug("Setting subscription"),this.logger.trace({type:"method",method:"setSubscription",id:e,subscription:t}),this.addSubscription(e,t)}addSubscription(e,t){this.subscriptions.set(e,de({},t)),this.topicMap.set(t.topic,e),this.events.emit($.created,t)}getSubscription(e){this.logger.debug("Getting subscription"),this.logger.trace({type:"method",method:"getSubscription",id:e});const t=this.subscriptions.get(e);if(!t){const{message:s}=b("NO_MATCHING_KEY",`${this.name}: ${e}`);throw new Error(s)}return t}deleteSubscription(e,t){this.logger.debug("Deleting subscription"),this.logger.trace({type:"method",method:"deleteSubscription",id:e,reason:t});const s=this.getSubscription(e);this.subscriptions.delete(e),this.topicMap.delete(s.topic,e),this.events.emit($.deleted,Be(de({},s),{reason:t}))}async persist(){await this.setRelayerSubscriptions(this.values),this.events.emit($.sync)}async onRestart(){if(this.cached.length){const e=[...this.cached],t=Math.ceil(this.cached.length/this.batchSubscribeTopicsLimit);for(let s=0;s<t;s++){const i=e.splice(0,this.batchSubscribeTopicsLimit);await this.batchSubscribe(i)}}this.events.emit($.resubscribed)}async restore(){try{const e=await this.getRelayerSubscriptions();if(typeof e>"u"||!e.length)return;if(this.subscriptions.size){const{message:t}=b("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(t),this.logger.error(`${this.name}: ${JSON.stringify(this.values)}`),new Error(t)}this.cached=e,this.logger.debug(`Successfully Restored subscriptions for ${this.name}`),this.logger.trace({type:"method",method:"restore",subscriptions:this.values})}catch(e){this.logger.debug(`Failed to Restore subscriptions for ${this.name}`),this.logger.error(e)}}async batchSubscribe(e){e.length&&(await this.rpcBatchSubscribe(e),this.onBatchSubscribe(await Promise.all(e.map(async t=>Be(de({},t),{id:await this.getSubscriptionId(t.topic)})))))}async batchFetchMessages(e){if(!e.length)return;this.logger.trace(`Fetching batch messages for ${e.length} subscriptions`);const t=await this.rpcBatchFetchMessages(e);t&&t.messages&&(await ps(R(Z)),await this.relayer.handleBatchMessageEvents(t.messages))}async onConnect(){await this.restart(),this.reset()}onDisconnect(){this.onDisable()}isInitialized(){if(!this.initialized){const{message:e}=b("NOT_INITIALIZED",this.name);throw new Error(e)}}async restartToComplete(e){!this.relayer.connected&&!this.relayer.connecting&&(this.cached.push(e),await this.relayer.transportOpen())}async getClientId(){return this.clientId||(this.clientId=await this.relayer.core.crypto.getClientId()),this.clientId}async getSubscriptionId(e){return Ce(e+await this.getClientId())}}var Mn=Object.defineProperty,_i=Object.getOwnPropertySymbols,Kn=Object.prototype.hasOwnProperty,Bn=Object.prototype.propertyIsEnumerable,je=(n,e,t)=>e in n?Mn(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,Ei=(n,e)=>{for(var t in e||(e={}))Kn.call(e,t)&&je(n,t,e[t]);if(_i)for(var t of _i(e))Bn.call(e,t)&&je(n,t,e[t]);return n},p=(n,e,t)=>je(n,typeof e!="symbol"?e+"":e,t);class wi extends qi{constructor(e){super(e),p(this,"protocol","wc"),p(this,"version",2),p(this,"core"),p(this,"logger"),p(this,"events",new ee),p(this,"provider"),p(this,"messages"),p(this,"subscriber"),p(this,"publisher"),p(this,"name",Rt),p(this,"transportExplicitlyClosed",!1),p(this,"initialized",!1),p(this,"connectionAttemptInProgress",!1),p(this,"relayUrl"),p(this,"projectId"),p(this,"packageName"),p(this,"bundleId"),p(this,"hasExperiencedNetworkDisruption",!1),p(this,"pingTimeout"),p(this,"heartBeatTimeout",R(Ie+we)),p(this,"reconnectTimeout"),p(this,"connectPromise"),p(this,"reconnectInProgress",!1),p(this,"requestsInFlight",[]),p(this,"connectTimeout",R(Z*15)),p(this,"request",async t=>{var s,i;this.logger.debug("Publishing Request Payload");const r=t.id||dt().toString();await this.toEstablishConnection();try{this.logger.trace({id:r,method:t.method,topic:(s=t.params)==null?void 0:s.topic},"relayer.request - publishing...");const o=`${r}:${((i=t.params)==null?void 0:i.tag)||""}`;this.requestsInFlight.push(o);const a=await this.provider.request(t);return this.requestsInFlight=this.requestsInFlight.filter(c=>c!==o),a}catch(o){throw this.logger.debug(`Failed to Publish Request: ${r}`),o}}),p(this,"resetPingTimeout",()=>{if(ot())try{clearTimeout(this.pingTimeout),this.pingTimeout=setTimeout(()=>{var t,s,i;this.logger.debug({},"pingTimeout: Connection stalled, terminating..."),(i=(s=(t=this.provider)==null?void 0:t.connection)==null?void 0:s.socket)==null||i.terminate()},this.heartBeatTimeout)}catch(t){this.logger.warn(t,t?.message)}}),p(this,"onPayloadHandler",t=>{this.onProviderPayload(t),this.resetPingTimeout()}),p(this,"onConnectHandler",()=>{this.logger.warn({},"Relayer connected \u{1F6DC}"),this.startPingTimeout(),this.events.emit(T.connect)}),p(this,"onDisconnectHandler",()=>{this.logger.warn({},"Relayer disconnected \u{1F6D1}"),this.requestsInFlight=[],this.onProviderDisconnect()}),p(this,"onProviderErrorHandler",t=>{this.logger.fatal(`Fatal socket error: ${t.message}`),this.events.emit(T.error,t),this.logger.fatal("Fatal socket error received, closing transport"),this.transportClose()}),p(this,"registerProviderListeners",()=>{this.provider.on(L.payload,this.onPayloadHandler),this.provider.on(L.connect,this.onConnectHandler),this.provider.on(L.disconnect,this.onDisconnectHandler),this.provider.on(L.error,this.onProviderErrorHandler)}),this.core=e.core,this.logger=typeof e.logger<"u"&&typeof e.logger!="string"?N(e.logger,this.name):Mi(He({level:e.logger||St})),this.messages=new Di(this.logger,e.core),this.subscriber=new fi(this,this.logger),this.publisher=new On(this,this.logger),this.relayUrl=e?.relayUrl||$e,this.projectId=e.projectId,gs()?this.packageName=rt():ys()&&(this.bundleId=rt()),this.provider={}}async init(){if(this.logger.trace("Initialized"),this.registerEventListeners(),await Promise.all([this.messages.init(),this.subscriber.init()]),this.initialized=!0,this.subscriber.hasAnyTopics)try{await this.transportOpen()}catch(e){this.logger.warn(e,e?.message)}}get context(){return U(this.logger)}get connected(){var e,t,s;return((s=(t=(e=this.provider)==null?void 0:e.connection)==null?void 0:t.socket)==null?void 0:s.readyState)===1||!1}get connecting(){var e,t,s;return((s=(t=(e=this.provider)==null?void 0:e.connection)==null?void 0:t.socket)==null?void 0:s.readyState)===0||this.connectPromise!==void 0||!1}async publish(e,t,s){this.isInitialized(),await this.publisher.publish(e,t,s),await this.recordMessageEvent({topic:e,message:t,publishedAt:Date.now(),transportType:Q.relay})}async subscribe(e,t){var s,i,r;this.isInitialized(),(!(t!=null&&t.transportType)||t?.transportType==="relay")&&await this.toEstablishConnection();const o=typeof((s=t?.internal)==null?void 0:s.throwOnFailedPublish)>"u"?!0:(i=t?.internal)==null?void 0:i.throwOnFailedPublish;let a=((r=this.subscriber.topicMap.get(e))==null?void 0:r[0])||"",c;const h=u=>{u.topic===e&&(this.subscriber.off($.created,h),c())};return await Promise.all([new Promise(u=>{c=u,this.subscriber.on($.created,h)}),new Promise(async(u,g)=>{a=await this.subscriber.subscribe(e,Ei({internal:{throwOnFailedPublish:o}},t)).catch(m=>{o&&g(m)})||a,u()})]),a}async unsubscribe(e,t){this.isInitialized(),await this.subscriber.unsubscribe(e,t)}on(e,t){this.events.on(e,t)}once(e,t){this.events.once(e,t)}off(e,t){this.events.off(e,t)}removeListener(e,t){this.events.removeListener(e,t)}async transportDisconnect(){this.provider.disconnect&&(this.hasExperiencedNetworkDisruption||this.connected)?await W(this.provider.disconnect(),2e3,"provider.disconnect()").catch(()=>this.onProviderDisconnect()):this.onProviderDisconnect()}async transportClose(){this.transportExplicitlyClosed=!0,await this.transportDisconnect()}async transportOpen(e){if(!this.subscriber.hasAnyTopics){this.logger.warn("Starting WS connection skipped because the client has no topics to work with.");return}if(this.connectPromise?(this.logger.debug({},"Waiting for existing connection attempt to resolve..."),await this.connectPromise,this.logger.debug({},"Existing connection attempt resolved")):(this.connectPromise=new Promise(async(t,s)=>{await this.connect(e).then(t).catch(s).finally(()=>{this.connectPromise=void 0})}),await this.connectPromise),!this.connected)throw new Error(`Couldn't establish socket connection to the relay server: ${this.relayUrl}`)}async restartTransport(e){this.logger.debug({},"Restarting transport..."),!this.connectionAttemptInProgress&&(this.relayUrl=e||this.relayUrl,await this.confirmOnlineStateOrThrow(),await this.transportClose(),await this.transportOpen())}async confirmOnlineStateOrThrow(){if(!await nt())throw new Error("No internet connection detected. Please restart your network and try again.")}async handleBatchMessageEvents(e){if(e?.length===0){this.logger.trace("Batch message events is empty. Ignoring...");return}const t=e.sort((s,i)=>s.publishedAt-i.publishedAt);this.logger.debug(`Batch of ${t.length} message events sorted`);for(const s of t)try{await this.onMessageEvent(s)}catch(i){this.logger.warn(i,"Error while processing batch message event: "+i?.message)}this.logger.trace(`Batch of ${t.length} message events processed`)}async onLinkMessageEvent(e,t){const{topic:s}=e;if(!t.sessionExists){const i=ce(ye),r={topic:s,expiry:i,relay:{protocol:"irn"},active:!1};await this.core.pairing.pairings.set(s,r)}this.events.emit(T.message,e),await this.recordMessageEvent(e)}async connect(e){await this.confirmOnlineStateOrThrow(),e&&e!==this.relayUrl&&(this.relayUrl=e,await this.transportDisconnect()),this.connectionAttemptInProgress=!0,this.transportExplicitlyClosed=!1;let t=1;for(;t<6;){try{if(this.transportExplicitlyClosed)break;this.logger.debug({},`Connecting to ${this.relayUrl}, attempt: ${t}...`),await this.createProvider(),await new Promise(async(s,i)=>{const r=()=>{i(new Error("Connection interrupted while trying to subscribe"))};this.provider.once(L.disconnect,r),await W(new Promise((o,a)=>{this.provider.connect().then(o).catch(a)}),this.connectTimeout,`Socket stalled when trying to connect to ${this.relayUrl}`).catch(o=>{i(o)}).finally(()=>{this.provider.off(L.disconnect,r),clearTimeout(this.reconnectTimeout)}),await new Promise(async(o,a)=>{const c=()=>{a(new Error("Connection interrupted while trying to subscribe"))};this.provider.once(L.disconnect,c),await this.subscriber.start().then(o).catch(a).finally(()=>{this.provider.off(L.disconnect,c)})}),this.hasExperiencedNetworkDisruption=!1,s()})}catch(s){await this.subscriber.stop();const i=s;this.logger.warn({},i.message),this.hasExperiencedNetworkDisruption=!0}finally{this.connectionAttemptInProgress=!1}if(this.connected){this.logger.debug({},`Connected to ${this.relayUrl} successfully on attempt: ${t}`);break}await new Promise(s=>setTimeout(s,R(t*1))),t++}}startPingTimeout(){var e,t,s,i,r;if(ot())try{(t=(e=this.provider)==null?void 0:e.connection)!=null&&t.socket&&((r=(i=(s=this.provider)==null?void 0:s.connection)==null?void 0:i.socket)==null||r.on("ping",()=>{this.resetPingTimeout()})),this.resetPingTimeout()}catch(o){this.logger.warn(o,o?.message)}}async createProvider(){this.provider.connection&&this.unregisterProviderListeners();const e=await this.core.crypto.signJWT(this.relayUrl);this.provider=new As(new zs(bs({sdkVersion:me,protocol:this.protocol,version:this.version,relayUrl:this.relayUrl,projectId:this.projectId,auth:e,useOnCloseEvent:!0,bundleId:this.bundleId,packageName:this.packageName}))),this.registerProviderListeners()}async recordMessageEvent(e){const{topic:t,message:s}=e;await this.messages.set(t,s)}async shouldIgnoreMessageEvent(e){const{topic:t,message:s}=e;if(!s||s.length===0)return this.logger.warn(`Ignoring invalid/empty message: ${s}`),!0;if(!await this.subscriber.isSubscribed(t))return this.logger.warn(`Ignoring message for non-subscribed topic ${t}`),!0;const i=this.messages.has(t,s);return i&&this.logger.warn(`Ignoring duplicate message: ${s}`),i}async onProviderPayload(e){if(this.logger.debug("Incoming Relay Payload"),this.logger.trace({type:"payload",direction:"incoming",payload:e}),pt(e)){if(!e.method.endsWith(xt))return;const t=e.params,{topic:s,message:i,publishedAt:r,attestation:o}=t.data,a={topic:s,message:i,publishedAt:r,transportType:Q.relay,attestation:o};this.logger.debug("Emitting Relayer Payload"),this.logger.trace(Ei({type:"event",event:t.id},a)),this.events.emit(t.id,a),await this.acknowledgePayload(e),await this.onMessageEvent(a)}else gt(e)&&this.events.emit(T.message_ack,e)}async onMessageEvent(e){await this.shouldIgnoreMessageEvent(e)||(this.events.emit(T.message,e),await this.recordMessageEvent(e))}async acknowledgePayload(e){const t=yt(e.id,!0);await this.provider.connection.send(t)}unregisterProviderListeners(){this.provider.off(L.payload,this.onPayloadHandler),this.provider.off(L.connect,this.onConnectHandler),this.provider.off(L.disconnect,this.onDisconnectHandler),this.provider.off(L.error,this.onProviderErrorHandler),clearTimeout(this.pingTimeout)}async registerEventListeners(){let e=await nt();Ds(async t=>{e!==t&&(e=t,t?await this.transportOpen().catch(s=>this.logger.error(s,s?.message)):(this.hasExperiencedNetworkDisruption=!0,await this.transportDisconnect(),this.transportExplicitlyClosed=!1))})}async onProviderDisconnect(){clearTimeout(this.pingTimeout),this.events.emit(T.disconnect),this.connectionAttemptInProgress=!1,!this.reconnectInProgress&&(this.reconnectInProgress=!0,await this.subscriber.stop(),this.subscriber.hasAnyTopics&&(this.transportExplicitlyClosed||(this.reconnectTimeout=setTimeout(async()=>{await this.transportOpen().catch(e=>this.logger.error(e,e?.message)),this.reconnectTimeout=void 0,this.reconnectInProgress=!1},R(Ot)))))}isInitialized(){if(!this.initialized){const{message:e}=b("NOT_INITIALIZED",this.name);throw new Error(e)}}async toEstablishConnection(){await this.confirmOnlineStateOrThrow(),!this.connected&&await this.connect()}}var jn=Object.defineProperty,Ii=Object.getOwnPropertySymbols,Vn=Object.prototype.hasOwnProperty,qn=Object.prototype.propertyIsEnumerable,Ve=(n,e,t)=>e in n?jn(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,Ti=(n,e)=>{for(var t in e||(e={}))Vn.call(e,t)&&Ve(n,t,e[t]);if(Ii)for(var t of Ii(e))qn.call(e,t)&&Ve(n,t,e[t]);return n},z=(n,e,t)=>Ve(n,typeof e!="symbol"?e+"":e,t);class Ci extends Gi{constructor(e,t,s,i=B,r=void 0){super(e,t,s,i),this.core=e,this.logger=t,this.name=s,z(this,"map",new Map),z(this,"version",At),z(this,"cached",[]),z(this,"initialized",!1),z(this,"getKey"),z(this,"storagePrefix",B),z(this,"recentlyDeleted",[]),z(this,"recentlyDeletedLimit",200),z(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(o=>{this.getKey&&o!==null&&!Pe(o)?this.map.set(this.getKey(o),o):ms(o)?this.map.set(o.id,o):vs(o)&&this.map.set(o.topic,o)}),this.cached=[],this.initialized=!0)}),z(this,"set",async(o,a)=>{this.isInitialized(),this.map.has(o)?await this.update(o,a):(this.logger.debug("Setting value"),this.logger.trace({type:"method",method:"set",key:o,value:a}),this.map.set(o,a),await this.persist())}),z(this,"get",o=>(this.isInitialized(),this.logger.debug("Getting value"),this.logger.trace({type:"method",method:"get",key:o}),this.getData(o))),z(this,"getAll",o=>(this.isInitialized(),o?this.values.filter(a=>Object.keys(o).every(c=>Ls(a[c],o[c]))):this.values)),z(this,"update",async(o,a)=>{this.isInitialized(),this.logger.debug("Updating value"),this.logger.trace({type:"method",method:"update",key:o,update:a});const c=Ti(Ti({},this.getData(o)),a);this.map.set(o,c),await this.persist()}),z(this,"delete",async(o,a)=>{this.isInitialized(),this.map.has(o)&&(this.logger.debug("Deleting value"),this.logger.trace({type:"method",method:"delete",key:o,reason:a}),this.map.delete(o),this.addToRecentlyDeleted(o),await this.persist())}),this.logger=N(t,this.name),this.storagePrefix=i,this.getKey=r}get context(){return U(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.map.size}get keys(){return Array.from(this.map.keys())}get values(){return Array.from(this.map.values())}addToRecentlyDeleted(e){this.recentlyDeleted.push(e),this.recentlyDeleted.length>=this.recentlyDeletedLimit&&this.recentlyDeleted.splice(0,this.recentlyDeletedLimit/2)}async setDataStore(e){await this.core.storage.setItem(this.storageKey,e)}async getDataStore(){return await this.core.storage.getItem(this.storageKey)}getData(e){const t=this.map.get(e);if(!t){if(this.recentlyDeleted.includes(e)){const{message:i}=b("MISSING_OR_INVALID",`Record was recently deleted - ${this.name}: ${e}`);throw this.logger.error(i),new Error(i)}const{message:s}=b("NO_MATCHING_KEY",`${this.name}: ${e}`);throw this.logger.error(s),new Error(s)}return t}async persist(){await this.setDataStore(this.values)}async restore(){try{const e=await this.getDataStore();if(typeof e>"u"||!e.length)return;if(this.map.size){const{message:t}=b("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(t),new Error(t)}this.cached=e,this.logger.debug(`Successfully Restored value for ${this.name}`),this.logger.trace({type:"method",method:"restore",value:this.values})}catch(e){this.logger.debug(`Failed to Restore value for ${this.name}`),this.logger.error(e)}}isInitialized(){if(!this.initialized){const{message:e}=b("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Gn=Object.defineProperty,Hn=(n,e,t)=>e in n?Gn(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,d=(n,e,t)=>Hn(n,typeof e!="symbol"?e+"":e,t);class Pi{constructor(e,t){this.core=e,this.logger=t,d(this,"name",kt),d(this,"version",Ut),d(this,"events",new ki),d(this,"pairings"),d(this,"initialized",!1),d(this,"storagePrefix",B),d(this,"ignoredPayloadTypes",[_s]),d(this,"registeredMethods",[]),d(this,"init",async()=>{this.initialized||(await this.pairings.init(),await this.cleanup(),this.registerRelayerEvents(),this.registerExpirerEvents(),this.initialized=!0,this.logger.trace("Initialized"))}),d(this,"register",({methods:s})=>{this.isInitialized(),this.registeredMethods=[...new Set([...this.registeredMethods,...s])]}),d(this,"create",async s=>{this.isInitialized();const i=Te(),r=await this.core.crypto.setSymKey(i),o=ce(ye),a={protocol:Pt},c={topic:r,expiry:o,relay:a,active:!1,methods:s?.methods},h=at({protocol:this.core.protocol,version:this.core.version,topic:r,symKey:i,relay:a,expiryTimestamp:o,methods:s?.methods});return this.events.emit(se.create,c),this.core.expirer.set(r,o),await this.pairings.set(r,c),await this.core.relayer.subscribe(r,{transportType:s?.transportType}),{topic:r,uri:h}}),d(this,"pair",async s=>{this.isInitialized();const i=this.core.eventClient.createEvent({properties:{topic:s?.uri,trace:[q.pairing_started]}});this.isValidPair(s,i);const{topic:r,symKey:o,relay:a,expiryTimestamp:c,methods:h}=ct(s.uri);i.props.properties.topic=r,i.addTrace(q.pairing_uri_validation_success),i.addTrace(q.pairing_uri_not_expired);let u;if(this.pairings.keys.includes(r)){if(u=this.pairings.get(r),i.addTrace(q.existing_pairing),u.active)throw i.setError(J.active_pairing_already_exists),new Error(`Pairing already exists: ${r}. Please try again with a new connection URI.`);i.addTrace(q.pairing_not_expired)}const g=c||ce(ye),m={topic:r,relay:a,expiry:g,active:!1,methods:h};this.core.expirer.set(r,g),await this.pairings.set(r,m),i.addTrace(q.store_new_pairing),s.activatePairing&&await this.activate({topic:r}),this.events.emit(se.create,m),i.addTrace(q.emit_inactive_pairing),this.core.crypto.keychain.has(r)||await this.core.crypto.setSymKey(o,r),i.addTrace(q.subscribing_pairing_topic);try{await this.core.relayer.confirmOnlineStateOrThrow()}catch{i.setError(J.no_internet_connection)}try{await this.core.relayer.subscribe(r,{relay:a})}catch(A){throw i.setError(J.subscribe_pairing_topic_failure),A}return i.addTrace(q.subscribe_pairing_topic_success),m}),d(this,"activate",async({topic:s})=>{this.isInitialized();const i=ce(ye);this.core.expirer.set(s,i),await this.pairings.update(s,{active:!0,expiry:i})}),d(this,"ping",async s=>{this.isInitialized(),await this.isValidPing(s),this.logger.warn("ping() is deprecated and will be removed in the next major release.");const{topic:i}=s;if(this.pairings.keys.includes(i)){const r=await this.sendRequest(i,"wc_pairingPing",{}),{done:o,resolve:a,reject:c}=Es();this.events.once(Se("pairing_ping",r),({error:h})=>{h?c(h):a()}),await o()}}),d(this,"updateExpiry",async({topic:s,expiry:i})=>{this.isInitialized(),await this.pairings.update(s,{expiry:i})}),d(this,"updateMetadata",async({topic:s,metadata:i})=>{this.isInitialized(),await this.pairings.update(s,{peerMetadata:i})}),d(this,"getPairings",()=>(this.isInitialized(),this.pairings.values)),d(this,"disconnect",async s=>{this.isInitialized(),await this.isValidDisconnect(s);const{topic:i}=s;this.pairings.keys.includes(i)&&(await this.sendRequest(i,"wc_pairingDelete",ae("USER_DISCONNECTED")),await this.deletePairing(i))}),d(this,"formatUriFromPairing",s=>{this.isInitialized();const{topic:i,relay:r,expiry:o,methods:a}=s,c=this.core.crypto.keychain.get(i);return at({protocol:this.core.protocol,version:this.core.version,topic:i,symKey:c,relay:r,expiryTimestamp:o,methods:a})}),d(this,"sendRequest",async(s,i,r)=>{const o=bt(i,r),a=await this.core.crypto.encode(s,o),c=ie[i].req;return this.core.history.set(s,o),this.core.relayer.publish(s,a,c),o.id}),d(this,"sendResult",async(s,i,r)=>{const o=yt(s,r),a=await this.core.crypto.encode(i,o),c=(await this.core.history.get(i,s)).request.method,h=ie[c].res;await this.core.relayer.publish(i,a,h),await this.core.history.resolve(o)}),d(this,"sendError",async(s,i,r)=>{const o=Ns(s,r),a=await this.core.crypto.encode(i,o),c=(await this.core.history.get(i,s)).request.method,h=ie[c]?ie[c].res:ie.unregistered_method.res;await this.core.relayer.publish(i,a,h),await this.core.history.resolve(o)}),d(this,"deletePairing",async(s,i)=>{await this.core.relayer.unsubscribe(s),await Promise.all([this.pairings.delete(s,ae("USER_DISCONNECTED")),this.core.crypto.deleteSymKey(s),i?Promise.resolve():this.core.expirer.del(s)])}),d(this,"cleanup",async()=>{const s=this.pairings.getAll().filter(i=>ht(i.expiry));await Promise.all(s.map(i=>this.deletePairing(i.topic)))}),d(this,"onRelayEventRequest",s=>{const{topic:i,payload:r}=s;switch(r.method){case"wc_pairingPing":return this.onPairingPingRequest(i,r);case"wc_pairingDelete":return this.onPairingDeleteRequest(i,r);default:return this.onUnknownRpcMethodRequest(i,r)}}),d(this,"onRelayEventResponse",async s=>{const{topic:i,payload:r}=s,o=(await this.core.history.get(i,r.id)).request.method;switch(o){case"wc_pairingPing":return this.onPairingPingResponse(i,r);default:return this.onUnknownRpcMethodResponse(o)}}),d(this,"onPairingPingRequest",async(s,i)=>{const{id:r}=i;try{this.isValidPing({topic:s}),await this.sendResult(r,s,!0),this.events.emit(se.ping,{id:r,topic:s})}catch(o){await this.sendError(r,s,o),this.logger.error(o)}}),d(this,"onPairingPingResponse",(s,i)=>{const{id:r}=i;setTimeout(()=>{$s(i)?this.events.emit(Se("pairing_ping",r),{}):Dt(i)&&this.events.emit(Se("pairing_ping",r),{error:i.error})},500)}),d(this,"onPairingDeleteRequest",async(s,i)=>{const{id:r}=i;try{this.isValidDisconnect({topic:s}),await this.deletePairing(s),this.events.emit(se.delete,{id:r,topic:s})}catch(o){await this.sendError(r,s,o),this.logger.error(o)}}),d(this,"onUnknownRpcMethodRequest",async(s,i)=>{const{id:r,method:o}=i;try{if(this.registeredMethods.includes(o))return;const a=ae("WC_METHOD_UNSUPPORTED",o);await this.sendError(r,s,a),this.logger.error(a)}catch(a){await this.sendError(r,s,a),this.logger.error(a)}}),d(this,"onUnknownRpcMethodResponse",s=>{this.registeredMethods.includes(s)||this.logger.error(ae("WC_METHOD_UNSUPPORTED",s))}),d(this,"isValidPair",(s,i)=>{var r;if(!Re(s)){const{message:a}=b("MISSING_OR_INVALID",`pair() params: ${s}`);throw i.setError(J.malformed_pairing_uri),new Error(a)}if(!ws(s.uri)){const{message:a}=b("MISSING_OR_INVALID",`pair() uri: ${s.uri}`);throw i.setError(J.malformed_pairing_uri),new Error(a)}const o=ct(s?.uri);if(!((r=o?.relay)!=null&&r.protocol)){const{message:a}=b("MISSING_OR_INVALID","pair() uri#relay-protocol");throw i.setError(J.malformed_pairing_uri),new Error(a)}if(!(o!=null&&o.symKey)){const{message:a}=b("MISSING_OR_INVALID","pair() uri#symKey");throw i.setError(J.malformed_pairing_uri),new Error(a)}if(o!=null&&o.expiryTimestamp&&R(o?.expiryTimestamp)<Date.now()){i.setError(J.pairing_expired);const{message:a}=b("EXPIRED","pair() URI has expired. Please try again with a new connection URI.");throw new Error(a)}}),d(this,"isValidPing",async s=>{if(!Re(s)){const{message:r}=b("MISSING_OR_INVALID",`ping() params: ${s}`);throw new Error(r)}const{topic:i}=s;await this.isValidPairingTopic(i)}),d(this,"isValidDisconnect",async s=>{if(!Re(s)){const{message:r}=b("MISSING_OR_INVALID",`disconnect() params: ${s}`);throw new Error(r)}const{topic:i}=s;await this.isValidPairingTopic(i)}),d(this,"isValidPairingTopic",async s=>{if(!Is(s,!1)){const{message:i}=b("MISSING_OR_INVALID",`pairing topic should be a string: ${s}`);throw new Error(i)}if(!this.pairings.keys.includes(s)){const{message:i}=b("NO_MATCHING_KEY",`pairing topic doesn't exist: ${s}`);throw new Error(i)}if(ht(this.pairings.get(s).expiry)){await this.deletePairing(s);const{message:i}=b("EXPIRED",`pairing topic: ${s}`);throw new Error(i)}}),this.core=e,this.logger=N(t,this.name),this.pairings=new Ci(this.core,this.logger,this.name,this.storagePrefix)}get context(){return U(this.logger)}isInitialized(){if(!this.initialized){const{message:e}=b("NOT_INITIALIZED",this.name);throw new Error(e)}}registerRelayerEvents(){this.core.relayer.on(T.message,async e=>{const{topic:t,message:s,transportType:i}=e;if(!this.pairings.keys.includes(t)||i===Q.link_mode||this.ignoredPayloadTypes.includes(this.core.crypto.getPayloadType(s)))return;const r=await this.core.crypto.decode(t,s);try{pt(r)?(this.core.history.set(t,r),this.onRelayEventRequest({topic:t,payload:r})):gt(r)&&(await this.core.history.resolve(r),await this.onRelayEventResponse({topic:t,payload:r}),this.core.history.delete(t,r.id))}catch(o){this.logger.error(o)}})}registerExpirerEvents(){this.core.expirer.on(M.expired,async e=>{const{topic:t}=fs(e.target);t&&this.pairings.keys.includes(t)&&(await this.deletePairing(t,!0),this.events.emit(se.expire,{topic:t}))})}}var Yn=Object.defineProperty,Jn=(n,e,t)=>e in n?Yn(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,S=(n,e,t)=>Jn(n,typeof e!="symbol"?e+"":e,t);class Si extends Hi{constructor(e,t){super(e,t),this.core=e,this.logger=t,S(this,"records",new Map),S(this,"events",new ee),S(this,"name",Ft),S(this,"version",Mt),S(this,"cached",[]),S(this,"initialized",!1),S(this,"storagePrefix",B),S(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(s=>this.records.set(s.id,s)),this.cached=[],this.registerEventListeners(),this.initialized=!0)}),S(this,"set",(s,i,r)=>{if(this.isInitialized(),this.logger.debug("Setting JSON-RPC request history record"),this.logger.trace({type:"method",method:"set",topic:s,request:i,chainId:r}),this.records.has(i.id))return;const o={id:i.id,topic:s,request:{method:i.method,params:i.params||null},chainId:r,expiry:ce(Ee)};this.records.set(o.id,o),this.persist(),this.events.emit(F.created,o)}),S(this,"resolve",async s=>{if(this.isInitialized(),this.logger.debug("Updating JSON-RPC response history record"),this.logger.trace({type:"method",method:"update",response:s}),!this.records.has(s.id))return;const i=await this.getRecord(s.id);typeof i.response>"u"&&(i.response=Dt(s)?{error:s.error}:{result:s.result},this.records.set(i.id,i),this.persist(),this.events.emit(F.updated,i))}),S(this,"get",async(s,i)=>(this.isInitialized(),this.logger.debug("Getting record"),this.logger.trace({type:"method",method:"get",topic:s,id:i}),await this.getRecord(i))),S(this,"delete",(s,i)=>{this.isInitialized(),this.logger.debug("Deleting record"),this.logger.trace({type:"method",method:"delete",id:i}),this.values.forEach(r=>{if(r.topic===s){if(typeof i<"u"&&r.id!==i)return;this.records.delete(r.id),this.events.emit(F.deleted,r)}}),this.persist()}),S(this,"exists",async(s,i)=>(this.isInitialized(),this.records.has(i)?(await this.getRecord(i)).topic===s:!1)),S(this,"on",(s,i)=>{this.events.on(s,i)}),S(this,"once",(s,i)=>{this.events.once(s,i)}),S(this,"off",(s,i)=>{this.events.off(s,i)}),S(this,"removeListener",(s,i)=>{this.events.removeListener(s,i)}),this.logger=N(t,this.name)}get context(){return U(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get size(){return this.records.size}get keys(){return Array.from(this.records.keys())}get values(){return Array.from(this.records.values())}get pending(){const e=[];return this.values.forEach(t=>{if(typeof t.response<"u")return;const s={topic:t.topic,request:bt(t.request.method,t.request.params,t.id),chainId:t.chainId};return e.push(s)}),e}async setJsonRpcRecords(e){await this.core.storage.setItem(this.storageKey,e)}async getJsonRpcRecords(){return await this.core.storage.getItem(this.storageKey)}getRecord(e){this.isInitialized();const t=this.records.get(e);if(!t){const{message:s}=b("NO_MATCHING_KEY",`${this.name}: ${e}`);throw new Error(s)}return t}async persist(){await this.setJsonRpcRecords(this.values),this.events.emit(F.sync)}async restore(){try{const e=await this.getJsonRpcRecords();if(typeof e>"u"||!e.length)return;if(this.records.size){const{message:t}=b("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(t),new Error(t)}this.cached=e,this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",records:this.values})}catch(e){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(e)}}registerEventListeners(){this.events.on(F.created,e=>{const t=F.created;this.logger.info(`Emitting ${t}`),this.logger.debug({type:"event",event:t,record:e})}),this.events.on(F.updated,e=>{const t=F.updated;this.logger.info(`Emitting ${t}`),this.logger.debug({type:"event",event:t,record:e})}),this.events.on(F.deleted,e=>{const t=F.deleted;this.logger.info(`Emitting ${t}`),this.logger.debug({type:"event",event:t,record:e})}),this.core.heartbeat.on(ne.pulse,()=>{this.cleanup()})}cleanup(){try{this.isInitialized();let e=!1;this.records.forEach(t=>{R(t.expiry||0)-Date.now()<=0&&(this.logger.info(`Deleting expired history log: ${t.id}`),this.records.delete(t.id),this.events.emit(F.deleted,t,!1),e=!0)}),e&&this.persist()}catch(e){this.logger.warn(e)}}isInitialized(){if(!this.initialized){const{message:e}=b("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Xn=Object.defineProperty,Wn=(n,e,t)=>e in n?Xn(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,x=(n,e,t)=>Wn(n,typeof e!="symbol"?e+"":e,t);class Ri extends Yi{constructor(e,t){super(e,t),this.core=e,this.logger=t,x(this,"expirations",new Map),x(this,"events",new ee),x(this,"name",Kt),x(this,"version",Bt),x(this,"cached",[]),x(this,"initialized",!1),x(this,"storagePrefix",B),x(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(s=>this.expirations.set(s.target,s)),this.cached=[],this.registerEventListeners(),this.initialized=!0)}),x(this,"has",s=>{try{const i=this.formatTarget(s);return typeof this.getExpiration(i)<"u"}catch{return!1}}),x(this,"set",(s,i)=>{this.isInitialized();const r=this.formatTarget(s),o={target:r,expiry:i};this.expirations.set(r,o),this.checkExpiry(r,o),this.events.emit(M.created,{target:r,expiration:o})}),x(this,"get",s=>{this.isInitialized();const i=this.formatTarget(s);return this.getExpiration(i)}),x(this,"del",s=>{if(this.isInitialized(),this.has(s)){const i=this.formatTarget(s),r=this.getExpiration(i);this.expirations.delete(i),this.events.emit(M.deleted,{target:i,expiration:r})}}),x(this,"on",(s,i)=>{this.events.on(s,i)}),x(this,"once",(s,i)=>{this.events.once(s,i)}),x(this,"off",(s,i)=>{this.events.off(s,i)}),x(this,"removeListener",(s,i)=>{this.events.removeListener(s,i)}),this.logger=N(t,this.name)}get context(){return U(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.expirations.size}get keys(){return Array.from(this.expirations.keys())}get values(){return Array.from(this.expirations.values())}formatTarget(e){if(typeof e=="string")return Ts(e);if(typeof e=="number")return Cs(e);const{message:t}=b("UNKNOWN_TYPE",`Target type: ${typeof e}`);throw new Error(t)}async setExpirations(e){await this.core.storage.setItem(this.storageKey,e)}async getExpirations(){return await this.core.storage.getItem(this.storageKey)}async persist(){await this.setExpirations(this.values),this.events.emit(M.sync)}async restore(){try{const e=await this.getExpirations();if(typeof e>"u"||!e.length)return;if(this.expirations.size){const{message:t}=b("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(t),new Error(t)}this.cached=e,this.logger.debug(`Successfully Restored expirations for ${this.name}`),this.logger.trace({type:"method",method:"restore",expirations:this.values})}catch(e){this.logger.debug(`Failed to Restore expirations for ${this.name}`),this.logger.error(e)}}getExpiration(e){const t=this.expirations.get(e);if(!t){const{message:s}=b("NO_MATCHING_KEY",`${this.name}: ${e}`);throw this.logger.warn(s),new Error(s)}return t}checkExpiry(e,t){const{expiry:s}=t;R(s)-Date.now()<=0&&this.expire(e,t)}expire(e,t){this.expirations.delete(e),this.events.emit(M.expired,{target:e,expiration:t})}checkExpirations(){this.core.relayer.connected&&this.expirations.forEach((e,t)=>this.checkExpiry(t,e))}registerEventListeners(){this.core.heartbeat.on(ne.pulse,()=>this.checkExpirations()),this.events.on(M.created,e=>{const t=M.created;this.logger.info(`Emitting ${t}`),this.logger.debug({type:"event",event:t,data:e}),this.persist()}),this.events.on(M.expired,e=>{const t=M.expired;this.logger.info(`Emitting ${t}`),this.logger.debug({type:"event",event:t,data:e}),this.persist()}),this.events.on(M.deleted,e=>{const t=M.deleted;this.logger.info(`Emitting ${t}`),this.logger.debug({type:"event",event:t,data:e}),this.persist()})}isInitialized(){if(!this.initialized){const{message:e}=b("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Zn=Object.defineProperty,Qn=(n,e,t)=>e in n?Zn(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,_=(n,e,t)=>Qn(n,typeof e!="symbol"?e+"":e,t);class xi extends Ji{constructor(e,t,s){super(e,t,s),this.core=e,this.logger=t,this.store=s,_(this,"name",jt),_(this,"abortController"),_(this,"isDevEnv"),_(this,"verifyUrlV3",qt),_(this,"storagePrefix",B),_(this,"version",Oe),_(this,"publicKey"),_(this,"fetchPromise"),_(this,"init",async()=>{var i;this.isDevEnv||(this.publicKey=await this.store.getItem(this.storeKey),this.publicKey&&R((i=this.publicKey)==null?void 0:i.expiresAt)<Date.now()&&(this.logger.debug("verify v2 public key expired"),await this.removePublicKey()))}),_(this,"register",async i=>{if(!Ps()||this.isDevEnv)return;const r=window.location.origin,{id:o,decryptedId:a}=i,c=`${this.verifyUrlV3}/attestation?projectId=${this.core.projectId}&origin=${r}&id=${o}&decryptedId=${a}`;try{const h=ks(),u=this.startAbortTimer(Z*5),g=await new Promise((m,A)=>{const l=()=>{window.removeEventListener("message",O),h.body.removeChild(y),A("attestation aborted")};this.abortController.signal.addEventListener("abort",l);const y=h.createElement("iframe");y.src=c,y.style.display="none",y.addEventListener("error",l,{signal:this.abortController.signal});const O=w=>{if(w.data&&typeof w.data=="string")try{const v=JSON.parse(w.data);if(v.type==="verify_attestation"){if(We(v.attestation).payload.id!==o)return;clearInterval(u),h.body.removeChild(y),this.abortController.signal.removeEventListener("abort",l),window.removeEventListener("message",O),m(v.attestation===null?"":v.attestation)}}catch(v){this.logger.warn(v)}};h.body.appendChild(y),window.addEventListener("message",O,{signal:this.abortController.signal})});return this.logger.debug("jwt attestation",g),g}catch(h){this.logger.warn(h)}return""}),_(this,"resolve",async i=>{if(this.isDevEnv)return"";const{attestationId:r,hash:o,encryptedId:a}=i;if(r===""){this.logger.debug("resolve: attestationId is empty, skipping");return}if(r){if(We(r).payload.id!==a)return;const h=await this.isValidJwtAttestation(r);if(h){if(!h.isVerified){this.logger.warn("resolve: jwt attestation: origin url not verified");return}return h}}if(!o)return;const c=this.getVerifyUrl(i?.verifyUrl);return this.fetchAttestation(o,c)}),_(this,"fetchAttestation",async(i,r)=>{this.logger.debug(`resolving attestation: ${i} from url: ${r}`);const o=this.startAbortTimer(Z*5),a=await fetch(`${r}/attestation/${i}?v2Supported=true`,{signal:this.abortController.signal});return clearTimeout(o),a.status===200?await a.json():void 0}),_(this,"getVerifyUrl",i=>{let r=i||le;return Gt.includes(r)||(this.logger.info(`verify url: ${r}, not included in trusted list, assigning default: ${le}`),r=le),r}),_(this,"fetchPublicKey",async()=>{try{this.logger.debug(`fetching public key from: ${this.verifyUrlV3}`);const i=this.startAbortTimer(we),r=await fetch(`${this.verifyUrlV3}/public-key`,{signal:this.abortController.signal});return clearTimeout(i),await r.json()}catch(i){this.logger.warn(i)}}),_(this,"persistPublicKey",async i=>{this.logger.debug("persisting public key to local storage",i),await this.store.setItem(this.storeKey,i),this.publicKey=i}),_(this,"removePublicKey",async()=>{this.logger.debug("removing verify v2 public key from storage"),await this.store.removeItem(this.storeKey),this.publicKey=void 0}),_(this,"isValidJwtAttestation",async i=>{const r=await this.getPublicKey();try{if(r)return this.validateAttestation(i,r)}catch(a){this.logger.error(a),this.logger.warn("error validating attestation")}const o=await this.fetchAndPersistPublicKey();try{if(o)return this.validateAttestation(i,o)}catch(a){this.logger.error(a),this.logger.warn("error validating attestation")}}),_(this,"getPublicKey",async()=>this.publicKey?this.publicKey:await this.fetchAndPersistPublicKey()),_(this,"fetchAndPersistPublicKey",async()=>{if(this.fetchPromise)return await this.fetchPromise,this.publicKey;this.fetchPromise=new Promise(async r=>{const o=await this.fetchPublicKey();o&&(await this.persistPublicKey(o),r(o))});const i=await this.fetchPromise;return this.fetchPromise=void 0,i}),_(this,"validateAttestation",(i,r)=>{const o=Ss(i,r.publicKey),a={hasExpired:R(o.exp)<Date.now(),payload:o};if(a.hasExpired)throw this.logger.warn("resolve: jwt attestation expired"),new Error("JWT attestation expired");return{origin:a.payload.origin,isScam:a.payload.isScam,isVerified:a.payload.isVerified}}),this.logger=N(t,this.name),this.abortController=new AbortController,this.isDevEnv=lt(),this.init()}get storeKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//verify:public:key"}get context(){return U(this.logger)}startAbortTimer(e){return this.abortController=new AbortController,setTimeout(()=>this.abortController.abort(),R(e))}}var eo=Object.defineProperty,to=(n,e,t)=>e in n?eo(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,Oi=(n,e,t)=>to(n,typeof e!="symbol"?e+"":e,t);class Ai extends Xi{constructor(e,t){super(e,t),this.projectId=e,this.logger=t,Oi(this,"context",Ht),Oi(this,"registerDeviceToken",async s=>{const{clientId:i,token:r,notificationType:o,enableEncrypted:a=!1}=s,c=`${Yt}/${this.projectId}/clients`;await fetch(c,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({client_id:i,type:o,token:r,always_raw:a})})}),this.logger=N(t,this.context)}}var io=Object.defineProperty,Ni=Object.getOwnPropertySymbols,so=Object.prototype.hasOwnProperty,ro=Object.prototype.propertyIsEnumerable,qe=(n,e,t)=>e in n?io(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,pe=(n,e)=>{for(var t in e||(e={}))so.call(e,t)&&qe(n,t,e[t]);if(Ni)for(var t of Ni(e))ro.call(e,t)&&qe(n,t,e[t]);return n},E=(n,e,t)=>qe(n,typeof e!="symbol"?e+"":e,t);class $i extends Wi{constructor(e,t,s=!0){super(e,t,s),this.core=e,this.logger=t,E(this,"context",Xt),E(this,"storagePrefix",B),E(this,"storageVersion",Jt),E(this,"events",new Map),E(this,"shouldPersist",!1),E(this,"init",async()=>{if(!lt())try{const i={eventId:ut(),timestamp:Date.now(),domain:this.getAppDomain(),props:{event:"INIT",type:"",properties:{client_id:await this.core.crypto.getClientId(),user_agent:Rs(this.core.relayer.protocol,this.core.relayer.version,me)}}};await this.sendEvent([i])}catch(i){this.logger.warn(i)}}),E(this,"createEvent",i=>{const{event:r="ERROR",type:o="",properties:{topic:a,trace:c}}=i,h=ut(),u=this.core.projectId||"",g=Date.now(),m=pe({eventId:h,timestamp:g,props:{event:r,type:o,properties:{topic:a,trace:c}},bundleId:u,domain:this.getAppDomain()},this.setMethods(h));return this.telemetryEnabled&&(this.events.set(h,m),this.shouldPersist=!0),m}),E(this,"getEvent",i=>{const{eventId:r,topic:o}=i;if(r)return this.events.get(r);const a=Array.from(this.events.values()).find(c=>c.props.properties.topic===o);if(a)return pe(pe({},a),this.setMethods(a.eventId))}),E(this,"deleteEvent",i=>{const{eventId:r}=i;this.events.delete(r),this.shouldPersist=!0}),E(this,"setEventListeners",()=>{this.core.heartbeat.on(ne.pulse,async()=>{this.shouldPersist&&await this.persist(),this.events.forEach(i=>{Je(Date.now())-Je(i.timestamp)>Wt&&(this.events.delete(i.eventId),this.shouldPersist=!0)})})}),E(this,"setMethods",i=>({addTrace:r=>this.addTrace(i,r),setError:r=>this.setError(i,r)})),E(this,"addTrace",(i,r)=>{const o=this.events.get(i);o&&(o.props.properties.trace.push(r),this.events.set(i,o),this.shouldPersist=!0)}),E(this,"setError",(i,r)=>{const o=this.events.get(i);o&&(o.props.type=r,o.timestamp=Date.now(),this.events.set(i,o),this.shouldPersist=!0)}),E(this,"persist",async()=>{await this.core.storage.setItem(this.storageKey,Array.from(this.events.values())),this.shouldPersist=!1}),E(this,"restore",async()=>{try{const i=await this.core.storage.getItem(this.storageKey)||[];if(!i.length)return;i.forEach(r=>{this.events.set(r.eventId,pe(pe({},r),this.setMethods(r.eventId)))})}catch(i){this.logger.warn(i)}}),E(this,"submit",async()=>{if(!this.telemetryEnabled||this.events.size===0)return;const i=[];for(const[r,o]of this.events)o.props.type&&i.push(o);if(i.length!==0)try{if((await this.sendEvent(i)).ok)for(const r of i)this.events.delete(r.eventId),this.shouldPersist=!0}catch(r){this.logger.warn(r)}}),E(this,"sendEvent",async i=>{const r=this.getAppDomain()?"":"&sp=desktop";return await fetch(`${Zt}?projectId=${this.core.projectId}&st=events_sdk&sv=js-${me}${r}`,{method:"POST",body:JSON.stringify(i)})}),E(this,"getAppDomain",()=>xs().url),this.logger=N(t,this.context),this.telemetryEnabled=s,s?this.restore().then(async()=>{await this.submit(),this.setEventListeners()}):this.persist()}get storageKey(){return this.storagePrefix+this.storageVersion+this.core.customStoragePrefix+"//"+this.context}}var no=Object.defineProperty,zi=Object.getOwnPropertySymbols,oo=Object.prototype.hasOwnProperty,ao=Object.prototype.propertyIsEnumerable,Ge=(n,e,t)=>e in n?no(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,Li=(n,e)=>{for(var t in e||(e={}))oo.call(e,t)&&Ge(n,t,e[t]);if(zi)for(var t of zi(e))ao.call(e,t)&&Ge(n,t,e[t]);return n},f=(n,e,t)=>Ge(n,typeof e!="symbol"?e+"":e,t);class _e extends Zi{constructor(e){var t;super(e),f(this,"protocol",xe),f(this,"version",Oe),f(this,"name",he),f(this,"relayUrl"),f(this,"projectId"),f(this,"customStoragePrefix"),f(this,"events",new ee),f(this,"logger"),f(this,"heartbeat"),f(this,"relayer"),f(this,"crypto"),f(this,"storage"),f(this,"history"),f(this,"expirer"),f(this,"pairing"),f(this,"verify"),f(this,"echoClient"),f(this,"linkModeSupportedApps"),f(this,"eventClient"),f(this,"initialized",!1),f(this,"logChunkController"),f(this,"on",(o,a)=>this.events.on(o,a)),f(this,"once",(o,a)=>this.events.once(o,a)),f(this,"off",(o,a)=>this.events.off(o,a)),f(this,"removeListener",(o,a)=>this.events.removeListener(o,a)),f(this,"dispatchEnvelope",({topic:o,message:a,sessionExists:c})=>{if(!o||!a)return;const h={topic:o,message:a,publishedAt:Date.now(),transportType:Q.link_mode};this.relayer.onLinkMessageEvent(h,{sessionExists:c})}),this.projectId=e?.projectId,this.relayUrl=e?.relayUrl||$e,this.customStoragePrefix=e!=null&&e.customStoragePrefix?`:${e.customStoragePrefix}`:"";const s=He({level:typeof e?.logger=="string"&&e.logger?e.logger:mt.logger,name:he}),{logger:i,chunkLoggerController:r}=Ki({opts:s,maxSizeInBytes:e?.maxLogBlobSizeInBytes,loggerOverride:e?.logger});this.logChunkController=r,(t=this.logChunkController)!=null&&t.downloadLogsBlobInBrowser&&(window.downloadLogsBlobInBrowser=async()=>{var o,a;(o=this.logChunkController)!=null&&o.downloadLogsBlobInBrowser&&((a=this.logChunkController)==null||a.downloadLogsBlobInBrowser({clientId:await this.crypto.getClientId()}))}),this.logger=N(i,this.name),this.heartbeat=new Ui,this.crypto=new bi(this,this.logger,e?.keychain),this.history=new Si(this,this.logger),this.expirer=new Ri(this,this.logger),this.storage=e!=null&&e.storage?e.storage:new Fi(Li(Li({},vt),e?.storageOptions)),this.relayer=new wi({core:this,logger:this.logger,relayUrl:this.relayUrl,projectId:this.projectId}),this.pairing=new Pi(this,this.logger),this.verify=new xi(this,this.logger,this.storage),this.echoClient=new Ai(this.projectId||"",this.logger),this.linkModeSupportedApps=[],this.eventClient=new $i(this,this.logger,e?.telemetryEnabled)}static async init(e){const t=new _e(e);await t.initialize();const s=await t.crypto.getClientId();return await t.storage.setItem(Nt,s),t}get context(){return U(this.logger)}async start(){this.initialized||await this.initialize()}async getLogsBlob(){var e;return(e=this.logChunkController)==null?void 0:e.logsToBlob({clientId:await this.crypto.getClientId()})}async addLinkModeSupportedApp(e){this.linkModeSupportedApps.includes(e)||(this.linkModeSupportedApps.push(e),await this.storage.setItem(ze,this.linkModeSupportedApps))}async initialize(){this.logger.trace("Initialized");try{await this.crypto.init(),await this.history.init(),await this.expirer.init(),await this.relayer.init(),await this.heartbeat.init(),await this.pairing.init(),this.linkModeSupportedApps=await this.storage.getItem(ze)||[],this.initialized=!0,this.logger.info("Core Initialization Success")}catch(e){throw this.logger.warn(`Core Initialization Failure at epoch ${Date.now()}`,e),this.logger.error(e.message),e}}}const co=_e;export{he as CORE_CONTEXT,mt as CORE_DEFAULT,xe as CORE_PROTOCOL,vt as CORE_STORAGE_OPTIONS,B as CORE_STORAGE_PREFIX,Oe as CORE_VERSION,Ae as CRYPTO_CLIENT_SEED,ft as CRYPTO_CONTEXT,_t as CRYPTO_JWT_TTL,co as Core,bi as Crypto,Ht as ECHO_CONTEXT,Yt as ECHO_URL,Zt as EVENTS_CLIENT_API_URL,Wt as EVENTS_STORAGE_CLEANUP_INTERVAL,Xt as EVENTS_STORAGE_CONTEXT,Jt as EVENTS_STORAGE_VERSION,Ys as EVENT_CLIENT_AUTHENTICATE_ERRORS,Hs as EVENT_CLIENT_AUTHENTICATE_TRACES,Vs as EVENT_CLIENT_CONTEXT,J as EVENT_CLIENT_PAIRING_ERRORS,q as EVENT_CLIENT_PAIRING_TRACES,Gs as EVENT_CLIENT_SESSION_ERRORS,qs as EVENT_CLIENT_SESSION_TRACES,Kt as EXPIRER_CONTEXT,Bs as EXPIRER_DEFAULT_TTL,M as EXPIRER_EVENTS,Bt as EXPIRER_STORAGE_VERSION,Ai as EchoClient,$i as EventClient,Ri as Expirer,Ft as HISTORY_CONTEXT,F as HISTORY_EVENTS,Mt as HISTORY_STORAGE_VERSION,Si as JsonRpcHistory,Et as KEYCHAIN_CONTEXT,wt as KEYCHAIN_STORAGE_VERSION,yi as KeyChain,It as MESSAGES_CONTEXT,Tt as MESSAGES_STORAGE_VERSION,Di as MessageTracker,kt as PAIRING_CONTEXT,Ks as PAIRING_DEFAULT_TTL,se as PAIRING_EVENTS,ie as PAIRING_RPC_OPTS,Ut as PAIRING_STORAGE_VERSION,Lt as PENDING_SUB_RESOLUTION_TIMEOUT,Ct as PUBLISHER_CONTEXT,Ne as PUBLISHER_DEFAULT_TTL,Pi as Pairing,Rt as RELAYER_CONTEXT,St as RELAYER_DEFAULT_LOGGER,Pt as RELAYER_DEFAULT_PROTOCOL,$e as RELAYER_DEFAULT_RELAY_URL,T as RELAYER_EVENTS,L as RELAYER_PROVIDER_EVENTS,Ot as RELAYER_RECONNECT_TIMEOUT,me as RELAYER_SDK_VERSION,Us as RELAYER_STORAGE_OPTIONS,xt as RELAYER_SUBSCRIBER_SUFFIX,Fs as RELAYER_TRANSPORT_CUTOFF,wi as Relayer,At as STORE_STORAGE_VERSION,$t as SUBSCRIBER_CONTEXT,Ms as SUBSCRIBER_DEFAULT_TTL,$ as SUBSCRIBER_EVENTS,zt as SUBSCRIBER_STORAGE_VERSION,Ci as Store,fi as Subscriber,Q as TRANSPORT_TYPES,Gt as TRUSTED_VERIFY_URLS,jt as VERIFY_CONTEXT,le as VERIFY_SERVER,qt as VERIFY_SERVER_V3,xi as Verify,Nt as WALLETCONNECT_CLIENT_ID,ze as WALLETCONNECT_LINK_MODE_APPS,_e as default};
//# sourceMappingURL=index.es.js.map
