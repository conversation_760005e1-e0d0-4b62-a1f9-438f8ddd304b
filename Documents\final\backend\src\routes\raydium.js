const express = require('express');
const router = express.Router();
const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { auth } = require('../middleware/auth');
const fetch = require('node-fetch');

// Solana connection
const SOLANA_RPC_URL = process.env.SOLANA_RPC_URL || 'https://api.devnet.solana.com';
const connection = new Connection(SOLANA_RPC_URL);

// Check if Raydium SDK is available
let Liquidity, Market, RaydiumToken;
let raydiumSdkAvailable = false;

try {
  const raydiumSdk = require('@raydium-io/raydium-sdk');
  Liquidity = raydiumSdk.Liquidity;
  Market = raydiumSdk.Market;
  RaydiumToken = raydiumSdk.Token;
  raydiumSdkAvailable = true;
  console.log('Raydium SDK loaded successfully');
} catch (error) {
  console.error('Failed to load Raydium SDK:', error.message);
}

// Helper function to get a keypair from private key
function getKeypairFromPrivateKey(privateKeyString) {
  try {
    // If the private key is provided as a JSON array
    if (Array.isArray(privateKeyString)) {
      return Keypair.fromSecretKey(new Uint8Array(privateKeyString));
    }

    // If the private key is provided as a JSON string of an array
    if (typeof privateKeyString === 'string' && privateKeyString.startsWith('[')) {
      return Keypair.fromSecretKey(new Uint8Array(JSON.parse(privateKeyString)));
    }

    // If the private key is provided as a base58 string
    return Keypair.fromSecretKey(Buffer.from(privateKeyString, 'base58'));
  } catch (error) {
    console.error('Error creating keypair from private key:', error);
    throw new Error('Invalid private key format');
  }
}

// Raydium liquidity pool creation endpoint
router.post('/create-liquidity-pool', auth, async (req, res) => {
  try {
    const { tokenMint, pairTokenMint, initialPrice, liquidityAmount, tokenAmount } = req.body;

    // Validate required parameters
    if (!tokenMint || !pairTokenMint || !initialPrice || !liquidityAmount || !tokenAmount) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters'
      });
    }

    console.log('Creating liquidity pool with data:', req.body);

    // Check if Raydium SDK is available
    if (!raydiumSdkAvailable) {
      return res.status(503).json({
        success: false,
        message: 'Raydium SDK is not available. Please install it first.'
      });
    }

    try {
      // Get the wallet keypair from environment variable
      const privateKey = process.env.SOLANA_PRIVATE_KEY;
      if (!privateKey) {
        throw new Error('Solana private key not found in environment variables');
      }

      const wallet = getKeypairFromPrivateKey(privateKey);

      // Convert parameters to the correct types
      const tokenMintPubkey = new PublicKey(tokenMint);
      const pairTokenMintPubkey = new PublicKey(pairTokenMint);
      const initialPriceNum = parseFloat(initialPrice);
      const liquidityAmountNum = parseFloat(liquidityAmount);
      const tokenAmountNum = parseInt(tokenAmount);

      // Step 1: Get token information
      const baseToken = new RaydiumToken({
        mint: tokenMintPubkey,
        decimals: 9 // Assuming 9 decimals for the token
      });

      const quoteToken = new RaydiumToken({
        mint: pairTokenMintPubkey,
        decimals: pairTokenMint === 'So11111111111111111111111111111111111111112' ? 9 : 6 // SOL has 9 decimals, USDC/USDT have 6
      });

      // Step 2: Create the market
      const marketId = await Market.makeCreateMarketInstructionSimple({
        connection,
        wallet,
        baseInfo: baseToken,
        quoteInfo: quoteToken,
        lotSize: 1,
        tickSize: 0.01,
        dexProgramId: new PublicKey('9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin'), // Serum DEX program ID
        makeTxVersion: 0
      });

      // Step 3: Create the liquidity pool
      const poolKeys = await Liquidity.makeCreatePoolInstructionSimple({
        connection,
        wallet,
        marketId,
        baseMint: tokenMintPubkey,
        quoteMint: pairTokenMintPubkey,
        baseAmount: tokenAmountNum,
        quoteAmount: liquidityAmountNum,
        startPrice: initialPriceNum,
        makeTxVersion: 0
      });

      // Return the result
      res.json({
        success: true,
        poolId: poolKeys.id.toString(),
        marketId: marketId.toString(),
        message: 'Liquidity pool created successfully'
      });
    } catch (sdkError) {
      console.error('Raydium SDK error:', sdkError);
      res.status(500).json({
        success: false,
        message: `Raydium SDK error: ${sdkError.message}`
      });
    }
  } catch (error) {
    console.error('Error creating liquidity pool:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create liquidity pool'
    });
  }
});

// Get liquidity pools for a token
router.get('/pools/:tokenMint', async (req, res) => {
  try {
    const { tokenMint } = req.params;

    console.log('Fetching liquidity pools for token:', tokenMint);

    // Fetch liquidity pools from Raydium API
    const raydiumApiUrl = process.env.RAYDIUM_POOLS_URL || 'https://api.raydium.io/v2/main/pairs';
    const response = await fetch(raydiumApiUrl);

    if (!response.ok) {
      throw new Error(`Failed to fetch pools from Raydium API: ${response.statusText}`);
    }

    const allPools = await response.json();

    // Filter pools for the specified token
    const tokenPools = allPools.filter(pool =>
      pool.baseMint === tokenMint || pool.quoteMint === tokenMint
    );

    // Format the response
    const formattedPools = tokenPools.map(pool => ({
      poolId: pool.ammId,
      tokenMint: tokenMint,
      pairTokenMint: pool.baseMint === tokenMint ? pool.quoteMint : pool.baseMint,
      liquidity: {
        token: parseFloat(pool.baseMint === tokenMint ? pool.baseVault : pool.quoteVault),
        pairToken: parseFloat(pool.baseMint === tokenMint ? pool.quoteVault : pool.baseVault)
      },
      price: parseFloat(pool.price),
      volume24h: parseFloat(pool.volume24h || 0)
    }));

    res.json({
      success: true,
      pools: formattedPools
    });
  } catch (error) {
    console.error('Error fetching liquidity pools:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch liquidity pools'
    });
  }
});

// Add liquidity to an existing pool
router.post('/add-liquidity', auth, async (req, res) => {
  try {
    const { poolId, tokenAmount, pairTokenAmount } = req.body;

    // Validate required parameters
    if (!poolId || !tokenAmount || !pairTokenAmount) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters'
      });
    }

    console.log('Adding liquidity with data:', req.body);

    // Check if Raydium SDK is available
    if (!raydiumSdkAvailable) {
      return res.status(503).json({
        success: false,
        message: 'Raydium SDK is not available. Please install it first.'
      });
    }

    try {
      // Get the wallet keypair from environment variable
      const privateKey = process.env.SOLANA_PRIVATE_KEY;
      if (!privateKey) {
        throw new Error('Solana private key not found in environment variables');
      }

      const wallet = getKeypairFromPrivateKey(privateKey);

      // Convert parameters to the correct types
      const poolIdPubkey = new PublicKey(poolId);
      const tokenAmountNum = parseFloat(tokenAmount);
      const pairTokenAmountNum = parseFloat(pairTokenAmount);

      // Get the pool information
      const poolInfo = await Liquidity.fetchInfo({
        connection,
        poolKeys: {
          id: poolIdPubkey
        }
      });

      // Add liquidity to the pool
      const addLiquidityResult = await Liquidity.addLiquidity({
        connection,
        wallet,
        poolInfo,
        amountIn: tokenAmountNum,
        amountOut: pairTokenAmountNum,
        fixedSide: 'in'
      });

      // Return the result
      res.json({
        success: true,
        signature: addLiquidityResult.txid,
        message: 'Liquidity added successfully'
      });
    } catch (sdkError) {
      console.error('Raydium SDK error:', sdkError);
      res.status(500).json({
        success: false,
        message: `Raydium SDK error: ${sdkError.message}`
      });
    }
  } catch (error) {
    console.error('Error adding liquidity:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to add liquidity'
    });
  }
});

module.exports = router;
