{"version": 3, "sources": ["../../qrcode/lib/can-promise.js", "../../qrcode/lib/core/utils.js", "../../qrcode/lib/core/error-correction-level.js", "../../qrcode/lib/core/bit-buffer.js", "../../qrcode/lib/core/bit-matrix.js", "../../qrcode/lib/core/alignment-pattern.js", "../../qrcode/lib/core/finder-pattern.js", "../../qrcode/lib/core/mask-pattern.js", "../../qrcode/lib/core/error-correction-code.js", "../../qrcode/lib/core/galois-field.js", "../../qrcode/lib/core/polynomial.js", "../../qrcode/lib/core/reed-solomon-encoder.js", "../../qrcode/lib/core/version-check.js", "../../qrcode/lib/core/regex.js", "../../qrcode/lib/core/mode.js", "../../qrcode/lib/core/version.js", "../../qrcode/lib/core/format-info.js", "../../qrcode/lib/core/numeric-data.js", "../../qrcode/lib/core/alphanumeric-data.js", "../../qrcode/lib/core/byte-data.js", "../../qrcode/lib/core/kanji-data.js", "../../qrcode/lib/core/segments.js", "../../qrcode/lib/core/qrcode.js", "../../qrcode/lib/renderer/utils.js", "../../qrcode/lib/renderer/canvas.js", "../../qrcode/lib/renderer/svg-tag.js", "../../qrcode/lib/browser.js", "../../@solana-mobile/mobile-wallet-adapter-protocol-web3js/node_modules/base-x/src/index.js", "../../@solana-mobile/mobile-wallet-adapter-protocol-web3js/node_modules/bs58/index.js", "../../@solana-mobile/wallet-standard-mobile/node_modules/base-x/src/index.js", "../../@solana-mobile/wallet-standard-mobile/node_modules/bs58/index.js", "../../@solana/wallet-adapter-react/src/ConnectionProvider.tsx", "../../@solana/wallet-adapter-react/src/useConnection.ts", "../../@solana/wallet-adapter-react/src/errors.ts", "../../@solana/wallet-adapter-react/src/useAnchorWallet.ts", "../../@solana/wallet-adapter-react/src/useWallet.ts", "../../@solana/wallet-adapter-react/src/useLocalStorage.ts", "../../@solana-mobile/wallet-adapter-mobile/lib/esm/index.browser.js", "../../@solana-mobile/wallet-standard-mobile/lib/esm/index.browser.js", "../../@solana-mobile/mobile-wallet-adapter-protocol-web3js/lib/esm/index.browser.js", "../../@solana-mobile/mobile-wallet-adapter-protocol-web3js/node_modules/@solana-mobile/mobile-wallet-adapter-protocol/lib/esm/index.browser.js", "../../@solana/wallet-standard-wallet-adapter-base/src/adapter.ts", "../../@solana/wallet-standard-wallet-adapter-base/src/types.ts", "../../@solana/wallet-standard-wallet-adapter-base/src/wallet.ts", "../../@wallet-standard/app/src/wallets.ts", "../../@solana/wallet-standard-wallet-adapter-react/src/useStandardWalletAdapters.ts", "../../@solana/wallet-adapter-react/src/WalletProvider.tsx", "../../@solana/wallet-adapter-react/src/getEnvironment.ts", "../../@solana/wallet-adapter-react/src/getInferredClusterFromEndpoint.ts", "../../@solana/wallet-adapter-react/src/WalletProviderBase.tsx"], "sourcesContent": ["// can-promise has a crash in some versions of react native that dont have\n// standard global objects\n// https://github.com/soldair/node-qrcode/issues/157\n\nmodule.exports = function () {\n  return typeof Promise === 'function' && Promise.prototype && Promise.prototype.then\n}\n", "let toSJISFunction\nconst CODEWORDS_COUNT = [\n  0, // Not used\n  26, 44, 70, 100, 134, 172, 196, 242, 292, 346,\n  404, 466, 532, 581, 655, 733, 815, 901, 991, 1085,\n  1156, 1258, 1364, 1474, 1588, 1706, 1828, 1921, 2051, 2185,\n  2323, 2465, 2611, 2761, 2876, 3034, 3196, 3362, 3532, 3706\n]\n\n/**\n * Returns the QR Code size for the specified version\n *\n * @param  {Number} version QR Code version\n * @return {Number}         size of QR code\n */\nexports.getSymbolSize = function getSymbolSize (version) {\n  if (!version) throw new Error('\"version\" cannot be null or undefined')\n  if (version < 1 || version > 40) throw new Error('\"version\" should be in range from 1 to 40')\n  return version * 4 + 17\n}\n\n/**\n * Returns the total number of codewords used to store data and EC information.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Data length in bits\n */\nexports.getSymbolTotalCodewords = function getSymbolTotalCodewords (version) {\n  return CODEWORDS_COUNT[version]\n}\n\n/**\n * Encode data with Bose-Chaudhuri-Hocquenghem\n *\n * @param  {Number} data Value to encode\n * @return {Number}      Encoded value\n */\nexports.getBCHDigit = function (data) {\n  let digit = 0\n\n  while (data !== 0) {\n    digit++\n    data >>>= 1\n  }\n\n  return digit\n}\n\nexports.setToSJISFunction = function setToSJISFunction (f) {\n  if (typeof f !== 'function') {\n    throw new Error('\"toSJISFunc\" is not a valid function.')\n  }\n\n  toSJISFunction = f\n}\n\nexports.isKanjiModeEnabled = function () {\n  return typeof toSJISFunction !== 'undefined'\n}\n\nexports.toSJIS = function toSJIS (kanji) {\n  return toSJISFunction(kanji)\n}\n", "exports.L = { bit: 1 }\nexports.M = { bit: 0 }\nexports.Q = { bit: 3 }\nexports.H = { bit: 2 }\n\nfunction fromString (string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string')\n  }\n\n  const lcStr = string.toLowerCase()\n\n  switch (lcStr) {\n    case 'l':\n    case 'low':\n      return exports.L\n\n    case 'm':\n    case 'medium':\n      return exports.M\n\n    case 'q':\n    case 'quartile':\n      return exports.Q\n\n    case 'h':\n    case 'high':\n      return exports.H\n\n    default:\n      throw new Error('Unknown EC Level: ' + string)\n  }\n}\n\nexports.isValid = function isValid (level) {\n  return level && typeof level.bit !== 'undefined' &&\n    level.bit >= 0 && level.bit < 4\n}\n\nexports.from = function from (value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value\n  }\n\n  try {\n    return fromString(value)\n  } catch (e) {\n    return defaultValue\n  }\n}\n", "function BitBuffer () {\n  this.buffer = []\n  this.length = 0\n}\n\nBitBuffer.prototype = {\n\n  get: function (index) {\n    const bufIndex = Math.floor(index / 8)\n    return ((this.buffer[bufIndex] >>> (7 - index % 8)) & 1) === 1\n  },\n\n  put: function (num, length) {\n    for (let i = 0; i < length; i++) {\n      this.putBit(((num >>> (length - i - 1)) & 1) === 1)\n    }\n  },\n\n  getLengthInBits: function () {\n    return this.length\n  },\n\n  putBit: function (bit) {\n    const bufIndex = Math.floor(this.length / 8)\n    if (this.buffer.length <= bufIndex) {\n      this.buffer.push(0)\n    }\n\n    if (bit) {\n      this.buffer[bufIndex] |= (0x80 >>> (this.length % 8))\n    }\n\n    this.length++\n  }\n}\n\nmodule.exports = BitBuffer\n", "/**\n * Helper class to handle QR Code symbol modules\n *\n * @param {Number} size Symbol size\n */\nfunction BitMatrix (size) {\n  if (!size || size < 1) {\n    throw new Error('BitMatrix size must be defined and greater than 0')\n  }\n\n  this.size = size\n  this.data = new Uint8Array(size * size)\n  this.reservedBit = new Uint8Array(size * size)\n}\n\n/**\n * Set bit value at specified location\n * If reserved flag is set, this bit will be ignored during masking process\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n * @param {Boolean} reserved\n */\nBitMatrix.prototype.set = function (row, col, value, reserved) {\n  const index = row * this.size + col\n  this.data[index] = value\n  if (reserved) this.reservedBit[index] = true\n}\n\n/**\n * Returns bit value at specified location\n *\n * @param  {Number}  row\n * @param  {Number}  col\n * @return {Boolean}\n */\nBitMatrix.prototype.get = function (row, col) {\n  return this.data[row * this.size + col]\n}\n\n/**\n * Applies xor operator at specified location\n * (used during masking process)\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n */\nBitMatrix.prototype.xor = function (row, col, value) {\n  this.data[row * this.size + col] ^= value\n}\n\n/**\n * Check if bit at specified location is reserved\n *\n * @param {Number}   row\n * @param {Number}   col\n * @return {Boolean}\n */\nBitMatrix.prototype.isReserved = function (row, col) {\n  return this.reservedBit[row * this.size + col]\n}\n\nmodule.exports = BitMatrix\n", "/**\n * Alignment pattern are fixed reference pattern in defined positions\n * in a matrix symbology, which enables the decode software to re-synchronise\n * the coordinate mapping of the image modules in the event of moderate amounts\n * of distortion of the image.\n *\n * Alignment patterns are present only in QR Code symbols of version 2 or larger\n * and their number depends on the symbol version.\n */\n\nconst getSymbolSize = require('./utils').getSymbolSize\n\n/**\n * Calculate the row/column coordinates of the center module of each alignment pattern\n * for the specified QR Code version.\n *\n * The alignment patterns are positioned symmetrically on either side of the diagonal\n * running from the top left corner of the symbol to the bottom right corner.\n *\n * Since positions are simmetrical only half of the coordinates are returned.\n * Each item of the array will represent in turn the x and y coordinate.\n * @see {@link getPositions}\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinate\n */\nexports.getRowColCoords = function getRowColCoords (version) {\n  if (version === 1) return []\n\n  const posCount = Math.floor(version / 7) + 2\n  const size = getSymbolSize(version)\n  const intervals = size === 145 ? 26 : Math.ceil((size - 13) / (2 * posCount - 2)) * 2\n  const positions = [size - 7] // Last coord is always (size - 7)\n\n  for (let i = 1; i < posCount - 1; i++) {\n    positions[i] = positions[i - 1] - intervals\n  }\n\n  positions.push(6) // First coord is always 6\n\n  return positions.reverse()\n}\n\n/**\n * Returns an array containing the positions of each alignment pattern.\n * Each array's element represent the center point of the pattern as (x, y) coordinates\n *\n * Coordinates are calculated expanding the row/column coordinates returned by {@link getRowColCoords}\n * and filtering out the items that overlaps with finder pattern\n *\n * @example\n * For a Version 7 symbol {@link getRowColCoords} returns values 6, 22 and 38.\n * The alignment patterns, therefore, are to be centered on (row, column)\n * positions (6,22), (22,6), (22,22), (22,38), (38,22), (38,38).\n * Note that the coordinates (6,6), (6,38), (38,6) are occupied by finder patterns\n * and are not therefore used for alignment patterns.\n *\n * let pos = getPositions(7)\n * // [[6,22], [22,6], [22,22], [22,38], [38,22], [38,38]]\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions (version) {\n  const coords = []\n  const pos = exports.getRowColCoords(version)\n  const posLength = pos.length\n\n  for (let i = 0; i < posLength; i++) {\n    for (let j = 0; j < posLength; j++) {\n      // Skip if position is occupied by finder patterns\n      if ((i === 0 && j === 0) || // top-left\n          (i === 0 && j === posLength - 1) || // bottom-left\n          (i === posLength - 1 && j === 0)) { // top-right\n        continue\n      }\n\n      coords.push([pos[i], pos[j]])\n    }\n  }\n\n  return coords\n}\n", "const getSymbolSize = require('./utils').getSymbolSize\nconst FINDER_PATTERN_SIZE = 7\n\n/**\n * Returns an array containing the positions of each finder pattern.\n * Each array's element represent the top-left point of the pattern as (x, y) coordinates\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions (version) {\n  const size = getSymbolSize(version)\n\n  return [\n    // top-left\n    [0, 0],\n    // top-right\n    [size - FINDER_PATTERN_SIZE, 0],\n    // bottom-left\n    [0, size - FINDER_PATTERN_SIZE]\n  ]\n}\n", "/**\n * Data mask pattern reference\n * @type {Object}\n */\nexports.Patterns = {\n  PATTERN000: 0,\n  PATTERN001: 1,\n  PATTERN010: 2,\n  PATTERN011: 3,\n  PATTERN100: 4,\n  PATTERN101: 5,\n  PATTERN110: 6,\n  PATTERN111: 7\n}\n\n/**\n * Weighted penalty scores for the undesirable features\n * @type {Object}\n */\nconst PenaltyScores = {\n  N1: 3,\n  N2: 3,\n  N3: 40,\n  N4: 10\n}\n\n/**\n * Check if mask pattern value is valid\n *\n * @param  {Number}  mask    Mask pattern\n * @return {Boolean}         true if valid, false otherwise\n */\nexports.isValid = function isValid (mask) {\n  return mask != null && mask !== '' && !isNaN(mask) && mask >= 0 && mask <= 7\n}\n\n/**\n * Returns mask pattern from a value.\n * If value is not valid, returns undefined\n *\n * @param  {Number|String} value        Mask pattern value\n * @return {Number}                     Valid mask pattern or undefined\n */\nexports.from = function from (value) {\n  return exports.isValid(value) ? parseInt(value, 10) : undefined\n}\n\n/**\n* Find adjacent modules in row/column with the same color\n* and assign a penalty value.\n*\n* Points: N1 + i\n* i is the amount by which the number of adjacent modules of the same color exceeds 5\n*/\nexports.getPenaltyN1 = function getPenaltyN1 (data) {\n  const size = data.size\n  let points = 0\n  let sameCountCol = 0\n  let sameCountRow = 0\n  let lastCol = null\n  let lastRow = null\n\n  for (let row = 0; row < size; row++) {\n    sameCountCol = sameCountRow = 0\n    lastCol = lastRow = null\n\n    for (let col = 0; col < size; col++) {\n      let module = data.get(row, col)\n      if (module === lastCol) {\n        sameCountCol++\n      } else {\n        if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5)\n        lastCol = module\n        sameCountCol = 1\n      }\n\n      module = data.get(col, row)\n      if (module === lastRow) {\n        sameCountRow++\n      } else {\n        if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5)\n        lastRow = module\n        sameCountRow = 1\n      }\n    }\n\n    if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5)\n    if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5)\n  }\n\n  return points\n}\n\n/**\n * Find 2x2 blocks with the same color and assign a penalty value\n *\n * Points: N2 * (m - 1) * (n - 1)\n */\nexports.getPenaltyN2 = function getPenaltyN2 (data) {\n  const size = data.size\n  let points = 0\n\n  for (let row = 0; row < size - 1; row++) {\n    for (let col = 0; col < size - 1; col++) {\n      const last = data.get(row, col) +\n        data.get(row, col + 1) +\n        data.get(row + 1, col) +\n        data.get(row + 1, col + 1)\n\n      if (last === 4 || last === 0) points++\n    }\n  }\n\n  return points * PenaltyScores.N2\n}\n\n/**\n * Find 1:1:3:1:1 ratio (dark:light:dark:light:dark) pattern in row/column,\n * preceded or followed by light area 4 modules wide\n *\n * Points: N3 * number of pattern found\n */\nexports.getPenaltyN3 = function getPenaltyN3 (data) {\n  const size = data.size\n  let points = 0\n  let bitsCol = 0\n  let bitsRow = 0\n\n  for (let row = 0; row < size; row++) {\n    bitsCol = bitsRow = 0\n    for (let col = 0; col < size; col++) {\n      bitsCol = ((bitsCol << 1) & 0x7FF) | data.get(row, col)\n      if (col >= 10 && (bitsCol === 0x5D0 || bitsCol === 0x05D)) points++\n\n      bitsRow = ((bitsRow << 1) & 0x7FF) | data.get(col, row)\n      if (col >= 10 && (bitsRow === 0x5D0 || bitsRow === 0x05D)) points++\n    }\n  }\n\n  return points * PenaltyScores.N3\n}\n\n/**\n * Calculate proportion of dark modules in entire symbol\n *\n * Points: N4 * k\n *\n * k is the rating of the deviation of the proportion of dark modules\n * in the symbol from 50% in steps of 5%\n */\nexports.getPenaltyN4 = function getPenaltyN4 (data) {\n  let darkCount = 0\n  const modulesCount = data.data.length\n\n  for (let i = 0; i < modulesCount; i++) darkCount += data.data[i]\n\n  const k = Math.abs(Math.ceil((darkCount * 100 / modulesCount) / 5) - 10)\n\n  return k * PenaltyScores.N4\n}\n\n/**\n * Return mask value at given position\n *\n * @param  {Number} maskPattern Pattern reference value\n * @param  {Number} i           Row\n * @param  {Number} j           Column\n * @return {Boolean}            Mask value\n */\nfunction getMaskAt (maskPattern, i, j) {\n  switch (maskPattern) {\n    case exports.Patterns.PATTERN000: return (i + j) % 2 === 0\n    case exports.Patterns.PATTERN001: return i % 2 === 0\n    case exports.Patterns.PATTERN010: return j % 3 === 0\n    case exports.Patterns.PATTERN011: return (i + j) % 3 === 0\n    case exports.Patterns.PATTERN100: return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 === 0\n    case exports.Patterns.PATTERN101: return (i * j) % 2 + (i * j) % 3 === 0\n    case exports.Patterns.PATTERN110: return ((i * j) % 2 + (i * j) % 3) % 2 === 0\n    case exports.Patterns.PATTERN111: return ((i * j) % 3 + (i + j) % 2) % 2 === 0\n\n    default: throw new Error('bad maskPattern:' + maskPattern)\n  }\n}\n\n/**\n * Apply a mask pattern to a BitMatrix\n *\n * @param  {Number}    pattern Pattern reference number\n * @param  {BitMatrix} data    BitMatrix data\n */\nexports.applyMask = function applyMask (pattern, data) {\n  const size = data.size\n\n  for (let col = 0; col < size; col++) {\n    for (let row = 0; row < size; row++) {\n      if (data.isReserved(row, col)) continue\n      data.xor(row, col, getMaskAt(pattern, row, col))\n    }\n  }\n}\n\n/**\n * Returns the best mask pattern for data\n *\n * @param  {BitMatrix} data\n * @return {Number} Mask pattern reference number\n */\nexports.getBestMask = function getBestMask (data, setupFormatFunc) {\n  const numPatterns = Object.keys(exports.Patterns).length\n  let bestPattern = 0\n  let lowerPenalty = Infinity\n\n  for (let p = 0; p < numPatterns; p++) {\n    setupFormatFunc(p)\n    exports.applyMask(p, data)\n\n    // Calculate penalty\n    const penalty =\n      exports.getPenaltyN1(data) +\n      exports.getPenaltyN2(data) +\n      exports.getPenaltyN3(data) +\n      exports.getPenaltyN4(data)\n\n    // Undo previously applied mask\n    exports.applyMask(p, data)\n\n    if (penalty < lowerPenalty) {\n      lowerPenalty = penalty\n      bestPattern = p\n    }\n  }\n\n  return bestPattern\n}\n", "const ECLevel = require('./error-correction-level')\r\n\r\nconst EC_BLOCKS_TABLE = [\r\n// L  M  Q  H\r\n  1, 1, 1, 1,\r\n  1, 1, 1, 1,\r\n  1, 1, 2, 2,\r\n  1, 2, 2, 4,\r\n  1, 2, 4, 4,\r\n  2, 4, 4, 4,\r\n  2, 4, 6, 5,\r\n  2, 4, 6, 6,\r\n  2, 5, 8, 8,\r\n  4, 5, 8, 8,\r\n  4, 5, 8, 11,\r\n  4, 8, 10, 11,\r\n  4, 9, 12, 16,\r\n  4, 9, 16, 16,\r\n  6, 10, 12, 18,\r\n  6, 10, 17, 16,\r\n  6, 11, 16, 19,\r\n  6, 13, 18, 21,\r\n  7, 14, 21, 25,\r\n  8, 16, 20, 25,\r\n  8, 17, 23, 25,\r\n  9, 17, 23, 34,\r\n  9, 18, 25, 30,\r\n  10, 20, 27, 32,\r\n  12, 21, 29, 35,\r\n  12, 23, 34, 37,\r\n  12, 25, 34, 40,\r\n  13, 26, 35, 42,\r\n  14, 28, 38, 45,\r\n  15, 29, 40, 48,\r\n  16, 31, 43, 51,\r\n  17, 33, 45, 54,\r\n  18, 35, 48, 57,\r\n  19, 37, 51, 60,\r\n  19, 38, 53, 63,\r\n  20, 40, 56, 66,\r\n  21, 43, 59, 70,\r\n  22, 45, 62, 74,\r\n  24, 47, 65, 77,\r\n  25, 49, 68, 81\r\n]\r\n\r\nconst EC_CODEWORDS_TABLE = [\r\n// L  M  Q  H\r\n  7, 10, 13, 17,\r\n  10, 16, 22, 28,\r\n  15, 26, 36, 44,\r\n  20, 36, 52, 64,\r\n  26, 48, 72, 88,\r\n  36, 64, 96, 112,\r\n  40, 72, 108, 130,\r\n  48, 88, 132, 156,\r\n  60, 110, 160, 192,\r\n  72, 130, 192, 224,\r\n  80, 150, 224, 264,\r\n  96, 176, 260, 308,\r\n  104, 198, 288, 352,\r\n  120, 216, 320, 384,\r\n  132, 240, 360, 432,\r\n  144, 280, 408, 480,\r\n  168, 308, 448, 532,\r\n  180, 338, 504, 588,\r\n  196, 364, 546, 650,\r\n  224, 416, 600, 700,\r\n  224, 442, 644, 750,\r\n  252, 476, 690, 816,\r\n  270, 504, 750, 900,\r\n  300, 560, 810, 960,\r\n  312, 588, 870, 1050,\r\n  336, 644, 952, 1110,\r\n  360, 700, 1020, 1200,\r\n  390, 728, 1050, 1260,\r\n  420, 784, 1140, 1350,\r\n  450, 812, 1200, 1440,\r\n  480, 868, 1290, 1530,\r\n  510, 924, 1350, 1620,\r\n  540, 980, 1440, 1710,\r\n  570, 1036, 1530, 1800,\r\n  570, 1064, 1590, 1890,\r\n  600, 1120, 1680, 1980,\r\n  630, 1204, 1770, 2100,\r\n  660, 1260, 1860, 2220,\r\n  720, 1316, 1950, 2310,\r\n  750, 1372, 2040, 2430\r\n]\r\n\r\n/**\r\n * Returns the number of error correction block that the QR Code should contain\r\n * for the specified version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction blocks\r\n */\r\nexports.getBlocksCount = function getBlocksCount (version, errorCorrectionLevel) {\r\n  switch (errorCorrectionLevel) {\r\n    case ECLevel.L:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 0]\r\n    case ECLevel.M:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 1]\r\n    case ECLevel.Q:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 2]\r\n    case ECLevel.H:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 3]\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the number of error correction codewords to use for the specified\r\n * version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction codewords\r\n */\r\nexports.getTotalCodewordsCount = function getTotalCodewordsCount (version, errorCorrectionLevel) {\r\n  switch (errorCorrectionLevel) {\r\n    case ECLevel.L:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 0]\r\n    case ECLevel.M:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 1]\r\n    case ECLevel.Q:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 2]\r\n    case ECLevel.H:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 3]\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n", "const EXP_TABLE = new Uint8Array(512)\nconst LOG_TABLE = new Uint8Array(256)\n/**\n * Precompute the log and anti-log tables for faster computation later\n *\n * For each possible value in the galois field 2^8, we will pre-compute\n * the logarithm and anti-logarithm (exponential) of this value\n *\n * ref {@link https://en.wikiversity.org/wiki/Reed%E2%80%93Solomon_codes_for_coders#Introduction_to_mathematical_fields}\n */\n;(function initTables () {\n  let x = 1\n  for (let i = 0; i < 255; i++) {\n    EXP_TABLE[i] = x\n    LOG_TABLE[x] = i\n\n    x <<= 1 // multiply by 2\n\n    // The QR code specification says to use byte-wise modulo 100011101 arithmetic.\n    // This means that when a number is 256 or larger, it should be XORed with 0x11D.\n    if (x & 0x100) { // similar to x >= 256, but a lot faster (because 0x100 == 256)\n      x ^= 0x11D\n    }\n  }\n\n  // Optimization: double the size of the anti-log table so that we don't need to mod 255 to\n  // stay inside the bounds (because we will mainly use this table for the multiplication of\n  // two GF numbers, no more).\n  // @see {@link mul}\n  for (let i = 255; i < 512; i++) {\n    EXP_TABLE[i] = EXP_TABLE[i - 255]\n  }\n}())\n\n/**\n * Returns log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.log = function log (n) {\n  if (n < 1) throw new Error('log(' + n + ')')\n  return LOG_TABLE[n]\n}\n\n/**\n * Returns anti-log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.exp = function exp (n) {\n  return EXP_TABLE[n]\n}\n\n/**\n * Multiplies two number inside Galois Field\n *\n * @param  {Number} x\n * @param  {Number} y\n * @return {Number}\n */\nexports.mul = function mul (x, y) {\n  if (x === 0 || y === 0) return 0\n\n  // should be EXP_TABLE[(LOG_TABLE[x] + LOG_TABLE[y]) % 255] if EXP_TABLE wasn't oversized\n  // @see {@link initTables}\n  return EXP_TABLE[LOG_TABLE[x] + LOG_TABLE[y]]\n}\n", "const GF = require('./galois-field')\n\n/**\n * Multiplies two polynomials inside Galois Field\n *\n * @param  {Uint8Array} p1 Polynomial\n * @param  {Uint8Array} p2 Polynomial\n * @return {Uint8Array}    Product of p1 and p2\n */\nexports.mul = function mul (p1, p2) {\n  const coeff = new Uint8Array(p1.length + p2.length - 1)\n\n  for (let i = 0; i < p1.length; i++) {\n    for (let j = 0; j < p2.length; j++) {\n      coeff[i + j] ^= GF.mul(p1[i], p2[j])\n    }\n  }\n\n  return coeff\n}\n\n/**\n * Calculate the remainder of polynomials division\n *\n * @param  {Uint8Array} divident Polynomial\n * @param  {Uint8Array} divisor  Polynomial\n * @return {Uint8Array}          Remainder\n */\nexports.mod = function mod (divident, divisor) {\n  let result = new Uint8Array(divident)\n\n  while ((result.length - divisor.length) >= 0) {\n    const coeff = result[0]\n\n    for (let i = 0; i < divisor.length; i++) {\n      result[i] ^= GF.mul(divisor[i], coeff)\n    }\n\n    // remove all zeros from buffer head\n    let offset = 0\n    while (offset < result.length && result[offset] === 0) offset++\n    result = result.slice(offset)\n  }\n\n  return result\n}\n\n/**\n * Generate an irreducible generator polynomial of specified degree\n * (used by Reed-Solomon encoder)\n *\n * @param  {Number} degree Degree of the generator polynomial\n * @return {Uint8Array}    Buffer containing polynomial coefficients\n */\nexports.generateECPolynomial = function generateECPolynomial (degree) {\n  let poly = new Uint8Array([1])\n  for (let i = 0; i < degree; i++) {\n    poly = exports.mul(poly, new Uint8Array([1, GF.exp(i)]))\n  }\n\n  return poly\n}\n", "const Polynomial = require('./polynomial')\n\nfunction ReedSolomonEncoder (degree) {\n  this.genPoly = undefined\n  this.degree = degree\n\n  if (this.degree) this.initialize(this.degree)\n}\n\n/**\n * Initialize the encoder.\n * The input param should correspond to the number of error correction codewords.\n *\n * @param  {Number} degree\n */\nReedSolomonEncoder.prototype.initialize = function initialize (degree) {\n  // create an irreducible generator polynomial\n  this.degree = degree\n  this.genPoly = Polynomial.generateECPolynomial(this.degree)\n}\n\n/**\n * Encodes a chunk of data\n *\n * @param  {Uint8Array} data Buffer containing input data\n * @return {Uint8Array}      Buffer containing encoded data\n */\nReedSolomonEncoder.prototype.encode = function encode (data) {\n  if (!this.genPoly) {\n    throw new Error('Encoder not initialized')\n  }\n\n  // Calculate EC for this data block\n  // extends data size to data+genPoly size\n  const paddedData = new Uint8Array(data.length + this.degree)\n  paddedData.set(data)\n\n  // The error correction codewords are the remainder after dividing the data codewords\n  // by a generator polynomial\n  const remainder = Polynomial.mod(paddedData, this.genPoly)\n\n  // return EC data blocks (last n byte, where n is the degree of genPoly)\n  // If coefficients number in remainder are less than genPoly degree,\n  // pad with 0s to the left to reach the needed number of coefficients\n  const start = this.degree - remainder.length\n  if (start > 0) {\n    const buff = new Uint8Array(this.degree)\n    buff.set(remainder, start)\n\n    return buff\n  }\n\n  return remainder\n}\n\nmodule.exports = ReedSolomonEncoder\n", "/**\n * Check if QR Code version is valid\n *\n * @param  {Number}  version QR Code version\n * @return {Boolean}         true if valid version, false otherwise\n */\nexports.isValid = function isValid (version) {\n  return !isNaN(version) && version >= 1 && version <= 40\n}\n", "const numeric = '[0-9]+'\nconst alphanumeric = '[A-Z $%*+\\\\-./:]+'\nlet kanji = '(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|' +\n  '[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|' +\n  '[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|' +\n  '[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+'\nkanji = kanji.replace(/u/g, '\\\\u')\n\nconst byte = '(?:(?![A-Z0-9 $%*+\\\\-./:]|' + kanji + ')(?:.|[\\r\\n]))+'\n\nexports.KANJI = new RegExp(kanji, 'g')\nexports.BYTE_KANJI = new RegExp('[^A-Z0-9 $%*+\\\\-./:]+', 'g')\nexports.BYTE = new RegExp(byte, 'g')\nexports.NUMERIC = new RegExp(numeric, 'g')\nexports.ALPHANUMERIC = new RegExp(alphanumeric, 'g')\n\nconst TEST_KANJI = new RegExp('^' + kanji + '$')\nconst TEST_NUMERIC = new RegExp('^' + numeric + '$')\nconst TEST_ALPHANUMERIC = new RegExp('^[A-Z0-9 $%*+\\\\-./:]+$')\n\nexports.testKanji = function testKanji (str) {\n  return TEST_KANJI.test(str)\n}\n\nexports.testNumeric = function testNumeric (str) {\n  return TEST_NUMERIC.test(str)\n}\n\nexports.testAlphanumeric = function testAlphanumeric (str) {\n  return TEST_ALPHANUMERIC.test(str)\n}\n", "const VersionCheck = require('./version-check')\nconst Regex = require('./regex')\n\n/**\n * Numeric mode encodes data from the decimal digit set (0 - 9)\n * (byte values 30HEX to 39HEX).\n * Normally, 3 data characters are represented by 10 bits.\n *\n * @type {Object}\n */\nexports.NUMERIC = {\n  id: 'Numeric',\n  bit: 1 << 0,\n  ccBits: [10, 12, 14]\n}\n\n/**\n * Alphanumeric mode encodes data from a set of 45 characters,\n * i.e. 10 numeric digits (0 - 9),\n *      26 alphabetic characters (A - Z),\n *   and 9 symbols (SP, $, %, *, +, -, ., /, :).\n * Normally, two input characters are represented by 11 bits.\n *\n * @type {Object}\n */\nexports.ALPHANUMERIC = {\n  id: 'Alphanumeric',\n  bit: 1 << 1,\n  ccBits: [9, 11, 13]\n}\n\n/**\n * In byte mode, data is encoded at 8 bits per character.\n *\n * @type {Object}\n */\nexports.BYTE = {\n  id: 'Byte',\n  bit: 1 << 2,\n  ccBits: [8, 16, 16]\n}\n\n/**\n * The Kanji mode efficiently encodes Kanji characters in accordance with\n * the Shift JIS system based on JIS X 0208.\n * The Shift JIS values are shifted from the JIS X 0208 values.\n * JIS X 0208 gives details of the shift coded representation.\n * Each two-byte character value is compacted to a 13-bit binary codeword.\n *\n * @type {Object}\n */\nexports.KANJI = {\n  id: 'Kanji',\n  bit: 1 << 3,\n  ccBits: [8, 10, 12]\n}\n\n/**\n * Mixed mode will contain a sequences of data in a combination of any of\n * the modes described above\n *\n * @type {Object}\n */\nexports.MIXED = {\n  bit: -1\n}\n\n/**\n * Returns the number of bits needed to store the data length\n * according to QR Code specifications.\n *\n * @param  {Mode}   mode    Data mode\n * @param  {Number} version QR Code version\n * @return {Number}         Number of bits\n */\nexports.getCharCountIndicator = function getCharCountIndicator (mode, version) {\n  if (!mode.ccBits) throw new Error('Invalid mode: ' + mode)\n\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid version: ' + version)\n  }\n\n  if (version >= 1 && version < 10) return mode.ccBits[0]\n  else if (version < 27) return mode.ccBits[1]\n  return mode.ccBits[2]\n}\n\n/**\n * Returns the most efficient mode to store the specified data\n *\n * @param  {String} dataStr Input data string\n * @return {Mode}           Best mode\n */\nexports.getBestModeForData = function getBestModeForData (dataStr) {\n  if (Regex.testNumeric(dataStr)) return exports.NUMERIC\n  else if (Regex.testAlphanumeric(dataStr)) return exports.ALPHANUMERIC\n  else if (Regex.testKanji(dataStr)) return exports.KANJI\n  else return exports.BYTE\n}\n\n/**\n * Return mode name as string\n *\n * @param {Mode} mode Mode object\n * @returns {String}  Mode name\n */\nexports.toString = function toString (mode) {\n  if (mode && mode.id) return mode.id\n  throw new Error('Invalid mode')\n}\n\n/**\n * Check if input param is a valid mode object\n *\n * @param   {Mode}    mode Mode object\n * @returns {Boolean} True if valid mode, false otherwise\n */\nexports.isValid = function isValid (mode) {\n  return mode && mode.bit && mode.ccBits\n}\n\n/**\n * Get mode object from its name\n *\n * @param   {String} string Mode name\n * @returns {Mode}          Mode object\n */\nfunction fromString (string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string')\n  }\n\n  const lcStr = string.toLowerCase()\n\n  switch (lcStr) {\n    case 'numeric':\n      return exports.NUMERIC\n    case 'alphanumeric':\n      return exports.ALPHANUMERIC\n    case 'kanji':\n      return exports.KANJI\n    case 'byte':\n      return exports.BYTE\n    default:\n      throw new Error('Unknown mode: ' + string)\n  }\n}\n\n/**\n * Returns mode from a value.\n * If value is not a valid mode, returns defaultValue\n *\n * @param  {Mode|String} value        Encoding mode\n * @param  {Mode}        defaultValue Fallback value\n * @return {Mode}                     Encoding mode\n */\nexports.from = function from (value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value\n  }\n\n  try {\n    return fromString(value)\n  } catch (e) {\n    return defaultValue\n  }\n}\n", "const Utils = require('./utils')\nconst ECCode = require('./error-correction-code')\nconst ECLevel = require('./error-correction-level')\nconst Mode = require('./mode')\nconst VersionCheck = require('./version-check')\n\n// Generator polynomial used to encode version information\nconst G18 = (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0)\nconst G18_BCH = Utils.getBCHDigit(G18)\n\nfunction getBestVersionForDataLength (mode, length, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, mode)) {\n      return currentVersion\n    }\n  }\n\n  return undefined\n}\n\nfunction getReservedBitsCount (mode, version) {\n  // Character count indicator + mode indicator bits\n  return Mode.getCharCountIndicator(mode, version) + 4\n}\n\nfunction getTotalBitsFromDataArray (segments, version) {\n  let totalBits = 0\n\n  segments.forEach(function (data) {\n    const reservedBits = getReservedBitsCount(data.mode, version)\n    totalBits += reservedBits + data.getBitsLength()\n  })\n\n  return totalBits\n}\n\nfunction getBestVersionForMixedData (segments, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    const length = getTotalBitsFromDataArray(segments, currentVersion)\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, Mode.MIXED)) {\n      return currentVersion\n    }\n  }\n\n  return undefined\n}\n\n/**\n * Returns version number from a value.\n * If value is not a valid version, returns defaultValue\n *\n * @param  {Number|String} value        QR Code version\n * @param  {Number}        defaultValue Fallback value\n * @return {Number}                     QR Code version number\n */\nexports.from = function from (value, defaultValue) {\n  if (VersionCheck.isValid(value)) {\n    return parseInt(value, 10)\n  }\n\n  return defaultValue\n}\n\n/**\n * Returns how much data can be stored with the specified QR code version\n * and error correction level\n *\n * @param  {Number} version              QR Code version (1-40)\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Mode}   mode                 Data mode\n * @return {Number}                      Quantity of storable data\n */\nexports.getCapacity = function getCapacity (version, errorCorrectionLevel, mode) {\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid QR Code version')\n  }\n\n  // Use Byte mode as default\n  if (typeof mode === 'undefined') mode = Mode.BYTE\n\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n\n  // Total number of data codewords\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8\n\n  if (mode === Mode.MIXED) return dataTotalCodewordsBits\n\n  const usableBits = dataTotalCodewordsBits - getReservedBitsCount(mode, version)\n\n  // Return max number of storable codewords\n  switch (mode) {\n    case Mode.NUMERIC:\n      return Math.floor((usableBits / 10) * 3)\n\n    case Mode.ALPHANUMERIC:\n      return Math.floor((usableBits / 11) * 2)\n\n    case Mode.KANJI:\n      return Math.floor(usableBits / 13)\n\n    case Mode.BYTE:\n    default:\n      return Math.floor(usableBits / 8)\n  }\n}\n\n/**\n * Returns the minimum version needed to contain the amount of data\n *\n * @param  {Segment} data                    Segment of data\n * @param  {Number} [errorCorrectionLevel=H] Error correction level\n * @param  {Mode} mode                       Data mode\n * @return {Number}                          QR Code version\n */\nexports.getBestVersionForData = function getBestVersionForData (data, errorCorrectionLevel) {\n  let seg\n\n  const ecl = ECLevel.from(errorCorrectionLevel, ECLevel.M)\n\n  if (Array.isArray(data)) {\n    if (data.length > 1) {\n      return getBestVersionForMixedData(data, ecl)\n    }\n\n    if (data.length === 0) {\n      return 1\n    }\n\n    seg = data[0]\n  } else {\n    seg = data\n  }\n\n  return getBestVersionForDataLength(seg.mode, seg.getLength(), ecl)\n}\n\n/**\n * Returns version information with relative error correction bits\n *\n * The version information is included in QR Code symbols of version 7 or larger.\n * It consists of an 18-bit sequence containing 6 data bits,\n * with 12 error correction bits calculated using the (18, 6) Golay code.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Encoded version info bits\n */\nexports.getEncodedBits = function getEncodedBits (version) {\n  if (!VersionCheck.isValid(version) || version < 7) {\n    throw new Error('Invalid QR Code version')\n  }\n\n  let d = version << 12\n\n  while (Utils.getBCHDigit(d) - G18_BCH >= 0) {\n    d ^= (G18 << (Utils.getBCHDigit(d) - G18_BCH))\n  }\n\n  return (version << 12) | d\n}\n", "const Utils = require('./utils')\n\nconst G15 = (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0)\nconst G15_MASK = (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1)\nconst G15_BCH = Utils.getBCHDigit(G15)\n\n/**\n * Returns format information with relative error correction bits\n *\n * The format information is a 15-bit sequence containing 5 data bits,\n * with 10 error correction bits calculated using the (15, 5) BCH code.\n *\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Number} mask                 Mask pattern\n * @return {Number}                      Encoded format information bits\n */\nexports.getEncodedBits = function getEncodedBits (errorCorrectionLevel, mask) {\n  const data = ((errorCorrectionLevel.bit << 3) | mask)\n  let d = data << 10\n\n  while (Utils.getBCHDigit(d) - G15_BCH >= 0) {\n    d ^= (G15 << (Utils.getBCHDigit(d) - G15_BCH))\n  }\n\n  // xor final data with mask pattern in order to ensure that\n  // no combination of Error Correction Level and data mask pattern\n  // will result in an all-zero data string\n  return ((data << 10) | d) ^ G15_MASK\n}\n", "const Mode = require('./mode')\n\nfunction NumericData (data) {\n  this.mode = Mode.NUMERIC\n  this.data = data.toString()\n}\n\nNumericData.getBitsLength = function getBitsLength (length) {\n  return 10 * Math.floor(length / 3) + ((length % 3) ? ((length % 3) * 3 + 1) : 0)\n}\n\nNumericData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nNumericData.prototype.getBitsLength = function getBitsLength () {\n  return NumericData.getBitsLength(this.data.length)\n}\n\nNumericData.prototype.write = function write (bitBuffer) {\n  let i, group, value\n\n  // The input data string is divided into groups of three digits,\n  // and each group is converted to its 10-bit binary equivalent.\n  for (i = 0; i + 3 <= this.data.length; i += 3) {\n    group = this.data.substr(i, 3)\n    value = parseInt(group, 10)\n\n    bitBuffer.put(value, 10)\n  }\n\n  // If the number of input digits is not an exact multiple of three,\n  // the final one or two digits are converted to 4 or 7 bits respectively.\n  const remainingNum = this.data.length - i\n  if (remainingNum > 0) {\n    group = this.data.substr(i)\n    value = parseInt(group, 10)\n\n    bitBuffer.put(value, remainingNum * 3 + 1)\n  }\n}\n\nmodule.exports = NumericData\n", "const Mode = require('./mode')\n\n/**\n * Array of characters available in alphanumeric mode\n *\n * As per QR Code specification, to each character\n * is assigned a value from 0 to 44 which in this case coincides\n * with the array index\n *\n * @type {Array}\n */\nconst ALPHA_NUM_CHARS = [\n  '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',\n  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',\n  'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',\n  ' ', '$', '%', '*', '+', '-', '.', '/', ':'\n]\n\nfunction AlphanumericData (data) {\n  this.mode = Mode.ALPHANUMERIC\n  this.data = data\n}\n\nAlphanumericData.getBitsLength = function getBitsLength (length) {\n  return 11 * Math.floor(length / 2) + 6 * (length % 2)\n}\n\nAlphanumericData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nAlphanumericData.prototype.getBitsLength = function getBitsLength () {\n  return AlphanumericData.getBitsLength(this.data.length)\n}\n\nAlphanumericData.prototype.write = function write (bitBuffer) {\n  let i\n\n  // Input data characters are divided into groups of two characters\n  // and encoded as 11-bit binary codes.\n  for (i = 0; i + 2 <= this.data.length; i += 2) {\n    // The character value of the first character is multiplied by 45\n    let value = ALPHA_NUM_CHARS.indexOf(this.data[i]) * 45\n\n    // The character value of the second digit is added to the product\n    value += ALPHA_NUM_CHARS.indexOf(this.data[i + 1])\n\n    // The sum is then stored as 11-bit binary number\n    bitBuffer.put(value, 11)\n  }\n\n  // If the number of input data characters is not a multiple of two,\n  // the character value of the final character is encoded as a 6-bit binary number.\n  if (this.data.length % 2) {\n    bitBuffer.put(ALPHA_NUM_CHARS.indexOf(this.data[i]), 6)\n  }\n}\n\nmodule.exports = AlphanumericData\n", "const Mode = require('./mode')\n\nfunction ByteData (data) {\n  this.mode = Mode.BYTE\n  if (typeof (data) === 'string') {\n    this.data = new TextEncoder().encode(data)\n  } else {\n    this.data = new Uint8Array(data)\n  }\n}\n\nByteData.getBitsLength = function getBitsLength (length) {\n  return length * 8\n}\n\nByteData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nByteData.prototype.getBitsLength = function getBitsLength () {\n  return ByteData.getBitsLength(this.data.length)\n}\n\nByteData.prototype.write = function (bitBuffer) {\n  for (let i = 0, l = this.data.length; i < l; i++) {\n    bitBuffer.put(this.data[i], 8)\n  }\n}\n\nmodule.exports = ByteData\n", "const Mode = require('./mode')\nconst Utils = require('./utils')\n\nfunction KanjiData (data) {\n  this.mode = Mode.KANJI\n  this.data = data\n}\n\nKanjiData.getBitsLength = function getBitsLength (length) {\n  return length * 13\n}\n\nKanjiData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nKanjiData.prototype.getBitsLength = function getBitsLength () {\n  return KanjiData.getBitsLength(this.data.length)\n}\n\nKanjiData.prototype.write = function (bitBuffer) {\n  let i\n\n  // In the Shift JIS system, Kanji characters are represented by a two byte combination.\n  // These byte values are shifted from the JIS X 0208 values.\n  // JIS X 0208 gives details of the shift coded representation.\n  for (i = 0; i < this.data.length; i++) {\n    let value = Utils.toSJIS(this.data[i])\n\n    // For characters with Shift JIS values from 0x8140 to 0x9FFC:\n    if (value >= 0x8140 && value <= 0x9FFC) {\n      // Subtract 0x8140 from Shift JIS value\n      value -= 0x8140\n\n    // For characters with Shift JIS values from 0xE040 to 0xEBBF\n    } else if (value >= 0xE040 && value <= 0xEBBF) {\n      // Subtract 0xC140 from Shift JIS value\n      value -= 0xC140\n    } else {\n      throw new Error(\n        'Invalid SJIS character: ' + this.data[i] + '\\n' +\n        'Make sure your charset is UTF-8')\n    }\n\n    // Multiply most significant byte of result by 0xC0\n    // and add least significant byte to product\n    value = (((value >>> 8) & 0xff) * 0xC0) + (value & 0xff)\n\n    // Convert result to a 13-bit binary string\n    bitBuffer.put(value, 13)\n  }\n}\n\nmodule.exports = KanjiData\n", "const Mode = require('./mode')\nconst NumericData = require('./numeric-data')\nconst AlphanumericData = require('./alphanumeric-data')\nconst ByteData = require('./byte-data')\nconst KanjiData = require('./kanji-data')\nconst Regex = require('./regex')\nconst Utils = require('./utils')\nconst dijkstra = require('dijkstrajs')\n\n/**\n * Returns UTF8 byte length\n *\n * @param  {String} str Input string\n * @return {Number}     Number of byte\n */\nfunction getStringByteLength (str) {\n  return unescape(encodeURIComponent(str)).length\n}\n\n/**\n * Get a list of segments of the specified mode\n * from a string\n *\n * @param  {Mode}   mode Segment mode\n * @param  {String} str  String to process\n * @return {Array}       Array of object with segments data\n */\nfunction getSegments (regex, mode, str) {\n  const segments = []\n  let result\n\n  while ((result = regex.exec(str)) !== null) {\n    segments.push({\n      data: result[0],\n      index: result.index,\n      mode: mode,\n      length: result[0].length\n    })\n  }\n\n  return segments\n}\n\n/**\n * Extracts a series of segments with the appropriate\n * modes from a string\n *\n * @param  {String} dataStr Input string\n * @return {Array}          Array of object with segments data\n */\nfunction getSegmentsFromString (dataStr) {\n  const numSegs = getSegments(Regex.NUMERIC, Mode.NUMERIC, dataStr)\n  const alphaNumSegs = getSegments(Regex.ALPHANUMERIC, Mode.ALPHANUMERIC, dataStr)\n  let byteSegs\n  let kanjiSegs\n\n  if (Utils.isKanjiModeEnabled()) {\n    byteSegs = getSegments(Regex.BYTE, Mode.BYTE, dataStr)\n    kanjiSegs = getSegments(Regex.KANJI, Mode.KANJI, dataStr)\n  } else {\n    byteSegs = getSegments(Regex.BYTE_KANJI, Mode.BYTE, dataStr)\n    kanjiSegs = []\n  }\n\n  const segs = numSegs.concat(alphaNumSegs, byteSegs, kanjiSegs)\n\n  return segs\n    .sort(function (s1, s2) {\n      return s1.index - s2.index\n    })\n    .map(function (obj) {\n      return {\n        data: obj.data,\n        mode: obj.mode,\n        length: obj.length\n      }\n    })\n}\n\n/**\n * Returns how many bits are needed to encode a string of\n * specified length with the specified mode\n *\n * @param  {Number} length String length\n * @param  {Mode} mode     Segment mode\n * @return {Number}        Bit length\n */\nfunction getSegmentBitsLength (length, mode) {\n  switch (mode) {\n    case Mode.NUMERIC:\n      return NumericData.getBitsLength(length)\n    case Mode.ALPHANUMERIC:\n      return AlphanumericData.getBitsLength(length)\n    case Mode.KANJI:\n      return KanjiData.getBitsLength(length)\n    case Mode.BYTE:\n      return ByteData.getBitsLength(length)\n  }\n}\n\n/**\n * Merges adjacent segments which have the same mode\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction mergeSegments (segs) {\n  return segs.reduce(function (acc, curr) {\n    const prevSeg = acc.length - 1 >= 0 ? acc[acc.length - 1] : null\n    if (prevSeg && prevSeg.mode === curr.mode) {\n      acc[acc.length - 1].data += curr.data\n      return acc\n    }\n\n    acc.push(curr)\n    return acc\n  }, [])\n}\n\n/**\n * Generates a list of all possible nodes combination which\n * will be used to build a segments graph.\n *\n * Nodes are divided by groups. Each group will contain a list of all the modes\n * in which is possible to encode the given text.\n *\n * For example the text '12345' can be encoded as Numeric, Alphanumeric or Byte.\n * The group for '12345' will contain then 3 objects, one for each\n * possible encoding mode.\n *\n * Each node represents a possible segment.\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction buildNodes (segs) {\n  const nodes = []\n  for (let i = 0; i < segs.length; i++) {\n    const seg = segs[i]\n\n    switch (seg.mode) {\n      case Mode.NUMERIC:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.ALPHANUMERIC, length: seg.length },\n          { data: seg.data, mode: Mode.BYTE, length: seg.length }\n        ])\n        break\n      case Mode.ALPHANUMERIC:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.BYTE, length: seg.length }\n        ])\n        break\n      case Mode.KANJI:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }\n        ])\n        break\n      case Mode.BYTE:\n        nodes.push([\n          { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }\n        ])\n    }\n  }\n\n  return nodes\n}\n\n/**\n * Builds a graph from a list of nodes.\n * All segments in each node group will be connected with all the segments of\n * the next group and so on.\n *\n * At each connection will be assigned a weight depending on the\n * segment's byte length.\n *\n * @param  {Array} nodes    Array of object with segments data\n * @param  {Number} version QR Code version\n * @return {Object}         Graph of all possible segments\n */\nfunction buildGraph (nodes, version) {\n  const table = {}\n  const graph = { start: {} }\n  let prevNodeIds = ['start']\n\n  for (let i = 0; i < nodes.length; i++) {\n    const nodeGroup = nodes[i]\n    const currentNodeIds = []\n\n    for (let j = 0; j < nodeGroup.length; j++) {\n      const node = nodeGroup[j]\n      const key = '' + i + j\n\n      currentNodeIds.push(key)\n      table[key] = { node: node, lastCount: 0 }\n      graph[key] = {}\n\n      for (let n = 0; n < prevNodeIds.length; n++) {\n        const prevNodeId = prevNodeIds[n]\n\n        if (table[prevNodeId] && table[prevNodeId].node.mode === node.mode) {\n          graph[prevNodeId][key] =\n            getSegmentBitsLength(table[prevNodeId].lastCount + node.length, node.mode) -\n            getSegmentBitsLength(table[prevNodeId].lastCount, node.mode)\n\n          table[prevNodeId].lastCount += node.length\n        } else {\n          if (table[prevNodeId]) table[prevNodeId].lastCount = node.length\n\n          graph[prevNodeId][key] = getSegmentBitsLength(node.length, node.mode) +\n            4 + Mode.getCharCountIndicator(node.mode, version) // switch cost\n        }\n      }\n    }\n\n    prevNodeIds = currentNodeIds\n  }\n\n  for (let n = 0; n < prevNodeIds.length; n++) {\n    graph[prevNodeIds[n]].end = 0\n  }\n\n  return { map: graph, table: table }\n}\n\n/**\n * Builds a segment from a specified data and mode.\n * If a mode is not specified, the more suitable will be used.\n *\n * @param  {String} data             Input data\n * @param  {Mode | String} modesHint Data mode\n * @return {Segment}                 Segment\n */\nfunction buildSingleSegment (data, modesHint) {\n  let mode\n  const bestMode = Mode.getBestModeForData(data)\n\n  mode = Mode.from(modesHint, bestMode)\n\n  // Make sure data can be encoded\n  if (mode !== Mode.BYTE && mode.bit < bestMode.bit) {\n    throw new Error('\"' + data + '\"' +\n      ' cannot be encoded with mode ' + Mode.toString(mode) +\n      '.\\n Suggested mode is: ' + Mode.toString(bestMode))\n  }\n\n  // Use Mode.BYTE if Kanji support is disabled\n  if (mode === Mode.KANJI && !Utils.isKanjiModeEnabled()) {\n    mode = Mode.BYTE\n  }\n\n  switch (mode) {\n    case Mode.NUMERIC:\n      return new NumericData(data)\n\n    case Mode.ALPHANUMERIC:\n      return new AlphanumericData(data)\n\n    case Mode.KANJI:\n      return new KanjiData(data)\n\n    case Mode.BYTE:\n      return new ByteData(data)\n  }\n}\n\n/**\n * Builds a list of segments from an array.\n * Array can contain Strings or Objects with segment's info.\n *\n * For each item which is a string, will be generated a segment with the given\n * string and the more appropriate encoding mode.\n *\n * For each item which is an object, will be generated a segment with the given\n * data and mode.\n * Objects must contain at least the property \"data\".\n * If property \"mode\" is not present, the more suitable mode will be used.\n *\n * @param  {Array} array Array of objects with segments data\n * @return {Array}       Array of Segments\n */\nexports.fromArray = function fromArray (array) {\n  return array.reduce(function (acc, seg) {\n    if (typeof seg === 'string') {\n      acc.push(buildSingleSegment(seg, null))\n    } else if (seg.data) {\n      acc.push(buildSingleSegment(seg.data, seg.mode))\n    }\n\n    return acc\n  }, [])\n}\n\n/**\n * Builds an optimized sequence of segments from a string,\n * which will produce the shortest possible bitstream.\n *\n * @param  {String} data    Input string\n * @param  {Number} version QR Code version\n * @return {Array}          Array of segments\n */\nexports.fromString = function fromString (data, version) {\n  const segs = getSegmentsFromString(data, Utils.isKanjiModeEnabled())\n\n  const nodes = buildNodes(segs)\n  const graph = buildGraph(nodes, version)\n  const path = dijkstra.find_path(graph.map, 'start', 'end')\n\n  const optimizedSegs = []\n  for (let i = 1; i < path.length - 1; i++) {\n    optimizedSegs.push(graph.table[path[i]].node)\n  }\n\n  return exports.fromArray(mergeSegments(optimizedSegs))\n}\n\n/**\n * Splits a string in various segments with the modes which\n * best represent their content.\n * The produced segments are far from being optimized.\n * The output of this function is only used to estimate a QR Code version\n * which may contain the data.\n *\n * @param  {string} data Input string\n * @return {Array}       Array of segments\n */\nexports.rawSplit = function rawSplit (data) {\n  return exports.fromArray(\n    getSegmentsFromString(data, Utils.isKanjiModeEnabled())\n  )\n}\n", "const Utils = require('./utils')\nconst ECLevel = require('./error-correction-level')\nconst BitBuffer = require('./bit-buffer')\nconst BitMatrix = require('./bit-matrix')\nconst AlignmentPattern = require('./alignment-pattern')\nconst FinderPattern = require('./finder-pattern')\nconst MaskPattern = require('./mask-pattern')\nconst ECCode = require('./error-correction-code')\nconst ReedSolomonEncoder = require('./reed-solomon-encoder')\nconst Version = require('./version')\nconst FormatInfo = require('./format-info')\nconst Mode = require('./mode')\nconst Segments = require('./segments')\n\n/**\n * QRCode for JavaScript\n *\n * modified by <PERSON> for nodejs support\n * Copyright (c) 2011 Ryan Day\n *\n * Licensed under the MIT license:\n *   http://www.opensource.org/licenses/mit-license.php\n *\n//---------------------------------------------------------------------\n// QRCode for JavaScript\n//\n// Copyright (c) 2009 <PERSON><PERSON><PERSON>\n//\n// URL: http://www.d-project.com/\n//\n// Licensed under the MIT license:\n//   http://www.opensource.org/licenses/mit-license.php\n//\n// The word \"QR Code\" is registered trademark of\n// DENSO WAVE INCORPORATED\n//   http://www.denso-wave.com/qrcode/faqpatent-e.html\n//\n//---------------------------------------------------------------------\n*/\n\n/**\n * Add finder patterns bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupFinderPattern (matrix, version) {\n  const size = matrix.size\n  const pos = FinderPattern.getPositions(version)\n\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0]\n    const col = pos[i][1]\n\n    for (let r = -1; r <= 7; r++) {\n      if (row + r <= -1 || size <= row + r) continue\n\n      for (let c = -1; c <= 7; c++) {\n        if (col + c <= -1 || size <= col + c) continue\n\n        if ((r >= 0 && r <= 6 && (c === 0 || c === 6)) ||\n          (c >= 0 && c <= 6 && (r === 0 || r === 6)) ||\n          (r >= 2 && r <= 4 && c >= 2 && c <= 4)) {\n          matrix.set(row + r, col + c, true, true)\n        } else {\n          matrix.set(row + r, col + c, false, true)\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add timing pattern bits to matrix\n *\n * Note: this function must be called before {@link setupAlignmentPattern}\n *\n * @param  {BitMatrix} matrix Modules matrix\n */\nfunction setupTimingPattern (matrix) {\n  const size = matrix.size\n\n  for (let r = 8; r < size - 8; r++) {\n    const value = r % 2 === 0\n    matrix.set(r, 6, value, true)\n    matrix.set(6, r, value, true)\n  }\n}\n\n/**\n * Add alignment patterns bits to matrix\n *\n * Note: this function must be called after {@link setupTimingPattern}\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupAlignmentPattern (matrix, version) {\n  const pos = AlignmentPattern.getPositions(version)\n\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0]\n    const col = pos[i][1]\n\n    for (let r = -2; r <= 2; r++) {\n      for (let c = -2; c <= 2; c++) {\n        if (r === -2 || r === 2 || c === -2 || c === 2 ||\n          (r === 0 && c === 0)) {\n          matrix.set(row + r, col + c, true, true)\n        } else {\n          matrix.set(row + r, col + c, false, true)\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add version info bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupVersionInfo (matrix, version) {\n  const size = matrix.size\n  const bits = Version.getEncodedBits(version)\n  let row, col, mod\n\n  for (let i = 0; i < 18; i++) {\n    row = Math.floor(i / 3)\n    col = i % 3 + size - 8 - 3\n    mod = ((bits >> i) & 1) === 1\n\n    matrix.set(row, col, mod, true)\n    matrix.set(col, row, mod, true)\n  }\n}\n\n/**\n * Add format info bits to matrix\n *\n * @param  {BitMatrix} matrix               Modules matrix\n * @param  {ErrorCorrectionLevel}    errorCorrectionLevel Error correction level\n * @param  {Number}    maskPattern          Mask pattern reference value\n */\nfunction setupFormatInfo (matrix, errorCorrectionLevel, maskPattern) {\n  const size = matrix.size\n  const bits = FormatInfo.getEncodedBits(errorCorrectionLevel, maskPattern)\n  let i, mod\n\n  for (i = 0; i < 15; i++) {\n    mod = ((bits >> i) & 1) === 1\n\n    // vertical\n    if (i < 6) {\n      matrix.set(i, 8, mod, true)\n    } else if (i < 8) {\n      matrix.set(i + 1, 8, mod, true)\n    } else {\n      matrix.set(size - 15 + i, 8, mod, true)\n    }\n\n    // horizontal\n    if (i < 8) {\n      matrix.set(8, size - i - 1, mod, true)\n    } else if (i < 9) {\n      matrix.set(8, 15 - i - 1 + 1, mod, true)\n    } else {\n      matrix.set(8, 15 - i - 1, mod, true)\n    }\n  }\n\n  // fixed module\n  matrix.set(size - 8, 8, 1, true)\n}\n\n/**\n * Add encoded data bits to matrix\n *\n * @param  {BitMatrix}  matrix Modules matrix\n * @param  {Uint8Array} data   Data codewords\n */\nfunction setupData (matrix, data) {\n  const size = matrix.size\n  let inc = -1\n  let row = size - 1\n  let bitIndex = 7\n  let byteIndex = 0\n\n  for (let col = size - 1; col > 0; col -= 2) {\n    if (col === 6) col--\n\n    while (true) {\n      for (let c = 0; c < 2; c++) {\n        if (!matrix.isReserved(row, col - c)) {\n          let dark = false\n\n          if (byteIndex < data.length) {\n            dark = (((data[byteIndex] >>> bitIndex) & 1) === 1)\n          }\n\n          matrix.set(row, col - c, dark)\n          bitIndex--\n\n          if (bitIndex === -1) {\n            byteIndex++\n            bitIndex = 7\n          }\n        }\n      }\n\n      row += inc\n\n      if (row < 0 || size <= row) {\n        row -= inc\n        inc = -inc\n        break\n      }\n    }\n  }\n}\n\n/**\n * Create encoded codewords from data input\n *\n * @param  {Number}   version              QR Code version\n * @param  {ErrorCorrectionLevel}   errorCorrectionLevel Error correction level\n * @param  {ByteData} data                 Data input\n * @return {Uint8Array}                    Buffer containing encoded codewords\n */\nfunction createData (version, errorCorrectionLevel, segments) {\n  // Prepare data buffer\n  const buffer = new BitBuffer()\n\n  segments.forEach(function (data) {\n    // prefix data with mode indicator (4 bits)\n    buffer.put(data.mode.bit, 4)\n\n    // Prefix data with character count indicator.\n    // The character count indicator is a string of bits that represents the\n    // number of characters that are being encoded.\n    // The character count indicator must be placed after the mode indicator\n    // and must be a certain number of bits long, depending on the QR version\n    // and data mode\n    // @see {@link Mode.getCharCountIndicator}.\n    buffer.put(data.getLength(), Mode.getCharCountIndicator(data.mode, version))\n\n    // add binary data sequence to buffer\n    data.write(buffer)\n  })\n\n  // Calculate required number of bits\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8\n\n  // Add a terminator.\n  // If the bit string is shorter than the total number of required bits,\n  // a terminator of up to four 0s must be added to the right side of the string.\n  // If the bit string is more than four bits shorter than the required number of bits,\n  // add four 0s to the end.\n  if (buffer.getLengthInBits() + 4 <= dataTotalCodewordsBits) {\n    buffer.put(0, 4)\n  }\n\n  // If the bit string is fewer than four bits shorter, add only the number of 0s that\n  // are needed to reach the required number of bits.\n\n  // After adding the terminator, if the number of bits in the string is not a multiple of 8,\n  // pad the string on the right with 0s to make the string's length a multiple of 8.\n  while (buffer.getLengthInBits() % 8 !== 0) {\n    buffer.putBit(0)\n  }\n\n  // Add pad bytes if the string is still shorter than the total number of required bits.\n  // Extend the buffer to fill the data capacity of the symbol corresponding to\n  // the Version and Error Correction Level by adding the Pad Codewords 11101100 (0xEC)\n  // and 00010001 (0x11) alternately.\n  const remainingByte = (dataTotalCodewordsBits - buffer.getLengthInBits()) / 8\n  for (let i = 0; i < remainingByte; i++) {\n    buffer.put(i % 2 ? 0x11 : 0xEC, 8)\n  }\n\n  return createCodewords(buffer, version, errorCorrectionLevel)\n}\n\n/**\n * Encode input data with Reed-Solomon and return codewords with\n * relative error correction bits\n *\n * @param  {BitBuffer} bitBuffer            Data to encode\n * @param  {Number}    version              QR Code version\n * @param  {ErrorCorrectionLevel} errorCorrectionLevel Error correction level\n * @return {Uint8Array}                     Buffer containing encoded codewords\n */\nfunction createCodewords (bitBuffer, version, errorCorrectionLevel) {\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n\n  // Total number of data codewords\n  const dataTotalCodewords = totalCodewords - ecTotalCodewords\n\n  // Total number of blocks\n  const ecTotalBlocks = ECCode.getBlocksCount(version, errorCorrectionLevel)\n\n  // Calculate how many blocks each group should contain\n  const blocksInGroup2 = totalCodewords % ecTotalBlocks\n  const blocksInGroup1 = ecTotalBlocks - blocksInGroup2\n\n  const totalCodewordsInGroup1 = Math.floor(totalCodewords / ecTotalBlocks)\n\n  const dataCodewordsInGroup1 = Math.floor(dataTotalCodewords / ecTotalBlocks)\n  const dataCodewordsInGroup2 = dataCodewordsInGroup1 + 1\n\n  // Number of EC codewords is the same for both groups\n  const ecCount = totalCodewordsInGroup1 - dataCodewordsInGroup1\n\n  // Initialize a Reed-Solomon encoder with a generator polynomial of degree ecCount\n  const rs = new ReedSolomonEncoder(ecCount)\n\n  let offset = 0\n  const dcData = new Array(ecTotalBlocks)\n  const ecData = new Array(ecTotalBlocks)\n  let maxDataSize = 0\n  const buffer = new Uint8Array(bitBuffer.buffer)\n\n  // Divide the buffer into the required number of blocks\n  for (let b = 0; b < ecTotalBlocks; b++) {\n    const dataSize = b < blocksInGroup1 ? dataCodewordsInGroup1 : dataCodewordsInGroup2\n\n    // extract a block of data from buffer\n    dcData[b] = buffer.slice(offset, offset + dataSize)\n\n    // Calculate EC codewords for this data block\n    ecData[b] = rs.encode(dcData[b])\n\n    offset += dataSize\n    maxDataSize = Math.max(maxDataSize, dataSize)\n  }\n\n  // Create final data\n  // Interleave the data and error correction codewords from each block\n  const data = new Uint8Array(totalCodewords)\n  let index = 0\n  let i, r\n\n  // Add data codewords\n  for (i = 0; i < maxDataSize; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      if (i < dcData[r].length) {\n        data[index++] = dcData[r][i]\n      }\n    }\n  }\n\n  // Apped EC codewords\n  for (i = 0; i < ecCount; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      data[index++] = ecData[r][i]\n    }\n  }\n\n  return data\n}\n\n/**\n * Build QR Code symbol\n *\n * @param  {String} data                 Input string\n * @param  {Number} version              QR Code version\n * @param  {ErrorCorretionLevel} errorCorrectionLevel Error level\n * @param  {MaskPattern} maskPattern     Mask pattern\n * @return {Object}                      Object containing symbol data\n */\nfunction createSymbol (data, version, errorCorrectionLevel, maskPattern) {\n  let segments\n\n  if (Array.isArray(data)) {\n    segments = Segments.fromArray(data)\n  } else if (typeof data === 'string') {\n    let estimatedVersion = version\n\n    if (!estimatedVersion) {\n      const rawSegments = Segments.rawSplit(data)\n\n      // Estimate best version that can contain raw splitted segments\n      estimatedVersion = Version.getBestVersionForData(rawSegments, errorCorrectionLevel)\n    }\n\n    // Build optimized segments\n    // If estimated version is undefined, try with the highest version\n    segments = Segments.fromString(data, estimatedVersion || 40)\n  } else {\n    throw new Error('Invalid data')\n  }\n\n  // Get the min version that can contain data\n  const bestVersion = Version.getBestVersionForData(segments, errorCorrectionLevel)\n\n  // If no version is found, data cannot be stored\n  if (!bestVersion) {\n    throw new Error('The amount of data is too big to be stored in a QR Code')\n  }\n\n  // If not specified, use min version as default\n  if (!version) {\n    version = bestVersion\n\n  // Check if the specified version can contain the data\n  } else if (version < bestVersion) {\n    throw new Error('\\n' +\n      'The chosen QR Code version cannot contain this amount of data.\\n' +\n      'Minimum version required to store current data is: ' + bestVersion + '.\\n'\n    )\n  }\n\n  const dataBits = createData(version, errorCorrectionLevel, segments)\n\n  // Allocate matrix buffer\n  const moduleCount = Utils.getSymbolSize(version)\n  const modules = new BitMatrix(moduleCount)\n\n  // Add function modules\n  setupFinderPattern(modules, version)\n  setupTimingPattern(modules)\n  setupAlignmentPattern(modules, version)\n\n  // Add temporary dummy bits for format info just to set them as reserved.\n  // This is needed to prevent these bits from being masked by {@link MaskPattern.applyMask}\n  // since the masking operation must be performed only on the encoding region.\n  // These blocks will be replaced with correct values later in code.\n  setupFormatInfo(modules, errorCorrectionLevel, 0)\n\n  if (version >= 7) {\n    setupVersionInfo(modules, version)\n  }\n\n  // Add data codewords\n  setupData(modules, dataBits)\n\n  if (isNaN(maskPattern)) {\n    // Find best mask pattern\n    maskPattern = MaskPattern.getBestMask(modules,\n      setupFormatInfo.bind(null, modules, errorCorrectionLevel))\n  }\n\n  // Apply mask pattern\n  MaskPattern.applyMask(maskPattern, modules)\n\n  // Replace format info bits with correct values\n  setupFormatInfo(modules, errorCorrectionLevel, maskPattern)\n\n  return {\n    modules: modules,\n    version: version,\n    errorCorrectionLevel: errorCorrectionLevel,\n    maskPattern: maskPattern,\n    segments: segments\n  }\n}\n\n/**\n * QR Code\n *\n * @param {String | Array} data                 Input data\n * @param {Object} options                      Optional configurations\n * @param {Number} options.version              QR Code version\n * @param {String} options.errorCorrectionLevel Error correction level\n * @param {Function} options.toSJISFunc         Helper func to convert utf8 to sjis\n */\nexports.create = function create (data, options) {\n  if (typeof data === 'undefined' || data === '') {\n    throw new Error('No input text')\n  }\n\n  let errorCorrectionLevel = ECLevel.M\n  let version\n  let mask\n\n  if (typeof options !== 'undefined') {\n    // Use higher error correction level as default\n    errorCorrectionLevel = ECLevel.from(options.errorCorrectionLevel, ECLevel.M)\n    version = Version.from(options.version)\n    mask = MaskPattern.from(options.maskPattern)\n\n    if (options.toSJISFunc) {\n      Utils.setToSJISFunction(options.toSJISFunc)\n    }\n  }\n\n  return createSymbol(data, version, errorCorrectionLevel, mask)\n}\n", "function hex2rgba (hex) {\n  if (typeof hex === 'number') {\n    hex = hex.toString()\n  }\n\n  if (typeof hex !== 'string') {\n    throw new Error('Color should be defined as hex string')\n  }\n\n  let hexCode = hex.slice().replace('#', '').split('')\n  if (hexCode.length < 3 || hexCode.length === 5 || hexCode.length > 8) {\n    throw new Error('Invalid hex color: ' + hex)\n  }\n\n  // Convert from short to long form (fff -> ffffff)\n  if (hexCode.length === 3 || hexCode.length === 4) {\n    hexCode = Array.prototype.concat.apply([], hexCode.map(function (c) {\n      return [c, c]\n    }))\n  }\n\n  // Add default alpha value\n  if (hexCode.length === 6) hexCode.push('F', 'F')\n\n  const hexValue = parseInt(hexCode.join(''), 16)\n\n  return {\n    r: (hexValue >> 24) & 255,\n    g: (hexValue >> 16) & 255,\n    b: (hexValue >> 8) & 255,\n    a: hexValue & 255,\n    hex: '#' + hexCode.slice(0, 6).join('')\n  }\n}\n\nexports.getOptions = function getOptions (options) {\n  if (!options) options = {}\n  if (!options.color) options.color = {}\n\n  const margin = typeof options.margin === 'undefined' ||\n    options.margin === null ||\n    options.margin < 0\n    ? 4\n    : options.margin\n\n  const width = options.width && options.width >= 21 ? options.width : undefined\n  const scale = options.scale || 4\n\n  return {\n    width: width,\n    scale: width ? 4 : scale,\n    margin: margin,\n    color: {\n      dark: hex2rgba(options.color.dark || '#000000ff'),\n      light: hex2rgba(options.color.light || '#ffffffff')\n    },\n    type: options.type,\n    rendererOpts: options.rendererOpts || {}\n  }\n}\n\nexports.getScale = function getScale (qrSize, opts) {\n  return opts.width && opts.width >= qrSize + opts.margin * 2\n    ? opts.width / (qrSize + opts.margin * 2)\n    : opts.scale\n}\n\nexports.getImageWidth = function getImageWidth (qrSize, opts) {\n  const scale = exports.getScale(qrSize, opts)\n  return Math.floor((qrSize + opts.margin * 2) * scale)\n}\n\nexports.qrToImageData = function qrToImageData (imgData, qr, opts) {\n  const size = qr.modules.size\n  const data = qr.modules.data\n  const scale = exports.getScale(size, opts)\n  const symbolSize = Math.floor((size + opts.margin * 2) * scale)\n  const scaledMargin = opts.margin * scale\n  const palette = [opts.color.light, opts.color.dark]\n\n  for (let i = 0; i < symbolSize; i++) {\n    for (let j = 0; j < symbolSize; j++) {\n      let posDst = (i * symbolSize + j) * 4\n      let pxColor = opts.color.light\n\n      if (i >= scaledMargin && j >= scaledMargin &&\n        i < symbolSize - scaledMargin && j < symbolSize - scaledMargin) {\n        const iSrc = Math.floor((i - scaledMargin) / scale)\n        const jSrc = Math.floor((j - scaledMargin) / scale)\n        pxColor = palette[data[iSrc * size + jSrc] ? 1 : 0]\n      }\n\n      imgData[posDst++] = pxColor.r\n      imgData[posDst++] = pxColor.g\n      imgData[posDst++] = pxColor.b\n      imgData[posDst] = pxColor.a\n    }\n  }\n}\n", "const Utils = require('./utils')\n\nfunction clearCanvas (ctx, canvas, size) {\n  ctx.clearRect(0, 0, canvas.width, canvas.height)\n\n  if (!canvas.style) canvas.style = {}\n  canvas.height = size\n  canvas.width = size\n  canvas.style.height = size + 'px'\n  canvas.style.width = size + 'px'\n}\n\nfunction getCanvasElement () {\n  try {\n    return document.createElement('canvas')\n  } catch (e) {\n    throw new Error('You need to specify a canvas element')\n  }\n}\n\nexports.render = function render (qrData, canvas, options) {\n  let opts = options\n  let canvasEl = canvas\n\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas\n    canvas = undefined\n  }\n\n  if (!canvas) {\n    canvasEl = getCanvasElement()\n  }\n\n  opts = Utils.getOptions(opts)\n  const size = Utils.getImageWidth(qrData.modules.size, opts)\n\n  const ctx = canvasEl.getContext('2d')\n  const image = ctx.createImageData(size, size)\n  Utils.qrToImageData(image.data, qrData, opts)\n\n  clearCanvas(ctx, canvasEl, size)\n  ctx.putImageData(image, 0, 0)\n\n  return canvasEl\n}\n\nexports.renderToDataURL = function renderToDataURL (qrData, canvas, options) {\n  let opts = options\n\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas\n    canvas = undefined\n  }\n\n  if (!opts) opts = {}\n\n  const canvasEl = exports.render(qrData, canvas, opts)\n\n  const type = opts.type || 'image/png'\n  const rendererOpts = opts.rendererOpts || {}\n\n  return canvasEl.toDataURL(type, rendererOpts.quality)\n}\n", "const Utils = require('./utils')\n\nfunction getColorAttrib (color, attrib) {\n  const alpha = color.a / 255\n  const str = attrib + '=\"' + color.hex + '\"'\n\n  return alpha < 1\n    ? str + ' ' + attrib + '-opacity=\"' + alpha.toFixed(2).slice(1) + '\"'\n    : str\n}\n\nfunction svgCmd (cmd, x, y) {\n  let str = cmd + x\n  if (typeof y !== 'undefined') str += ' ' + y\n\n  return str\n}\n\nfunction qrToPath (data, size, margin) {\n  let path = ''\n  let moveBy = 0\n  let newRow = false\n  let lineLength = 0\n\n  for (let i = 0; i < data.length; i++) {\n    const col = Math.floor(i % size)\n    const row = Math.floor(i / size)\n\n    if (!col && !newRow) newRow = true\n\n    if (data[i]) {\n      lineLength++\n\n      if (!(i > 0 && col > 0 && data[i - 1])) {\n        path += newRow\n          ? svgCmd('M', col + margin, 0.5 + row + margin)\n          : svgCmd('m', moveBy, 0)\n\n        moveBy = 0\n        newRow = false\n      }\n\n      if (!(col + 1 < size && data[i + 1])) {\n        path += svgCmd('h', lineLength)\n        lineLength = 0\n      }\n    } else {\n      moveBy++\n    }\n  }\n\n  return path\n}\n\nexports.render = function render (qrData, options, cb) {\n  const opts = Utils.getOptions(options)\n  const size = qrData.modules.size\n  const data = qrData.modules.data\n  const qrcodesize = size + opts.margin * 2\n\n  const bg = !opts.color.light.a\n    ? ''\n    : '<path ' + getColorAttrib(opts.color.light, 'fill') +\n      ' d=\"M0 0h' + qrcodesize + 'v' + qrcodesize + 'H0z\"/>'\n\n  const path =\n    '<path ' + getColorAttrib(opts.color.dark, 'stroke') +\n    ' d=\"' + qrToPath(data, size, opts.margin) + '\"/>'\n\n  const viewBox = 'viewBox=\"' + '0 0 ' + qrcodesize + ' ' + qrcodesize + '\"'\n\n  const width = !opts.width ? '' : 'width=\"' + opts.width + '\" height=\"' + opts.width + '\" '\n\n  const svgTag = '<svg xmlns=\"http://www.w3.org/2000/svg\" ' + width + viewBox + ' shape-rendering=\"crispEdges\">' + bg + path + '</svg>\\n'\n\n  if (typeof cb === 'function') {\n    cb(null, svgTag)\n  }\n\n  return svgTag\n}\n", "\nconst canPromise = require('./can-promise')\n\nconst QRCode = require('./core/qrcode')\nconst CanvasRenderer = require('./renderer/canvas')\nconst SvgRenderer = require('./renderer/svg-tag.js')\n\nfunction renderCanvas (renderFunc, canvas, text, opts, cb) {\n  const args = [].slice.call(arguments, 1)\n  const argsNum = args.length\n  const isLastArgCb = typeof args[argsNum - 1] === 'function'\n\n  if (!isLastArgCb && !canPromise()) {\n    throw new Error('Callback required as last argument')\n  }\n\n  if (isLastArgCb) {\n    if (argsNum < 2) {\n      throw new Error('Too few arguments provided')\n    }\n\n    if (argsNum === 2) {\n      cb = text\n      text = canvas\n      canvas = opts = undefined\n    } else if (argsNum === 3) {\n      if (canvas.getContext && typeof cb === 'undefined') {\n        cb = opts\n        opts = undefined\n      } else {\n        cb = opts\n        opts = text\n        text = canvas\n        canvas = undefined\n      }\n    }\n  } else {\n    if (argsNum < 1) {\n      throw new Error('Too few arguments provided')\n    }\n\n    if (argsNum === 1) {\n      text = canvas\n      canvas = opts = undefined\n    } else if (argsNum === 2 && !canvas.getContext) {\n      opts = text\n      text = canvas\n      canvas = undefined\n    }\n\n    return new Promise(function (resolve, reject) {\n      try {\n        const data = QRCode.create(text, opts)\n        resolve(renderFunc(data, canvas, opts))\n      } catch (e) {\n        reject(e)\n      }\n    })\n  }\n\n  try {\n    const data = QRCode.create(text, opts)\n    cb(null, renderFunc(data, canvas, opts))\n  } catch (e) {\n    cb(e)\n  }\n}\n\nexports.create = QRCode.create\nexports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render)\nexports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL)\n\n// only svg for now.\nexports.toString = renderCanvas.bind(null, function (data, _, opts) {\n  return SvgRenderer.render(data, opts)\n})\n", "'use strict'\n// base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\nfunction base (ALPHABET) {\n  if (ALPHABET.length >= 255) { throw new TypeError('Alphabet too long') }\n  var BASE_MAP = new Uint8Array(256)\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i)\n    var xc = x.charCodeAt(0)\n    if (BASE_MAP[xc] !== 255) { throw new TypeError(x + ' is ambiguous') }\n    BASE_MAP[xc] = i\n  }\n  var BASE = ALPHABET.length\n  var LEADER = ALPHABET.charAt(0)\n  var FACTOR = Math.log(BASE) / Math.log(256) // log(BASE) / log(256), rounded up\n  var iFACTOR = Math.log(256) / Math.log(BASE) // log(256) / log(BASE), rounded up\n  function encode (source) {\n    if (source instanceof Uint8Array) {\n    } else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength)\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source)\n    }\n    if (!(source instanceof Uint8Array)) { throw new TypeError('Expected Uint8Array') }\n    if (source.length === 0) { return '' }\n        // Skip & count leading zeroes.\n    var zeroes = 0\n    var length = 0\n    var pbegin = 0\n    var pend = source.length\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++\n      zeroes++\n    }\n        // Allocate enough space in big-endian base58 representation.\n    var size = ((pend - pbegin) * iFACTOR + 1) >>> 0\n    var b58 = new Uint8Array(size)\n        // Process the bytes.\n    while (pbegin !== pend) {\n      var carry = source[pbegin]\n            // Apply \"b58 = b58 * 256 + ch\".\n      var i = 0\n      for (var it1 = size - 1; (carry !== 0 || i < length) && (it1 !== -1); it1--, i++) {\n        carry += (256 * b58[it1]) >>> 0\n        b58[it1] = (carry % BASE) >>> 0\n        carry = (carry / BASE) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      pbegin++\n    }\n        // Skip leading zeroes in base58 result.\n    var it2 = size - length\n    while (it2 !== size && b58[it2] === 0) {\n      it2++\n    }\n        // Translate the result into a string.\n    var str = LEADER.repeat(zeroes)\n    for (; it2 < size; ++it2) { str += ALPHABET.charAt(b58[it2]) }\n    return str\n  }\n  function decodeUnsafe (source) {\n    if (typeof source !== 'string') { throw new TypeError('Expected String') }\n    if (source.length === 0) { return new Uint8Array() }\n    var psz = 0\n        // Skip and count leading '1's.\n    var zeroes = 0\n    var length = 0\n    while (source[psz] === LEADER) {\n      zeroes++\n      psz++\n    }\n        // Allocate enough space in big-endian base256 representation.\n    var size = (((source.length - psz) * FACTOR) + 1) >>> 0 // log(58) / log(256), rounded up.\n    var b256 = new Uint8Array(size)\n        // Process the characters.\n    while (source[psz]) {\n            // Find code of next character\n      var charCode = source.charCodeAt(psz)\n            // Base map can not be indexed using char code\n      if (charCode > 255) { return }\n            // Decode character\n      var carry = BASE_MAP[charCode]\n            // Invalid character\n      if (carry === 255) { return }\n      var i = 0\n      for (var it3 = size - 1; (carry !== 0 || i < length) && (it3 !== -1); it3--, i++) {\n        carry += (BASE * b256[it3]) >>> 0\n        b256[it3] = (carry % 256) >>> 0\n        carry = (carry / 256) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      psz++\n    }\n        // Skip leading zeroes in b256.\n    var it4 = size - length\n    while (it4 !== size && b256[it4] === 0) {\n      it4++\n    }\n    var vch = new Uint8Array(zeroes + (size - it4))\n    var j = zeroes\n    while (it4 !== size) {\n      vch[j++] = b256[it4++]\n    }\n    return vch\n  }\n  function decode (string) {\n    var buffer = decodeUnsafe(string)\n    if (buffer) { return buffer }\n    throw new Error('Non-base' + BASE + ' character')\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  }\n}\nmodule.exports = base\n", "const basex = require('base-x')\nconst ALPHABET = '**********************************************************'\n\nmodule.exports = basex(ALPHABET)\n", "'use strict'\n// base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\nfunction base (ALPHABET) {\n  if (ALPHABET.length >= 255) { throw new TypeError('Alphabet too long') }\n  var BASE_MAP = new Uint8Array(256)\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i)\n    var xc = x.charCodeAt(0)\n    if (BASE_MAP[xc] !== 255) { throw new TypeError(x + ' is ambiguous') }\n    BASE_MAP[xc] = i\n  }\n  var BASE = ALPHABET.length\n  var LEADER = ALPHABET.charAt(0)\n  var FACTOR = Math.log(BASE) / Math.log(256) // log(BASE) / log(256), rounded up\n  var iFACTOR = Math.log(256) / Math.log(BASE) // log(256) / log(BASE), rounded up\n  function encode (source) {\n    if (source instanceof Uint8Array) {\n    } else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength)\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source)\n    }\n    if (!(source instanceof Uint8Array)) { throw new TypeError('Expected Uint8Array') }\n    if (source.length === 0) { return '' }\n        // Skip & count leading zeroes.\n    var zeroes = 0\n    var length = 0\n    var pbegin = 0\n    var pend = source.length\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++\n      zeroes++\n    }\n        // Allocate enough space in big-endian base58 representation.\n    var size = ((pend - pbegin) * iFACTOR + 1) >>> 0\n    var b58 = new Uint8Array(size)\n        // Process the bytes.\n    while (pbegin !== pend) {\n      var carry = source[pbegin]\n            // Apply \"b58 = b58 * 256 + ch\".\n      var i = 0\n      for (var it1 = size - 1; (carry !== 0 || i < length) && (it1 !== -1); it1--, i++) {\n        carry += (256 * b58[it1]) >>> 0\n        b58[it1] = (carry % BASE) >>> 0\n        carry = (carry / BASE) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      pbegin++\n    }\n        // Skip leading zeroes in base58 result.\n    var it2 = size - length\n    while (it2 !== size && b58[it2] === 0) {\n      it2++\n    }\n        // Translate the result into a string.\n    var str = LEADER.repeat(zeroes)\n    for (; it2 < size; ++it2) { str += ALPHABET.charAt(b58[it2]) }\n    return str\n  }\n  function decodeUnsafe (source) {\n    if (typeof source !== 'string') { throw new TypeError('Expected String') }\n    if (source.length === 0) { return new Uint8Array() }\n    var psz = 0\n        // Skip and count leading '1's.\n    var zeroes = 0\n    var length = 0\n    while (source[psz] === LEADER) {\n      zeroes++\n      psz++\n    }\n        // Allocate enough space in big-endian base256 representation.\n    var size = (((source.length - psz) * FACTOR) + 1) >>> 0 // log(58) / log(256), rounded up.\n    var b256 = new Uint8Array(size)\n        // Process the characters.\n    while (source[psz]) {\n            // Find code of next character\n      var charCode = source.charCodeAt(psz)\n            // Base map can not be indexed using char code\n      if (charCode > 255) { return }\n            // Decode character\n      var carry = BASE_MAP[charCode]\n            // Invalid character\n      if (carry === 255) { return }\n      var i = 0\n      for (var it3 = size - 1; (carry !== 0 || i < length) && (it3 !== -1); it3--, i++) {\n        carry += (BASE * b256[it3]) >>> 0\n        b256[it3] = (carry % 256) >>> 0\n        carry = (carry / 256) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      psz++\n    }\n        // Skip leading zeroes in b256.\n    var it4 = size - length\n    while (it4 !== size && b256[it4] === 0) {\n      it4++\n    }\n    var vch = new Uint8Array(zeroes + (size - it4))\n    var j = zeroes\n    while (it4 !== size) {\n      vch[j++] = b256[it4++]\n    }\n    return vch\n  }\n  function decode (string) {\n    var buffer = decodeUnsafe(string)\n    if (buffer) { return buffer }\n    throw new Error('Non-base' + BASE + ' character')\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  }\n}\nmodule.exports = base\n", "const basex = require('base-x')\nconst ALPHABET = '**********************************************************'\n\nmodule.exports = basex(ALPHABET)\n", "import { Connection, type ConnectionConfig } from '@solana/web3.js';\nimport React, { type FC, type ReactNode, useMemo } from 'react';\nimport { ConnectionContext } from './useConnection.js';\n\nexport interface ConnectionProviderProps {\n    children: ReactNode;\n    endpoint: string;\n    config?: ConnectionConfig;\n}\n\nexport const ConnectionProvider: FC<ConnectionProviderProps> = ({\n    children,\n    endpoint,\n    config = { commitment: 'confirmed' },\n}) => {\n    const connection = useMemo(() => new Connection(endpoint, config), [endpoint, config]);\n\n    return <ConnectionContext.Provider value={{ connection }}>{children}</ConnectionContext.Provider>;\n};\n", "import { type Connection } from '@solana/web3.js';\nimport { createContext, useContext } from 'react';\n\nexport interface ConnectionContextState {\n    connection: Connection;\n}\n\nexport const ConnectionContext = createContext<ConnectionContextState>({} as ConnectionContextState);\n\nexport function useConnection(): ConnectionContextState {\n    return useContext(ConnectionContext);\n}\n", "import { WalletError } from '@solana/wallet-adapter-base';\n\nexport class WalletNotSelectedError extends WalletError {\n    name = 'WalletNotSelectedError';\n}\n", "import { type PublicKey, type Transaction, type VersionedTransaction } from '@solana/web3.js';\nimport { useMemo } from 'react';\nimport { useWallet } from './useWallet.js';\n\nexport interface AnchorWallet {\n    publicKey: PublicKey;\n    signTransaction<T extends Transaction | VersionedTransaction>(transaction: T): Promise<T>;\n    signAllTransactions<T extends Transaction | VersionedTransaction>(transactions: T[]): Promise<T[]>;\n}\n\nexport function useAnchorWallet(): AnchorWallet | undefined {\n    const { publicKey, signTransaction, signAllTransactions } = useWallet();\n    return useMemo(\n        () =>\n            publicKey && signTransaction && signAllTransactions\n                ? { publicKey, signTransaction, signAllTransactions }\n                : undefined,\n        [publicKey, signTransaction, signAllTransactions]\n    );\n}\n", "import {\n    type Adapter,\n    type MessageSignerWalletAdapterProps,\n    type SignerWalletAdapterProps,\n    type SignInMessageSignerWalletAdapterProps,\n    type WalletAdapterProps,\n    type WalletName,\n    type WalletReadyState,\n} from '@solana/wallet-adapter-base';\nimport { type PublicKey } from '@solana/web3.js';\nimport { createContext, useContext } from 'react';\n\nexport interface Wallet {\n    adapter: Adapter;\n    readyState: WalletReadyState;\n}\n\nexport interface WalletContextState {\n    autoConnect: boolean;\n    wallets: Wallet[];\n    wallet: Wallet | null;\n    publicKey: PublicKey | null;\n    connecting: boolean;\n    connected: boolean;\n    disconnecting: boolean;\n\n    select(walletName: WalletName | null): void;\n    connect(): Promise<void>;\n    disconnect(): Promise<void>;\n\n    sendTransaction: WalletAdapterProps['sendTransaction'];\n    signTransaction: SignerWalletAdapterProps['signTransaction'] | undefined;\n    signAllTransactions: SignerWalletAdapterProps['signAllTransactions'] | undefined;\n    signMessage: MessageSignerWalletAdapterProps['signMessage'] | undefined;\n    signIn: SignInMessageSignerWalletAdapterProps['signIn'] | undefined;\n}\n\nconst EMPTY_ARRAY: ReadonlyArray<never> = [];\n\nconst DEFAULT_CONTEXT: Partial<WalletContextState> = {\n    autoConnect: false,\n    connecting: false,\n    connected: false,\n    disconnecting: false,\n    select() {\n        logMissingProviderError('call', 'select');\n    },\n    connect() {\n        return Promise.reject(logMissingProviderError('call', 'connect'));\n    },\n    disconnect() {\n        return Promise.reject(logMissingProviderError('call', 'disconnect'));\n    },\n    sendTransaction() {\n        return Promise.reject(logMissingProviderError('call', 'sendTransaction'));\n    },\n    signTransaction() {\n        return Promise.reject(logMissingProviderError('call', 'signTransaction'));\n    },\n    signAllTransactions() {\n        return Promise.reject(logMissingProviderError('call', 'signAllTransactions'));\n    },\n    signMessage() {\n        return Promise.reject(logMissingProviderError('call', 'signMessage'));\n    },\n    signIn() {\n        return Promise.reject(logMissingProviderError('call', 'signIn'));\n    },\n};\nObject.defineProperty(DEFAULT_CONTEXT, 'wallets', {\n    get() {\n        logMissingProviderError('read', 'wallets');\n        return EMPTY_ARRAY;\n    },\n});\nObject.defineProperty(DEFAULT_CONTEXT, 'wallet', {\n    get() {\n        logMissingProviderError('read', 'wallet');\n        return null;\n    },\n});\nObject.defineProperty(DEFAULT_CONTEXT, 'publicKey', {\n    get() {\n        logMissingProviderError('read', 'publicKey');\n        return null;\n    },\n});\n\nfunction logMissingProviderError(action: string, property: string) {\n    const error = new Error(\n        `You have tried to ${action} \"${property}\" on a WalletContext without providing one. ` +\n            'Make sure to render a WalletProvider as an ancestor of the component that uses WalletContext.'\n    );\n    console.error(error);\n    return error;\n}\n\nexport const WalletContext = createContext<WalletContextState>(DEFAULT_CONTEXT as WalletContextState);\n\nexport function useWallet(): WalletContextState {\n    return useContext(WalletContext);\n}\n", "import { type Dispatch, type SetStateAction, useEffect, useRef, useState } from 'react';\n\nexport function useLocalStorage<T>(key: string, defaultState: T): [T, Dispatch<SetStateAction<T>>] {\n    const state = useState<T>(() => {\n        try {\n            const value = localStorage.getItem(key);\n            if (value) return JSON.parse(value) as T;\n        } catch (error: any) {\n            if (typeof window !== 'undefined') {\n                console.error(error);\n            }\n        }\n\n        return defaultState;\n    });\n    const value = state[0];\n\n    const isFirstRenderRef = useRef(true);\n    useEffect(() => {\n        if (isFirstRenderRef.current) {\n            isFirstRenderRef.current = false;\n            return;\n        }\n        try {\n            if (value === null) {\n                localStorage.removeItem(key);\n            } else {\n                localStorage.setItem(key, JSON.stringify(value));\n            }\n        } catch (error: any) {\n            if (typeof window !== 'undefined') {\n                console.error(error);\n            }\n        }\n    }, [value, key]);\n\n    return state;\n}\n", "import { BaseSignInMessageSignerWalletAdapter, WalletReadyState, WalletPublicKeyError, WalletConnectionError, WalletNotReadyError, WalletSignMessageError, WalletSendTransactionError, WalletSignTransactionError, WalletNotConnectedError } from '@solana/wallet-adapter-base';\nimport { PublicKey, VersionedMessage, Transaction, VersionedTransaction } from '@solana/web3.js';\nimport { SolanaSignIn, SolanaSignMessage, SolanaSignAndSendTransaction, SolanaSignTransaction } from '@solana/wallet-standard-features';\nimport { LocalSolanaMobileWalletAdapterWallet, createDefaultChainSelector, RemoteSolanaMobileWalletAdapterWallet, createDefaultAuthorizationCache, defaultErrorModalWalletNotFoundHandler } from '@solana-mobile/wallet-standard-mobile';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\n\n(undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\n(undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\n\n/** Name of the feature. */\nconst StandardConnect = 'standard:connect';\n\n/** Name of the feature. */\nconst StandardDisconnect = 'standard:disconnect';\n\n/** Name of the feature. */\nconst StandardEvents = 'standard:events';\n\n(undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\n(undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\n\n(undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\n(undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\n\nfunction fromUint8Array(byteArray) {\n    return window.btoa(String.fromCharCode.call(null, ...byteArray));\n}\n\nfunction getIsSupported() {\n    return (typeof window !== 'undefined' &&\n        window.isSecureContext &&\n        typeof document !== 'undefined' &&\n        /android/i.test(navigator.userAgent));\n}\n\nvar _BaseSolanaMobileWalletAdapter_instances, _BaseSolanaMobileWalletAdapter_wallet, _BaseSolanaMobileWalletAdapter_connecting, _BaseSolanaMobileWalletAdapter_readyState, _BaseSolanaMobileWalletAdapter_accountSelector, _BaseSolanaMobileWalletAdapter_selectedAccount, _BaseSolanaMobileWalletAdapter_publicKey, _BaseSolanaMobileWalletAdapter_handleChangeEvent, _BaseSolanaMobileWalletAdapter_connect, _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled, _BaseSolanaMobileWalletAdapter_assertIsAuthorized, _BaseSolanaMobileWalletAdapter_performSignTransactions, _BaseSolanaMobileWalletAdapter_runWithGuard;\nconst SolanaMobileWalletAdapterWalletName = 'Mobile Wallet Adapter';\nconst SIGNATURE_LENGTH_IN_BYTES = 64;\nfunction isVersionedTransaction(transaction) {\n    return 'version' in transaction;\n}\nfunction chainOrClusterToChainId(chain) {\n    switch (chain) {\n        case 'mainnet-beta':\n            return 'solana:mainnet';\n        case 'testnet':\n            return 'solana:testnet';\n        case 'devnet':\n            return 'solana:devnet';\n        default:\n            return chain;\n    }\n}\nclass BaseSolanaMobileWalletAdapter extends BaseSignInMessageSignerWalletAdapter {\n    constructor(wallet, config) {\n        super();\n        _BaseSolanaMobileWalletAdapter_instances.add(this);\n        this.supportedTransactionVersions = new Set(\n        // FIXME(#244): We can't actually know what versions are supported until we know which wallet we're talking to.\n        ['legacy', 0]);\n        _BaseSolanaMobileWalletAdapter_wallet.set(this, void 0);\n        _BaseSolanaMobileWalletAdapter_connecting.set(this, false);\n        _BaseSolanaMobileWalletAdapter_readyState.set(this, getIsSupported() ? WalletReadyState.Loadable : WalletReadyState.Unsupported);\n        _BaseSolanaMobileWalletAdapter_accountSelector.set(this, void 0);\n        _BaseSolanaMobileWalletAdapter_selectedAccount.set(this, void 0);\n        _BaseSolanaMobileWalletAdapter_publicKey.set(this, void 0);\n        _BaseSolanaMobileWalletAdapter_handleChangeEvent.set(this, (properties) => __awaiter(this, void 0, void 0, function* () {\n            if (properties.accounts && properties.accounts.length > 0) {\n                __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled).call(this);\n                const nextSelectedAccount = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_accountSelector, \"f\").call(this, properties.accounts);\n                if (nextSelectedAccount !== __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, \"f\")) {\n                    __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, nextSelectedAccount, \"f\");\n                    __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_publicKey, undefined, \"f\");\n                    this.emit('connect', \n                    // Having just set `this.#selectedAccount`, `this.publicKey` is definitely non-null\n                    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                    this.publicKey);\n                }\n            }\n        }));\n        // this.#chain = chainOrClusterToChainId(config.chain);\n        __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_accountSelector, (accounts) => __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            const selectedBase64EncodedAddress = yield config.addressSelector.select(accounts.map(({ publicKey }) => fromUint8Array(publicKey)));\n            return (_a = accounts.find(({ publicKey }) => fromUint8Array(publicKey) === selectedBase64EncodedAddress)) !== null && _a !== void 0 ? _a : accounts[0];\n        }), \"f\");\n        __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_wallet, wallet, \"f\");\n        __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[StandardEvents].on('change', __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_handleChangeEvent, \"f\"));\n        this.name = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").name;\n        this.icon = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").icon;\n        this.url = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").url;\n        // TODO: evaluate if this logic should be kept - it seems to create a nasty bug where \n        //  the wallet tries to auto connect on page load and gets blocked by the popup blocker\n        // if (this.#readyState !== WalletReadyState.Unsupported) {\n        //     config.authorizationResultCache.get().then((authorizationResult) => {\n        //         if (authorizationResult) {\n        //             // Having a prior authorization result is, right now, the best\n        //             // indication that a mobile wallet is installed. There is no API\n        //             // we can use to test for whether the association URI is supported.\n        //             this.#declareWalletAsInstalled();\n        //         }\n        //     });\n        // }\n    }\n    get publicKey() {\n        var _a;\n        if (!__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_publicKey, \"f\") && __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, \"f\")) {\n            try {\n                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_publicKey, new PublicKey(__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, \"f\").publicKey), \"f\");\n            }\n            catch (e) {\n                throw new WalletPublicKeyError((e instanceof Error && (e === null || e === void 0 ? void 0 : e.message)) || 'Unknown error', e);\n            }\n        }\n        return (_a = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_publicKey, \"f\")) !== null && _a !== void 0 ? _a : null;\n    }\n    get connected() {\n        return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").connected;\n    }\n    get connecting() {\n        return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_connecting, \"f\");\n    }\n    get readyState() {\n        return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, \"f\");\n    }\n    /** @deprecated Use `autoConnect()` instead. */\n    autoConnect_DO_NOT_USE_OR_YOU_WILL_BE_FIRED() {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield this.autoConnect();\n        });\n    }\n    autoConnect() {\n        return __awaiter(this, void 0, void 0, function* () {\n            __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_connect).call(this, true);\n        });\n    }\n    connect() {\n        return __awaiter(this, void 0, void 0, function* () {\n            __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_connect).call(this);\n        });\n    }\n    /** @deprecated Use `connect()` or `autoConnect()` instead. */\n    performAuthorization(signInPayload) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const cachedAuthorizationResult = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").cachedAuthorizationResult;\n                if (cachedAuthorizationResult) {\n                    yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[StandardConnect].connect({ silent: true });\n                    return cachedAuthorizationResult;\n                }\n                if (signInPayload) {\n                    yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[SolanaSignIn].signIn(signInPayload);\n                }\n                else\n                    yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[StandardConnect].connect();\n                const authorizationResult = yield yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").cachedAuthorizationResult;\n                return authorizationResult;\n            }\n            catch (e) {\n                throw new WalletConnectionError((e instanceof Error && e.message) || 'Unknown error', e);\n            }\n        });\n    }\n    disconnect() {\n        return __awaiter(this, void 0, void 0, function* () {\n            // return await this.#runWithGuard(this.#wallet.features[StandardDisconnect].disconnect);\n            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, false, \"f\");\n                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_publicKey, undefined, \"f\");\n                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, undefined, \"f\");\n                yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[StandardDisconnect].disconnect();\n                this.emit('disconnect');\n            }));\n        });\n    }\n    signIn(input) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n                var _a;\n                if (__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, \"f\") !== WalletReadyState.Installed && __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, \"f\") !== WalletReadyState.Loadable) {\n                    throw new WalletNotReadyError();\n                }\n                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, true, \"f\");\n                try {\n                    const outputs = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[SolanaSignIn].signIn(Object.assign(Object.assign({}, input), { domain: (_a = input === null || input === void 0 ? void 0 : input.domain) !== null && _a !== void 0 ? _a : window.location.host }));\n                    if (outputs.length > 0) {\n                        return outputs[0];\n                    }\n                    else {\n                        throw new Error(\"Sign in failed, no sign in result returned by wallet\");\n                    }\n                }\n                catch (e) {\n                    throw new WalletConnectionError((e instanceof Error && e.message) || 'Unknown error', e);\n                }\n                finally {\n                    __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, false, \"f\");\n                }\n            }));\n        });\n    }\n    signMessage(message) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n                const account = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_assertIsAuthorized).call(this);\n                try {\n                    const outputs = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[SolanaSignMessage].signMessage({\n                        account, message: message\n                    });\n                    return outputs[0].signature;\n                }\n                catch (error) {\n                    throw new WalletSignMessageError(error === null || error === void 0 ? void 0 : error.message, error);\n                }\n            }));\n        });\n    }\n    sendTransaction(transaction, connection, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n                const account = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_assertIsAuthorized).call(this);\n                try {\n                    function getTargetCommitment() {\n                        let targetCommitment;\n                        switch (connection.commitment) {\n                            case 'confirmed':\n                            case 'finalized':\n                            case 'processed':\n                                targetCommitment = connection.commitment;\n                                break;\n                            default:\n                                targetCommitment = 'finalized';\n                        }\n                        let targetPreflightCommitment;\n                        switch (options === null || options === void 0 ? void 0 : options.preflightCommitment) {\n                            case 'confirmed':\n                            case 'finalized':\n                            case 'processed':\n                                targetPreflightCommitment = options.preflightCommitment;\n                                break;\n                            case undefined:\n                                targetPreflightCommitment = targetCommitment;\n                                break;\n                            default:\n                                targetPreflightCommitment = 'finalized';\n                        }\n                        const preflightCommitmentScore = targetPreflightCommitment === 'finalized'\n                            ? 2\n                            : targetPreflightCommitment === 'confirmed'\n                                ? 1\n                                : 0;\n                        const targetCommitmentScore = targetCommitment === 'finalized' ? 2 : targetCommitment === 'confirmed' ? 1 : 0;\n                        return preflightCommitmentScore < targetCommitmentScore\n                            ? targetPreflightCommitment\n                            : targetCommitment;\n                    }\n                    if (SolanaSignAndSendTransaction in __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features) {\n                        const chain = chainOrClusterToChainId(__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").currentAuthorization.chain);\n                        const [signature] = (yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[SolanaSignAndSendTransaction].signAndSendTransaction({\n                            account,\n                            transaction: transaction.serialize(),\n                            chain: chain,\n                            options: options ? {\n                                skipPreflight: options.skipPreflight,\n                                maxRetries: options.maxRetries\n                            } : undefined\n                        })).map(((output) => {\n                            return fromUint8Array(output.signature);\n                        }));\n                        return signature;\n                    }\n                    else {\n                        const [signedTransaction] = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_performSignTransactions).call(this, [transaction]);\n                        if (isVersionedTransaction(signedTransaction)) {\n                            return yield connection.sendTransaction(signedTransaction);\n                        }\n                        else {\n                            const serializedTransaction = signedTransaction.serialize();\n                            return yield connection.sendRawTransaction(serializedTransaction, Object.assign(Object.assign({}, options), { preflightCommitment: getTargetCommitment() }));\n                        }\n                    }\n                }\n                catch (error) {\n                    throw new WalletSendTransactionError(error === null || error === void 0 ? void 0 : error.message, error);\n                }\n            }));\n        });\n    }\n    signTransaction(transaction) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n                const [signedTransaction] = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_performSignTransactions).call(this, [transaction]);\n                return signedTransaction;\n            }));\n        });\n    }\n    signAllTransactions(transactions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n                const signedTransactions = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_performSignTransactions).call(this, transactions);\n                return signedTransactions;\n            }));\n        });\n    }\n}\n_BaseSolanaMobileWalletAdapter_wallet = new WeakMap(), _BaseSolanaMobileWalletAdapter_connecting = new WeakMap(), _BaseSolanaMobileWalletAdapter_readyState = new WeakMap(), _BaseSolanaMobileWalletAdapter_accountSelector = new WeakMap(), _BaseSolanaMobileWalletAdapter_selectedAccount = new WeakMap(), _BaseSolanaMobileWalletAdapter_publicKey = new WeakMap(), _BaseSolanaMobileWalletAdapter_handleChangeEvent = new WeakMap(), _BaseSolanaMobileWalletAdapter_instances = new WeakSet(), _BaseSolanaMobileWalletAdapter_connect = function _BaseSolanaMobileWalletAdapter_connect(autoConnect = false) {\n    return __awaiter(this, void 0, void 0, function* () {\n        if (this.connecting || this.connected) {\n            return;\n        }\n        return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n            if (__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, \"f\") !== WalletReadyState.Installed && __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, \"f\") !== WalletReadyState.Loadable) {\n                throw new WalletNotReadyError();\n            }\n            __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, true, \"f\");\n            try {\n                yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[StandardConnect].connect({ silent: autoConnect });\n            }\n            catch (e) {\n                throw new WalletConnectionError((e instanceof Error && e.message) || 'Unknown error', e);\n            }\n            finally {\n                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, false, \"f\");\n            }\n        }));\n    });\n}, _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled = function _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled() {\n    if (__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, \"f\") !== WalletReadyState.Installed) {\n        this.emit('readyStateChange', (__classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_readyState, WalletReadyState.Installed, \"f\")));\n    }\n}, _BaseSolanaMobileWalletAdapter_assertIsAuthorized = function _BaseSolanaMobileWalletAdapter_assertIsAuthorized() {\n    if (!__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").isAuthorized || !__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, \"f\"))\n        throw new WalletNotConnectedError();\n    return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, \"f\");\n}, _BaseSolanaMobileWalletAdapter_performSignTransactions = function _BaseSolanaMobileWalletAdapter_performSignTransactions(transactions) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const account = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_assertIsAuthorized).call(this);\n        try {\n            if (SolanaSignTransaction in __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features) {\n                return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[SolanaSignTransaction].signTransaction(...transactions.map((value) => {\n                    return { account, transaction: value.serialize() };\n                })).then((outputs) => {\n                    return outputs.map((output) => {\n                        const byteArray = output.signedTransaction;\n                        const numSignatures = byteArray[0];\n                        const messageOffset = numSignatures * SIGNATURE_LENGTH_IN_BYTES + 1;\n                        const version = VersionedMessage.deserializeMessageVersion(byteArray.slice(messageOffset, byteArray.length));\n                        if (version === 'legacy') {\n                            return Transaction.from(byteArray);\n                        }\n                        else {\n                            return VersionedTransaction.deserialize(byteArray);\n                        }\n                    });\n                });\n            }\n            else {\n                throw new Error('Connected wallet does not support signing transactions');\n            }\n        }\n        catch (error) {\n            throw new WalletSignTransactionError(error === null || error === void 0 ? void 0 : error.message, error);\n        }\n    });\n}, _BaseSolanaMobileWalletAdapter_runWithGuard = function _BaseSolanaMobileWalletAdapter_runWithGuard(callback) {\n    return __awaiter(this, void 0, void 0, function* () {\n        try {\n            return yield callback();\n        }\n        catch (e) {\n            this.emit('error', e);\n            throw e;\n        }\n    });\n};\nclass LocalSolanaMobileWalletAdapter extends BaseSolanaMobileWalletAdapter {\n    constructor(config) {\n        var _a;\n        const chain = chainOrClusterToChainId((_a = config.chain) !== null && _a !== void 0 ? _a : config.cluster);\n        super(new LocalSolanaMobileWalletAdapterWallet({\n            appIdentity: config.appIdentity,\n            authorizationCache: {\n                set: config.authorizationResultCache.set,\n                get: () => __awaiter(this, void 0, void 0, function* () {\n                    const authorizationResult = yield config.authorizationResultCache.get();\n                    if (authorizationResult && 'chain' in authorizationResult) {\n                        return authorizationResult;\n                    }\n                    else if (authorizationResult) {\n                        return Object.assign(Object.assign({}, authorizationResult), { chain: chain });\n                    }\n                    else\n                        return undefined;\n                }),\n                clear: config.authorizationResultCache.clear,\n            },\n            chains: [chain],\n            chainSelector: createDefaultChainSelector(),\n            onWalletNotFound: () => __awaiter(this, void 0, void 0, function* () {\n                config.onWalletNotFound(this);\n            }),\n        }), {\n            addressSelector: config.addressSelector,\n            chain: chain,\n        });\n    }\n}\nclass RemoteSolanaMobileWalletAdapter extends BaseSolanaMobileWalletAdapter {\n    constructor(config) {\n        const chain = chainOrClusterToChainId(config.chain);\n        super(new RemoteSolanaMobileWalletAdapterWallet({\n            appIdentity: config.appIdentity,\n            authorizationCache: {\n                set: config.authorizationResultCache.set,\n                get: () => __awaiter(this, void 0, void 0, function* () {\n                    const authorizationResult = yield config.authorizationResultCache.get();\n                    if (authorizationResult && 'chain' in authorizationResult) {\n                        return authorizationResult;\n                    }\n                    else if (authorizationResult) {\n                        return Object.assign(Object.assign({}, authorizationResult), { chain: chain });\n                    }\n                    else\n                        return undefined;\n                }),\n                clear: config.authorizationResultCache.clear,\n            },\n            chains: [chain],\n            chainSelector: createDefaultChainSelector(),\n            remoteHostAuthority: config.remoteHostAuthority,\n            onWalletNotFound: () => __awaiter(this, void 0, void 0, function* () {\n                config.onWalletNotFound(this);\n            }),\n        }), {\n            addressSelector: config.addressSelector,\n            chain: chain,\n        });\n    }\n}\nclass SolanaMobileWalletAdapter extends LocalSolanaMobileWalletAdapter {\n}\n\nfunction createDefaultAddressSelector() {\n    return {\n        select(addresses) {\n            return __awaiter(this, void 0, void 0, function* () {\n                return addresses[0];\n            });\n        },\n    };\n}\n\nfunction createDefaultAuthorizationResultCache() {\n    return createDefaultAuthorizationCache();\n}\n\nfunction defaultWalletNotFoundHandler(mobileWalletAdapter) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return defaultErrorModalWalletNotFoundHandler();\n    });\n}\nfunction createDefaultWalletNotFoundHandler() {\n    return defaultWalletNotFoundHandler;\n}\n\nexport { LocalSolanaMobileWalletAdapter, RemoteSolanaMobileWalletAdapter, SolanaMobileWalletAdapter, SolanaMobileWalletAdapterWalletName, createDefaultAddressSelector, createDefaultAuthorizationResultCache, createDefaultWalletNotFoundHandler };\n", "import { SolanaSignAndSendTransaction, SolanaSignTransaction, SolanaSignMessage, SolanaSignIn } from '@solana/wallet-standard-features';\nimport { VersionedTransaction, PublicKey } from '@solana/web3.js';\nimport QRCode from 'qrcode';\nimport { StandardConnect, StandardDisconnect, StandardEvents } from '@wallet-standard/features';\nimport { SOLANA_MAINNET_CHAIN } from '@solana/wallet-standard-chains';\nimport { transact, startRemoteScenario } from '@solana-mobile/mobile-wallet-adapter-protocol-web3js';\nimport base58 from 'bs58';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nfunction __classPrivateFieldGet$1(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nfunction __classPrivateFieldSet$1(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\n\nvar _EmbeddedModal_instances, _EmbeddedModal_root, _EmbeddedModal_eventListeners, _EmbeddedModal_listenersAttached, _EmbeddedModal_injectHTML, _EmbeddedModal_attachEventListeners, _EmbeddedModal_removeEventListeners, _EmbeddedModal_handleKeyDown;\nconst modalHtml = `\n<div class=\"mobile-wallet-adapter-embedded-modal-container\" role=\"dialog\" aria-modal=\"true\" aria-labelledby=\"modal-title\">\n    <div data-modal-close style=\"position: absolute; width: 100%; height: 100%;\"></div>\n\t<div class=\"mobile-wallet-adapter-embedded-modal-card\">\n\t\t<div>\n\t\t\t<button data-modal-close class=\"mobile-wallet-adapter-embedded-modal-close\">\n\t\t\t\t<svg width=\"14\" height=\"14\">\n\t\t\t\t\t<path d=\"M 6.7125,8.3036995 1.9082,13.108199 c -0.2113,0.2112 -0.4765,0.3168 -0.7957,0.3168 -0.3192,0 -0.5844,-0.1056 -0.7958,-0.3168 C 0.1056,12.896899 0,12.631699 0,12.312499 c 0,-0.3192 0.1056,-0.5844 0.3167,-0.7958 L 5.1212,6.7124995 0.3167,1.9082 C 0.1056,1.6969 0,1.4317 0,1.1125 0,0.7933 0.1056,0.5281 0.3167,0.3167 0.5281,0.1056 0.7933,0 1.1125,0 1.4317,0 1.6969,0.1056 1.9082,0.3167 L 6.7125,5.1212 11.5167,0.3167 C 11.7281,0.1056 11.9933,0 12.3125,0 c 0.3192,0 0.5844,0.1056 0.7957,0.3167 0.2112,0.2114 0.3168,0.4766 0.3168,0.7958 0,0.3192 -0.1056,0.5844 -0.3168,0.7957 L 8.3037001,6.7124995 13.1082,11.516699 c 0.2112,0.2114 0.3168,0.4766 0.3168,0.7958 0,0.3192 -0.1056,0.5844 -0.3168,0.7957 -0.2113,0.2112 -0.4765,0.3168 -0.7957,0.3168 -0.3192,0 -0.5844,-0.1056 -0.7958,-0.3168 z\" />\n\t\t\t\t</svg>\n\t\t\t</button>\n\t\t</div>\n\t\t<div class=\"mobile-wallet-adapter-embedded-modal-content\"></div>\n\t</div>\n</div>\n`;\nconst css$2 = `\n.mobile-wallet-adapter-embedded-modal-container {\n    display: flex; /* Use flexbox to center content */\n    justify-content: center; /* Center horizontally */\n    align-items: center; /* Center vertically */\n    position: fixed; /* Stay in place */\n    z-index: 1; /* Sit on top */\n    left: 0;\n    top: 0;\n    width: 100%; /* Full width */\n    height: 100%; /* Full height */\n    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */\n    overflow-y: auto; /* enable scrolling */\n}\n\n.mobile-wallet-adapter-embedded-modal-card {\n    display: flex;\n    flex-direction: column;\n    margin: auto 20px;\n    max-width: 780px;\n    padding: 20px;\n    border-radius: 24px;\n    background: #ffffff;\n    font-family: \"Inter Tight\", \"PT Sans\", Calibri, sans-serif;\n    transform: translateY(-200%);\n    animation: slide-in 0.5s forwards;\n}\n\n@keyframes slide-in {\n    100% { transform: translateY(0%); }\n}\n\n.mobile-wallet-adapter-embedded-modal-close {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 32px;\n    height: 32px;\n    cursor: pointer;\n    background: #e4e9e9;\n    border: none;\n    border-radius: 50%;\n}\n\n.mobile-wallet-adapter-embedded-modal-close:focus-visible {\n    outline-color: red;\n}\n\n.mobile-wallet-adapter-embedded-modal-close svg {\n    fill: #546266;\n    transition: fill 200ms ease 0s;\n}\n\n.mobile-wallet-adapter-embedded-modal-close:hover svg {\n    fill: #fff;\n}\n`;\nconst fonts = `\n<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n<link href=\"https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&display=swap\" rel=\"stylesheet\">\n`;\nclass EmbeddedModal {\n    constructor() {\n        _EmbeddedModal_instances.add(this);\n        _EmbeddedModal_root.set(this, null);\n        _EmbeddedModal_eventListeners.set(this, {});\n        _EmbeddedModal_listenersAttached.set(this, false);\n        this.dom = null;\n        this.open = () => {\n            console.debug('Modal open');\n            __classPrivateFieldGet$1(this, _EmbeddedModal_instances, \"m\", _EmbeddedModal_attachEventListeners).call(this);\n            if (__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\")) {\n                __classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").style.display = 'flex';\n            }\n        };\n        this.close = (event = undefined) => {\n            var _a;\n            console.debug('Modal close');\n            __classPrivateFieldGet$1(this, _EmbeddedModal_instances, \"m\", _EmbeddedModal_removeEventListeners).call(this);\n            if (__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\")) {\n                __classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").style.display = 'none';\n            }\n            (_a = __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, \"f\")['close']) === null || _a === void 0 ? void 0 : _a.forEach((listener) => listener(event));\n        };\n        _EmbeddedModal_handleKeyDown.set(this, (event) => {\n            if (event.key === 'Escape')\n                this.close(event);\n        });\n        // Bind methods to ensure `this` context is correct\n        this.init = this.init.bind(this);\n        __classPrivateFieldSet$1(this, _EmbeddedModal_root, document.getElementById('mobile-wallet-adapter-embedded-root-ui'), \"f\");\n    }\n    init() {\n        return __awaiter(this, void 0, void 0, function* () {\n            console.log('Injecting modal');\n            __classPrivateFieldGet$1(this, _EmbeddedModal_instances, \"m\", _EmbeddedModal_injectHTML).call(this);\n        });\n    }\n    addEventListener(event, listener) {\n        var _a;\n        ((_a = __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.push(listener)) || (__classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, \"f\")[event] = [listener]);\n        return () => this.removeEventListener(event, listener);\n    }\n    removeEventListener(event, listener) {\n        var _a;\n        __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, \"f\")[event] = (_a = __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.filter((existingListener) => listener !== existingListener);\n    }\n}\n_EmbeddedModal_root = new WeakMap(), _EmbeddedModal_eventListeners = new WeakMap(), _EmbeddedModal_listenersAttached = new WeakMap(), _EmbeddedModal_handleKeyDown = new WeakMap(), _EmbeddedModal_instances = new WeakSet(), _EmbeddedModal_injectHTML = function _EmbeddedModal_injectHTML() {\n    // Check if the HTML has already been injected\n    if (document.getElementById('mobile-wallet-adapter-embedded-root-ui')) {\n        if (!__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\"))\n            __classPrivateFieldSet$1(this, _EmbeddedModal_root, document.getElementById('mobile-wallet-adapter-embedded-root-ui'), \"f\");\n        return;\n    }\n    // Create a container for the modal\n    __classPrivateFieldSet$1(this, _EmbeddedModal_root, document.createElement('div'), \"f\");\n    __classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").id = 'mobile-wallet-adapter-embedded-root-ui';\n    __classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").innerHTML = modalHtml;\n    __classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").style.display = 'none';\n    // Add modal content\n    const content = __classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").querySelector('.mobile-wallet-adapter-embedded-modal-content');\n    if (content)\n        content.innerHTML = this.contentHtml;\n    // Apply styles\n    const styles = document.createElement('style');\n    styles.id = 'mobile-wallet-adapter-embedded-modal-styles';\n    styles.textContent = css$2 + this.contentStyles;\n    // Create a shadow DOM to encapsulate the modal\n    const host = document.createElement('div');\n    host.innerHTML = fonts;\n    this.dom = host.attachShadow({ mode: 'closed' });\n    this.dom.appendChild(styles);\n    this.dom.appendChild(__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\"));\n    // Append the shadow DOM host to the body\n    document.body.appendChild(host);\n}, _EmbeddedModal_attachEventListeners = function _EmbeddedModal_attachEventListeners() {\n    if (!__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\") || __classPrivateFieldGet$1(this, _EmbeddedModal_listenersAttached, \"f\"))\n        return;\n    const closers = [...__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").querySelectorAll('[data-modal-close]')];\n    closers.forEach(closer => closer === null || closer === void 0 ? void 0 : closer.addEventListener('click', this.close));\n    window.addEventListener('load', this.close);\n    document.addEventListener('keydown', __classPrivateFieldGet$1(this, _EmbeddedModal_handleKeyDown, \"f\"));\n    __classPrivateFieldSet$1(this, _EmbeddedModal_listenersAttached, true, \"f\");\n}, _EmbeddedModal_removeEventListeners = function _EmbeddedModal_removeEventListeners() {\n    if (!__classPrivateFieldGet$1(this, _EmbeddedModal_listenersAttached, \"f\"))\n        return;\n    window.removeEventListener('load', this.close);\n    document.removeEventListener('keydown', __classPrivateFieldGet$1(this, _EmbeddedModal_handleKeyDown, \"f\"));\n    if (!__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\"))\n        return;\n    const closers = [...__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").querySelectorAll('[data-modal-close]')];\n    closers.forEach(closer => closer === null || closer === void 0 ? void 0 : closer.removeEventListener('click', this.close));\n    __classPrivateFieldSet$1(this, _EmbeddedModal_listenersAttached, false, \"f\");\n};\n\nclass RemoteConnectionModal extends EmbeddedModal {\n    constructor() {\n        super(...arguments);\n        this.contentStyles = css$1;\n        this.contentHtml = QRCodeHtml;\n    }\n    initWithQR(qrCode) {\n        const _super = Object.create(null, {\n            init: { get: () => super.init }\n        });\n        return __awaiter(this, void 0, void 0, function* () {\n            _super.init.call(this);\n            this.populateQRCode(qrCode);\n        });\n    }\n    populateQRCode(qrUrl) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            const qrcodeContainer = (_a = this.dom) === null || _a === void 0 ? void 0 : _a.getElementById('mobile-wallet-adapter-embedded-modal-qr-code-container');\n            if (qrcodeContainer) {\n                const qrCodeElement = yield QRCode.toCanvas(qrUrl, { width: 200, margin: 0 });\n                if (qrcodeContainer.firstElementChild !== null) {\n                    qrcodeContainer.replaceChild(qrCodeElement, qrcodeContainer.firstElementChild);\n                }\n                else\n                    qrcodeContainer.appendChild(qrCodeElement);\n            }\n            else {\n                console.error('QRCode Container not found');\n            }\n        });\n    }\n}\nconst QRCodeHtml = `\n<div class=\"mobile-wallet-adapter-embedded-modal-qr-content\">\n    <div>\n        <svg class=\"mobile-wallet-adapter-embedded-modal-icon\" width=\"100%\" height=\"100%\">\n            <circle r=\"52\" cx=\"53\" cy=\"53\" fill=\"#99b3be\" stroke=\"#000000\" stroke-width=\"2\"/>\n            <path d=\"m 53,82.7305 c -3.3116,0 -6.1361,-1.169 -8.4735,-3.507 -2.338,-2.338 -3.507,-5.1625 -3.507,-8.4735 0,-3.3116 1.169,-6.1364 3.507,-8.4744 2.3374,-2.338 5.1619,-3.507 8.4735,-3.507 3.3116,0 6.1361,1.169 8.4735,3.507 2.338,2.338 3.507,5.1628 3.507,8.4744 0,3.311 -1.169,6.1355 -3.507,8.4735 -2.3374,2.338 -5.1619,3.507 -8.4735,3.507 z m 0.007,-5.25 c 1.8532,0 3.437,-0.6598 4.7512,-1.9793 1.3149,-1.3195 1.9723,-2.9058 1.9723,-4.7591 0,-1.8526 -0.6598,-3.4364 -1.9793,-4.7512 -1.3195,-1.3149 -2.9055,-1.9723 -4.7582,-1.9723 -1.8533,0 -3.437,0.6598 -4.7513,1.9793 -1.3148,1.3195 -1.9722,2.9058 -1.9722,4.7591 0,1.8527 0.6597,3.4364 1.9792,4.7512 1.3195,1.3149 2.9056,1.9723 4.7583,1.9723 z m -28,-33.5729 -3.85,-3.6347 c 4.1195,-4.025 8.8792,-7.1984 14.2791,-9.52 5.4005,-2.3223 11.2551,-3.4834 17.5639,-3.4834 6.3087,0 12.1634,1.1611 17.5639,3.4834 5.3999,2.3216 10.1596,5.495 14.2791,9.52 l -3.85,3.6347 C 77.2999,40.358 73.0684,37.5726 68.2985,35.5514 63.5292,33.5301 58.4296,32.5195 53,32.5195 c -5.4297,0 -10.5292,1.0106 -15.2985,3.0319 -4.7699,2.0212 -9.0014,4.8066 -12.6945,8.3562 z m 44.625,10.8771 c -2.2709,-2.1046 -4.7962,-3.7167 -7.5758,-4.8361 -2.7795,-1.12 -5.7983,-1.68 -9.0562,-1.68 -3.2579,0 -6.2621,0.56 -9.0125,1.68 -2.7504,1.1194 -5.2903,2.7315 -7.6195,4.8361 L 32.5189,51.15 c 2.8355,-2.6028 5.9777,-4.6086 9.4263,-6.0174 3.4481,-1.4087 7.133,-2.1131 11.0548,-2.1131 3.9217,0 7.5979,0.7044 11.0285,2.1131 3.43,1.4088 6.5631,3.4146 9.3992,6.0174 z\"/>\n        </svg>\n        <div class=\"mobile-wallet-adapter-embedded-modal-title\">Remote Mobile Wallet Adapter</div>\n    </div>\n    <div>\n        <div>\n            <h4 class=\"mobile-wallet-adapter-embedded-modal-qr-label\">\n                Open your wallet and scan this code\n            </h4>\n        </div>\n        <div id=\"mobile-wallet-adapter-embedded-modal-qr-code-container\" class=\"mobile-wallet-adapter-embedded-modal-qr-code-container\"></div>\n    </div>\n</div>\n<div class=\"mobile-wallet-adapter-embedded-modal-divider\"><hr></div>\n<div class=\"mobile-wallet-adapter-embedded-modal-footer\">\n    <div class=\"mobile-wallet-adapter-embedded-modal-subtitle\">\n        Follow the instructions on your device. When you're finished, this screen will update.\n    </div>\n    <div class=\"mobile-wallet-adapter-embedded-modal-progress-badge\">\n        <div>\n            <div class=\"spinner\">\n                <div class=\"leftWrapper\">\n                    <div class=\"left\">\n                        <div class=\"circle\"></div>\n                    </div>\n                </div>\n                <div class=\"rightWrapper\">\n                    <div class=\"right\">\n                        <div class=\"circle\"></div>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <div>Waiting for scan</div>\n    </div>\n</div>\n`;\nconst css$1 = `\n.mobile-wallet-adapter-embedded-modal-qr-content {\n    display: flex; \n    margin-top: 10px;\n    padding: 10px;\n}\n\n.mobile-wallet-adapter-embedded-modal-qr-content > div:first-child {\n    display: flex;\n    flex-direction: column;\n    flex: 2;\n    margin-top: auto;\n    margin-right: 30px;\n}\n\n.mobile-wallet-adapter-embedded-modal-qr-content > div:nth-child(2) {\n    display: flex;\n    flex-direction: column;\n    flex: 1;\n    margin-left: auto;\n}\n\n.mobile-wallet-adapter-embedded-modal-footer {\n    display: flex;\n    padding: 10px;\n}\n\n.mobile-wallet-adapter-embedded-modal-icon {}\n\n.mobile-wallet-adapter-embedded-modal-title {\n    color: #000000;\n    font-size: 2.5em;\n    font-weight: 600;\n}\n\n.mobile-wallet-adapter-embedded-modal-qr-label {\n    text-align: right;\n    color: #000000;\n}\n\n.mobile-wallet-adapter-embedded-modal-qr-code-container {\n    margin-left: auto;\n}\n\n.mobile-wallet-adapter-embedded-modal-divider {\n    margin-top: 20px;\n    padding-left: 10px;\n    padding-right: 10px;\n}\n\n.mobile-wallet-adapter-embedded-modal-divider hr {\n    border-top: 1px solid #D9DEDE;\n}\n\n.mobile-wallet-adapter-embedded-modal-subtitle {\n    margin: auto;\n    margin-right: 60px;\n    padding: 20px;\n    color: #6E8286;\n}\n\n.mobile-wallet-adapter-embedded-modal-progress-badge {\n    display: flex;\n    background: #F7F8F8;\n    height: 56px;\n    min-width: 200px;\n    margin: auto;\n    padding-left: 20px;\n    padding-right: 20px;\n    border-radius: 18px;\n    color: #A8B6B8;\n    align-items: center;\n}\n\n.mobile-wallet-adapter-embedded-modal-progress-badge > div:first-child {\n    margin-left: auto;\n    margin-right: 20px;\n}\n\n.mobile-wallet-adapter-embedded-modal-progress-badge > div:nth-child(2) {\n    margin-right: auto;\n}\n\n/* Smaller screens */\n@media all and (max-width: 600px) {\n    .mobile-wallet-adapter-embedded-modal-card {\n        text-align: center;\n    }\n    .mobile-wallet-adapter-embedded-modal-qr-content {\n        flex-direction: column;\n    }\n    .mobile-wallet-adapter-embedded-modal-qr-content > div:first-child {\n        margin: auto;\n    }\n    .mobile-wallet-adapter-embedded-modal-qr-content > div:nth-child(2) {\n        margin: auto;\n        flex: 2 auto;\n    }\n    .mobile-wallet-adapter-embedded-modal-footer {\n        flex-direction: column;\n    }\n    .mobile-wallet-adapter-embedded-modal-icon {\n        display: none;\n    }\n    .mobile-wallet-adapter-embedded-modal-title {\n        font-size: 1.5em;\n    }\n    .mobile-wallet-adapter-embedded-modal-subtitle {\n        margin-right: unset;\n    }\n    .mobile-wallet-adapter-embedded-modal-qr-label {\n        text-align: center;\n    }\n    .mobile-wallet-adapter-embedded-modal-qr-code-container {\n        margin: auto;\n    }\n}\n\n/* Spinner */\n@keyframes spinLeft {\n    0% {\n        transform: rotate(20deg);\n    }\n    50% {\n        transform: rotate(160deg);\n    }\n    100% {\n        transform: rotate(20deg);\n    }\n}\n@keyframes spinRight {\n    0% {\n        transform: rotate(160deg);\n    }\n    50% {\n        transform: rotate(20deg);\n    }\n    100% {\n        transform: rotate(160deg);\n    }\n}\n@keyframes spin {\n    0% {\n        transform: rotate(0deg);\n    }\n    100% {\n        transform: rotate(2520deg);\n    }\n}\n\n.spinner {\n    position: relative;\n    width: 1.5em;\n    height: 1.5em;\n    margin: auto;\n    animation: spin 10s linear infinite;\n}\n.spinner::before {\n    content: \"\";\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n}\n.right, .rightWrapper, .left, .leftWrapper {\n    position: absolute;\n    top: 0;\n    overflow: hidden;\n    width: .75em;\n    height: 1.5em;\n}\n.left, .leftWrapper {\n    left: 0;\n}\n.right {\n    left: -12px;\n}\n.rightWrapper {\n    right: 0;\n}\n.circle {\n    border: .125em solid #A8B6B8;\n    width: 1.25em; /* 1.5em - 2*0.125em border */\n    height: 1.25em; /* 1.5em - 2*0.125em border */\n    border-radius: 0.75em; /* 0.5*1.5em spinner size 8 */\n}\n.left {\n    transform-origin: 100% 50%;\n    animation: spinLeft 2.5s cubic-bezier(.2,0,.8,1) infinite;\n}\n.right {\n    transform-origin: 100% 50%;\n    animation: spinRight 2.5s cubic-bezier(.2,0,.8,1) infinite;\n}\n`;\n\nconst icon = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03IDIuNUgxN0MxNy44Mjg0IDIuNSAxOC41IDMuMTcxNTcgMTguNSA0VjIwQzE4LjUgMjAuODI4NCAxNy44Mjg0IDIxLjUgMTcgMjEuNUg3QzYuMTcxNTcgMjEuNSA1LjUgMjAuODI4NCA1LjUgMjBWNEM1LjUgMy4xNzE1NyA2LjE3MTU3IDIuNSA3IDIuNVpNMyA0QzMgMS43OTA4NiA0Ljc5MDg2IDAgNyAwSDE3QzE5LjIwOTEgMCAyMSAxLjc5MDg2IDIxIDRWMjBDMjEgMjIuMjA5MSAxOS4yMDkxIDI0IDE3IDI0SDdDNC43OTA4NiAyNCAzIDIyLjIwOTEgMyAyMFY0Wk0xMSA0LjYxNTM4QzEwLjQ0NzcgNC42MTUzOCAxMCA1LjA2MzEgMTAgNS42MTUzOFY2LjM4NDYyQzEwIDYuOTM2OSAxMC40NDc3IDcuMzg0NjIgMTEgNy4zODQ2MkgxM0MxMy41NTIzIDcuMzg0NjIgMTQgNi45MzY5IDE0IDYuMzg0NjJWNS42MTUzOEMxNCA1LjA2MzEgMTMuNTUyMyA0LjYxNTM4IDEzIDQuNjE1MzhIMTFaIiBmaWxsPSIjRENCOEZGIi8+Cjwvc3ZnPgo=';\n\nfunction isVersionedTransaction(transaction) {\n    return 'version' in transaction;\n}\n\nfunction fromUint8Array(byteArray) {\n    return window.btoa(String.fromCharCode.call(null, ...byteArray));\n}\nfunction toUint8Array(base64EncodedByteArray) {\n    return new Uint8Array(window\n        .atob(base64EncodedByteArray)\n        .split('')\n        .map((c) => c.charCodeAt(0)));\n}\n\nvar _LocalSolanaMobileWalletAdapterWallet_instances, _LocalSolanaMobileWalletAdapterWallet_listeners, _LocalSolanaMobileWalletAdapterWallet_version, _LocalSolanaMobileWalletAdapterWallet_name, _LocalSolanaMobileWalletAdapterWallet_url, _LocalSolanaMobileWalletAdapterWallet_icon, _LocalSolanaMobileWalletAdapterWallet_appIdentity, _LocalSolanaMobileWalletAdapterWallet_authorization, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, _LocalSolanaMobileWalletAdapterWallet_connecting, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, _LocalSolanaMobileWalletAdapterWallet_chains, _LocalSolanaMobileWalletAdapterWallet_chainSelector, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound, _LocalSolanaMobileWalletAdapterWallet_on, _LocalSolanaMobileWalletAdapterWallet_emit, _LocalSolanaMobileWalletAdapterWallet_off, _LocalSolanaMobileWalletAdapterWallet_connect, _LocalSolanaMobileWalletAdapterWallet_performAuthorization, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, _LocalSolanaMobileWalletAdapterWallet_disconnect, _LocalSolanaMobileWalletAdapterWallet_transact, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, _LocalSolanaMobileWalletAdapterWallet_performSignTransactions, _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction, _LocalSolanaMobileWalletAdapterWallet_signTransaction, _LocalSolanaMobileWalletAdapterWallet_signMessage, _LocalSolanaMobileWalletAdapterWallet_signIn, _LocalSolanaMobileWalletAdapterWallet_performSignIn, _RemoteSolanaMobileWalletAdapterWallet_instances, _RemoteSolanaMobileWalletAdapterWallet_listeners, _RemoteSolanaMobileWalletAdapterWallet_version, _RemoteSolanaMobileWalletAdapterWallet_name, _RemoteSolanaMobileWalletAdapterWallet_url, _RemoteSolanaMobileWalletAdapterWallet_icon, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, _RemoteSolanaMobileWalletAdapterWallet_authorization, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, _RemoteSolanaMobileWalletAdapterWallet_connecting, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, _RemoteSolanaMobileWalletAdapterWallet_chains, _RemoteSolanaMobileWalletAdapterWallet_chainSelector, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound, _RemoteSolanaMobileWalletAdapterWallet_hostAuthority, _RemoteSolanaMobileWalletAdapterWallet_session, _RemoteSolanaMobileWalletAdapterWallet_on, _RemoteSolanaMobileWalletAdapterWallet_emit, _RemoteSolanaMobileWalletAdapterWallet_off, _RemoteSolanaMobileWalletAdapterWallet_connect, _RemoteSolanaMobileWalletAdapterWallet_performAuthorization, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, _RemoteSolanaMobileWalletAdapterWallet_disconnect, _RemoteSolanaMobileWalletAdapterWallet_transact, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions, _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction, _RemoteSolanaMobileWalletAdapterWallet_signTransaction, _RemoteSolanaMobileWalletAdapterWallet_signMessage, _RemoteSolanaMobileWalletAdapterWallet_signIn, _RemoteSolanaMobileWalletAdapterWallet_performSignIn;\nconst SolanaMobileWalletAdapterWalletName = 'Mobile Wallet Adapter';\nconst SIGNATURE_LENGTH_IN_BYTES = 64;\nconst DEFAULT_FEATURES = [SolanaSignAndSendTransaction, SolanaSignTransaction, SolanaSignMessage, SolanaSignIn];\nclass LocalSolanaMobileWalletAdapterWallet {\n    constructor(config) {\n        _LocalSolanaMobileWalletAdapterWallet_instances.add(this);\n        _LocalSolanaMobileWalletAdapterWallet_listeners.set(this, {});\n        _LocalSolanaMobileWalletAdapterWallet_version.set(this, '1.0.0'); // wallet-standard version\n        _LocalSolanaMobileWalletAdapterWallet_name.set(this, SolanaMobileWalletAdapterWalletName);\n        _LocalSolanaMobileWalletAdapterWallet_url.set(this, 'https://solanamobile.com/wallets');\n        _LocalSolanaMobileWalletAdapterWallet_icon.set(this, icon);\n        _LocalSolanaMobileWalletAdapterWallet_appIdentity.set(this, void 0);\n        _LocalSolanaMobileWalletAdapterWallet_authorization.set(this, void 0);\n        _LocalSolanaMobileWalletAdapterWallet_authorizationCache.set(this, void 0);\n        _LocalSolanaMobileWalletAdapterWallet_connecting.set(this, false);\n        /**\n         * Every time the connection is recycled in some way (eg. `disconnect()` is called)\n         * increment this and use it to make sure that `transact` calls from the previous\n         * 'generation' don't continue to do work and throw exceptions.\n         */\n        _LocalSolanaMobileWalletAdapterWallet_connectionGeneration.set(this, 0);\n        _LocalSolanaMobileWalletAdapterWallet_chains.set(this, []);\n        _LocalSolanaMobileWalletAdapterWallet_chainSelector.set(this, void 0);\n        _LocalSolanaMobileWalletAdapterWallet_optionalFeatures.set(this, void 0);\n        _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound.set(this, void 0);\n        _LocalSolanaMobileWalletAdapterWallet_on.set(this, (event, listener) => {\n            var _a;\n            ((_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.push(listener)) || (__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, \"f\")[event] = [listener]);\n            return () => __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, \"m\", _LocalSolanaMobileWalletAdapterWallet_off).call(this, event, listener);\n        });\n        _LocalSolanaMobileWalletAdapterWallet_connect.set(this, ({ silent } = {}) => __awaiter(this, void 0, void 0, function* () {\n            if (__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, \"f\") || this.connected) {\n                return { accounts: this.accounts };\n            }\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, true, \"f\");\n            try {\n                if (silent) {\n                    const cachedAuthorization = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").get();\n                    if (cachedAuthorization) {\n                        yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, cachedAuthorization);\n                    }\n                    else {\n                        return { accounts: this.accounts };\n                    }\n                }\n                else {\n                    yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performAuthorization, \"f\").call(this);\n                }\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n            finally {\n                __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, false, \"f\");\n            }\n            return { accounts: this.accounts };\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_performAuthorization.set(this, (signInPayload) => __awaiter(this, void 0, void 0, function* () {\n            try {\n                const cachedAuthorizationResult = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").get();\n                if (cachedAuthorizationResult) {\n                    // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n                    __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, cachedAuthorizationResult);\n                    return cachedAuthorizationResult;\n                }\n                const selectedChain = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chainSelector, \"f\").select(__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, \"f\"));\n                return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    const [capabilities, mwaAuthorizationResult] = yield Promise.all([\n                        wallet.getCapabilities(),\n                        wallet.authorize({\n                            chain: selectedChain,\n                            identity: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_appIdentity, \"f\"),\n                            sign_in_payload: signInPayload,\n                        })\n                    ]);\n                    const accounts = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, \"f\").call(this, mwaAuthorizationResult.accounts);\n                    const authorization = Object.assign(Object.assign({}, mwaAuthorizationResult), { accounts, chain: selectedChain });\n                    // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n                    Promise.all([\n                        __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, \"f\").call(this, capabilities),\n                        __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").set(authorization),\n                        __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, authorization),\n                    ]);\n                    return authorization;\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult.set(this, (authorization) => __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            const didPublicKeysChange = \n            // Case 1: We started from having no authorization.\n            __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\") == null ||\n                // Case 2: The number of authorized accounts changed.\n                ((_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\")) === null || _a === void 0 ? void 0 : _a.accounts.length) !== authorization.accounts.length ||\n                // Case 3: The new list of addresses isn't exactly the same as the old list, in the same order.\n                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\").accounts.some((account, ii) => account.address !== authorization.accounts[ii].address);\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, authorization, \"f\");\n            if (didPublicKeysChange) {\n                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, \"m\", _LocalSolanaMobileWalletAdapterWallet_emit).call(this, 'change', { accounts: this.accounts });\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult.set(this, (capabilities) => __awaiter(this, void 0, void 0, function* () {\n            // TODO: investigate why using SolanaSignTransactions constant breaks treeshaking\n            const supportsSignTransaction = capabilities.features.includes('solana:signTransactions'); //SolanaSignTransactions);\n            const supportsSignAndSendTransaction = capabilities.supports_sign_and_send_transactions;\n            const didCapabilitiesChange = SolanaSignAndSendTransaction in this.features !== supportsSignAndSendTransaction ||\n                SolanaSignTransaction in this.features !== supportsSignTransaction;\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, Object.assign(Object.assign({}, ((supportsSignAndSendTransaction || (!supportsSignAndSendTransaction && !supportsSignTransaction)) && {\n                [SolanaSignAndSendTransaction]: {\n                    version: '1.0.0',\n                    supportedTransactionVersions: ['legacy', 0],\n                    signAndSendTransaction: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction, \"f\"),\n                },\n            })), (supportsSignTransaction && {\n                [SolanaSignTransaction]: {\n                    version: '1.0.0',\n                    supportedTransactionVersions: ['legacy', 0],\n                    signTransaction: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signTransaction, \"f\"),\n                },\n            })), \"f\");\n            if (didCapabilitiesChange) {\n                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, \"m\", _LocalSolanaMobileWalletAdapterWallet_emit).call(this, 'change', { features: this.features });\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_performReauthorization.set(this, (wallet, authToken, chain) => __awaiter(this, void 0, void 0, function* () {\n            try {\n                const mwaAuthorizationResult = yield wallet.authorize({\n                    auth_token: authToken,\n                    identity: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_appIdentity, \"f\"),\n                    chain: chain\n                });\n                const accounts = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, \"f\").call(this, mwaAuthorizationResult.accounts);\n                const authorization = Object.assign(Object.assign({}, mwaAuthorizationResult), { accounts: accounts, chain: chain });\n                // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n                Promise.all([\n                    __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").set(authorization),\n                    __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, authorization),\n                ]);\n            }\n            catch (e) {\n                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_disconnect, \"f\").call(this);\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_disconnect.set(this, () => __awaiter(this, void 0, void 0, function* () {\n            var _b;\n            __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").clear(); // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, false, \"f\");\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, (_b = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, \"f\"), _b++, _b), \"f\");\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, undefined, \"f\");\n            __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, \"m\", _LocalSolanaMobileWalletAdapterWallet_emit).call(this, 'change', { accounts: this.accounts });\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_transact.set(this, (callback) => __awaiter(this, void 0, void 0, function* () {\n            var _c;\n            const walletUriBase = (_c = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\")) === null || _c === void 0 ? void 0 : _c.wallet_uri_base;\n            const config = walletUriBase ? { baseUri: walletUriBase } : undefined;\n            const currentConnectionGeneration = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, \"f\");\n            try {\n                return yield transact(callback, config);\n            }\n            catch (e) {\n                if (__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, \"f\") !== currentConnectionGeneration) {\n                    yield new Promise(() => { }); // Never resolve.\n                }\n                if (e instanceof Error &&\n                    e.name === 'SolanaMobileWalletAdapterError' &&\n                    e.code === 'ERROR_WALLET_NOT_FOUND') {\n                    yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound, \"f\").call(this, this);\n                }\n                throw e;\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized.set(this, () => {\n            if (!__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\"))\n                throw new Error('Wallet not connected');\n            return { authToken: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\").auth_token, chain: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\").chain };\n        });\n        _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts.set(this, (accounts) => {\n            return accounts.map((account) => {\n                var _a, _b;\n                const publicKey = toUint8Array(account.address);\n                return {\n                    address: base58.encode(publicKey),\n                    publicKey,\n                    label: account.label,\n                    icon: account.icon,\n                    chains: (_a = account.chains) !== null && _a !== void 0 ? _a : __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, \"f\"),\n                    // TODO: get supported features from getCapabilities API \n                    features: (_b = account.features) !== null && _b !== void 0 ? _b : DEFAULT_FEATURES\n                };\n            });\n        });\n        _LocalSolanaMobileWalletAdapterWallet_performSignTransactions.set(this, (transactions) => __awaiter(this, void 0, void 0, function* () {\n            const { authToken, chain } = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, \"f\").call(this);\n            try {\n                return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, \"f\").call(this, wallet, authToken, chain);\n                    const signedTransactions = yield wallet.signTransactions({\n                        transactions,\n                    });\n                    return signedTransactions;\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction.set(this, (transaction, options) => __awaiter(this, void 0, void 0, function* () {\n            const { authToken, chain } = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, \"f\").call(this);\n            try {\n                return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    const [capabilities, _1] = yield Promise.all([\n                        wallet.getCapabilities(),\n                        __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, \"f\").call(this, wallet, authToken, chain)\n                    ]);\n                    if (capabilities.supports_sign_and_send_transactions) {\n                        const signatures = yield wallet.signAndSendTransactions(Object.assign(Object.assign({}, options), { transactions: [transaction] }));\n                        return signatures[0];\n                    }\n                    else {\n                        throw new Error('connected wallet does not support signAndSendTransaction');\n                    }\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const outputs = [];\n            for (const input of inputs) {\n                const transaction = VersionedTransaction.deserialize(input.transaction);\n                const signature = (yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, \"f\").call(this, transaction, input.options));\n                outputs.push({ signature: base58.decode(signature) });\n            }\n            return outputs;\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_signTransaction.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const transactions = inputs.map(({ transaction }) => VersionedTransaction.deserialize(transaction));\n            const signedTransactions = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignTransactions, \"f\").call(this, transactions);\n            return signedTransactions.map((signedTransaction) => {\n                const serializedTransaction = isVersionedTransaction(signedTransaction)\n                    ? signedTransaction.serialize()\n                    : new Uint8Array(signedTransaction.serialize({\n                        requireAllSignatures: false,\n                        verifySignatures: false,\n                    }));\n                return { signedTransaction: serializedTransaction };\n            });\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_signMessage.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const { authToken, chain } = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, \"f\").call(this);\n            const addresses = inputs.map(({ account }) => fromUint8Array(account.publicKey));\n            const messages = inputs.map(({ message }) => message);\n            try {\n                return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, \"f\").call(this, wallet, authToken, chain);\n                    const signedMessages = yield wallet.signMessages({\n                        addresses: addresses,\n                        payloads: messages,\n                    });\n                    return signedMessages.map((signedMessage) => {\n                        return { signedMessage: signedMessage, signature: signedMessage.slice(-SIGNATURE_LENGTH_IN_BYTES) };\n                    });\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_signIn.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const outputs = [];\n            if (inputs.length > 1) {\n                for (const input of inputs) {\n                    outputs.push(yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignIn, \"f\").call(this, input));\n                }\n            }\n            else {\n                return [yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignIn, \"f\").call(this, inputs[0])];\n            }\n            return outputs;\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_performSignIn.set(this, (input) => __awaiter(this, void 0, void 0, function* () {\n            var _d, _e;\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, true, \"f\");\n            try {\n                const authorizationResult = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performAuthorization, \"f\").call(this, Object.assign(Object.assign({}, input), { domain: (_d = input === null || input === void 0 ? void 0 : input.domain) !== null && _d !== void 0 ? _d : window.location.host }));\n                if (!authorizationResult.sign_in_result) {\n                    throw new Error(\"Sign in failed, no sign in result returned by wallet\");\n                }\n                const signedInAddress = authorizationResult.sign_in_result.address;\n                const signedInAccount = Object.assign(Object.assign({}, (_e = authorizationResult.accounts.find(acc => acc.address == signedInAddress)) !== null && _e !== void 0 ? _e : {\n                    address: signedInAddress\n                }), { publicKey: toUint8Array(signedInAddress) });\n                return {\n                    account: signedInAccount,\n                    signedMessage: toUint8Array(authorizationResult.sign_in_result.signed_message),\n                    signature: toUint8Array(authorizationResult.sign_in_result.signature)\n                };\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n            finally {\n                __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, false, \"f\");\n            }\n        }));\n        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, config.authorizationCache, \"f\");\n        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_appIdentity, config.appIdentity, \"f\");\n        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, config.chains, \"f\");\n        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_chainSelector, config.chainSelector, \"f\");\n        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound, config.onWalletNotFound, \"f\");\n        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, {\n            // We are forced to provide either SolanaSignAndSendTransaction or SolanaSignTransaction\n            // because the wallet-adapter compatible wallet-standard wallet requires at least one of them.\n            // MWA 2.0+ wallets must implement signAndSend and pre 2.0 wallets have always provided it so \n            // this is a safe assumption. We later update the features after we get the wallets capabilities. \n            [SolanaSignAndSendTransaction]: {\n                version: '1.0.0',\n                supportedTransactionVersions: ['legacy', 0],\n                signAndSendTransaction: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction, \"f\"),\n            },\n        }, \"f\");\n    }\n    get version() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_version, \"f\");\n    }\n    get name() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_name, \"f\");\n    }\n    get url() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_url, \"f\");\n    }\n    get icon() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_icon, \"f\");\n    }\n    get chains() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, \"f\");\n    }\n    get features() {\n        return Object.assign({ [StandardConnect]: {\n                version: '1.0.0',\n                connect: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connect, \"f\"),\n            }, [StandardDisconnect]: {\n                version: '1.0.0',\n                disconnect: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_disconnect, \"f\"),\n            }, [StandardEvents]: {\n                version: '1.0.0',\n                on: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_on, \"f\"),\n            }, [SolanaSignMessage]: {\n                version: '1.0.0',\n                signMessage: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signMessage, \"f\"),\n            }, [SolanaSignIn]: {\n                version: '1.0.0',\n                signIn: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signIn, \"f\"),\n            } }, __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, \"f\"));\n    }\n    get accounts() {\n        var _a, _b;\n        return (_b = (_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\")) === null || _a === void 0 ? void 0 : _a.accounts) !== null && _b !== void 0 ? _b : [];\n    }\n    get connected() {\n        return !!__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\");\n    }\n    get isAuthorized() {\n        return !!__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\");\n    }\n    get currentAuthorization() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\");\n    }\n    get cachedAuthorizationResult() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").get();\n    }\n}\n_LocalSolanaMobileWalletAdapterWallet_listeners = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_version = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_name = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_url = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_icon = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_appIdentity = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_authorization = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_authorizationCache = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_connecting = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_connectionGeneration = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_chains = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_chainSelector = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_optionalFeatures = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_on = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_connect = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performAuthorization = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performReauthorization = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_disconnect = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_transact = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performSignTransactions = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signTransaction = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signMessage = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signIn = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performSignIn = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_instances = new WeakSet(), _LocalSolanaMobileWalletAdapterWallet_emit = function _LocalSolanaMobileWalletAdapterWallet_emit(event, ...args) {\n    var _a;\n    // eslint-disable-next-line prefer-spread\n    (_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.forEach((listener) => listener.apply(null, args));\n}, _LocalSolanaMobileWalletAdapterWallet_off = function _LocalSolanaMobileWalletAdapterWallet_off(event, listener) {\n    var _a;\n    __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, \"f\")[event] = (_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.filter((existingListener) => listener !== existingListener);\n};\nclass RemoteSolanaMobileWalletAdapterWallet {\n    constructor(config) {\n        _RemoteSolanaMobileWalletAdapterWallet_instances.add(this);\n        _RemoteSolanaMobileWalletAdapterWallet_listeners.set(this, {});\n        _RemoteSolanaMobileWalletAdapterWallet_version.set(this, '1.0.0'); // wallet-standard version\n        _RemoteSolanaMobileWalletAdapterWallet_name.set(this, SolanaMobileWalletAdapterWalletName);\n        _RemoteSolanaMobileWalletAdapterWallet_url.set(this, 'https://solanamobile.com/wallets');\n        _RemoteSolanaMobileWalletAdapterWallet_icon.set(this, icon);\n        _RemoteSolanaMobileWalletAdapterWallet_appIdentity.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_authorization.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_authorizationCache.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_connecting.set(this, false);\n        /**\n         * Every time the connection is recycled in some way (eg. `disconnect()` is called)\n         * increment this and use it to make sure that `transact` calls from the previous\n         * 'generation' don't continue to do work and throw exceptions.\n         */\n        _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration.set(this, 0);\n        _RemoteSolanaMobileWalletAdapterWallet_chains.set(this, []);\n        _RemoteSolanaMobileWalletAdapterWallet_chainSelector.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_hostAuthority.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_session.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_on.set(this, (event, listener) => {\n            var _a;\n            ((_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.push(listener)) || (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, \"f\")[event] = [listener]);\n            return () => __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, \"m\", _RemoteSolanaMobileWalletAdapterWallet_off).call(this, event, listener);\n        });\n        _RemoteSolanaMobileWalletAdapterWallet_connect.set(this, ({ silent } = {}) => __awaiter(this, void 0, void 0, function* () {\n            if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, \"f\") || this.connected) {\n                return { accounts: this.accounts };\n            }\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, true, \"f\");\n            try {\n                yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performAuthorization, \"f\").call(this);\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n            finally {\n                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, false, \"f\");\n            }\n            return { accounts: this.accounts };\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_performAuthorization.set(this, (signInPayload) => __awaiter(this, void 0, void 0, function* () {\n            try {\n                const cachedAuthorizationResult = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").get();\n                if (cachedAuthorizationResult) {\n                    // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n                    __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, cachedAuthorizationResult);\n                    return cachedAuthorizationResult;\n                }\n                if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, \"f\"))\n                    __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, undefined, \"f\");\n                const selectedChain = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chainSelector, \"f\").select(__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, \"f\"));\n                return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    const [capabilities, mwaAuthorizationResult] = yield Promise.all([\n                        wallet.getCapabilities(),\n                        wallet.authorize({\n                            chain: selectedChain,\n                            identity: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, \"f\"),\n                            sign_in_payload: signInPayload,\n                        })\n                    ]);\n                    const accounts = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, \"f\").call(this, mwaAuthorizationResult.accounts);\n                    const authorizationResult = Object.assign(Object.assign({}, mwaAuthorizationResult), { accounts, chain: selectedChain });\n                    // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n                    Promise.all([\n                        __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, \"f\").call(this, capabilities),\n                        __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").set(authorizationResult),\n                        __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, authorizationResult),\n                    ]);\n                    return authorizationResult;\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult.set(this, (authorization) => __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            const didPublicKeysChange = \n            // Case 1: We started from having no authorization.\n            __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\") == null ||\n                // Case 2: The number of authorized accounts changed.\n                ((_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\")) === null || _a === void 0 ? void 0 : _a.accounts.length) !== authorization.accounts.length ||\n                // Case 3: The new list of addresses isn't exactly the same as the old list, in the same order.\n                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\").accounts.some((account, ii) => account.address !== authorization.accounts[ii].address);\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, authorization, \"f\");\n            if (didPublicKeysChange) {\n                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, \"m\", _RemoteSolanaMobileWalletAdapterWallet_emit).call(this, 'change', { accounts: this.accounts });\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult.set(this, (capabilities) => __awaiter(this, void 0, void 0, function* () {\n            // TODO: investigate why using SolanaSignTransactions constant breaks treeshaking\n            const supportsSignTransaction = capabilities.features.includes('solana:signTransactions'); //SolanaSignTransactions);\n            const supportsSignAndSendTransaction = capabilities.supports_sign_and_send_transactions ||\n                capabilities.features.includes('solana:signAndSendTransaction');\n            const didCapabilitiesChange = SolanaSignAndSendTransaction in this.features !== supportsSignAndSendTransaction ||\n                SolanaSignTransaction in this.features !== supportsSignTransaction;\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, Object.assign(Object.assign({}, (supportsSignAndSendTransaction && {\n                [SolanaSignAndSendTransaction]: {\n                    version: '1.0.0',\n                    supportedTransactionVersions: capabilities.supported_transaction_versions,\n                    signAndSendTransaction: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction, \"f\"),\n                },\n            })), (supportsSignTransaction && {\n                [SolanaSignTransaction]: {\n                    version: '1.0.0',\n                    supportedTransactionVersions: capabilities.supported_transaction_versions,\n                    signTransaction: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signTransaction, \"f\"),\n                },\n            })), \"f\");\n            if (didCapabilitiesChange) {\n                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, \"m\", _RemoteSolanaMobileWalletAdapterWallet_emit).call(this, 'change', { features: this.features });\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_performReauthorization.set(this, (wallet, authToken, chain) => __awaiter(this, void 0, void 0, function* () {\n            try {\n                const mwaAuthorizationResult = yield wallet.authorize({\n                    auth_token: authToken,\n                    identity: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, \"f\"),\n                });\n                const accounts = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, \"f\").call(this, mwaAuthorizationResult.accounts);\n                const authorization = Object.assign(Object.assign({}, mwaAuthorizationResult), { accounts: accounts, chain: chain });\n                // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n                Promise.all([\n                    __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").set(authorization),\n                    __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, authorization),\n                ]);\n            }\n            catch (e) {\n                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_disconnect, \"f\").call(this);\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_disconnect.set(this, () => __awaiter(this, void 0, void 0, function* () {\n            var _b;\n            var _c;\n            (_b = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, \"f\")) === null || _b === void 0 ? void 0 : _b.close();\n            __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").clear(); // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, false, \"f\");\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, (_c = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, \"f\"), _c++, _c), \"f\");\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, undefined, \"f\");\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, undefined, \"f\");\n            __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, \"m\", _RemoteSolanaMobileWalletAdapterWallet_emit).call(this, 'change', { accounts: this.accounts });\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_transact.set(this, (callback) => __awaiter(this, void 0, void 0, function* () {\n            var _d;\n            const walletUriBase = (_d = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\")) === null || _d === void 0 ? void 0 : _d.wallet_uri_base;\n            const baseConfig = walletUriBase ? { baseUri: walletUriBase } : undefined;\n            const remoteConfig = Object.assign(Object.assign({}, baseConfig), { remoteHostAuthority: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_hostAuthority, \"f\") });\n            const currentConnectionGeneration = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, \"f\");\n            const modal = new RemoteConnectionModal();\n            if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, \"f\")) {\n                return callback(__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, \"f\").wallet);\n            }\n            try {\n                const { associationUrl, close, wallet } = yield startRemoteScenario(remoteConfig);\n                const removeCloseListener = modal.addEventListener('close', (event) => {\n                    if (event)\n                        close();\n                });\n                modal.initWithQR(associationUrl.toString());\n                modal.open();\n                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, { close, wallet: yield wallet }, \"f\");\n                removeCloseListener();\n                modal.close();\n                return yield callback(__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, \"f\").wallet);\n            }\n            catch (e) {\n                modal.close();\n                if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, \"f\") !== currentConnectionGeneration) {\n                    yield new Promise(() => { }); // Never resolve.\n                }\n                if (e instanceof Error &&\n                    e.name === 'SolanaMobileWalletAdapterError' &&\n                    e.code === 'ERROR_WALLET_NOT_FOUND') {\n                    yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound, \"f\").call(this, this);\n                }\n                throw e;\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized.set(this, () => {\n            if (!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\"))\n                throw new Error('Wallet not connected');\n            return { authToken: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\").auth_token, chain: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\").chain };\n        });\n        _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts.set(this, (accounts) => {\n            return accounts.map((account) => {\n                var _a, _b;\n                const publicKey = toUint8Array(account.address);\n                return {\n                    address: base58.encode(publicKey),\n                    publicKey,\n                    label: account.label,\n                    icon: account.icon,\n                    chains: (_a = account.chains) !== null && _a !== void 0 ? _a : __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, \"f\"),\n                    // TODO: get supported features from getCapabilities API \n                    features: (_b = account.features) !== null && _b !== void 0 ? _b : DEFAULT_FEATURES\n                };\n            });\n        });\n        _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions.set(this, (transactions) => __awaiter(this, void 0, void 0, function* () {\n            const { authToken, chain } = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, \"f\").call(this);\n            try {\n                return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, \"f\").call(this, wallet, authToken, chain);\n                    const signedTransactions = yield wallet.signTransactions({\n                        transactions,\n                    });\n                    return signedTransactions;\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction.set(this, (transaction, options) => __awaiter(this, void 0, void 0, function* () {\n            const { authToken, chain } = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, \"f\").call(this);\n            try {\n                return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    const [capabilities, _1] = yield Promise.all([\n                        wallet.getCapabilities(),\n                        __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, \"f\").call(this, wallet, authToken, chain)\n                    ]);\n                    if (capabilities.supports_sign_and_send_transactions) {\n                        const signatures = yield wallet.signAndSendTransactions(Object.assign(Object.assign({}, options), { transactions: [transaction] }));\n                        return signatures[0];\n                    }\n                    else {\n                        throw new Error('connected wallet does not support signAndSendTransaction');\n                    }\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const outputs = [];\n            for (const input of inputs) {\n                const transaction = VersionedTransaction.deserialize(input.transaction);\n                const signature = (yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, \"f\").call(this, transaction, input.options));\n                outputs.push({ signature: base58.decode(signature) });\n            }\n            return outputs;\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_signTransaction.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const transactions = inputs.map(({ transaction }) => VersionedTransaction.deserialize(transaction));\n            const signedTransactions = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions, \"f\").call(this, transactions);\n            return signedTransactions.map((signedTransaction) => {\n                const serializedTransaction = isVersionedTransaction(signedTransaction)\n                    ? signedTransaction.serialize()\n                    : new Uint8Array(signedTransaction.serialize({\n                        requireAllSignatures: false,\n                        verifySignatures: false,\n                    }));\n                return { signedTransaction: serializedTransaction };\n            });\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_signMessage.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const { authToken, chain } = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, \"f\").call(this);\n            const addresses = inputs.map(({ account }) => fromUint8Array(account.publicKey));\n            const messages = inputs.map(({ message }) => message);\n            try {\n                return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, \"f\").call(this, wallet, authToken, chain);\n                    const signedMessages = yield wallet.signMessages({\n                        addresses: addresses,\n                        payloads: messages,\n                    });\n                    return signedMessages.map((signedMessage) => {\n                        return { signedMessage: signedMessage, signature: signedMessage.slice(-SIGNATURE_LENGTH_IN_BYTES) };\n                    });\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_signIn.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const outputs = [];\n            if (inputs.length > 1) {\n                for (const input of inputs) {\n                    outputs.push(yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignIn, \"f\").call(this, input));\n                }\n            }\n            else {\n                return [yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignIn, \"f\").call(this, inputs[0])];\n            }\n            return outputs;\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_performSignIn.set(this, (input) => __awaiter(this, void 0, void 0, function* () {\n            var _e, _f;\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, true, \"f\");\n            try {\n                const authorizationResult = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performAuthorization, \"f\").call(this, Object.assign(Object.assign({}, input), { domain: (_e = input === null || input === void 0 ? void 0 : input.domain) !== null && _e !== void 0 ? _e : window.location.host }));\n                if (!authorizationResult.sign_in_result) {\n                    throw new Error(\"Sign in failed, no sign in result returned by wallet\");\n                }\n                const signedInAddress = authorizationResult.sign_in_result.address;\n                const signedInAccount = Object.assign(Object.assign({}, (_f = authorizationResult.accounts.find(acc => acc.address == signedInAddress)) !== null && _f !== void 0 ? _f : {\n                    address: signedInAddress\n                }), { publicKey: toUint8Array(signedInAddress) });\n                return {\n                    account: signedInAccount,\n                    signedMessage: toUint8Array(authorizationResult.sign_in_result.signed_message),\n                    signature: toUint8Array(authorizationResult.sign_in_result.signature)\n                };\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n            finally {\n                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, false, \"f\");\n            }\n        }));\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, config.authorizationCache, \"f\");\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, config.appIdentity, \"f\");\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, config.chains, \"f\");\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chainSelector, config.chainSelector, \"f\");\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_hostAuthority, config.remoteHostAuthority, \"f\");\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound, config.onWalletNotFound, \"f\");\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, {\n            // We are forced to provide either SolanaSignAndSendTransaction or SolanaSignTransaction\n            // because the wallet-adapter compatible wallet-standard wallet requires at least one of them.\n            // MWA 2.0+ wallets must implement signAndSend and pre 2.0 wallets have always provided it so \n            // this is a safe assumption. We later update the features after we get the wallets capabilities. \n            [SolanaSignAndSendTransaction]: {\n                version: '1.0.0',\n                supportedTransactionVersions: ['legacy', 0],\n                signAndSendTransaction: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction, \"f\"),\n            },\n        }, \"f\");\n    }\n    get version() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_version, \"f\");\n    }\n    get name() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_name, \"f\");\n    }\n    get url() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_url, \"f\");\n    }\n    get icon() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_icon, \"f\");\n    }\n    get chains() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, \"f\");\n    }\n    get features() {\n        return Object.assign({ [StandardConnect]: {\n                version: '1.0.0',\n                connect: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connect, \"f\"),\n            }, [StandardDisconnect]: {\n                version: '1.0.0',\n                disconnect: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_disconnect, \"f\"),\n            }, [StandardEvents]: {\n                version: '1.0.0',\n                on: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_on, \"f\"),\n            }, [SolanaSignMessage]: {\n                version: '1.0.0',\n                signMessage: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signMessage, \"f\"),\n            }, [SolanaSignIn]: {\n                version: '1.0.0',\n                signIn: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signIn, \"f\"),\n            } }, __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, \"f\"));\n    }\n    get accounts() {\n        var _a, _b;\n        return (_b = (_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\")) === null || _a === void 0 ? void 0 : _a.accounts) !== null && _b !== void 0 ? _b : [];\n    }\n    get connected() {\n        return !!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, \"f\") && !!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\");\n    }\n    get isAuthorized() {\n        return !!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\");\n    }\n    get currentAuthorization() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\");\n    }\n    get cachedAuthorizationResult() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").get();\n    }\n}\n_RemoteSolanaMobileWalletAdapterWallet_listeners = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_version = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_name = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_url = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_icon = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_appIdentity = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_authorization = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_authorizationCache = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_connecting = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_chains = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_chainSelector = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_hostAuthority = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_session = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_on = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_connect = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performAuthorization = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performReauthorization = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_disconnect = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_transact = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signTransaction = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signMessage = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signIn = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performSignIn = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_instances = new WeakSet(), _RemoteSolanaMobileWalletAdapterWallet_emit = function _RemoteSolanaMobileWalletAdapterWallet_emit(event, ...args) {\n    var _a;\n    // eslint-disable-next-line prefer-spread\n    (_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.forEach((listener) => listener.apply(null, args));\n}, _RemoteSolanaMobileWalletAdapterWallet_off = function _RemoteSolanaMobileWalletAdapterWallet_off(event, listener) {\n    var _a;\n    __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, \"f\")[event] = (_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.filter((existingListener) => listener !== existingListener);\n};\n\nvar __classPrivateFieldSet = (undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _RegisterWalletEvent_detail;\n/**\n * Register a {@link \"@wallet-standard/base\".Wallet} as a Standard Wallet with the app.\n *\n * This dispatches a {@link \"@wallet-standard/base\".WindowRegisterWalletEvent} to notify the app that the Wallet is\n * ready to be registered.\n *\n * This also adds a listener for {@link \"@wallet-standard/base\".WindowAppReadyEvent} to listen for a notification from\n * the app that the app is ready to register the Wallet.\n *\n * This combination of event dispatch and listener guarantees that the Wallet will be registered synchronously as soon\n * as the app is ready whether the Wallet loads before or after the app.\n *\n * @param wallet Wallet to register.\n *\n * @group Wallet\n */\nfunction registerWallet(wallet) {\n    const callback = ({ register }) => register(wallet);\n    try {\n        window.dispatchEvent(new RegisterWalletEvent(callback));\n    }\n    catch (error) {\n        console.error('wallet-standard:register-wallet event could not be dispatched\\n', error);\n    }\n    try {\n        window.addEventListener('wallet-standard:app-ready', ({ detail: api }) => callback(api));\n    }\n    catch (error) {\n        console.error('wallet-standard:app-ready event listener could not be added\\n', error);\n    }\n}\nclass RegisterWalletEvent extends Event {\n    constructor(callback) {\n        super('wallet-standard:register-wallet', {\n            bubbles: false,\n            cancelable: false,\n            composed: false,\n        });\n        _RegisterWalletEvent_detail.set(this, void 0);\n        __classPrivateFieldSet(this, _RegisterWalletEvent_detail, callback, \"f\");\n    }\n    get detail() {\n        return __classPrivateFieldGet(this, _RegisterWalletEvent_detail, \"f\");\n    }\n    get type() {\n        return 'wallet-standard:register-wallet';\n    }\n    /** @deprecated */\n    preventDefault() {\n        throw new Error('preventDefault cannot be called');\n    }\n    /** @deprecated */\n    stopImmediatePropagation() {\n        throw new Error('stopImmediatePropagation cannot be called');\n    }\n    /** @deprecated */\n    stopPropagation() {\n        throw new Error('stopPropagation cannot be called');\n    }\n}\n_RegisterWalletEvent_detail = new WeakMap();\n\n(undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\n(undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\n\nfunction getIsLocalAssociationSupported() {\n    return (typeof window !== 'undefined' &&\n        window.isSecureContext &&\n        typeof document !== 'undefined' &&\n        /android/i.test(navigator.userAgent));\n}\nfunction getIsRemoteAssociationSupported() {\n    return (typeof window !== 'undefined' &&\n        window.isSecureContext &&\n        typeof document !== 'undefined' &&\n        !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));\n}\n\nfunction registerMwa(config) {\n    if (getIsLocalAssociationSupported()) {\n        registerWallet(new LocalSolanaMobileWalletAdapterWallet(config));\n    }\n    else if (getIsRemoteAssociationSupported() && config.remoteHostAuthority !== undefined) {\n        registerWallet(new RemoteSolanaMobileWalletAdapterWallet(Object.assign(Object.assign({}, config), { remoteHostAuthority: config.remoteHostAuthority })));\n    }\n    else ;\n}\n\nconst WALLET_NOT_FOUND_ERROR_MESSAGE = 'To use mobile wallet adapter, you must have a compatible mobile wallet application installed on your device.';\nconst BROWSER_NOT_SUPPORTED_ERROR_MESSAGE = 'This browser appears to be incompatible with mobile wallet adapter. Open this page in a compatible mobile browser app and try again.';\nclass ErrorModal extends EmbeddedModal {\n    constructor() {\n        super(...arguments);\n        this.contentStyles = css;\n        this.contentHtml = ErrorDialogHtml;\n    }\n    initWithError(error) {\n        super.init();\n        this.populateError(error);\n    }\n    populateError(error) {\n        var _a, _b;\n        const errorMessageElement = (_a = this.dom) === null || _a === void 0 ? void 0 : _a.getElementById('mobile-wallet-adapter-error-message');\n        const actionBtn = (_b = this.dom) === null || _b === void 0 ? void 0 : _b.getElementById('mobile-wallet-adapter-error-action');\n        if (errorMessageElement) {\n            if (error.name === 'SolanaMobileWalletAdapterError') {\n                switch (error.code) {\n                    case 'ERROR_WALLET_NOT_FOUND':\n                        errorMessageElement.innerHTML = WALLET_NOT_FOUND_ERROR_MESSAGE;\n                        if (actionBtn)\n                            actionBtn.addEventListener('click', () => {\n                                window.location.href = 'https://solanamobile.com/wallets';\n                            });\n                        return;\n                    case 'ERROR_BROWSER_NOT_SUPPORTED':\n                        errorMessageElement.innerHTML = BROWSER_NOT_SUPPORTED_ERROR_MESSAGE;\n                        if (actionBtn)\n                            actionBtn.style.display = 'none';\n                        return;\n                }\n            }\n            errorMessageElement.innerHTML = `An unexpected error occurred: ${error.message}`;\n        }\n        else {\n            console.log('Failed to locate error dialog element');\n        }\n    }\n}\nconst ErrorDialogHtml = `\n<svg class=\"mobile-wallet-adapter-embedded-modal-error-icon\" xmlns=\"http://www.w3.org/2000/svg\" height=\"50px\" viewBox=\"0 -960 960 960\" width=\"50px\" fill=\"#000000\"><path d=\"M 280,-80 Q 197,-80 138.5,-138.5 80,-197 80,-280 80,-363 138.5,-421.5 197,-480 280,-480 q 83,0 141.5,58.5 58.5,58.5 58.5,141.5 0,83 -58.5,141.5 Q 363,-80 280,-80 Z M 824,-120 568,-376 Q 556,-389 542.5,-402.5 529,-416 516,-428 q 38,-24 61,-64 23,-40 23,-88 0,-75 -52.5,-127.5 Q 495,-760 420,-760 345,-760 292.5,-707.5 240,-655 240,-580 q 0,6 0.5,11.5 0.5,5.5 1.5,11.5 -18,2 -39.5,8 -21.5,6 -38.5,14 -2,-11 -3,-22 -1,-11 -1,-23 0,-109 75.5,-184.5 Q 311,-840 420,-840 q 109,0 184.5,75.5 75.5,75.5 75.5,184.5 0,43 -13.5,81.5 Q 653,-460 629,-428 l 251,252 z m -615,-61 71,-71 70,71 29,-28 -71,-71 71,-71 -28,-28 -71,71 -71,-71 -28,28 71,71 -71,71 z\"/></svg>\n<div class=\"mobile-wallet-adapter-embedded-modal-title\">We can't find a wallet.</div>\n<div id=\"mobile-wallet-adapter-error-message\" class=\"mobile-wallet-adapter-embedded-modal-subtitle\"></div>\n<div>\n    <button data-error-action id=\"mobile-wallet-adapter-error-action\" class=\"mobile-wallet-adapter-embedded-modal-error-action\">\n        Find a wallet\n    </button>\n</div>\n`;\nconst css = `\n.mobile-wallet-adapter-embedded-modal-content {\n    text-align: center;\n}\n\n.mobile-wallet-adapter-embedded-modal-error-icon {\n    margin-top: 24px;\n}\n\n.mobile-wallet-adapter-embedded-modal-title {\n    margin: 18px 100px auto 100px;\n    color: #000000;\n    font-size: 2.75em;\n    font-weight: 600;\n}\n\n.mobile-wallet-adapter-embedded-modal-subtitle {\n    margin: 30px 60px 40px 60px;\n    color: #000000;\n    font-size: 1.25em;\n    font-weight: 400;\n}\n\n.mobile-wallet-adapter-embedded-modal-error-action {\n    display: block;\n    width: 100%;\n    height: 56px;\n    /*margin-top: 40px;*/\n    font-size: 1.25em;\n    /*line-height: 24px;*/\n    /*letter-spacing: -1%;*/\n    background: #000000;\n    color: #FFFFFF;\n    border-radius: 18px;\n}\n\n/* Smaller screens */\n@media all and (max-width: 600px) {\n    .mobile-wallet-adapter-embedded-modal-title {\n        font-size: 1.5em;\n        margin-right: 12px;\n        margin-left: 12px;\n    }\n    .mobile-wallet-adapter-embedded-modal-subtitle {\n        margin-right: 12px;\n        margin-left: 12px;\n    }\n}\n`;\n\nfunction defaultErrorModalWalletNotFoundHandler() {\n    return __awaiter(this, void 0, void 0, function* () {\n        if (typeof window !== 'undefined') {\n            const userAgent = window.navigator.userAgent.toLowerCase();\n            const errorDialog = new ErrorModal();\n            if (userAgent.includes('wv')) { // Android WebView\n                // MWA is not supported in this browser so we inform the user\n                // errorDialog.initWithError(\n                //     new SolanaMobileWalletAdapterError(\n                //         SolanaMobileWalletAdapterErrorCode.ERROR_BROWSER_NOT_SUPPORTED, \n                //         ''\n                //     )\n                // );\n                // TODO: investigate why instantiating a new SolanaMobileWalletAdapterError here breaks treeshaking \n                errorDialog.initWithError({\n                    name: 'SolanaMobileWalletAdapterError',\n                    code: 'ERROR_BROWSER_NOT_SUPPORTED',\n                    message: ''\n                });\n            }\n            else { // Browser, user does not have a wallet installed.\n                // errorDialog.initWithError(\n                //     new SolanaMobileWalletAdapterError(\n                //         SolanaMobileWalletAdapterErrorCode.ERROR_WALLET_NOT_FOUND, \n                //         ''\n                //     )\n                // );\n                // TODO: investigate why instantiating a new SolanaMobileWalletAdapterError here breaks treeshaking \n                errorDialog.initWithError({\n                    name: 'SolanaMobileWalletAdapterError',\n                    code: 'ERROR_WALLET_NOT_FOUND',\n                    message: ''\n                });\n            }\n            errorDialog.open();\n        }\n    });\n}\nfunction createDefaultWalletNotFoundHandler() {\n    return () => __awaiter(this, void 0, void 0, function* () { defaultErrorModalWalletNotFoundHandler(); });\n}\n\nconst CACHE_KEY = 'SolanaMobileWalletAdapterDefaultAuthorizationCache';\nfunction createDefaultAuthorizationCache() {\n    let storage;\n    try {\n        storage = window.localStorage;\n        // eslint-disable-next-line no-empty\n    }\n    catch (_a) { }\n    return {\n        clear() {\n            return __awaiter(this, void 0, void 0, function* () {\n                if (!storage) {\n                    return;\n                }\n                try {\n                    storage.removeItem(CACHE_KEY);\n                    // eslint-disable-next-line no-empty\n                }\n                catch (_a) { }\n            });\n        },\n        get() {\n            return __awaiter(this, void 0, void 0, function* () {\n                if (!storage) {\n                    return;\n                }\n                try {\n                    const parsed = JSON.parse(storage.getItem(CACHE_KEY));\n                    if (parsed && parsed.accounts) {\n                        const parsedAccounts = parsed.accounts.map((account) => {\n                            return Object.assign(Object.assign({}, account), { publicKey: 'publicKey' in account\n                                    ? new Uint8Array(Object.values(account.publicKey)) // Rebuild publicKey for WalletAccount\n                                    : new PublicKey(account.address).toBytes() });\n                        });\n                        return Object.assign(Object.assign({}, parsed), { accounts: parsedAccounts });\n                    }\n                    else\n                        return parsed || undefined;\n                    // eslint-disable-next-line no-empty\n                }\n                catch (_a) { }\n            });\n        },\n        set(authorizationResult) {\n            return __awaiter(this, void 0, void 0, function* () {\n                if (!storage) {\n                    return;\n                }\n                try {\n                    storage.setItem(CACHE_KEY, JSON.stringify(authorizationResult));\n                    // eslint-disable-next-line no-empty\n                }\n                catch (_a) { }\n            });\n        },\n    };\n}\n\nfunction createDefaultChainSelector() {\n    return {\n        select(chains) {\n            return __awaiter(this, void 0, void 0, function* () {\n                if (chains.length === 1) {\n                    return chains[0];\n                }\n                else if (chains.includes(SOLANA_MAINNET_CHAIN)) {\n                    return SOLANA_MAINNET_CHAIN;\n                }\n                else\n                    return chains[0];\n            });\n        },\n    };\n}\n\nexport { LocalSolanaMobileWalletAdapterWallet, RemoteSolanaMobileWalletAdapterWallet, SolanaMobileWalletAdapterWalletName, createDefaultAuthorizationCache, createDefaultChainSelector, createDefaultWalletNotFoundHandler, defaultErrorModalWalletNotFoundHandler, registerMwa };\n", "import { VersionedMessage, Transaction, VersionedTransaction, SIGNATURE_LENGTH_IN_BYTES } from '@solana/web3.js';\nimport { transact as transact$1, startRemoteScenario as startRemoteScenario$1 } from '@solana-mobile/mobile-wallet-adapter-protocol';\nimport bs58 from 'bs58';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\n\nfunction fromUint8Array(byteArray) {\n    return window.btoa(String.fromCharCode.call(null, ...byteArray));\n}\nfunction toUint8Array(base64EncodedByteArray) {\n    return new Uint8Array(window\n        .atob(base64EncodedByteArray)\n        .split('')\n        .map((c) => c.charCodeAt(0)));\n}\n\nfunction getPayloadFromTransaction(transaction) {\n    const serializedTransaction = 'version' in transaction\n        ? transaction.serialize()\n        : transaction.serialize({\n            requireAllSignatures: false,\n            verifySignatures: false,\n        });\n    const payload = fromUint8Array(serializedTransaction);\n    return payload;\n}\nfunction getTransactionFromWireMessage(byteArray) {\n    const numSignatures = byteArray[0];\n    const messageOffset = numSignatures * SIGNATURE_LENGTH_IN_BYTES + 1;\n    const version = VersionedMessage.deserializeMessageVersion(byteArray.slice(messageOffset, byteArray.length));\n    if (version === 'legacy') {\n        return Transaction.from(byteArray);\n    }\n    else {\n        return VersionedTransaction.deserialize(byteArray);\n    }\n}\nfunction transact(callback, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const augmentedCallback = (wallet) => {\n            return callback(augmentWalletAPI(wallet));\n        };\n        return yield transact$1(augmentedCallback, config);\n    });\n}\nfunction startRemoteScenario(config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const { wallet, close, associationUrl } = yield startRemoteScenario$1(config);\n        const augmentedPromise = wallet.then((wallet) => {\n            return augmentWalletAPI(wallet);\n        });\n        return { wallet: augmentedPromise, close, associationUrl };\n    });\n}\nfunction augmentWalletAPI(wallet) {\n    return new Proxy({}, {\n        get(target, p) {\n            if (target[p] == null) {\n                switch (p) {\n                    case 'signAndSendTransactions':\n                        target[p] = function (_a) {\n                            var { minContextSlot, commitment, skipPreflight, maxRetries, waitForCommitmentToSendNextTransaction, transactions } = _a, rest = __rest(_a, [\"minContextSlot\", \"commitment\", \"skipPreflight\", \"maxRetries\", \"waitForCommitmentToSendNextTransaction\", \"transactions\"]);\n                            return __awaiter(this, void 0, void 0, function* () {\n                                const payloads = transactions.map(getPayloadFromTransaction);\n                                const options = {\n                                    min_context_slot: minContextSlot,\n                                    commitment: commitment,\n                                    skip_preflight: skipPreflight,\n                                    max_retries: maxRetries,\n                                    wait_for_commitment_to_send_next_transaction: waitForCommitmentToSendNextTransaction\n                                };\n                                const { signatures: base64EncodedSignatures } = yield wallet.signAndSendTransactions(Object.assign(Object.assign(Object.assign({}, rest), (Object.values(options).some(element => element != null)\n                                    ? { options: options }\n                                    : null)), { payloads }));\n                                const signatures = base64EncodedSignatures.map(toUint8Array).map(bs58.encode);\n                                return signatures;\n                            });\n                        };\n                        break;\n                    case 'signMessages':\n                        target[p] = function (_a) {\n                            var { payloads } = _a, rest = __rest(_a, [\"payloads\"]);\n                            return __awaiter(this, void 0, void 0, function* () {\n                                const base64EncodedPayloads = payloads.map(fromUint8Array);\n                                const { signed_payloads: base64EncodedSignedMessages } = yield wallet.signMessages(Object.assign(Object.assign({}, rest), { payloads: base64EncodedPayloads }));\n                                const signedMessages = base64EncodedSignedMessages.map(toUint8Array);\n                                return signedMessages;\n                            });\n                        };\n                        break;\n                    case 'signTransactions':\n                        target[p] = function (_a) {\n                            var { transactions } = _a, rest = __rest(_a, [\"transactions\"]);\n                            return __awaiter(this, void 0, void 0, function* () {\n                                const payloads = transactions.map(getPayloadFromTransaction);\n                                const { signed_payloads: base64EncodedCompiledTransactions } = yield wallet.signTransactions(Object.assign(Object.assign({}, rest), { payloads }));\n                                const compiledTransactions = base64EncodedCompiledTransactions.map(toUint8Array);\n                                const signedTransactions = compiledTransactions.map(getTransactionFromWireMessage);\n                                return signedTransactions;\n                            });\n                        };\n                        break;\n                    default: {\n                        target[p] = wallet[p];\n                        break;\n                    }\n                }\n            }\n            return target[p];\n        },\n        defineProperty() {\n            return false;\n        },\n        deleteProperty() {\n            return false;\n        },\n    });\n}\n\nexport { startRemoteScenario, transact };\n", "import { createSignInMessageText } from '@solana/wallet-standard-util';\n\n// Typescript `enums` thwart tree-shaking. See https://bargsten.org/jsts/enums/\nconst SolanaMobileWalletAdapterErrorCode = {\n    ERROR_ASSOCIATION_PORT_OUT_OF_RANGE: 'ERROR_ASSOCIATION_PORT_OUT_OF_RANGE',\n    ERROR_REFLECTOR_ID_OUT_OF_RANGE: 'ERROR_REFLECTOR_ID_OUT_OF_RANGE',\n    ERROR_FORBIDDEN_WALLET_BASE_URL: 'ERROR_FORBIDDEN_WALLET_BASE_URL',\n    ERROR_SECURE_CONTEXT_REQUIRED: 'ERROR_SECURE_CONTEXT_REQUIRED',\n    ERROR_SESSION_CLOSED: 'ERROR_SESSION_CLOSED',\n    ERROR_SESSION_TIMEOUT: 'ERROR_SESSION_TIMEOUT',\n    ERROR_WALLET_NOT_FOUND: 'ERROR_WALLET_NOT_FOUND',\n    ERROR_INVALID_PROTOCOL_VERSION: 'ERROR_INVALID_PROTOCOL_VERSION',\n    ERROR_BROWSER_NOT_SUPPORTED: 'ERROR_BROWSER_NOT_SUPPORTED',\n};\nclass SolanaMobileWalletAdapterError extends Error {\n    constructor(...args) {\n        const [code, message, data] = args;\n        super(message);\n        this.code = code;\n        this.data = data;\n        this.name = 'SolanaMobileWalletAdapterError';\n    }\n}\n// Typescript `enums` thwart tree-shaking. See https://bargsten.org/jsts/enums/\nconst SolanaMobileWalletAdapterProtocolErrorCode = {\n    // Keep these in sync with `mobilewalletadapter/common/ProtocolContract.java`.\n    ERROR_AUTHORIZATION_FAILED: -1,\n    ERROR_INVALID_PAYLOADS: -2,\n    ERROR_NOT_SIGNED: -3,\n    ERROR_NOT_SUBMITTED: -4,\n    ERROR_TOO_MANY_PAYLOADS: -5,\n    ERROR_ATTEST_ORIGIN_ANDROID: -100,\n};\nclass SolanaMobileWalletAdapterProtocolError extends Error {\n    constructor(...args) {\n        const [jsonRpcMessageId, code, message, data] = args;\n        super(message);\n        this.code = code;\n        this.data = data;\n        this.jsonRpcMessageId = jsonRpcMessageId;\n        this.name = 'SolanaMobileWalletAdapterProtocolError';\n    }\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\n\nfunction encode(input) {\n    return window.btoa(input);\n}\nfunction fromUint8Array(byteArray, urlsafe) {\n    const base64 = window.btoa(String.fromCharCode.call(null, ...byteArray));\n    if (urlsafe) {\n        return base64\n            .replace(/\\+/g, '-')\n            .replace(/\\//g, '_')\n            .replace(/=+$/, '');\n    }\n    else\n        return base64;\n}\nfunction toUint8Array(base64EncodedByteArray) {\n    return new Uint8Array(window\n        .atob(base64EncodedByteArray)\n        .split('')\n        .map((c) => c.charCodeAt(0)));\n}\n\nfunction createHelloReq(ecdhPublicKey, associationKeypairPrivateKey) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const publicKeyBuffer = yield crypto.subtle.exportKey('raw', ecdhPublicKey);\n        const signatureBuffer = yield crypto.subtle.sign({ hash: 'SHA-256', name: 'ECDSA' }, associationKeypairPrivateKey, publicKeyBuffer);\n        const response = new Uint8Array(publicKeyBuffer.byteLength + signatureBuffer.byteLength);\n        response.set(new Uint8Array(publicKeyBuffer), 0);\n        response.set(new Uint8Array(signatureBuffer), publicKeyBuffer.byteLength);\n        return response;\n    });\n}\n\nfunction createSIWSMessage(payload) {\n    return createSignInMessageText(payload);\n}\nfunction createSIWSMessageBase64(payload) {\n    return encode(createSIWSMessage(payload));\n}\n\n// optional features\nconst SolanaSignTransactions = 'solana:signTransactions';\nconst SolanaCloneAuthorization = 'solana:cloneAuthorization';\nconst SolanaSignInWithSolana = 'solana:signInWithSolana';\n\n/**\n * Creates a {@link MobileWallet} proxy that handles backwards compatibility and API to RPC conversion.\n *\n * @param protocolVersion the protocol version in use for this session/request\n * @param protocolRequestHandler callback function that handles sending the RPC request to the wallet endpoint.\n * @returns a {@link MobileWallet} proxy\n */\nfunction createMobileWalletProxy(protocolVersion, protocolRequestHandler) {\n    return new Proxy({}, {\n        get(target, p) {\n            // Wrapping a Proxy in a promise results in the Proxy being asked for a 'then' property so must \n            // return null if 'then' is called on this proxy to let the 'resolve()' call know this is not a promise.\n            // see: https://stackoverflow.com/a/53890904\n            //@ts-ignore\n            if (p === 'then') {\n                return null;\n            }\n            if (target[p] == null) {\n                target[p] = function (inputParams) {\n                    return __awaiter(this, void 0, void 0, function* () {\n                        const { method, params } = handleMobileWalletRequest(p, inputParams, protocolVersion);\n                        const result = yield protocolRequestHandler(method, params);\n                        // if the request tried to sign in but the wallet did not return a sign in result, fallback on message signing\n                        if (method === 'authorize' && params.sign_in_payload && !result.sign_in_result) {\n                            result['sign_in_result'] = yield signInFallback(params.sign_in_payload, result, protocolRequestHandler);\n                        }\n                        return handleMobileWalletResponse(p, result, protocolVersion);\n                    });\n                };\n            }\n            return target[p];\n        },\n        defineProperty() {\n            return false;\n        },\n        deleteProperty() {\n            return false;\n        },\n    });\n}\n/**\n * Handles all {@link MobileWallet} API requests and determines the correct MWA RPC method and params to call.\n * This handles backwards compatibility, based on the provided @protocolVersion.\n *\n * @param methodName the name of {@link MobileWallet} method that was called\n * @param methodParams the parameters that were passed to the method\n * @param protocolVersion the protocol version in use for this session/request\n * @returns the RPC request method and params that should be sent to the wallet endpoint\n */\nfunction handleMobileWalletRequest(methodName, methodParams, protocolVersion) {\n    let params = methodParams;\n    let method = methodName\n        .toString()\n        .replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`)\n        .toLowerCase();\n    switch (methodName) {\n        case 'authorize': {\n            let { chain } = params;\n            if (protocolVersion === 'legacy') {\n                switch (chain) {\n                    case 'solana:testnet': {\n                        chain = 'testnet';\n                        break;\n                    }\n                    case 'solana:devnet': {\n                        chain = 'devnet';\n                        break;\n                    }\n                    case 'solana:mainnet': {\n                        chain = 'mainnet-beta';\n                        break;\n                    }\n                    default: {\n                        chain = params.cluster;\n                    }\n                }\n                params.cluster = chain;\n            }\n            else {\n                switch (chain) {\n                    case 'testnet':\n                    case 'devnet': {\n                        chain = `solana:${chain}`;\n                        break;\n                    }\n                    case 'mainnet-beta': {\n                        chain = 'solana:mainnet';\n                        break;\n                    }\n                }\n                params.chain = chain;\n            }\n        }\n        case 'reauthorize': {\n            const { auth_token, identity } = params;\n            if (auth_token) {\n                switch (protocolVersion) {\n                    case 'legacy': {\n                        method = 'reauthorize';\n                        params = { auth_token: auth_token, identity: identity };\n                        break;\n                    }\n                    default: {\n                        method = 'authorize';\n                        break;\n                    }\n                }\n            }\n            break;\n        }\n    }\n    return { method, params };\n}\n/**\n * Handles all {@link MobileWallet} API responses and modifies the response for backwards compatibility, if needed\n *\n * @param method the {@link MobileWallet} method that was called\n * @param response the original response that was returned by the method call\n * @param protocolVersion the protocol version in use for this session/request\n * @returns the possibly modified response\n */\nfunction handleMobileWalletResponse(method, response, protocolVersion) {\n    switch (method) {\n        case 'getCapabilities': {\n            const capabilities = response;\n            switch (protocolVersion) {\n                case 'legacy': {\n                    const features = [SolanaSignTransactions];\n                    if (capabilities.supports_clone_authorization === true) {\n                        features.push(SolanaCloneAuthorization);\n                    }\n                    return Object.assign(Object.assign({}, capabilities), { features: features });\n                }\n                case 'v1': {\n                    return Object.assign(Object.assign({}, capabilities), { supports_sign_and_send_transactions: true, supports_clone_authorization: capabilities.features.includes(SolanaCloneAuthorization) });\n                }\n            }\n        }\n    }\n    return response;\n}\nfunction signInFallback(signInPayload, authorizationResult, protocolRequestHandler) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function* () {\n        const domain = (_a = signInPayload.domain) !== null && _a !== void 0 ? _a : window.location.host;\n        const address = authorizationResult.accounts[0].address;\n        const siwsMessage = createSIWSMessageBase64(Object.assign(Object.assign({}, signInPayload), { domain, address }));\n        const signMessageResult = yield protocolRequestHandler('sign_messages', {\n            addresses: [address],\n            payloads: [siwsMessage]\n        });\n        const signInResult = {\n            address: address,\n            signed_message: siwsMessage,\n            signature: signMessageResult.signed_payloads[0].slice(siwsMessage.length)\n        };\n        return signInResult;\n    });\n}\n\nconst SEQUENCE_NUMBER_BYTES = 4;\nfunction createSequenceNumberVector(sequenceNumber) {\n    if (sequenceNumber >= **********) {\n        throw new Error('Outbound sequence number overflow. The maximum sequence number is 32-bytes.');\n    }\n    const byteArray = new ArrayBuffer(SEQUENCE_NUMBER_BYTES);\n    const view = new DataView(byteArray);\n    view.setUint32(0, sequenceNumber, /* littleEndian */ false);\n    return new Uint8Array(byteArray);\n}\n\nconst INITIALIZATION_VECTOR_BYTES = 12;\nconst ENCODED_PUBLIC_KEY_LENGTH_BYTES = 65;\nfunction encryptMessage(plaintext, sequenceNumber, sharedSecret) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const sequenceNumberVector = createSequenceNumberVector(sequenceNumber);\n        const initializationVector = new Uint8Array(INITIALIZATION_VECTOR_BYTES);\n        crypto.getRandomValues(initializationVector);\n        const ciphertext = yield crypto.subtle.encrypt(getAlgorithmParams(sequenceNumberVector, initializationVector), sharedSecret, new TextEncoder().encode(plaintext));\n        const response = new Uint8Array(sequenceNumberVector.byteLength + initializationVector.byteLength + ciphertext.byteLength);\n        response.set(new Uint8Array(sequenceNumberVector), 0);\n        response.set(new Uint8Array(initializationVector), sequenceNumberVector.byteLength);\n        response.set(new Uint8Array(ciphertext), sequenceNumberVector.byteLength + initializationVector.byteLength);\n        return response;\n    });\n}\nfunction decryptMessage(message, sharedSecret) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const sequenceNumberVector = message.slice(0, SEQUENCE_NUMBER_BYTES);\n        const initializationVector = message.slice(SEQUENCE_NUMBER_BYTES, SEQUENCE_NUMBER_BYTES + INITIALIZATION_VECTOR_BYTES);\n        const ciphertext = message.slice(SEQUENCE_NUMBER_BYTES + INITIALIZATION_VECTOR_BYTES);\n        const plaintextBuffer = yield crypto.subtle.decrypt(getAlgorithmParams(sequenceNumberVector, initializationVector), sharedSecret, ciphertext);\n        const plaintext = getUtf8Decoder().decode(plaintextBuffer);\n        return plaintext;\n    });\n}\nfunction getAlgorithmParams(sequenceNumber, initializationVector) {\n    return {\n        additionalData: sequenceNumber,\n        iv: initializationVector,\n        name: 'AES-GCM',\n        tagLength: 128, // 16 byte tag => 128 bits\n    };\n}\nlet _utf8Decoder;\nfunction getUtf8Decoder() {\n    if (_utf8Decoder === undefined) {\n        _utf8Decoder = new TextDecoder('utf-8');\n    }\n    return _utf8Decoder;\n}\n\nfunction generateAssociationKeypair() {\n    return __awaiter(this, void 0, void 0, function* () {\n        return yield crypto.subtle.generateKey({\n            name: 'ECDSA',\n            namedCurve: 'P-256',\n        }, false /* extractable */, ['sign'] /* keyUsages */);\n    });\n}\n\nfunction generateECDHKeypair() {\n    return __awaiter(this, void 0, void 0, function* () {\n        return yield crypto.subtle.generateKey({\n            name: 'ECDH',\n            namedCurve: 'P-256',\n        }, false /* extractable */, ['deriveKey', 'deriveBits'] /* keyUsages */);\n    });\n}\n\n// https://stackoverflow.com/a/9458996/802047\nfunction arrayBufferToBase64String(buffer) {\n    let binary = '';\n    const bytes = new Uint8Array(buffer);\n    const len = bytes.byteLength;\n    for (let ii = 0; ii < len; ii++) {\n        binary += String.fromCharCode(bytes[ii]);\n    }\n    return window.btoa(binary);\n}\n\nfunction getRandomAssociationPort() {\n    return assertAssociationPort(49152 + Math.floor(Math.random() * (65535 - 49152 + 1)));\n}\nfunction assertAssociationPort(port) {\n    if (port < 49152 || port > 65535) {\n        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_ASSOCIATION_PORT_OUT_OF_RANGE, `Association port number must be between 49152 and 65535. ${port} given.`, { port });\n    }\n    return port;\n}\n\nfunction getStringWithURLUnsafeCharactersReplaced(unsafeBase64EncodedString) {\n    return unsafeBase64EncodedString.replace(/[/+=]/g, (m) => ({\n        '/': '_',\n        '+': '-',\n        '=': '.',\n    }[m]));\n}\n\nconst INTENT_NAME = 'solana-wallet';\nfunction getPathParts(pathString) {\n    return (pathString\n        // Strip leading and trailing slashes\n        .replace(/(^\\/+|\\/+$)/g, '')\n        // Return an array of directories\n        .split('/'));\n}\nfunction getIntentURL(methodPathname, intentUrlBase) {\n    let baseUrl = null;\n    if (intentUrlBase) {\n        try {\n            baseUrl = new URL(intentUrlBase);\n        }\n        catch (_a) { } // eslint-disable-line no-empty\n        if ((baseUrl === null || baseUrl === void 0 ? void 0 : baseUrl.protocol) !== 'https:') {\n            throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_FORBIDDEN_WALLET_BASE_URL, 'Base URLs supplied by wallets must be valid `https` URLs');\n        }\n    }\n    baseUrl || (baseUrl = new URL(`${INTENT_NAME}:/`));\n    const pathname = methodPathname.startsWith('/')\n        ? // Method is an absolute path. Replace it wholesale.\n            methodPathname\n        : // Method is a relative path. Merge it with the existing one.\n            [...getPathParts(baseUrl.pathname), ...getPathParts(methodPathname)].join('/');\n    return new URL(pathname, baseUrl);\n}\nfunction getAssociateAndroidIntentURL(associationPublicKey, putativePort, associationURLBase, protocolVersions = ['v1']) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const associationPort = assertAssociationPort(putativePort);\n        const exportedKey = yield crypto.subtle.exportKey('raw', associationPublicKey);\n        const encodedKey = arrayBufferToBase64String(exportedKey);\n        const url = getIntentURL('v1/associate/local', associationURLBase);\n        url.searchParams.set('association', getStringWithURLUnsafeCharactersReplaced(encodedKey));\n        url.searchParams.set('port', `${associationPort}`);\n        protocolVersions.forEach((version) => {\n            url.searchParams.set('v', version);\n        });\n        return url;\n    });\n}\nfunction getRemoteAssociateAndroidIntentURL(associationPublicKey, hostAuthority, reflectorId, associationURLBase, protocolVersions = ['v1']) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const exportedKey = yield crypto.subtle.exportKey('raw', associationPublicKey);\n        const encodedKey = arrayBufferToBase64String(exportedKey);\n        const url = getIntentURL('v1/associate/remote', associationURLBase);\n        url.searchParams.set('association', getStringWithURLUnsafeCharactersReplaced(encodedKey));\n        url.searchParams.set('reflector', `${hostAuthority}`);\n        url.searchParams.set('id', `${fromUint8Array(reflectorId, true)}`);\n        protocolVersions.forEach((version) => {\n            url.searchParams.set('v', version);\n        });\n        return url;\n    });\n}\n\nfunction encryptJsonRpcMessage(jsonRpcMessage, sharedSecret) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const plaintext = JSON.stringify(jsonRpcMessage);\n        const sequenceNumber = jsonRpcMessage.id;\n        return encryptMessage(plaintext, sequenceNumber, sharedSecret);\n    });\n}\nfunction decryptJsonRpcMessage(message, sharedSecret) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const plaintext = yield decryptMessage(message, sharedSecret);\n        const jsonRpcMessage = JSON.parse(plaintext);\n        if (Object.hasOwnProperty.call(jsonRpcMessage, 'error')) {\n            throw new SolanaMobileWalletAdapterProtocolError(jsonRpcMessage.id, jsonRpcMessage.error.code, jsonRpcMessage.error.message);\n        }\n        return jsonRpcMessage;\n    });\n}\n\nfunction parseHelloRsp(payloadBuffer, // The X9.62-encoded wallet endpoint ephemeral ECDH public keypoint.\nassociationPublicKey, ecdhPrivateKey) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const [associationPublicKeyBuffer, walletPublicKey] = yield Promise.all([\n            crypto.subtle.exportKey('raw', associationPublicKey),\n            crypto.subtle.importKey('raw', payloadBuffer.slice(0, ENCODED_PUBLIC_KEY_LENGTH_BYTES), { name: 'ECDH', namedCurve: 'P-256' }, false /* extractable */, [] /* keyUsages */),\n        ]);\n        const sharedSecret = yield crypto.subtle.deriveBits({ name: 'ECDH', public: walletPublicKey }, ecdhPrivateKey, 256);\n        const ecdhSecretKey = yield crypto.subtle.importKey('raw', sharedSecret, 'HKDF', false /* extractable */, ['deriveKey'] /* keyUsages */);\n        const aesKeyMaterialVal = yield crypto.subtle.deriveKey({\n            name: 'HKDF',\n            hash: 'SHA-256',\n            salt: new Uint8Array(associationPublicKeyBuffer),\n            info: new Uint8Array(),\n        }, ecdhSecretKey, { name: 'AES-GCM', length: 128 }, false /* extractable */, ['encrypt', 'decrypt']);\n        return aesKeyMaterialVal;\n    });\n}\n\nfunction parseSessionProps(message, sharedSecret) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const plaintext = yield decryptMessage(message, sharedSecret);\n        const jsonProperties = JSON.parse(plaintext);\n        let protocolVersion = 'legacy';\n        if (Object.hasOwnProperty.call(jsonProperties, 'v')) {\n            switch (jsonProperties.v) {\n                case 1:\n                case '1':\n                case 'v1':\n                    protocolVersion = 'v1';\n                    break;\n                case 'legacy':\n                    protocolVersion = 'legacy';\n                    break;\n                default:\n                    throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_INVALID_PROTOCOL_VERSION, `Unknown/unsupported protocol version: ${jsonProperties.v}`);\n            }\n        }\n        return ({\n            protocol_version: protocolVersion\n        });\n    });\n}\n\n// Typescript `enums` thwart tree-shaking. See https://bargsten.org/jsts/enums/\nconst Browser = {\n    Firefox: 0,\n    Other: 1,\n};\nfunction assertUnreachable(x) {\n    return x;\n}\nfunction getBrowser() {\n    return navigator.userAgent.indexOf('Firefox/') !== -1 ? Browser.Firefox : Browser.Other;\n}\nfunction getDetectionPromise() {\n    // Chrome and others silently fail if a custom protocol is not supported.\n    // For these, we wait to see if the browser is navigated away from in\n    // a reasonable amount of time (ie. the native wallet opened).\n    return new Promise((resolve, reject) => {\n        function cleanup() {\n            clearTimeout(timeoutId);\n            window.removeEventListener('blur', handleBlur);\n        }\n        function handleBlur() {\n            cleanup();\n            resolve();\n        }\n        window.addEventListener('blur', handleBlur);\n        const timeoutId = setTimeout(() => {\n            cleanup();\n            reject();\n        }, 3000);\n    });\n}\nlet _frame = null;\nfunction launchUrlThroughHiddenFrame(url) {\n    if (_frame == null) {\n        _frame = document.createElement('iframe');\n        _frame.style.display = 'none';\n        document.body.appendChild(_frame);\n    }\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    _frame.contentWindow.location.href = url.toString();\n}\nfunction launchAssociation(associationUrl) {\n    return __awaiter(this, void 0, void 0, function* () {\n        if (associationUrl.protocol === 'https:') {\n            // The association URL is an Android 'App Link' or iOS 'Universal Link'.\n            // These are regular web URLs that are designed to launch an app if it\n            // is installed or load the actual target webpage if not.\n            window.location.assign(associationUrl);\n        }\n        else {\n            // The association URL has a custom protocol (eg. `solana-wallet:`)\n            try {\n                const browser = getBrowser();\n                switch (browser) {\n                    case Browser.Firefox:\n                        // If a custom protocol is not supported in Firefox, it throws.\n                        launchUrlThroughHiddenFrame(associationUrl);\n                        // If we reached this line, it's supported.\n                        break;\n                    case Browser.Other: {\n                        const detectionPromise = getDetectionPromise();\n                        window.location.assign(associationUrl);\n                        yield detectionPromise;\n                        break;\n                    }\n                    default:\n                        assertUnreachable(browser);\n                }\n            }\n            catch (e) {\n                throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_WALLET_NOT_FOUND, 'Found no installed wallet that supports the mobile wallet protocol.');\n            }\n        }\n    });\n}\nfunction startSession(associationPublicKey, associationURLBase) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const randomAssociationPort = getRandomAssociationPort();\n        const associationUrl = yield getAssociateAndroidIntentURL(associationPublicKey, randomAssociationPort, associationURLBase);\n        yield launchAssociation(associationUrl);\n        return randomAssociationPort;\n    });\n}\n\nconst WEBSOCKET_CONNECTION_CONFIG = {\n    /**\n     * 300 milliseconds is a generally accepted threshold for what someone\n     * would consider an acceptable response time for a user interface\n     * after having performed a low-attention tapping task. We set the initial\n     * interval at which we wait for the wallet to set up the websocket at\n     * half this, as per the Nyquist frequency, with a progressive backoff\n     * sequence from there. The total wait time is 30s, which allows for the\n     * user to be presented with a disambiguation dialog, select a wallet, and\n     * for the wallet app to subsequently start.\n     */\n    retryDelayScheduleMs: [150, 150, 200, 500, 500, 750, 750, 1000],\n    timeoutMs: 30000,\n};\nconst WEBSOCKET_PROTOCOL_BINARY = 'com.solana.mobilewalletadapter.v1';\nconst WEBSOCKET_PROTOCOL_BASE64 = 'com.solana.mobilewalletadapter.v1.base64';\nfunction assertSecureContext() {\n    if (typeof window === 'undefined' || window.isSecureContext !== true) {\n        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SECURE_CONTEXT_REQUIRED, 'The mobile wallet adapter protocol must be used in a secure context (`https`).');\n    }\n}\nfunction assertSecureEndpointSpecificURI(walletUriBase) {\n    let url;\n    try {\n        url = new URL(walletUriBase);\n    }\n    catch (_a) {\n        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_FORBIDDEN_WALLET_BASE_URL, 'Invalid base URL supplied by wallet');\n    }\n    if (url.protocol !== 'https:') {\n        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_FORBIDDEN_WALLET_BASE_URL, 'Base URLs supplied by wallets must be valid `https` URLs');\n    }\n}\nfunction getSequenceNumberFromByteArray(byteArray) {\n    const view = new DataView(byteArray);\n    return view.getUint32(0, /* littleEndian */ false);\n}\nfunction decodeVarLong(byteArray) {\n    var bytes = new Uint8Array(byteArray), l = byteArray.byteLength, limit = 10, value = 0, offset = 0, b;\n    do {\n        if (offset >= l || offset > limit)\n            throw new RangeError('Failed to decode varint');\n        b = bytes[offset++];\n        value |= (b & 0x7F) << (7 * offset);\n    } while (b >= 0x80);\n    return { value, offset };\n}\nfunction getReflectorIdFromByteArray(byteArray) {\n    let { value: length, offset } = decodeVarLong(byteArray);\n    return new Uint8Array(byteArray.slice(offset, offset + length));\n}\nfunction transact(callback, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        assertSecureContext();\n        const associationKeypair = yield generateAssociationKeypair();\n        const sessionPort = yield startSession(associationKeypair.publicKey, config === null || config === void 0 ? void 0 : config.baseUri);\n        const websocketURL = `ws://localhost:${sessionPort}/solana-wallet`;\n        let connectionStartTime;\n        const getNextRetryDelayMs = (() => {\n            const schedule = [...WEBSOCKET_CONNECTION_CONFIG.retryDelayScheduleMs];\n            return () => (schedule.length > 1 ? schedule.shift() : schedule[0]);\n        })();\n        let nextJsonRpcMessageId = 1;\n        let lastKnownInboundSequenceNumber = 0;\n        let state = { __type: 'disconnected' };\n        return new Promise((resolve, reject) => {\n            let socket;\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const jsonRpcResponsePromises = {};\n            const handleOpen = () => __awaiter(this, void 0, void 0, function* () {\n                if (state.__type !== 'connecting') {\n                    console.warn('Expected adapter state to be `connecting` at the moment the websocket opens. ' +\n                        `Got \\`${state.__type}\\`.`);\n                    return;\n                }\n                socket.removeEventListener('open', handleOpen);\n                // previous versions of this library and walletlib incorrectly implemented the MWA session \n                // establishment protocol for local connections. The dapp is supposed to wait for the \n                // APP_PING message before sending the HELLO_REQ. Instead, the dapp was sending the HELLO_REQ \n                // immediately upon connection to the websocket server regardless of wether or not an \n                // APP_PING was sent by the wallet/websocket server. We must continue to support this behavior \n                // in case the user is using a wallet that has not updated their walletlib implementation. \n                const { associationKeypair } = state;\n                const ecdhKeypair = yield generateECDHKeypair();\n                socket.send(yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey));\n                state = {\n                    __type: 'hello_req_sent',\n                    associationPublicKey: associationKeypair.publicKey,\n                    ecdhPrivateKey: ecdhKeypair.privateKey,\n                };\n            });\n            const handleClose = (evt) => {\n                if (evt.wasClean) {\n                    state = { __type: 'disconnected' };\n                }\n                else {\n                    reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_CLOSED, `The wallet session dropped unexpectedly (${evt.code}: ${evt.reason}).`, { closeEvent: evt }));\n                }\n                disposeSocket();\n            };\n            const handleError = (_evt) => __awaiter(this, void 0, void 0, function* () {\n                disposeSocket();\n                if (Date.now() - connectionStartTime >= WEBSOCKET_CONNECTION_CONFIG.timeoutMs) {\n                    reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_TIMEOUT, `Failed to connect to the wallet websocket at ${websocketURL}.`));\n                }\n                else {\n                    yield new Promise((resolve) => {\n                        const retryDelayMs = getNextRetryDelayMs();\n                        retryWaitTimeoutId = window.setTimeout(resolve, retryDelayMs);\n                    });\n                    attemptSocketConnection();\n                }\n            });\n            const handleMessage = (evt) => __awaiter(this, void 0, void 0, function* () {\n                const responseBuffer = yield evt.data.arrayBuffer();\n                switch (state.__type) {\n                    case 'connecting':\n                        if (responseBuffer.byteLength !== 0) {\n                            throw new Error('Encountered unexpected message while connecting');\n                        }\n                        const ecdhKeypair = yield generateECDHKeypair();\n                        socket.send(yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey));\n                        state = {\n                            __type: 'hello_req_sent',\n                            associationPublicKey: associationKeypair.publicKey,\n                            ecdhPrivateKey: ecdhKeypair.privateKey,\n                        };\n                        break;\n                    case 'connected':\n                        try {\n                            const sequenceNumberVector = responseBuffer.slice(0, SEQUENCE_NUMBER_BYTES);\n                            const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);\n                            if (sequenceNumber !== (lastKnownInboundSequenceNumber + 1)) {\n                                throw new Error('Encrypted message has invalid sequence number');\n                            }\n                            lastKnownInboundSequenceNumber = sequenceNumber;\n                            const jsonRpcMessage = yield decryptJsonRpcMessage(responseBuffer, state.sharedSecret);\n                            const responsePromise = jsonRpcResponsePromises[jsonRpcMessage.id];\n                            delete jsonRpcResponsePromises[jsonRpcMessage.id];\n                            responsePromise.resolve(jsonRpcMessage.result);\n                        }\n                        catch (e) {\n                            if (e instanceof SolanaMobileWalletAdapterProtocolError) {\n                                const responsePromise = jsonRpcResponsePromises[e.jsonRpcMessageId];\n                                delete jsonRpcResponsePromises[e.jsonRpcMessageId];\n                                responsePromise.reject(e);\n                            }\n                            else {\n                                throw e;\n                            }\n                        }\n                        break;\n                    case 'hello_req_sent': {\n                        // if we receive an APP_PING message (empty message), resend the HELLO_REQ (see above)\n                        if (responseBuffer.byteLength === 0) {\n                            const ecdhKeypair = yield generateECDHKeypair();\n                            socket.send(yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey));\n                            state = {\n                                __type: 'hello_req_sent',\n                                associationPublicKey: associationKeypair.publicKey,\n                                ecdhPrivateKey: ecdhKeypair.privateKey,\n                            };\n                            break;\n                        }\n                        const sharedSecret = yield parseHelloRsp(responseBuffer, state.associationPublicKey, state.ecdhPrivateKey);\n                        const sessionPropertiesBuffer = responseBuffer.slice(ENCODED_PUBLIC_KEY_LENGTH_BYTES);\n                        const sessionProperties = sessionPropertiesBuffer.byteLength !== 0\n                            ? yield (() => __awaiter(this, void 0, void 0, function* () {\n                                const sequenceNumberVector = sessionPropertiesBuffer.slice(0, SEQUENCE_NUMBER_BYTES);\n                                const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);\n                                if (sequenceNumber !== (lastKnownInboundSequenceNumber + 1)) {\n                                    throw new Error('Encrypted message has invalid sequence number');\n                                }\n                                lastKnownInboundSequenceNumber = sequenceNumber;\n                                return parseSessionProps(sessionPropertiesBuffer, sharedSecret);\n                            }))() : { protocol_version: 'legacy' };\n                        state = { __type: 'connected', sharedSecret, sessionProperties };\n                        const wallet = createMobileWalletProxy(sessionProperties.protocol_version, (method, params) => __awaiter(this, void 0, void 0, function* () {\n                            const id = nextJsonRpcMessageId++;\n                            socket.send(yield encryptJsonRpcMessage({\n                                id,\n                                jsonrpc: '2.0',\n                                method,\n                                params: params !== null && params !== void 0 ? params : {},\n                            }, sharedSecret));\n                            return new Promise((resolve, reject) => {\n                                jsonRpcResponsePromises[id] = {\n                                    resolve(result) {\n                                        switch (method) {\n                                            case 'authorize':\n                                            case 'reauthorize': {\n                                                const { wallet_uri_base } = result;\n                                                if (wallet_uri_base != null) {\n                                                    try {\n                                                        assertSecureEndpointSpecificURI(wallet_uri_base);\n                                                    }\n                                                    catch (e) {\n                                                        reject(e);\n                                                        return;\n                                                    }\n                                                }\n                                                break;\n                                            }\n                                        }\n                                        resolve(result);\n                                    },\n                                    reject,\n                                };\n                            });\n                        }));\n                        try {\n                            resolve(yield callback(wallet));\n                        }\n                        catch (e) {\n                            reject(e);\n                        }\n                        finally {\n                            disposeSocket();\n                            socket.close();\n                        }\n                        break;\n                    }\n                }\n            });\n            let disposeSocket;\n            let retryWaitTimeoutId;\n            const attemptSocketConnection = () => {\n                if (disposeSocket) {\n                    disposeSocket();\n                }\n                state = { __type: 'connecting', associationKeypair };\n                if (connectionStartTime === undefined) {\n                    connectionStartTime = Date.now();\n                }\n                socket = new WebSocket(websocketURL, [WEBSOCKET_PROTOCOL_BINARY]);\n                socket.addEventListener('open', handleOpen);\n                socket.addEventListener('close', handleClose);\n                socket.addEventListener('error', handleError);\n                socket.addEventListener('message', handleMessage);\n                disposeSocket = () => {\n                    window.clearTimeout(retryWaitTimeoutId);\n                    socket.removeEventListener('open', handleOpen);\n                    socket.removeEventListener('close', handleClose);\n                    socket.removeEventListener('error', handleError);\n                    socket.removeEventListener('message', handleMessage);\n                };\n            };\n            attemptSocketConnection();\n        });\n    });\n}\nfunction startRemoteScenario(config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        assertSecureContext();\n        const associationKeypair = yield generateAssociationKeypair();\n        const websocketURL = `wss://${config === null || config === void 0 ? void 0 : config.remoteHostAuthority}/reflect`;\n        let connectionStartTime;\n        const getNextRetryDelayMs = (() => {\n            const schedule = [...WEBSOCKET_CONNECTION_CONFIG.retryDelayScheduleMs];\n            return () => (schedule.length > 1 ? schedule.shift() : schedule[0]);\n        })();\n        let nextJsonRpcMessageId = 1;\n        let lastKnownInboundSequenceNumber = 0;\n        let encoding;\n        let state = { __type: 'disconnected' };\n        let socket;\n        let disposeSocket;\n        let decodeBytes = (evt) => __awaiter(this, void 0, void 0, function* () {\n            if (encoding == 'base64') { // base64 encoding\n                const message = yield evt.data;\n                return toUint8Array(message).buffer;\n            }\n            else {\n                return yield evt.data.arrayBuffer();\n            }\n        });\n        // Reflector Connection Phase\n        // here we connect to the reflector and wait for the REFLECTOR_ID message \n        // so we build the association URL and return that back to the caller\n        const associationUrl = yield new Promise((resolve, reject) => {\n            const handleOpen = () => __awaiter(this, void 0, void 0, function* () {\n                if (state.__type !== 'connecting') {\n                    console.warn('Expected adapter state to be `connecting` at the moment the websocket opens. ' +\n                        `Got \\`${state.__type}\\`.`);\n                    return;\n                }\n                if (socket.protocol.includes(WEBSOCKET_PROTOCOL_BASE64)) {\n                    encoding = 'base64';\n                }\n                else {\n                    encoding = 'binary';\n                }\n                socket.removeEventListener('open', handleOpen);\n            });\n            const handleClose = (evt) => {\n                if (evt.wasClean) {\n                    state = { __type: 'disconnected' };\n                }\n                else {\n                    reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_CLOSED, `The wallet session dropped unexpectedly (${evt.code}: ${evt.reason}).`, { closeEvent: evt }));\n                }\n                disposeSocket();\n            };\n            const handleError = (_evt) => __awaiter(this, void 0, void 0, function* () {\n                disposeSocket();\n                if (Date.now() - connectionStartTime >= WEBSOCKET_CONNECTION_CONFIG.timeoutMs) {\n                    reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_TIMEOUT, `Failed to connect to the wallet websocket at ${websocketURL}.`));\n                }\n                else {\n                    yield new Promise((resolve) => {\n                        const retryDelayMs = getNextRetryDelayMs();\n                        retryWaitTimeoutId = window.setTimeout(resolve, retryDelayMs);\n                    });\n                    attemptSocketConnection();\n                }\n            });\n            const handleReflectorIdMessage = (evt) => __awaiter(this, void 0, void 0, function* () {\n                const responseBuffer = yield decodeBytes(evt);\n                if (state.__type === 'connecting') {\n                    if (responseBuffer.byteLength == 0) {\n                        throw new Error('Encountered unexpected message while connecting');\n                    }\n                    const reflectorId = getReflectorIdFromByteArray(responseBuffer);\n                    state = {\n                        __type: 'reflector_id_received',\n                        reflectorId: reflectorId\n                    };\n                    const associationUrl = yield getRemoteAssociateAndroidIntentURL(associationKeypair.publicKey, config.remoteHostAuthority, reflectorId, config === null || config === void 0 ? void 0 : config.baseUri);\n                    socket.removeEventListener('message', handleReflectorIdMessage);\n                    resolve(associationUrl);\n                }\n            });\n            let retryWaitTimeoutId;\n            const attemptSocketConnection = () => {\n                if (disposeSocket) {\n                    disposeSocket();\n                }\n                state = { __type: 'connecting', associationKeypair };\n                if (connectionStartTime === undefined) {\n                    connectionStartTime = Date.now();\n                }\n                socket = new WebSocket(websocketURL, [WEBSOCKET_PROTOCOL_BINARY, WEBSOCKET_PROTOCOL_BASE64]);\n                socket.addEventListener('open', handleOpen);\n                socket.addEventListener('close', handleClose);\n                socket.addEventListener('error', handleError);\n                socket.addEventListener('message', handleReflectorIdMessage);\n                disposeSocket = () => {\n                    window.clearTimeout(retryWaitTimeoutId);\n                    socket.removeEventListener('open', handleOpen);\n                    socket.removeEventListener('close', handleClose);\n                    socket.removeEventListener('error', handleError);\n                    socket.removeEventListener('message', handleReflectorIdMessage);\n                };\n            };\n            attemptSocketConnection();\n        });\n        // Wallet Connection Phase\n        // here we return the association URL (containing the reflector ID) to the caller + \n        // a promise that will resolve the MobileWallet object once the wallet connects.\n        let sessionEstablished = false;\n        let handleClose;\n        return { associationUrl, close: () => {\n                socket.close();\n                handleClose();\n            }, wallet: new Promise((resolve, reject) => {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                const jsonRpcResponsePromises = {};\n                const handleMessage = (evt) => __awaiter(this, void 0, void 0, function* () {\n                    const responseBuffer = yield decodeBytes(evt);\n                    switch (state.__type) {\n                        case 'reflector_id_received':\n                            if (responseBuffer.byteLength !== 0) {\n                                throw new Error('Encountered unexpected message while awaiting reflection');\n                            }\n                            const ecdhKeypair = yield generateECDHKeypair();\n                            const binaryMsg = yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey);\n                            if (encoding == 'base64') {\n                                socket.send(fromUint8Array(binaryMsg));\n                            }\n                            else {\n                                socket.send(binaryMsg);\n                            }\n                            state = {\n                                __type: 'hello_req_sent',\n                                associationPublicKey: associationKeypair.publicKey,\n                                ecdhPrivateKey: ecdhKeypair.privateKey,\n                            };\n                            break;\n                        case 'connected':\n                            try {\n                                const sequenceNumberVector = responseBuffer.slice(0, SEQUENCE_NUMBER_BYTES);\n                                const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);\n                                if (sequenceNumber !== (lastKnownInboundSequenceNumber + 1)) {\n                                    throw new Error('Encrypted message has invalid sequence number');\n                                }\n                                lastKnownInboundSequenceNumber = sequenceNumber;\n                                const jsonRpcMessage = yield decryptJsonRpcMessage(responseBuffer, state.sharedSecret);\n                                const responsePromise = jsonRpcResponsePromises[jsonRpcMessage.id];\n                                delete jsonRpcResponsePromises[jsonRpcMessage.id];\n                                responsePromise.resolve(jsonRpcMessage.result);\n                            }\n                            catch (e) {\n                                if (e instanceof SolanaMobileWalletAdapterProtocolError) {\n                                    const responsePromise = jsonRpcResponsePromises[e.jsonRpcMessageId];\n                                    delete jsonRpcResponsePromises[e.jsonRpcMessageId];\n                                    responsePromise.reject(e);\n                                }\n                                else {\n                                    throw e;\n                                }\n                            }\n                            break;\n                        case 'hello_req_sent': {\n                            const sharedSecret = yield parseHelloRsp(responseBuffer, state.associationPublicKey, state.ecdhPrivateKey);\n                            const sessionPropertiesBuffer = responseBuffer.slice(ENCODED_PUBLIC_KEY_LENGTH_BYTES);\n                            const sessionProperties = sessionPropertiesBuffer.byteLength !== 0\n                                ? yield (() => __awaiter(this, void 0, void 0, function* () {\n                                    const sequenceNumberVector = sessionPropertiesBuffer.slice(0, SEQUENCE_NUMBER_BYTES);\n                                    const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);\n                                    if (sequenceNumber !== (lastKnownInboundSequenceNumber + 1)) {\n                                        throw new Error('Encrypted message has invalid sequence number');\n                                    }\n                                    lastKnownInboundSequenceNumber = sequenceNumber;\n                                    return parseSessionProps(sessionPropertiesBuffer, sharedSecret);\n                                }))() : { protocol_version: 'legacy' };\n                            state = { __type: 'connected', sharedSecret, sessionProperties };\n                            const wallet = createMobileWalletProxy(sessionProperties.protocol_version, (method, params) => __awaiter(this, void 0, void 0, function* () {\n                                const id = nextJsonRpcMessageId++;\n                                const binaryMsg = yield encryptJsonRpcMessage({\n                                    id,\n                                    jsonrpc: '2.0',\n                                    method,\n                                    params: params !== null && params !== void 0 ? params : {},\n                                }, sharedSecret);\n                                if (encoding == 'base64') {\n                                    socket.send(fromUint8Array(binaryMsg));\n                                }\n                                else {\n                                    socket.send(binaryMsg);\n                                }\n                                return new Promise((resolve, reject) => {\n                                    jsonRpcResponsePromises[id] = {\n                                        resolve(result) {\n                                            switch (method) {\n                                                case 'authorize':\n                                                case 'reauthorize': {\n                                                    const { wallet_uri_base } = result;\n                                                    if (wallet_uri_base != null) {\n                                                        try {\n                                                            assertSecureEndpointSpecificURI(wallet_uri_base);\n                                                        }\n                                                        catch (e) {\n                                                            reject(e);\n                                                            return;\n                                                        }\n                                                    }\n                                                    break;\n                                                }\n                                            }\n                                            resolve(result);\n                                        },\n                                        reject,\n                                    };\n                                });\n                            }));\n                            sessionEstablished = true;\n                            try {\n                                resolve(wallet);\n                            }\n                            catch (e) {\n                                reject(e);\n                            }\n                            break;\n                        }\n                    }\n                });\n                socket.addEventListener('message', handleMessage);\n                handleClose = () => {\n                    socket.removeEventListener('message', handleMessage);\n                    disposeSocket();\n                    if (!sessionEstablished) {\n                        reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_CLOSED, `The wallet session was closed before connection.`, { closeEvent: new CloseEvent('socket was closed before connection') }));\n                    }\n                };\n            }) };\n    });\n}\n\nexport { SolanaCloneAuthorization, SolanaMobileWalletAdapterError, SolanaMobileWalletAdapterErrorCode, SolanaMobileWalletAdapterProtocolError, SolanaMobileWalletAdapterProtocolErrorCode, SolanaSignInWithSolana, SolanaSignTransactions, startRemoteScenario, transact };\n", "import {\n    BaseWalletAdapter,\n    isVersionedTransaction,\n    type SendTransactionOptions,\n    type StandardWalletAdapter as StandardWalletAdapterType,\n    type SupportedTransactionVersions,\n    WalletAccountError,\n    type WalletAdapterCompatibleStandardWallet,\n    WalletConfigError,\n    WalletConnectionError,\n    WalletDisconnectedError,\n    WalletDisconnectionError,\n    WalletError,\n    type WalletName,\n    WalletNotConnectedError,\n    WalletNotReadyError,\n    WalletPublicKeyError,\n    WalletReadyState,\n    WalletSendTransactionError,\n    WalletSignInError,\n    WalletSignMessageError,\n    WalletSignTransactionError,\n} from '@solana/wallet-adapter-base';\nimport {\n    SolanaSignAndSendTransaction,\n    type SolanaSignAndSendTransactionFeature,\n    SolanaSignIn,\n    type SolanaSignInInput,\n    type SolanaSignInOutput,\n    SolanaSignMessage,\n    SolanaSignTransaction,\n    type SolanaSignTransactionFeature,\n} from '@solana/wallet-standard-features';\nimport { getChainForEndpoint, getCommitment } from '@solana/wallet-standard-util';\nimport type { Connection, TransactionSignature } from '@solana/web3.js';\nimport { PublicKey, Transaction, VersionedTransaction } from '@solana/web3.js';\nimport type { WalletAccount } from '@wallet-standard/base';\nimport {\n    StandardConnect,\n    type StandardConnectInput,\n    StandardDisconnect,\n    StandardEvents,\n    type StandardEventsListeners,\n} from '@wallet-standard/features';\nimport { arraysEqual } from '@wallet-standard/wallet';\nimport bs58 from 'bs58';\n\n/** TODO: docs */\nexport interface StandardWalletAdapterConfig {\n    wallet: WalletAdapterCompatibleStandardWallet;\n}\n\n/** TODO: docs */\nexport class StandardWalletAdapter extends BaseWalletAdapter implements StandardWalletAdapterType {\n    #account: WalletAccount | null;\n    #publicKey: PublicKey | null;\n    #connecting: boolean;\n    #disconnecting: boolean;\n    #off: (() => void) | null;\n    #supportedTransactionVersions: SupportedTransactionVersions;\n    readonly #wallet: WalletAdapterCompatibleStandardWallet;\n    readonly #readyState: WalletReadyState =\n        typeof window === 'undefined' || typeof document === 'undefined'\n            ? WalletReadyState.Unsupported\n            : WalletReadyState.Installed;\n\n    get name() {\n        return this.#wallet.name as WalletName;\n    }\n\n    get url() {\n        return 'https://github.com/solana-labs/wallet-standard';\n    }\n\n    get icon() {\n        return this.#wallet.icon;\n    }\n\n    get readyState() {\n        return this.#readyState;\n    }\n\n    get publicKey() {\n        return this.#publicKey;\n    }\n\n    get connecting() {\n        return this.#connecting;\n    }\n\n    get supportedTransactionVersions() {\n        return this.#supportedTransactionVersions;\n    }\n\n    get wallet(): WalletAdapterCompatibleStandardWallet {\n        return this.#wallet;\n    }\n\n    get standard() {\n        return true as const;\n    }\n\n    constructor({ wallet }: StandardWalletAdapterConfig) {\n        super();\n\n        this.#wallet = wallet;\n        this.#account = null;\n        this.#publicKey = null;\n        this.#connecting = false;\n        this.#disconnecting = false;\n        this.#off = this.#wallet.features[StandardEvents].on('change', this.#changed);\n\n        this.#reset();\n    }\n\n    destroy(): void {\n        this.#account = null;\n        this.#publicKey = null;\n        this.#connecting = false;\n        this.#disconnecting = false;\n\n        const off = this.#off;\n        if (off) {\n            this.#off = null;\n            off();\n        }\n    }\n\n    async autoConnect(): Promise<void> {\n        return this.#connect({ silent: true });\n    }\n\n    async connect(): Promise<void> {\n        return this.#connect();\n    }\n\n    async #connect(input?: StandardConnectInput): Promise<void> {\n        try {\n            if (this.connected || this.connecting) return;\n            if (this.#readyState !== WalletReadyState.Installed) throw new WalletNotReadyError();\n\n            this.#connecting = true;\n\n            if (!this.#wallet.accounts.length) {\n                try {\n                    await this.#wallet.features[StandardConnect].connect(input);\n                } catch (error: any) {\n                    throw new WalletConnectionError(error?.message, error);\n                }\n            }\n\n            const account = this.#wallet.accounts[0];\n            if (!account) throw new WalletAccountError();\n\n            this.#connected(account);\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        } finally {\n            this.#connecting = false;\n        }\n    }\n\n    async disconnect(): Promise<void> {\n        if (StandardDisconnect in this.#wallet.features) {\n            try {\n                this.#disconnecting = true;\n                await this.#wallet.features[StandardDisconnect].disconnect();\n            } catch (error: any) {\n                this.emit('error', new WalletDisconnectionError(error?.message, error));\n            } finally {\n                this.#disconnecting = false;\n            }\n        }\n\n        this.#disconnected();\n    }\n\n    #connected(account: WalletAccount) {\n        let publicKey: PublicKey;\n        try {\n            // Use account.address instead of account.publicKey since address could be a PDA\n            publicKey = new PublicKey(account.address);\n        } catch (error: any) {\n            throw new WalletPublicKeyError(error?.message, error);\n        }\n\n        this.#account = account;\n        this.#publicKey = publicKey;\n        this.#reset();\n        this.emit('connect', publicKey);\n    }\n\n    #disconnected(): void {\n        this.#account = null;\n        this.#publicKey = null;\n        this.#reset();\n        this.emit('disconnect');\n    }\n\n    #reset() {\n        const supportedTransactionVersions =\n            SolanaSignAndSendTransaction in this.#wallet.features\n                ? this.#wallet.features[SolanaSignAndSendTransaction].supportedTransactionVersions\n                : this.#wallet.features[SolanaSignTransaction].supportedTransactionVersions;\n        this.#supportedTransactionVersions = arraysEqual(supportedTransactionVersions, ['legacy'])\n            ? null\n            : new Set(supportedTransactionVersions);\n\n        if (SolanaSignTransaction in this.#wallet.features && this.#account?.features.includes(SolanaSignTransaction)) {\n            this.signTransaction = this.#signTransaction;\n            this.signAllTransactions = this.#signAllTransactions;\n        } else {\n            delete this.signTransaction;\n            delete this.signAllTransactions;\n        }\n\n        if (SolanaSignMessage in this.#wallet.features && this.#account?.features.includes(SolanaSignMessage)) {\n            this.signMessage = this.#signMessage;\n        } else {\n            delete this.signMessage;\n        }\n\n        if (SolanaSignIn in this.#wallet.features) {\n            this.signIn = this.#signIn;\n        } else {\n            delete this.signIn;\n        }\n    }\n\n    #changed: StandardEventsListeners['change'] = (properties) => {\n        // If accounts have changed on the wallet, reflect this on the adapter.\n        if ('accounts' in properties) {\n            const account = this.#wallet.accounts[0];\n            // If the adapter isn't connected, or is disconnecting, or the first account hasn't changed, do nothing.\n            if (this.#account && !this.#disconnecting && account !== this.#account) {\n                // If there's a connected account, connect the adapter. Otherwise, disconnect it.\n                if (account) {\n                    // Connect the adapter.\n                    this.#connected(account);\n                } else {\n                    // Emit an error because the wallet spontaneously disconnected.\n                    this.emit('error', new WalletDisconnectedError());\n                    // Disconnect the adapter.\n                    this.#disconnected();\n                }\n            }\n        }\n\n        // After reflecting account changes, if features have changed on the wallet, reflect this on the adapter.\n        if ('features' in properties) {\n            this.#reset();\n        }\n    };\n\n    async sendTransaction<T extends Transaction | VersionedTransaction>(\n        transaction: T,\n        connection: Connection,\n        options: SendTransactionOptions = {}\n    ): Promise<TransactionSignature> {\n        try {\n            const account = this.#account;\n            if (!account) throw new WalletNotConnectedError();\n\n            let feature: typeof SolanaSignAndSendTransaction | typeof SolanaSignTransaction;\n            if (SolanaSignAndSendTransaction in this.#wallet.features) {\n                if (account.features.includes(SolanaSignAndSendTransaction)) {\n                    feature = SolanaSignAndSendTransaction;\n                } else if (\n                    SolanaSignTransaction in this.#wallet.features &&\n                    account.features.includes(SolanaSignTransaction)\n                ) {\n                    feature = SolanaSignTransaction;\n                } else {\n                    throw new WalletAccountError();\n                }\n            } else if (SolanaSignTransaction in this.#wallet.features) {\n                if (!account.features.includes(SolanaSignTransaction)) throw new WalletAccountError();\n                feature = SolanaSignTransaction;\n            } else {\n                throw new WalletConfigError();\n            }\n\n            const chain = getChainForEndpoint(connection.rpcEndpoint);\n            if (!account.chains.includes(chain)) throw new WalletSendTransactionError();\n\n            try {\n                const { signers, ...sendOptions } = options;\n\n                let serializedTransaction: Uint8Array;\n                if (isVersionedTransaction(transaction)) {\n                    signers?.length && transaction.sign(signers);\n                    serializedTransaction = transaction.serialize();\n                } else {\n                    transaction = (await this.prepareTransaction(transaction, connection, sendOptions)) as T;\n                    signers?.length && (transaction as Transaction).partialSign(...signers);\n                    serializedTransaction = new Uint8Array(\n                        (transaction as Transaction).serialize({\n                            requireAllSignatures: false,\n                            verifySignatures: false,\n                        })\n                    );\n                }\n\n                if (feature === SolanaSignAndSendTransaction) {\n                    const [output] = await (this.#wallet.features as SolanaSignAndSendTransactionFeature)[\n                        SolanaSignAndSendTransaction\n                    ].signAndSendTransaction({\n                        account,\n                        chain,\n                        transaction: serializedTransaction,\n                        options: {\n                            preflightCommitment: getCommitment(\n                                sendOptions.preflightCommitment || connection.commitment\n                            ),\n                            skipPreflight: sendOptions.skipPreflight,\n                            maxRetries: sendOptions.maxRetries,\n                            minContextSlot: sendOptions.minContextSlot,\n                        },\n                    });\n\n                    return bs58.encode(output!.signature);\n                } else {\n                    const [output] = await (this.#wallet.features as SolanaSignTransactionFeature)[\n                        SolanaSignTransaction\n                    ].signTransaction({\n                        account,\n                        chain,\n                        transaction: serializedTransaction,\n                        options: {\n                            preflightCommitment: getCommitment(\n                                sendOptions.preflightCommitment || connection.commitment\n                            ),\n                            minContextSlot: sendOptions.minContextSlot,\n                        },\n                    });\n\n                    return await connection.sendRawTransaction(output!.signedTransaction, {\n                        ...sendOptions,\n                        preflightCommitment: getCommitment(sendOptions.preflightCommitment || connection.commitment),\n                    });\n                }\n            } catch (error: any) {\n                if (error instanceof WalletError) throw error;\n                throw new WalletSendTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    signTransaction: (<T extends Transaction | VersionedTransaction>(transaction: T) => Promise<T>) | undefined;\n    async #signTransaction<T extends Transaction | VersionedTransaction>(transaction: T): Promise<T> {\n        try {\n            const account = this.#account;\n            if (!account) throw new WalletNotConnectedError();\n\n            if (!(SolanaSignTransaction in this.#wallet.features)) throw new WalletConfigError();\n            if (!account.features.includes(SolanaSignTransaction)) throw new WalletAccountError();\n\n            try {\n                const signedTransactions = await this.#wallet.features[SolanaSignTransaction].signTransaction({\n                    account,\n                    transaction: isVersionedTransaction(transaction)\n                        ? transaction.serialize()\n                        : new Uint8Array(\n                              transaction.serialize({\n                                  requireAllSignatures: false,\n                                  verifySignatures: false,\n                              })\n                          ),\n                });\n\n                const serializedTransaction = signedTransactions[0]!.signedTransaction;\n\n                return (\n                    isVersionedTransaction(transaction)\n                        ? VersionedTransaction.deserialize(serializedTransaction)\n                        : Transaction.from(serializedTransaction)\n                ) as T;\n            } catch (error: any) {\n                if (error instanceof WalletError) throw error;\n                throw new WalletSignTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    signAllTransactions: (<T extends Transaction | VersionedTransaction>(transaction: T[]) => Promise<T[]>) | undefined;\n    async #signAllTransactions<T extends Transaction | VersionedTransaction>(transactions: T[]): Promise<T[]> {\n        try {\n            const account = this.#account;\n            if (!account) throw new WalletNotConnectedError();\n\n            if (!(SolanaSignTransaction in this.#wallet.features)) throw new WalletConfigError();\n            if (!account.features.includes(SolanaSignTransaction)) throw new WalletAccountError();\n\n            try {\n                const signedTransactions = await this.#wallet.features[SolanaSignTransaction].signTransaction(\n                    ...transactions.map((transaction) => ({\n                        account,\n                        transaction: isVersionedTransaction(transaction)\n                            ? transaction.serialize()\n                            : new Uint8Array(\n                                  transaction.serialize({\n                                      requireAllSignatures: false,\n                                      verifySignatures: false,\n                                  })\n                              ),\n                    }))\n                );\n\n                return transactions.map((transaction, index) => {\n                    const signedTransaction = signedTransactions[index]!.signedTransaction;\n\n                    return (\n                        isVersionedTransaction(transaction)\n                            ? VersionedTransaction.deserialize(signedTransaction)\n                            : Transaction.from(signedTransaction)\n                    ) as T;\n                });\n            } catch (error: any) {\n                throw new WalletSignTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    signMessage: ((message: Uint8Array) => Promise<Uint8Array>) | undefined;\n    async #signMessage(message: Uint8Array): Promise<Uint8Array> {\n        try {\n            const account = this.#account;\n            if (!account) throw new WalletNotConnectedError();\n\n            if (!(SolanaSignMessage in this.#wallet.features)) throw new WalletConfigError();\n            if (!account.features.includes(SolanaSignMessage)) throw new WalletAccountError();\n\n            try {\n                const signedMessages = await this.#wallet.features[SolanaSignMessage].signMessage({\n                    account,\n                    message,\n                });\n\n                return signedMessages[0]!.signature;\n            } catch (error: any) {\n                throw new WalletSignMessageError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    signIn: ((input?: SolanaSignInInput) => Promise<SolanaSignInOutput>) | undefined;\n    async #signIn(input: SolanaSignInInput = {}): Promise<SolanaSignInOutput> {\n        try {\n            if (!(SolanaSignIn in this.#wallet.features)) throw new WalletConfigError();\n\n            let output: SolanaSignInOutput | undefined;\n            try {\n                [output] = await this.#wallet.features[SolanaSignIn].signIn(input);\n            } catch (error: any) {\n                throw new WalletSignInError(error?.message, error);\n            }\n\n            if (!output) throw new WalletSignInError();\n            this.#connected(output.account);\n            return output;\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n}\n", "import {\n    isWalletAdapterCompatibleStandardWallet,\n    type StandardWalletAdapter,\n    type WalletAdapterCompatibleStandardWallet,\n} from '@solana/wallet-adapter-base';\n\n/**\n * @deprecated Use `StandardWalletAdapter` from `@solana/wallet-adapter-base` instead.\n *\n * @group Deprecated\n */\nexport type StandardAdapter = StandardWalletAdapter;\n\n/**\n * @deprecated Use `WalletAdapterCompatibleStandardWallet` from `@solana/wallet-adapter-base` instead.\n *\n * @group Deprecated\n */\nexport type WalletAdapterCompatibleWallet = WalletAdapterCompatibleStandardWallet;\n\n/**\n * @deprecated Use `isWalletAdapterCompatibleStandardWallet` from `@solana/wallet-adapter-base` instead.\n *\n * @group Deprecated\n */\nexport const isWalletAdapterCompatibleWallet = isWalletAdapterCompatibleStandardWallet;\n", "import { type Adapter, isVersionedTransaction, WalletReadyState } from '@solana/wallet-adapter-base';\nimport { isSolana<PERSON>hain, type <PERSON><PERSON>Chain } from '@solana/wallet-standard-chains';\nimport {\n    SolanaSignAndSendTransaction,\n    type SolanaSignAndSendTransactionFeature,\n    type SolanaSignAndSendTransactionMethod,\n    type SolanaSignAndSendTransactionOutput,\n    SolanaSignIn,\n    type SolanaSignInFeature,\n    type SolanaSignInMethod,\n    type SolanaSignInOutput,\n    SolanaSignMessage,\n    type SolanaSignMessageFeature,\n    type SolanaSignMessageMethod,\n    type SolanaSignMessageOutput,\n    SolanaSignTransaction,\n    type SolanaSignTransactionFeature,\n    type SolanaSignTransactionMethod,\n    type SolanaSignTransactionOutput,\n    type SolanaTransactionVersion,\n} from '@solana/wallet-standard-features';\nimport { getEndpointForChain } from '@solana/wallet-standard-util';\nimport { Connection, Transaction, VersionedTransaction } from '@solana/web3.js';\nimport { getWallets } from '@wallet-standard/app';\nimport type { Wallet, WalletIcon } from '@wallet-standard/base';\nimport {\n    StandardConnect,\n    type StandardConnectFeature,\n    type StandardConnectMethod,\n    StandardDisconnect,\n    type StandardDisconnectFeature,\n    type StandardDisconnectMethod,\n    StandardEvents,\n    type StandardEventsFeature,\n    type StandardEventsListeners,\n    type StandardEventsNames,\n    type StandardEventsOnMethod,\n} from '@wallet-standard/features';\nimport { arraysEqual, bytesEqual, ReadonlyWalletAccount } from '@wallet-standard/wallet';\nimport bs58 from 'bs58';\n\n/** TODO: docs */\nexport class SolanaWalletAdapterWalletAccount extends ReadonlyWalletAccount {\n    // eslint-disable-next-line no-unused-private-class-members\n    readonly #adapter: Adapter;\n\n    constructor({\n        adapter,\n        address,\n        publicKey,\n        chains,\n    }: {\n        adapter: Adapter;\n        address: string;\n        publicKey: Uint8Array;\n        chains: readonly SolanaChain[];\n    }) {\n        const features: (keyof (SolanaSignAndSendTransactionFeature &\n            SolanaSignTransactionFeature &\n            SolanaSignMessageFeature &\n            SolanaSignInFeature))[] = [SolanaSignAndSendTransaction];\n        if ('signTransaction' in adapter) {\n            features.push(SolanaSignTransaction);\n        }\n        if ('signMessage' in adapter) {\n            features.push(SolanaSignMessage);\n        }\n        if ('signIn' in adapter) {\n            features.push(SolanaSignIn);\n        }\n\n        super({ address, publicKey, chains, features });\n        if (new.target === SolanaWalletAdapterWalletAccount) {\n            Object.freeze(this);\n        }\n\n        this.#adapter = adapter;\n    }\n}\n\n/** TODO: docs */\nexport class SolanaWalletAdapterWallet implements Wallet {\n    readonly #listeners: {\n        [E in StandardEventsNames]?: StandardEventsListeners[E][];\n    } = {};\n    readonly #adapter: Adapter;\n    readonly #supportedTransactionVersions: readonly SolanaTransactionVersion[];\n    readonly #chain: SolanaChain;\n    readonly #endpoint: string | undefined;\n    #account: SolanaWalletAdapterWalletAccount | undefined;\n\n    get version() {\n        return '1.0.0' as const;\n    }\n\n    get name() {\n        return this.#adapter.name;\n    }\n\n    get icon() {\n        return this.#adapter.icon as WalletIcon;\n    }\n\n    get chains() {\n        return [this.#chain];\n    }\n\n    get features(): StandardConnectFeature &\n        StandardDisconnectFeature &\n        StandardEventsFeature &\n        SolanaSignAndSendTransactionFeature &\n        Partial<SolanaSignTransactionFeature & SolanaSignMessageFeature & SolanaSignInFeature> {\n        const features: StandardConnectFeature &\n            StandardDisconnectFeature &\n            StandardEventsFeature &\n            SolanaSignAndSendTransactionFeature = {\n            [StandardConnect]: {\n                version: '1.0.0',\n                connect: this.#connect,\n            },\n            [StandardDisconnect]: {\n                version: '1.0.0',\n                disconnect: this.#disconnect,\n            },\n            [StandardEvents]: {\n                version: '1.0.0',\n                on: this.#on,\n            },\n            [SolanaSignAndSendTransaction]: {\n                version: '1.0.0',\n                supportedTransactionVersions: this.#supportedTransactionVersions,\n                signAndSendTransaction: this.#signAndSendTransaction,\n            },\n        };\n\n        let signTransactionFeature: SolanaSignTransactionFeature | undefined;\n        if ('signTransaction' in this.#adapter) {\n            signTransactionFeature = {\n                [SolanaSignTransaction]: {\n                    version: '1.0.0',\n                    supportedTransactionVersions: this.#supportedTransactionVersions,\n                    signTransaction: this.#signTransaction,\n                },\n            };\n        }\n\n        let signMessageFeature: SolanaSignMessageFeature | undefined;\n        if ('signMessage' in this.#adapter) {\n            signMessageFeature = {\n                [SolanaSignMessage]: {\n                    version: '1.0.0',\n                    signMessage: this.#signMessage,\n                },\n            };\n        }\n\n        let signInFeature: SolanaSignInFeature | undefined;\n        if ('signIn' in this.#adapter) {\n            signInFeature = {\n                [SolanaSignIn]: {\n                    version: '1.0.0',\n                    signIn: this.#signIn,\n                },\n            };\n        }\n\n        return { ...features, ...signTransactionFeature, ...signMessageFeature };\n    }\n\n    get accounts() {\n        return this.#account ? [this.#account] : [];\n    }\n\n    get endpoint() {\n        return this.#endpoint;\n    }\n\n    constructor(adapter: Adapter, chain: SolanaChain, endpoint?: string) {\n        if (new.target === SolanaWalletAdapterWallet) {\n            Object.freeze(this);\n        }\n\n        const supportedTransactionVersions = [...(adapter.supportedTransactionVersions || ['legacy'])];\n        if (!supportedTransactionVersions.length) {\n            supportedTransactionVersions.push('legacy');\n        }\n\n        this.#adapter = adapter;\n        this.#supportedTransactionVersions = supportedTransactionVersions;\n        this.#chain = chain;\n        this.#endpoint = endpoint;\n\n        adapter.on('connect', this.#connected, this);\n        adapter.on('disconnect', this.#disconnected, this);\n\n        this.#connected();\n    }\n\n    destroy(): void {\n        this.#adapter.off('connect', this.#connected, this);\n        this.#adapter.off('disconnect', this.#disconnected, this);\n    }\n\n    #connected(): void {\n        const publicKey = this.#adapter.publicKey?.toBytes();\n        if (publicKey) {\n            const address = this.#adapter.publicKey!.toBase58();\n            const account = this.#account;\n            if (\n                !account ||\n                account.address !== address ||\n                account.chains.includes(this.#chain) ||\n                !bytesEqual(account.publicKey, publicKey)\n            ) {\n                this.#account = new SolanaWalletAdapterWalletAccount({\n                    adapter: this.#adapter,\n                    address,\n                    publicKey,\n                    chains: [this.#chain],\n                });\n                this.#emit('change', { accounts: this.accounts });\n            }\n        }\n    }\n\n    #disconnected(): void {\n        if (this.#account) {\n            this.#account = undefined;\n            this.#emit('change', { accounts: this.accounts });\n        }\n    }\n\n    #connect: StandardConnectMethod = async ({ silent } = {}) => {\n        if (!silent && !this.#adapter.connected) {\n            await this.#adapter.connect();\n        }\n\n        this.#connected();\n\n        return { accounts: this.accounts };\n    };\n\n    #disconnect: StandardDisconnectMethod = async () => {\n        await this.#adapter.disconnect();\n    };\n\n    #on: StandardEventsOnMethod = (event, listener) => {\n        this.#listeners[event]?.push(listener) || (this.#listeners[event] = [listener]);\n        return (): void => this.#off(event, listener);\n    };\n\n    #emit<E extends StandardEventsNames>(event: E, ...args: Parameters<StandardEventsListeners[E]>): void {\n        // eslint-disable-next-line prefer-spread\n        this.#listeners[event]?.forEach((listener) => listener.apply(null, args));\n    }\n\n    #off<E extends StandardEventsNames>(event: E, listener: StandardEventsListeners[E]): void {\n        this.#listeners[event] = this.#listeners[event]?.filter((existingListener) => listener !== existingListener);\n    }\n\n    #deserializeTransaction(serializedTransaction: Uint8Array): Transaction | VersionedTransaction {\n        const transaction = VersionedTransaction.deserialize(serializedTransaction);\n        if (!this.#supportedTransactionVersions.includes(transaction.version))\n            throw new Error('unsupported transaction version');\n        if (transaction.version === 'legacy' && arraysEqual(this.#supportedTransactionVersions, ['legacy']))\n            return Transaction.from(serializedTransaction);\n        return transaction;\n    }\n\n    #signAndSendTransaction: SolanaSignAndSendTransactionMethod = async (...inputs) => {\n        const outputs: SolanaSignAndSendTransactionOutput[] = [];\n\n        if (inputs.length === 1) {\n            const input = inputs[0]!;\n            if (input.account !== this.#account) throw new Error('invalid account');\n            if (!isSolanaChain(input.chain)) throw new Error('invalid chain');\n            const transaction = this.#deserializeTransaction(input.transaction);\n            const { commitment, preflightCommitment, skipPreflight, maxRetries, minContextSlot } = input.options || {};\n            const endpoint = getEndpointForChain(input.chain, this.#endpoint);\n            const connection = new Connection(endpoint, commitment || 'confirmed');\n\n            const latestBlockhash = commitment\n                ? await connection.getLatestBlockhash({\n                      commitment: preflightCommitment || commitment,\n                      minContextSlot,\n                  })\n                : undefined;\n\n            const signature = await this.#adapter.sendTransaction(transaction, connection, {\n                preflightCommitment,\n                skipPreflight,\n                maxRetries,\n                minContextSlot,\n            });\n\n            if (latestBlockhash) {\n                await connection.confirmTransaction(\n                    {\n                        ...latestBlockhash,\n                        signature,\n                    },\n                    commitment || 'confirmed'\n                );\n            }\n\n            outputs.push({ signature: bs58.decode(signature) });\n        } else if (inputs.length > 1) {\n            // Adapters have no `sendAllTransactions` method, so just sign and send each transaction in serial.\n            for (const input of inputs) {\n                outputs.push(...(await this.#signAndSendTransaction(input)));\n            }\n        }\n\n        return outputs;\n    };\n\n    #signTransaction: SolanaSignTransactionMethod = async (...inputs) => {\n        if (!('signTransaction' in this.#adapter)) throw new Error('signTransaction not implemented by adapter');\n        const outputs: SolanaSignTransactionOutput[] = [];\n\n        if (inputs.length === 1) {\n            const input = inputs[0]!;\n            if (input.account !== this.#account) throw new Error('invalid account');\n            if (input.chain && !isSolanaChain(input.chain)) throw new Error('invalid chain');\n            const transaction = this.#deserializeTransaction(input.transaction);\n\n            const signedTransaction = await this.#adapter.signTransaction(transaction);\n\n            const serializedTransaction = isVersionedTransaction(signedTransaction)\n                ? signedTransaction.serialize()\n                : new Uint8Array(\n                      signedTransaction.serialize({\n                          requireAllSignatures: false,\n                          verifySignatures: false,\n                      })\n                  );\n\n            outputs.push({ signedTransaction: serializedTransaction });\n        } else if (inputs.length > 1) {\n            for (const input of inputs) {\n                if (input.account !== this.#account) throw new Error('invalid account');\n                if (input.chain && !isSolanaChain(input.chain)) throw new Error('invalid chain');\n            }\n            const transactions = inputs.map(({ transaction }) => this.#deserializeTransaction(transaction));\n\n            const signedTransactions = await this.#adapter.signAllTransactions(transactions);\n\n            outputs.push(\n                ...signedTransactions.map((signedTransaction) => {\n                    const serializedTransaction = isVersionedTransaction(signedTransaction)\n                        ? signedTransaction.serialize()\n                        : new Uint8Array(\n                              signedTransaction.serialize({\n                                  requireAllSignatures: false,\n                                  verifySignatures: false,\n                              })\n                          );\n\n                    return { signedTransaction: serializedTransaction };\n                })\n            );\n        }\n\n        return outputs;\n    };\n\n    #signMessage: SolanaSignMessageMethod = async (...inputs) => {\n        if (!('signMessage' in this.#adapter)) throw new Error('signMessage not implemented by adapter');\n        const outputs: SolanaSignMessageOutput[] = [];\n\n        if (inputs.length === 1) {\n            const input = inputs[0]!;\n            if (input.account !== this.#account) throw new Error('invalid account');\n\n            const signature = await this.#adapter.signMessage(input.message);\n\n            outputs.push({ signedMessage: input.message, signature });\n        } else if (inputs.length > 1) {\n            // Adapters have no `signAllMessages` method, so just sign each message in serial.\n            for (const input of inputs) {\n                outputs.push(...(await this.#signMessage(input)));\n            }\n        }\n\n        return outputs;\n    };\n\n    #signIn: SolanaSignInMethod = async (...inputs) => {\n        if (!('signIn' in this.#adapter)) throw new Error('signIn not implemented by adapter');\n\n        if (inputs.length > 1) {\n            // Adapters don't support `signIn` with multiple inputs, so just sign in with each input in serial.\n            const outputs: SolanaSignInOutput[] = [];\n            for (const input of inputs) {\n                outputs.push(await this.#adapter.signIn(input));\n            }\n            return outputs;\n        } else {\n            return [await this.#adapter.signIn(inputs[0])];\n        }\n    };\n}\n\n/** TODO: docs */\nexport function registerWalletAdapter(\n    adapter: Adapter,\n    chain: SolanaChain,\n    endpoint?: string,\n    match: (wallet: Wallet) => boolean = (wallet) => wallet.name === adapter.name\n): () => void {\n    const { register, get, on } = getWallets();\n    const destructors: (() => void)[] = [];\n\n    function destroy(): void {\n        destructors.forEach((destroy) => destroy());\n        destructors.length = 0;\n    }\n\n    function setup(): boolean {\n        // If the adapter is unsupported, or a standard wallet that matches it has already been registered, do nothing.\n        if (adapter.readyState === WalletReadyState.Unsupported || get().some(match)) return true;\n\n        // If the adapter isn't ready, try again later.\n        const ready =\n            adapter.readyState === WalletReadyState.Installed || adapter.readyState === WalletReadyState.Loadable;\n        if (ready) {\n            const wallet = new SolanaWalletAdapterWallet(adapter, chain, endpoint);\n            destructors.push(() => wallet.destroy());\n            // Register the adapter wrapped as a standard wallet, and receive a function to unregister the adapter.\n            destructors.push(register(wallet));\n            // Whenever a standard wallet is registered ...\n            destructors.push(\n                on('register', (...wallets) => {\n                    // ... check if it matches the adapter.\n                    if (wallets.some(match)) {\n                        // If it does, remove the event listener and unregister the adapter.\n                        destroy();\n                    }\n                })\n            );\n        }\n        return ready;\n    }\n\n    if (!setup()) {\n        function listener(): void {\n            if (setup()) {\n                adapter.off('readyStateChange', listener);\n            }\n        }\n\n        adapter.on('readyStateChange', listener);\n        destructors.push(() => adapter.off('readyStateChange', listener));\n    }\n\n    return destroy;\n}\n", "import type {\n    DEPRECATED_WalletsCallback,\n    DEPRECATED_WalletsWindow,\n    Wallet,\n    WalletEventsWindow,\n    WindowAppReadyEvent,\n    WindowAppReadyEventAPI,\n} from '@wallet-standard/base';\n\nlet wallets: Wallets | undefined = undefined;\nconst registeredWalletsSet = new Set<Wallet>();\nfunction addRegisteredWallet(wallet: Wallet) {\n    cachedWalletsArray = undefined;\n    registeredWalletsSet.add(wallet);\n}\nfunction removeRegisteredWallet(wallet: Wallet) {\n    cachedWalletsArray = undefined;\n    registeredWalletsSet.delete(wallet);\n}\nconst listeners: { [E in WalletsEventNames]?: WalletsEventsListeners[E][] } = {};\n\n/**\n * Get an API for {@link Wallets.get | getting}, {@link Wallets.on | listening for}, and\n * {@link Wallets.register | registering} {@link \"@wallet-standard/base\".Wallet | Wallets}.\n *\n * When called for the first time --\n *\n * This dispatches a {@link \"@wallet-standard/base\".WindowAppReadyEvent} to notify each Wallet that the app is ready\n * to register it.\n *\n * This also adds a listener for {@link \"@wallet-standard/base\".WindowRegisterWalletEvent} to listen for a notification\n * from each Wallet that the Wallet is ready to be registered by the app.\n *\n * This combination of event dispatch and listener guarantees that each Wallet will be registered synchronously as soon\n * as the app is ready whether the app loads before or after each Wallet.\n *\n * @return API for getting, listening for, and registering Wallets.\n *\n * @group App\n */\nexport function getWallets(): Wallets {\n    if (wallets) return wallets;\n    wallets = Object.freeze({ register, get, on });\n    if (typeof window === 'undefined') return wallets;\n\n    const api = Object.freeze({ register });\n    try {\n        (window as WalletEventsWindow).addEventListener('wallet-standard:register-wallet', ({ detail: callback }) =>\n            callback(api)\n        );\n    } catch (error) {\n        console.error('wallet-standard:register-wallet event listener could not be added\\n', error);\n    }\n    try {\n        (window as WalletEventsWindow).dispatchEvent(new AppReadyEvent(api));\n    } catch (error) {\n        console.error('wallet-standard:app-ready event could not be dispatched\\n', error);\n    }\n\n    return wallets;\n}\n\n/**\n * API for {@link Wallets.get | getting}, {@link Wallets.on | listening for}, and\n * {@link Wallets.register | registering} {@link \"@wallet-standard/base\".Wallet | Wallets}.\n *\n * @group App\n */\nexport interface Wallets {\n    /**\n     * Get all Wallets that have been registered.\n     *\n     * @return Registered Wallets.\n     */\n    get(): readonly Wallet[];\n\n    /**\n     * Add an event listener and subscribe to events for Wallets that are\n     * {@link WalletsEventsListeners.register | registered} and\n     * {@link WalletsEventsListeners.unregister | unregistered}.\n     *\n     * @param event    Event type to listen for. {@link WalletsEventsListeners.register | `register`} and\n     * {@link WalletsEventsListeners.unregister | `unregister`} are the only event types.\n     * @param listener Function that will be called when an event of the type is emitted.\n     *\n     * @return\n     * `off` function which may be called to remove the event listener and unsubscribe from events.\n     *\n     * As with all event listeners, be careful to avoid memory leaks.\n     */\n    on<E extends WalletsEventNames>(event: E, listener: WalletsEventsListeners[E]): () => void;\n\n    /**\n     * Register Wallets. This can be used to programmatically wrap non-standard wallets as Standard Wallets.\n     *\n     * Apps generally do not need to, and should not, call this.\n     *\n     * @param wallets Wallets to register.\n     *\n     * @return\n     * `unregister` function which may be called to programmatically unregister the registered Wallets.\n     *\n     * Apps generally do not need to, and should not, call this.\n     */\n    register(...wallets: Wallet[]): () => void;\n}\n\n/**\n * Types of event listeners of the {@link Wallets} API.\n *\n * @group App\n */\nexport interface WalletsEventsListeners {\n    /**\n     * Emitted when Wallets are registered.\n     *\n     * @param wallets Wallets that were registered.\n     */\n    register(...wallets: Wallet[]): void;\n\n    /**\n     * Emitted when Wallets are unregistered.\n     *\n     * @param wallets Wallets that were unregistered.\n     */\n    unregister(...wallets: Wallet[]): void;\n}\n\n/**\n * Names of {@link WalletsEventsListeners} that can be listened for.\n *\n * @group App\n */\nexport type WalletsEventNames = keyof WalletsEventsListeners;\n\n/**\n * @deprecated Use {@link WalletsEventsListeners} instead.\n *\n * @group Deprecated\n */\nexport type WalletsEvents = WalletsEventsListeners;\n\nfunction register(...wallets: Wallet[]): () => void {\n    // Filter out wallets that have already been registered.\n    // This prevents the same wallet from being registered twice, but it also prevents wallets from being\n    // unregistered by reusing a reference to the wallet to obtain the unregister function for it.\n    wallets = wallets.filter((wallet) => !registeredWalletsSet.has(wallet));\n    // If there are no new wallets to register, just return a no-op unregister function.\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    if (!wallets.length) return () => {};\n\n    wallets.forEach((wallet) => addRegisteredWallet(wallet));\n    listeners['register']?.forEach((listener) => guard(() => listener(...wallets)));\n    // Return a function that unregisters the registered wallets.\n    return function unregister(): void {\n        wallets.forEach((wallet) => removeRegisteredWallet(wallet));\n        listeners['unregister']?.forEach((listener) => guard(() => listener(...wallets)));\n    };\n}\n\nlet cachedWalletsArray: readonly Wallet[] | undefined;\nfunction get(): readonly Wallet[] {\n    if (!cachedWalletsArray) {\n        cachedWalletsArray = [...registeredWalletsSet];\n    }\n    return cachedWalletsArray;\n}\n\nfunction on<E extends WalletsEventNames>(event: E, listener: WalletsEventsListeners[E]): () => void {\n    listeners[event]?.push(listener) || (listeners[event] = [listener]);\n    // Return a function that removes the event listener.\n    return function off(): void {\n        listeners[event] = listeners[event]?.filter((existingListener) => listener !== existingListener);\n    };\n}\n\nfunction guard(callback: () => void) {\n    try {\n        callback();\n    } catch (error) {\n        console.error(error);\n    }\n}\n\nclass AppReadyEvent extends Event implements WindowAppReadyEvent {\n    readonly #detail: WindowAppReadyEventAPI;\n\n    get detail() {\n        return this.#detail;\n    }\n\n    get type() {\n        return 'wallet-standard:app-ready' as const;\n    }\n\n    constructor(api: WindowAppReadyEventAPI) {\n        super('wallet-standard:app-ready', {\n            bubbles: false,\n            cancelable: false,\n            composed: false,\n        });\n        this.#detail = api;\n    }\n\n    /** @deprecated */\n    preventDefault(): never {\n        throw new Error('preventDefault cannot be called');\n    }\n\n    /** @deprecated */\n    stopImmediatePropagation(): never {\n        throw new Error('stopImmediatePropagation cannot be called');\n    }\n\n    /** @deprecated */\n    stopPropagation(): never {\n        throw new Error('stopPropagation cannot be called');\n    }\n}\n\n/**\n * @deprecated Use {@link getWallets} instead.\n *\n * @group Deprecated\n */\nexport function DEPRECATED_getWallets(): Wallets {\n    if (wallets) return wallets;\n    wallets = getWallets();\n    if (typeof window === 'undefined') return wallets;\n\n    const callbacks = (window as DEPRECATED_WalletsWindow).navigator.wallets || [];\n    if (!Array.isArray(callbacks)) {\n        console.error('window.navigator.wallets is not an array');\n        return wallets;\n    }\n\n    const { register } = wallets;\n    const push = (...callbacks: DEPRECATED_WalletsCallback[]): void =>\n        callbacks.forEach((callback) => guard(() => callback({ register })));\n    try {\n        Object.defineProperty((window as DEPRECATED_WalletsWindow).navigator, 'wallets', {\n            value: Object.freeze({ push }),\n        });\n    } catch (error) {\n        console.error('window.navigator.wallets could not be set');\n        return wallets;\n    }\n\n    push(...callbacks);\n    return wallets;\n}\n", "import type { Adapter, WalletName } from '@solana/wallet-adapter-base';\nimport { isWalletAdapterCompatibleWallet, StandardWalletAdapter } from '@solana/wallet-standard-wallet-adapter-base';\nimport { DEPRECATED_getWallets } from '@wallet-standard/app';\nimport type { Wallet } from '@wallet-standard/base';\nimport { useEffect, useMemo, useRef, useState } from 'react';\n\nexport function useStandardWalletAdapters(adapters: Adapter[]): Adapter[] {\n    const warnings = useConstant(() => new Set<WalletName>());\n    const { get, on } = useConstant(() => DEPRECATED_getWallets());\n    const [standardAdapters, setStandardAdapters] = useState(() => wrapWalletsWithAdapters(get()));\n\n    useEffect(() => {\n        const listeners = [\n            on('register', (...wallets) =>\n                setStandardAdapters((standardAdapters) => [...standardAdapters, ...wrapWalletsWithAdapters(wallets)])\n            ),\n            on('unregister', (...wallets) =>\n                setStandardAdapters((standardAdapters) =>\n                    standardAdapters.filter((standardAdapter) =>\n                        wallets.some((wallet) => wallet === standardAdapter.wallet)\n                    )\n                )\n            ),\n        ];\n        return () => listeners.forEach((off) => off());\n    }, [on]);\n\n    const prevStandardAdapters = usePrevious(standardAdapters);\n    useEffect(() => {\n        if (!prevStandardAdapters) return;\n\n        const currentAdapters = new Set(standardAdapters);\n        const removedAdapters = new Set(\n            prevStandardAdapters.filter((previousAdapter) => !currentAdapters.has(previousAdapter))\n        );\n        removedAdapters.forEach((adapter) => adapter.destroy());\n    }, [prevStandardAdapters, standardAdapters]);\n\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    useEffect(() => () => standardAdapters.forEach((adapter) => adapter.destroy()), []);\n\n    return useMemo(\n        () => [\n            ...standardAdapters,\n            ...adapters.filter(({ name }) => {\n                if (standardAdapters.some((standardAdapter) => standardAdapter.name === name)) {\n                    if (!warnings.has(name)) {\n                        warnings.add(name);\n                        console.warn(\n                            `${name} was registered as a Standard Wallet. The Wallet Adapter for ${name} can be removed from your app.`\n                        );\n                    }\n                    return false;\n                }\n                return true;\n            }),\n        ],\n        [standardAdapters, adapters, warnings]\n    );\n}\n\nfunction useConstant<T>(fn: () => T): T {\n    const ref = useRef<{ value: T }>(undefined);\n    if (ref.current === undefined) {\n        ref.current = { value: fn() };\n    }\n    return ref.current.value;\n}\n\nfunction usePrevious<T>(state: T): T | undefined {\n    const ref = useRef<T>(undefined);\n    useEffect(() => {\n        ref.current = state;\n    });\n    return ref.current;\n}\n\nfunction wrapWalletsWithAdapters(wallets: readonly Wallet[]): readonly StandardWalletAdapter[] {\n    return wallets.filter(isWalletAdapterCompatibleWallet).map((wallet) => new StandardWalletAdapter({ wallet }));\n}\n", "import {\n    createDefaultAddressSelector,\n    createDefaultAuthorizationResultCache,\n    createDefaultWalletNotFound<PERSON>andler,\n    SolanaMobileWalletAdapter,\n    SolanaMobileWalletAdapterWalletName,\n} from '@solana-mobile/wallet-adapter-mobile';\nimport { type Adapter, type WalletError, type WalletName } from '@solana/wallet-adapter-base';\nimport { useStandardWalletAdapters } from '@solana/wallet-standard-wallet-adapter-react';\nimport React, { type ReactNode, useCallback, useEffect, useMemo, useRef } from 'react';\nimport getEnvironment, { Environment } from './getEnvironment.js';\nimport getInferredClusterFromEndpoint from './getInferredClusterFromEndpoint.js';\nimport { useConnection } from './useConnection.js';\nimport { useLocalStorage } from './useLocalStorage.js';\nimport { WalletProviderBase } from './WalletProviderBase.js';\n\nexport interface WalletProviderProps {\n    children: ReactNode;\n    wallets: Adapter[];\n    autoConnect?: boolean | ((adapter: Adapter) => Promise<boolean>);\n    localStorageKey?: string;\n    onError?: (error: WalletError, adapter?: Adapter) => void;\n}\n\nlet _userAgent: string | null;\nfunction getUserAgent() {\n    if (_userAgent === undefined) {\n        _userAgent = globalThis.navigator?.userAgent ?? null;\n    }\n    return _userAgent;\n}\n\nfunction getIsMobile(adapters: Adapter[]) {\n    const userAgentString = getUserAgent();\n    return getEnvironment({ adapters, userAgentString }) === Environment.MOBILE_WEB;\n}\n\nfunction getUriForAppIdentity() {\n    const location = globalThis.location;\n    if (!location) return;\n    return `${location.protocol}//${location.host}`;\n}\n\nexport function WalletProvider({\n    children,\n    wallets: adapters,\n    autoConnect,\n    localStorageKey = 'walletName',\n    onError,\n}: WalletProviderProps) {\n    const { connection } = useConnection();\n    const adaptersWithStandardAdapters = useStandardWalletAdapters(adapters);\n    const mobileWalletAdapter = useMemo(() => {\n        if (!getIsMobile(adaptersWithStandardAdapters)) {\n            return null;\n        }\n        const existingMobileWalletAdapter = adaptersWithStandardAdapters.find(\n            (adapter) => adapter.name === SolanaMobileWalletAdapterWalletName\n        );\n        if (existingMobileWalletAdapter) {\n            return existingMobileWalletAdapter;\n        }\n        return new SolanaMobileWalletAdapter({\n            addressSelector: createDefaultAddressSelector(),\n            appIdentity: {\n                uri: getUriForAppIdentity(),\n            },\n            authorizationResultCache: createDefaultAuthorizationResultCache(),\n            cluster: getInferredClusterFromEndpoint(connection?.rpcEndpoint),\n            onWalletNotFound: createDefaultWalletNotFoundHandler(),\n        });\n    }, [adaptersWithStandardAdapters, connection?.rpcEndpoint]);\n    const adaptersWithMobileWalletAdapter = useMemo(() => {\n        if (mobileWalletAdapter == null || adaptersWithStandardAdapters.indexOf(mobileWalletAdapter) !== -1) {\n            return adaptersWithStandardAdapters;\n        }\n        return [mobileWalletAdapter, ...adaptersWithStandardAdapters];\n    }, [adaptersWithStandardAdapters, mobileWalletAdapter]);\n    const [walletName, setWalletName] = useLocalStorage<WalletName | null>(localStorageKey, null);\n    const adapter = useMemo(\n        () => adaptersWithMobileWalletAdapter.find((a) => a.name === walletName) ?? null,\n        [adaptersWithMobileWalletAdapter, walletName]\n    );\n    const changeWallet = useCallback(\n        (nextWalletName: WalletName<string> | null) => {\n            if (walletName === nextWalletName) return;\n            if (\n                adapter &&\n                // Selecting a wallet other than the mobile wallet adapter is not\n                // sufficient reason to call `disconnect` on the mobile wallet adapter.\n                // Calling `disconnect` on the mobile wallet adapter causes the entire\n                // authorization store to be wiped.\n                adapter.name !== SolanaMobileWalletAdapterWalletName\n            ) {\n                adapter.disconnect();\n            }\n            setWalletName(nextWalletName);\n        },\n        [adapter, setWalletName, walletName]\n    );\n    useEffect(() => {\n        if (!adapter) return;\n        function handleDisconnect() {\n            if (isUnloadingRef.current) return;\n            setWalletName(null);\n        }\n        adapter.on('disconnect', handleDisconnect);\n        return () => {\n            adapter.off('disconnect', handleDisconnect);\n        };\n    }, [adapter, adaptersWithStandardAdapters, setWalletName, walletName]);\n    const hasUserSelectedAWallet = useRef(false);\n    const handleAutoConnectRequest = useMemo(() => {\n        if (!autoConnect || !adapter) return;\n        return async () => {\n            // If autoConnect is true or returns true, use the default autoConnect behavior.\n            if (autoConnect === true || (await autoConnect(adapter))) {\n                if (hasUserSelectedAWallet.current) {\n                    await adapter.connect();\n                } else {\n                    await adapter.autoConnect();\n                }\n            }\n        };\n    }, [autoConnect, adapter]);\n    const isUnloadingRef = useRef(false);\n    useEffect(() => {\n        if (walletName === SolanaMobileWalletAdapterWalletName && getIsMobile(adaptersWithStandardAdapters)) {\n            isUnloadingRef.current = false;\n            return;\n        }\n        function handleBeforeUnload() {\n            isUnloadingRef.current = true;\n        }\n        /**\n         * Some wallets fire disconnection events when the window unloads. Since there's no way to\n         * distinguish between a disconnection event received because a user initiated it, and one\n         * that was received because they've closed the window, we have to track window unload\n         * events themselves. Downstream components use this information to decide whether to act\n         * upon or drop wallet events and errors.\n         */\n        window.addEventListener('beforeunload', handleBeforeUnload);\n        return () => {\n            window.removeEventListener('beforeunload', handleBeforeUnload);\n        };\n    }, [adaptersWithStandardAdapters, walletName]);\n    const handleConnectError = useCallback(() => {\n        if (adapter) {\n            // If any error happens while connecting, unset the adapter.\n            changeWallet(null);\n        }\n    }, [adapter, changeWallet]);\n    const selectWallet = useCallback(\n        (walletName: WalletName | null) => {\n            hasUserSelectedAWallet.current = true;\n            changeWallet(walletName);\n        },\n        [changeWallet]\n    );\n    return (\n        <WalletProviderBase\n            wallets={adaptersWithMobileWalletAdapter}\n            adapter={adapter}\n            isUnloadingRef={isUnloadingRef}\n            onAutoConnectRequest={handleAutoConnectRequest}\n            onConnectError={handleConnectError}\n            onError={onError}\n            onSelectWallet={selectWallet}\n        >\n            {children}\n        </WalletProviderBase>\n    );\n}\n", "import { SolanaMobileWalletAdapterWalletName } from '@solana-mobile/wallet-adapter-mobile';\nimport { type Adapter, WalletReadyState } from '@solana/wallet-adapter-base';\n\nexport enum Environment {\n    DESKTOP_WEB,\n    MOBILE_WEB,\n}\n\ntype Config = Readonly<{\n    adapters: Adapter[];\n    userAgentString: string | null;\n}>;\n\nfunction isWebView(userAgentString: string) {\n    return /(WebView|Version\\/.+(Chrome)\\/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)|; wv\\).+(Chrome)\\/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+))/i.test(\n        userAgentString\n    );\n}\n\nexport default function getEnvironment({ adapters, userAgentString }: Config): Environment {\n    if (\n        adapters.some(\n            (adapter) =>\n                adapter.name !== SolanaMobileWalletAdapterWalletName &&\n                adapter.readyState === WalletReadyState.Installed\n        )\n    ) {\n        /**\n         * There are only two ways a browser extension adapter should be able to reach `Installed` status:\n         *\n         *     1. Its browser extension is installed.\n         *     2. The app is running on a mobile wallet's in-app browser.\n         *\n         * In either case, we consider the environment to be desktop-like.\n         */\n        return Environment.DESKTOP_WEB;\n    }\n    if (\n        userAgentString &&\n        // Step 1: Check whether we're on a platform that supports MWA at all.\n        /android/i.test(userAgentString) &&\n        // Step 2: Determine that we are *not* running in a WebView.\n        !isWebView(userAgentString)\n    ) {\n        return Environment.MOBILE_WEB;\n    } else {\n        return Environment.DESKTOP_WEB;\n    }\n}\n", "import { type Cluster } from '@solana/web3.js';\n\nexport default function getInferredClusterFromEndpoint(endpoint?: string): Cluster {\n    if (!endpoint) {\n        return 'mainnet-beta';\n    }\n    if (/devnet/i.test(endpoint)) {\n        return 'devnet';\n    } else if (/testnet/i.test(endpoint)) {\n        return 'testnet';\n    } else {\n        return 'mainnet-beta';\n    }\n}\n", "import {\n    type Adapter,\n    type MessageSignerWalletAdapterProps,\n    type SignerWalletAdapterProps,\n    type SignInMessageSignerWalletAdapterProps,\n    type WalletAdapterProps,\n    type WalletError,\n    type WalletName,\n    WalletNotConnectedError,\n    WalletNotReadyError,\n    WalletReadyState,\n} from '@solana/wallet-adapter-base';\nimport { type PublicKey } from '@solana/web3.js';\nimport React, { type ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport { WalletNotSelectedError } from './errors.js';\nimport { WalletContext } from './useWallet.js';\n\nexport interface WalletProviderBaseProps {\n    children: ReactNode;\n    wallets: Adapter[];\n    adapter: Adapter | null;\n    isUnloadingRef: React.RefObject<boolean>;\n    // NOTE: The presence/absence of this handler implies that auto-connect is enabled/disabled.\n    onAutoConnectRequest?: () => Promise<void>;\n    onConnectError: () => void;\n    onError?: (error: WalletError, adapter?: Adapter) => void;\n    onSelectWallet: (walletName: WalletName | null) => void;\n}\n\nexport function WalletProviderBase({\n    children,\n    wallets: adapters,\n    adapter,\n    isUnloadingRef,\n    onAutoConnectRequest,\n    onConnectError,\n    onError,\n    onSelectWallet,\n}: WalletProviderBaseProps) {\n    const isConnectingRef = useRef(false);\n    const [connecting, setConnecting] = useState(false);\n    const isDisconnectingRef = useRef(false);\n    const [disconnecting, setDisconnecting] = useState(false);\n    const [publicKey, setPublicKey] = useState(() => adapter?.publicKey ?? null);\n    const [connected, setConnected] = useState(() => adapter?.connected ?? false);\n\n    /**\n     * Store the error handlers as refs so that a change in the\n     * custom error handler does not recompute other dependencies.\n     */\n    const onErrorRef = useRef(onError);\n    useEffect(() => {\n        onErrorRef.current = onError;\n        return () => {\n            onErrorRef.current = undefined;\n        };\n    }, [onError]);\n    const handleErrorRef = useRef((error: WalletError, adapter?: Adapter) => {\n        if (!isUnloadingRef.current) {\n            if (onErrorRef.current) {\n                onErrorRef.current(error, adapter);\n            } else {\n                console.error(error, adapter);\n                if (error instanceof WalletNotReadyError && typeof window !== 'undefined' && adapter) {\n                    window.open(adapter.url, '_blank');\n                }\n            }\n        }\n        return error;\n    });\n\n    // Wrap adapters to conform to the `Wallet` interface\n    const [wallets, setWallets] = useState(() =>\n        adapters\n            .map((adapter) => ({\n                adapter,\n                readyState: adapter.readyState,\n            }))\n            .filter(({ readyState }) => readyState !== WalletReadyState.Unsupported)\n    );\n\n    // When the adapters change, start to listen for changes to their `readyState`\n    useEffect(() => {\n        // When the adapters change, wrap them to conform to the `Wallet` interface\n        setWallets((wallets) =>\n            adapters\n                .map((adapter, index) => {\n                    const wallet = wallets[index];\n                    // If the wallet hasn't changed, return the same instance\n                    return wallet && wallet.adapter === adapter && wallet.readyState === adapter.readyState\n                        ? wallet\n                        : {\n                              adapter: adapter,\n                              readyState: adapter.readyState,\n                          };\n                })\n                .filter(({ readyState }) => readyState !== WalletReadyState.Unsupported)\n        );\n        function handleReadyStateChange(this: Adapter, readyState: WalletReadyState) {\n            setWallets((prevWallets) => {\n                const index = prevWallets.findIndex(({ adapter }) => adapter === this);\n                if (index === -1) return prevWallets;\n\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                const { adapter } = prevWallets[index]!;\n                return [\n                    ...prevWallets.slice(0, index),\n                    { adapter, readyState },\n                    ...prevWallets.slice(index + 1),\n                ].filter(({ readyState }) => readyState !== WalletReadyState.Unsupported);\n            });\n        }\n        adapters.forEach((adapter) => adapter.on('readyStateChange', handleReadyStateChange, adapter));\n        return () => {\n            adapters.forEach((adapter) => adapter.off('readyStateChange', handleReadyStateChange, adapter));\n        };\n    }, [adapter, adapters]);\n\n    const wallet = useMemo(() => wallets.find((wallet) => wallet.adapter === adapter) ?? null, [adapter, wallets]);\n\n    // Setup and teardown event listeners when the adapter changes\n    useEffect(() => {\n        if (!adapter) return;\n\n        const handleConnect = (publicKey: PublicKey) => {\n            setPublicKey(publicKey);\n            isConnectingRef.current = false;\n            setConnecting(false);\n            setConnected(true);\n            isDisconnectingRef.current = false;\n            setDisconnecting(false);\n        };\n\n        const handleDisconnect = () => {\n            if (isUnloadingRef.current) return;\n\n            setPublicKey(null);\n            isConnectingRef.current = false;\n            setConnecting(false);\n            setConnected(false);\n            isDisconnectingRef.current = false;\n            setDisconnecting(false);\n        };\n\n        const handleError = (error: WalletError) => {\n            handleErrorRef.current(error, adapter);\n        };\n\n        adapter.on('connect', handleConnect);\n        adapter.on('disconnect', handleDisconnect);\n        adapter.on('error', handleError);\n\n        return () => {\n            adapter.off('connect', handleConnect);\n            adapter.off('disconnect', handleDisconnect);\n            adapter.off('error', handleError);\n\n            handleDisconnect();\n        };\n    }, [adapter, isUnloadingRef]);\n\n    // When the adapter changes, clear the `autoConnect` tracking flag\n    const didAttemptAutoConnectRef = useRef(false);\n    useEffect(() => {\n        return () => {\n            didAttemptAutoConnectRef.current = false;\n        };\n    }, [adapter]);\n\n    // If auto-connect is enabled, request to connect when the adapter changes and is ready\n    useEffect(() => {\n        if (\n            didAttemptAutoConnectRef.current ||\n            isConnectingRef.current ||\n            connected ||\n            !onAutoConnectRequest ||\n            !(wallet?.readyState === WalletReadyState.Installed || wallet?.readyState === WalletReadyState.Loadable)\n        )\n            return;\n\n        isConnectingRef.current = true;\n        setConnecting(true);\n        didAttemptAutoConnectRef.current = true;\n        (async function () {\n            try {\n                await onAutoConnectRequest();\n            } catch {\n                onConnectError();\n                // Drop the error. It will be caught by `handleError` anyway.\n            } finally {\n                setConnecting(false);\n                isConnectingRef.current = false;\n            }\n        })();\n    }, [connected, onAutoConnectRequest, onConnectError, wallet]);\n\n    // Send a transaction using the provided connection\n    const sendTransaction: WalletAdapterProps['sendTransaction'] = useCallback(\n        async (transaction, connection, options) => {\n            if (!adapter) throw handleErrorRef.current(new WalletNotSelectedError());\n            if (!connected) throw handleErrorRef.current(new WalletNotConnectedError(), adapter);\n            return await adapter.sendTransaction(transaction, connection, options);\n        },\n        [adapter, connected]\n    );\n\n    // Sign a transaction if the wallet supports it\n    const signTransaction: SignerWalletAdapterProps['signTransaction'] | undefined = useMemo(\n        () =>\n            adapter && 'signTransaction' in adapter\n                ? async (transaction) => {\n                      if (!connected) throw handleErrorRef.current(new WalletNotConnectedError(), adapter);\n                      return await adapter.signTransaction(transaction);\n                  }\n                : undefined,\n        [adapter, connected]\n    );\n\n    // Sign multiple transactions if the wallet supports it\n    const signAllTransactions: SignerWalletAdapterProps['signAllTransactions'] | undefined = useMemo(\n        () =>\n            adapter && 'signAllTransactions' in adapter\n                ? async (transactions) => {\n                      if (!connected) throw handleErrorRef.current(new WalletNotConnectedError(), adapter);\n                      return await adapter.signAllTransactions(transactions);\n                  }\n                : undefined,\n        [adapter, connected]\n    );\n\n    // Sign an arbitrary message if the wallet supports it\n    const signMessage: MessageSignerWalletAdapterProps['signMessage'] | undefined = useMemo(\n        () =>\n            adapter && 'signMessage' in adapter\n                ? async (message) => {\n                      if (!connected) throw handleErrorRef.current(new WalletNotConnectedError(), adapter);\n                      return await adapter.signMessage(message);\n                  }\n                : undefined,\n        [adapter, connected]\n    );\n\n    // Sign in if the wallet supports it\n    const signIn: SignInMessageSignerWalletAdapterProps['signIn'] | undefined = useMemo(\n        () =>\n            adapter && 'signIn' in adapter\n                ? async (input) => {\n                      return await adapter.signIn(input);\n                  }\n                : undefined,\n        [adapter]\n    );\n\n    const handleConnect = useCallback(async () => {\n        if (isConnectingRef.current || isDisconnectingRef.current || wallet?.adapter.connected) return;\n        if (!wallet) throw handleErrorRef.current(new WalletNotSelectedError());\n        const { adapter, readyState } = wallet;\n        if (!(readyState === WalletReadyState.Installed || readyState === WalletReadyState.Loadable))\n            throw handleErrorRef.current(new WalletNotReadyError(), adapter);\n        isConnectingRef.current = true;\n        setConnecting(true);\n        try {\n            await adapter.connect();\n        } catch (e) {\n            onConnectError();\n            throw e;\n        } finally {\n            setConnecting(false);\n            isConnectingRef.current = false;\n        }\n    }, [onConnectError, wallet]);\n\n    const handleDisconnect = useCallback(async () => {\n        if (isDisconnectingRef.current) return;\n        if (!adapter) return;\n        isDisconnectingRef.current = true;\n        setDisconnecting(true);\n        try {\n            await adapter.disconnect();\n        } finally {\n            setDisconnecting(false);\n            isDisconnectingRef.current = false;\n        }\n    }, [adapter]);\n\n    return (\n        <WalletContext.Provider\n            value={{\n                autoConnect: !!onAutoConnectRequest,\n                wallets,\n                wallet,\n                publicKey,\n                connected,\n                connecting,\n                disconnecting,\n                select: onSelectWallet,\n                connect: handleConnect,\n                disconnect: handleDisconnect,\n                sendTransaction,\n                signTransaction,\n                signAllTransactions,\n                signMessage,\n                signIn,\n            }}\n        >\n            {children}\n        </WalletContext.Provider>\n    );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAIA,WAAO,UAAU,WAAY;AAC3B,aAAO,OAAO,YAAY,cAAc,QAAQ,aAAa,QAAQ,UAAU;AAAA,IACjF;AAAA;AAAA;;;ACNA;AAAA;AAAA,QAAI;AACJ,QAAM,kBAAkB;AAAA,MACtB;AAAA;AAAA,MACA;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC1C;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC7C;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MACtD;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,IACxD;AAQA,YAAQ,gBAAgB,SAAS,cAAe,SAAS;AACvD,UAAI,CAAC,QAAS,OAAM,IAAI,MAAM,uCAAuC;AACrE,UAAI,UAAU,KAAK,UAAU,GAAI,OAAM,IAAI,MAAM,2CAA2C;AAC5F,aAAO,UAAU,IAAI;AAAA,IACvB;AAQA,YAAQ,0BAA0B,SAAS,wBAAyB,SAAS;AAC3E,aAAO,gBAAgB,OAAO;AAAA,IAChC;AAQA,YAAQ,cAAc,SAAU,MAAM;AACpC,UAAI,QAAQ;AAEZ,aAAO,SAAS,GAAG;AACjB;AACA,kBAAU;AAAA,MACZ;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,oBAAoB,SAAS,kBAAmB,GAAG;AACzD,UAAI,OAAO,MAAM,YAAY;AAC3B,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AAEA,uBAAiB;AAAA,IACnB;AAEA,YAAQ,qBAAqB,WAAY;AACvC,aAAO,OAAO,mBAAmB;AAAA,IACnC;AAEA,YAAQ,SAAS,SAAS,OAAQ,OAAO;AACvC,aAAO,eAAe,KAAK;AAAA,IAC7B;AAAA;AAAA;;;AC9DA;AAAA;AAAA,YAAQ,IAAI,EAAE,KAAK,EAAE;AACrB,YAAQ,IAAI,EAAE,KAAK,EAAE;AACrB,YAAQ,IAAI,EAAE,KAAK,EAAE;AACrB,YAAQ,IAAI,EAAE,KAAK,EAAE;AAErB,aAAS,WAAY,QAAQ;AAC3B,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC;AAEA,YAAM,QAAQ,OAAO,YAAY;AAEjC,cAAQ,OAAO;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QAEjB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QAEjB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QAEjB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QAEjB;AACE,gBAAM,IAAI,MAAM,uBAAuB,MAAM;AAAA,MACjD;AAAA,IACF;AAEA,YAAQ,UAAU,SAAS,QAAS,OAAO;AACzC,aAAO,SAAS,OAAO,MAAM,QAAQ,eACnC,MAAM,OAAO,KAAK,MAAM,MAAM;AAAA,IAClC;AAEA,YAAQ,OAAO,SAAS,KAAM,OAAO,cAAc;AACjD,UAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1B,eAAO;AAAA,MACT;AAEA,UAAI;AACF,eAAO,WAAW,KAAK;AAAA,MACzB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;ACjDA;AAAA;AAAA,aAAS,YAAa;AACpB,WAAK,SAAS,CAAC;AACf,WAAK,SAAS;AAAA,IAChB;AAEA,cAAU,YAAY;AAAA,MAEpB,KAAK,SAAU,OAAO;AACpB,cAAM,WAAW,KAAK,MAAM,QAAQ,CAAC;AACrC,gBAAS,KAAK,OAAO,QAAQ,MAAO,IAAI,QAAQ,IAAM,OAAO;AAAA,MAC/D;AAAA,MAEA,KAAK,SAAU,KAAK,QAAQ;AAC1B,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,eAAK,QAAS,QAAS,SAAS,IAAI,IAAM,OAAO,CAAC;AAAA,QACpD;AAAA,MACF;AAAA,MAEA,iBAAiB,WAAY;AAC3B,eAAO,KAAK;AAAA,MACd;AAAA,MAEA,QAAQ,SAAU,KAAK;AACrB,cAAM,WAAW,KAAK,MAAM,KAAK,SAAS,CAAC;AAC3C,YAAI,KAAK,OAAO,UAAU,UAAU;AAClC,eAAK,OAAO,KAAK,CAAC;AAAA,QACpB;AAEA,YAAI,KAAK;AACP,eAAK,OAAO,QAAQ,KAAM,QAAU,KAAK,SAAS;AAAA,QACpD;AAEA,aAAK;AAAA,MACP;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpCjB;AAAA;AAKA,aAAS,UAAW,MAAM;AACxB,UAAI,CAAC,QAAQ,OAAO,GAAG;AACrB,cAAM,IAAI,MAAM,mDAAmD;AAAA,MACrE;AAEA,WAAK,OAAO;AACZ,WAAK,OAAO,IAAI,WAAW,OAAO,IAAI;AACtC,WAAK,cAAc,IAAI,WAAW,OAAO,IAAI;AAAA,IAC/C;AAWA,cAAU,UAAU,MAAM,SAAU,KAAK,KAAK,OAAO,UAAU;AAC7D,YAAM,QAAQ,MAAM,KAAK,OAAO;AAChC,WAAK,KAAK,KAAK,IAAI;AACnB,UAAI,SAAU,MAAK,YAAY,KAAK,IAAI;AAAA,IAC1C;AASA,cAAU,UAAU,MAAM,SAAU,KAAK,KAAK;AAC5C,aAAO,KAAK,KAAK,MAAM,KAAK,OAAO,GAAG;AAAA,IACxC;AAUA,cAAU,UAAU,MAAM,SAAU,KAAK,KAAK,OAAO;AACnD,WAAK,KAAK,MAAM,KAAK,OAAO,GAAG,KAAK;AAAA,IACtC;AASA,cAAU,UAAU,aAAa,SAAU,KAAK,KAAK;AACnD,aAAO,KAAK,YAAY,MAAM,KAAK,OAAO,GAAG;AAAA,IAC/C;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChEjB;AAAA;AAUA,QAAM,gBAAgB,gBAAmB;AAgBzC,YAAQ,kBAAkB,SAAS,gBAAiB,SAAS;AAC3D,UAAI,YAAY,EAAG,QAAO,CAAC;AAE3B,YAAM,WAAW,KAAK,MAAM,UAAU,CAAC,IAAI;AAC3C,YAAM,OAAO,cAAc,OAAO;AAClC,YAAM,YAAY,SAAS,MAAM,KAAK,KAAK,MAAM,OAAO,OAAO,IAAI,WAAW,EAAE,IAAI;AACpF,YAAM,YAAY,CAAC,OAAO,CAAC;AAE3B,eAAS,IAAI,GAAG,IAAI,WAAW,GAAG,KAAK;AACrC,kBAAU,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI;AAAA,MACpC;AAEA,gBAAU,KAAK,CAAC;AAEhB,aAAO,UAAU,QAAQ;AAAA,IAC3B;AAsBA,YAAQ,eAAe,SAAS,aAAc,SAAS;AACrD,YAAM,SAAS,CAAC;AAChB,YAAM,MAAM,QAAQ,gBAAgB,OAAO;AAC3C,YAAM,YAAY,IAAI;AAEtB,eAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAElC,cAAK,MAAM,KAAK,MAAM;AAAA,UACjB,MAAM,KAAK,MAAM,YAAY;AAAA,UAC7B,MAAM,YAAY,KAAK,MAAM,GAAI;AACpC;AAAA,UACF;AAEA,iBAAO,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,QAC9B;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AClFA;AAAA;AAAA,QAAM,gBAAgB,gBAAmB;AACzC,QAAM,sBAAsB;AAS5B,YAAQ,eAAe,SAAS,aAAc,SAAS;AACrD,YAAM,OAAO,cAAc,OAAO;AAElC,aAAO;AAAA;AAAA,QAEL,CAAC,GAAG,CAAC;AAAA;AAAA,QAEL,CAAC,OAAO,qBAAqB,CAAC;AAAA;AAAA,QAE9B,CAAC,GAAG,OAAO,mBAAmB;AAAA,MAChC;AAAA,IACF;AAAA;AAAA;;;ACrBA;AAAA;AAIA,YAAQ,WAAW;AAAA,MACjB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,IACd;AAMA,QAAM,gBAAgB;AAAA,MACpB,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN;AAQA,YAAQ,UAAU,SAAS,QAAS,MAAM;AACxC,aAAO,QAAQ,QAAQ,SAAS,MAAM,CAAC,MAAM,IAAI,KAAK,QAAQ,KAAK,QAAQ;AAAA,IAC7E;AASA,YAAQ,OAAO,SAAS,KAAM,OAAO;AACnC,aAAO,QAAQ,QAAQ,KAAK,IAAI,SAAS,OAAO,EAAE,IAAI;AAAA,IACxD;AASA,YAAQ,eAAe,SAAS,aAAc,MAAM;AAClD,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS;AACb,UAAI,eAAe;AACnB,UAAI,eAAe;AACnB,UAAI,UAAU;AACd,UAAI,UAAU;AAEd,eAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,uBAAe,eAAe;AAC9B,kBAAU,UAAU;AAEpB,iBAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,cAAIA,UAAS,KAAK,IAAI,KAAK,GAAG;AAC9B,cAAIA,YAAW,SAAS;AACtB;AAAA,UACF,OAAO;AACL,gBAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AACpE,sBAAUA;AACV,2BAAe;AAAA,UACjB;AAEA,UAAAA,UAAS,KAAK,IAAI,KAAK,GAAG;AAC1B,cAAIA,YAAW,SAAS;AACtB;AAAA,UACF,OAAO;AACL,gBAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AACpE,sBAAUA;AACV,2BAAe;AAAA,UACjB;AAAA,QACF;AAEA,YAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AACpE,YAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AAAA,MACtE;AAEA,aAAO;AAAA,IACT;AAOA,YAAQ,eAAe,SAAS,aAAc,MAAM;AAClD,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS;AAEb,eAAS,MAAM,GAAG,MAAM,OAAO,GAAG,OAAO;AACvC,iBAAS,MAAM,GAAG,MAAM,OAAO,GAAG,OAAO;AACvC,gBAAM,OAAO,KAAK,IAAI,KAAK,GAAG,IAC5B,KAAK,IAAI,KAAK,MAAM,CAAC,IACrB,KAAK,IAAI,MAAM,GAAG,GAAG,IACrB,KAAK,IAAI,MAAM,GAAG,MAAM,CAAC;AAE3B,cAAI,SAAS,KAAK,SAAS,EAAG;AAAA,QAChC;AAAA,MACF;AAEA,aAAO,SAAS,cAAc;AAAA,IAChC;AAQA,YAAQ,eAAe,SAAS,aAAc,MAAM;AAClD,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS;AACb,UAAI,UAAU;AACd,UAAI,UAAU;AAEd,eAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,kBAAU,UAAU;AACpB,iBAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,oBAAY,WAAW,IAAK,OAAS,KAAK,IAAI,KAAK,GAAG;AACtD,cAAI,OAAO,OAAO,YAAY,QAAS,YAAY,IAAQ;AAE3D,oBAAY,WAAW,IAAK,OAAS,KAAK,IAAI,KAAK,GAAG;AACtD,cAAI,OAAO,OAAO,YAAY,QAAS,YAAY,IAAQ;AAAA,QAC7D;AAAA,MACF;AAEA,aAAO,SAAS,cAAc;AAAA,IAChC;AAUA,YAAQ,eAAe,SAAS,aAAc,MAAM;AAClD,UAAI,YAAY;AAChB,YAAM,eAAe,KAAK,KAAK;AAE/B,eAAS,IAAI,GAAG,IAAI,cAAc,IAAK,cAAa,KAAK,KAAK,CAAC;AAE/D,YAAM,IAAI,KAAK,IAAI,KAAK,KAAM,YAAY,MAAM,eAAgB,CAAC,IAAI,EAAE;AAEvE,aAAO,IAAI,cAAc;AAAA,IAC3B;AAUA,aAAS,UAAW,aAAa,GAAG,GAAG;AACrC,cAAQ,aAAa;AAAA,QACnB,KAAK,QAAQ,SAAS;AAAY,kBAAQ,IAAI,KAAK,MAAM;AAAA,QACzD,KAAK,QAAQ,SAAS;AAAY,iBAAO,IAAI,MAAM;AAAA,QACnD,KAAK,QAAQ,SAAS;AAAY,iBAAO,IAAI,MAAM;AAAA,QACnD,KAAK,QAAQ,SAAS;AAAY,kBAAQ,IAAI,KAAK,MAAM;AAAA,QACzD,KAAK,QAAQ,SAAS;AAAY,kBAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM;AAAA,QACzF,KAAK,QAAQ,SAAS;AAAY,iBAAQ,IAAI,IAAK,IAAK,IAAI,IAAK,MAAM;AAAA,QACvE,KAAK,QAAQ,SAAS;AAAY,kBAAS,IAAI,IAAK,IAAK,IAAI,IAAK,KAAK,MAAM;AAAA,QAC7E,KAAK,QAAQ,SAAS;AAAY,kBAAS,IAAI,IAAK,KAAK,IAAI,KAAK,KAAK,MAAM;AAAA,QAE7E;AAAS,gBAAM,IAAI,MAAM,qBAAqB,WAAW;AAAA,MAC3D;AAAA,IACF;AAQA,YAAQ,YAAY,SAAS,UAAW,SAAS,MAAM;AACrD,YAAM,OAAO,KAAK;AAElB,eAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,iBAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,cAAI,KAAK,WAAW,KAAK,GAAG,EAAG;AAC/B,eAAK,IAAI,KAAK,KAAK,UAAU,SAAS,KAAK,GAAG,CAAC;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAQA,YAAQ,cAAc,SAAS,YAAa,MAAM,iBAAiB;AACjE,YAAM,cAAc,OAAO,KAAK,QAAQ,QAAQ,EAAE;AAClD,UAAI,cAAc;AAClB,UAAI,eAAe;AAEnB,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,wBAAgB,CAAC;AACjB,gBAAQ,UAAU,GAAG,IAAI;AAGzB,cAAM,UACJ,QAAQ,aAAa,IAAI,IACzB,QAAQ,aAAa,IAAI,IACzB,QAAQ,aAAa,IAAI,IACzB,QAAQ,aAAa,IAAI;AAG3B,gBAAQ,UAAU,GAAG,IAAI;AAEzB,YAAI,UAAU,cAAc;AAC1B,yBAAe;AACf,wBAAc;AAAA,QAChB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACzOA;AAAA;AAAA,QAAM,UAAU;AAEhB,QAAM,kBAAkB;AAAA;AAAA,MAEtB;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MACV;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MACV;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MACV;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,IACd;AAEA,QAAM,qBAAqB;AAAA;AAAA,MAEzB;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MACb;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MACb;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MACd;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MACd;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MACd;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MACd;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,IACnB;AAUA,YAAQ,iBAAiB,SAAS,eAAgB,SAAS,sBAAsB;AAC/E,cAAQ,sBAAsB;AAAA,QAC5B,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAUA,YAAQ,yBAAyB,SAAS,uBAAwB,SAAS,sBAAsB;AAC/F,cAAQ,sBAAsB;AAAA,QAC5B,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAAA;AAAA;;;ACtIA;AAAA;AAAA,QAAM,YAAY,IAAI,WAAW,GAAG;AACpC,QAAM,YAAY,IAAI,WAAW,GAAG;AASnC,KAAC,SAAS,aAAc;AACvB,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,kBAAU,CAAC,IAAI;AACf,kBAAU,CAAC,IAAI;AAEf,cAAM;AAIN,YAAI,IAAI,KAAO;AACb,eAAK;AAAA,QACP;AAAA,MACF;AAMA,eAAS,IAAI,KAAK,IAAI,KAAK,KAAK;AAC9B,kBAAU,CAAC,IAAI,UAAU,IAAI,GAAG;AAAA,MAClC;AAAA,IACF,GAAE;AAQF,YAAQ,MAAM,SAAS,IAAK,GAAG;AAC7B,UAAI,IAAI,EAAG,OAAM,IAAI,MAAM,SAAS,IAAI,GAAG;AAC3C,aAAO,UAAU,CAAC;AAAA,IACpB;AAQA,YAAQ,MAAM,SAAS,IAAK,GAAG;AAC7B,aAAO,UAAU,CAAC;AAAA,IACpB;AASA,YAAQ,MAAM,SAAS,IAAK,GAAG,GAAG;AAChC,UAAI,MAAM,KAAK,MAAM,EAAG,QAAO;AAI/B,aAAO,UAAU,UAAU,CAAC,IAAI,UAAU,CAAC,CAAC;AAAA,IAC9C;AAAA;AAAA;;;ACpEA;AAAA;AAAA,QAAM,KAAK;AASX,YAAQ,MAAM,SAAS,IAAK,IAAI,IAAI;AAClC,YAAM,QAAQ,IAAI,WAAW,GAAG,SAAS,GAAG,SAAS,CAAC;AAEtD,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,gBAAM,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,QACrC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AASA,YAAQ,MAAM,SAAS,IAAK,UAAU,SAAS;AAC7C,UAAI,SAAS,IAAI,WAAW,QAAQ;AAEpC,aAAQ,OAAO,SAAS,QAAQ,UAAW,GAAG;AAC5C,cAAM,QAAQ,OAAO,CAAC;AAEtB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,iBAAO,CAAC,KAAK,GAAG,IAAI,QAAQ,CAAC,GAAG,KAAK;AAAA,QACvC;AAGA,YAAI,SAAS;AACb,eAAO,SAAS,OAAO,UAAU,OAAO,MAAM,MAAM,EAAG;AACvD,iBAAS,OAAO,MAAM,MAAM;AAAA,MAC9B;AAEA,aAAO;AAAA,IACT;AASA,YAAQ,uBAAuB,SAAS,qBAAsB,QAAQ;AACpE,UAAI,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC;AAC7B,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,eAAO,QAAQ,IAAI,MAAM,IAAI,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAAA,MACzD;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC7DA;AAAA;AAAA,QAAM,aAAa;AAEnB,aAAS,mBAAoB,QAAQ;AACnC,WAAK,UAAU;AACf,WAAK,SAAS;AAEd,UAAI,KAAK,OAAQ,MAAK,WAAW,KAAK,MAAM;AAAA,IAC9C;AAQA,uBAAmB,UAAU,aAAa,SAAS,WAAY,QAAQ;AAErE,WAAK,SAAS;AACd,WAAK,UAAU,WAAW,qBAAqB,KAAK,MAAM;AAAA,IAC5D;AAQA,uBAAmB,UAAU,SAAS,SAASC,QAAQ,MAAM;AAC3D,UAAI,CAAC,KAAK,SAAS;AACjB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAIA,YAAM,aAAa,IAAI,WAAW,KAAK,SAAS,KAAK,MAAM;AAC3D,iBAAW,IAAI,IAAI;AAInB,YAAM,YAAY,WAAW,IAAI,YAAY,KAAK,OAAO;AAKzD,YAAM,QAAQ,KAAK,SAAS,UAAU;AACtC,UAAI,QAAQ,GAAG;AACb,cAAM,OAAO,IAAI,WAAW,KAAK,MAAM;AACvC,aAAK,IAAI,WAAW,KAAK;AAEzB,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvDjB;AAAA;AAMA,YAAQ,UAAU,SAAS,QAAS,SAAS;AAC3C,aAAO,CAAC,MAAM,OAAO,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD;AAAA;AAAA;;;ACRA;AAAA;AAAA,QAAM,UAAU;AAChB,QAAM,eAAe;AACrB,QAAI,QAAQ;AAIZ,YAAQ,MAAM,QAAQ,MAAM,KAAK;AAEjC,QAAM,OAAO,+BAA+B,QAAQ;AAEpD,YAAQ,QAAQ,IAAI,OAAO,OAAO,GAAG;AACrC,YAAQ,aAAa,IAAI,OAAO,yBAAyB,GAAG;AAC5D,YAAQ,OAAO,IAAI,OAAO,MAAM,GAAG;AACnC,YAAQ,UAAU,IAAI,OAAO,SAAS,GAAG;AACzC,YAAQ,eAAe,IAAI,OAAO,cAAc,GAAG;AAEnD,QAAM,aAAa,IAAI,OAAO,MAAM,QAAQ,GAAG;AAC/C,QAAM,eAAe,IAAI,OAAO,MAAM,UAAU,GAAG;AACnD,QAAM,oBAAoB,IAAI,OAAO,wBAAwB;AAE7D,YAAQ,YAAY,SAAS,UAAW,KAAK;AAC3C,aAAO,WAAW,KAAK,GAAG;AAAA,IAC5B;AAEA,YAAQ,cAAc,SAAS,YAAa,KAAK;AAC/C,aAAO,aAAa,KAAK,GAAG;AAAA,IAC9B;AAEA,YAAQ,mBAAmB,SAAS,iBAAkB,KAAK;AACzD,aAAO,kBAAkB,KAAK,GAAG;AAAA,IACnC;AAAA;AAAA;;;AC9BA;AAAA;AAAA,QAAM,eAAe;AACrB,QAAM,QAAQ;AASd,YAAQ,UAAU;AAAA,MAChB,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,IAAI,IAAI,EAAE;AAAA,IACrB;AAWA,YAAQ,eAAe;AAAA,MACrB,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,GAAG,IAAI,EAAE;AAAA,IACpB;AAOA,YAAQ,OAAO;AAAA,MACb,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,GAAG,IAAI,EAAE;AAAA,IACpB;AAWA,YAAQ,QAAQ;AAAA,MACd,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,GAAG,IAAI,EAAE;AAAA,IACpB;AAQA,YAAQ,QAAQ;AAAA,MACd,KAAK;AAAA,IACP;AAUA,YAAQ,wBAAwB,SAAS,sBAAuB,MAAM,SAAS;AAC7E,UAAI,CAAC,KAAK,OAAQ,OAAM,IAAI,MAAM,mBAAmB,IAAI;AAEzD,UAAI,CAAC,aAAa,QAAQ,OAAO,GAAG;AAClC,cAAM,IAAI,MAAM,sBAAsB,OAAO;AAAA,MAC/C;AAEA,UAAI,WAAW,KAAK,UAAU,GAAI,QAAO,KAAK,OAAO,CAAC;AAAA,eAC7C,UAAU,GAAI,QAAO,KAAK,OAAO,CAAC;AAC3C,aAAO,KAAK,OAAO,CAAC;AAAA,IACtB;AAQA,YAAQ,qBAAqB,SAAS,mBAAoB,SAAS;AACjE,UAAI,MAAM,YAAY,OAAO,EAAG,QAAO,QAAQ;AAAA,eACtC,MAAM,iBAAiB,OAAO,EAAG,QAAO,QAAQ;AAAA,eAChD,MAAM,UAAU,OAAO,EAAG,QAAO,QAAQ;AAAA,UAC7C,QAAO,QAAQ;AAAA,IACtB;AAQA,YAAQ,WAAW,SAAS,SAAU,MAAM;AAC1C,UAAI,QAAQ,KAAK,GAAI,QAAO,KAAK;AACjC,YAAM,IAAI,MAAM,cAAc;AAAA,IAChC;AAQA,YAAQ,UAAU,SAAS,QAAS,MAAM;AACxC,aAAO,QAAQ,KAAK,OAAO,KAAK;AAAA,IAClC;AAQA,aAAS,WAAY,QAAQ;AAC3B,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC;AAEA,YAAM,QAAQ,OAAO,YAAY;AAEjC,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB;AACE,gBAAM,IAAI,MAAM,mBAAmB,MAAM;AAAA,MAC7C;AAAA,IACF;AAUA,YAAQ,OAAO,SAAS,KAAM,OAAO,cAAc;AACjD,UAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1B,eAAO;AAAA,MACT;AAEA,UAAI;AACF,eAAO,WAAW,KAAK;AAAA,MACzB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;ACtKA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,SAAS;AACf,QAAM,UAAU;AAChB,QAAM,OAAO;AACb,QAAM,eAAe;AAGrB,QAAM,MAAO,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AAClG,QAAM,UAAU,MAAM,YAAY,GAAG;AAErC,aAAS,4BAA6B,MAAM,QAAQ,sBAAsB;AACxE,eAAS,iBAAiB,GAAG,kBAAkB,IAAI,kBAAkB;AACnE,YAAI,UAAU,QAAQ,YAAY,gBAAgB,sBAAsB,IAAI,GAAG;AAC7E,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,qBAAsB,MAAM,SAAS;AAE5C,aAAO,KAAK,sBAAsB,MAAM,OAAO,IAAI;AAAA,IACrD;AAEA,aAAS,0BAA2B,UAAU,SAAS;AACrD,UAAI,YAAY;AAEhB,eAAS,QAAQ,SAAU,MAAM;AAC/B,cAAM,eAAe,qBAAqB,KAAK,MAAM,OAAO;AAC5D,qBAAa,eAAe,KAAK,cAAc;AAAA,MACjD,CAAC;AAED,aAAO;AAAA,IACT;AAEA,aAAS,2BAA4B,UAAU,sBAAsB;AACnE,eAAS,iBAAiB,GAAG,kBAAkB,IAAI,kBAAkB;AACnE,cAAM,SAAS,0BAA0B,UAAU,cAAc;AACjE,YAAI,UAAU,QAAQ,YAAY,gBAAgB,sBAAsB,KAAK,KAAK,GAAG;AACnF,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAUA,YAAQ,OAAO,SAAS,KAAM,OAAO,cAAc;AACjD,UAAI,aAAa,QAAQ,KAAK,GAAG;AAC/B,eAAO,SAAS,OAAO,EAAE;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AAWA,YAAQ,cAAc,SAAS,YAAa,SAAS,sBAAsB,MAAM;AAC/E,UAAI,CAAC,aAAa,QAAQ,OAAO,GAAG;AAClC,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAGA,UAAI,OAAO,SAAS,YAAa,QAAO,KAAK;AAG7C,YAAM,iBAAiB,MAAM,wBAAwB,OAAO;AAG5D,YAAM,mBAAmB,OAAO,uBAAuB,SAAS,oBAAoB;AAGpF,YAAM,0BAA0B,iBAAiB,oBAAoB;AAErE,UAAI,SAAS,KAAK,MAAO,QAAO;AAEhC,YAAM,aAAa,yBAAyB,qBAAqB,MAAM,OAAO;AAG9E,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,iBAAO,KAAK,MAAO,aAAa,KAAM,CAAC;AAAA,QAEzC,KAAK,KAAK;AACR,iBAAO,KAAK,MAAO,aAAa,KAAM,CAAC;AAAA,QAEzC,KAAK,KAAK;AACR,iBAAO,KAAK,MAAM,aAAa,EAAE;AAAA,QAEnC,KAAK,KAAK;AAAA,QACV;AACE,iBAAO,KAAK,MAAM,aAAa,CAAC;AAAA,MACpC;AAAA,IACF;AAUA,YAAQ,wBAAwB,SAAS,sBAAuB,MAAM,sBAAsB;AAC1F,UAAI;AAEJ,YAAM,MAAM,QAAQ,KAAK,sBAAsB,QAAQ,CAAC;AAExD,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,YAAI,KAAK,SAAS,GAAG;AACnB,iBAAO,2BAA2B,MAAM,GAAG;AAAA,QAC7C;AAEA,YAAI,KAAK,WAAW,GAAG;AACrB,iBAAO;AAAA,QACT;AAEA,cAAM,KAAK,CAAC;AAAA,MACd,OAAO;AACL,cAAM;AAAA,MACR;AAEA,aAAO,4BAA4B,IAAI,MAAM,IAAI,UAAU,GAAG,GAAG;AAAA,IACnE;AAYA,YAAQ,iBAAiB,SAAS,eAAgB,SAAS;AACzD,UAAI,CAAC,aAAa,QAAQ,OAAO,KAAK,UAAU,GAAG;AACjD,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAEA,UAAI,IAAI,WAAW;AAEnB,aAAO,MAAM,YAAY,CAAC,IAAI,WAAW,GAAG;AAC1C,aAAM,OAAQ,MAAM,YAAY,CAAC,IAAI;AAAA,MACvC;AAEA,aAAQ,WAAW,KAAM;AAAA,IAC3B;AAAA;AAAA;;;AClKA;AAAA;AAAA,QAAM,QAAQ;AAEd,QAAM,MAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AACrF,QAAM,WAAY,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK;AACtE,QAAM,UAAU,MAAM,YAAY,GAAG;AAYrC,YAAQ,iBAAiB,SAAS,eAAgB,sBAAsB,MAAM;AAC5E,YAAM,OAAS,qBAAqB,OAAO,IAAK;AAChD,UAAI,IAAI,QAAQ;AAEhB,aAAO,MAAM,YAAY,CAAC,IAAI,WAAW,GAAG;AAC1C,aAAM,OAAQ,MAAM,YAAY,CAAC,IAAI;AAAA,MACvC;AAKA,cAAS,QAAQ,KAAM,KAAK;AAAA,IAC9B;AAAA;AAAA;;;AC5BA;AAAA;AAAA,QAAM,OAAO;AAEb,aAAS,YAAa,MAAM;AAC1B,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO,KAAK,SAAS;AAAA,IAC5B;AAEA,gBAAY,gBAAgB,SAAS,cAAe,QAAQ;AAC1D,aAAO,KAAK,KAAK,MAAM,SAAS,CAAC,KAAM,SAAS,IAAO,SAAS,IAAK,IAAI,IAAK;AAAA,IAChF;AAEA,gBAAY,UAAU,YAAY,SAAS,YAAa;AACtD,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,gBAAY,UAAU,gBAAgB,SAAS,gBAAiB;AAC9D,aAAO,YAAY,cAAc,KAAK,KAAK,MAAM;AAAA,IACnD;AAEA,gBAAY,UAAU,QAAQ,SAAS,MAAO,WAAW;AACvD,UAAI,GAAG,OAAO;AAId,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,GAAG;AAC7C,gBAAQ,KAAK,KAAK,OAAO,GAAG,CAAC;AAC7B,gBAAQ,SAAS,OAAO,EAAE;AAE1B,kBAAU,IAAI,OAAO,EAAE;AAAA,MACzB;AAIA,YAAM,eAAe,KAAK,KAAK,SAAS;AACxC,UAAI,eAAe,GAAG;AACpB,gBAAQ,KAAK,KAAK,OAAO,CAAC;AAC1B,gBAAQ,SAAS,OAAO,EAAE;AAE1B,kBAAU,IAAI,OAAO,eAAe,IAAI,CAAC;AAAA,MAC3C;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1CjB;AAAA;AAAA,QAAM,OAAO;AAWb,QAAM,kBAAkB;AAAA,MACtB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC7C;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC5D;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC5D;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,IAC1C;AAEA,aAAS,iBAAkB,MAAM;AAC/B,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO;AAAA,IACd;AAEA,qBAAiB,gBAAgB,SAAS,cAAe,QAAQ;AAC/D,aAAO,KAAK,KAAK,MAAM,SAAS,CAAC,IAAI,KAAK,SAAS;AAAA,IACrD;AAEA,qBAAiB,UAAU,YAAY,SAAS,YAAa;AAC3D,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,qBAAiB,UAAU,gBAAgB,SAAS,gBAAiB;AACnE,aAAO,iBAAiB,cAAc,KAAK,KAAK,MAAM;AAAA,IACxD;AAEA,qBAAiB,UAAU,QAAQ,SAAS,MAAO,WAAW;AAC5D,UAAI;AAIJ,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,GAAG;AAE7C,YAAI,QAAQ,gBAAgB,QAAQ,KAAK,KAAK,CAAC,CAAC,IAAI;AAGpD,iBAAS,gBAAgB,QAAQ,KAAK,KAAK,IAAI,CAAC,CAAC;AAGjD,kBAAU,IAAI,OAAO,EAAE;AAAA,MACzB;AAIA,UAAI,KAAK,KAAK,SAAS,GAAG;AACxB,kBAAU,IAAI,gBAAgB,QAAQ,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC;AAAA,MACxD;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1DjB;AAAA;AAAA,QAAM,OAAO;AAEb,aAAS,SAAU,MAAM;AACvB,WAAK,OAAO,KAAK;AACjB,UAAI,OAAQ,SAAU,UAAU;AAC9B,aAAK,OAAO,IAAI,YAAY,EAAE,OAAO,IAAI;AAAA,MAC3C,OAAO;AACL,aAAK,OAAO,IAAI,WAAW,IAAI;AAAA,MACjC;AAAA,IACF;AAEA,aAAS,gBAAgB,SAAS,cAAe,QAAQ;AACvD,aAAO,SAAS;AAAA,IAClB;AAEA,aAAS,UAAU,YAAY,SAAS,YAAa;AACnD,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,aAAS,UAAU,gBAAgB,SAAS,gBAAiB;AAC3D,aAAO,SAAS,cAAc,KAAK,KAAK,MAAM;AAAA,IAChD;AAEA,aAAS,UAAU,QAAQ,SAAU,WAAW;AAC9C,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,IAAI,GAAG,KAAK;AAChD,kBAAU,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC;AAAA,MAC/B;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7BjB;AAAA;AAAA,QAAM,OAAO;AACb,QAAM,QAAQ;AAEd,aAAS,UAAW,MAAM;AACxB,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO;AAAA,IACd;AAEA,cAAU,gBAAgB,SAAS,cAAe,QAAQ;AACxD,aAAO,SAAS;AAAA,IAClB;AAEA,cAAU,UAAU,YAAY,SAAS,YAAa;AACpD,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,cAAU,UAAU,gBAAgB,SAAS,gBAAiB;AAC5D,aAAO,UAAU,cAAc,KAAK,KAAK,MAAM;AAAA,IACjD;AAEA,cAAU,UAAU,QAAQ,SAAU,WAAW;AAC/C,UAAI;AAKJ,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACrC,YAAI,QAAQ,MAAM,OAAO,KAAK,KAAK,CAAC,CAAC;AAGrC,YAAI,SAAS,SAAU,SAAS,OAAQ;AAEtC,mBAAS;AAAA,QAGX,WAAW,SAAS,SAAU,SAAS,OAAQ;AAE7C,mBAAS;AAAA,QACX,OAAO;AACL,gBAAM,IAAI;AAAA,YACR,6BAA6B,KAAK,KAAK,CAAC,IAAI;AAAA,UACX;AAAA,QACrC;AAIA,iBAAW,UAAU,IAAK,OAAQ,OAAS,QAAQ;AAGnD,kBAAU,IAAI,OAAO,EAAE;AAAA,MACzB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrDjB;AAAA;AAAA,QAAM,OAAO;AACb,QAAM,cAAc;AACpB,QAAM,mBAAmB;AACzB,QAAM,WAAW;AACjB,QAAM,YAAY;AAClB,QAAM,QAAQ;AACd,QAAM,QAAQ;AACd,QAAM,WAAW;AAQjB,aAAS,oBAAqB,KAAK;AACjC,aAAO,SAAS,mBAAmB,GAAG,CAAC,EAAE;AAAA,IAC3C;AAUA,aAAS,YAAa,OAAO,MAAM,KAAK;AACtC,YAAM,WAAW,CAAC;AAClB,UAAI;AAEJ,cAAQ,SAAS,MAAM,KAAK,GAAG,OAAO,MAAM;AAC1C,iBAAS,KAAK;AAAA,UACZ,MAAM,OAAO,CAAC;AAAA,UACd,OAAO,OAAO;AAAA,UACd;AAAA,UACA,QAAQ,OAAO,CAAC,EAAE;AAAA,QACpB,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IACT;AASA,aAAS,sBAAuB,SAAS;AACvC,YAAM,UAAU,YAAY,MAAM,SAAS,KAAK,SAAS,OAAO;AAChE,YAAM,eAAe,YAAY,MAAM,cAAc,KAAK,cAAc,OAAO;AAC/E,UAAI;AACJ,UAAI;AAEJ,UAAI,MAAM,mBAAmB,GAAG;AAC9B,mBAAW,YAAY,MAAM,MAAM,KAAK,MAAM,OAAO;AACrD,oBAAY,YAAY,MAAM,OAAO,KAAK,OAAO,OAAO;AAAA,MAC1D,OAAO;AACL,mBAAW,YAAY,MAAM,YAAY,KAAK,MAAM,OAAO;AAC3D,oBAAY,CAAC;AAAA,MACf;AAEA,YAAM,OAAO,QAAQ,OAAO,cAAc,UAAU,SAAS;AAE7D,aAAO,KACJ,KAAK,SAAU,IAAI,IAAI;AACtB,eAAO,GAAG,QAAQ,GAAG;AAAA,MACvB,CAAC,EACA,IAAI,SAAU,KAAK;AAClB,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM,IAAI;AAAA,UACV,QAAQ,IAAI;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACL;AAUA,aAAS,qBAAsB,QAAQ,MAAM;AAC3C,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,iBAAO,YAAY,cAAc,MAAM;AAAA,QACzC,KAAK,KAAK;AACR,iBAAO,iBAAiB,cAAc,MAAM;AAAA,QAC9C,KAAK,KAAK;AACR,iBAAO,UAAU,cAAc,MAAM;AAAA,QACvC,KAAK,KAAK;AACR,iBAAO,SAAS,cAAc,MAAM;AAAA,MACxC;AAAA,IACF;AAQA,aAAS,cAAe,MAAM;AAC5B,aAAO,KAAK,OAAO,SAAU,KAAK,MAAM;AACtC,cAAM,UAAU,IAAI,SAAS,KAAK,IAAI,IAAI,IAAI,SAAS,CAAC,IAAI;AAC5D,YAAI,WAAW,QAAQ,SAAS,KAAK,MAAM;AACzC,cAAI,IAAI,SAAS,CAAC,EAAE,QAAQ,KAAK;AACjC,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,IAAI;AACb,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAkBA,aAAS,WAAY,MAAM;AACzB,YAAM,QAAQ,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,MAAM,KAAK,CAAC;AAElB,gBAAQ,IAAI,MAAM;AAAA,UAChB,KAAK,KAAK;AACR,kBAAM,KAAK;AAAA,cAAC;AAAA,cACV,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,cAAc,QAAQ,IAAI,OAAO;AAAA,cAC9D,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,IAAI,OAAO;AAAA,YACxD,CAAC;AACD;AAAA,UACF,KAAK,KAAK;AACR,kBAAM,KAAK;AAAA,cAAC;AAAA,cACV,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,IAAI,OAAO;AAAA,YACxD,CAAC;AACD;AAAA,UACF,KAAK,KAAK;AACR,kBAAM,KAAK;AAAA,cAAC;AAAA,cACV,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,oBAAoB,IAAI,IAAI,EAAE;AAAA,YAC3E,CAAC;AACD;AAAA,UACF,KAAK,KAAK;AACR,kBAAM,KAAK;AAAA,cACT,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,oBAAoB,IAAI,IAAI,EAAE;AAAA,YAC3E,CAAC;AAAA,QACL;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAcA,aAAS,WAAY,OAAO,SAAS;AACnC,YAAM,QAAQ,CAAC;AACf,YAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;AAC1B,UAAI,cAAc,CAAC,OAAO;AAE1B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,YAAY,MAAM,CAAC;AACzB,cAAM,iBAAiB,CAAC;AAExB,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAM,OAAO,UAAU,CAAC;AACxB,gBAAM,MAAM,KAAK,IAAI;AAErB,yBAAe,KAAK,GAAG;AACvB,gBAAM,GAAG,IAAI,EAAE,MAAY,WAAW,EAAE;AACxC,gBAAM,GAAG,IAAI,CAAC;AAEd,mBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,kBAAM,aAAa,YAAY,CAAC;AAEhC,gBAAI,MAAM,UAAU,KAAK,MAAM,UAAU,EAAE,KAAK,SAAS,KAAK,MAAM;AAClE,oBAAM,UAAU,EAAE,GAAG,IACnB,qBAAqB,MAAM,UAAU,EAAE,YAAY,KAAK,QAAQ,KAAK,IAAI,IACzE,qBAAqB,MAAM,UAAU,EAAE,WAAW,KAAK,IAAI;AAE7D,oBAAM,UAAU,EAAE,aAAa,KAAK;AAAA,YACtC,OAAO;AACL,kBAAI,MAAM,UAAU,EAAG,OAAM,UAAU,EAAE,YAAY,KAAK;AAE1D,oBAAM,UAAU,EAAE,GAAG,IAAI,qBAAqB,KAAK,QAAQ,KAAK,IAAI,IAClE,IAAI,KAAK,sBAAsB,KAAK,MAAM,OAAO;AAAA,YACrD;AAAA,UACF;AAAA,QACF;AAEA,sBAAc;AAAA,MAChB;AAEA,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,cAAM,YAAY,CAAC,CAAC,EAAE,MAAM;AAAA,MAC9B;AAEA,aAAO,EAAE,KAAK,OAAO,MAAa;AAAA,IACpC;AAUA,aAAS,mBAAoB,MAAM,WAAW;AAC5C,UAAI;AACJ,YAAM,WAAW,KAAK,mBAAmB,IAAI;AAE7C,aAAO,KAAK,KAAK,WAAW,QAAQ;AAGpC,UAAI,SAAS,KAAK,QAAQ,KAAK,MAAM,SAAS,KAAK;AACjD,cAAM,IAAI,MAAM,MAAM,OAAO,mCACO,KAAK,SAAS,IAAI,IACpD,4BAA4B,KAAK,SAAS,QAAQ,CAAC;AAAA,MACvD;AAGA,UAAI,SAAS,KAAK,SAAS,CAAC,MAAM,mBAAmB,GAAG;AACtD,eAAO,KAAK;AAAA,MACd;AAEA,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,iBAAO,IAAI,YAAY,IAAI;AAAA,QAE7B,KAAK,KAAK;AACR,iBAAO,IAAI,iBAAiB,IAAI;AAAA,QAElC,KAAK,KAAK;AACR,iBAAO,IAAI,UAAU,IAAI;AAAA,QAE3B,KAAK,KAAK;AACR,iBAAO,IAAI,SAAS,IAAI;AAAA,MAC5B;AAAA,IACF;AAiBA,YAAQ,YAAY,SAAS,UAAW,OAAO;AAC7C,aAAO,MAAM,OAAO,SAAU,KAAK,KAAK;AACtC,YAAI,OAAO,QAAQ,UAAU;AAC3B,cAAI,KAAK,mBAAmB,KAAK,IAAI,CAAC;AAAA,QACxC,WAAW,IAAI,MAAM;AACnB,cAAI,KAAK,mBAAmB,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,QACjD;AAEA,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAUA,YAAQ,aAAa,SAAS,WAAY,MAAM,SAAS;AACvD,YAAM,OAAO,sBAAsB,MAAM,MAAM,mBAAmB,CAAC;AAEnE,YAAM,QAAQ,WAAW,IAAI;AAC7B,YAAM,QAAQ,WAAW,OAAO,OAAO;AACvC,YAAM,OAAO,SAAS,UAAU,MAAM,KAAK,SAAS,KAAK;AAEzD,YAAM,gBAAgB,CAAC;AACvB,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACxC,sBAAc,KAAK,MAAM,MAAM,KAAK,CAAC,CAAC,EAAE,IAAI;AAAA,MAC9C;AAEA,aAAO,QAAQ,UAAU,cAAc,aAAa,CAAC;AAAA,IACvD;AAYA,YAAQ,WAAW,SAAS,SAAU,MAAM;AAC1C,aAAO,QAAQ;AAAA,QACb,sBAAsB,MAAM,MAAM,mBAAmB,CAAC;AAAA,MACxD;AAAA,IACF;AAAA;AAAA;;;ACzUA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,UAAU;AAChB,QAAM,YAAY;AAClB,QAAM,YAAY;AAClB,QAAM,mBAAmB;AACzB,QAAM,gBAAgB;AACtB,QAAM,cAAc;AACpB,QAAM,SAAS;AACf,QAAM,qBAAqB;AAC3B,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,OAAO;AACb,QAAM,WAAW;AAkCjB,aAAS,mBAAoB,QAAQ,SAAS;AAC5C,YAAM,OAAO,OAAO;AACpB,YAAM,MAAM,cAAc,aAAa,OAAO;AAE9C,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAM,MAAM,IAAI,CAAC,EAAE,CAAC;AACpB,cAAM,MAAM,IAAI,CAAC,EAAE,CAAC;AAEpB,iBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC5B,cAAI,MAAM,KAAK,MAAM,QAAQ,MAAM,EAAG;AAEtC,mBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC5B,gBAAI,MAAM,KAAK,MAAM,QAAQ,MAAM,EAAG;AAEtC,gBAAK,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,MACxC,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,MACtC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAI;AACxC,qBAAO,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,IAAI;AAAA,YACzC,OAAO;AACL,qBAAO,IAAI,MAAM,GAAG,MAAM,GAAG,OAAO,IAAI;AAAA,YAC1C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AASA,aAAS,mBAAoB,QAAQ;AACnC,YAAM,OAAO,OAAO;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK;AACjC,cAAM,QAAQ,IAAI,MAAM;AACxB,eAAO,IAAI,GAAG,GAAG,OAAO,IAAI;AAC5B,eAAO,IAAI,GAAG,GAAG,OAAO,IAAI;AAAA,MAC9B;AAAA,IACF;AAUA,aAAS,sBAAuB,QAAQ,SAAS;AAC/C,YAAM,MAAM,iBAAiB,aAAa,OAAO;AAEjD,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAM,MAAM,IAAI,CAAC,EAAE,CAAC;AACpB,cAAM,MAAM,IAAI,CAAC,EAAE,CAAC;AAEpB,iBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC5B,mBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC5B,gBAAI,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAC1C,MAAM,KAAK,MAAM,GAAI;AACtB,qBAAO,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,IAAI;AAAA,YACzC,OAAO;AACL,qBAAO,IAAI,MAAM,GAAG,MAAM,GAAG,OAAO,IAAI;AAAA,YAC1C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAQA,aAAS,iBAAkB,QAAQ,SAAS;AAC1C,YAAM,OAAO,OAAO;AACpB,YAAM,OAAO,QAAQ,eAAe,OAAO;AAC3C,UAAI,KAAK,KAAK;AAEd,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,KAAK,MAAM,IAAI,CAAC;AACtB,cAAM,IAAI,IAAI,OAAO,IAAI;AACzB,eAAQ,QAAQ,IAAK,OAAO;AAE5B,eAAO,IAAI,KAAK,KAAK,KAAK,IAAI;AAC9B,eAAO,IAAI,KAAK,KAAK,KAAK,IAAI;AAAA,MAChC;AAAA,IACF;AASA,aAAS,gBAAiB,QAAQ,sBAAsB,aAAa;AACnE,YAAM,OAAO,OAAO;AACpB,YAAM,OAAO,WAAW,eAAe,sBAAsB,WAAW;AACxE,UAAI,GAAG;AAEP,WAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,eAAQ,QAAQ,IAAK,OAAO;AAG5B,YAAI,IAAI,GAAG;AACT,iBAAO,IAAI,GAAG,GAAG,KAAK,IAAI;AAAA,QAC5B,WAAW,IAAI,GAAG;AAChB,iBAAO,IAAI,IAAI,GAAG,GAAG,KAAK,IAAI;AAAA,QAChC,OAAO;AACL,iBAAO,IAAI,OAAO,KAAK,GAAG,GAAG,KAAK,IAAI;AAAA,QACxC;AAGA,YAAI,IAAI,GAAG;AACT,iBAAO,IAAI,GAAG,OAAO,IAAI,GAAG,KAAK,IAAI;AAAA,QACvC,WAAW,IAAI,GAAG;AAChB,iBAAO,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI;AAAA,QACzC,OAAO;AACL,iBAAO,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI;AAAA,QACrC;AAAA,MACF;AAGA,aAAO,IAAI,OAAO,GAAG,GAAG,GAAG,IAAI;AAAA,IACjC;AAQA,aAAS,UAAW,QAAQ,MAAM;AAChC,YAAM,OAAO,OAAO;AACpB,UAAI,MAAM;AACV,UAAI,MAAM,OAAO;AACjB,UAAI,WAAW;AACf,UAAI,YAAY;AAEhB,eAAS,MAAM,OAAO,GAAG,MAAM,GAAG,OAAO,GAAG;AAC1C,YAAI,QAAQ,EAAG;AAEf,eAAO,MAAM;AACX,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAI,CAAC,OAAO,WAAW,KAAK,MAAM,CAAC,GAAG;AACpC,kBAAI,OAAO;AAEX,kBAAI,YAAY,KAAK,QAAQ;AAC3B,wBAAU,KAAK,SAAS,MAAM,WAAY,OAAO;AAAA,cACnD;AAEA,qBAAO,IAAI,KAAK,MAAM,GAAG,IAAI;AAC7B;AAEA,kBAAI,aAAa,IAAI;AACnB;AACA,2BAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAEP,cAAI,MAAM,KAAK,QAAQ,KAAK;AAC1B,mBAAO;AACP,kBAAM,CAAC;AACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAUA,aAAS,WAAY,SAAS,sBAAsB,UAAU;AAE5D,YAAM,SAAS,IAAI,UAAU;AAE7B,eAAS,QAAQ,SAAU,MAAM;AAE/B,eAAO,IAAI,KAAK,KAAK,KAAK,CAAC;AAS3B,eAAO,IAAI,KAAK,UAAU,GAAG,KAAK,sBAAsB,KAAK,MAAM,OAAO,CAAC;AAG3E,aAAK,MAAM,MAAM;AAAA,MACnB,CAAC;AAGD,YAAM,iBAAiB,MAAM,wBAAwB,OAAO;AAC5D,YAAM,mBAAmB,OAAO,uBAAuB,SAAS,oBAAoB;AACpF,YAAM,0BAA0B,iBAAiB,oBAAoB;AAOrE,UAAI,OAAO,gBAAgB,IAAI,KAAK,wBAAwB;AAC1D,eAAO,IAAI,GAAG,CAAC;AAAA,MACjB;AAOA,aAAO,OAAO,gBAAgB,IAAI,MAAM,GAAG;AACzC,eAAO,OAAO,CAAC;AAAA,MACjB;AAMA,YAAM,iBAAiB,yBAAyB,OAAO,gBAAgB,KAAK;AAC5E,eAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,eAAO,IAAI,IAAI,IAAI,KAAO,KAAM,CAAC;AAAA,MACnC;AAEA,aAAO,gBAAgB,QAAQ,SAAS,oBAAoB;AAAA,IAC9D;AAWA,aAAS,gBAAiB,WAAW,SAAS,sBAAsB;AAElE,YAAM,iBAAiB,MAAM,wBAAwB,OAAO;AAG5D,YAAM,mBAAmB,OAAO,uBAAuB,SAAS,oBAAoB;AAGpF,YAAM,qBAAqB,iBAAiB;AAG5C,YAAM,gBAAgB,OAAO,eAAe,SAAS,oBAAoB;AAGzE,YAAM,iBAAiB,iBAAiB;AACxC,YAAM,iBAAiB,gBAAgB;AAEvC,YAAM,yBAAyB,KAAK,MAAM,iBAAiB,aAAa;AAExE,YAAM,wBAAwB,KAAK,MAAM,qBAAqB,aAAa;AAC3E,YAAM,wBAAwB,wBAAwB;AAGtD,YAAM,UAAU,yBAAyB;AAGzC,YAAM,KAAK,IAAI,mBAAmB,OAAO;AAEzC,UAAI,SAAS;AACb,YAAM,SAAS,IAAI,MAAM,aAAa;AACtC,YAAM,SAAS,IAAI,MAAM,aAAa;AACtC,UAAI,cAAc;AAClB,YAAM,SAAS,IAAI,WAAW,UAAU,MAAM;AAG9C,eAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,cAAM,WAAW,IAAI,iBAAiB,wBAAwB;AAG9D,eAAO,CAAC,IAAI,OAAO,MAAM,QAAQ,SAAS,QAAQ;AAGlD,eAAO,CAAC,IAAI,GAAG,OAAO,OAAO,CAAC,CAAC;AAE/B,kBAAU;AACV,sBAAc,KAAK,IAAI,aAAa,QAAQ;AAAA,MAC9C;AAIA,YAAM,OAAO,IAAI,WAAW,cAAc;AAC1C,UAAI,QAAQ;AACZ,UAAI,GAAG;AAGP,WAAK,IAAI,GAAG,IAAI,aAAa,KAAK;AAChC,aAAK,IAAI,GAAG,IAAI,eAAe,KAAK;AAClC,cAAI,IAAI,OAAO,CAAC,EAAE,QAAQ;AACxB,iBAAK,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAGA,WAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC5B,aAAK,IAAI,GAAG,IAAI,eAAe,KAAK;AAClC,eAAK,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,QAC7B;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,aAAS,aAAc,MAAM,SAAS,sBAAsB,aAAa;AACvE,UAAI;AAEJ,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,mBAAW,SAAS,UAAU,IAAI;AAAA,MACpC,WAAW,OAAO,SAAS,UAAU;AACnC,YAAI,mBAAmB;AAEvB,YAAI,CAAC,kBAAkB;AACrB,gBAAM,cAAc,SAAS,SAAS,IAAI;AAG1C,6BAAmB,QAAQ,sBAAsB,aAAa,oBAAoB;AAAA,QACpF;AAIA,mBAAW,SAAS,WAAW,MAAM,oBAAoB,EAAE;AAAA,MAC7D,OAAO;AACL,cAAM,IAAI,MAAM,cAAc;AAAA,MAChC;AAGA,YAAM,cAAc,QAAQ,sBAAsB,UAAU,oBAAoB;AAGhF,UAAI,CAAC,aAAa;AAChB,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAGA,UAAI,CAAC,SAAS;AACZ,kBAAU;AAAA,MAGZ,WAAW,UAAU,aAAa;AAChC,cAAM,IAAI;AAAA,UAAM,0HAE0C,cAAc;AAAA,QACxE;AAAA,MACF;AAEA,YAAM,WAAW,WAAW,SAAS,sBAAsB,QAAQ;AAGnE,YAAM,cAAc,MAAM,cAAc,OAAO;AAC/C,YAAM,UAAU,IAAI,UAAU,WAAW;AAGzC,yBAAmB,SAAS,OAAO;AACnC,yBAAmB,OAAO;AAC1B,4BAAsB,SAAS,OAAO;AAMtC,sBAAgB,SAAS,sBAAsB,CAAC;AAEhD,UAAI,WAAW,GAAG;AAChB,yBAAiB,SAAS,OAAO;AAAA,MACnC;AAGA,gBAAU,SAAS,QAAQ;AAE3B,UAAI,MAAM,WAAW,GAAG;AAEtB,sBAAc,YAAY;AAAA,UAAY;AAAA,UACpC,gBAAgB,KAAK,MAAM,SAAS,oBAAoB;AAAA,QAAC;AAAA,MAC7D;AAGA,kBAAY,UAAU,aAAa,OAAO;AAG1C,sBAAgB,SAAS,sBAAsB,WAAW;AAE1D,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAWA,YAAQ,SAAS,SAAS,OAAQ,MAAM,SAAS;AAC/C,UAAI,OAAO,SAAS,eAAe,SAAS,IAAI;AAC9C,cAAM,IAAI,MAAM,eAAe;AAAA,MACjC;AAEA,UAAI,uBAAuB,QAAQ;AACnC,UAAI;AACJ,UAAI;AAEJ,UAAI,OAAO,YAAY,aAAa;AAElC,+BAAuB,QAAQ,KAAK,QAAQ,sBAAsB,QAAQ,CAAC;AAC3E,kBAAU,QAAQ,KAAK,QAAQ,OAAO;AACtC,eAAO,YAAY,KAAK,QAAQ,WAAW;AAE3C,YAAI,QAAQ,YAAY;AACtB,gBAAM,kBAAkB,QAAQ,UAAU;AAAA,QAC5C;AAAA,MACF;AAEA,aAAO,aAAa,MAAM,SAAS,sBAAsB,IAAI;AAAA,IAC/D;AAAA;AAAA;;;AC9eA,IAAAC,iBAAA;AAAA;AAAA,aAAS,SAAU,KAAK;AACtB,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,SAAS;AAAA,MACrB;AAEA,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AAEA,UAAI,UAAU,IAAI,MAAM,EAAE,QAAQ,KAAK,EAAE,EAAE,MAAM,EAAE;AACnD,UAAI,QAAQ,SAAS,KAAK,QAAQ,WAAW,KAAK,QAAQ,SAAS,GAAG;AACpE,cAAM,IAAI,MAAM,wBAAwB,GAAG;AAAA,MAC7C;AAGA,UAAI,QAAQ,WAAW,KAAK,QAAQ,WAAW,GAAG;AAChD,kBAAU,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,QAAQ,IAAI,SAAU,GAAG;AAClE,iBAAO,CAAC,GAAG,CAAC;AAAA,QACd,CAAC,CAAC;AAAA,MACJ;AAGA,UAAI,QAAQ,WAAW,EAAG,SAAQ,KAAK,KAAK,GAAG;AAE/C,YAAM,WAAW,SAAS,QAAQ,KAAK,EAAE,GAAG,EAAE;AAE9C,aAAO;AAAA,QACL,GAAI,YAAY,KAAM;AAAA,QACtB,GAAI,YAAY,KAAM;AAAA,QACtB,GAAI,YAAY,IAAK;AAAA,QACrB,GAAG,WAAW;AAAA,QACd,KAAK,MAAM,QAAQ,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE;AAAA,MACxC;AAAA,IACF;AAEA,YAAQ,aAAa,SAAS,WAAY,SAAS;AACjD,UAAI,CAAC,QAAS,WAAU,CAAC;AACzB,UAAI,CAAC,QAAQ,MAAO,SAAQ,QAAQ,CAAC;AAErC,YAAM,SAAS,OAAO,QAAQ,WAAW,eACvC,QAAQ,WAAW,QACnB,QAAQ,SAAS,IACf,IACA,QAAQ;AAEZ,YAAM,QAAQ,QAAQ,SAAS,QAAQ,SAAS,KAAK,QAAQ,QAAQ;AACrE,YAAM,QAAQ,QAAQ,SAAS;AAE/B,aAAO;AAAA,QACL;AAAA,QACA,OAAO,QAAQ,IAAI;AAAA,QACnB;AAAA,QACA,OAAO;AAAA,UACL,MAAM,SAAS,QAAQ,MAAM,QAAQ,WAAW;AAAA,UAChD,OAAO,SAAS,QAAQ,MAAM,SAAS,WAAW;AAAA,QACpD;AAAA,QACA,MAAM,QAAQ;AAAA,QACd,cAAc,QAAQ,gBAAgB,CAAC;AAAA,MACzC;AAAA,IACF;AAEA,YAAQ,WAAW,SAAS,SAAU,QAAQ,MAAM;AAClD,aAAO,KAAK,SAAS,KAAK,SAAS,SAAS,KAAK,SAAS,IACtD,KAAK,SAAS,SAAS,KAAK,SAAS,KACrC,KAAK;AAAA,IACX;AAEA,YAAQ,gBAAgB,SAAS,cAAe,QAAQ,MAAM;AAC5D,YAAM,QAAQ,QAAQ,SAAS,QAAQ,IAAI;AAC3C,aAAO,KAAK,OAAO,SAAS,KAAK,SAAS,KAAK,KAAK;AAAA,IACtD;AAEA,YAAQ,gBAAgB,SAAS,cAAe,SAAS,IAAI,MAAM;AACjE,YAAM,OAAO,GAAG,QAAQ;AACxB,YAAM,OAAO,GAAG,QAAQ;AACxB,YAAM,QAAQ,QAAQ,SAAS,MAAM,IAAI;AACzC,YAAM,aAAa,KAAK,OAAO,OAAO,KAAK,SAAS,KAAK,KAAK;AAC9D,YAAM,eAAe,KAAK,SAAS;AACnC,YAAM,UAAU,CAAC,KAAK,MAAM,OAAO,KAAK,MAAM,IAAI;AAElD,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,cAAI,UAAU,IAAI,aAAa,KAAK;AACpC,cAAI,UAAU,KAAK,MAAM;AAEzB,cAAI,KAAK,gBAAgB,KAAK,gBAC5B,IAAI,aAAa,gBAAgB,IAAI,aAAa,cAAc;AAChE,kBAAM,OAAO,KAAK,OAAO,IAAI,gBAAgB,KAAK;AAClD,kBAAM,OAAO,KAAK,OAAO,IAAI,gBAAgB,KAAK;AAClD,sBAAU,QAAQ,KAAK,OAAO,OAAO,IAAI,IAAI,IAAI,CAAC;AAAA,UACpD;AAEA,kBAAQ,QAAQ,IAAI,QAAQ;AAC5B,kBAAQ,QAAQ,IAAI,QAAQ;AAC5B,kBAAQ,QAAQ,IAAI,QAAQ;AAC5B,kBAAQ,MAAM,IAAI,QAAQ;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AClGA;AAAA;AAAA,QAAM,QAAQ;AAEd,aAAS,YAAa,KAAK,QAAQ,MAAM;AACvC,UAAI,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAE/C,UAAI,CAAC,OAAO,MAAO,QAAO,QAAQ,CAAC;AACnC,aAAO,SAAS;AAChB,aAAO,QAAQ;AACf,aAAO,MAAM,SAAS,OAAO;AAC7B,aAAO,MAAM,QAAQ,OAAO;AAAA,IAC9B;AAEA,aAAS,mBAAoB;AAC3B,UAAI;AACF,eAAO,SAAS,cAAc,QAAQ;AAAA,MACxC,SAAS,GAAG;AACV,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD;AAAA,IACF;AAEA,YAAQ,SAAS,SAAS,OAAQ,QAAQ,QAAQ,SAAS;AACzD,UAAI,OAAO;AACX,UAAI,WAAW;AAEf,UAAI,OAAO,SAAS,gBAAgB,CAAC,UAAU,CAAC,OAAO,aAAa;AAClE,eAAO;AACP,iBAAS;AAAA,MACX;AAEA,UAAI,CAAC,QAAQ;AACX,mBAAW,iBAAiB;AAAA,MAC9B;AAEA,aAAO,MAAM,WAAW,IAAI;AAC5B,YAAM,OAAO,MAAM,cAAc,OAAO,QAAQ,MAAM,IAAI;AAE1D,YAAM,MAAM,SAAS,WAAW,IAAI;AACpC,YAAM,QAAQ,IAAI,gBAAgB,MAAM,IAAI;AAC5C,YAAM,cAAc,MAAM,MAAM,QAAQ,IAAI;AAE5C,kBAAY,KAAK,UAAU,IAAI;AAC/B,UAAI,aAAa,OAAO,GAAG,CAAC;AAE5B,aAAO;AAAA,IACT;AAEA,YAAQ,kBAAkB,SAAS,gBAAiB,QAAQ,QAAQ,SAAS;AAC3E,UAAI,OAAO;AAEX,UAAI,OAAO,SAAS,gBAAgB,CAAC,UAAU,CAAC,OAAO,aAAa;AAClE,eAAO;AACP,iBAAS;AAAA,MACX;AAEA,UAAI,CAAC,KAAM,QAAO,CAAC;AAEnB,YAAM,WAAW,QAAQ,OAAO,QAAQ,QAAQ,IAAI;AAEpD,YAAM,OAAO,KAAK,QAAQ;AAC1B,YAAM,eAAe,KAAK,gBAAgB,CAAC;AAE3C,aAAO,SAAS,UAAU,MAAM,aAAa,OAAO;AAAA,IACtD;AAAA;AAAA;;;AC9DA;AAAA;AAAA,QAAM,QAAQ;AAEd,aAAS,eAAgB,OAAO,QAAQ;AACtC,YAAM,QAAQ,MAAM,IAAI;AACxB,YAAM,MAAM,SAAS,OAAO,MAAM,MAAM;AAExC,aAAO,QAAQ,IACX,MAAM,MAAM,SAAS,eAAe,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,IAAI,MAChE;AAAA,IACN;AAEA,aAAS,OAAQ,KAAK,GAAG,GAAG;AAC1B,UAAI,MAAM,MAAM;AAChB,UAAI,OAAO,MAAM,YAAa,QAAO,MAAM;AAE3C,aAAO;AAAA,IACT;AAEA,aAAS,SAAU,MAAM,MAAM,QAAQ;AACrC,UAAI,OAAO;AACX,UAAI,SAAS;AACb,UAAI,SAAS;AACb,UAAI,aAAa;AAEjB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,MAAM,KAAK,MAAM,IAAI,IAAI;AAC/B,cAAM,MAAM,KAAK,MAAM,IAAI,IAAI;AAE/B,YAAI,CAAC,OAAO,CAAC,OAAQ,UAAS;AAE9B,YAAI,KAAK,CAAC,GAAG;AACX;AAEA,cAAI,EAAE,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,IAAI;AACtC,oBAAQ,SACJ,OAAO,KAAK,MAAM,QAAQ,MAAM,MAAM,MAAM,IAC5C,OAAO,KAAK,QAAQ,CAAC;AAEzB,qBAAS;AACT,qBAAS;AAAA,UACX;AAEA,cAAI,EAAE,MAAM,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI;AACpC,oBAAQ,OAAO,KAAK,UAAU;AAC9B,yBAAa;AAAA,UACf;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,SAAS,SAAS,OAAQ,QAAQ,SAAS,IAAI;AACrD,YAAM,OAAO,MAAM,WAAW,OAAO;AACrC,YAAM,OAAO,OAAO,QAAQ;AAC5B,YAAM,OAAO,OAAO,QAAQ;AAC5B,YAAM,aAAa,OAAO,KAAK,SAAS;AAExC,YAAM,KAAK,CAAC,KAAK,MAAM,MAAM,IACzB,KACA,WAAW,eAAe,KAAK,MAAM,OAAO,MAAM,IAClD,cAAc,aAAa,MAAM,aAAa;AAElD,YAAM,OACJ,WAAW,eAAe,KAAK,MAAM,MAAM,QAAQ,IACnD,SAAS,SAAS,MAAM,MAAM,KAAK,MAAM,IAAI;AAE/C,YAAM,UAAU,kBAAuB,aAAa,MAAM,aAAa;AAEvE,YAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,eAAe,KAAK,QAAQ;AAEtF,YAAM,SAAS,6CAA6C,QAAQ,UAAU,mCAAmC,KAAK,OAAO;AAE7H,UAAI,OAAO,OAAO,YAAY;AAC5B,WAAG,MAAM,MAAM;AAAA,MACjB;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AChFA;AAAA;AACA,QAAM,aAAa;AAEnB,QAAMC,UAAS;AACf,QAAM,iBAAiB;AACvB,QAAM,cAAc;AAEpB,aAAS,aAAc,YAAY,QAAQ,MAAM,MAAM,IAAI;AACzD,YAAM,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AACvC,YAAM,UAAU,KAAK;AACrB,YAAM,cAAc,OAAO,KAAK,UAAU,CAAC,MAAM;AAEjD,UAAI,CAAC,eAAe,CAAC,WAAW,GAAG;AACjC,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACtD;AAEA,UAAI,aAAa;AACf,YAAI,UAAU,GAAG;AACf,gBAAM,IAAI,MAAM,4BAA4B;AAAA,QAC9C;AAEA,YAAI,YAAY,GAAG;AACjB,eAAK;AACL,iBAAO;AACP,mBAAS,OAAO;AAAA,QAClB,WAAW,YAAY,GAAG;AACxB,cAAI,OAAO,cAAc,OAAO,OAAO,aAAa;AAClD,iBAAK;AACL,mBAAO;AAAA,UACT,OAAO;AACL,iBAAK;AACL,mBAAO;AACP,mBAAO;AACP,qBAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,UAAU,GAAG;AACf,gBAAM,IAAI,MAAM,4BAA4B;AAAA,QAC9C;AAEA,YAAI,YAAY,GAAG;AACjB,iBAAO;AACP,mBAAS,OAAO;AAAA,QAClB,WAAW,YAAY,KAAK,CAAC,OAAO,YAAY;AAC9C,iBAAO;AACP,iBAAO;AACP,mBAAS;AAAA,QACX;AAEA,eAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,cAAI;AACF,kBAAM,OAAOA,QAAO,OAAO,MAAM,IAAI;AACrC,oBAAQ,WAAW,MAAM,QAAQ,IAAI,CAAC;AAAA,UACxC,SAAS,GAAG;AACV,mBAAO,CAAC;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI;AACF,cAAM,OAAOA,QAAO,OAAO,MAAM,IAAI;AACrC,WAAG,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAC;AAAA,MACzC,SAAS,GAAG;AACV,WAAG,CAAC;AAAA,MACN;AAAA,IACF;AAEA,YAAQ,SAASA,QAAO;AACxB,YAAQ,WAAW,aAAa,KAAK,MAAM,eAAe,MAAM;AAChE,YAAQ,YAAY,aAAa,KAAK,MAAM,eAAe,eAAe;AAG1E,YAAQ,WAAW,aAAa,KAAK,MAAM,SAAU,MAAM,GAAG,MAAM;AAClE,aAAO,YAAY,OAAO,MAAM,IAAI;AAAA,IACtC,CAAC;AAAA;AAAA;;;AC3ED;AAAA;AAAA;AAMA,aAAS,KAAM,UAAU;AACvB,UAAI,SAAS,UAAU,KAAK;AAAE,cAAM,IAAI,UAAU,mBAAmB;AAAA,MAAE;AACvE,UAAI,WAAW,IAAI,WAAW,GAAG;AACjC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,iBAAS,CAAC,IAAI;AAAA,MAChB;AACA,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,IAAI,SAAS,OAAO,CAAC;AACzB,YAAI,KAAK,EAAE,WAAW,CAAC;AACvB,YAAI,SAAS,EAAE,MAAM,KAAK;AAAE,gBAAM,IAAI,UAAU,IAAI,eAAe;AAAA,QAAE;AACrE,iBAAS,EAAE,IAAI;AAAA,MACjB;AACA,UAAI,OAAO,SAAS;AACpB,UAAI,SAAS,SAAS,OAAO,CAAC;AAC9B,UAAI,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAC1C,UAAI,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAC3C,eAASC,QAAQ,QAAQ;AACvB,YAAI,kBAAkB,YAAY;AAAA,QAClC,WAAW,YAAY,OAAO,MAAM,GAAG;AACrC,mBAAS,IAAI,WAAW,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;AAAA,QAC7E,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,mBAAS,WAAW,KAAK,MAAM;AAAA,QACjC;AACA,YAAI,EAAE,kBAAkB,aAAa;AAAE,gBAAM,IAAI,UAAU,qBAAqB;AAAA,QAAE;AAClF,YAAI,OAAO,WAAW,GAAG;AAAE,iBAAO;AAAA,QAAG;AAErC,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,OAAO,OAAO;AAClB,eAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,GAAG;AAC9C;AACA;AAAA,QACF;AAEA,YAAI,QAAS,OAAO,UAAU,UAAU,MAAO;AAC/C,YAAI,MAAM,IAAI,WAAW,IAAI;AAE7B,eAAO,WAAW,MAAM;AACtB,cAAI,QAAQ,OAAO,MAAM;AAEzB,cAAIC,KAAI;AACR,mBAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAI,WAAY,QAAQ,IAAK,OAAOA,MAAK;AAChF,qBAAU,MAAM,IAAI,GAAG,MAAO;AAC9B,gBAAI,GAAG,IAAK,QAAQ,SAAU;AAC9B,oBAAS,QAAQ,SAAU;AAAA,UAC7B;AACA,cAAI,UAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAAE;AACrD,mBAASA;AACT;AAAA,QACF;AAEA,YAAI,MAAM,OAAO;AACjB,eAAO,QAAQ,QAAQ,IAAI,GAAG,MAAM,GAAG;AACrC;AAAA,QACF;AAEA,YAAI,MAAM,OAAO,OAAO,MAAM;AAC9B,eAAO,MAAM,MAAM,EAAE,KAAK;AAAE,iBAAO,SAAS,OAAO,IAAI,GAAG,CAAC;AAAA,QAAE;AAC7D,eAAO;AAAA,MACT;AACA,eAAS,aAAc,QAAQ;AAC7B,YAAI,OAAO,WAAW,UAAU;AAAE,gBAAM,IAAI,UAAU,iBAAiB;AAAA,QAAE;AACzE,YAAI,OAAO,WAAW,GAAG;AAAE,iBAAO,IAAI,WAAW;AAAA,QAAE;AACnD,YAAI,MAAM;AAEV,YAAI,SAAS;AACb,YAAI,SAAS;AACb,eAAO,OAAO,GAAG,MAAM,QAAQ;AAC7B;AACA;AAAA,QACF;AAEA,YAAI,QAAU,OAAO,SAAS,OAAO,SAAU,MAAO;AACtD,YAAI,OAAO,IAAI,WAAW,IAAI;AAE9B,eAAO,OAAO,GAAG,GAAG;AAElB,cAAI,WAAW,OAAO,WAAW,GAAG;AAEpC,cAAI,WAAW,KAAK;AAAE;AAAA,UAAO;AAE7B,cAAI,QAAQ,SAAS,QAAQ;AAE7B,cAAI,UAAU,KAAK;AAAE;AAAA,UAAO;AAC5B,cAAIA,KAAI;AACR,mBAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAI,WAAY,QAAQ,IAAK,OAAOA,MAAK;AAChF,qBAAU,OAAO,KAAK,GAAG,MAAO;AAChC,iBAAK,GAAG,IAAK,QAAQ,QAAS;AAC9B,oBAAS,QAAQ,QAAS;AAAA,UAC5B;AACA,cAAI,UAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAAE;AACrD,mBAASA;AACT;AAAA,QACF;AAEA,YAAI,MAAM,OAAO;AACjB,eAAO,QAAQ,QAAQ,KAAK,GAAG,MAAM,GAAG;AACtC;AAAA,QACF;AACA,YAAI,MAAM,IAAI,WAAW,UAAU,OAAO,IAAI;AAC9C,YAAIC,KAAI;AACR,eAAO,QAAQ,MAAM;AACnB,cAAIA,IAAG,IAAI,KAAK,KAAK;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,eAAS,OAAQ,QAAQ;AACvB,YAAI,SAAS,aAAa,MAAM;AAChC,YAAI,QAAQ;AAAE,iBAAO;AAAA,QAAO;AAC5B,cAAM,IAAI,MAAM,aAAa,OAAO,YAAY;AAAA,MAClD;AACA,aAAO;AAAA,QACL,QAAQF;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;AC5HjB;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,WAAW;AAEjB,WAAO,UAAU,MAAM,QAAQ;AAAA;AAAA;;;ACH/B,IAAAG,eAAA;AAAA;AAAA;AAMA,aAAS,KAAM,UAAU;AACvB,UAAI,SAAS,UAAU,KAAK;AAAE,cAAM,IAAI,UAAU,mBAAmB;AAAA,MAAE;AACvE,UAAI,WAAW,IAAI,WAAW,GAAG;AACjC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,iBAAS,CAAC,IAAI;AAAA,MAChB;AACA,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,IAAI,SAAS,OAAO,CAAC;AACzB,YAAI,KAAK,EAAE,WAAW,CAAC;AACvB,YAAI,SAAS,EAAE,MAAM,KAAK;AAAE,gBAAM,IAAI,UAAU,IAAI,eAAe;AAAA,QAAE;AACrE,iBAAS,EAAE,IAAI;AAAA,MACjB;AACA,UAAI,OAAO,SAAS;AACpB,UAAI,SAAS,SAAS,OAAO,CAAC;AAC9B,UAAI,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAC1C,UAAI,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAC3C,eAASC,QAAQ,QAAQ;AACvB,YAAI,kBAAkB,YAAY;AAAA,QAClC,WAAW,YAAY,OAAO,MAAM,GAAG;AACrC,mBAAS,IAAI,WAAW,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;AAAA,QAC7E,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,mBAAS,WAAW,KAAK,MAAM;AAAA,QACjC;AACA,YAAI,EAAE,kBAAkB,aAAa;AAAE,gBAAM,IAAI,UAAU,qBAAqB;AAAA,QAAE;AAClF,YAAI,OAAO,WAAW,GAAG;AAAE,iBAAO;AAAA,QAAG;AAErC,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,OAAO,OAAO;AAClB,eAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,GAAG;AAC9C;AACA;AAAA,QACF;AAEA,YAAI,QAAS,OAAO,UAAU,UAAU,MAAO;AAC/C,YAAI,MAAM,IAAI,WAAW,IAAI;AAE7B,eAAO,WAAW,MAAM;AACtB,cAAI,QAAQ,OAAO,MAAM;AAEzB,cAAIC,KAAI;AACR,mBAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAI,WAAY,QAAQ,IAAK,OAAOA,MAAK;AAChF,qBAAU,MAAM,IAAI,GAAG,MAAO;AAC9B,gBAAI,GAAG,IAAK,QAAQ,SAAU;AAC9B,oBAAS,QAAQ,SAAU;AAAA,UAC7B;AACA,cAAI,UAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAAE;AACrD,mBAASA;AACT;AAAA,QACF;AAEA,YAAI,MAAM,OAAO;AACjB,eAAO,QAAQ,QAAQ,IAAI,GAAG,MAAM,GAAG;AACrC;AAAA,QACF;AAEA,YAAI,MAAM,OAAO,OAAO,MAAM;AAC9B,eAAO,MAAM,MAAM,EAAE,KAAK;AAAE,iBAAO,SAAS,OAAO,IAAI,GAAG,CAAC;AAAA,QAAE;AAC7D,eAAO;AAAA,MACT;AACA,eAAS,aAAc,QAAQ;AAC7B,YAAI,OAAO,WAAW,UAAU;AAAE,gBAAM,IAAI,UAAU,iBAAiB;AAAA,QAAE;AACzE,YAAI,OAAO,WAAW,GAAG;AAAE,iBAAO,IAAI,WAAW;AAAA,QAAE;AACnD,YAAI,MAAM;AAEV,YAAI,SAAS;AACb,YAAI,SAAS;AACb,eAAO,OAAO,GAAG,MAAM,QAAQ;AAC7B;AACA;AAAA,QACF;AAEA,YAAI,QAAU,OAAO,SAAS,OAAO,SAAU,MAAO;AACtD,YAAI,OAAO,IAAI,WAAW,IAAI;AAE9B,eAAO,OAAO,GAAG,GAAG;AAElB,cAAI,WAAW,OAAO,WAAW,GAAG;AAEpC,cAAI,WAAW,KAAK;AAAE;AAAA,UAAO;AAE7B,cAAI,QAAQ,SAAS,QAAQ;AAE7B,cAAI,UAAU,KAAK;AAAE;AAAA,UAAO;AAC5B,cAAIA,KAAI;AACR,mBAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAI,WAAY,QAAQ,IAAK,OAAOA,MAAK;AAChF,qBAAU,OAAO,KAAK,GAAG,MAAO;AAChC,iBAAK,GAAG,IAAK,QAAQ,QAAS;AAC9B,oBAAS,QAAQ,QAAS;AAAA,UAC5B;AACA,cAAI,UAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAAE;AACrD,mBAASA;AACT;AAAA,QACF;AAEA,YAAI,MAAM,OAAO;AACjB,eAAO,QAAQ,QAAQ,KAAK,GAAG,MAAM,GAAG;AACtC;AAAA,QACF;AACA,YAAI,MAAM,IAAI,WAAW,UAAU,OAAO,IAAI;AAC9C,YAAIC,KAAI;AACR,eAAO,QAAQ,MAAM;AACnB,cAAIA,IAAG,IAAI,KAAK,KAAK;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,eAAS,OAAQ,QAAQ;AACvB,YAAI,SAAS,aAAa,MAAM;AAChC,YAAI,QAAQ;AAAE,iBAAO;AAAA,QAAO;AAC5B,cAAM,IAAI,MAAM,aAAa,OAAO,YAAY;AAAA,MAClD;AACA,aAAO;AAAA,QACL,QAAQF;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;AC5HjB,IAAAG,gBAAA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,WAAW;AAEjB,WAAO,UAAU,MAAM,QAAQ;AAAA;AAAA;;;ACH/B;AACA,IAAAC,gBAAwD;;;ACAxD,mBAA0C;AAMnC,IAAM,wBAAoB,4BAAsC,CAAA,CAA4B;AAE7F,SAAU,gBAAa;AACzB,aAAO,yBAAW,iBAAiB;AACvC;;;ADDO,IAAM,qBAAkD,CAAC,EAC5D,UACA,UACA,SAAS,EAAE,YAAY,YAAW,EAAE,MACnC;AACD,QAAM,iBAAa,uBAAQ,MAAM,IAAI,WAAW,UAAU,MAAM,GAAG,CAAC,UAAU,MAAM,CAAC;AAErF,SAAO,cAAAC,QAAA,cAAC,kBAAkB,UAAQ,EAAC,OAAO,EAAE,WAAU,EAAE,GAAG,QAAQ;AACvE;;;AEhBM,IAAO,yBAAP,cAAsC,YAAW;EAAvD,cAAA;;AACI,SAAA,OAAO;EACX;;;;ACHA,IAAAC,gBAAwB;;;ACSxB,IAAAC,gBAA0C;AA2B1C,IAAM,cAAoC,CAAA;AAE1C,IAAM,kBAA+C;EACjD,aAAa;EACb,YAAY;EACZ,WAAW;EACX,eAAe;EACf,SAAM;AACF,4BAAwB,QAAQ,QAAQ;EAC5C;EACA,UAAO;AACH,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,SAAS,CAAC;EACpE;EACA,aAAU;AACN,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,YAAY,CAAC;EACvE;EACA,kBAAe;AACX,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,iBAAiB,CAAC;EAC5E;EACA,kBAAe;AACX,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,iBAAiB,CAAC;EAC5E;EACA,sBAAmB;AACf,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,qBAAqB,CAAC;EAChF;EACA,cAAW;AACP,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,aAAa,CAAC;EACxE;EACA,SAAM;AACF,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,QAAQ,CAAC;EACnE;;AAEJ,OAAO,eAAe,iBAAiB,WAAW;EAC9C,MAAG;AACC,4BAAwB,QAAQ,SAAS;AACzC,WAAO;EACX;CACH;AACD,OAAO,eAAe,iBAAiB,UAAU;EAC7C,MAAG;AACC,4BAAwB,QAAQ,QAAQ;AACxC,WAAO;EACX;CACH;AACD,OAAO,eAAe,iBAAiB,aAAa;EAChD,MAAG;AACC,4BAAwB,QAAQ,WAAW;AAC3C,WAAO;EACX;CACH;AAED,SAAS,wBAAwB,QAAgB,UAAgB;AAC7D,QAAM,QAAQ,IAAI,MACd,qBAAqB,MAAM,KAAK,QAAQ,2IAC2D;AAEvG,UAAQ,MAAM,KAAK;AACnB,SAAO;AACX;AAEO,IAAM,oBAAgB,6BAAkC,eAAqC;AAE9F,SAAU,YAAS;AACrB,aAAO,0BAAW,aAAa;AACnC;;;AD3FM,SAAU,kBAAe;AAC3B,QAAM,EAAE,WAAW,iBAAiB,oBAAmB,IAAK,UAAS;AACrE,aAAO,uBACH,MACI,aAAa,mBAAmB,sBAC1B,EAAE,WAAW,iBAAiB,oBAAmB,IACjD,QACV,CAAC,WAAW,iBAAiB,mBAAmB,CAAC;AAEzD;;;AEnBA,IAAAC,gBAAgF;AAE1E,SAAU,gBAAmB,KAAa,cAAe;AAC3D,QAAM,YAAQ,wBAAY,MAAK;AAC3B,QAAI;AACA,YAAMC,SAAQ,aAAa,QAAQ,GAAG;AACtC,UAAIA;AAAO,eAAO,KAAK,MAAMA,MAAK;IACtC,SAAS,OAAY;AACjB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,MAAM,KAAK;MACvB;IACJ;AAEA,WAAO;EACX,CAAC;AACD,QAAM,QAAQ,MAAM,CAAC;AAErB,QAAM,uBAAmB,sBAAO,IAAI;AACpC,+BAAU,MAAK;AACX,QAAI,iBAAiB,SAAS;AAC1B,uBAAiB,UAAU;AAC3B;IACJ;AACA,QAAI;AACA,UAAI,UAAU,MAAM;AAChB,qBAAa,WAAW,GAAG;MAC/B,OAAO;AACH,qBAAa,QAAQ,KAAK,KAAK,UAAU,KAAK,CAAC;MACnD;IACJ,SAAS,OAAY;AACjB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,MAAM,KAAK;MACvB;IACJ;EACJ,GAAG,CAAC,OAAO,GAAG,CAAC;AAEf,SAAO;AACX;;;ACpCA;;;ACAA;AACA,oBAAmB;;;ACFnB;;;ACGA,IAAM,qCAAqC;AAAA,EACvC,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,gCAAgC;AAAA,EAChC,6BAA6B;AACjC;AACA,IAAM,iCAAN,cAA6C,MAAM;AAAA,EAC/C,eAAe,MAAM;AACjB,UAAM,CAAC,MAAM,SAAS,IAAI,IAAI;AAC9B,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EAChB;AACJ;AAWA,IAAM,yCAAN,cAAqD,MAAM;AAAA,EACvD,eAAe,MAAM;AACjB,UAAM,CAAC,kBAAkB,MAAM,SAAS,IAAI,IAAI;AAChD,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,mBAAmB;AACxB,SAAK,OAAO;AAAA,EAChB;AACJ;AAiBA,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AAClD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAEA,SAAS,OAAO,OAAO;AACnB,SAAO,OAAO,KAAK,KAAK;AAC5B;AAmBA,SAAS,eAAe,eAAe,8BAA8B;AACjE,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAM,kBAAkB,MAAM,OAAO,OAAO,UAAU,OAAO,aAAa;AAC1E,UAAM,kBAAkB,MAAM,OAAO,OAAO,KAAK,EAAE,MAAM,WAAW,MAAM,QAAQ,GAAG,8BAA8B,eAAe;AAClI,UAAM,WAAW,IAAI,WAAW,gBAAgB,aAAa,gBAAgB,UAAU;AACvF,aAAS,IAAI,IAAI,WAAW,eAAe,GAAG,CAAC;AAC/C,aAAS,IAAI,IAAI,WAAW,eAAe,GAAG,gBAAgB,UAAU;AACxE,WAAO;AAAA,EACX,CAAC;AACL;AAEA,SAAS,kBAAkB,SAAS;AAChC,SAAO,wBAAwB,OAAO;AAC1C;AACA,SAAS,wBAAwB,SAAS;AACtC,SAAO,OAAO,kBAAkB,OAAO,CAAC;AAC5C;AAGA,IAAM,yBAAyB;AAC/B,IAAM,2BAA2B;AAUjC,SAAS,wBAAwB,iBAAiB,wBAAwB;AACtE,SAAO,IAAI,MAAM,CAAC,GAAG;AAAA,IACjB,IAAI,QAAQ,GAAG;AAKX,UAAI,MAAM,QAAQ;AACd,eAAO;AAAA,MACX;AACA,UAAI,OAAO,CAAC,KAAK,MAAM;AACnB,eAAO,CAAC,IAAI,SAAU,aAAa;AAC/B,iBAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,kBAAM,EAAE,QAAQ,OAAO,IAAI,0BAA0B,GAAG,aAAa,eAAe;AACpF,kBAAM,SAAS,MAAM,uBAAuB,QAAQ,MAAM;AAE1D,gBAAI,WAAW,eAAe,OAAO,mBAAmB,CAAC,OAAO,gBAAgB;AAC5E,qBAAO,gBAAgB,IAAI,MAAM,eAAe,OAAO,iBAAiB,QAAQ,sBAAsB;AAAA,YAC1G;AACA,mBAAO,2BAA2B,GAAG,QAAQ,eAAe;AAAA,UAChE,CAAC;AAAA,QACL;AAAA,MACJ;AACA,aAAO,OAAO,CAAC;AAAA,IACnB;AAAA,IACA,iBAAiB;AACb,aAAO;AAAA,IACX;AAAA,IACA,iBAAiB;AACb,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AAUA,SAAS,0BAA0B,YAAY,cAAc,iBAAiB;AAC1E,MAAI,SAAS;AACb,MAAI,SAAS,WACR,SAAS,EACT,QAAQ,UAAU,CAAC,WAAW,IAAI,OAAO,YAAY,CAAC,EAAE,EACxD,YAAY;AACjB,UAAQ,YAAY;AAAA,IAChB,KAAK,aAAa;AACd,UAAI,EAAE,MAAM,IAAI;AAChB,UAAI,oBAAoB,UAAU;AAC9B,gBAAQ,OAAO;AAAA,UACX,KAAK,kBAAkB;AACnB,oBAAQ;AACR;AAAA,UACJ;AAAA,UACA,KAAK,iBAAiB;AAClB,oBAAQ;AACR;AAAA,UACJ;AAAA,UACA,KAAK,kBAAkB;AACnB,oBAAQ;AACR;AAAA,UACJ;AAAA,UACA,SAAS;AACL,oBAAQ,OAAO;AAAA,UACnB;AAAA,QACJ;AACA,eAAO,UAAU;AAAA,MACrB,OACK;AACD,gBAAQ,OAAO;AAAA,UACX,KAAK;AAAA,UACL,KAAK,UAAU;AACX,oBAAQ,UAAU,KAAK;AACvB;AAAA,UACJ;AAAA,UACA,KAAK,gBAAgB;AACjB,oBAAQ;AACR;AAAA,UACJ;AAAA,QACJ;AACA,eAAO,QAAQ;AAAA,MACnB;AAAA,IACJ;AAAA,IACA,KAAK,eAAe;AAChB,YAAM,EAAE,YAAY,SAAS,IAAI;AACjC,UAAI,YAAY;AACZ,gBAAQ,iBAAiB;AAAA,UACrB,KAAK,UAAU;AACX,qBAAS;AACT,qBAAS,EAAE,YAAwB,SAAmB;AACtD;AAAA,UACJ;AAAA,UACA,SAAS;AACL,qBAAS;AACT;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,QAAQ,OAAO;AAC5B;AASA,SAAS,2BAA2B,QAAQ,UAAU,iBAAiB;AACnE,UAAQ,QAAQ;AAAA,IACZ,KAAK,mBAAmB;AACpB,YAAM,eAAe;AACrB,cAAQ,iBAAiB;AAAA,QACrB,KAAK,UAAU;AACX,gBAAM,WAAW,CAAC,sBAAsB;AACxC,cAAI,aAAa,iCAAiC,MAAM;AACpD,qBAAS,KAAK,wBAAwB;AAAA,UAC1C;AACA,iBAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,SAAmB,CAAC;AAAA,QAChF;AAAA,QACA,KAAK,MAAM;AACP,iBAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,qCAAqC,MAAM,8BAA8B,aAAa,SAAS,SAAS,wBAAwB,EAAE,CAAC;AAAA,QAC/L;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,eAAe,eAAe,qBAAqB,wBAAwB;AAChF,MAAI;AACJ,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAM,UAAU,KAAK,cAAc,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO,SAAS;AAC5F,UAAM,UAAU,oBAAoB,SAAS,CAAC,EAAE;AAChD,UAAM,cAAc,wBAAwB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,GAAG,EAAE,QAAQ,QAAQ,CAAC,CAAC;AAChH,UAAM,oBAAoB,MAAM,uBAAuB,iBAAiB;AAAA,MACpE,WAAW,CAAC,OAAO;AAAA,MACnB,UAAU,CAAC,WAAW;AAAA,IAC1B,CAAC;AACD,UAAM,eAAe;AAAA,MACjB;AAAA,MACA,gBAAgB;AAAA,MAChB,WAAW,kBAAkB,gBAAgB,CAAC,EAAE,MAAM,YAAY,MAAM;AAAA,IAC5E;AACA,WAAO;AAAA,EACX,CAAC;AACL;AAEA,IAAM,wBAAwB;AAC9B,SAAS,2BAA2B,gBAAgB;AAChD,MAAI,kBAAkB,YAAY;AAC9B,UAAM,IAAI,MAAM,6EAA6E;AAAA,EACjG;AACA,QAAM,YAAY,IAAI,YAAY,qBAAqB;AACvD,QAAM,OAAO,IAAI,SAAS,SAAS;AACnC,OAAK;AAAA,IAAU;AAAA,IAAG;AAAA;AAAA,IAAmC;AAAA,EAAK;AAC1D,SAAO,IAAI,WAAW,SAAS;AACnC;AAEA,IAAM,8BAA8B;AACpC,IAAM,kCAAkC;AACxC,SAAS,eAAe,WAAW,gBAAgB,cAAc;AAC7D,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAM,uBAAuB,2BAA2B,cAAc;AACtE,UAAM,uBAAuB,IAAI,WAAW,2BAA2B;AACvE,WAAO,gBAAgB,oBAAoB;AAC3C,UAAM,aAAa,MAAM,OAAO,OAAO,QAAQ,mBAAmB,sBAAsB,oBAAoB,GAAG,cAAc,IAAI,YAAY,EAAE,OAAO,SAAS,CAAC;AAChK,UAAM,WAAW,IAAI,WAAW,qBAAqB,aAAa,qBAAqB,aAAa,WAAW,UAAU;AACzH,aAAS,IAAI,IAAI,WAAW,oBAAoB,GAAG,CAAC;AACpD,aAAS,IAAI,IAAI,WAAW,oBAAoB,GAAG,qBAAqB,UAAU;AAClF,aAAS,IAAI,IAAI,WAAW,UAAU,GAAG,qBAAqB,aAAa,qBAAqB,UAAU;AAC1G,WAAO;AAAA,EACX,CAAC;AACL;AACA,SAAS,eAAe,SAAS,cAAc;AAC3C,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAM,uBAAuB,QAAQ,MAAM,GAAG,qBAAqB;AACnE,UAAM,uBAAuB,QAAQ,MAAM,uBAAuB,wBAAwB,2BAA2B;AACrH,UAAM,aAAa,QAAQ,MAAM,wBAAwB,2BAA2B;AACpF,UAAM,kBAAkB,MAAM,OAAO,OAAO,QAAQ,mBAAmB,sBAAsB,oBAAoB,GAAG,cAAc,UAAU;AAC5I,UAAM,YAAY,eAAe,EAAE,OAAO,eAAe;AACzD,WAAO;AAAA,EACX,CAAC;AACL;AACA,SAAS,mBAAmB,gBAAgB,sBAAsB;AAC9D,SAAO;AAAA,IACH,gBAAgB;AAAA,IAChB,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,WAAW;AAAA;AAAA,EACf;AACJ;AACA,IAAI;AACJ,SAAS,iBAAiB;AACtB,MAAI,iBAAiB,QAAW;AAC5B,mBAAe,IAAI,YAAY,OAAO;AAAA,EAC1C;AACA,SAAO;AACX;AAEA,SAAS,6BAA6B;AAClC,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,WAAO,MAAM,OAAO,OAAO;AAAA,MAAY;AAAA,QACnC,MAAM;AAAA,QACN,YAAY;AAAA,MAChB;AAAA,MAAG;AAAA,MAAyB,CAAC,MAAM;AAAA;AAAA,IAAiB;AAAA,EACxD,CAAC;AACL;AAEA,SAAS,sBAAsB;AAC3B,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,WAAO,MAAM,OAAO,OAAO;AAAA,MAAY;AAAA,QACnC,MAAM;AAAA,QACN,YAAY;AAAA,MAChB;AAAA,MAAG;AAAA,MAAyB,CAAC,aAAa,YAAY;AAAA;AAAA,IAAiB;AAAA,EAC3E,CAAC;AACL;AAGA,SAAS,0BAA0B,QAAQ;AACvC,MAAI,SAAS;AACb,QAAM,QAAQ,IAAI,WAAW,MAAM;AACnC,QAAM,MAAM,MAAM;AAClB,WAAS,KAAK,GAAG,KAAK,KAAK,MAAM;AAC7B,cAAU,OAAO,aAAa,MAAM,EAAE,CAAC;AAAA,EAC3C;AACA,SAAO,OAAO,KAAK,MAAM;AAC7B;AAEA,SAAS,2BAA2B;AAChC,SAAO,sBAAsB,QAAQ,KAAK,MAAM,KAAK,OAAO,KAAK,QAAQ,QAAQ,EAAE,CAAC;AACxF;AACA,SAAS,sBAAsB,MAAM;AACjC,MAAI,OAAO,SAAS,OAAO,OAAO;AAC9B,UAAM,IAAI,+BAA+B,mCAAmC,qCAAqC,4DAA4D,IAAI,WAAW,EAAE,KAAK,CAAC;AAAA,EACxM;AACA,SAAO;AACX;AAEA,SAAS,yCAAyC,2BAA2B;AACzE,SAAO,0BAA0B,QAAQ,UAAU,CAAC,OAAO;AAAA,IACvD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACT,GAAE,CAAC,CAAE;AACT;AAEA,IAAM,cAAc;AACpB,SAAS,aAAa,YAAY;AAC9B,SAAQ,WAEH,QAAQ,gBAAgB,EAAE,EAE1B,MAAM,GAAG;AAClB;AACA,SAAS,aAAa,gBAAgB,eAAe;AACjD,MAAI,UAAU;AACd,MAAI,eAAe;AACf,QAAI;AACA,gBAAU,IAAI,IAAI,aAAa;AAAA,IACnC,SACO,IAAI;AAAA,IAAE;AACb,SAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,UAAU;AACnF,YAAM,IAAI,+BAA+B,mCAAmC,iCAAiC,0DAA0D;AAAA,IAC3K;AAAA,EACJ;AACA,cAAY,UAAU,IAAI,IAAI,GAAG,WAAW,IAAI;AAChD,QAAM,WAAW,eAAe,WAAW,GAAG;AAAA;AAAA,IAEtC;AAAA;AAAA;AAAA,IAEA,CAAC,GAAG,aAAa,QAAQ,QAAQ,GAAG,GAAG,aAAa,cAAc,CAAC,EAAE,KAAK,GAAG;AAAA;AACrF,SAAO,IAAI,IAAI,UAAU,OAAO;AACpC;AACA,SAAS,6BAA6B,sBAAsB,cAAc,oBAAoB,mBAAmB,CAAC,IAAI,GAAG;AACrH,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAM,kBAAkB,sBAAsB,YAAY;AAC1D,UAAM,cAAc,MAAM,OAAO,OAAO,UAAU,OAAO,oBAAoB;AAC7E,UAAM,aAAa,0BAA0B,WAAW;AACxD,UAAM,MAAM,aAAa,sBAAsB,kBAAkB;AACjE,QAAI,aAAa,IAAI,eAAe,yCAAyC,UAAU,CAAC;AACxF,QAAI,aAAa,IAAI,QAAQ,GAAG,eAAe,EAAE;AACjD,qBAAiB,QAAQ,CAAC,YAAY;AAClC,UAAI,aAAa,IAAI,KAAK,OAAO;AAAA,IACrC,CAAC;AACD,WAAO;AAAA,EACX,CAAC;AACL;AAgBA,SAAS,sBAAsB,gBAAgB,cAAc;AACzD,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAM,YAAY,KAAK,UAAU,cAAc;AAC/C,UAAM,iBAAiB,eAAe;AACtC,WAAO,eAAe,WAAW,gBAAgB,YAAY;AAAA,EACjE,CAAC;AACL;AACA,SAAS,sBAAsB,SAAS,cAAc;AAClD,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAM,YAAY,MAAM,eAAe,SAAS,YAAY;AAC5D,UAAM,iBAAiB,KAAK,MAAM,SAAS;AAC3C,QAAI,OAAO,eAAe,KAAK,gBAAgB,OAAO,GAAG;AACrD,YAAM,IAAI,uCAAuC,eAAe,IAAI,eAAe,MAAM,MAAM,eAAe,MAAM,OAAO;AAAA,IAC/H;AACA,WAAO;AAAA,EACX,CAAC;AACL;AAEA,SAAS,cAAc,eACvB,sBAAsB,gBAAgB;AAClC,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAM,CAAC,4BAA4B,eAAe,IAAI,MAAM,QAAQ,IAAI;AAAA,MACpE,OAAO,OAAO,UAAU,OAAO,oBAAoB;AAAA,MACnD,OAAO,OAAO;AAAA,QAAU;AAAA,QAAO,cAAc,MAAM,GAAG,+BAA+B;AAAA,QAAG,EAAE,MAAM,QAAQ,YAAY,QAAQ;AAAA,QAAG;AAAA,QAAyB,CAAC;AAAA;AAAA,MAAiB;AAAA,IAC9K,CAAC;AACD,UAAM,eAAe,MAAM,OAAO,OAAO,WAAW,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,GAAG,gBAAgB,GAAG;AAClH,UAAM,gBAAgB,MAAM,OAAO,OAAO;AAAA,MAAU;AAAA,MAAO;AAAA,MAAc;AAAA,MAAQ;AAAA,MAAyB,CAAC,WAAW;AAAA;AAAA,IAAiB;AACvI,UAAM,oBAAoB,MAAM,OAAO,OAAO,UAAU;AAAA,MACpD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM,IAAI,WAAW,0BAA0B;AAAA,MAC/C,MAAM,IAAI,WAAW;AAAA,IACzB,GAAG,eAAe,EAAE,MAAM,WAAW,QAAQ,IAAI,GAAG,OAAyB,CAAC,WAAW,SAAS,CAAC;AACnG,WAAO;AAAA,EACX,CAAC;AACL;AAEA,SAAS,kBAAkB,SAAS,cAAc;AAC9C,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAM,YAAY,MAAM,eAAe,SAAS,YAAY;AAC5D,UAAM,iBAAiB,KAAK,MAAM,SAAS;AAC3C,QAAI,kBAAkB;AACtB,QAAI,OAAO,eAAe,KAAK,gBAAgB,GAAG,GAAG;AACjD,cAAQ,eAAe,GAAG;AAAA,QACtB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,4BAAkB;AAClB;AAAA,QACJ,KAAK;AACD,4BAAkB;AAClB;AAAA,QACJ;AACI,gBAAM,IAAI,+BAA+B,mCAAmC,gCAAgC,yCAAyC,eAAe,CAAC,EAAE;AAAA,MAC/K;AAAA,IACJ;AACA,WAAQ;AAAA,MACJ,kBAAkB;AAAA,IACtB;AAAA,EACJ,CAAC;AACL;AAGA,IAAM,UAAU;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AACX;AACA,SAAS,kBAAkB,GAAG;AAC1B,SAAO;AACX;AACA,SAAS,aAAa;AAClB,SAAO,UAAU,UAAU,QAAQ,UAAU,MAAM,KAAK,QAAQ,UAAU,QAAQ;AACtF;AACA,SAAS,sBAAsB;AAI3B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,aAAS,UAAU;AACf,mBAAa,SAAS;AACtB,aAAO,oBAAoB,QAAQ,UAAU;AAAA,IACjD;AACA,aAAS,aAAa;AAClB,cAAQ;AACR,cAAQ;AAAA,IACZ;AACA,WAAO,iBAAiB,QAAQ,UAAU;AAC1C,UAAM,YAAY,WAAW,MAAM;AAC/B,cAAQ;AACR,aAAO;AAAA,IACX,GAAG,GAAI;AAAA,EACX,CAAC;AACL;AACA,IAAI,SAAS;AACb,SAAS,4BAA4B,KAAK;AACtC,MAAI,UAAU,MAAM;AAChB,aAAS,SAAS,cAAc,QAAQ;AACxC,WAAO,MAAM,UAAU;AACvB,aAAS,KAAK,YAAY,MAAM;AAAA,EACpC;AAEA,SAAO,cAAc,SAAS,OAAO,IAAI,SAAS;AACtD;AACA,SAAS,kBAAkB,gBAAgB;AACvC,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,QAAI,eAAe,aAAa,UAAU;AAItC,aAAO,SAAS,OAAO,cAAc;AAAA,IACzC,OACK;AAED,UAAI;AACA,cAAM,UAAU,WAAW;AAC3B,gBAAQ,SAAS;AAAA,UACb,KAAK,QAAQ;AAET,wCAA4B,cAAc;AAE1C;AAAA,UACJ,KAAK,QAAQ,OAAO;AAChB,kBAAM,mBAAmB,oBAAoB;AAC7C,mBAAO,SAAS,OAAO,cAAc;AACrC,kBAAM;AACN;AAAA,UACJ;AAAA,UACA;AACI,8BAAkB,OAAO;AAAA,QACjC;AAAA,MACJ,SACO,GAAG;AACN,cAAM,IAAI,+BAA+B,mCAAmC,wBAAwB,qEAAqE;AAAA,MAC7K;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACA,SAAS,aAAa,sBAAsB,oBAAoB;AAC5D,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAM,wBAAwB,yBAAyB;AACvD,UAAM,iBAAiB,MAAM,6BAA6B,sBAAsB,uBAAuB,kBAAkB;AACzH,UAAM,kBAAkB,cAAc;AACtC,WAAO;AAAA,EACX,CAAC;AACL;AAEA,IAAM,8BAA8B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWhC,sBAAsB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAI;AAAA,EAC9D,WAAW;AACf;AACA,IAAM,4BAA4B;AAElC,SAAS,sBAAsB;AAC3B,MAAI,OAAO,WAAW,eAAe,OAAO,oBAAoB,MAAM;AAClE,UAAM,IAAI,+BAA+B,mCAAmC,+BAA+B,gFAAgF;AAAA,EAC/L;AACJ;AACA,SAAS,gCAAgC,eAAe;AACpD,MAAI;AACJ,MAAI;AACA,UAAM,IAAI,IAAI,aAAa;AAAA,EAC/B,SACO,IAAI;AACP,UAAM,IAAI,+BAA+B,mCAAmC,iCAAiC,qCAAqC;AAAA,EACtJ;AACA,MAAI,IAAI,aAAa,UAAU;AAC3B,UAAM,IAAI,+BAA+B,mCAAmC,iCAAiC,0DAA0D;AAAA,EAC3K;AACJ;AACA,SAAS,+BAA+B,WAAW;AAC/C,QAAM,OAAO,IAAI,SAAS,SAAS;AACnC,SAAO,KAAK;AAAA,IAAU;AAAA;AAAA,IAAsB;AAAA,EAAK;AACrD;AAeA,SAAS,SAAS,UAAU,QAAQ;AAChC,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,wBAAoB;AACpB,UAAM,qBAAqB,MAAM,2BAA2B;AAC5D,UAAM,cAAc,MAAM,aAAa,mBAAmB,WAAW,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,OAAO;AACnI,UAAM,eAAe,kBAAkB,WAAW;AAClD,QAAI;AACJ,UAAM,uBAAuB,MAAM;AAC/B,YAAM,WAAW,CAAC,GAAG,4BAA4B,oBAAoB;AACrE,aAAO,MAAO,SAAS,SAAS,IAAI,SAAS,MAAM,IAAI,SAAS,CAAC;AAAA,IACrE,GAAG;AACH,QAAI,uBAAuB;AAC3B,QAAI,iCAAiC;AACrC,QAAI,QAAQ,EAAE,QAAQ,eAAe;AACrC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAI;AAEJ,YAAM,0BAA0B,CAAC;AACjC,YAAM,aAAa,MAAM,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAClE,YAAI,MAAM,WAAW,cAAc;AAC/B,kBAAQ,KAAK,wFACA,MAAM,MAAM,KAAK;AAC9B;AAAA,QACJ;AACA,eAAO,oBAAoB,QAAQ,UAAU;AAO7C,cAAM,EAAE,oBAAAC,oBAAmB,IAAI;AAC/B,cAAM,cAAc,MAAM,oBAAoB;AAC9C,eAAO,KAAK,MAAM,eAAe,YAAY,WAAWA,oBAAmB,UAAU,CAAC;AACtF,gBAAQ;AAAA,UACJ,QAAQ;AAAA,UACR,sBAAsBA,oBAAmB;AAAA,UACzC,gBAAgB,YAAY;AAAA,QAChC;AAAA,MACJ,CAAC;AACD,YAAM,cAAc,CAAC,QAAQ;AACzB,YAAI,IAAI,UAAU;AACd,kBAAQ,EAAE,QAAQ,eAAe;AAAA,QACrC,OACK;AACD,iBAAO,IAAI,+BAA+B,mCAAmC,sBAAsB,4CAA4C,IAAI,IAAI,KAAK,IAAI,MAAM,MAAM,EAAE,YAAY,IAAI,CAAC,CAAC;AAAA,QACpM;AACA,sBAAc;AAAA,MAClB;AACA,YAAM,cAAc,CAAC,SAAS,UAAU,MAAM,QAAQ,QAAQ,aAAa;AACvE,sBAAc;AACd,YAAI,KAAK,IAAI,IAAI,uBAAuB,4BAA4B,WAAW;AAC3E,iBAAO,IAAI,+BAA+B,mCAAmC,uBAAuB,gDAAgD,YAAY,GAAG,CAAC;AAAA,QACxK,OACK;AACD,gBAAM,IAAI,QAAQ,CAACC,aAAY;AAC3B,kBAAM,eAAe,oBAAoB;AACzC,iCAAqB,OAAO,WAAWA,UAAS,YAAY;AAAA,UAChE,CAAC;AACD,kCAAwB;AAAA,QAC5B;AAAA,MACJ,CAAC;AACD,YAAM,gBAAgB,CAAC,QAAQ,UAAU,MAAM,QAAQ,QAAQ,aAAa;AACxE,cAAM,iBAAiB,MAAM,IAAI,KAAK,YAAY;AAClD,gBAAQ,MAAM,QAAQ;AAAA,UAClB,KAAK;AACD,gBAAI,eAAe,eAAe,GAAG;AACjC,oBAAM,IAAI,MAAM,iDAAiD;AAAA,YACrE;AACA,kBAAM,cAAc,MAAM,oBAAoB;AAC9C,mBAAO,KAAK,MAAM,eAAe,YAAY,WAAW,mBAAmB,UAAU,CAAC;AACtF,oBAAQ;AAAA,cACJ,QAAQ;AAAA,cACR,sBAAsB,mBAAmB;AAAA,cACzC,gBAAgB,YAAY;AAAA,YAChC;AACA;AAAA,UACJ,KAAK;AACD,gBAAI;AACA,oBAAM,uBAAuB,eAAe,MAAM,GAAG,qBAAqB;AAC1E,oBAAM,iBAAiB,+BAA+B,oBAAoB;AAC1E,kBAAI,mBAAoB,iCAAiC,GAAI;AACzD,sBAAM,IAAI,MAAM,+CAA+C;AAAA,cACnE;AACA,+CAAiC;AACjC,oBAAM,iBAAiB,MAAM,sBAAsB,gBAAgB,MAAM,YAAY;AACrF,oBAAM,kBAAkB,wBAAwB,eAAe,EAAE;AACjE,qBAAO,wBAAwB,eAAe,EAAE;AAChD,8BAAgB,QAAQ,eAAe,MAAM;AAAA,YACjD,SACO,GAAG;AACN,kBAAI,aAAa,wCAAwC;AACrD,sBAAM,kBAAkB,wBAAwB,EAAE,gBAAgB;AAClE,uBAAO,wBAAwB,EAAE,gBAAgB;AACjD,gCAAgB,OAAO,CAAC;AAAA,cAC5B,OACK;AACD,sBAAM;AAAA,cACV;AAAA,YACJ;AACA;AAAA,UACJ,KAAK,kBAAkB;AAEnB,gBAAI,eAAe,eAAe,GAAG;AACjC,oBAAMC,eAAc,MAAM,oBAAoB;AAC9C,qBAAO,KAAK,MAAM,eAAeA,aAAY,WAAW,mBAAmB,UAAU,CAAC;AACtF,sBAAQ;AAAA,gBACJ,QAAQ;AAAA,gBACR,sBAAsB,mBAAmB;AAAA,gBACzC,gBAAgBA,aAAY;AAAA,cAChC;AACA;AAAA,YACJ;AACA,kBAAM,eAAe,MAAM,cAAc,gBAAgB,MAAM,sBAAsB,MAAM,cAAc;AACzG,kBAAM,0BAA0B,eAAe,MAAM,+BAA+B;AACpF,kBAAM,oBAAoB,wBAAwB,eAAe,IAC3D,OAAO,MAAM,UAAU,MAAM,QAAQ,QAAQ,aAAa;AACxD,oBAAM,uBAAuB,wBAAwB,MAAM,GAAG,qBAAqB;AACnF,oBAAM,iBAAiB,+BAA+B,oBAAoB;AAC1E,kBAAI,mBAAoB,iCAAiC,GAAI;AACzD,sBAAM,IAAI,MAAM,+CAA+C;AAAA,cACnE;AACA,+CAAiC;AACjC,qBAAO,kBAAkB,yBAAyB,YAAY;AAAA,YAClE,CAAC,GAAG,IAAI,EAAE,kBAAkB,SAAS;AACzC,oBAAQ,EAAE,QAAQ,aAAa,cAAc,kBAAkB;AAC/D,kBAAM,SAAS,wBAAwB,kBAAkB,kBAAkB,CAAC,QAAQ,WAAW,UAAU,MAAM,QAAQ,QAAQ,aAAa;AACxI,oBAAM,KAAK;AACX,qBAAO,KAAK,MAAM,sBAAsB;AAAA,gBACpC;AAAA,gBACA,SAAS;AAAA,gBACT;AAAA,gBACA,QAAQ,WAAW,QAAQ,WAAW,SAAS,SAAS,CAAC;AAAA,cAC7D,GAAG,YAAY,CAAC;AAChB,qBAAO,IAAI,QAAQ,CAACD,UAASE,YAAW;AACpC,wCAAwB,EAAE,IAAI;AAAA,kBAC1B,QAAQ,QAAQ;AACZ,4BAAQ,QAAQ;AAAA,sBACZ,KAAK;AAAA,sBACL,KAAK,eAAe;AAChB,8BAAM,EAAE,gBAAgB,IAAI;AAC5B,4BAAI,mBAAmB,MAAM;AACzB,8BAAI;AACA,4DAAgC,eAAe;AAAA,0BACnD,SACO,GAAG;AACN,4BAAAA,QAAO,CAAC;AACR;AAAA,0BACJ;AAAA,wBACJ;AACA;AAAA,sBACJ;AAAA,oBACJ;AACA,oBAAAF,SAAQ,MAAM;AAAA,kBAClB;AAAA,kBACA,QAAAE;AAAA,gBACJ;AAAA,cACJ,CAAC;AAAA,YACL,CAAC,CAAC;AACF,gBAAI;AACA,sBAAQ,MAAM,SAAS,MAAM,CAAC;AAAA,YAClC,SACO,GAAG;AACN,qBAAO,CAAC;AAAA,YACZ,UACA;AACI,4BAAc;AACd,qBAAO,MAAM;AAAA,YACjB;AACA;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,UAAI;AACJ,UAAI;AACJ,YAAM,0BAA0B,MAAM;AAClC,YAAI,eAAe;AACf,wBAAc;AAAA,QAClB;AACA,gBAAQ,EAAE,QAAQ,cAAc,mBAAmB;AACnD,YAAI,wBAAwB,QAAW;AACnC,gCAAsB,KAAK,IAAI;AAAA,QACnC;AACA,iBAAS,IAAI,UAAU,cAAc,CAAC,yBAAyB,CAAC;AAChE,eAAO,iBAAiB,QAAQ,UAAU;AAC1C,eAAO,iBAAiB,SAAS,WAAW;AAC5C,eAAO,iBAAiB,SAAS,WAAW;AAC5C,eAAO,iBAAiB,WAAW,aAAa;AAChD,wBAAgB,MAAM;AAClB,iBAAO,aAAa,kBAAkB;AACtC,iBAAO,oBAAoB,QAAQ,UAAU;AAC7C,iBAAO,oBAAoB,SAAS,WAAW;AAC/C,iBAAO,oBAAoB,SAAS,WAAW;AAC/C,iBAAO,oBAAoB,WAAW,aAAa;AAAA,QACvD;AAAA,MACJ;AACA,8BAAwB;AAAA,IAC5B,CAAC;AAAA,EACL,CAAC;AACL;;;ADtzBA,kBAAiB;AAiBjB,SAAS,OAAO,GAAG,GAAG;AAClB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACX;AAEA,SAASC,WAAU,SAAS,YAAY,GAAG,WAAW;AAClD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAEA,SAAS,eAAe,WAAW;AAC/B,SAAO,OAAO,KAAK,OAAO,aAAa,KAAK,MAAM,GAAG,SAAS,CAAC;AACnE;AACA,SAAS,aAAa,wBAAwB;AAC1C,SAAO,IAAI,WAAW,OACjB,KAAK,sBAAsB,EAC3B,MAAM,EAAE,EACR,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;AACpC;AAEA,SAAS,0BAA0B,aAAa;AAC5C,QAAM,wBAAwB,aAAa,cACrC,YAAY,UAAU,IACtB,YAAY,UAAU;AAAA,IACpB,sBAAsB;AAAA,IACtB,kBAAkB;AAAA,EACtB,CAAC;AACL,QAAM,UAAU,eAAe,qBAAqB;AACpD,SAAO;AACX;AACA,SAAS,8BAA8B,WAAW;AAC9C,QAAM,gBAAgB,UAAU,CAAC;AACjC,QAAM,gBAAgB,gBAAgB,4BAA4B;AAClE,QAAM,UAAU,iBAAiB,0BAA0B,UAAU,MAAM,eAAe,UAAU,MAAM,CAAC;AAC3G,MAAI,YAAY,UAAU;AACtB,WAAO,YAAY,KAAK,SAAS;AAAA,EACrC,OACK;AACD,WAAO,qBAAqB,YAAY,SAAS;AAAA,EACrD;AACJ;AACA,SAASC,UAAS,UAAU,QAAQ;AAChC,SAAOD,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAM,oBAAoB,CAAC,WAAW;AAClC,aAAO,SAAS,iBAAiB,MAAM,CAAC;AAAA,IAC5C;AACA,WAAO,MAAM,SAAW,mBAAmB,MAAM;AAAA,EACrD,CAAC;AACL;AAUA,SAAS,iBAAiB,QAAQ;AAC9B,SAAO,IAAI,MAAM,CAAC,GAAG;AAAA,IACjB,IAAI,QAAQ,GAAG;AACX,UAAI,OAAO,CAAC,KAAK,MAAM;AACnB,gBAAQ,GAAG;AAAA,UACP,KAAK;AACD,mBAAO,CAAC,IAAI,SAAU,IAAI;AACtB,kBAAI,EAAE,gBAAgB,YAAY,eAAe,YAAY,wCAAwC,aAAa,IAAI,IAAI,OAAO,OAAO,IAAI,CAAC,kBAAkB,cAAc,iBAAiB,cAAc,0CAA0C,cAAc,CAAC;AACrQ,qBAAOE,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,sBAAM,WAAW,aAAa,IAAI,yBAAyB;AAC3D,sBAAM,UAAU;AAAA,kBACZ,kBAAkB;AAAA,kBAClB;AAAA,kBACA,gBAAgB;AAAA,kBAChB,aAAa;AAAA,kBACb,8CAA8C;AAAA,gBAClD;AACA,sBAAM,EAAE,YAAY,wBAAwB,IAAI,MAAM,OAAO,wBAAwB,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAI,OAAO,OAAO,OAAO,EAAE,KAAK,aAAW,WAAW,IAAI,IAC3L,EAAE,QAAiB,IACnB,IAAK,GAAG,EAAE,SAAS,CAAC,CAAC;AAC3B,sBAAM,aAAa,wBAAwB,IAAI,YAAY,EAAE,IAAI,YAAAC,QAAK,MAAM;AAC5E,uBAAO;AAAA,cACX,CAAC;AAAA,YACL;AACA;AAAA,UACJ,KAAK;AACD,mBAAO,CAAC,IAAI,SAAU,IAAI;AACtB,kBAAI,EAAE,SAAS,IAAI,IAAI,OAAO,OAAO,IAAI,CAAC,UAAU,CAAC;AACrD,qBAAOD,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,sBAAM,wBAAwB,SAAS,IAAI,cAAc;AACzD,sBAAM,EAAE,iBAAiB,4BAA4B,IAAI,MAAM,OAAO,aAAa,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,UAAU,sBAAsB,CAAC,CAAC;AAC9J,sBAAM,iBAAiB,4BAA4B,IAAI,YAAY;AACnE,uBAAO;AAAA,cACX,CAAC;AAAA,YACL;AACA;AAAA,UACJ,KAAK;AACD,mBAAO,CAAC,IAAI,SAAU,IAAI;AACtB,kBAAI,EAAE,aAAa,IAAI,IAAI,OAAO,OAAO,IAAI,CAAC,cAAc,CAAC;AAC7D,qBAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,sBAAM,WAAW,aAAa,IAAI,yBAAyB;AAC3D,sBAAM,EAAE,iBAAiB,kCAAkC,IAAI,MAAM,OAAO,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,SAAS,CAAC,CAAC;AACjJ,sBAAM,uBAAuB,kCAAkC,IAAI,YAAY;AAC/E,sBAAM,qBAAqB,qBAAqB,IAAI,6BAA6B;AACjF,uBAAO;AAAA,cACX,CAAC;AAAA,YACL;AACA;AAAA,UACJ,SAAS;AACL,mBAAO,CAAC,IAAI,OAAO,CAAC;AACpB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,OAAO,CAAC;AAAA,IACnB;AAAA,IACA,iBAAiB;AACb,aAAO;AAAA,IACX;AAAA,IACA,iBAAiB;AACb,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;;;ADlJA,IAAAE,eAAmB;AAiBnB,SAASC,WAAU,SAAS,YAAY,GAAG,WAAW;AAClD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAEA,SAAS,yBAAyB,UAAU,OAAO,MAAM,GAAG;AACxD,MAAI,SAAS,OAAO,CAAC,EAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,0EAA0E;AACjL,SAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,KAAK,QAAQ,IAAI,IAAI,EAAE,QAAQ,MAAM,IAAI,QAAQ;AAChG;AAEA,SAAS,yBAAyB,UAAU,OAAO,OAAO,MAAM,GAAG;AAC/D,MAAI,SAAS,IAAK,OAAM,IAAI,UAAU,gCAAgC;AACtE,MAAI,SAAS,OAAO,CAAC,EAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,yEAAyE;AAChL,SAAQ,SAAS,MAAM,EAAE,KAAK,UAAU,KAAK,IAAI,IAAI,EAAE,QAAQ,QAAQ,MAAM,IAAI,UAAU,KAAK,GAAI;AACxG;AAEA,IAAI;AAAJ,IAA8B;AAA9B,IAAmD;AAAnD,IAAkF;AAAlF,IAAoH;AAApH,IAA+I;AAA/I,IAAoL;AAApL,IAAyN;AACzN,IAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAelB,IAAM,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyDd,IAAM,QAAQ;AAAA;AAAA;AAAA;AAAA;AAKd,IAAM,gBAAN,MAAoB;AAAA,EAChB,cAAc;AACV,6BAAyB,IAAI,IAAI;AACjC,wBAAoB,IAAI,MAAM,IAAI;AAClC,kCAA8B,IAAI,MAAM,CAAC,CAAC;AAC1C,qCAAiC,IAAI,MAAM,KAAK;AAChD,SAAK,MAAM;AACX,SAAK,OAAO,MAAM;AACd,cAAQ,MAAM,YAAY;AAC1B,+BAAyB,MAAM,0BAA0B,KAAK,mCAAmC,EAAE,KAAK,IAAI;AAC5G,UAAI,yBAAyB,MAAM,qBAAqB,GAAG,GAAG;AAC1D,iCAAyB,MAAM,qBAAqB,GAAG,EAAE,MAAM,UAAU;AAAA,MAC7E;AAAA,IACJ;AACA,SAAK,QAAQ,CAAC,QAAQ,WAAc;AAChC,UAAI;AACJ,cAAQ,MAAM,aAAa;AAC3B,+BAAyB,MAAM,0BAA0B,KAAK,mCAAmC,EAAE,KAAK,IAAI;AAC5G,UAAI,yBAAyB,MAAM,qBAAqB,GAAG,GAAG;AAC1D,iCAAyB,MAAM,qBAAqB,GAAG,EAAE,MAAM,UAAU;AAAA,MAC7E;AACA,OAAC,KAAK,yBAAyB,MAAM,+BAA+B,GAAG,EAAE,OAAO,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC,aAAa,SAAS,KAAK,CAAC;AAAA,IACpK;AACA,iCAA6B,IAAI,MAAM,CAAC,UAAU;AAC9C,UAAI,MAAM,QAAQ;AACd,aAAK,MAAM,KAAK;AAAA,IACxB,CAAC;AAED,SAAK,OAAO,KAAK,KAAK,KAAK,IAAI;AAC/B,6BAAyB,MAAM,qBAAqB,SAAS,eAAe,wCAAwC,GAAG,GAAG;AAAA,EAC9H;AAAA,EACA,OAAO;AACH,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,cAAQ,IAAI,iBAAiB;AAC7B,+BAAyB,MAAM,0BAA0B,KAAK,yBAAyB,EAAE,KAAK,IAAI;AAAA,IACtG,CAAC;AAAA,EACL;AAAA,EACA,iBAAiB,OAAO,UAAU;AAC9B,QAAI;AACJ,MAAE,KAAK,yBAAyB,MAAM,+BAA+B,GAAG,EAAE,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ,OAAO,yBAAyB,MAAM,+BAA+B,GAAG,EAAE,KAAK,IAAI,CAAC,QAAQ;AACnO,WAAO,MAAM,KAAK,oBAAoB,OAAO,QAAQ;AAAA,EACzD;AAAA,EACA,oBAAoB,OAAO,UAAU;AACjC,QAAI;AACJ,6BAAyB,MAAM,+BAA+B,GAAG,EAAE,KAAK,KAAK,KAAK,yBAAyB,MAAM,+BAA+B,GAAG,EAAE,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,CAAC,qBAAqB,aAAa,gBAAgB;AAAA,EACnQ;AACJ;AACA,sBAAsB,oBAAI,QAAQ,GAAG,gCAAgC,oBAAI,QAAQ,GAAG,mCAAmC,oBAAI,QAAQ,GAAG,+BAA+B,oBAAI,QAAQ,GAAG,2BAA2B,oBAAI,QAAQ,GAAG,4BAA4B,SAASC,6BAA4B;AAE3R,MAAI,SAAS,eAAe,wCAAwC,GAAG;AACnE,QAAI,CAAC,yBAAyB,MAAM,qBAAqB,GAAG;AACxD,+BAAyB,MAAM,qBAAqB,SAAS,eAAe,wCAAwC,GAAG,GAAG;AAC9H;AAAA,EACJ;AAEA,2BAAyB,MAAM,qBAAqB,SAAS,cAAc,KAAK,GAAG,GAAG;AACtF,2BAAyB,MAAM,qBAAqB,GAAG,EAAE,KAAK;AAC9D,2BAAyB,MAAM,qBAAqB,GAAG,EAAE,YAAY;AACrE,2BAAyB,MAAM,qBAAqB,GAAG,EAAE,MAAM,UAAU;AAEzE,QAAM,UAAU,yBAAyB,MAAM,qBAAqB,GAAG,EAAE,cAAc,+CAA+C;AACtI,MAAI;AACA,YAAQ,YAAY,KAAK;AAE7B,QAAM,SAAS,SAAS,cAAc,OAAO;AAC7C,SAAO,KAAK;AACZ,SAAO,cAAc,QAAQ,KAAK;AAElC,QAAM,OAAO,SAAS,cAAc,KAAK;AACzC,OAAK,YAAY;AACjB,OAAK,MAAM,KAAK,aAAa,EAAE,MAAM,SAAS,CAAC;AAC/C,OAAK,IAAI,YAAY,MAAM;AAC3B,OAAK,IAAI,YAAY,yBAAyB,MAAM,qBAAqB,GAAG,CAAC;AAE7E,WAAS,KAAK,YAAY,IAAI;AAClC,GAAG,sCAAsC,SAASC,uCAAsC;AACpF,MAAI,CAAC,yBAAyB,MAAM,qBAAqB,GAAG,KAAK,yBAAyB,MAAM,kCAAkC,GAAG;AACjI;AACJ,QAAM,UAAU,CAAC,GAAG,yBAAyB,MAAM,qBAAqB,GAAG,EAAE,iBAAiB,oBAAoB,CAAC;AACnH,UAAQ,QAAQ,YAAU,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,iBAAiB,SAAS,KAAK,KAAK,CAAC;AACtH,SAAO,iBAAiB,QAAQ,KAAK,KAAK;AAC1C,WAAS,iBAAiB,WAAW,yBAAyB,MAAM,8BAA8B,GAAG,CAAC;AACtG,2BAAyB,MAAM,kCAAkC,MAAM,GAAG;AAC9E,GAAG,sCAAsC,SAASC,uCAAsC;AACpF,MAAI,CAAC,yBAAyB,MAAM,kCAAkC,GAAG;AACrE;AACJ,SAAO,oBAAoB,QAAQ,KAAK,KAAK;AAC7C,WAAS,oBAAoB,WAAW,yBAAyB,MAAM,8BAA8B,GAAG,CAAC;AACzG,MAAI,CAAC,yBAAyB,MAAM,qBAAqB,GAAG;AACxD;AACJ,QAAM,UAAU,CAAC,GAAG,yBAAyB,MAAM,qBAAqB,GAAG,EAAE,iBAAiB,oBAAoB,CAAC;AACnH,UAAQ,QAAQ,YAAU,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,oBAAoB,SAAS,KAAK,KAAK,CAAC;AACzH,2BAAyB,MAAM,kCAAkC,OAAO,GAAG;AAC/E;AAkRA,IAAM,OAAO;AAEb,SAASC,wBAAuB,aAAa;AACzC,SAAO,aAAa;AACxB;AAEA,SAASC,gBAAe,WAAW;AAC/B,SAAO,OAAO,KAAK,OAAO,aAAa,KAAK,MAAM,GAAG,SAAS,CAAC;AACnE;AACA,SAASC,cAAa,wBAAwB;AAC1C,SAAO,IAAI,WAAW,OACjB,KAAK,sBAAsB,EAC3B,MAAM,EAAE,EACR,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;AACpC;AAEA,IAAI;AAAJ,IAAqD;AAArD,IAAsG;AAAtG,IAAqJ;AAArJ,IAAiM;AAAjM,IAA4O;AAA5O,IAAwR;AAAxR,IAA2U;AAA3U,IAAgY;AAAhY,IAA0b;AAA1b,IAA4e;AAA5e,IAAwiB;AAAxiB,IAAslB;AAAtlB,IAA2oB;AAA3oB,IAAmsB;AAAnsB,IAA2vB;AAA3vB,IAAqyB;AAAryB,IAAi1B;AAAj1B,IAA43B;AAA53B,IAA26B;AAA36B,IAAu+B;AAAv+B,IAAwiC;AAAxiC,IAA8mC;AAA9mC,IAA4qC;AAA5qC,IAA8tC;AAA9tC,IAA8wC;AAA9wC,IAAw0C;AAAx0C,IAAg5C;AAAh5C,IAA+8C;AAA/8C,IAAohD;AAAphD,IAAklD;AAAllD,IAAyoD;AAAzoD,IAA4rD;AAA5rD,IAA0uD;AAA1uD,IAA+xD;AAA/xD,IAAi1D;AAAj1D,IAAm4D;AAAn4D,IAAm7D;AAAn7D,IAAg+D;AAAh+D,IAA4gE;AAA5gE,IAAyjE;AAAzjE,IAA6mE;AAA7mE,IAAmqE;AAAnqE,IAA8tE;AAA9tE,IAAixE;AAAjxE,IAA80E;AAA90E,IAA63E;AAA73E,IAAm7E;AAAn7E,IAA4+E;AAA5+E,IAAqiF;AAAriF,IAA2lF;AAA3lF,IAA2oF;AAA3oF,IAAsrF;AAAtrF,IAAmuF;AAAnuF,IAA+wF;AAA/wF,IAA+zF;AAA/zF,IAA43F;AAA53F,IAA87F;AAA97F,IAAqgG;AAArgG,IAAokG;AAApkG,IAAunG;AAAvnG,IAAwqG;AAAxqG,IAAmuG;AAAnuG,IAA4yG;AAA5yG,IAA42G;AAA52G,IAAk7G;AAAl7G,IAAi/G;AAAj/G,IAAyiH;AAAziH,IAA6lH;AAA7lH,IAA4oH;AAC5oH,IAAM,sCAAsC;AAC5C,IAAMC,6BAA4B;AAClC,IAAM,mBAAmB,CAAC,8BAA8B,uBAAuB,mBAAmB,YAAY;AAC9G,IAAM,uCAAN,MAA2C;AAAA,EACvC,YAAY,QAAQ;AAChB,oDAAgD,IAAI,IAAI;AACxD,oDAAgD,IAAI,MAAM,CAAC,CAAC;AAC5D,kDAA8C,IAAI,MAAM,OAAO;AAC/D,+CAA2C,IAAI,MAAM,mCAAmC;AACxF,8CAA0C,IAAI,MAAM,kCAAkC;AACtF,+CAA2C,IAAI,MAAM,IAAI;AACzD,sDAAkD,IAAI,MAAM,MAAM;AAClE,wDAAoD,IAAI,MAAM,MAAM;AACpE,6DAAyD,IAAI,MAAM,MAAM;AACzE,qDAAiD,IAAI,MAAM,KAAK;AAMhE,+DAA2D,IAAI,MAAM,CAAC;AACtE,iDAA6C,IAAI,MAAM,CAAC,CAAC;AACzD,wDAAoD,IAAI,MAAM,MAAM;AACpE,2DAAuD,IAAI,MAAM,MAAM;AACvE,2DAAuD,IAAI,MAAM,MAAM;AACvE,6CAAyC,IAAI,MAAM,CAAC,OAAO,aAAa;AACpE,UAAI;AACJ,QAAE,KAAK,yBAAyB,MAAM,iDAAiD,GAAG,EAAE,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ,OAAO,yBAAyB,MAAM,iDAAiD,GAAG,EAAE,KAAK,IAAI,CAAC,QAAQ;AACvQ,aAAO,MAAM,yBAAyB,MAAM,iDAAiD,KAAK,yCAAyC,EAAE,KAAK,MAAM,OAAO,QAAQ;AAAA,IAC3K,CAAC;AACD,kDAA8C,IAAI,MAAM,CAAC,EAAE,OAAO,IAAI,CAAC,MAAMC,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACtH,UAAI,yBAAyB,MAAM,kDAAkD,GAAG,KAAK,KAAK,WAAW;AACzG,eAAO,EAAE,UAAU,KAAK,SAAS;AAAA,MACrC;AACA,+BAAyB,MAAM,kDAAkD,MAAM,GAAG;AAC1F,UAAI;AACA,YAAI,QAAQ;AACR,gBAAM,sBAAsB,MAAM,yBAAyB,MAAM,0DAA0D,GAAG,EAAE,IAAI;AACpI,cAAI,qBAAqB;AACrB,kBAAM,yBAAyB,MAAM,iEAAiE,GAAG,EAAE,KAAK,MAAM,mBAAmB;AAAA,UAC7I,OACK;AACD,mBAAO,EAAE,UAAU,KAAK,SAAS;AAAA,UACrC;AAAA,QACJ,OACK;AACD,gBAAM,yBAAyB,MAAM,4DAA4D,GAAG,EAAE,KAAK,IAAI;AAAA,QACnH;AAAA,MACJ,SACO,GAAG;AACN,cAAM,IAAI,MAAO,aAAa,SAAS,EAAE,WAAY,eAAe;AAAA,MACxE,UACA;AACI,iCAAyB,MAAM,kDAAkD,OAAO,GAAG;AAAA,MAC/F;AACA,aAAO,EAAE,UAAU,KAAK,SAAS;AAAA,IACrC,CAAC,CAAC;AACF,+DAA2D,IAAI,MAAM,CAAC,kBAAkBA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACjI,UAAI;AACA,cAAM,4BAA4B,MAAM,yBAAyB,MAAM,0DAA0D,GAAG,EAAE,IAAI;AAC1I,YAAI,2BAA2B;AAE3B,mCAAyB,MAAM,iEAAiE,GAAG,EAAE,KAAK,MAAM,yBAAyB;AACzI,iBAAO;AAAA,QACX;AACA,cAAM,gBAAgB,MAAM,yBAAyB,MAAM,qDAAqD,GAAG,EAAE,OAAO,yBAAyB,MAAM,8CAA8C,GAAG,CAAC;AAC7M,eAAO,MAAM,yBAAyB,MAAM,gDAAgD,GAAG,EAAE,KAAK,MAAM,CAAC,WAAWA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACjK,gBAAM,CAAC,cAAc,sBAAsB,IAAI,MAAM,QAAQ,IAAI;AAAA,YAC7D,OAAO,gBAAgB;AAAA,YACvB,OAAO,UAAU;AAAA,cACb,OAAO;AAAA,cACP,UAAU,yBAAyB,MAAM,mDAAmD,GAAG;AAAA,cAC/F,iBAAiB;AAAA,YACrB,CAAC;AAAA,UACL,CAAC;AACD,gBAAM,WAAW,yBAAyB,MAAM,wEAAwE,GAAG,EAAE,KAAK,MAAM,uBAAuB,QAAQ;AACvK,gBAAM,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,sBAAsB,GAAG,EAAE,UAAU,OAAO,cAAc,CAAC;AAEjH,kBAAQ,IAAI;AAAA,YACR,yBAAyB,MAAM,sEAAsE,GAAG,EAAE,KAAK,MAAM,YAAY;AAAA,YACjI,yBAAyB,MAAM,0DAA0D,GAAG,EAAE,IAAI,aAAa;AAAA,YAC/G,yBAAyB,MAAM,iEAAiE,GAAG,EAAE,KAAK,MAAM,aAAa;AAAA,UACjI,CAAC;AACD,iBAAO;AAAA,QACX,CAAC,CAAC;AAAA,MACN,SACO,GAAG;AACN,cAAM,IAAI,MAAO,aAAa,SAAS,EAAE,WAAY,eAAe;AAAA,MACxE;AAAA,IACJ,CAAC,CAAC;AACF,oEAAgE,IAAI,MAAM,CAAC,kBAAkBA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACtI,UAAI;AACJ,YAAM;AAAA;AAAA,QAEN,yBAAyB,MAAM,qDAAqD,GAAG,KAAK;AAAA,UAEtF,KAAK,yBAAyB,MAAM,qDAAqD,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,YAAY,cAAc,SAAS;AAAA,QAErL,yBAAyB,MAAM,qDAAqD,GAAG,EAAE,SAAS,KAAK,CAAC,SAAS,OAAO,QAAQ,YAAY,cAAc,SAAS,EAAE,EAAE,OAAO;AAAA;AAClL,+BAAyB,MAAM,qDAAqD,eAAe,GAAG;AACtG,UAAI,qBAAqB;AACrB,iCAAyB,MAAM,iDAAiD,KAAK,0CAA0C,EAAE,KAAK,MAAM,UAAU,EAAE,UAAU,KAAK,SAAS,CAAC;AAAA,MACrL;AAAA,IACJ,CAAC,CAAC;AACF,yEAAqE,IAAI,MAAM,CAAC,iBAAiBA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAE1I,YAAM,0BAA0B,aAAa,SAAS,SAAS,yBAAyB;AACxF,YAAM,iCAAiC,aAAa;AACpD,YAAM,wBAAwB,gCAAgC,KAAK,aAAa,kCAC5E,yBAAyB,KAAK,aAAa;AAC/C,+BAAyB,MAAM,wDAAwD,OAAO,OAAO,OAAO,OAAO,CAAC,IAAK,kCAAmC,CAAC,kCAAkC,CAAC,4BAA6B;AAAA,QACzN,CAAC,4BAA4B,GAAG;AAAA,UAC5B,SAAS;AAAA,UACT,8BAA8B,CAAC,UAAU,CAAC;AAAA,UAC1C,wBAAwB,yBAAyB,MAAM,8DAA8D,GAAG;AAAA,QAC5H;AAAA,MACJ,CAAE,GAAI,2BAA2B;AAAA,QAC7B,CAAC,qBAAqB,GAAG;AAAA,UACrB,SAAS;AAAA,UACT,8BAA8B,CAAC,UAAU,CAAC;AAAA,UAC1C,iBAAiB,yBAAyB,MAAM,uDAAuD,GAAG;AAAA,QAC9G;AAAA,MACJ,CAAE,GAAG,GAAG;AACR,UAAI,uBAAuB;AACvB,iCAAyB,MAAM,iDAAiD,KAAK,0CAA0C,EAAE,KAAK,MAAM,UAAU,EAAE,UAAU,KAAK,SAAS,CAAC;AAAA,MACrL;AAAA,IACJ,CAAC,CAAC;AACF,iEAA6D,IAAI,MAAM,CAAC,QAAQ,WAAW,UAAUA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAC9I,UAAI;AACA,cAAM,yBAAyB,MAAM,OAAO,UAAU;AAAA,UAClD,YAAY;AAAA,UACZ,UAAU,yBAAyB,MAAM,mDAAmD,GAAG;AAAA,UAC/F;AAAA,QACJ,CAAC;AACD,cAAM,WAAW,yBAAyB,MAAM,wEAAwE,GAAG,EAAE,KAAK,MAAM,uBAAuB,QAAQ;AACvK,cAAM,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,sBAAsB,GAAG,EAAE,UAAoB,MAAa,CAAC;AAEnH,gBAAQ,IAAI;AAAA,UACR,yBAAyB,MAAM,0DAA0D,GAAG,EAAE,IAAI,aAAa;AAAA,UAC/G,yBAAyB,MAAM,iEAAiE,GAAG,EAAE,KAAK,MAAM,aAAa;AAAA,QACjI,CAAC;AAAA,MACL,SACO,GAAG;AACN,iCAAyB,MAAM,kDAAkD,GAAG,EAAE,KAAK,IAAI;AAC/F,cAAM,IAAI,MAAO,aAAa,SAAS,EAAE,WAAY,eAAe;AAAA,MACxE;AAAA,IACJ,CAAC,CAAC;AACF,qDAAiD,IAAI,MAAM,MAAMA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAC1G,UAAI;AACJ,+BAAyB,MAAM,0DAA0D,GAAG,EAAE,MAAM;AACpG,+BAAyB,MAAM,kDAAkD,OAAO,GAAG;AAC3F,+BAAyB,MAAM,6DAA6D,KAAK,yBAAyB,MAAM,4DAA4D,GAAG,GAAG,MAAM,KAAK,GAAG;AAChN,+BAAyB,MAAM,qDAAqD,QAAW,GAAG;AAClG,+BAAyB,MAAM,iDAAiD,KAAK,0CAA0C,EAAE,KAAK,MAAM,UAAU,EAAE,UAAU,KAAK,SAAS,CAAC;AAAA,IACrL,CAAC,CAAC;AACF,mDAA+C,IAAI,MAAM,CAAC,aAAaA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChH,UAAI;AACJ,YAAM,iBAAiB,KAAK,yBAAyB,MAAM,qDAAqD,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC9J,YAAMC,UAAS,gBAAgB,EAAE,SAAS,cAAc,IAAI;AAC5D,YAAM,8BAA8B,yBAAyB,MAAM,4DAA4D,GAAG;AAClI,UAAI;AACA,eAAO,MAAMC,UAAS,UAAUD,OAAM;AAAA,MAC1C,SACO,GAAG;AACN,YAAI,yBAAyB,MAAM,4DAA4D,GAAG,MAAM,6BAA6B;AACjI,gBAAM,IAAI,QAAQ,MAAM;AAAA,UAAE,CAAC;AAAA,QAC/B;AACA,YAAI,aAAa,SACb,EAAE,SAAS,oCACX,EAAE,SAAS,0BAA0B;AACrC,gBAAM,yBAAyB,MAAM,wDAAwD,GAAG,EAAE,KAAK,MAAM,IAAI;AAAA,QACrH;AACA,cAAM;AAAA,MACV;AAAA,IACJ,CAAC,CAAC;AACF,6DAAyD,IAAI,MAAM,MAAM;AACrE,UAAI,CAAC,yBAAyB,MAAM,qDAAqD,GAAG;AACxF,cAAM,IAAI,MAAM,sBAAsB;AAC1C,aAAO,EAAE,WAAW,yBAAyB,MAAM,qDAAqD,GAAG,EAAE,YAAY,OAAO,yBAAyB,MAAM,qDAAqD,GAAG,EAAE,MAAM;AAAA,IACnO,CAAC;AACD,2EAAuE,IAAI,MAAM,CAAC,aAAa;AAC3F,aAAO,SAAS,IAAI,CAAC,YAAY;AAC7B,YAAI,IAAI;AACR,cAAM,YAAYH,cAAa,QAAQ,OAAO;AAC9C,eAAO;AAAA,UACH,SAAS,aAAAK,QAAO,OAAO,SAAS;AAAA,UAChC;AAAA,UACA,OAAO,QAAQ;AAAA,UACf,MAAM,QAAQ;AAAA,UACd,SAAS,KAAK,QAAQ,YAAY,QAAQ,OAAO,SAAS,KAAK,yBAAyB,MAAM,8CAA8C,GAAG;AAAA;AAAA,UAE/I,WAAW,KAAK,QAAQ,cAAc,QAAQ,OAAO,SAAS,KAAK;AAAA,QACvE;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,kEAA8D,IAAI,MAAM,CAAC,iBAAiBH,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACnI,YAAM,EAAE,WAAW,MAAM,IAAI,yBAAyB,MAAM,0DAA0D,GAAG,EAAE,KAAK,IAAI;AACpI,UAAI;AACA,eAAO,MAAM,yBAAyB,MAAM,gDAAgD,GAAG,EAAE,KAAK,MAAM,CAAC,WAAWA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACjK,gBAAM,yBAAyB,MAAM,8DAA8D,GAAG,EAAE,KAAK,MAAM,QAAQ,WAAW,KAAK;AAC3I,gBAAM,qBAAqB,MAAM,OAAO,iBAAiB;AAAA,YACrD;AAAA,UACJ,CAAC;AACD,iBAAO;AAAA,QACX,CAAC,CAAC;AAAA,MACN,SACO,GAAG;AACN,cAAM,IAAI,MAAO,aAAa,SAAS,EAAE,WAAY,eAAe;AAAA,MACxE;AAAA,IACJ,CAAC,CAAC;AACF,wEAAoE,IAAI,MAAM,CAAC,aAAa,YAAYA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACjJ,YAAM,EAAE,WAAW,MAAM,IAAI,yBAAyB,MAAM,0DAA0D,GAAG,EAAE,KAAK,IAAI;AACpI,UAAI;AACA,eAAO,MAAM,yBAAyB,MAAM,gDAAgD,GAAG,EAAE,KAAK,MAAM,CAAC,WAAWA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACjK,gBAAM,CAAC,cAAc,EAAE,IAAI,MAAM,QAAQ,IAAI;AAAA,YACzC,OAAO,gBAAgB;AAAA,YACvB,yBAAyB,MAAM,8DAA8D,GAAG,EAAE,KAAK,MAAM,QAAQ,WAAW,KAAK;AAAA,UACzI,CAAC;AACD,cAAI,aAAa,qCAAqC;AAClD,kBAAM,aAAa,MAAM,OAAO,wBAAwB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;AAClI,mBAAO,WAAW,CAAC;AAAA,UACvB,OACK;AACD,kBAAM,IAAI,MAAM,0DAA0D;AAAA,UAC9E;AAAA,QACJ,CAAC,CAAC;AAAA,MACN,SACO,GAAG;AACN,cAAM,IAAI,MAAO,aAAa,SAAS,EAAE,WAAY,eAAe;AAAA,MACxE;AAAA,IACJ,CAAC,CAAC;AACF,iEAA6D,IAAI,MAAM,IAAI,WAAWA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAC/H,YAAM,UAAU,CAAC;AACjB,iBAAW,SAAS,QAAQ;AACxB,cAAM,cAAc,qBAAqB,YAAY,MAAM,WAAW;AACtE,cAAM,YAAa,MAAM,yBAAyB,MAAM,qEAAqE,GAAG,EAAE,KAAK,MAAM,aAAa,MAAM,OAAO;AACvK,gBAAQ,KAAK,EAAE,WAAW,aAAAG,QAAO,OAAO,SAAS,EAAE,CAAC;AAAA,MACxD;AACA,aAAO;AAAA,IACX,CAAC,CAAC;AACF,0DAAsD,IAAI,MAAM,IAAI,WAAWH,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACxH,YAAM,eAAe,OAAO,IAAI,CAAC,EAAE,YAAY,MAAM,qBAAqB,YAAY,WAAW,CAAC;AAClG,YAAM,qBAAqB,MAAM,yBAAyB,MAAM,+DAA+D,GAAG,EAAE,KAAK,MAAM,YAAY;AAC3J,aAAO,mBAAmB,IAAI,CAAC,sBAAsB;AACjD,cAAM,wBAAwBJ,wBAAuB,iBAAiB,IAChE,kBAAkB,UAAU,IAC5B,IAAI,WAAW,kBAAkB,UAAU;AAAA,UACzC,sBAAsB;AAAA,UACtB,kBAAkB;AAAA,QACtB,CAAC,CAAC;AACN,eAAO,EAAE,mBAAmB,sBAAsB;AAAA,MACtD,CAAC;AAAA,IACL,CAAC,CAAC;AACF,sDAAkD,IAAI,MAAM,IAAI,WAAWI,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACpH,YAAM,EAAE,WAAW,MAAM,IAAI,yBAAyB,MAAM,0DAA0D,GAAG,EAAE,KAAK,IAAI;AACpI,YAAM,YAAY,OAAO,IAAI,CAAC,EAAE,QAAQ,MAAMH,gBAAe,QAAQ,SAAS,CAAC;AAC/E,YAAM,WAAW,OAAO,IAAI,CAAC,EAAE,QAAQ,MAAM,OAAO;AACpD,UAAI;AACA,eAAO,MAAM,yBAAyB,MAAM,gDAAgD,GAAG,EAAE,KAAK,MAAM,CAAC,WAAWG,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACjK,gBAAM,yBAAyB,MAAM,8DAA8D,GAAG,EAAE,KAAK,MAAM,QAAQ,WAAW,KAAK;AAC3I,gBAAM,iBAAiB,MAAM,OAAO,aAAa;AAAA,YAC7C;AAAA,YACA,UAAU;AAAA,UACd,CAAC;AACD,iBAAO,eAAe,IAAI,CAAC,kBAAkB;AACzC,mBAAO,EAAE,eAA8B,WAAW,cAAc,MAAM,CAACD,0BAAyB,EAAE;AAAA,UACtG,CAAC;AAAA,QACL,CAAC,CAAC;AAAA,MACN,SACO,GAAG;AACN,cAAM,IAAI,MAAO,aAAa,SAAS,EAAE,WAAY,eAAe;AAAA,MACxE;AAAA,IACJ,CAAC,CAAC;AACF,iDAA6C,IAAI,MAAM,IAAI,WAAWC,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAC/G,YAAM,UAAU,CAAC;AACjB,UAAI,OAAO,SAAS,GAAG;AACnB,mBAAW,SAAS,QAAQ;AACxB,kBAAQ,KAAK,MAAM,yBAAyB,MAAM,qDAAqD,GAAG,EAAE,KAAK,MAAM,KAAK,CAAC;AAAA,QACjI;AAAA,MACJ,OACK;AACD,eAAO,CAAC,MAAM,yBAAyB,MAAM,qDAAqD,GAAG,EAAE,KAAK,MAAM,OAAO,CAAC,CAAC,CAAC;AAAA,MAChI;AACA,aAAO;AAAA,IACX,CAAC,CAAC;AACF,wDAAoD,IAAI,MAAM,CAAC,UAAUA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClH,UAAI,IAAI;AACR,+BAAyB,MAAM,kDAAkD,MAAM,GAAG;AAC1F,UAAI;AACA,cAAM,sBAAsB,MAAM,yBAAyB,MAAM,4DAA4D,GAAG,EAAE,KAAK,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,SAAS,KAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO,SAAS,KAAK,CAAC,CAAC;AACzT,YAAI,CAAC,oBAAoB,gBAAgB;AACrC,gBAAM,IAAI,MAAM,sDAAsD;AAAA,QAC1E;AACA,cAAM,kBAAkB,oBAAoB,eAAe;AAC3D,cAAM,kBAAkB,OAAO,OAAO,OAAO,OAAO,CAAC,IAAI,KAAK,oBAAoB,SAAS,KAAK,SAAO,IAAI,WAAW,eAAe,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,UACrK,SAAS;AAAA,QACb,CAAC,GAAG,EAAE,WAAWF,cAAa,eAAe,EAAE,CAAC;AAChD,eAAO;AAAA,UACH,SAAS;AAAA,UACT,eAAeA,cAAa,oBAAoB,eAAe,cAAc;AAAA,UAC7E,WAAWA,cAAa,oBAAoB,eAAe,SAAS;AAAA,QACxE;AAAA,MACJ,SACO,GAAG;AACN,cAAM,IAAI,MAAO,aAAa,SAAS,EAAE,WAAY,eAAe;AAAA,MACxE,UACA;AACI,iCAAyB,MAAM,kDAAkD,OAAO,GAAG;AAAA,MAC/F;AAAA,IACJ,CAAC,CAAC;AACF,6BAAyB,MAAM,0DAA0D,OAAO,oBAAoB,GAAG;AACvH,6BAAyB,MAAM,mDAAmD,OAAO,aAAa,GAAG;AACzG,6BAAyB,MAAM,8CAA8C,OAAO,QAAQ,GAAG;AAC/F,6BAAyB,MAAM,qDAAqD,OAAO,eAAe,GAAG;AAC7G,6BAAyB,MAAM,wDAAwD,OAAO,kBAAkB,GAAG;AACnH,6BAAyB,MAAM,wDAAwD;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnF,CAAC,4BAA4B,GAAG;AAAA,QAC5B,SAAS;AAAA,QACT,8BAA8B,CAAC,UAAU,CAAC;AAAA,QAC1C,wBAAwB,yBAAyB,MAAM,8DAA8D,GAAG;AAAA,MAC5H;AAAA,IACJ,GAAG,GAAG;AAAA,EACV;AAAA,EACA,IAAI,UAAU;AACV,WAAO,yBAAyB,MAAM,+CAA+C,GAAG;AAAA,EAC5F;AAAA,EACA,IAAI,OAAO;AACP,WAAO,yBAAyB,MAAM,4CAA4C,GAAG;AAAA,EACzF;AAAA,EACA,IAAI,MAAM;AACN,WAAO,yBAAyB,MAAM,2CAA2C,GAAG;AAAA,EACxF;AAAA,EACA,IAAI,OAAO;AACP,WAAO,yBAAyB,MAAM,4CAA4C,GAAG;AAAA,EACzF;AAAA,EACA,IAAI,SAAS;AACT,WAAO,yBAAyB,MAAM,8CAA8C,GAAG;AAAA,EAC3F;AAAA,EACA,IAAI,WAAW;AACX,WAAO,OAAO,OAAO,EAAE,CAAC,eAAe,GAAG;AAAA,MAClC,SAAS;AAAA,MACT,SAAS,yBAAyB,MAAM,+CAA+C,GAAG;AAAA,IAC9F,GAAG,CAAC,kBAAkB,GAAG;AAAA,MACrB,SAAS;AAAA,MACT,YAAY,yBAAyB,MAAM,kDAAkD,GAAG;AAAA,IACpG,GAAG,CAAC,cAAc,GAAG;AAAA,MACjB,SAAS;AAAA,MACT,IAAI,yBAAyB,MAAM,0CAA0C,GAAG;AAAA,IACpF,GAAG,CAAC,iBAAiB,GAAG;AAAA,MACpB,SAAS;AAAA,MACT,aAAa,yBAAyB,MAAM,mDAAmD,GAAG;AAAA,IACtG,GAAG,CAAC,YAAY,GAAG;AAAA,MACf,SAAS;AAAA,MACT,QAAQ,yBAAyB,MAAM,8CAA8C,GAAG;AAAA,IAC5F,EAAE,GAAG,yBAAyB,MAAM,wDAAwD,GAAG,CAAC;AAAA,EACxG;AAAA,EACA,IAAI,WAAW;AACX,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,yBAAyB,MAAM,qDAAqD,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,EACrM;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,CAAC,CAAC,yBAAyB,MAAM,qDAAqD,GAAG;AAAA,EACpG;AAAA,EACA,IAAI,eAAe;AACf,WAAO,CAAC,CAAC,yBAAyB,MAAM,qDAAqD,GAAG;AAAA,EACpG;AAAA,EACA,IAAI,uBAAuB;AACvB,WAAO,yBAAyB,MAAM,qDAAqD,GAAG;AAAA,EAClG;AAAA,EACA,IAAI,4BAA4B;AAC5B,WAAO,yBAAyB,MAAM,0DAA0D,GAAG,EAAE,IAAI;AAAA,EAC7G;AACJ;AACA,kDAAkD,oBAAI,QAAQ,GAAG,gDAAgD,oBAAI,QAAQ,GAAG,6CAA6C,oBAAI,QAAQ,GAAG,4CAA4C,oBAAI,QAAQ,GAAG,6CAA6C,oBAAI,QAAQ,GAAG,oDAAoD,oBAAI,QAAQ,GAAG,sDAAsD,oBAAI,QAAQ,GAAG,2DAA2D,oBAAI,QAAQ,GAAG,mDAAmD,oBAAI,QAAQ,GAAG,6DAA6D,oBAAI,QAAQ,GAAG,+CAA+C,oBAAI,QAAQ,GAAG,sDAAsD,oBAAI,QAAQ,GAAG,yDAAyD,oBAAI,QAAQ,GAAG,yDAAyD,oBAAI,QAAQ,GAAG,2CAA2C,oBAAI,QAAQ,GAAG,gDAAgD,oBAAI,QAAQ,GAAG,6DAA6D,oBAAI,QAAQ,GAAG,kEAAkE,oBAAI,QAAQ,GAAG,uEAAuE,oBAAI,QAAQ,GAAG,+DAA+D,oBAAI,QAAQ,GAAG,mDAAmD,oBAAI,QAAQ,GAAG,iDAAiD,oBAAI,QAAQ,GAAG,2DAA2D,oBAAI,QAAQ,GAAG,yEAAyE,oBAAI,QAAQ,GAAG,gEAAgE,oBAAI,QAAQ,GAAG,sEAAsE,oBAAI,QAAQ,GAAG,+DAA+D,oBAAI,QAAQ,GAAG,wDAAwD,oBAAI,QAAQ,GAAG,oDAAoD,oBAAI,QAAQ,GAAG,+CAA+C,oBAAI,QAAQ,GAAG,sDAAsD,oBAAI,QAAQ,GAAG,kDAAkD,oBAAI,QAAQ,GAAG,6CAA6C,SAASM,4CAA2C,UAAU,MAAM;AACjzE,MAAI;AAEJ,GAAC,KAAK,yBAAyB,MAAM,iDAAiD,GAAG,EAAE,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC,aAAa,SAAS,MAAM,MAAM,IAAI,CAAC;AAC/L,GAAG,4CAA4C,SAASC,2CAA0C,OAAO,UAAU;AAC/G,MAAI;AACJ,2BAAyB,MAAM,iDAAiD,GAAG,EAAE,KAAK,KAAK,KAAK,yBAAyB,MAAM,iDAAiD,GAAG,EAAE,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,CAAC,qBAAqB,aAAa,gBAAgB;AACvS;AAoYA,mDAAmD,oBAAI,QAAQ,GAAG,iDAAiD,oBAAI,QAAQ,GAAG,8CAA8C,oBAAI,QAAQ,GAAG,6CAA6C,oBAAI,QAAQ,GAAG,8CAA8C,oBAAI,QAAQ,GAAG,qDAAqD,oBAAI,QAAQ,GAAG,uDAAuD,oBAAI,QAAQ,GAAG,4DAA4D,oBAAI,QAAQ,GAAG,oDAAoD,oBAAI,QAAQ,GAAG,8DAA8D,oBAAI,QAAQ,GAAG,gDAAgD,oBAAI,QAAQ,GAAG,uDAAuD,oBAAI,QAAQ,GAAG,0DAA0D,oBAAI,QAAQ,GAAG,0DAA0D,oBAAI,QAAQ,GAAG,uDAAuD,oBAAI,QAAQ,GAAG,iDAAiD,oBAAI,QAAQ,GAAG,4CAA4C,oBAAI,QAAQ,GAAG,iDAAiD,oBAAI,QAAQ,GAAG,8DAA8D,oBAAI,QAAQ,GAAG,mEAAmE,oBAAI,QAAQ,GAAG,wEAAwE,oBAAI,QAAQ,GAAG,gEAAgE,oBAAI,QAAQ,GAAG,oDAAoD,oBAAI,QAAQ,GAAG,kDAAkD,oBAAI,QAAQ,GAAG,4DAA4D,oBAAI,QAAQ,GAAG,0EAA0E,oBAAI,QAAQ,GAAG,iEAAiE,oBAAI,QAAQ,GAAG,uEAAuE,oBAAI,QAAQ,GAAG,gEAAgE,oBAAI,QAAQ,GAAG,yDAAyD,oBAAI,QAAQ,GAAG,qDAAqD,oBAAI,QAAQ,GAAG,gDAAgD,oBAAI,QAAQ,GAAG,uDAAuD,oBAAI,QAAQ,GAAG,mDAAmD,oBAAI,QAAQ,GAAG,8CAA8C,SAASC,6CAA4C,UAAU,MAAM;AACz9E,MAAI;AAEJ,GAAC,KAAK,yBAAyB,MAAM,kDAAkD,GAAG,EAAE,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC,aAAa,SAAS,MAAM,MAAM,IAAI,CAAC;AAChM,GAAG,6CAA6C,SAASC,4CAA2C,OAAO,UAAU;AACjH,MAAI;AACJ,2BAAyB,MAAM,kDAAkD,GAAG,EAAE,KAAK,KAAK,KAAK,yBAAyB,MAAM,kDAAkD,GAAG,EAAE,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,CAAC,qBAAqB,aAAa,gBAAgB;AACzS;AAaA,IAAI;AA6DJ,8BAA8B,oBAAI,QAAQ;AAqC1C,IAAM,iCAAiC;AACvC,IAAM,sCAAsC;AAC5C,IAAM,aAAN,cAAyB,cAAc;AAAA,EACnC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AAAA,EACvB;AAAA,EACA,cAAc,OAAO;AACjB,UAAM,KAAK;AACX,SAAK,cAAc,KAAK;AAAA,EAC5B;AAAA,EACA,cAAc,OAAO;AACjB,QAAI,IAAI;AACR,UAAM,uBAAuB,KAAK,KAAK,SAAS,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,qCAAqC;AACxI,UAAM,aAAa,KAAK,KAAK,SAAS,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,oCAAoC;AAC7H,QAAI,qBAAqB;AACrB,UAAI,MAAM,SAAS,kCAAkC;AACjD,gBAAQ,MAAM,MAAM;AAAA,UAChB,KAAK;AACD,gCAAoB,YAAY;AAChC,gBAAI;AACA,wBAAU,iBAAiB,SAAS,MAAM;AACtC,uBAAO,SAAS,OAAO;AAAA,cAC3B,CAAC;AACL;AAAA,UACJ,KAAK;AACD,gCAAoB,YAAY;AAChC,gBAAI;AACA,wBAAU,MAAM,UAAU;AAC9B;AAAA,QACR;AAAA,MACJ;AACA,0BAAoB,YAAY,iCAAiC,MAAM,OAAO;AAAA,IAClF,OACK;AACD,cAAQ,IAAI,uCAAuC;AAAA,IACvD;AAAA,EACJ;AACJ;AACA,IAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUxB,IAAM,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkDZ,SAAS,yCAAyC;AAC9C,SAAOC,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,YAAY,OAAO,UAAU,UAAU,YAAY;AACzD,YAAM,cAAc,IAAI,WAAW;AACnC,UAAI,UAAU,SAAS,IAAI,GAAG;AAS1B,oBAAY,cAAc;AAAA,UACtB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACb,CAAC;AAAA,MACL,OACK;AAQD,oBAAY,cAAc;AAAA,UACtB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACb,CAAC;AAAA,MACL;AACA,kBAAY,KAAK;AAAA,IACrB;AAAA,EACJ,CAAC;AACL;AAKA,IAAM,YAAY;AAClB,SAAS,kCAAkC;AACvC,MAAI;AACJ,MAAI;AACA,cAAU,OAAO;AAAA,EAErB,SACO,IAAI;AAAA,EAAE;AACb,SAAO;AAAA,IACH,QAAQ;AACJ,aAAOC,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,YAAI,CAAC,SAAS;AACV;AAAA,QACJ;AACA,YAAI;AACA,kBAAQ,WAAW,SAAS;AAAA,QAEhC,SACO,IAAI;AAAA,QAAE;AAAA,MACjB,CAAC;AAAA,IACL;AAAA,IACA,MAAM;AACF,aAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,YAAI,CAAC,SAAS;AACV;AAAA,QACJ;AACA,YAAI;AACA,gBAAM,SAAS,KAAK,MAAM,QAAQ,QAAQ,SAAS,CAAC;AACpD,cAAI,UAAU,OAAO,UAAU;AAC3B,kBAAM,iBAAiB,OAAO,SAAS,IAAI,CAAC,YAAY;AACpD,qBAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,WAAW,eAAe,UACnE,IAAI,WAAW,OAAO,OAAO,QAAQ,SAAS,CAAC,IAC/C,IAAI,UAAU,QAAQ,OAAO,EAAE,QAAQ,EAAE,CAAC;AAAA,YACxD,CAAC;AACD,mBAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,eAAe,CAAC;AAAA,UAChF;AAEI,mBAAO,UAAU;AAAA,QAEzB,SACO,IAAI;AAAA,QAAE;AAAA,MACjB,CAAC;AAAA,IACL;AAAA,IACA,IAAI,qBAAqB;AACrB,aAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,YAAI,CAAC,SAAS;AACV;AAAA,QACJ;AACA,YAAI;AACA,kBAAQ,QAAQ,WAAW,KAAK,UAAU,mBAAmB,CAAC;AAAA,QAElE,SACO,IAAI;AAAA,QAAE;AAAA,MACjB,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AAEA,SAAS,6BAA6B;AAClC,SAAO;AAAA,IACH,OAAO,QAAQ;AACX,aAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,YAAI,OAAO,WAAW,GAAG;AACrB,iBAAO,OAAO,CAAC;AAAA,QACnB,WACS,OAAO,SAAS,oBAAoB,GAAG;AAC5C,iBAAO;AAAA,QACX;AAEI,iBAAO,OAAO,CAAC;AAAA,MACvB,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;;;ADzjDA,SAASC,WAAU,SAAS,YAAY,GAAG,WAAW;AAClD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAEA,SAAS,uBAAuB,UAAU,OAAO,MAAM,GAAG;AACtD,MAAI,SAAS,OAAO,CAAC,EAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,0EAA0E;AACjL,SAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,KAAK,QAAQ,IAAI,IAAI,EAAE,QAAQ,MAAM,IAAI,QAAQ;AAChG;AAEA,SAAS,uBAAuB,UAAU,OAAO,OAAO,MAAM,GAAG;AAC7D,MAAI,SAAS,IAAK,OAAM,IAAI,UAAU,gCAAgC;AACtE,MAAI,SAAS,OAAO,CAAC,EAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,yEAAyE;AAChL,SAAQ,SAAS,MAAM,EAAE,KAAK,UAAU,KAAK,IAAI,IAAI,EAAE,QAAQ,QAAQ,MAAM,IAAI,UAAU,KAAK,GAAI;AACxG;AAeA,IAAMC,mBAAkB;AAGxB,IAAMC,sBAAqB;AAG3B,IAAMC,kBAAiB;AA0BvB,SAASC,gBAAe,WAAW;AAC/B,SAAO,OAAO,KAAK,OAAO,aAAa,KAAK,MAAM,GAAG,SAAS,CAAC;AACnE;AAEA,SAAS,iBAAiB;AACtB,SAAQ,OAAO,WAAW,eACtB,OAAO,mBACP,OAAO,aAAa,eACpB,WAAW,KAAK,UAAU,SAAS;AAC3C;AAEA,IAAI;AAAJ,IAA8C;AAA9C,IAAqF;AAArF,IAAgI;AAAhI,IAA2K;AAA3K,IAA2N;AAA3N,IAA2Q;AAA3Q,IAAqT;AAArT,IAAuW;AAAvW,IAA+Y;AAA/Y,IAAwc;AAAxc,IAA2f;AAA3f,IAAmjB;AACnjB,IAAMC,uCAAsC;AAC5C,IAAMC,6BAA4B;AAClC,SAASC,wBAAuB,aAAa;AACzC,SAAO,aAAa;AACxB;AACA,SAAS,wBAAwB,OAAO;AACpC,UAAQ,OAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;AACA,IAAM,gCAAN,cAA4C,qCAAqC;AAAA,EAC7E,YAAY,QAAQ,QAAQ;AACxB,UAAM;AACN,6CAAyC,IAAI,IAAI;AACjD,SAAK,+BAA+B,oBAAI;AAAA;AAAA,MAExC,CAAC,UAAU,CAAC;AAAA,IAAC;AACb,0CAAsC,IAAI,MAAM,MAAM;AACtD,8CAA0C,IAAI,MAAM,KAAK;AACzD,8CAA0C,IAAI,MAAM,eAAe,IAAI,iBAAiB,WAAW,iBAAiB,WAAW;AAC/H,mDAA+C,IAAI,MAAM,MAAM;AAC/D,mDAA+C,IAAI,MAAM,MAAM;AAC/D,6CAAyC,IAAI,MAAM,MAAM;AACzD,qDAAiD,IAAI,MAAM,CAAC,eAAeC,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACpH,UAAI,WAAW,YAAY,WAAW,SAAS,SAAS,GAAG;AACvD,+BAAuB,MAAM,0CAA0C,KAAK,uDAAuD,EAAE,KAAK,IAAI;AAC9I,cAAM,sBAAsB,MAAM,uBAAuB,MAAM,gDAAgD,GAAG,EAAE,KAAK,MAAM,WAAW,QAAQ;AAClJ,YAAI,wBAAwB,uBAAuB,MAAM,gDAAgD,GAAG,GAAG;AAC3G,iCAAuB,MAAM,gDAAgD,qBAAqB,GAAG;AACrG,iCAAuB,MAAM,0CAA0C,QAAW,GAAG;AACrF,eAAK;AAAA,YAAK;AAAA;AAAA;AAAA,YAGV,KAAK;AAAA,UAAS;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ,CAAC,CAAC;AAEF,2BAAuB,MAAM,gDAAgD,CAAC,aAAaA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACpI,UAAI;AACJ,YAAM,+BAA+B,MAAM,OAAO,gBAAgB,OAAO,SAAS,IAAI,CAAC,EAAE,UAAU,MAAMJ,gBAAe,SAAS,CAAC,CAAC;AACnI,cAAQ,KAAK,SAAS,KAAK,CAAC,EAAE,UAAU,MAAMA,gBAAe,SAAS,MAAM,4BAA4B,OAAO,QAAQ,OAAO,SAAS,KAAK,SAAS,CAAC;AAAA,IAC1J,CAAC,GAAG,GAAG;AACP,2BAAuB,MAAM,uCAAuC,QAAQ,GAAG;AAC/E,2BAAuB,MAAM,uCAAuC,GAAG,EAAE,SAASK,eAAc,EAAE,GAAG,UAAU,uBAAuB,MAAM,kDAAkD,GAAG,CAAC;AAClM,SAAK,OAAO,uBAAuB,MAAM,uCAAuC,GAAG,EAAE;AACrF,SAAK,OAAO,uBAAuB,MAAM,uCAAuC,GAAG,EAAE;AACrF,SAAK,MAAM,uBAAuB,MAAM,uCAAuC,GAAG,EAAE;AAAA,EAaxF;AAAA,EACA,IAAI,YAAY;AACZ,QAAI;AACJ,QAAI,CAAC,uBAAuB,MAAM,0CAA0C,GAAG,KAAK,uBAAuB,MAAM,gDAAgD,GAAG,GAAG;AACnK,UAAI;AACA,+BAAuB,MAAM,0CAA0C,IAAI,UAAU,uBAAuB,MAAM,gDAAgD,GAAG,EAAE,SAAS,GAAG,GAAG;AAAA,MAC1L,SACO,GAAG;AACN,cAAM,IAAI,qBAAsB,aAAa,UAAU,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE,YAAa,iBAAiB,CAAC;AAAA,MAClI;AAAA,IACJ;AACA,YAAQ,KAAK,uBAAuB,MAAM,0CAA0C,GAAG,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,EAC/H;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,uBAAuB,MAAM,uCAAuC,GAAG,EAAE;AAAA,EACpF;AAAA,EACA,IAAI,aAAa;AACb,WAAO,uBAAuB,MAAM,2CAA2C,GAAG;AAAA,EACtF;AAAA,EACA,IAAI,aAAa;AACb,WAAO,uBAAuB,MAAM,2CAA2C,GAAG;AAAA,EACtF;AAAA;AAAA,EAEA,8CAA8C;AAC1C,WAAOD,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,aAAO,MAAM,KAAK,YAAY;AAAA,IAClC,CAAC;AAAA,EACL;AAAA,EACA,cAAc;AACV,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,6BAAuB,MAAM,0CAA0C,KAAK,sCAAsC,EAAE,KAAK,MAAM,IAAI;AAAA,IACvI,CAAC;AAAA,EACL;AAAA,EACA,UAAU;AACN,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,6BAAuB,MAAM,0CAA0C,KAAK,sCAAsC,EAAE,KAAK,IAAI;AAAA,IACjI,CAAC;AAAA,EACL;AAAA;AAAA,EAEA,qBAAqB,eAAe;AAChC,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAI;AACA,cAAM,4BAA4B,MAAM,uBAAuB,MAAM,uCAAuC,GAAG,EAAE;AACjH,YAAI,2BAA2B;AAC3B,gBAAM,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,SAASE,gBAAe,EAAE,QAAQ,EAAE,QAAQ,KAAK,CAAC;AACjI,iBAAO;AAAA,QACX;AACA,YAAI,eAAe;AACf,gBAAM,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,SAAS,YAAY,EAAE,OAAO,aAAa;AAAA,QAC9H;AAEI,gBAAM,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,SAASA,gBAAe,EAAE,QAAQ;AACrH,cAAM,sBAAsB,MAAM,MAAM,uBAAuB,MAAM,uCAAuC,GAAG,EAAE;AACjH,eAAO;AAAA,MACX,SACO,GAAG;AACN,cAAM,IAAI,sBAAuB,aAAa,SAAS,EAAE,WAAY,iBAAiB,CAAC;AAAA,MAC3F;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,aAAa;AACT,WAAOF,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAEhD,aAAO,MAAM,uBAAuB,MAAM,0CAA0C,KAAK,2CAA2C,EAAE,KAAK,MAAM,MAAMA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChM,+BAAuB,MAAM,2CAA2C,OAAO,GAAG;AAClF,+BAAuB,MAAM,0CAA0C,QAAW,GAAG;AACrF,+BAAuB,MAAM,gDAAgD,QAAW,GAAG;AAC3F,cAAM,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,SAASG,mBAAkB,EAAE,WAAW;AACvH,aAAK,KAAK,YAAY;AAAA,MAC1B,CAAC,CAAC;AAAA,IACN,CAAC;AAAA,EACL;AAAA,EACA,OAAO,OAAO;AACV,WAAOH,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,aAAO,uBAAuB,MAAM,0CAA0C,KAAK,2CAA2C,EAAE,KAAK,MAAM,MAAMA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAC1L,YAAI;AACJ,YAAI,uBAAuB,MAAM,2CAA2C,GAAG,MAAM,iBAAiB,aAAa,uBAAuB,MAAM,2CAA2C,GAAG,MAAM,iBAAiB,UAAU;AAC3N,gBAAM,IAAI,oBAAoB;AAAA,QAClC;AACA,+BAAuB,MAAM,2CAA2C,MAAM,GAAG;AACjF,YAAI;AACA,gBAAM,UAAU,MAAM,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,SAAS,YAAY,EAAE,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,SAAS,KAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO,SAAS,KAAK,CAAC,CAAC;AACzS,cAAI,QAAQ,SAAS,GAAG;AACpB,mBAAO,QAAQ,CAAC;AAAA,UACpB,OACK;AACD,kBAAM,IAAI,MAAM,sDAAsD;AAAA,UAC1E;AAAA,QACJ,SACO,GAAG;AACN,gBAAM,IAAI,sBAAuB,aAAa,SAAS,EAAE,WAAY,iBAAiB,CAAC;AAAA,QAC3F,UACA;AACI,iCAAuB,MAAM,2CAA2C,OAAO,GAAG;AAAA,QACtF;AAAA,MACJ,CAAC,CAAC;AAAA,IACN,CAAC;AAAA,EACL;AAAA,EACA,YAAY,SAAS;AACjB,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,aAAO,MAAM,uBAAuB,MAAM,0CAA0C,KAAK,2CAA2C,EAAE,KAAK,MAAM,MAAMA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChM,cAAM,UAAU,uBAAuB,MAAM,0CAA0C,KAAK,iDAAiD,EAAE,KAAK,IAAI;AACxJ,YAAI;AACA,gBAAM,UAAU,MAAM,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,SAAS,iBAAiB,EAAE,YAAY;AAAA,YACnI;AAAA,YAAS;AAAA,UACb,CAAC;AACD,iBAAO,QAAQ,CAAC,EAAE;AAAA,QACtB,SACO,OAAO;AACV,gBAAM,IAAI,uBAAuB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,SAAS,KAAK;AAAA,QACvG;AAAA,MACJ,CAAC,CAAC;AAAA,IACN,CAAC;AAAA,EACL;AAAA,EACA,gBAAgB,aAAa,YAAY,SAAS;AAC9C,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,aAAO,MAAM,uBAAuB,MAAM,0CAA0C,KAAK,2CAA2C,EAAE,KAAK,MAAM,MAAMA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChM,cAAM,UAAU,uBAAuB,MAAM,0CAA0C,KAAK,iDAAiD,EAAE,KAAK,IAAI;AACxJ,YAAI;AACA,cAAS,sBAAT,WAA+B;AAC3B,gBAAI;AACJ,oBAAQ,WAAW,YAAY;AAAA,cAC3B,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AACD,mCAAmB,WAAW;AAC9B;AAAA,cACJ;AACI,mCAAmB;AAAA,YAC3B;AACA,gBAAI;AACJ,oBAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,qBAAqB;AAAA,cACnF,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AACD,4CAA4B,QAAQ;AACpC;AAAA,cACJ,KAAK;AACD,4CAA4B;AAC5B;AAAA,cACJ;AACI,4CAA4B;AAAA,YACpC;AACA,kBAAM,2BAA2B,8BAA8B,cACzD,IACA,8BAA8B,cAC1B,IACA;AACV,kBAAM,wBAAwB,qBAAqB,cAAc,IAAI,qBAAqB,cAAc,IAAI;AAC5G,mBAAO,2BAA2B,wBAC5B,4BACA;AAAA,UACV;AACA,cAAI,gCAAgC,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,UAAU;AACnH,kBAAM,QAAQ,wBAAwB,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,qBAAqB,KAAK;AACzI,kBAAM,CAAC,SAAS,KAAK,MAAM,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,SAAS,4BAA4B,EAAE,uBAAuB;AAAA,cAC9J;AAAA,cACA,aAAa,YAAY,UAAU;AAAA,cACnC;AAAA,cACA,SAAS,UAAU;AAAA,gBACf,eAAe,QAAQ;AAAA,gBACvB,YAAY,QAAQ;AAAA,cACxB,IAAI;AAAA,YACR,CAAC,GAAG,IAAK,CAAC,WAAW;AACjB,qBAAOJ,gBAAe,OAAO,SAAS;AAAA,YAC1C,CAAE;AACF,mBAAO;AAAA,UACX,OACK;AACD,kBAAM,CAAC,iBAAiB,IAAI,MAAM,uBAAuB,MAAM,0CAA0C,KAAK,sDAAsD,EAAE,KAAK,MAAM,CAAC,WAAW,CAAC;AAC9L,gBAAIG,wBAAuB,iBAAiB,GAAG;AAC3C,qBAAO,MAAM,WAAW,gBAAgB,iBAAiB;AAAA,YAC7D,OACK;AACD,oBAAM,wBAAwB,kBAAkB,UAAU;AAC1D,qBAAO,MAAM,WAAW,mBAAmB,uBAAuB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,qBAAqB,oBAAoB,EAAE,CAAC,CAAC;AAAA,YAC/J;AAAA,UACJ;AAAA,QACJ,SACO,OAAO;AACV,gBAAM,IAAI,2BAA2B,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,SAAS,KAAK;AAAA,QAC3G;AAAA,MACJ,CAAC,CAAC;AAAA,IACN,CAAC;AAAA,EACL;AAAA,EACA,gBAAgB,aAAa;AACzB,WAAOC,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,aAAO,MAAM,uBAAuB,MAAM,0CAA0C,KAAK,2CAA2C,EAAE,KAAK,MAAM,MAAMA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChM,cAAM,CAAC,iBAAiB,IAAI,MAAM,uBAAuB,MAAM,0CAA0C,KAAK,sDAAsD,EAAE,KAAK,MAAM,CAAC,WAAW,CAAC;AAC9L,eAAO;AAAA,MACX,CAAC,CAAC;AAAA,IACN,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB,cAAc;AAC9B,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,aAAO,MAAM,uBAAuB,MAAM,0CAA0C,KAAK,2CAA2C,EAAE,KAAK,MAAM,MAAMA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChM,cAAM,qBAAqB,MAAM,uBAAuB,MAAM,0CAA0C,KAAK,sDAAsD,EAAE,KAAK,MAAM,YAAY;AAC5L,eAAO;AAAA,MACX,CAAC,CAAC;AAAA,IACN,CAAC;AAAA,EACL;AACJ;AACA,wCAAwC,oBAAI,QAAQ,GAAG,4CAA4C,oBAAI,QAAQ,GAAG,4CAA4C,oBAAI,QAAQ,GAAG,iDAAiD,oBAAI,QAAQ,GAAG,iDAAiD,oBAAI,QAAQ,GAAG,2CAA2C,oBAAI,QAAQ,GAAG,mDAAmD,oBAAI,QAAQ,GAAG,2CAA2C,oBAAI,QAAQ,GAAG,yCAAyC,SAASI,wCAAuC,cAAc,OAAO;AAC7kB,SAAOJ,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,QAAI,KAAK,cAAc,KAAK,WAAW;AACnC;AAAA,IACJ;AACA,WAAO,MAAM,uBAAuB,MAAM,0CAA0C,KAAK,2CAA2C,EAAE,KAAK,MAAM,MAAMA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChM,UAAI,uBAAuB,MAAM,2CAA2C,GAAG,MAAM,iBAAiB,aAAa,uBAAuB,MAAM,2CAA2C,GAAG,MAAM,iBAAiB,UAAU;AAC3N,cAAM,IAAI,oBAAoB;AAAA,MAClC;AACA,6BAAuB,MAAM,2CAA2C,MAAM,GAAG;AACjF,UAAI;AACA,cAAM,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,SAASE,gBAAe,EAAE,QAAQ,EAAE,QAAQ,YAAY,CAAC;AAAA,MAC5I,SACO,GAAG;AACN,cAAM,IAAI,sBAAuB,aAAa,SAAS,EAAE,WAAY,iBAAiB,CAAC;AAAA,MAC3F,UACA;AACI,+BAAuB,MAAM,2CAA2C,OAAO,GAAG;AAAA,MACtF;AAAA,IACJ,CAAC,CAAC;AAAA,EACN,CAAC;AACL,GAAG,0DAA0D,SAASG,2DAA0D;AAC5H,MAAI,uBAAuB,MAAM,2CAA2C,GAAG,MAAM,iBAAiB,WAAW;AAC7G,SAAK,KAAK,oBAAqB,uBAAuB,MAAM,2CAA2C,iBAAiB,WAAW,GAAG,CAAE;AAAA,EAC5I;AACJ,GAAG,oDAAoD,SAASC,qDAAoD;AAChH,MAAI,CAAC,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,gBAAgB,CAAC,uBAAuB,MAAM,gDAAgD,GAAG;AAC3K,UAAM,IAAI,wBAAwB;AACtC,SAAO,uBAAuB,MAAM,gDAAgD,GAAG;AAC3F,GAAG,yDAAyD,SAASC,wDAAuD,cAAc;AACtI,SAAOP,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAM,UAAU,uBAAuB,MAAM,0CAA0C,KAAK,iDAAiD,EAAE,KAAK,IAAI;AACxJ,QAAI;AACA,UAAI,yBAAyB,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,UAAU;AAC5G,eAAO,uBAAuB,MAAM,uCAAuC,GAAG,EAAE,SAAS,qBAAqB,EAAE,gBAAgB,GAAG,aAAa,IAAI,CAAC,UAAU;AAC3J,iBAAO,EAAE,SAAS,aAAa,MAAM,UAAU,EAAE;AAAA,QACrD,CAAC,CAAC,EAAE,KAAK,CAAC,YAAY;AAClB,iBAAO,QAAQ,IAAI,CAAC,WAAW;AAC3B,kBAAM,YAAY,OAAO;AACzB,kBAAM,gBAAgB,UAAU,CAAC;AACjC,kBAAM,gBAAgB,gBAAgBF,6BAA4B;AAClE,kBAAM,UAAU,iBAAiB,0BAA0B,UAAU,MAAM,eAAe,UAAU,MAAM,CAAC;AAC3G,gBAAI,YAAY,UAAU;AACtB,qBAAO,YAAY,KAAK,SAAS;AAAA,YACrC,OACK;AACD,qBAAO,qBAAqB,YAAY,SAAS;AAAA,YACrD;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MACL,OACK;AACD,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC5E;AAAA,IACJ,SACO,OAAO;AACV,YAAM,IAAI,2BAA2B,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,SAAS,KAAK;AAAA,IAC3G;AAAA,EACJ,CAAC;AACL,GAAG,8CAA8C,SAASU,6CAA4C,UAAU;AAC5G,SAAOR,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,QAAI;AACA,aAAO,MAAM,SAAS;AAAA,IAC1B,SACO,GAAG;AACN,WAAK,KAAK,SAAS,CAAC;AACpB,YAAM;AAAA,IACV;AAAA,EACJ,CAAC;AACL;AACA,IAAM,iCAAN,cAA6C,8BAA8B;AAAA,EACvE,YAAY,QAAQ;AAChB,QAAI;AACJ,UAAM,QAAQ,yBAAyB,KAAK,OAAO,WAAW,QAAQ,OAAO,SAAS,KAAK,OAAO,OAAO;AACzG,UAAM,IAAI,qCAAqC;AAAA,MAC3C,aAAa,OAAO;AAAA,MACpB,oBAAoB;AAAA,QAChB,KAAK,OAAO,yBAAyB;AAAA,QACrC,KAAK,MAAMA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACpD,gBAAM,sBAAsB,MAAM,OAAO,yBAAyB,IAAI;AACtE,cAAI,uBAAuB,WAAW,qBAAqB;AACvD,mBAAO;AAAA,UACX,WACS,qBAAqB;AAC1B,mBAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,mBAAmB,GAAG,EAAE,MAAa,CAAC;AAAA,UACjF;AAEI,mBAAO;AAAA,QACf,CAAC;AAAA,QACD,OAAO,OAAO,yBAAyB;AAAA,MAC3C;AAAA,MACA,QAAQ,CAAC,KAAK;AAAA,MACd,eAAe,2BAA2B;AAAA,MAC1C,kBAAkB,MAAMA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AACjE,eAAO,iBAAiB,IAAI;AAAA,MAChC,CAAC;AAAA,IACL,CAAC,GAAG;AAAA,MACA,iBAAiB,OAAO;AAAA,MACxB;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAiCA,IAAM,4BAAN,cAAwC,+BAA+B;AACvE;AAEA,SAAS,+BAA+B;AACpC,SAAO;AAAA,IACH,OAAO,WAAW;AACd,aAAOS,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,eAAO,UAAU,CAAC;AAAA,MACtB,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AAEA,SAAS,wCAAwC;AAC7C,SAAO,gCAAgC;AAC3C;AAEA,SAAS,6BAA6B,qBAAqB;AACvD,SAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,WAAO,uCAAuC;AAAA,EAClD,CAAC;AACL;AACA,SAAS,qCAAqC;AAC1C,SAAO;AACX;;;AI5eA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBM,IAAO,wBAAP,cAAqC,kBAAiB;EAaxD,IAAI,OAAI;AACJ,WAAOC,wBAAA,MAAI,+BAAA,GAAA,EAAS;EACxB;EAEA,IAAI,MAAG;AACH,WAAO;EACX;EAEA,IAAI,OAAI;AACJ,WAAOA,wBAAA,MAAI,+BAAA,GAAA,EAAS;EACxB;EAEA,IAAI,aAAU;AACV,WAAOA,wBAAA,MAAI,mCAAA,GAAA;EACf;EAEA,IAAI,YAAS;AACT,WAAOA,wBAAA,MAAI,kCAAA,GAAA;EACf;EAEA,IAAI,aAAU;AACV,WAAOA,wBAAA,MAAI,mCAAA,GAAA;EACf;EAEA,IAAI,+BAA4B;AAC5B,WAAOA,wBAAA,MAAI,qDAAA,GAAA;EACf;EAEA,IAAI,SAAM;AACN,WAAOA,wBAAA,MAAI,+BAAA,GAAA;EACf;EAEA,IAAI,WAAQ;AACR,WAAO;EACX;EAEA,YAAY,EAAE,OAAM,GAA+B;AAC/C,UAAK;;AAjDT,mCAAA,IAAA,MAAA,MAAA;AACA,qCAAA,IAAA,MAAA,MAAA;AACA,sCAAA,IAAA,MAAA,MAAA;AACA,yCAAA,IAAA,MAAA,MAAA;AACA,+BAAA,IAAA,MAAA,MAAA;AACA,wDAAA,IAAA,MAAA,MAAA;AACS,kCAAA,IAAA,MAAA,MAAA;AACA,sCAAA,IAAA,MACL,OAAO,WAAW,eAAe,OAAO,aAAa,cAC/C,iBAAiB,cACjB,iBAAiB,SAAS;AAsKpC,mCAAA,IAAA,MAA8C,CAAC,eAAc;AAEzD,UAAI,cAAc,YAAY;AAC1B,cAAM,UAAUA,wBAAA,MAAI,+BAAA,GAAA,EAAS,SAAS,CAAC;AAEvC,YAAIA,wBAAA,MAAI,gCAAA,GAAA,KAAa,CAACA,wBAAA,MAAI,sCAAA,GAAA,KAAmB,YAAYA,wBAAA,MAAI,gCAAA,GAAA,GAAW;AAEpE,cAAI,SAAS;AAET,YAAAA,wBAAA,MAAI,kCAAA,KAAA,gCAAA,EAAW,KAAf,MAAgB,OAAO;UAC3B,OAAO;AAEH,iBAAK,KAAK,SAAS,IAAI,wBAAuB,CAAE;AAEhD,YAAAA,wBAAA,MAAI,kCAAA,KAAA,mCAAA,EAAc,KAAlB,IAAI;UACR;QACJ;MACJ;AAGA,UAAI,cAAc,YAAY;AAC1B,QAAAA,wBAAA,MAAI,kCAAA,KAAA,4BAAA,EAAO,KAAX,IAAI;MACR;IACJ,CAAC;AApJG,IAAAC,wBAAA,MAAI,+BAAW,QAAM,GAAA;AACrB,IAAAA,wBAAA,MAAI,gCAAY,MAAI,GAAA;AACpB,IAAAA,wBAAA,MAAI,kCAAc,MAAI,GAAA;AACtB,IAAAA,wBAAA,MAAI,mCAAe,OAAK,GAAA;AACxB,IAAAA,wBAAA,MAAI,sCAAkB,OAAK,GAAA;AAC3B,IAAAA,wBAAA,MAAI,4BAAQD,wBAAA,MAAI,+BAAA,GAAA,EAAS,SAAS,cAAc,EAAE,GAAG,UAAUA,wBAAA,MAAI,gCAAA,GAAA,CAAS,GAAC,GAAA;AAE7E,IAAAA,wBAAA,MAAI,kCAAA,KAAA,4BAAA,EAAO,KAAX,IAAI;EACR;EAEA,UAAO;AACH,IAAAC,wBAAA,MAAI,gCAAY,MAAI,GAAA;AACpB,IAAAA,wBAAA,MAAI,kCAAc,MAAI,GAAA;AACtB,IAAAA,wBAAA,MAAI,mCAAe,OAAK,GAAA;AACxB,IAAAA,wBAAA,MAAI,sCAAkB,OAAK,GAAA;AAE3B,UAAM,MAAMD,wBAAA,MAAI,4BAAA,GAAA;AAChB,QAAI,KAAK;AACL,MAAAC,wBAAA,MAAI,4BAAQ,MAAI,GAAA;AAChB,UAAG;IACP;EACJ;EAEA,MAAM,cAAW;AACb,WAAOD,wBAAA,MAAI,kCAAA,KAAA,8BAAA,EAAS,KAAb,MAAc,EAAE,QAAQ,KAAI,CAAE;EACzC;EAEA,MAAM,UAAO;AACT,WAAOA,wBAAA,MAAI,kCAAA,KAAA,8BAAA,EAAS,KAAb,IAAI;EACf;EA6BA,MAAM,aAAU;AACZ,QAAI,sBAAsBA,wBAAA,MAAI,+BAAA,GAAA,EAAS,UAAU;AAC7C,UAAI;AACA,QAAAC,wBAAA,MAAI,sCAAkB,MAAI,GAAA;AAC1B,cAAMD,wBAAA,MAAI,+BAAA,GAAA,EAAS,SAAS,kBAAkB,EAAE,WAAU;MAC9D,SAAS,OAAY;AACjB,aAAK,KAAK,SAAS,IAAI,yBAAyB,+BAAO,SAAS,KAAK,CAAC;MAC1E;AACI,QAAAC,wBAAA,MAAI,sCAAkB,OAAK,GAAA;MAC/B;IACJ;AAEA,IAAAD,wBAAA,MAAI,kCAAA,KAAA,mCAAA,EAAc,KAAlB,IAAI;EACR;EA+EA,MAAM,gBACF,aACA,YACA,UAAkC,CAAA,GAAE;AAEpC,QAAI;AACA,YAAM,UAAUA,wBAAA,MAAI,gCAAA,GAAA;AACpB,UAAI,CAAC;AAAS,cAAM,IAAI,wBAAuB;AAE/C,UAAI;AACJ,UAAI,gCAAgCA,wBAAA,MAAI,+BAAA,GAAA,EAAS,UAAU;AACvD,YAAI,QAAQ,SAAS,SAAS,4BAA4B,GAAG;AACzD,oBAAU;QACd,WACI,yBAAyBA,wBAAA,MAAI,+BAAA,GAAA,EAAS,YACtC,QAAQ,SAAS,SAAS,qBAAqB,GACjD;AACE,oBAAU;QACd,OAAO;AACH,gBAAM,IAAI,mBAAkB;QAChC;MACJ,WAAW,yBAAyBA,wBAAA,MAAI,+BAAA,GAAA,EAAS,UAAU;AACvD,YAAI,CAAC,QAAQ,SAAS,SAAS,qBAAqB;AAAG,gBAAM,IAAI,mBAAkB;AACnF,kBAAU;MACd,OAAO;AACH,cAAM,IAAI,kBAAiB;MAC/B;AAEA,YAAM,QAAQ,oBAAoB,WAAW,WAAW;AACxD,UAAI,CAAC,QAAQ,OAAO,SAAS,KAAK;AAAG,cAAM,IAAI,2BAA0B;AAEzE,UAAI;AACA,cAAM,EAAE,SAAS,GAAG,YAAW,IAAK;AAEpC,YAAI;AACJ,YAAI,uBAAuB,WAAW,GAAG;AACrC,8CAAS,WAAU,YAAY,KAAK,OAAO;AAC3C,kCAAwB,YAAY,UAAS;QACjD,OAAO;AACH,wBAAe,MAAM,KAAK,mBAAmB,aAAa,YAAY,WAAW;AACjF,8CAAS,WAAW,YAA4B,YAAY,GAAG,OAAO;AACtE,kCAAwB,IAAI,WACvB,YAA4B,UAAU;YACnC,sBAAsB;YACtB,kBAAkB;WACrB,CAAC;QAEV;AAEA,YAAI,YAAY,8BAA8B;AAC1C,gBAAM,CAAC,MAAM,IAAI,MAAOA,wBAAA,MAAI,+BAAA,GAAA,EAAS,SACjC,4BAA4B,EAC9B,uBAAuB;YACrB;YACA;YACA,aAAa;YACb,SAAS;cACL,qBAAqB,cACjB,YAAY,uBAAuB,WAAW,UAAU;cAE5D,eAAe,YAAY;cAC3B,YAAY,YAAY;cACxB,gBAAgB,YAAY;;WAEnC;AAED,iBAAO,YAAK,OAAO,OAAQ,SAAS;QACxC,OAAO;AACH,gBAAM,CAAC,MAAM,IAAI,MAAOA,wBAAA,MAAI,+BAAA,GAAA,EAAS,SACjC,qBAAqB,EACvB,gBAAgB;YACd;YACA;YACA,aAAa;YACb,SAAS;cACL,qBAAqB,cACjB,YAAY,uBAAuB,WAAW,UAAU;cAE5D,gBAAgB,YAAY;;WAEnC;AAED,iBAAO,MAAM,WAAW,mBAAmB,OAAQ,mBAAmB;YAClE,GAAG;YACH,qBAAqB,cAAc,YAAY,uBAAuB,WAAW,UAAU;WAC9F;QACL;MACJ,SAAS,OAAY;AACjB,YAAI,iBAAiB;AAAa,gBAAM;AACxC,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;MAC9D;IACJ,SAAS,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;IACV;EACJ;;isBAtNA,eAAKE,gCAAU,OAA4B;AACvC,MAAI;AACA,QAAI,KAAK,aAAa,KAAK;AAAY;AACvC,QAAIF,wBAAA,MAAI,mCAAA,GAAA,MAAiB,iBAAiB;AAAW,YAAM,IAAI,oBAAmB;AAElF,IAAAC,wBAAA,MAAI,mCAAe,MAAI,GAAA;AAEvB,QAAI,CAACD,wBAAA,MAAI,+BAAA,GAAA,EAAS,SAAS,QAAQ;AAC/B,UAAI;AACA,cAAMA,wBAAA,MAAI,+BAAA,GAAA,EAAS,SAAS,eAAe,EAAE,QAAQ,KAAK;MAC9D,SAAS,OAAY;AACjB,cAAM,IAAI,sBAAsB,+BAAO,SAAS,KAAK;MACzD;IACJ;AAEA,UAAM,UAAUA,wBAAA,MAAI,+BAAA,GAAA,EAAS,SAAS,CAAC;AACvC,QAAI,CAAC;AAAS,YAAM,IAAI,mBAAkB;AAE1C,IAAAA,wBAAA,MAAI,kCAAA,KAAA,gCAAA,EAAW,KAAf,MAAgB,OAAO;EAC3B,SAAS,OAAY;AACjB,SAAK,KAAK,SAAS,KAAK;AACxB,UAAM;EACV;AACI,IAAAC,wBAAA,MAAI,mCAAe,OAAK,GAAA;EAC5B;AACJ,GAAC,mCAAA,SAAAE,kCAiBU,SAAsB;AAC7B,MAAI;AACJ,MAAI;AAEA,gBAAY,IAAI,UAAU,QAAQ,OAAO;EAC7C,SAAS,OAAY;AACjB,UAAM,IAAI,qBAAqB,+BAAO,SAAS,KAAK;EACxD;AAEA,EAAAF,wBAAA,MAAI,gCAAY,SAAO,GAAA;AACvB,EAAAA,wBAAA,MAAI,kCAAc,WAAS,GAAA;AAC3B,EAAAD,wBAAA,MAAI,kCAAA,KAAA,4BAAA,EAAO,KAAX,IAAI;AACJ,OAAK,KAAK,WAAW,SAAS;AAClC,GAAC,sCAAA,SAAAI,uCAAA;AAGG,EAAAH,wBAAA,MAAI,gCAAY,MAAI,GAAA;AACpB,EAAAA,wBAAA,MAAI,kCAAc,MAAI,GAAA;AACtB,EAAAD,wBAAA,MAAI,kCAAA,KAAA,4BAAA,EAAO,KAAX,IAAI;AACJ,OAAK,KAAK,YAAY;AAC1B,GAAC,+BAAA,SAAAK,gCAAA;;AAGG,QAAM,+BACF,gCAAgCL,wBAAA,MAAI,+BAAA,GAAA,EAAS,WACvCA,wBAAA,MAAI,+BAAA,GAAA,EAAS,SAAS,4BAA4B,EAAE,+BACpDA,wBAAA,MAAI,+BAAA,GAAA,EAAS,SAAS,qBAAqB,EAAE;AACvD,EAAAC,wBAAA,MAAI,qDAAiC,YAAY,8BAA8B,CAAC,QAAQ,CAAC,IACnF,OACA,IAAI,IAAI,4BAA4B,GAAC,GAAA;AAE3C,MAAI,yBAAyBD,wBAAA,MAAI,+BAAA,GAAA,EAAS,cAAY,KAAAA,wBAAA,MAAI,gCAAA,GAAA,MAAJ,mBAAe,SAAS,SAAS,yBAAwB;AAC3G,SAAK,kBAAkBA,wBAAA,MAAI,kCAAA,KAAA,sCAAA;AAC3B,SAAK,sBAAsBA,wBAAA,MAAI,kCAAA,KAAA,0CAAA;EACnC,OAAO;AACH,WAAO,KAAK;AACZ,WAAO,KAAK;EAChB;AAEA,MAAI,qBAAqBA,wBAAA,MAAI,+BAAA,GAAA,EAAS,cAAY,KAAAA,wBAAA,MAAI,gCAAA,GAAA,MAAJ,mBAAe,SAAS,SAAS,qBAAoB;AACnG,SAAK,cAAcA,wBAAA,MAAI,kCAAA,KAAA,kCAAA;EAC3B,OAAO;AACH,WAAO,KAAK;EAChB;AAEA,MAAI,gBAAgBA,wBAAA,MAAI,+BAAA,GAAA,EAAS,UAAU;AACvC,SAAK,SAASA,wBAAA,MAAI,kCAAA,KAAA,6BAAA;EACtB,OAAO;AACH,WAAO,KAAK;EAChB;AACJ,GAAC,yCA6HD,eAAKM,wCAAgE,aAAc;AAC/E,MAAI;AACA,UAAM,UAAUN,wBAAA,MAAI,gCAAA,GAAA;AACpB,QAAI,CAAC;AAAS,YAAM,IAAI,wBAAuB;AAE/C,QAAI,EAAE,yBAAyBA,wBAAA,MAAI,+BAAA,GAAA,EAAS;AAAW,YAAM,IAAI,kBAAiB;AAClF,QAAI,CAAC,QAAQ,SAAS,SAAS,qBAAqB;AAAG,YAAM,IAAI,mBAAkB;AAEnF,QAAI;AACA,YAAM,qBAAqB,MAAMA,wBAAA,MAAI,+BAAA,GAAA,EAAS,SAAS,qBAAqB,EAAE,gBAAgB;QAC1F;QACA,aAAa,uBAAuB,WAAW,IACzC,YAAY,UAAS,IACrB,IAAI,WACA,YAAY,UAAU;UAClB,sBAAsB;UACtB,kBAAkB;SACrB,CAAC;OAEf;AAED,YAAM,wBAAwB,mBAAmB,CAAC,EAAG;AAErD,aACI,uBAAuB,WAAW,IAC5B,qBAAqB,YAAY,qBAAqB,IACtD,YAAY,KAAK,qBAAqB;IAEpD,SAAS,OAAY;AACjB,UAAI,iBAAiB;AAAa,cAAM;AACxC,YAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;IAC9D;EACJ,SAAS,OAAY;AACjB,SAAK,KAAK,SAAS,KAAK;AACxB,UAAM;EACV;AACJ,GAAC,6CAGD,eAAKO,4CAAoE,cAAiB;AACtF,MAAI;AACA,UAAM,UAAUP,wBAAA,MAAI,gCAAA,GAAA;AACpB,QAAI,CAAC;AAAS,YAAM,IAAI,wBAAuB;AAE/C,QAAI,EAAE,yBAAyBA,wBAAA,MAAI,+BAAA,GAAA,EAAS;AAAW,YAAM,IAAI,kBAAiB;AAClF,QAAI,CAAC,QAAQ,SAAS,SAAS,qBAAqB;AAAG,YAAM,IAAI,mBAAkB;AAEnF,QAAI;AACA,YAAM,qBAAqB,MAAMA,wBAAA,MAAI,+BAAA,GAAA,EAAS,SAAS,qBAAqB,EAAE,gBAC1E,GAAG,aAAa,IAAI,CAAC,iBAAiB;QAClC;QACA,aAAa,uBAAuB,WAAW,IACzC,YAAY,UAAS,IACrB,IAAI,WACA,YAAY,UAAU;UAClB,sBAAsB;UACtB,kBAAkB;SACrB,CAAC;QAEd,CAAC;AAGP,aAAO,aAAa,IAAI,CAAC,aAAa,UAAS;AAC3C,cAAM,oBAAoB,mBAAmB,KAAK,EAAG;AAErD,eACI,uBAAuB,WAAW,IAC5B,qBAAqB,YAAY,iBAAiB,IAClD,YAAY,KAAK,iBAAiB;MAEhD,CAAC;IACL,SAAS,OAAY;AACjB,YAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;IAC9D;EACJ,SAAS,OAAY;AACjB,SAAK,KAAK,SAAS,KAAK;AACxB,UAAM;EACV;AACJ,GAAC,qCAGD,eAAKQ,oCAAc,SAAmB;AAClC,MAAI;AACA,UAAM,UAAUR,wBAAA,MAAI,gCAAA,GAAA;AACpB,QAAI,CAAC;AAAS,YAAM,IAAI,wBAAuB;AAE/C,QAAI,EAAE,qBAAqBA,wBAAA,MAAI,+BAAA,GAAA,EAAS;AAAW,YAAM,IAAI,kBAAiB;AAC9E,QAAI,CAAC,QAAQ,SAAS,SAAS,iBAAiB;AAAG,YAAM,IAAI,mBAAkB;AAE/E,QAAI;AACA,YAAM,iBAAiB,MAAMA,wBAAA,MAAI,+BAAA,GAAA,EAAS,SAAS,iBAAiB,EAAE,YAAY;QAC9E;QACA;OACH;AAED,aAAO,eAAe,CAAC,EAAG;IAC9B,SAAS,OAAY;AACjB,YAAM,IAAI,uBAAuB,+BAAO,SAAS,KAAK;IAC1D;EACJ,SAAS,OAAY;AACjB,SAAK,KAAK,SAAS,KAAK;AACxB,UAAM;EACV;AACJ,GAAC,gCAGD,eAAKS,+BAAS,QAA2B,CAAA,GAAE;AACvC,MAAI;AACA,QAAI,EAAE,gBAAgBT,wBAAA,MAAI,+BAAA,GAAA,EAAS;AAAW,YAAM,IAAI,kBAAiB;AAEzE,QAAI;AACJ,QAAI;AACA,OAAC,MAAM,IAAI,MAAMA,wBAAA,MAAI,+BAAA,GAAA,EAAS,SAAS,YAAY,EAAE,OAAO,KAAK;IACrE,SAAS,OAAY;AACjB,YAAM,IAAI,kBAAkB,+BAAO,SAAS,KAAK;IACrD;AAEA,QAAI,CAAC;AAAQ,YAAM,IAAI,kBAAiB;AACxC,IAAAA,wBAAA,MAAI,kCAAA,KAAA,gCAAA,EAAW,KAAf,MAAgB,OAAO,OAAO;AAC9B,WAAO;EACX,SAAS,OAAY;AACjB,SAAK,KAAK,SAAS,KAAK;AACxB,UAAM;EACV;AACJ;;;ACpcG,IAAM,kCAAkC;;;ACH/C;;;;;;;;;;;;;;;ACbA,IAAI,UAA+B;AACnC,IAAM,uBAAuB,oBAAI,IAAG;AACpC,SAAS,oBAAoB,QAAc;AACvC,uBAAqB;AACrB,uBAAqB,IAAI,MAAM;AACnC;AACA,SAAS,uBAAuB,QAAc;AAC1C,uBAAqB;AACrB,uBAAqB,OAAO,MAAM;AACtC;AACA,IAAM,YAAwE,CAAA;AAqBxE,SAAU,aAAU;AACtB,MAAI;AAAS,WAAO;AACpB,YAAU,OAAO,OAAO,EAAE,UAAU,KAAK,GAAE,CAAE;AAC7C,MAAI,OAAO,WAAW;AAAa,WAAO;AAE1C,QAAM,MAAM,OAAO,OAAO,EAAE,SAAQ,CAAE;AACtC,MAAI;AACC,WAA8B,iBAAiB,mCAAmC,CAAC,EAAE,QAAQ,SAAQ,MAClG,SAAS,GAAG,CAAC;EAErB,SAAS,OAAO;AACZ,YAAQ,MAAM,uEAAuE,KAAK;EAC9F;AACA,MAAI;AACC,WAA8B,cAAc,IAAI,cAAc,GAAG,CAAC;EACvE,SAAS,OAAO;AACZ,YAAQ,MAAM,6DAA6D,KAAK;EACpF;AAEA,SAAO;AACX;AAkFA,SAAS,YAAYU,UAAiB;;AAIlC,EAAAA,WAAUA,SAAQ,OAAO,CAAC,WAAW,CAAC,qBAAqB,IAAI,MAAM,CAAC;AAGtE,MAAI,CAACA,SAAQ;AAAQ,WAAO,MAAK;IAAE;AAEnC,EAAAA,SAAQ,QAAQ,CAAC,WAAW,oBAAoB,MAAM,CAAC;AACvD,kBAAU,UAAU,MAApB,mBAAuB,QAAQ,CAAC,aAAa,MAAM,MAAM,SAAS,GAAGA,QAAO,CAAC;AAE7E,SAAO,SAAS,aAAU;;AACtB,IAAAA,SAAQ,QAAQ,CAAC,WAAW,uBAAuB,MAAM,CAAC;AAC1D,KAAAC,MAAA,UAAU,YAAY,MAAtB,gBAAAA,IAAyB,QAAQ,CAAC,aAAa,MAAM,MAAM,SAAS,GAAGD,QAAO,CAAC;EACnF;AACJ;AAEA,IAAI;AACJ,SAAS,MAAG;AACR,MAAI,CAAC,oBAAoB;AACrB,yBAAqB,CAAC,GAAG,oBAAoB;EACjD;AACA,SAAO;AACX;AAEA,SAAS,GAAgC,OAAU,UAAmC;;AAClF,mBAAU,KAAK,MAAf,mBAAkB,KAAK,eAAc,UAAU,KAAK,IAAI,CAAC,QAAQ;AAEjE,SAAO,SAAS,MAAG;;AACf,cAAU,KAAK,KAAIC,MAAA,UAAU,KAAK,MAAf,gBAAAA,IAAkB,OAAO,CAAC,qBAAqB,aAAa;EACnF;AACJ;AAEA,SAAS,MAAM,UAAoB;AAC/B,MAAI;AACA,aAAQ;EACZ,SAAS,OAAO;AACZ,YAAQ,MAAM,KAAK;EACvB;AACJ;AAEA,IAAM,gBAAN,cAA4B,MAAK;EAG7B,IAAI,SAAM;AACN,WAAOC,wBAAA,MAAI,uBAAA,GAAA;EACf;EAEA,IAAI,OAAI;AACJ,WAAO;EACX;EAEA,YAAY,KAA2B;AACnC,UAAM,6BAA6B;MAC/B,SAAS;MACT,YAAY;MACZ,UAAU;KACb;AAfI,0BAAA,IAAA,MAAA,MAAA;AAgBL,IAAAC,wBAAA,MAAI,uBAAW,KAAG,GAAA;EACtB;;EAGA,iBAAc;AACV,UAAM,IAAI,MAAM,iCAAiC;EACrD;;EAGA,2BAAwB;AACpB,UAAM,IAAI,MAAM,2CAA2C;EAC/D;;EAGA,kBAAe;AACX,UAAM,IAAI,MAAM,kCAAkC;EACtD;;;AAQE,SAAU,wBAAqB;AACjC,MAAI;AAAS,WAAO;AACpB,YAAU,WAAU;AACpB,MAAI,OAAO,WAAW;AAAa,WAAO;AAE1C,QAAM,YAAa,OAAoC,UAAU,WAAW,CAAA;AAC5E,MAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC3B,YAAQ,MAAM,0CAA0C;AACxD,WAAO;EACX;AAEA,QAAM,EAAE,UAAAC,UAAQ,IAAK;AACrB,QAAM,OAAO,IAAIC,eACbA,WAAU,QAAQ,CAAC,aAAa,MAAM,MAAM,SAAS,EAAE,UAAAD,UAAQ,CAAE,CAAC,CAAC;AACvE,MAAI;AACA,WAAO,eAAgB,OAAoC,WAAW,WAAW;MAC7E,OAAO,OAAO,OAAO,EAAE,KAAI,CAAE;KAChC;EACL,SAAS,OAAO;AACZ,YAAQ,MAAM,2CAA2C;AACzD,WAAO;EACX;AAEA,OAAK,GAAG,SAAS;AACjB,SAAO;AACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADhNM,IAAO,mCAAP,MAAO,0CAAyC,sBAAqB;EAIvE,YAAY,EACR,SACA,SACA,WACA,OAAM,GAMT;AACG,UAAM,WAGwB,CAAC,4BAA4B;AAC3D,QAAI,qBAAqB,SAAS;AAC9B,eAAS,KAAK,qBAAqB;IACvC;AACA,QAAI,iBAAiB,SAAS;AAC1B,eAAS,KAAK,iBAAiB;IACnC;AACA,QAAI,YAAY,SAAS;AACrB,eAAS,KAAK,YAAY;IAC9B;AAEA,UAAM,EAAE,SAAS,WAAW,QAAQ,SAAQ,CAAE;AA3BzC,8CAAA,IAAA,MAAA,MAAA;AA4BL,QAAI,eAAe,mCAAkC;AACjD,aAAO,OAAO,IAAI;IACtB;AAEA,IAAAE,wBAAA,MAAI,2CAAY,SAAO,GAAA;EAC3B;;;;;AA+HI,QAAM,aAAY,KAAAC,wBAAA,MAAI,oCAAA,GAAA,EAAU,cAAd,mBAAyB;AAC3C,MAAI,WAAW;AACX,UAAM,UAAUA,wBAAA,MAAI,oCAAA,GAAA,EAAU,UAAW,SAAQ;AACjD,UAAM,UAAUA,wBAAA,MAAI,oCAAA,GAAA;AACpB,QACI,CAAC,WACD,QAAQ,YAAY,WACpB,QAAQ,OAAO,SAASA,wBAAA,MAAI,kCAAA,GAAA,CAAO,KACnC,CAAC,WAAW,QAAQ,WAAW,SAAS,GAC1C;AACE,MAAAC,wBAAA,MAAI,oCAAY,IAAI,iCAAiC;QACjD,SAASD,wBAAA,MAAI,oCAAA,GAAA;QACb;QACA;QACA,QAAQ,CAACA,wBAAA,MAAI,kCAAA,GAAA,CAAO;OACvB,GAAC,GAAA;AACF,MAAAA,wBAAA,MAAI,sCAAA,KAAA,+BAAA,EAAM,KAAV,MAAW,UAAU,EAAE,UAAU,KAAK,SAAQ,CAAE;IACpD;EACJ;AACJ,GAAC,0CAAA,SAAAE,2CAAA;AAGG,MAAIF,wBAAA,MAAI,oCAAA,GAAA,GAAW;AACf,IAAAC,wBAAA,MAAI,oCAAY,QAAS,GAAA;AACzB,IAAAD,wBAAA,MAAI,sCAAA,KAAA,+BAAA,EAAM,KAAV,MAAW,UAAU,EAAE,UAAU,KAAK,SAAQ,CAAE;EACpD;AACJ,GAAC,kCAAA,SAAAG,iCAqBoC,UAAa,MAA4C;;AAE1F,QAAAH,wBAAA,MAAI,sCAAA,GAAA,EAAY,KAAK,MAArB,mBAAwB,QAAQ,CAAC,aAAa,SAAS,MAAM,MAAM,IAAI;AAC3E,GAAC,iCAAA,SAAAI,gCAEmC,OAAU,UAAoC;;AAC9E,EAAAJ,wBAAA,MAAI,sCAAA,GAAA,EAAY,KAAK,KAAI,KAAAA,wBAAA,MAAI,sCAAA,GAAA,EAAY,KAAK,MAArB,mBAAwB,OAAO,CAAC,qBAAqB,aAAa;AAC/F,GAAC,oDAAA,SAAAK,mDAEuB,uBAAiC;AACrD,QAAM,cAAc,qBAAqB,YAAY,qBAAqB;AAC1E,MAAI,CAACL,wBAAA,MAAI,yDAAA,GAAA,EAA+B,SAAS,YAAY,OAAO;AAChE,UAAM,IAAI,MAAM,iCAAiC;AACrD,MAAI,YAAY,YAAY,YAAY,YAAYA,wBAAA,MAAI,yDAAA,GAAA,GAAgC,CAAC,QAAQ,CAAC;AAC9F,WAAO,YAAY,KAAK,qBAAqB;AACjD,SAAO;AACX;;;AEvQJ,IAAAM,gBAAqD;AAE/C,SAAU,0BAA0B,UAAmB;AACzD,QAAM,WAAW,YAAY,MAAM,oBAAI,IAAG,CAAc;AACxD,QAAM,EAAE,KAAAC,MAAK,IAAAC,IAAE,IAAK,YAAY,MAAM,sBAAqB,CAAE;AAC7D,QAAM,CAAC,kBAAkB,mBAAmB,QAAI,wBAAS,MAAM,wBAAwBD,KAAG,CAAE,CAAC;AAE7F,+BAAU,MAAK;AACX,UAAME,aAAY;MACdD,IAAG,YAAY,IAAIE,aACf,oBAAoB,CAACC,sBAAqB,CAAC,GAAGA,mBAAkB,GAAG,wBAAwBD,QAAO,CAAC,CAAC,CAAC;MAEzGF,IAAG,cAAc,IAAIE,aACjB,oBAAoB,CAACC,sBACjBA,kBAAiB,OAAO,CAAC,oBACrBD,SAAQ,KAAK,CAAC,WAAW,WAAW,gBAAgB,MAAM,CAAC,CAC9D,CACJ;;AAGT,WAAO,MAAMD,WAAU,QAAQ,CAAC,QAAQ,IAAG,CAAE;EACjD,GAAG,CAACD,GAAE,CAAC;AAEP,QAAM,uBAAuB,YAAY,gBAAgB;AACzD,+BAAU,MAAK;AACX,QAAI,CAAC;AAAsB;AAE3B,UAAM,kBAAkB,IAAI,IAAI,gBAAgB;AAChD,UAAM,kBAAkB,IAAI,IACxB,qBAAqB,OAAO,CAAC,oBAAoB,CAAC,gBAAgB,IAAI,eAAe,CAAC,CAAC;AAE3F,oBAAgB,QAAQ,CAAC,YAAY,QAAQ,QAAO,CAAE;EAC1D,GAAG,CAAC,sBAAsB,gBAAgB,CAAC;AAG3C,+BAAU,MAAM,MAAM,iBAAiB,QAAQ,CAAC,YAAY,QAAQ,QAAO,CAAE,GAAG,CAAA,CAAE;AAElF,aAAO,uBACH,MAAM;IACF,GAAG;IACH,GAAG,SAAS,OAAO,CAAC,EAAE,KAAI,MAAM;AAC5B,UAAI,iBAAiB,KAAK,CAAC,oBAAoB,gBAAgB,SAAS,IAAI,GAAG;AAC3E,YAAI,CAAC,SAAS,IAAI,IAAI,GAAG;AACrB,mBAAS,IAAI,IAAI;AACjB,kBAAQ,KACJ,GAAG,IAAI,gEAAgE,IAAI,gCAAgC;QAEnH;AACA,eAAO;MACX;AACA,aAAO;IACX,CAAC;KAEL,CAAC,kBAAkB,UAAU,QAAQ,CAAC;AAE9C;AAEA,SAAS,YAAe,IAAW;AAC/B,QAAM,UAAM,sBAAqB,MAAS;AAC1C,MAAI,IAAI,YAAY,QAAW;AAC3B,QAAI,UAAU,EAAE,OAAO,GAAE,EAAE;EAC/B;AACA,SAAO,IAAI,QAAQ;AACvB;AAEA,SAAS,YAAe,OAAQ;AAC5B,QAAM,UAAM,sBAAU,MAAS;AAC/B,+BAAU,MAAK;AACX,QAAI,UAAU;EAClB,CAAC;AACD,SAAO,IAAI;AACf;AAEA,SAAS,wBAAwBE,UAA0B;AACvD,SAAOA,SAAQ,OAAO,+BAA+B,EAAE,IAAI,CAAC,WAAW,IAAI,sBAAsB,EAAE,OAAM,CAAE,CAAC;AAChH;;;ACtEA,IAAAE,gBAA+E;;;ACN/E,IAAY;CAAZ,SAAYC,cAAW;AACnB,EAAAA,aAAAA,aAAA,aAAA,IAAA,CAAA,IAAA;AACA,EAAAA,aAAAA,aAAA,YAAA,IAAA,CAAA,IAAA;AACJ,GAHY,gBAAA,cAAW,CAAA,EAAA;AAUvB,SAAS,UAAU,iBAAuB;AACtC,SAAO,0GAA0G,KAC7G,eAAe;AAEvB;AAEc,SAAP,eAAgC,EAAE,UAAU,gBAAe,GAAU;AACxE,MACI,SAAS,KACL,CAAC,YACG,QAAQ,SAASC,wCACjB,QAAQ,eAAe,iBAAiB,SAAS,GAE3D;AASE,WAAO,YAAY;EACvB;AACA,MACI;EAEA,WAAW,KAAK,eAAe;EAE/B,CAAC,UAAU,eAAe,GAC5B;AACE,WAAO,YAAY;EACvB,OAAO;AACH,WAAO,YAAY;EACvB;AACJ;;;AC9Cc,SAAP,+BAAgD,UAAiB;AACpE,MAAI,CAAC,UAAU;AACX,WAAO;EACX;AACA,MAAI,UAAU,KAAK,QAAQ,GAAG;AAC1B,WAAO;EACX,WAAW,WAAW,KAAK,QAAQ,GAAG;AAClC,WAAO;EACX,OAAO;AACH,WAAO;EACX;AACJ;;;ACAA,IAAAC,gBAAyF;AAgBnF,SAAU,mBAAmB,EAC/B,UACA,SAAS,UACT,SACA,gBACA,sBACA,gBACA,SACA,eAAc,GACQ;AACtB,QAAM,sBAAkB,sBAAO,KAAK;AACpC,QAAM,CAAC,YAAY,aAAa,QAAI,wBAAS,KAAK;AAClD,QAAM,yBAAqB,sBAAO,KAAK;AACvC,QAAM,CAAC,eAAe,gBAAgB,QAAI,wBAAS,KAAK;AACxD,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,OAAM,mCAAS,cAAa,IAAI;AAC3E,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,OAAM,mCAAS,cAAa,KAAK;AAM5E,QAAM,iBAAa,sBAAO,OAAO;AACjC,+BAAU,MAAK;AACX,eAAW,UAAU;AACrB,WAAO,MAAK;AACR,iBAAW,UAAU;IACzB;EACJ,GAAG,CAAC,OAAO,CAAC;AACZ,QAAM,qBAAiB,sBAAO,CAAC,OAAoBC,aAAqB;AACpE,QAAI,CAAC,eAAe,SAAS;AACzB,UAAI,WAAW,SAAS;AACpB,mBAAW,QAAQ,OAAOA,QAAO;MACrC,OAAO;AACH,gBAAQ,MAAM,OAAOA,QAAO;AAC5B,YAAI,iBAAiB,uBAAuB,OAAO,WAAW,eAAeA,UAAS;AAClF,iBAAO,KAAKA,SAAQ,KAAK,QAAQ;QACrC;MACJ;IACJ;AACA,WAAO;EACX,CAAC;AAGD,QAAM,CAACC,UAAS,UAAU,QAAI,wBAAS,MACnC,SACK,IAAI,CAACD,cAAa;IACf,SAAAA;IACA,YAAYA,SAAQ;IACtB,EACD,OAAO,CAAC,EAAE,WAAU,MAAO,eAAe,iBAAiB,WAAW,CAAC;AAIhF,+BAAU,MAAK;AAEX,eAAW,CAACC,aACR,SACK,IAAI,CAACD,UAAS,UAAS;AACpB,YAAME,UAASD,SAAQ,KAAK;AAE5B,aAAOC,WAAUA,QAAO,YAAYF,YAAWE,QAAO,eAAeF,SAAQ,aACvEE,UACA;QACI,SAASF;QACT,YAAYA,SAAQ;;IAElC,CAAC,EACA,OAAO,CAAC,EAAE,WAAU,MAAO,eAAe,iBAAiB,WAAW,CAAC;AAEhF,aAAS,uBAAsC,YAA4B;AACvE,iBAAW,CAAC,gBAAe;AACvB,cAAM,QAAQ,YAAY,UAAU,CAAC,EAAE,SAAAA,SAAO,MAAOA,aAAY,IAAI;AACrE,YAAI,UAAU;AAAI,iBAAO;AAGzB,cAAM,EAAE,SAAAA,SAAO,IAAK,YAAY,KAAK;AACrC,eAAO;UACH,GAAG,YAAY,MAAM,GAAG,KAAK;UAC7B,EAAE,SAAAA,UAAS,WAAU;UACrB,GAAG,YAAY,MAAM,QAAQ,CAAC;UAChC,OAAO,CAAC,EAAE,YAAAG,YAAU,MAAOA,gBAAe,iBAAiB,WAAW;MAC5E,CAAC;IACL;AACA,aAAS,QAAQ,CAACH,aAAYA,SAAQ,GAAG,oBAAoB,wBAAwBA,QAAO,CAAC;AAC7F,WAAO,MAAK;AACR,eAAS,QAAQ,CAACA,aAAYA,SAAQ,IAAI,oBAAoB,wBAAwBA,QAAO,CAAC;IAClG;EACJ,GAAG,CAAC,SAAS,QAAQ,CAAC;AAEtB,QAAM,aAAS,uBAAQ,MAAMC,SAAQ,KAAK,CAACC,YAAWA,QAAO,YAAY,OAAO,KAAK,MAAM,CAAC,SAASD,QAAO,CAAC;AAG7G,+BAAU,MAAK;AACX,QAAI,CAAC;AAAS;AAEd,UAAMG,iBAAgB,CAACC,eAAwB;AAC3C,mBAAaA,UAAS;AACtB,sBAAgB,UAAU;AAC1B,oBAAc,KAAK;AACnB,mBAAa,IAAI;AACjB,yBAAmB,UAAU;AAC7B,uBAAiB,KAAK;IAC1B;AAEA,UAAMC,oBAAmB,MAAK;AAC1B,UAAI,eAAe;AAAS;AAE5B,mBAAa,IAAI;AACjB,sBAAgB,UAAU;AAC1B,oBAAc,KAAK;AACnB,mBAAa,KAAK;AAClB,yBAAmB,UAAU;AAC7B,uBAAiB,KAAK;IAC1B;AAEA,UAAM,cAAc,CAAC,UAAsB;AACvC,qBAAe,QAAQ,OAAO,OAAO;IACzC;AAEA,YAAQ,GAAG,WAAWF,cAAa;AACnC,YAAQ,GAAG,cAAcE,iBAAgB;AACzC,YAAQ,GAAG,SAAS,WAAW;AAE/B,WAAO,MAAK;AACR,cAAQ,IAAI,WAAWF,cAAa;AACpC,cAAQ,IAAI,cAAcE,iBAAgB;AAC1C,cAAQ,IAAI,SAAS,WAAW;AAEhC,MAAAA,kBAAgB;IACpB;EACJ,GAAG,CAAC,SAAS,cAAc,CAAC;AAG5B,QAAM,+BAA2B,sBAAO,KAAK;AAC7C,+BAAU,MAAK;AACX,WAAO,MAAK;AACR,+BAAyB,UAAU;IACvC;EACJ,GAAG,CAAC,OAAO,CAAC;AAGZ,+BAAU,MAAK;AACX,QACI,yBAAyB,WACzB,gBAAgB,WAChB,aACA,CAAC,wBACD,GAAE,iCAAQ,gBAAe,iBAAiB,cAAa,iCAAQ,gBAAe,iBAAiB;AAE/F;AAEJ,oBAAgB,UAAU;AAC1B,kBAAc,IAAI;AAClB,6BAAyB,UAAU;AACnC,KAAC,iBAAK;AACF,UAAI;AACA,cAAM,qBAAoB;MAC9B,QAAQ;AACJ,uBAAc;MAElB;AACI,sBAAc,KAAK;AACnB,wBAAgB,UAAU;MAC9B;IACJ,GAAE;EACN,GAAG,CAAC,WAAW,sBAAsB,gBAAgB,MAAM,CAAC;AAG5D,QAAM,sBAAyD,2BAC3D,OAAO,aAAa,YAAY,YAAW;AACvC,QAAI,CAAC;AAAS,YAAM,eAAe,QAAQ,IAAI,uBAAsB,CAAE;AACvE,QAAI,CAAC;AAAW,YAAM,eAAe,QAAQ,IAAI,wBAAuB,GAAI,OAAO;AACnF,WAAO,MAAM,QAAQ,gBAAgB,aAAa,YAAY,OAAO;EACzE,GACA,CAAC,SAAS,SAAS,CAAC;AAIxB,QAAM,sBAA2E,uBAC7E,MACI,WAAW,qBAAqB,UAC1B,OAAO,gBAAe;AAClB,QAAI,CAAC;AAAW,YAAM,eAAe,QAAQ,IAAI,wBAAuB,GAAI,OAAO;AACnF,WAAO,MAAM,QAAQ,gBAAgB,WAAW;EACpD,IACA,QACV,CAAC,SAAS,SAAS,CAAC;AAIxB,QAAM,0BAAmF,uBACrF,MACI,WAAW,yBAAyB,UAC9B,OAAO,iBAAgB;AACnB,QAAI,CAAC;AAAW,YAAM,eAAe,QAAQ,IAAI,wBAAuB,GAAI,OAAO;AACnF,WAAO,MAAM,QAAQ,oBAAoB,YAAY;EACzD,IACA,QACV,CAAC,SAAS,SAAS,CAAC;AAIxB,QAAM,kBAA0E,uBAC5E,MACI,WAAW,iBAAiB,UACtB,OAAO,YAAW;AACd,QAAI,CAAC;AAAW,YAAM,eAAe,QAAQ,IAAI,wBAAuB,GAAI,OAAO;AACnF,WAAO,MAAM,QAAQ,YAAY,OAAO;EAC5C,IACA,QACV,CAAC,SAAS,SAAS,CAAC;AAIxB,QAAM,aAAsE,uBACxE,MACI,WAAW,YAAY,UACjB,OAAO,UAAS;AACZ,WAAO,MAAM,QAAQ,OAAO,KAAK;EACrC,IACA,QACV,CAAC,OAAO,CAAC;AAGb,QAAM,oBAAgB,2BAAY,YAAW;AACzC,QAAI,gBAAgB,WAAW,mBAAmB,YAAW,iCAAQ,QAAQ;AAAW;AACxF,QAAI,CAAC;AAAQ,YAAM,eAAe,QAAQ,IAAI,uBAAsB,CAAE;AACtE,UAAM,EAAE,SAAAN,UAAS,WAAU,IAAK;AAChC,QAAI,EAAE,eAAe,iBAAiB,aAAa,eAAe,iBAAiB;AAC/E,YAAM,eAAe,QAAQ,IAAI,oBAAmB,GAAIA,QAAO;AACnE,oBAAgB,UAAU;AAC1B,kBAAc,IAAI;AAClB,QAAI;AACA,YAAMA,SAAQ,QAAO;IACzB,SAAS,GAAG;AACR,qBAAc;AACd,YAAM;IACV;AACI,oBAAc,KAAK;AACnB,sBAAgB,UAAU;IAC9B;EACJ,GAAG,CAAC,gBAAgB,MAAM,CAAC;AAE3B,QAAM,uBAAmB,2BAAY,YAAW;AAC5C,QAAI,mBAAmB;AAAS;AAChC,QAAI,CAAC;AAAS;AACd,uBAAmB,UAAU;AAC7B,qBAAiB,IAAI;AACrB,QAAI;AACA,YAAM,QAAQ,WAAU;IAC5B;AACI,uBAAiB,KAAK;AACtB,yBAAmB,UAAU;IACjC;EACJ,GAAG,CAAC,OAAO,CAAC;AAEZ,SACI,cAAAO,QAAA,cAAC,cAAc,UAAQ,EACnB,OAAO;IACH,aAAa,CAAC,CAAC;IACf,SAAAN;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ;IACR,SAAS;IACT,YAAY;IACZ;IACA;IACA;IACA;IACA;IACH,GAEA,QAAQ;AAGrB;;;AH5RA,IAAI;AACJ,SAAS,eAAY;AAzBrB;AA0BI,MAAI,eAAe,QAAW;AAC1B,mBAAa,gBAAW,cAAX,mBAAsB,cAAa;EACpD;AACA,SAAO;AACX;AAEA,SAAS,YAAY,UAAmB;AACpC,QAAM,kBAAkB,aAAY;AACpC,SAAO,eAAe,EAAE,UAAU,gBAAe,CAAE,MAAM,YAAY;AACzE;AAEA,SAAS,uBAAoB;AACzB,QAAM,WAAW,WAAW;AAC5B,MAAI,CAAC;AAAU;AACf,SAAO,GAAG,SAAS,QAAQ,KAAK,SAAS,IAAI;AACjD;AAEM,SAAU,eAAe,EAC3B,UACA,SAAS,UACT,aACA,kBAAkB,cAClB,QAAO,GACW;AAClB,QAAM,EAAE,WAAU,IAAK,cAAa;AACpC,QAAM,+BAA+B,0BAA0B,QAAQ;AACvE,QAAM,0BAAsB,uBAAQ,MAAK;AACrC,QAAI,CAAC,YAAY,4BAA4B,GAAG;AAC5C,aAAO;IACX;AACA,UAAM,8BAA8B,6BAA6B,KAC7D,CAACO,aAAYA,SAAQ,SAASC,oCAAmC;AAErE,QAAI,6BAA6B;AAC7B,aAAO;IACX;AACA,WAAO,IAAI,0BAA0B;MACjC,iBAAiB,6BAA4B;MAC7C,aAAa;QACT,KAAK,qBAAoB;;MAE7B,0BAA0B,sCAAqC;MAC/D,SAAS,+BAA+B,yCAAY,WAAW;MAC/D,kBAAkB,mCAAkC;KACvD;EACL,GAAG,CAAC,8BAA8B,yCAAY,WAAW,CAAC;AAC1D,QAAM,sCAAkC,uBAAQ,MAAK;AACjD,QAAI,uBAAuB,QAAQ,6BAA6B,QAAQ,mBAAmB,MAAM,IAAI;AACjG,aAAO;IACX;AACA,WAAO,CAAC,qBAAqB,GAAG,4BAA4B;EAChE,GAAG,CAAC,8BAA8B,mBAAmB,CAAC;AACtD,QAAM,CAAC,YAAY,aAAa,IAAI,gBAAmC,iBAAiB,IAAI;AAC5F,QAAM,cAAU,uBACZ,MAAM,gCAAgC,KAAK,CAAC,MAAM,EAAE,SAAS,UAAU,KAAK,MAC5E,CAAC,iCAAiC,UAAU,CAAC;AAEjD,QAAM,mBAAe,2BACjB,CAAC,mBAA6C;AAC1C,QAAI,eAAe;AAAgB;AACnC,QACI;;;;IAKA,QAAQ,SAASA,sCACnB;AACE,cAAQ,WAAU;IACtB;AACA,kBAAc,cAAc;EAChC,GACA,CAAC,SAAS,eAAe,UAAU,CAAC;AAExC,+BAAU,MAAK;AACX,QAAI,CAAC;AAAS;AACd,aAAS,mBAAgB;AACrB,UAAI,eAAe;AAAS;AAC5B,oBAAc,IAAI;IACtB;AACA,YAAQ,GAAG,cAAc,gBAAgB;AACzC,WAAO,MAAK;AACR,cAAQ,IAAI,cAAc,gBAAgB;IAC9C;EACJ,GAAG,CAAC,SAAS,8BAA8B,eAAe,UAAU,CAAC;AACrE,QAAM,6BAAyB,sBAAO,KAAK;AAC3C,QAAM,+BAA2B,uBAAQ,MAAK;AAC1C,QAAI,CAAC,eAAe,CAAC;AAAS;AAC9B,WAAO,YAAW;AAEd,UAAI,gBAAgB,QAAS,MAAM,YAAY,OAAO,GAAI;AACtD,YAAI,uBAAuB,SAAS;AAChC,gBAAM,QAAQ,QAAO;QACzB,OAAO;AACH,gBAAM,QAAQ,YAAW;QAC7B;MACJ;IACJ;EACJ,GAAG,CAAC,aAAa,OAAO,CAAC;AACzB,QAAM,qBAAiB,sBAAO,KAAK;AACnC,+BAAU,MAAK;AACX,QAAI,eAAeA,wCAAuC,YAAY,4BAA4B,GAAG;AACjG,qBAAe,UAAU;AACzB;IACJ;AACA,aAAS,qBAAkB;AACvB,qBAAe,UAAU;IAC7B;AAQA,WAAO,iBAAiB,gBAAgB,kBAAkB;AAC1D,WAAO,MAAK;AACR,aAAO,oBAAoB,gBAAgB,kBAAkB;IACjE;EACJ,GAAG,CAAC,8BAA8B,UAAU,CAAC;AAC7C,QAAM,yBAAqB,2BAAY,MAAK;AACxC,QAAI,SAAS;AAET,mBAAa,IAAI;IACrB;EACJ,GAAG,CAAC,SAAS,YAAY,CAAC;AAC1B,QAAM,mBAAe,2BACjB,CAACC,gBAAiC;AAC9B,2BAAuB,UAAU;AACjC,iBAAaA,WAAU;EAC3B,GACA,CAAC,YAAY,CAAC;AAElB,SACI,cAAAC,QAAA,cAAC,oBAAkB,EACf,SAAS,iCACT,SACA,gBACA,sBAAsB,0BACtB,gBAAgB,oBAChB,SACA,gBAAgB,aAAY,GAE3B,QAAQ;AAGrB;", "names": ["module", "encode", "require_utils", "QRCode", "encode", "i", "j", "require_src", "encode", "i", "j", "require_bs58", "import_react", "React", "import_react", "import_react", "import_react", "value", "associationKeypair", "resolve", "ecdhKeypair", "reject", "__awaiter", "transact", "__awaiter", "bs58", "import_bs58", "__awaiter", "_EmbeddedModal_injectHTML", "_EmbeddedModal_attachEventListeners", "_EmbeddedModal_removeEventListeners", "isVersionedTransaction", "fromUint8Array", "toUint8Array", "SIGNATURE_LENGTH_IN_BYTES", "__awaiter", "config", "transact", "base58", "_LocalSolanaMobileWalletAdapterWallet_emit", "_LocalSolanaMobileWalletAdapterWallet_off", "_RemoteSolanaMobileWalletAdapterWallet_emit", "_RemoteSolanaMobileWalletAdapterWallet_off", "__awaiter", "__awaiter", "__awaiter", "StandardConnect", "StandardDisconnect", "StandardEvents", "fromUint8Array", "SolanaMobileWalletAdapterWalletName", "SIGNATURE_LENGTH_IN_BYTES", "isVersionedTransaction", "__awaiter", "StandardEvents", "StandardConnect", "StandardDisconnect", "_BaseSolanaMobileWalletAdapter_connect", "_BaseSolanaMobileWalletAdapter_declareWalletAsInstalled", "_BaseSolanaMobileWalletAdapter_assertIsAuthorized", "_BaseSolanaMobileWalletAdapter_performSignTransactions", "_BaseSolanaMobileWalletAdapter_runWithGuard", "__awaiter", "__classPrivateFieldGet", "__classPrivateFieldSet", "_StandardWalletAdapter_connect", "_StandardWalletAdapter_connected", "_StandardWalletAdapter_disconnected", "_StandardWalletAdapter_reset", "_StandardWalletAdapter_signTransaction", "_StandardWalletAdapter_signAllTransactions", "_StandardWalletAdapter_signMessage", "_StandardWalletAdapter_signIn", "wallets", "_a", "__classPrivateFieldGet", "__classPrivateFieldSet", "register", "callbacks", "__classPrivateFieldSet", "__classPrivateFieldGet", "__classPrivateFieldSet", "_SolanaWalletAdapterWallet_disconnected", "_SolanaWalletAdapterWallet_emit", "_SolanaWalletAdapterWallet_off", "_SolanaWalletAdapterWallet_deserializeTransaction", "import_react", "get", "on", "listeners", "wallets", "standardAdapters", "import_react", "Environment", "SolanaMobileWalletAdapterWalletName", "import_react", "adapter", "wallets", "wallet", "readyState", "handleConnect", "public<PERSON>ey", "handleDisconnect", "React", "adapter", "SolanaMobileWalletAdapterWalletName", "walletName", "React"]}