# @walletconnect/sign-client

Sign Client for WalletConnect Protocol

## Description

This library provides a Sign Client for WalletConnect v2.0 Protocol for both Dapps and Wallets. Integration will differ from the perspective of each client as the Proposer and Responder, respectively. It's compatible with NodeJS, Browser and React-Native applications (NodeJS modules required to be polyfilled for React-Native)

## Documentation

Check out documentation [here](https://docs.walletconnect.com/).

Also available quick start for [Dapps](https://docs.reown.com/api/sign/dapp-usage) and for [Wallets](https://docs.reown.com/api/sign/wallet-usage)

## License

Apache 2.0
