const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');

// Log environment variables
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('Current directory:', __dirname);

// Check if .env file exists
const envPath = path.resolve(__dirname, '../.env');
console.log('.env file exists:', fs.existsSync(envPath));

// Load environment variables
require('dotenv').config({ path: envPath });

// Log MongoDB URI
console.log('MongoDB URI:', process.env.MONGODB_URI ? 'Found' : 'Not found');
if (process.env.MONGODB_URI) {
  // Mask the actual URI for security
  const maskedUri = process.env.MONGODB_URI.replace(/mongodb\+srv:\/\/([^:]+):([^@]+)@/, 'mongodb+srv://****:****@');
  console.log('Masked MongoDB URI:', maskedUri);
}

// Try to connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  console.log('MongoDB connected successfully');
  mongoose.connection.close();
})
.catch(err => {
  console.error('MongoDB connection error:', err);
});
