const mongoose = require('mongoose');

const CommentSchema = new mongoose.Schema({
  tokenSymbol: {
    type: String,
    required: true,
    index: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: true,
    trim: true,
    maxlength: 500
  },
  likes: {
    count: {
      type: Number,
      default: 0
    },
    users: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }]
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create compound index for efficient queries
CommentSchema.index({ tokenSymbol: 1, createdAt: -1 });

// Pre-save hook to ensure likes count is always in sync with users array
CommentSchema.pre('save', function(next) {
  // Ensure likes count matches the number of users who liked the comment
  if (this.likes && this.likes.users) {
    this.likes.count = this.likes.users.length;
  }
  next();
});

// Pre-find hook to ensure likes count is always in sync when retrieving comments
CommentSchema.pre('find', function() {
  this.populate('user', 'username profileImage profilePicture');
});

module.exports = mongoose.model('Comment', CommentSchema);
