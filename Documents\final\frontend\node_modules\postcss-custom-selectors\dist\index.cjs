"use strict";var e=require("postcss-selector-parser"),t=require("fs"),s=require("path"),n=require("postcss");function r(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function c(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(s){if("default"!==s){var n=Object.getOwnPropertyDescriptor(e,s);Object.defineProperty(t,s,n.get?n:{enumerable:!0,get:function(){return e[s]}})}})),t.default=e,Object.freeze(t)}var o=r(e),a=r(t),i=r(s),u=r(n),l=(e,t)=>{const s={};return e.nodes.slice().forEach((e=>{var n,r,c;if("atrule"!==e.type||"custom-selector"!==e.name)return;if(!e.params||!e.params.includes(":--"))return;const a=e.params.trim(),i=o.default().astSync(a),u=null==i||null==(n=i.nodes)||null==(r=n[0])||null==(c=r.nodes)?void 0:c[0];if(!u||"pseudo"!==u.type||!u.value.startsWith(":--"))return;const l=u.toString();s[l]=o.default().astSync(a.slice(l.length).trim()),Object(t).preserve||e.remove()})),s};function f(e,t){let s=e.nodes.length-1;for(;s>=0;){const n=p(e.nodes[s],t);n.length&&e.nodes.splice(s,1,...n),--s}return e}function p(e,t){const s=[];for(const n in e.nodes){const{value:r,nodes:c}=e.nodes[n];if(r in t){for(const c of t[r].nodes){const r=e.clone(),o=c.clone().nodes;r.nodes.splice(n,1,...o.map(((t,s)=>("selector"===t.type&&(t.spaces={...e.nodes[s].spaces}),0===s&&t.spaces&&(t.spaces.before=""),s===o.length-1&&t.spaces&&(t.spaces.after=""),t))));const a=p(r,t);b(r.nodes,Number(n)),a.length?s.push(...a):s.push(r)}return s}c&&c.length&&f(e.nodes[n],t)}return s}const d=/^(tag|universal)$/,m=/^(class|id|pseudo|tag|universal)$/,j=e=>m.test(Object(e).type),b=(e,t)=>{if(t&&(s=e[t],d.test(Object(s).type))&&j(e[t-1])){let s=t-1;for(;s&&j(e[s]);)--s;if(s<t){const n=e.splice(t,1)[0];e.splice(s,0,n),e[s].spaces.before=e[s+1].spaces.before,e[s+1].spaces.before="",e[t]&&(e[t].spaces.after=e[s].spaces.after,e[s].spaces.after="")}}var s};function y(e){return l(e)}function O(e){const t=Object.assign({},Object(e).customSelectors||Object(e)["custom-selectors"]);for(const e in t)t[e]=o.default().astSync(t[e]);return t}function g(e){return e.map((e=>{if(e instanceof Promise)return e;if(e instanceof Function)return e();const t=e===Object(e)?e:{from:String(e)};if(Object(t).customSelectors||Object(t)["custom-selectors"])return t;const s=String(t.from||"");return{type:(t.type||i.default.extname(s).slice(1)).toLowerCase(),from:s}})).reduce((async(e,t)=>{const s=await e,{type:n,from:r}=await t;return"ast"===n?Object.assign(s,y(r)):"css"===n?Object.assign(s,await async function(e){const t=await w(i.default.resolve(e));return y(u.default.parse(t,{from:i.default.resolve(e)}))}(r)):"js"===n?Object.assign(s,await async function(e){var t;return O(await(t=i.default.resolve(e),Promise.resolve().then((function(){return c(require(t))}))))}(r)):"json"===n?Object.assign(s,await async function(e){return O(await v(i.default.resolve(e)))}(r)):Object.assign(s,O(await t))}),Promise.resolve({}))}const w=e=>new Promise(((t,s)=>{a.default.readFile(e,"utf8",((e,n)=>{e?s(e):t(n)}))})),v=async e=>JSON.parse(await w(e));function S(e,t){return Promise.all(t.map((async t=>{if(t instanceof Function)await t(h(e));else{const s=t===Object(t)?t:{to:String(t)},n=s.toJSON||h;if("customSelectors"in s)s.customSelectors=n(e);else if("custom-selectors"in s)s["custom-selectors"]=n(e);else{const t=String(s.to||""),r=(s.type||i.default.extname(s.to).slice(1)).toLowerCase(),c=n(e);"css"===r&&await async function(e,t){const s=`${Object.keys(t).reduce(((e,s)=>(e.push(`@custom-selector ${s} ${t[s]};`),e)),[]).join("\n")}\n`;await $(e,s)}(t,c),"js"===r&&await async function(e,t){const s=`module.exports = {\n\tcustomSelectors: {\n${Object.keys(t).reduce(((e,s)=>(e.push(`\t\t'${P(s)}': '${P(t[s])}'`),e)),[]).join(",\n")}\n\t}\n};\n`;await $(e,s)}(t,c),"json"===r&&await async function(e,t){const s=`${JSON.stringify({"custom-selectors":t},null,"\t")}\n`;await $(e,s)}(t,c),"mjs"===r&&await async function(e,t){const s=`export const customSelectors = {\n${Object.keys(t).reduce(((e,s)=>(e.push(`\t'${P(s)}': '${P(t[s])}'`),e)),[]).join(",\n")}\n};\n`;await $(e,s)}(t,c)}}})))}const h=e=>Object.keys(e).reduce(((t,s)=>(t[s]=String(e[s]),t)),{}),$=(e,t)=>new Promise(((s,n)=>{a.default.writeFile(e,t,(e=>{e?n(e):s()}))})),P=e=>e.replace(/\\([\s\S])|(')/g,"\\$1$2").replace(/\n/g,"\\n").replace(/\r/g,"\\r"),x=e=>{const t=Boolean(Object(e).preserve),s=[].concat(Object(e).importFrom||[]),n=[].concat(Object(e).exportTo||[]),r=g(s),c=Symbol("customSelectorHelper");return{postcssPlugin:"postcss-custom-selectors",Once:async(e,s)=>{s[c]=Object.assign(await r,l(e,{preserve:t})),await S(s[c],n)},Rule:(e,s)=>{e.selector.includes(":--")&&((e,t,s)=>{const n=o.default((e=>{f(e,t)})).processSync(e.selector);n!==e.selector&&(e.cloneBefore({selector:n}),s.preserve||e.remove())})(e,s[c],{preserve:t})}}};x.postcss=!0,module.exports=x;
