const express = require('express');
const router = express.Router();
const bondingCurveService = require('../services/bondingCurveService');
const { authenticateToken } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');
const TokenModel = require('../models/Token');
const logger = require('../utils/logger');

// Create a new token with bonding curve
router.post(
  '/create',
  authenticateToken,
  validateRequest({
    body: {
      name: { type: 'string', required: true },
      symbol: { type: 'string', required: true },
      decimals: { type: 'number', required: false, default: 9 },
      initialSupply: { type: 'number', required: true },
      bondingCurve: {
        type: 'object',
        required: true,
        properties: {
          curveType: { type: 'string', required: true, enum: ['linear', 'exponential', 'logarithmic', 'bancor'] },
          basePrice: { type: 'number', required: true },
          slope: { type: 'number', required: true },
          reserveRatio: { type: 'number', required: false },
          maxSupply: { type: 'number', required: false, default: 0 }
        }
      }
    }
  }),
  async (req, res) => {
    try {
      const { name, symbol, decimals = 9, initialSupply, bondingCurve } = req.body;

      // Validate bonding curve parameters
      if (bondingCurve.curveType === 'bancor' && !bondingCurve.reserveRatio) {
        return res.status(400).json({
          success: false,
          error: 'Reserve ratio is required for Bancor curve'
        });
      }

      // Create token with bonding curve
      const tokenData = await bondingCurveService.createToken(
        { name, symbol, decimals, initialSupply },
        {
          curveType: bondingCurve.curveType,
          basePrice: bondingCurve.basePrice,
          slope: bondingCurve.slope,
          reserveRatio: bondingCurve.reserveRatio || 0.2, // Default reserve ratio
          maxSupply: bondingCurve.maxSupply || 0 // 0 means unlimited
        }
      );

      // Save token to database
      const newToken = new TokenModel({
        name,
        symbol,
        decimals,
        totalSupply: initialSupply,
        currentSupply: initialSupply,
        mintAddress: tokenData.mint,
        creator: req.user.id,
        bondingCurve: {
          address: tokenData.bondingCurve,
          curveType: bondingCurve.curveType,
          basePrice: bondingCurve.basePrice,
          slope: bondingCurve.slope,
          reserveRatio: bondingCurve.reserveRatio || 0.2,
          maxSupply: bondingCurve.maxSupply || 0
        },
        createdAt: new Date()
      });

      await newToken.save();

      res.json({
        success: true,
        token: newToken
      });
    } catch (error) {
      logger.error('Error creating token with bonding curve:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create token'
      });
    }
  }
);

// Get token price
router.get(
  '/:mintAddress/price',
  async (req, res) => {
    try {
      const { mintAddress } = req.params;

      // Get token from database
      const token = await TokenModel.findOne({ mintAddress });

      if (!token) {
        return res.status(404).json({
          success: false,
          error: 'Token not found'
        });
      }

      if (!token.bondingCurve || !token.bondingCurve.address) {
        return res.status(400).json({
          success: false,
          error: 'Token does not use a bonding curve'
        });
      }

      // Get current price
      const price = await bondingCurveService.getTokenPrice(token.bondingCurve.address);

      res.json({
        success: true,
        price,
        symbol: token.symbol,
        currentSupply: token.currentSupply,
        timestamp: Math.floor(Date.now() / 1000)
      });
    } catch (error) {
      logger.error('Error getting token price:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get token price'
      });
    }
  }
);

// Calculate tokens to buy
router.get(
  '/:mintAddress/calculate-buy',
  validateRequest({
    query: {
      amount: { type: 'number', required: true }
    }
  }),
  async (req, res) => {
    try {
      const { mintAddress } = req.params;
      const { amount } = req.query;

      // Get token from database
      const token = await TokenModel.findOne({ mintAddress });

      if (!token) {
        return res.status(404).json({
          success: false,
          error: 'Token not found'
        });
      }

      if (!token.bondingCurve || !token.bondingCurve.address) {
        return res.status(400).json({
          success: false,
          error: 'Token does not use a bonding curve'
        });
      }

      // Calculate tokens to buy
      const tokens = await bondingCurveService.calculateTokensToBuy(
        token.bondingCurve.address,
        parseFloat(amount)
      );

      res.json({
        success: true,
        paymentAmount: parseFloat(amount),
        tokenAmount: tokens,
        symbol: token.symbol
      });
    } catch (error) {
      logger.error('Error calculating tokens to buy:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to calculate tokens to buy'
      });
    }
  }
);

// Calculate payment for selling tokens
router.get(
  '/:mintAddress/calculate-sell',
  validateRequest({
    query: {
      amount: { type: 'number', required: true }
    }
  }),
  async (req, res) => {
    try {
      const { mintAddress } = req.params;
      const { amount } = req.query;

      // Get token from database
      const token = await TokenModel.findOne({ mintAddress });

      if (!token) {
        return res.status(404).json({
          success: false,
          error: 'Token not found'
        });
      }

      if (!token.bondingCurve || !token.bondingCurve.address) {
        return res.status(400).json({
          success: false,
          error: 'Token does not use a bonding curve'
        });
      }

      // Calculate payment for selling tokens
      const payment = await bondingCurveService.calculatePaymentForSelling(
        token.bondingCurve.address,
        parseFloat(amount)
      );

      res.json({
        success: true,
        tokenAmount: parseFloat(amount),
        paymentAmount: payment,
        symbol: token.symbol
      });
    } catch (error) {
      logger.error('Error calculating payment for selling tokens:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to calculate payment for selling tokens'
      });
    }
  }
);

// Get bonding curve info
router.get(
  '/:mintAddress/curve-info',
  async (req, res) => {
    try {
      const { mintAddress } = req.params;

      // Get token from database
      const token = await TokenModel.findOne({ mintAddress });

      if (!token) {
        return res.status(404).json({
          success: false,
          error: 'Token not found'
        });
      }

      if (!token.bondingCurve || !token.bondingCurve.address) {
        return res.status(400).json({
          success: false,
          error: 'Token does not use a bonding curve'
        });
      }

      // Get current price
      const currentPrice = await bondingCurveService.getTokenPrice(token.bondingCurve.address);

      // Try to get price points from C++ engine first
      let pricePoints = [];
      try {
        const { orderMatchingService } = require('../services/orderMatchingServiceTCP');
        pricePoints = await orderMatchingService.getPricePoints(token.bondingCurve.address, 20);
        logger.info(`Got ${pricePoints.length} price points from C++ engine`);
      } catch (engineError) {
        logger.warn(`Failed to get price points from C++ engine, using fallback: ${engineError.message}`);

        // Generate price points for chart using the fallback method
        const maxSupply = token.bondingCurve.maxSupply || token.currentSupply * 2;
        const step = maxSupply / 20;

        for (let i = 0; i <= 20; i++) {
          const supply = i * step;
          // This is a simplified calculation - in a real implementation,
          // you would calculate the actual price based on the bonding curve formula
          let price;

          switch (token.bondingCurve.curveType) {
            case 'linear':
              price = token.bondingCurve.basePrice + (token.bondingCurve.slope * supply);
              break;
            case 'exponential':
              price = token.bondingCurve.basePrice * Math.pow(supply / token.initialSupply, token.bondingCurve.slope);
              break;
            case 'logarithmic':
              price = token.bondingCurve.basePrice * (1 + Math.log(Math.max(1, supply / token.initialSupply)));
              break;
            case 'bancor':
              price = currentPrice * Math.pow(supply / token.currentSupply, 1 / token.bondingCurve.reserveRatio);
              break;
            default:
              price = currentPrice;
          }

          pricePoints.push({
            supply,
            price: Math.max(0.00001, price)
          });
        }
      }

      res.json({
        success: true,
        token: {
          name: token.name,
          symbol: token.symbol,
          currentSupply: token.currentSupply,
          currentPrice
        },
        bondingCurve: {
          address: token.bondingCurve.address,
          curveType: token.bondingCurve.curveType,
          basePrice: token.bondingCurve.basePrice,
          slope: token.bondingCurve.slope,
          reserveRatio: token.bondingCurve.reserveRatio,
          maxSupply: token.bondingCurve.maxSupply
        },
        pricePoints
      });
    } catch (error) {
      logger.error('Error getting bonding curve info:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get bonding curve info'
      });
    }
  }
);

module.exports = router;
