const multer = require('multer');
const path = require('path');
const fs = require('fs');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const User = require('../models/User');
const AWS = require('aws-sdk');
const sharp = require('sharp');
require('dotenv').config();

// Get JWT secret from environment or use default
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Configure AWS S3
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || 'us-east-1'
});

const S3_BUCKET = process.env.AWS_S3_BUCKET || 'swap-profile-pictures';

// Configure multer to use disk storage
const diskStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads');
    // Ensure uploads directory exists
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Use user ID in filename if available
    let userId = 'unknown';
    if (req.user) {
      if (req.user._id) {
        userId = String(req.user._id);
      } else if (req.user.id) {
        userId = String(req.user.id);
      }
    }
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1E9)}`;
    cb(null, `${userId}-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

// Configure S3 storage
const s3Storage = multer.memoryStorage();

// Configure the image filter to only allow images
const imageFilter = (req, file, cb) => {
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

// Create upload middleware
const upload = multer({
  storage: diskStorage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB max size
  fileFilter: imageFilter
});

// Create S3 upload middleware
const uploadS3 = multer({
  storage: s3Storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB max size
  fileFilter: imageFilter
});

// Middleware to handle profile picture uploads with compression and MongoDB storage
const uploadProfilePictureMiddleware = (req, res, next) => {
  console.log("Running profile picture upload middleware with compression and MongoDB storage");

  // Use memory storage for multer to get buffer for compression
  const memoryStorage = multer.memoryStorage();
  const uploadMemory = multer({
    storage: memoryStorage,
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (req, file, cb) => {
      // Accept only image files
      if (!file.mimetype.startsWith('image/')) {
        return cb(new Error('Only image files are allowed'), false);
      }
      cb(null, true);
    }
  });

  // Use memory storage to get buffer for compression
  uploadMemory.single('profilePicture')(req, res, async (err) => {
    if (err) {
      console.error('File upload error:', err);
      return res.status(400).json({ message: err.message || 'Error uploading file' });
    }

    try {
      // User should already be authenticated by the auth middleware
      // and attached to the request object
      if (!req.user || !req.user.id) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      console.log('User authenticated in upload middleware:', req.user.id);

      // Check if file was uploaded
      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      // Compress the image using sharp
      try {
        console.log('Compressing image...');

        // Resize and compress the image
        const compressedImageBuffer = await sharp(req.file.buffer)
          .resize(200, 200, { // Resize to 200x200 pixels for profile picture
            fit: 'cover',
            position: 'center'
          })
          .jpeg({ quality: 80 }) // Convert to JPEG with 80% quality
          .toBuffer();

        console.log('Image compressed successfully');

        // Convert the compressed image to base64 for storing in MongoDB
        const base64Image = `data:${req.file.mimetype};base64,${compressedImageBuffer.toString('base64')}`;

        // Set the file URL to the base64 string
        req.file.url = base64Image;

        // Find the user in the database to ensure we have the full user object
        try {
          // Find the user - handle both ObjectId and string IDs
          let user;

          // Make sure mongoose is available in this scope
          const mongoose = require('mongoose');

          // Check if the ID is a valid ObjectId
          if (mongoose.Types.ObjectId.isValid(req.user.id)) {
            user = await User.findById(req.user.id);
          } else {
            // If not a valid ObjectId, try to find by username or email
            user = await User.findOne({
              $or: [
                { username: req.user.id },
                { email: req.user.id }
              ]
            });
          }

          if (!user) {
            // Create a temporary user object if not found in database
            // This allows the upload to continue for admin users
            if (req.user.id === 'admin123' || req.user.role === 'admin') {
              console.log('Creating temporary admin user object for file upload');
              user = {
                _id: req.user.id,
                username: req.user.username || 'admin',
                email: req.user.email || '<EMAIL>',
                role: 'admin'
              };

              // For admin users, we'll store the profile picture URL even though
              // they don't have a database record
              console.log('Admin user profile picture URL:', req.file.url);
            } else {
              return res.status(401).json({ message: 'User not found' });
            }
          } else {
            // Update the user's profile picture URL in the database
            user.profilePicture = req.file.url;
            try {
              await user.save();
              console.log('Updated user profile picture URL in database:', req.file.url);
            } catch (saveErr) {
              console.error('Error saving profile picture URL:', saveErr);
              // Continue anyway
            }

            // Ensure username exists to avoid validation errors
            if (!user.username) {
              user.username = user.email.split('@')[0];
              try {
                await user.save();
                console.log('Added missing username:', user.username);
              } catch (saveErr) {
                console.error('Error saving username:', saveErr);
                // Continue anyway
              }
            }
          }

          // Attach full user object to request
          req.user = user;
          next();
        } catch (userError) {
          console.error('User lookup failed:', userError);
          return res.status(500).json({ message: 'Server error looking up user' });
        }
      } catch (s3Error) {
        console.error('Error uploading to S3:', s3Error);
        return res.status(500).json({ message: 'Error uploading to cloud storage' });
      }
    } catch (error) {
      console.error('Profile picture middleware error:', error);
      return res.status(500).json({ message: 'Server error in profile picture middleware' });
    }
  });
};

// S3 upload middleware for fallback
const uploadS3Middleware = (req, res, next) => {
  uploadS3.single('profilePicture')(req, res, async function(s3Err) {
            if (s3Err) {
              console.error('S3 upload error:', s3Err);
              return res.status(500).json({ message: `S3 upload error: ${s3Err.message}` });
            }

            if (!req.file) {
              return res.status(400).json({ message: 'No file uploaded' });
            }

            // Upload to S3
            try {
              const userId = String(req.user._id || req.user.id || 'unknown');
              const fileExt = path.extname(req.file.originalname);
              const fileName = `${userId}/${Date.now()}-${Math.round(Math.random() * 1E9)}${fileExt}`;

              const s3Params = {
                Bucket: S3_BUCKET,
                Key: fileName,
                Body: req.file.buffer,
                ACL: 'public-read',
                ContentType: req.file.mimetype,
                Metadata: {
                  userId: String(userId),
                  originalName: req.file.originalname,
                  contentType: req.file.mimetype,
                  uploadDate: new Date().toISOString()
                }
              };

              s3.upload(s3Params, (uploadErr, s3Data) => {
                if (uploadErr) {
                  console.error('Error uploading to S3:', uploadErr);
                  return res.status(500).json({ message: 'Failed to upload to S3' });
                }

                console.log('File uploaded to S3 successfully:', s3Data.Location);
                req.file.url = s3Data.Location;
                next();
              });
            } catch (s3UploadErr) {
              console.error('S3 upload process error:', s3UploadErr);
              return res.status(500).json({ message: 'Failed during S3 upload process' });
            }
          });
        } catch (alternativeUploadErr) {
          console.error('Alternative upload approach failed:', alternativeUploadErr);
          return res.status(500).json({ message: 'All upload approaches failed' });
        }

        return;
      }

      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      // Add a publicly accessible URL to the file object
      const imageUrl = `/uploads/${req.file.filename}`;
      req.file.url = imageUrl;
      console.log('File uploaded successfully:', imageUrl);

      next();
    });
  } catch (error) {
    console.error('File upload middleware error:', error);
    res.status(500).json({ message: 'Server error during file upload' });
  }
};

module.exports = {
  uploadProfilePictureMiddleware
};
