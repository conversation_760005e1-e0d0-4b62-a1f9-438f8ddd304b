## 5.0.1
* Moving postcss to peer dependencies [#74](https://github.com/luisrudge/postcss-flexbugs-fixes/pull/74)

## 5.0.1
* Adding postcss as dependency [#74](https://github.com/luisrudge/postcss-flexbugs-fixes/pull/74)

## 5.0.0
* upgrade to postcss 8 [#71](https://github.com/luisrudge/postcss-flexbugs-fixes/pull/71)

## 4.2.1
* Fix `calc` regex [#69](https://github.com/luisrudge/postcss-flexbugs-fixes/pull/69)

## 4.2.0
* Don't change values that reference custom props [#64](https://github.com/luisrudge/postcss-flexbugs-fixes/pull/64)

## 4.1.0
* Add option to disable bug fixes [#53](https://github.com/luisrudge/postcss-flexbugs-fixes/pull/53)

## 4.0.0
* upgrade to postcss 7

## 3.3.1
* Autoremoval of 0% Basis [#46](https://github.com/luisrudge/postcss-flexbugs-fixes/pull/46). More context [here](https://github.com/luisrudge/postcss-flexbugs-fixes/issues/45#issuecomment-385070879)

## 3.3.0
* Revert Autoremoval of 0% Basis [#43](https://github.com/luisrudge/postcss-flexbugs-fixes/pull/43)

## 3.2.0
* Set flex basis if second value is not a number [#41](https://github.com/luisrudge/postcss-flexbugs-fixes/pull/41)

## 3.1.0
* Fix safari issues with flex-basis [#38](https://github.com/luisrudge/postcss-flexbugs-fixes/pull/38)

## 3.0.0
* upgrade to postcss 6

## 2.1.0
* Prevent mutating value when flex value is one of `['none', 'auto', 'content', 'inherit', 'initial', 'unset']`

## 2.0.0
* upgrade to postcss 5

## 1.1.0
* Ignore flex set to none [#24](https://github.com/luisrudge/postcss-flexbugs-fixes/pull/24)
* The default value of flex-basis is 0% [#22](https://github.com/luisrudge/postcss-flexbugs-fixes/pull/22)

## 1.0
* Initial release
