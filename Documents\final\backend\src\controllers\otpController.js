const { validationResult } = require('express-validator');
const otpService = require('../services/otpService');
const User = require('../models/User');

/**
 * Send OTP for email verification
 * @route POST /api/otp/send-email
 */
exports.sendEmailOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { email, purpose } = req.body;

    // Check if the email exists in the database based on purpose
    if (purpose === 'registration') {
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(400).json({ message: 'Email already registered' });
      }
    } else if (purpose === 'passwordReset' || purpose === 'login') {
      const user = await User.findOne({ email });
      if (!user) {
        return res.status(404).json({ message: 'Email not registered' });
      }
    }

    // Check rate limiting (max 5 OTPs per day per email per purpose)
    const isLimitExceeded = await otpService.checkOTPRequestLimit(email, purpose, 5);
    if (isLimitExceeded) {
      return res.status(429).json({ 
        message: 'OTP request limit exceeded. Please try again later.',
        retryAfter: 24 * 60 * 60 // 24 hours in seconds
      });
    }

    // Generate a 6-digit OTP
    const otp = otpService.generateOTP(6);
    
    // Store OTP in Redis with 5 minute expiry
    await otpService.storeOTP(email, purpose, otp, 300);
    
    // Send OTP via email (different templates based on purpose)
    await otpService.sendOTPViaEmail(email, otp, purpose);
    
    res.status(200).json({ 
      message: 'OTP sent successfully',
      email: email,
      expiresIn: 300 // 5 minutes in seconds
    });
  } catch (error) {
    console.error('Error sending OTP:', error);
    res.status(500).json({ message: 'Failed to send OTP', error: error.message });
  }
};

/**
 * Send OTP via SMS
 * @route POST /api/otp/send-sms
 */
exports.sendSmsOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { phoneNumber, purpose } = req.body;

    // Check if the phone number exists in the database based on purpose
    if (purpose === 'registration') {
      const existingUser = await User.findOne({ phoneNumber });
      if (existingUser) {
        return res.status(400).json({ message: 'Phone number already registered' });
      }
    } else if (purpose === 'passwordReset' || purpose === 'login') {
      const user = await User.findOne({ phoneNumber });
      if (!user) {
        return res.status(404).json({ message: 'Phone number not registered' });
      }
    }

    // Check rate limiting (max 5 OTPs per day per phone number per purpose)
    const isLimitExceeded = await otpService.checkOTPRequestLimit(phoneNumber, purpose, 5);
    if (isLimitExceeded) {
      return res.status(429).json({ 
        message: 'SMS OTP request limit exceeded. Please try again later.',
        retryAfter: 24 * 60 * 60 // 24 hours in seconds
      });
    }

    // Generate a 6-digit OTP
    const otp = otpService.generateOTP(6);
    
    // Store OTP in Redis with 5 minute expiry
    await otpService.storeOTP(phoneNumber, purpose, otp, 300);
    
    // Send OTP via SMS
    await otpService.sendOTPViaSMS(phoneNumber, otp, purpose);
    
    // Mask the phone number for privacy in the response
    const maskedPhoneNumber = phoneNumber.slice(0, 2) + '*'.repeat(phoneNumber.length - 4) + phoneNumber.slice(-2);
    
    res.status(200).json({ 
      message: 'SMS OTP sent successfully',
      phoneNumber: maskedPhoneNumber,
      expiresIn: 300 // 5 minutes in seconds
    });
  } catch (error) {
    console.error('Error sending SMS OTP:', error);
    res.status(500).json({ message: 'Failed to send SMS OTP', error: error.message });
  }
};

/**
 * Verify OTP
 * @route POST /api/otp/verify
 */
exports.verifyOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { identifier, otp, purpose } = req.body;
    
    // Verify OTP (identifier can be email or phone number)
    const isValid = await otpService.verifyOTP(identifier, purpose, otp);
    
    if (!isValid) {
      return res.status(400).json({ message: 'Invalid or expired OTP' });
    }
    
    res.status(200).json({ 
      message: 'OTP verified successfully',
      verified: true
    });
  } catch (error) {
    console.error('Error verifying OTP:', error);
    res.status(500).json({ message: 'Failed to verify OTP', error: error.message });
  }
};

/**
 * Resend OTP
 * @route POST /api/otp/resend
 */
exports.resendOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { identifier, purpose, method = 'email' } = req.body;
    
    // Check rate limiting
    const isLimitExceeded = await otpService.checkOTPRequestLimit(identifier, purpose, 5);
    if (isLimitExceeded) {
      return res.status(429).json({ 
        message: 'OTP request limit exceeded. Please try again later.',
        retryAfter: 24 * 60 * 60 // 24 hours in seconds
      });
    }
    
    // Delete any existing OTP
    await otpService.deleteOTP(identifier, purpose);
    
    // Generate a new OTP
    const otp = otpService.generateOTP(6);
    
    // Store OTP in Redis with 5 minute expiry
    await otpService.storeOTP(identifier, purpose, otp, 300);
    
    // Send OTP via chosen method
    if (method === 'sms') {
      await otpService.sendOTPViaSMS(identifier, otp, purpose);
      
      // Mask the phone number for privacy
      const maskedIdentifier = identifier.slice(0, 2) + '*'.repeat(identifier.length - 4) + identifier.slice(-2);
      
      return res.status(200).json({ 
        message: 'SMS OTP resent successfully',
        phoneNumber: maskedIdentifier,
        expiresIn: 300 // 5 minutes in seconds
      });
    } else {
      // Default to email
      await otpService.sendOTPViaEmail(identifier, otp, purpose);
      
      return res.status(200).json({ 
        message: 'Email OTP resent successfully',
        email: identifier,
        expiresIn: 300 // 5 minutes in seconds
      });
    }
  } catch (error) {
    console.error('Error resending OTP:', error);
    res.status(500).json({ message: 'Failed to resend OTP', error: error.message });
  }
};
