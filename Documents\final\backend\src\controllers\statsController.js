/**
 * Stats Controller
 * 
 * This controller provides endpoints for platform statistics.
 */

const statsService = require('../services/statsService');

/**
 * Get platform statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Platform statistics
 */
exports.getPlatformStats = async (req, res) => {
  try {
    const stats = await statsService.getPlatformStats();
    return res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Error getting platform stats:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get platform statistics'
    });
  }
};

/**
 * Get active users count
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Active users count
 */
exports.getActiveUsersCount = async (req, res) => {
  try {
    const count = await statsService.getActiveUsersCount();
    return res.json({
      success: true,
      count
    });
  } catch (error) {
    console.error('Error getting active users count:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get active users count'
    });
  }
};
