import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const hychain = /*#__PURE__*/ defineChain({
    id: 2911,
    name: 'HYCHAIN',
    nativeCurrency: { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', symbol: 'TOPI<PERSON>', decimals: 18 },
    rpcUrls: {
        default: { http: ['https://rpc.hychain.com/http'] },
    },
    blockExplorers: {
        default: {
            name: 'HYCHAIN Explorer',
            url: 'https://explorer.hychain.com',
        },
    },
    testnet: false,
});
//# sourceMappingURL=hychain.js.map