const session = require('express-session');
const MongoStore = require('connect-mongo');

const sessionConfig = {
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  resave: false,
  saveUninitialized: true,
  store: MongoStore.create({
    mongoUrl: process.env.MONGODB_URI || 'mongodb://localhost:27017/swapDB',
    ttl: 60 * 5, // Session TTL (5 minutes)
    autoRemove: 'native' // Enable automatic removal of expired sessions
  }),
  cookie: {
    secure: false,
    httpOnly: true,
    maxAge: 1000 * 60 * 5, // 5 minutes
    sameSite: 'lax'
  }
};

module.exports = sessionConfig; 