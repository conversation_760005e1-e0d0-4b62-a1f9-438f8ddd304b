const express = require('express');
const router = express.Router();
const solanaService = require('../src/services/solanaService');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads/tokens');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'token-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

/**
 * @route POST /api/tokens/create
 * @desc Create a new token
 * @access Public
 */
router.post('/create', upload.single('image'), async (req, res) => {
  try {
    const {
      name,
      symbol,
      description,
      initialSupply,
      website,
      twitter,
      telegram,
      discord,
      creatorPublicKey
    } = req.body;

    // Validate required fields
    if (!name || !symbol || !description || !initialSupply || !creatorPublicKey) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: name, symbol, description, initialSupply, creatorPublicKey'
      });
    }

    // Validate initialSupply is a number
    const supply = parseInt(initialSupply);
    if (isNaN(supply) || supply <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Initial supply must be a positive number'
      });
    }

    // Create metadata URI (in a real app, you'd upload to IPFS or Arweave)
    let imageUri = '';
    if (req.file) {
      imageUri = `/uploads/tokens/${req.file.filename}`;
    }

    const metadata = {
      name,
      symbol,
      description,
      image: imageUri,
      external_url: website || '',
      attributes: []
    };

    // Add social links as attributes
    if (website) metadata.attributes.push({ trait_type: 'Website', value: website });
    if (twitter) metadata.attributes.push({ trait_type: 'Twitter', value: twitter });
    if (telegram) metadata.attributes.push({ trait_type: 'Telegram', value: telegram });
    if (discord) metadata.attributes.push({ trait_type: 'Discord', value: discord });

    // Save metadata to file (in production, upload to IPFS/Arweave)
    const metadataPath = path.join(__dirname, '../uploads/tokens', `metadata-${Date.now()}.json`);
    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
    const metadataUri = `/uploads/tokens/${path.basename(metadataPath)}`;

    // Create token transaction on Solana
    const tokenData = {
      name,
      symbol,
      description,
      initialSupply: supply,
      decimals: 9,
      basePrice: 1000000, // 0.001 SOL
      curveSlope: 1000,
      creatorFeePercent: 100, // 1%
      reserveRatio: 5000, // 50%
      maxSupply: null,
      minimumLiquidity: 100000000, // 0.1 SOL
      uri: metadataUri,
      creatorPublicKey
    };

    const result = await solanaService.createTokenTransaction(tokenData);

    if (result.success) {
      // Store token info in database
      const Token = require('../src/models/Token');

      try {
        const tokenDoc = new Token({
          name,
          symbol: symbol.toUpperCase(),
          totalSupply: supply,
          currentSupply: supply,
          decimals: 9,
          mintAddress: result.mint,
          creator: creatorPublicKey, // Now accepts Solana public key strings
          description,
          website,
          social: {
            twitter,
            telegram,
            discord
          },
          logo: imageUri,
          bondingCurve: {
            address: result.bondingCurve,
            curveType: 'linear',
            basePrice: 1000000, // 0.001 SOL
            slope: 1000,
            reserveRatio: 0.5,
            maxSupply: 0 // unlimited
          },
          metadata: {
            metadataUri,
            initialSupply: supply,
            createdAt: new Date()
          }
        });

        await tokenDoc.save();
        console.log('Token saved to database:', tokenDoc._id);
      } catch (dbError) {
        console.error('Failed to save token to database:', dbError);
        // Continue anyway - the blockchain transaction is more important
      }

      const tokenInfo = {
        mint: result.mint,
        bondingCurve: result.bondingCurve,
        name,
        symbol: symbol.toUpperCase(),
        description,
        creator: creatorPublicKey,
        imageUri,
        metadataUri,
        website,
        twitter,
        telegram,
        discord,
        initialSupply: supply,
        createdAt: new Date()
      };

      res.json({
        success: true,
        token: tokenInfo,
        transaction: result.transaction, // Base64 encoded transaction for frontend to sign
        message: 'Transaction created. Please sign it in your wallet.'
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error creating token:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/tokens/buy
 * @desc Buy tokens using bonding curve
 * @access Public
 */
router.post('/buy', async (req, res) => {
  try {
    const { mint, solAmount, buyerPublicKey } = req.body;

    if (!mint || !solAmount || !buyerPublicKey) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: mint, solAmount, buyerPublicKey'
      });
    }

    const amount = parseInt(solAmount);
    if (isNaN(amount) || amount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'SOL amount must be a positive number'
      });
    }

    const result = await solanaService.buyTokens(mint, amount, buyerPublicKey);

    res.json(result);
  } catch (error) {
    console.error('Error buying tokens:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/tokens/price/:mint
 * @desc Get current token price
 * @access Public
 */
router.get('/price/:mint', async (req, res) => {
  try {
    const { mint } = req.params;

    if (!mint) {
      return res.status(400).json({
        success: false,
        error: 'Token mint address is required'
      });
    }

    const result = await solanaService.getTokenPrice(mint);

    res.json(result);
  } catch (error) {
    console.error('Error getting token price:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/tokens/curve/:mint
 * @desc Get bonding curve data
 * @access Public
 */
router.get('/curve/:mint', async (req, res) => {
  try {
    const { mint } = req.params;

    if (!mint) {
      return res.status(400).json({
        success: false,
        error: 'Token mint address is required'
      });
    }

    const result = await solanaService.getBondingCurveData(mint);

    res.json(result);
  } catch (error) {
    console.error('Error getting bonding curve data:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/tokens/all
 * @desc Get all tokens on the platform
 * @access Public
 */
router.get('/all', async (req, res) => {
  try {
    const result = await solanaService.getAllTokens();

    res.json(result);
  } catch (error) {
    console.error('Error getting all tokens:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/tokens/trending
 * @desc Get trending tokens
 * @access Public
 */
router.get('/trending', async (req, res) => {
  try {
    const result = await solanaService.getAllTokens();

    if (result.success) {
      // Sort by volume and take top 10
      const trending = result.tokens
        .sort((a, b) => parseInt(b.totalVolume) - parseInt(a.totalVolume))
        .slice(0, 10);

      res.json({
        success: true,
        tokens: trending
      });
    } else {
      res.json(result);
    }
  } catch (error) {
    console.error('Error getting trending tokens:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/tokens/generate-keypair
 * @desc Generate a new Solana keypair
 * @access Public
 */
router.post('/generate-keypair', async (req, res) => {
  try {
    const keypair = solanaService.generateKeypair();

    res.json({
      success: true,
      keypair
    });
  } catch (error) {
    console.error('Error generating keypair:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/tokens/balance/:publicKey
 * @desc Get SOL balance for a public key
 * @access Public
 */
router.get('/balance/:publicKey', async (req, res) => {
  try {
    const { publicKey } = req.params;

    if (!publicKey) {
      return res.status(400).json({
        success: false,
        error: 'Public key is required'
      });
    }

    const result = await solanaService.getBalance(publicKey);

    res.json(result);
  } catch (error) {
    console.error('Error getting balance:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/tokens/airdrop
 * @desc Request SOL airdrop (devnet only)
 * @access Public
 */
router.post('/airdrop', async (req, res) => {
  try {
    const { publicKey, amount = 1 } = req.body;

    if (!publicKey) {
      return res.status(400).json({
        success: false,
        error: 'Public key is required'
      });
    }

    const result = await solanaService.airdrop(publicKey, amount);

    res.json(result);
  } catch (error) {
    console.error('Error requesting airdrop:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Legacy route for compatibility
router.get('/', async (req, res) => {
  try {
    const result = await solanaService.getAllTokens();
    res.json(result.success ? result.tokens : []);
  } catch (error) {
    res.status(200).json([]);
  }
});

router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await solanaService.getBondingCurveData(id);

    if (result.success) {
      res.json(result.data);
    } else {
      res.status(404).json({ message: 'Token not found' });
    }
  } catch (error) {
    res.status(404).json({ message: 'Token not found' });
  }
});

module.exports = router;
