/**
 * <PERSON>ript to run both migration scripts and write output to a file
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Create a log file
const logFile = path.join(__dirname, 'migration_log.txt');
fs.writeFileSync(logFile, `Migration Log - ${new Date().toISOString()}\n\n`);

// Function to append to log file
function log(message) {
  fs.appendFileSync(logFile, message + '\n');
  console.log(message);
}

// Function to run a script and log output
function runScript(scriptPath) {
  return new Promise((resolve, reject) => {
    log(`Running script: ${scriptPath}`);
    
    const child = exec(`node ${scriptPath}`, {
      cwd: path.join(__dirname, '..', '..')
    });
    
    child.stdout.on('data', (data) => {
      log(`STDOUT: ${data.toString().trim()}`);
    });
    
    child.stderr.on('data', (data) => {
      log(`STDERR: ${data.toString().trim()}`);
    });
    
    child.on('close', (code) => {
      log(`Script exited with code ${code}`);
      resolve();
    });
    
    child.on('error', (error) => {
      log(`Script error: ${error.message}`);
      reject(error);
    });
  });
}

// Run both scripts
async function runMigrationScripts() {
  try {
    log('Starting migration scripts');
    
    // Run createBalanceRecords.js
    log('\n=== Running createBalanceRecords.js ===\n');
    await runScript(path.join(__dirname, 'createBalanceRecords.js'));
    
    // Run migrateOrdersToUserIds.js
    log('\n=== Running migrateOrdersToUserIds.js ===\n');
    await runScript(path.join(__dirname, 'migrateOrdersToUserIds.js'));
    
    log('\nAll migration scripts completed successfully');
    log(`Log file written to: ${logFile}`);
  } catch (error) {
    log(`Error running migration scripts: ${error.message}`);
  }
}

// Run the migration scripts
runMigrationScripts();
