{"version": 3, "sources": ["../../@solana/wallet-standard-util/src/signMessage.ts", "../../@solana/wallet-standard-util/src/signIn.ts", "../../@solana/wallet-standard-util/src/commitment.ts", "../../@solana/wallet-standard-chains/src/index.ts", "../../@solana/wallet-standard-util/src/endpoint.ts", "../../@wallet-standard/wallet/src/register.ts", "../../@wallet-standard/wallet/src/util.ts"], "sourcesContent": ["import { ed25519 } from '@noble/curves/ed25519';\nimport type { SolanaSignMessageInput, SolanaSignMessageOutput } from '@solana/wallet-standard-features';\nimport { bytesEqual } from './util.js';\n\n/**\n * TODO: docs\n */\nexport function verifyMessageSignature({\n    message,\n    signedMessage,\n    signature,\n    publicKey,\n}: {\n    message: Uint8Array;\n    signedMessage: Uint8Array;\n    signature: Uint8Array;\n    publicKey: Uint8Array;\n}): boolean {\n    // TODO: implement https://github.com/solana-labs/solana/blob/master/docs/src/proposals/off-chain-message-signing.md\n    return bytesEqual(message, signedMessage) && ed25519.verify(signature, signedMessage, publicKey);\n}\n\n/**\n * TODO: docs\n */\nexport function verifySignMessage(input: SolanaSignMessageInput, output: SolanaSignMessageOutput): boolean {\n    const {\n        message,\n        account: { publicKey },\n    } = input;\n    const { signedMessage, signature } = output;\n    return verifyMessageSignature({ message, signedMessage, signature, publicKey: publicKey as Uint8Array });\n}\n", "import type { SolanaSignInInput, SolanaSignInOutput } from '@solana/wallet-standard-features';\nimport { verifyMessageSignature } from './signMessage.js';\nimport { arraysEqual } from './util.js';\n\n/**\n * TODO: docs\n */\nexport function verifySignIn(input: SolanaSignInInput, output: SolanaSignInOutput): boolean {\n    const {\n        signedMessage,\n        signature,\n        account: { publicKey },\n    } = output;\n    const message = deriveSignInMessage(input, output);\n    return (\n        !!message && verifyMessageSignature({ message, signedMessage, signature, publicKey: publicKey as Uint8Array })\n    );\n}\n\n/**\n * TODO: docs\n */\nexport function deriveSignInMessage(input: SolanaSignInInput, output: SolanaSignInOutput): Uint8Array | null {\n    const text = deriveSignInMessageText(input, output);\n    if (!text) return null;\n    return new TextEncoder().encode(text);\n}\n\n/**\n * TODO: docs\n */\nexport function deriveSignInMessageText(input: SolanaSignInInput, output: SolanaSignInOutput): string | null {\n    const parsed = parseSignInMessage(output.signedMessage);\n    if (!parsed) return null;\n\n    if (input.domain && input.domain !== parsed.domain) return null;\n    if (input.address && input.address !== parsed.address) return null;\n    if (input.statement !== parsed.statement) return null;\n    if (input.uri !== parsed.uri) return null;\n    if (input.version !== parsed.version) return null;\n    if (input.chainId !== parsed.chainId) return null;\n    if (input.nonce !== parsed.nonce) return null;\n    if (input.issuedAt !== parsed.issuedAt) return null;\n    if (input.expirationTime !== parsed.expirationTime) return null;\n    if (input.notBefore !== parsed.notBefore) return null;\n    if (input.requestId !== parsed.requestId) return null;\n    if (input.resources) {\n        if (!parsed.resources) return null;\n        if (!arraysEqual(input.resources, parsed.resources)) return null;\n    } else if (parsed.resources) return null;\n\n    return createSignInMessageText(parsed);\n}\n\n/**\n * TODO: docs\n */\nexport type SolanaSignInInputWithRequiredFields = SolanaSignInInput &\n    Required<Pick<SolanaSignInInput, 'domain' | 'address'>>;\n\n/**\n * TODO: docs\n */\nexport function parseSignInMessage(message: Uint8Array): SolanaSignInInputWithRequiredFields | null {\n    const text = new TextDecoder().decode(message);\n    return parseSignInMessageText(text);\n}\n\n// TODO: implement https://github.com/solana-labs/solana/blob/master/docs/src/proposals/off-chain-message-signing.md\nconst DOMAIN = '(?<domain>[^\\\\n]+?) wants you to sign in with your Solana account:\\\\n';\nconst ADDRESS = '(?<address>[^\\\\n]+)(?:\\\\n|$)';\nconst STATEMENT = '(?:\\\\n(?<statement>[\\\\S\\\\s]*?)(?:\\\\n|$))??';\nconst URI = '(?:\\\\nURI: (?<uri>[^\\\\n]+))?';\nconst VERSION = '(?:\\\\nVersion: (?<version>[^\\\\n]+))?';\nconst CHAIN_ID = '(?:\\\\nChain ID: (?<chainId>[^\\\\n]+))?';\nconst NONCE = '(?:\\\\nNonce: (?<nonce>[^\\\\n]+))?';\nconst ISSUED_AT = '(?:\\\\nIssued At: (?<issuedAt>[^\\\\n]+))?';\nconst EXPIRATION_TIME = '(?:\\\\nExpiration Time: (?<expirationTime>[^\\\\n]+))?';\nconst NOT_BEFORE = '(?:\\\\nNot Before: (?<notBefore>[^\\\\n]+))?';\nconst REQUEST_ID = '(?:\\\\nRequest ID: (?<requestId>[^\\\\n]+))?';\nconst RESOURCES = '(?:\\\\nResources:(?<resources>(?:\\\\n- [^\\\\n]+)*))?';\nconst FIELDS = `${URI}${VERSION}${CHAIN_ID}${NONCE}${ISSUED_AT}${EXPIRATION_TIME}${NOT_BEFORE}${REQUEST_ID}${RESOURCES}`;\nconst MESSAGE = new RegExp(`^${DOMAIN}${ADDRESS}${STATEMENT}${FIELDS}\\\\n*$`);\n\n/**\n * TODO: docs\n */\nexport function parseSignInMessageText(text: string): SolanaSignInInputWithRequiredFields | null {\n    const match = MESSAGE.exec(text);\n    if (!match) return null;\n    const groups = match.groups;\n    if (!groups) return null;\n\n    return {\n        domain: groups.domain!,\n\n        address: groups.address!,\n        statement: groups.statement,\n        uri: groups.uri,\n        version: groups.version,\n        nonce: groups.nonce,\n        chainId: groups.chainId,\n        issuedAt: groups.issuedAt,\n        expirationTime: groups.expirationTime,\n        notBefore: groups.notBefore,\n        requestId: groups.requestId,\n        resources: groups.resources?.split('\\n- ').slice(1),\n    };\n}\n\n/**\n * TODO: docs\n */\nexport function createSignInMessage(input: SolanaSignInInputWithRequiredFields): Uint8Array {\n    const text = createSignInMessageText(input);\n    return new TextEncoder().encode(text);\n}\n\n/**\n * TODO: docs\n */\nexport function createSignInMessageText(input: SolanaSignInInputWithRequiredFields): string {\n    // ${domain} wants you to sign in with your Solana account:\n    // ${address}\n    //\n    // ${statement}\n    //\n    // URI: ${uri}\n    // Version: ${version}\n    // Chain ID: ${chain}\n    // Nonce: ${nonce}\n    // Issued At: ${issued-at}\n    // Expiration Time: ${expiration-time}\n    // Not Before: ${not-before}\n    // Request ID: ${request-id}\n    // Resources:\n    // - ${resources[0]}\n    // - ${resources[1]}\n    // ...\n    // - ${resources[n]}\n\n    let message = `${input.domain} wants you to sign in with your Solana account:\\n`;\n    message += `${input.address}`;\n\n    if (input.statement) {\n        message += `\\n\\n${input.statement}`;\n    }\n\n    const fields: string[] = [];\n    if (input.uri) {\n        fields.push(`URI: ${input.uri}`);\n    }\n    if (input.version) {\n        fields.push(`Version: ${input.version}`);\n    }\n    if (input.chainId) {\n        fields.push(`Chain ID: ${input.chainId}`);\n    }\n    if (input.nonce) {\n        fields.push(`Nonce: ${input.nonce}`);\n    }\n    if (input.issuedAt) {\n        fields.push(`Issued At: ${input.issuedAt}`);\n    }\n    if (input.expirationTime) {\n        fields.push(`Expiration Time: ${input.expirationTime}`);\n    }\n    if (input.notBefore) {\n        fields.push(`Not Before: ${input.notBefore}`);\n    }\n    if (input.requestId) {\n        fields.push(`Request ID: ${input.requestId}`);\n    }\n    if (input.resources) {\n        fields.push(`Resources:`);\n        for (const resource of input.resources) {\n            fields.push(`- ${resource}`);\n        }\n    }\n    if (fields.length) {\n        message += `\\n\\n${fields.join('\\n')}`;\n    }\n\n    return message;\n}\n", "import type { SolanaTransactionCommitment } from '@solana/wallet-standard-features';\n\n// Copied from @solana/web3.js\ntype Commitment = 'processed' | 'confirmed' | 'finalized' | 'recent' | 'single' | 'singleGossip' | 'root' | 'max';\n\n/**\n * TODO: docs\n */\nexport function getCommitment(commitment?: Commitment): SolanaTransactionCommitment | undefined {\n    switch (commitment) {\n        case 'processed':\n        case 'confirmed':\n        case 'finalized':\n        case undefined:\n            return commitment;\n        case 'recent':\n            return 'processed';\n        case 'single':\n        case 'singleGossip':\n            return 'confirmed';\n        case 'max':\n        case 'root':\n            return 'finalized';\n        default:\n            return undefined;\n    }\n}\n", "import type { IdentifierString } from '@wallet-standard/base';\n\n/** Solana Mainnet (beta) cluster, e.g. https://api.mainnet-beta.solana.com */\nexport const SOLANA_MAINNET_CHAIN = 'solana:mainnet';\n\n/** Solana Devnet cluster, e.g. https://api.devnet.solana.com */\nexport const SOLANA_DEVNET_CHAIN = 'solana:devnet';\n\n/** Solana Testnet cluster, e.g. https://api.testnet.solana.com */\nexport const SOLANA_TESTNET_CHAIN = 'solana:testnet';\n\n/** Solana Localnet cluster, e.g. http://localhost:8899 */\nexport const SOLANA_LOCALNET_CHAIN = 'solana:localnet';\n\n/** Array of all Solana clusters */\nexport const SOLANA_CHAINS = [\n    SOLANA_MAINNET_CHAIN,\n    SOLANA_DEVNET_CHAIN,\n    SOLANA_TESTNET_CHAIN,\n    SOLANA_LOCALNET_CHAIN,\n] as const;\n\n/** Type of all Solana clusters */\nexport type SolanaChain = (typeof SOLANA_CHAINS)[number];\n\n/**\n * Check if a chain corresponds with one of the Solana clusters.\n */\nexport function isSolanaChain(chain: IdentifierString): chain is SolanaChain {\n    return SOLANA_CHAINS.includes(chain as SolanaChain);\n}\n", "import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@solana/wallet-standard-chains';\nimport {\n    SOLANA_DEVNET_CHAIN,\n    SOLANA_LOCALNET_CHAIN,\n    SOLANA_MAINNET_CHAIN,\n    SOLANA_TESTNET_CHAIN,\n} from '@solana/wallet-standard-chains';\n\n/** TODO: docs */\nexport const MAINNET_ENDPOINT = 'https://api.mainnet-beta.solana.com';\n/** TODO: docs */\nexport const DEVNET_ENDPOINT = 'https://api.devnet.solana.com';\n/** TODO: docs */\nexport const TESTNET_ENDPOINT = 'https://api.testnet.solana.com';\n/** TODO: docs */\nexport const LOCALNET_ENDPOINT = 'http://localhost:8899';\n\n/**\n * TODO: docs\n */\nexport function getChainForEndpoint(endpoint: string): SolanaChain {\n    if (endpoint.includes(MAINNET_ENDPOINT)) return SOLAN<PERSON>_MAINNET_CHAIN;\n    if (/\\bdevnet\\b/i.test(endpoint)) return SOLANA_DEVNET_CHAIN;\n    if (/\\btestnet\\b/i.test(endpoint)) return SOLANA_TESTNET_CHAIN;\n    if (/\\blocalhost\\b/i.test(endpoint) || /\\b127\\.0\\.0\\.1\\b/.test(endpoint)) return SOLANA_LOCALNET_CHAIN;\n    return SOLANA_MAINNET_CHAIN;\n}\n\n/**\n * TODO: docs\n */\nexport function getEndpointForChain(chain: SolanaChain, endpoint?: string): string {\n    if (endpoint) return endpoint;\n    if (chain === SOLANA_MAINNET_CHAIN) return MAINNET_ENDPOINT;\n    if (chain === SOLANA_DEVNET_CHAIN) return DEVNET_ENDPOINT;\n    if (chain === SOLANA_TESTNET_CHAIN) return TESTNET_ENDPOINT;\n    if (chain === SOLANA_LOCALNET_CHAIN) return LOCALNET_ENDPOINT;\n    return MAINNET_ENDPOINT;\n}\n", "import type {\n    DEPRECATED_WalletsWindow,\n    Wallet,\n    WalletEventsWindow,\n    WindowRegisterWalletEvent,\n    WindowRegisterWalletEventCallback,\n} from '@wallet-standard/base';\n\n/**\n * Register a {@link \"@wallet-standard/base\".Wallet} as a Standard Wallet with the app.\n *\n * This dispatches a {@link \"@wallet-standard/base\".WindowRegisterWalletEvent} to notify the app that the Wallet is\n * ready to be registered.\n *\n * This also adds a listener for {@link \"@wallet-standard/base\".WindowAppReadyEvent} to listen for a notification from\n * the app that the app is ready to register the Wallet.\n *\n * This combination of event dispatch and listener guarantees that the Wallet will be registered synchronously as soon\n * as the app is ready whether the Wallet loads before or after the app.\n *\n * @param wallet Wallet to register.\n *\n * @group Wallet\n */\nexport function registerWallet(wallet: Wallet): void {\n    const callback: WindowRegisterWalletEventCallback = ({ register }) => register(wallet);\n    try {\n        (window as WalletEventsWindow).dispatchEvent(new RegisterWalletEvent(callback));\n    } catch (error) {\n        console.error('wallet-standard:register-wallet event could not be dispatched\\n', error);\n    }\n    try {\n        (window as WalletEventsWindow).addEventListener('wallet-standard:app-ready', ({ detail: api }) =>\n            callback(api)\n        );\n    } catch (error) {\n        console.error('wallet-standard:app-ready event listener could not be added\\n', error);\n    }\n}\n\nclass RegisterWalletEvent extends Event implements WindowRegisterWalletEvent {\n    readonly #detail: WindowRegisterWalletEventCallback;\n\n    get detail() {\n        return this.#detail;\n    }\n\n    get type() {\n        return 'wallet-standard:register-wallet' as const;\n    }\n\n    constructor(callback: WindowRegisterWalletEventCallback) {\n        super('wallet-standard:register-wallet', {\n            bubbles: false,\n            cancelable: false,\n            composed: false,\n        });\n        this.#detail = callback;\n    }\n\n    /** @deprecated */\n    preventDefault(): never {\n        throw new Error('preventDefault cannot be called');\n    }\n\n    /** @deprecated */\n    stopImmediatePropagation(): never {\n        throw new Error('stopImmediatePropagation cannot be called');\n    }\n\n    /** @deprecated */\n    stopPropagation(): never {\n        throw new Error('stopPropagation cannot be called');\n    }\n}\n\n/**\n * @deprecated Use {@link registerWallet} instead.\n *\n * @group Deprecated\n */\nexport function DEPRECATED_registerWallet(wallet: Wallet): void {\n    registerWallet(wallet);\n    try {\n        ((window as DEPRECATED_WalletsWindow).navigator.wallets ||= []).push(({ register }) => register(wallet));\n    } catch (error) {\n        console.error('window.navigator.wallets could not be pushed\\n', error);\n    }\n}\n", "import type { ReadonlyUint8Array, WalletAccount } from '@wallet-standard/base';\n\n/**\n * Base implementation of a {@link \"@wallet-standard/base\".WalletAccount} to be used or extended by a\n * {@link \"@wallet-standard/base\".Wallet}.\n *\n * `WalletAccount` properties must be read-only. This class enforces this by making all properties\n * [truly private](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Classes/Private_class_fields) and\n * read-only, using getters for access, returning copies instead of references, and calling\n * [Object.freeze](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze)\n * on the instance.\n *\n * @group Account\n */\nexport class ReadonlyWalletAccount implements WalletAccount {\n    readonly #address: WalletAccount['address'];\n    readonly #publicKey: WalletAccount['publicKey'];\n    readonly #chains: WalletAccount['chains'];\n    readonly #features: WalletAccount['features'];\n    readonly #label: WalletAccount['label'];\n    readonly #icon: WalletAccount['icon'];\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.address | WalletAccount::address} */\n    get address() {\n        return this.#address;\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.publicKey | WalletAccount::publicKey} */\n    get publicKey() {\n        return this.#publicKey.slice();\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.chains | WalletAccount::chains} */\n    get chains() {\n        return this.#chains.slice();\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.features | WalletAccount::features} */\n    get features() {\n        return this.#features.slice();\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.label | WalletAccount::label} */\n    get label() {\n        return this.#label;\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.icon | WalletAccount::icon} */\n    get icon() {\n        return this.#icon;\n    }\n\n    /**\n     * Create and freeze a read-only account.\n     *\n     * @param account Account to copy properties from.\n     */\n    constructor(account: WalletAccount) {\n        if (new.target === ReadonlyWalletAccount) {\n            Object.freeze(this);\n        }\n\n        this.#address = account.address;\n        this.#publicKey = account.publicKey.slice();\n        this.#chains = account.chains.slice();\n        this.#features = account.features.slice();\n        this.#label = account.label;\n        this.#icon = account.icon;\n    }\n}\n\n/**\n * Efficiently compare {@link Indexed} arrays (e.g. `Array` and `Uint8Array`).\n *\n * @param a An array.\n * @param b Another array.\n *\n * @return `true` if the arrays have the same length and elements, `false` otherwise.\n *\n * @group Util\n */\nexport function arraysEqual<T>(a: Indexed<T>, b: Indexed<T>): boolean {\n    if (a === b) return true;\n\n    const length = a.length;\n    if (length !== b.length) return false;\n\n    for (let i = 0; i < length; i++) {\n        if (a[i] !== b[i]) return false;\n    }\n\n    return true;\n}\n\n/**\n * Efficiently compare byte arrays, using {@link arraysEqual}.\n *\n * @param a A byte array.\n * @param b Another byte array.\n *\n * @return `true` if the byte arrays have the same length and bytes, `false` otherwise.\n *\n * @group Util\n */\nexport function bytesEqual(a: ReadonlyUint8Array, b: ReadonlyUint8Array): boolean {\n    return arraysEqual(a, b);\n}\n\n/**\n * Efficiently concatenate byte arrays without modifying them.\n *\n * @param first  A byte array.\n * @param others Additional byte arrays.\n *\n * @return New byte array containing the concatenation of all the byte arrays.\n *\n * @group Util\n */\nexport function concatBytes(first: ReadonlyUint8Array, ...others: ReadonlyUint8Array[]): Uint8Array {\n    const length = others.reduce((length, bytes) => length + bytes.length, first.length);\n    const bytes = new Uint8Array(length);\n\n    bytes.set(first, 0);\n    for (const other of others) {\n        bytes.set(other, bytes.length);\n    }\n\n    return bytes;\n}\n\n/**\n * Create a new object with a subset of fields from a source object.\n *\n * @param source Object to pick fields from.\n * @param keys   Names of fields to pick.\n *\n * @return New object with only the picked fields.\n *\n * @group Util\n */\nexport function pick<T, K extends keyof T>(source: T, ...keys: K[]): Pick<T, K> {\n    const picked = {} as Pick<T, K>;\n    for (const key of keys) {\n        picked[key] = source[key];\n    }\n    return picked;\n}\n\n/**\n * Call a callback function, catch an error if it throws, and log the error without rethrowing.\n *\n * @param callback Function to call.\n *\n * @group Util\n */\nexport function guard(callback: () => void) {\n    try {\n        callback();\n    } catch (error) {\n        console.error(error);\n    }\n}\n\n/**\n * @internal\n *\n * Type with a numeric `length` and numerically indexed elements of a generic type `T`.\n *\n * For example, `Array<T>` and `Uint8Array`.\n *\n * @group Internal\n */\nexport interface Indexed<T> {\n    length: number;\n    [index: number]: T;\n}\n"], "mappings": ";;;;;AAAA;;;ACqEA,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,IAAM,MAAM;AACZ,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,YAAY;AAClB,IAAM,kBAAkB;AACxB,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,SAAS,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,GAAG,KAAK,GAAG,SAAS,GAAG,eAAe,GAAG,UAAU,GAAG,UAAU,GAAG,SAAS;AACtH,IAAM,UAAU,IAAI,OAAO,IAAI,MAAM,GAAG,OAAO,GAAG,SAAS,GAAG,MAAM,OAAO;AA+BrE,SAAU,oBAAoB,OAA0C;AAC1E,QAAM,OAAO,wBAAwB,KAAK;AAC1C,SAAO,IAAI,YAAW,EAAG,OAAO,IAAI;AACxC;AAKM,SAAU,wBAAwB,OAA0C;AAoB9E,MAAI,UAAU,GAAG,MAAM,MAAM;;AAC7B,aAAW,GAAG,MAAM,OAAO;AAE3B,MAAI,MAAM,WAAW;AACjB,eAAW;;EAAO,MAAM,SAAS;EACrC;AAEA,QAAM,SAAmB,CAAA;AACzB,MAAI,MAAM,KAAK;AACX,WAAO,KAAK,QAAQ,MAAM,GAAG,EAAE;EACnC;AACA,MAAI,MAAM,SAAS;AACf,WAAO,KAAK,YAAY,MAAM,OAAO,EAAE;EAC3C;AACA,MAAI,MAAM,SAAS;AACf,WAAO,KAAK,aAAa,MAAM,OAAO,EAAE;EAC5C;AACA,MAAI,MAAM,OAAO;AACb,WAAO,KAAK,UAAU,MAAM,KAAK,EAAE;EACvC;AACA,MAAI,MAAM,UAAU;AAChB,WAAO,KAAK,cAAc,MAAM,QAAQ,EAAE;EAC9C;AACA,MAAI,MAAM,gBAAgB;AACtB,WAAO,KAAK,oBAAoB,MAAM,cAAc,EAAE;EAC1D;AACA,MAAI,MAAM,WAAW;AACjB,WAAO,KAAK,eAAe,MAAM,SAAS,EAAE;EAChD;AACA,MAAI,MAAM,WAAW;AACjB,WAAO,KAAK,eAAe,MAAM,SAAS,EAAE;EAChD;AACA,MAAI,MAAM,WAAW;AACjB,WAAO,KAAK,YAAY;AACxB,eAAW,YAAY,MAAM,WAAW;AACpC,aAAO,KAAK,KAAK,QAAQ,EAAE;IAC/B;EACJ;AACA,MAAI,OAAO,QAAQ;AACf,eAAW;;EAAO,OAAO,KAAK,IAAI,CAAC;EACvC;AAEA,SAAO;AACX;;;AChLM,SAAU,cAAc,YAAuB;AACjD,UAAQ,YAAY;IAChB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACD,aAAO;IACX,KAAK;AACD,aAAO;IACX,KAAK;IACL,KAAK;AACD,aAAO;IACX,KAAK;IACL,KAAK;AACD,aAAO;IACX;AACI,aAAO;EACf;AACJ;;;ACvBO,IAAM,uBAAuB;AAG7B,IAAM,sBAAsB;AAG5B,IAAM,uBAAuB;AAG7B,IAAM,wBAAwB;;;ACH9B,IAAM,mBAAmB;AAW1B,SAAU,oBAAoB,UAAgB;AAChD,MAAI,SAAS,SAAS,gBAAgB;AAAG,WAAO;AAChD,MAAI,cAAc,KAAK,QAAQ;AAAG,WAAO;AACzC,MAAI,eAAe,KAAK,QAAQ;AAAG,WAAO;AAC1C,MAAI,iBAAiB,KAAK,QAAQ,KAAK,mBAAmB,KAAK,QAAQ;AAAG,WAAO;AACjF,SAAO;AACX;;;;;;;;;;;;;;;ACFM,SAAU,eAAe,QAAc;AACzC,QAAM,WAA8C,CAAC,EAAE,SAAQ,MAAO,SAAS,MAAM;AACrF,MAAI;AACC,WAA8B,cAAc,IAAI,oBAAoB,QAAQ,CAAC;EAClF,SAAS,OAAO;AACZ,YAAQ,MAAM,mEAAmE,KAAK;EAC1F;AACA,MAAI;AACC,WAA8B,iBAAiB,6BAA6B,CAAC,EAAE,QAAQ,IAAG,MACvF,SAAS,GAAG,CAAC;EAErB,SAAS,OAAO;AACZ,YAAQ,MAAM,iEAAiE,KAAK;EACxF;AACJ;AAEA,IAAM,sBAAN,cAAkC,MAAK;EAGnC,IAAI,SAAM;AACN,WAAO,uBAAA,MAAI,6BAAA,GAAA;EACf;EAEA,IAAI,OAAI;AACJ,WAAO;EACX;EAEA,YAAY,UAA2C;AACnD,UAAM,mCAAmC;MACrC,SAAS;MACT,YAAY;MACZ,UAAU;KACb;AAfI,gCAAA,IAAA,MAAA,MAAA;AAgBL,2BAAA,MAAI,6BAAW,UAAQ,GAAA;EAC3B;;EAGA,iBAAc;AACV,UAAM,IAAI,MAAM,iCAAiC;EACrD;;EAGA,2BAAwB;AACpB,UAAM,IAAI,MAAM,2CAA2C;EAC/D;;EAGA,kBAAe;AACX,UAAM,IAAI,MAAM,kCAAkC;EACtD;;;;;;;;;;;;;;;;;;;;;;AC3DE,IAAO,wBAAP,MAAO,uBAAqB;;EAS9B,IAAI,UAAO;AACP,WAAOA,wBAAA,MAAI,gCAAA,GAAA;EACf;;EAGA,IAAI,YAAS;AACT,WAAOA,wBAAA,MAAI,kCAAA,GAAA,EAAY,MAAK;EAChC;;EAGA,IAAI,SAAM;AACN,WAAOA,wBAAA,MAAI,+BAAA,GAAA,EAAS,MAAK;EAC7B;;EAGA,IAAI,WAAQ;AACR,WAAOA,wBAAA,MAAI,iCAAA,GAAA,EAAW,MAAK;EAC/B;;EAGA,IAAI,QAAK;AACL,WAAOA,wBAAA,MAAI,8BAAA,GAAA;EACf;;EAGA,IAAI,OAAI;AACJ,WAAOA,wBAAA,MAAI,6BAAA,GAAA;EACf;;;;;;EAOA,YAAY,SAAsB;AA1CzB,mCAAA,IAAA,MAAA,MAAA;AACA,qCAAA,IAAA,MAAA,MAAA;AACA,kCAAA,IAAA,MAAA,MAAA;AACA,oCAAA,IAAA,MAAA,MAAA;AACA,iCAAA,IAAA,MAAA,MAAA;AACA,gCAAA,IAAA,MAAA,MAAA;AAsCL,QAAI,eAAe,wBAAuB;AACtC,aAAO,OAAO,IAAI;IACtB;AAEA,IAAAC,wBAAA,MAAI,gCAAY,QAAQ,SAAO,GAAA;AAC/B,IAAAA,wBAAA,MAAI,kCAAc,QAAQ,UAAU,MAAK,GAAE,GAAA;AAC3C,IAAAA,wBAAA,MAAI,+BAAW,QAAQ,OAAO,MAAK,GAAE,GAAA;AACrC,IAAAA,wBAAA,MAAI,iCAAa,QAAQ,SAAS,MAAK,GAAE,GAAA;AACzC,IAAAA,wBAAA,MAAI,8BAAU,QAAQ,OAAK,GAAA;AAC3B,IAAAA,wBAAA,MAAI,6BAAS,QAAQ,MAAI,GAAA;EAC7B;;;AAaE,SAAUC,aAAe,GAAe,GAAa;AACvD,MAAI,MAAM;AAAG,WAAO;AAEpB,QAAM,SAAS,EAAE;AACjB,MAAI,WAAW,EAAE;AAAQ,WAAO;AAEhC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG,aAAO;EAC9B;AAEA,SAAO;AACX;AAYM,SAAUC,YAAW,GAAuB,GAAqB;AACnE,SAAOD,aAAY,GAAG,CAAC;AAC3B;", "names": ["__classPrivateFieldGet", "__classPrivateFieldSet", "arraysEqual", "bytesEqual"]}