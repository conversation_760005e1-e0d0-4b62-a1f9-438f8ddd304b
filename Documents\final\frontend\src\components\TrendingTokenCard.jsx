import React from 'react';
import { useNavigate } from 'react-router-dom';

const TrendingTokenCard = ({ token }) => {
  const navigate = useNavigate();

  const handleCardClick = () => {
    navigate(`/trade/${token.id}`);
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return `$${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `$${(num / 1000).toFixed(1)}K`;
    }
    return `$${num}`;
  };

  return (
    <div className="trending-card" onClick={handleCardClick}>
      <div className="trending-card-image">
        <img
          src={token.image || '/default-token.png'}
          alt={token.name}
          onError={(e) => {
            e.target.src = '/default-token.png';
          }}
        />
      </div>

      <div className="trending-card-content">
        <div className="trending-card-header">
          <h3 className="trending-card-name">{token.name}</h3>
          <span className="trending-card-symbol">{token.symbol}</span>
        </div>

        <div className="trending-card-stats">
          <div className="trending-stat">
            <span className="trending-stat-label">Market Cap</span>
            <span className="trending-stat-value">{formatNumber(token.marketCap || 0)}</span>
          </div>
          <div className="trending-stat">
            <span className="trending-stat-label">Holders</span>
            <span className="trending-stat-value">{token.holders || 0}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrendingTokenCard;
