{"name": "postcss-discard-empty", "version": "5.1.1", "description": "Discard empty rules and values with PostCSS.", "main": "src/index.js", "types": "types/index.d.ts", "files": ["src", "LICENSE-MIT", "types"], "keywords": ["compress", "css", "empty", "minify", "optimisation", "postcss", "postcss-plugin"], "license": "MIT", "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "devDependencies": {"postcss": "^8.2.15"}, "peerDependencies": {"postcss": "^8.2.15"}, "readme": "# [postcss][postcss]-discard-empty\n\n> Discard empty rules and values with PostCSS.\n\n## Install\n\nWith [npm](https://npmjs.org/package/postcss-discard-empty) do:\n\n```\nnpm install postcss-discard-empty --save\n```\n\n## Example\n\nFor more examples see the [tests](src/__tests__/index.js).\n\n### Input\n\n```css\n@font-face;\nh1 {}\n{color:blue}\nh2 {color:}\nh3 {color:red}\n```\n\n### Output\n\n```css\nh3 {color:red}\n```\n\n## Usage\n\nSee the [PostCSS documentation](https://github.com/postcss/postcss#usage) for\nexamples for your environment.\n\n\n## Contributors\n\nSee [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).\n\n\n## License\n\nMIT © [<PERSON>](http://beneb.info)\n\n\n[postcss]: https://github.com/postcss/postcss\n"}