/**
 * Central Database Initialization for WB-Swap
 *
 * This file initializes and connects to the databases used by the platform:
 * - MongoDB: All application data (users, tokens, transactions, sessions)
 */

const mongodbRobust = require('./mongodb_robust');
const mongooseConnection = require('./mongoose_connection');
const mongoose = require('mongoose');
// const postgres = require('./postgres'); // REMOVED - PostgreSQL no longer used
// const redis = require('./redis'); // REMOVED - Redis no longer used

// Configuration for database roles in the platform
const dbConfig = {
  // Core databases - always initialized
  mongodb: true, // MongoDB - REQUIRED - for all application data
  // redis: false,   // REMOVED - Redis no longer used
  // postgres: false, // REMOVED - PostgreSQL no longer used
};

// Initialize all databases
async function initializeDatabases() {
  console.log('Initializing databases...');

  const results = {
    initialized: [],
    failed: []
  };

  try {
    // Connect to MongoDB (REQUIRED)
    if (dbConfig.mongodb) {
      try {
        console.log('Initializing MongoDB - REQUIRED COMPONENT...');

        // Use the robust MongoDB connection manager
        const { client, db, gridFSBucket } = await mongodbRobust.connectToMongoDB();
        results.initialized.push('MongoDB');

        // Set up connection state monitoring
        mongodbRobust.connectionEvents.on('connected', () => {
          console.log('MongoDB connection re-established');
        });

        mongodbRobust.connectionEvents.on('connection_failed', (error) => {
          console.error('MongoDB connection failed:', error.message);
        });

        // Verify connection state
        const connectionState = mongodbRobust.getConnectionState();
        console.log('MongoDB connection state:', connectionState,
          connectionState === mongodbRobust.CONNECTION_STATES.CONNECTED ? '(Connected)' :
          connectionState === mongodbRobust.CONNECTION_STATES.CONNECTING ? '(Connecting)' :
          connectionState === mongodbRobust.CONNECTION_STATES.DISCONNECTED ? '(Disconnected)' :
          connectionState === mongodbRobust.CONNECTION_STATES.DISCONNECTING ? '(Disconnecting)' : 'Unknown');

        console.log('MongoDB initialized successfully');

        // Initialize Mongoose connection
        console.log('Initializing Mongoose connection...');
        await mongooseConnection.initializeMongoose();
        results.initialized.push('Mongoose');

        // Verify Mongoose connection state
        const mongooseState = mongooseConnection.getConnectionState();
        console.log('Mongoose connection state:', mongooseState,
          mongooseState === 1 ? '(Connected)' :
          mongooseState === 2 ? '(Connecting)' :
          mongooseState === 0 ? '(Disconnected)' :
          mongooseState === 3 ? '(Disconnecting)' : 'Unknown');

        console.log('Mongoose initialized successfully');
      } catch (mongoError) {
        console.error('MongoDB initialization failed:', mongoError.message);
        console.error('MongoDB error stack:', mongoError.stack);
        results.failed.push('MongoDB');

        // MongoDB is required - throw an error to stop the server
        throw new Error('Failed to connect to MongoDB. This is a required component.');
      }
    }

    // Redis and PostgreSQL removed - using MongoDB only
    // try {
    //   if (dbConfig.redis) {
    //     console.log('Initializing Redis for session management & real-time caching...');
    //     await redis.initializeRedis();
    //     results.initialized.push('Redis');
    //     console.log('Redis initialized successfully - session management ready');
    //   }
    // } catch (error) {
    //   console.error('Redis initialization failed:', error);
    //   results.failed.push('Redis');
    //   throw new Error('Failed to connect to Redis. Session management & real-time features unavailable.');
    // }

    // Connect to PostgreSQL (for structured user data, KYC details)
    // if (dbConfig.postgres) {
    //   try {
    //     console.log('Initializing PostgreSQL for structured user data & KYC details...');
    //     await postgres.initializePostgres();
    //     results.initialized.push('PostgreSQL');
    //     console.log('PostgreSQL initialized successfully - structured user data ready');
    //   } catch (error) {
    //     console.error('PostgreSQL initialization failed:', error);
    //     results.failed.push('PostgreSQL');
    //     console.warn('PostgreSQL unavailable - structured user data & KYC features limited');
    //   }
    // }

    // No additional database initialization needed

    console.log('Database initialization complete');
    console.log('Successfully initialized:', results.initialized.join(', '));

    if (results.failed.length > 0) {
      console.warn('Failed to initialize:', results.failed.join(', '));
      // Continue anyway for development purposes
    }

    return {
      success: results.failed.length === 0,
      ...results
    };
  } catch (error) {
    console.error('Database initialization error:', error);
    return {
      success: false,
      error: error.message,
      ...results
    };
  }
}

// Graceful shutdown of all database connections
async function shutdownDatabases() {
  console.log('Shutting down database connections...');

  try {
    // MongoDB shutdown
    if (dbConfig.mongodb) {
      await mongoose.connection.close();
      console.log('MongoDB connection closed');
    }

    // Redis and PostgreSQL shutdown removed - using MongoDB only
    // if (dbConfig.redis) {
    //   const standardClient = redis.getStandardClient();
    //   if (standardClient) {
    //     await standardClient.quit();
    //     console.log('Redis connection closed');
    //   }
    // }

    // PostgreSQL shutdown
    // if (dbConfig.postgres && postgres.pgPool) {
    //   await postgres.pgPool.end();
    //   console.log('PostgreSQL connection closed');
    // }

    // No additional database shutdown needed

    console.log('All database connections closed successfully');
    return true;
  } catch (error) {
    console.error('Error during database shutdown:', error);
    return false;
  }
}

// Health check function for all databases
async function checkDatabaseHealth() {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    databases: {}
  };

  try {
    // MongoDB health check
    if (dbConfig.mongodb) {
      try {
        const isConnected = mongoose.connection.readyState === 1;
        health.databases.mongodb = {
          status: isConnected ? 'connected' : 'disconnected',
          healthy: isConnected
        };

        if (!isConnected) {
          health.status = 'degraded';
        }
      } catch (error) {
        health.databases.mongodb = {
          status: 'error',
          error: error.message,
          healthy: false
        };
        health.status = 'degraded';
      }
    }

    // Redis and PostgreSQL health checks removed - using MongoDB only
    // if (dbConfig.redis) {
    //   try {
    //     const upstashResponse = await redis.upstashClient.ping();
    //     const standardClient = redis.getStandardClient();
    //     const standardResponse = standardClient ? await standardClient.ping() : null;

    //     health.databases.redis = {
    //       status: upstashResponse ? 'connected' : 'disconnected',
    //       upstash: !!upstashResponse,
    //       standard: !!standardResponse,
    //       healthy: !!upstashResponse
    //     };

    //     if (!upstashResponse) {
    //       health.status = 'degraded';
    //     }
    //   } catch (error) {
    //     health.databases.redis = {
    //       status: 'error',
    //       error: error.message,
    //       healthy: false
    //     };
    //     health.status = 'degraded';
    //   }
    // }

    // PostgreSQL health check
    // if (dbConfig.postgres) {
    //   try {
    //     const isConnected = await postgres.testPostgresConnection();
    //     health.databases.postgres = {
    //       status: isConnected ? 'connected' : 'disconnected',
    //       healthy: isConnected
    //     };

    //     if (!isConnected) {
    //       health.status = 'degraded';
    //     }
    //   } catch (error) {
    //     health.databases.postgres = {
    //       status: 'error',
    //       error: error.message,
    //       healthy: false
    //     };
    //     health.status = 'degraded';
    //   }
    // }

    // No additional health checks needed

    return health;
  } catch (error) {
    console.error('Error checking database health:', error);
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}

module.exports = {
  initializeDatabases,
  shutdownDatabases,
  checkDatabaseHealth,
  databases: {
    mongodb: mongodbRobust,
    mongoose: mongooseConnection,
    // postgres, // REMOVED - PostgreSQL no longer used
    // redis // REMOVED - Redis no longer used
  },
  dbConfig
};