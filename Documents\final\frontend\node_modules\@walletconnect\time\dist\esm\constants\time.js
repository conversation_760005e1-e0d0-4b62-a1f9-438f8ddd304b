export const ONE_SECOND = 1;
export const FIVE_SECONDS = 5;
export const TEN_SECONDS = 10;
export const THIRTY_SECONDS = 30;
export const SIXTY_SECONDS = 60;
export const ONE_MINUTE = SIXTY_SECONDS;
export const FIVE_MINUTES = ONE_MINUTE * 5;
export const TEN_MINUTES = ONE_MINUTE * 10;
export const THIRTY_MINUTES = ONE_MINUTE * 30;
export const SIXTY_MINUTES = ONE_MINUTE * 60;
export const ONE_HOUR = SIXTY_MINUTES;
export const THREE_HOURS = ONE_HOUR * 3;
export const SIX_HOURS = ONE_HOUR * 6;
export const TWELVE_HOURS = ONE_HOUR * 12;
export const TWENTY_FOUR_HOURS = ONE_HOUR * 24;
export const ONE_DAY = TWENTY_FOUR_HOURS;
export const THREE_DAYS = ONE_DAY * 3;
export const FIVE_DAYS = ONE_DAY * 5;
export const SEVEN_DAYS = ONE_DAY * 7;
export const THIRTY_DAYS = ONE_DAY * 30;
export const ONE_WEEK = SEVEN_DAYS;
export const TWO_WEEKS = ONE_WEEK * 2;
export const THREE_WEEKS = ONE_WEEK * 3;
export const FOUR_WEEKS = ONE_WEEK * 4;
export const ONE_YEAR = ONE_DAY * 365;
//# sourceMappingURL=time.js.map