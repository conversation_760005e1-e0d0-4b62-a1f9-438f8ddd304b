"use strict";var e=require("postcss-value-parser"),t=require("fs"),n=require("path"),s=require("postcss");function r(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var s=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,s.get?s:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var i=r(e),a=r(t),c=r(n);function u(e,t){const n=[];let s="",r=!1,o=0,i=-1;for(;++i<e.length;){const a=e[i];"("===a?o+=1:")"===a?o>0&&(o-=1):0===o&&(t&&p.test(s+a)?r=!0:t||","!==a||(r=!0)),r?(n.push(t?new l(s+a):new d(s)),s="",r=!1):s+=a}return""!==s&&n.push(t?new l(s):new d(s)),n}class f{constructor(e){this.nodes=u(e)}invert(){return this.nodes.forEach((e=>{e.invert()})),this}clone(){return new f(String(this))}toString(){return this.nodes.join(",")}}class d{constructor(e){const[,t,n,s]=e.match(m),[,r="",o=" ",i="",a="",c="",f="",d="",l=""]=n.match(w)||[],p={before:t,after:s,afterModifier:o,originalModifier:r||"",beforeAnd:a,and:c,beforeExpression:f},h=u(d||l,!0);Object.assign(this,{modifier:r,type:i,raws:p,nodes:h})}clone(e){const t=new d(String(this));return Object.assign(t,e),t}invert(){return this.modifier=this.modifier?"":this.raws.originalModifier,this}toString(){const{raws:e}=this;return`${e.before}${this.modifier}${this.modifier?`${e.afterModifier}`:""}${this.type}${e.beforeAnd}${e.and}${e.beforeExpression}${this.nodes.join("")}${this.raws.after}`}}class l{constructor(e){const[,t,n="",s="",r=""]=e.match(p)||[null,e],o={after:n,and:s,afterAnd:r};Object.assign(this,{value:t,raws:o})}clone(e){const t=new l(String(this));return Object.assign(t,e),t}toString(){const{raws:e}=this;return`${this.value}${e.after}${e.and}${e.afterAnd}`}}const p=new RegExp("^([\\W\\w]+)(?:(?:(\\s+)(and))(\\s+))$","i"),m=new RegExp("^(\\s*)([\\W\\w]*)(\\s*)$"),w=new RegExp("^(?:(not|only)(\\s+))?(?:(all|print|screen|speech)(?:(?:(\\s+)(and))(\\s+)([\\W\\w]+))?|([\\W\\w]+))$","i");var h=e=>new f(e),g=(e,t)=>{const n={};return e.nodes.slice().forEach((e=>{if("atrule"!==e.type)return;if("custom-media"!==e.name.toLowerCase())return;let s=null;try{s=i.default(e.params)}catch(e){return}if(!s||!s.nodes||!s.nodes.length)return;let r=-1;for(let e=0;e<s.nodes.length;e++){const t=s.nodes[e];if("space"!==t.type&&"comment"!==t.type){if("word"===t.type&&t.value.startsWith("--")){r=e;break}return}}if(r<0)return;const o=s.nodes[r].value.trim(),a=i.default.stringify(s.nodes.slice(r+1)).trim();n[o]=h(a),Object(t).preserve||e.remove()})),n};function b(e){const t=Object.assign({},Object(e).customMedia,Object(e)["custom-media"]);for(const e in t)t[e]=h(t[e]);return t}function y(e){return e.map((e=>{if(e instanceof Promise)return e;if(e instanceof Function)return e();const t=e===Object(e)?e:{from:String(e)};if(Object(t).customMedia||Object(t)["custom-media"])return t;const n=c.default.resolve(String(t.from||""));return{type:(t.type||c.default.extname(n).slice(1)).toLowerCase(),from:n}})).reduce((async(e,t)=>{const{type:n,from:r}=await t;return"css"===n||"pcss"===n?Object.assign(await e,await async function(e){const t=await j(e),n=s.parse(t,{from:e});return g(n,{preserve:!0})}(r)):"js"===n?Object.assign(await e,await async function(e){var t;return b(await(t=e,Promise.resolve().then((function(){return o(require(t))}))))}(r)):"json"===n?Object.assign(await e,await async function(e){return b(await O(e))}(r)):Object.assign(await e,b(await t))}),{})}const j=e=>new Promise(((t,n)=>{a.default.readFile(e,"utf8",((e,s)=>{e?n(e):t(s)}))})),O=async e=>JSON.parse(await j(e));function v(e){if(!e)return;let t=null;try{t=i.default(e)}catch(e){return}if(!t||!t.nodes||!t.nodes.length)return;if(1!==t.nodes.length)return;for(;"function"===t.nodes[0].type&&""===t.nodes[0].value;)t=t.nodes[0];let n=-1;for(let e=0;e<t.nodes.length;e++){const s=t.nodes[e];if("space"!==s.type&&"comment"!==s.type){if("word"===s.type&&s.value.startsWith("--")){n=e;break}return}}return n<0?void 0:t.nodes[n].value.trim()}function $(e,t){let n=e.nodes.length-1;for(;n>=0;){const s=S(e.nodes[n],t);s.length&&e.nodes.splice(n,1,...s),--n}return e}function S(e,t){const n=[];for(const s in e.nodes){const{value:r,nodes:o}=e.nodes[s],i=v(r);if(i&&i in t){for(const r of t[i].nodes){const o=e.modifier!==r.modifier?e.modifier||r.modifier:"",a=e.clone({modifier:o,raws:!o||e.modifier?{...e.raws}:{...r.raws},type:e.type||r.type});a.type===r.type&&Object.assign(a.raws,{and:r.raws.and,beforeAnd:r.raws.beforeAnd,beforeExpression:r.raws.beforeExpression}),a.nodes.splice(s,1,...r.clone().nodes.map((t=>(e.nodes[s].raws.and&&(t.raws={...e.nodes[s].raws}),t.spaces={...e.nodes[s].spaces},t))));const c=S(a,x(t,i));c.length?n.push(...c):n.push(a)}return n}o&&o.length&&$(e.nodes[s],t)}return n}const x=(e,t)=>{const n=Object.assign({},e);return delete n[t],n};function M(e,t){return Promise.all(t.map((async t=>{if(t instanceof Function)await t(E(e));else{const n=t===Object(t)?t:{to:String(t)},s=n.toJSON||E;if("customMedia"in n)n.customMedia=s(e);else if("custom-media"in n)n["custom-media"]=s(e);else{const t=String(n.to||""),r=(n.type||c.default.extname(t).slice(1)).toLowerCase(),o=s(e);"css"===r&&await async function(e,t){const n=`${Object.keys(t).reduce(((e,n)=>(e.push(`@custom-media ${n} ${t[n]};`),e)),[]).join("\n")}\n`;await P(e,n)}(t,o),"js"===r&&await async function(e,t){const n=`module.exports = {\n\tcustomMedia: {\n${Object.keys(t).reduce(((e,n)=>(e.push(`\t\t'${k(n)}': '${k(t[n])}'`),e)),[]).join(",\n")}\n\t}\n};\n`;await P(e,n)}(t,o),"json"===r&&await async function(e,t){const n=`${JSON.stringify({"custom-media":t},null,"\t")}\n`;await P(e,n)}(t,o),"mjs"===r&&await async function(e,t){const n=`export const customMedia = {\n${Object.keys(t).reduce(((e,n)=>(e.push(`\t'${k(n)}': '${k(t[n])}'`),e)),[]).join(",\n")}\n};\n`;await P(e,n)}(t,o)}}})))}const E=e=>Object.keys(e).reduce(((t,n)=>(t[n]=String(e[n]),t)),{}),P=(e,t)=>new Promise(((n,s)=>{a.default.writeFile(e,t,(e=>{e?s(e):n()}))})),k=e=>e.replace(/\\([\s\S])|(')/g,"\\$1$2").replace(/\n/g,"\\n").replace(/\r/g,"\\r"),A=e=>{const t="preserve"in Object(e)&&Boolean(e.preserve),n=[].concat(Object(e).importFrom||[]),s=[].concat(Object(e).exportTo||[]),r=y(n),o=Symbol("customMediaHelper");return{postcssPlugin:"postcss-custom-media",Once:async(e,n)=>{n[o]=Object.assign(await r,g(e,{preserve:t})),await M(n[o],s)},AtRule:(e,n)=>{"media"===e.name&&((e,t,{preserve:n})=>{if(e.params.indexOf("--")>-1){const s=h(e.params),r=String($(s,t));if(null===r)return;if(r===e.params)return;e.cloneBefore({params:r}),n||e.remove()}})(e,n[o],{preserve:t})}}};A.postcss=!0,module.exports=A;
