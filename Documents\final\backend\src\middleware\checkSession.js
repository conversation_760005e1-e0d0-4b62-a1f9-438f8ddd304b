/**
 * Middleware to check if user session is valid
 */

const jwt = require('jsonwebtoken');
const redis = require('../config/redis');

/**
 * Check if the user's session is valid
 * This middleware should be used after the auth middleware
 */
const checkSession = async (req, res, next) => {
  try {
    // If no user is authenticated, skip session check
    if (!req.user) {
      return next();
    }

    const userId = req.user.id;
    const sessionId = req.cookies?.sessionId;

    // If no session ID is provided, continue (will be handled by auth middleware)
    if (!sessionId) {
      return next();
    }

    // Check if session exists in Redis
    if (redis.client) {
      const sessionKey = `session:${userId}:${sessionId}`;
      const session = await redis.client.get(sessionKey);

      if (!session) {
        return res.status(401).json({ 
          message: 'Your session has expired. Please log in again.',
          code: 'SESSION_EXPIRED'
        });
      }

      // Update session expiry
      await redis.client.expire(session<PERSON><PERSON>, 60 * 60 * 24); // 24 hours
    }

    // Continue to the next middleware
    next();
  } catch (error) {
    console.error('Session check error:', error);
    next();
  }
};

module.exports = checkSession;
