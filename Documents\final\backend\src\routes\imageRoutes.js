const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const { conn, gfs: gridFsInstance } = require('../config/gridfs');
const User = require('../models/User');

// GridFS bucket ref
let gfs;
if (conn) {
  conn.once('open', () => {
    gfs = new mongoose.mongo.GridFSBucket(conn.db, {
      bucketName: 'profilePictures'
    });
  });
} else {
  console.warn('GridFS not available in imageRoutes due to missing MongoDB connection');
}

// @route GET /api/images/:filename
// @desc Get image by filename
router.get('/:filename', async (req, res) => {
  try {
    // Check if GridFS is available
    if (!conn || !gfs) {
      // Try to serve from local filesystem instead
      const fs = require('fs');
      const path = require('path');
      const filePath = path.join(__dirname, '../../uploads', req.params.filename);

      if (fs.existsSync(filePath)) {
        return res.sendFile(filePath);
      } else {
        return res.status(404).json({ message: 'No image found' });
      }
    }

    // If GridFS is available, use it
    // First try to find the file by filename
    const file = await conn.db.collection('profilePictures.files').findOne({ filename: req.params.filename });

    // Check if file exists
    if (!file || file.length === 0) {
      return res.status(404).json({ message: 'No image found' });
    }

    // Check if it's an image
    if (file.contentType && !file.contentType.startsWith('image/')) {
      return res.status(400).json({ message: 'Not an image' });
    }

    // Set the content type
    res.set('Content-Type', file.contentType || 'image/jpeg');

    // Create a read stream from GridFS
    const readStream = gfs.openDownloadStream(file._id);

    // Pipe the data to the response
    readStream.pipe(res);
  } catch (error) {
    console.error('Error retrieving image:', error);
    res.status(500).json({ message: 'Server error retrieving image' });
  }
});

// @route GET /api/images/profile/:filename
// @desc Get profile picture by filename
router.get('/profile/:filename', async (req, res) => {
  try {
    // Check if GridFS is available
    if (!conn || !gfs) {
      // Try to serve from local filesystem instead
      const fs = require('fs');
      const path = require('path');
      const filePath = path.join(__dirname, '../../uploads', req.params.filename);

      if (fs.existsSync(filePath)) {
        return res.sendFile(filePath);
      } else {
        return res.status(404).json({ message: 'No profile picture found' });
      }
    }

    // If GridFS is available, use it
    // First try to find the file by filename
    const file = await conn.db.collection('profilePictures.files').findOne({ filename: req.params.filename });

    // Check if file exists
    if (!file || file.length === 0) {
      return res.status(404).json({ message: 'No profile picture found' });
    }

    // Check if it's an image
    if (file.contentType && !file.contentType.startsWith('image/')) {
      return res.status(400).json({ message: 'Not an image' });
    }

    // Set the content type
    res.set('Content-Type', file.contentType || 'image/jpeg');

    // Create a read stream from GridFS
    const readStream = gfs.openDownloadStream(file._id);

    // Pipe the data to the response
    readStream.pipe(res);
  } catch (error) {
    console.error('Error retrieving profile picture:', error);
    res.status(500).json({ message: 'Server error retrieving profile picture' });
  }
});

// @route GET /api/images/user/:userId
// @desc Get profile picture by user ID
router.get('/user/:userId', async (req, res) => {
  try {
    // Find the user to get their profile picture URL
    const user = await User.findById(req.params.userId);

    if (!user || !user.profilePicture) {
      return res.status(404).json({ message: 'User or profile picture not found' });
    }

    // Extract the filename from the profile picture URL
    const urlParts = user.profilePicture.split('/');
    const filename = urlParts[urlParts.length - 1];

    // Redirect to the profile picture endpoint
    res.redirect(`/api/images/profile/${filename}`);
  } catch (error) {
    console.error('Error retrieving user profile picture:', error);
    res.status(500).json({ message: 'Server error retrieving user profile picture' });
  }
});

module.exports = router;
