# Balance Integration Scripts

This directory contains scripts for integrating user balances with the PostgreSQL database and migrating from wallet-based to user ID-based system.

## createBalanceRecords.js

This script creates balance records in PostgreSQL for all existing users in MongoDB who don't already have a balance record.

### Usage

```bash
node src/scripts/createBalanceRecords.js
```

### What it does

1. Connects to both MongoDB and PostgreSQL databases
2. Retrieves all users from MongoDB
3. For each user, checks if they have a balance record in PostgreSQL
4. If not, creates a balance record with an initial balance of 0 SOL
5. Logs the results (created, already existing, errors)

## migrateOrdersToUserIds.js

This script migrates existing orders from using wallet addresses to using user IDs.

### Usage

```bash
node src/scripts/migrateOrdersToUserIds.js
```

### What it does

1. Connects to both MongoDB and PostgreSQL databases
2. Retrieves all users from MongoDB
3. Creates a map of wallet addresses to user IDs
4. Retrieves all orders from PostgreSQL
5. Updates each order to use the user ID instead of the wallet address
6. Logs the results (updated, skipped, errors)

## Changes Made to the System

The following changes have been made to integrate user balances with the PostgreSQL database and remove wallet connections:

1. **User Registration**: When a user registers, a balance record is automatically created in PostgreSQL with an initial balance of 0 SOL.

2. **Token Creation**: When a user creates a token, a fee of 0.1 SOL is deducted from their balance. If they don't have enough balance, the token creation fails.

3. **Trading**: When a user places a trade, a fee of 0.01 SOL is deducted from their balance. If they don't have enough balance, the trade fails.

4. **Wallet Connections Removed**: The system no longer requires users to connect their wallets. Instead, all transactions are processed through the platform's central wallet, and balances are tracked in the PostgreSQL database.

5. **User ID-Based System**: The system now uses user IDs instead of wallet addresses for all operations, including trading, deposits, and withdrawals.

## Fee Structure

- **Token Creation**: 0.1 SOL
- **Trading**: 0.01 SOL per trade

These fees can be adjusted in the respective route files:
- `backend/src/routes/tokenRoutes.js`
- `backend/src/routes/tradingRoutes.js`
