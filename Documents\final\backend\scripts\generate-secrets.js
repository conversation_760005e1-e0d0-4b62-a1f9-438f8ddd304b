/**
 * Secret Generator Script
 * 
 * This script generates secure random strings for use as JWT secrets, session secrets, etc.
 * Run this script when setting up a new environment to generate secure secrets.
 * 
 * Usage: node scripts/generate-secrets.js
 */

const crypto = require('crypto');

// Generate a secure random string of specified length
function generateSecureString(length = 64) {
  return crypto.randomBytes(length).toString('hex');
}

// Generate secrets
const jwtSecret = generateSecureString(32);
const cookieSecret = generateSecureString(32);
const sessionSecret = generateSecureString(32);

console.log('\n=== GENERATED SECRETS ===');
console.log('These secrets should be set in your production environment variables.');
console.log('DO NOT store these in your code or commit them to version control!\n');

console.log(`JWT_SECRET=${jwtSecret}`);
console.log(`COOKIE_SECRET=${cookieSecret}`);
console.log(`SESSION_SECRET=${sessionSecret}\n`);

console.log('=== INSTRUCTIONS ===');
console.log('1. Copy these values to your production environment variables');
console.log('2. In your hosting platform (e.g., Render, Heroku, AWS), set these as environment variables');
console.log('3. Never share these secrets or commit them to your repository\n');
