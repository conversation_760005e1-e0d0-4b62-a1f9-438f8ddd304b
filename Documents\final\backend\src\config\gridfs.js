const mongoose = require('mongoose');
const { GridFsStorage } = require('multer-gridfs-storage');
const multer = require('multer');
const crypto = require('crypto');
const path = require('path');

// MongoDB connection URL from environment variable
const mongoURI = process.env.MONGODB_URI;

// Create a MongoDB connection
let conn;
if (mongoURI) {
  try {
    conn = mongoose.createConnection(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error.message);
    conn = null;
  }
} else {
  console.warn('MongoDB URI not available, GridFS functionality will be limited');
  conn = null;
}

// Initialize GridFS storage
let gfs;
if (conn) {
  conn.once('open', () => {
    // Initialize stream
    gfs = new mongoose.mongo.GridFSBucket(conn.db, {
      bucketName: 'profilePictures'
    });
    console.log('GridFS connection established');
  });
} else {
  console.warn('GridFS not initialized due to missing MongoDB connection');
}

// Create storage engine for GridFS
let storage;

// Check if MongoDB URI is available
if (mongoURI) {
  storage = new GridFsStorage({
    url: mongoURI,
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true
    },
  file: (req, file) => {
    return new Promise((resolve, reject) => {
      // Generate a random filename
      crypto.randomBytes(16, (err, buf) => {
        if (err) {
          return reject(err);
        }
        const filename = buf.toString('hex') + path.extname(file.originalname);
        const fileInfo = {
          filename: filename,
          bucketName: 'profilePictures',
          metadata: {
            userId: req.user ? (req.user._id || req.user.id || 'unknown') : 'unknown',
            originalName: file.originalname,
            contentType: file.mimetype,
            uploadDate: new Date()
          }
        };
        resolve(fileInfo);
      });
    });
  }
});
} else {
  // Fallback to disk storage if MongoDB URI is not available
  console.warn('MongoDB URI not available, falling back to disk storage for file uploads');
  const diskStorage = multer.diskStorage({
    destination: function (req, file, cb) {
      const uploadDir = path.join(__dirname, '../../uploads');
      // Create directory if it doesn't exist
      if (!require('fs').existsSync(uploadDir)) {
        require('fs').mkdirSync(uploadDir, { recursive: true });
      }
      cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
      crypto.randomBytes(16, (err, buf) => {
        if (err) return cb(err);
        const filename = buf.toString('hex') + path.extname(file.originalname);
        cb(null, filename);
      });
    }
  });
  storage = diskStorage;
}

// Create multer upload middleware
const imageFilter = (req, file, cb) => {
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

const upload = multer({
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB max size
  fileFilter: imageFilter
});

// Export the upload middleware and GridFS stream
module.exports = {
  gfs,
  upload,
  conn
};
