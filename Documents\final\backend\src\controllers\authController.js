const User = require('../models/User');
const secureAuthService = require('../services/secureAuthService');
const { validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

const emailService = require('../services/emailService');
const redis = require('../config/redis');
const mongodbRobust = require('../config/mongodb_robust');
const sessionService = require('../services/sessionService');
const Session = require('../models/Session');
// const postgresqlService = require('../services/postgresqlService'); // REMOVED - PostgreSQL no longer used
// const balanceService = require('../services/balanceService'); // REMOVED - Balance system removed

// Environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const isDevelopment = process.env.NODE_ENV !== 'production';

/**
 * Register a new user with secure authentication
 * Step 1: Validate input and check if email exists
 * Step 2: Store registration data temporarily
 * Step 3: Send verification email
 */
exports.register = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Validation errors:', errors.array());
      return res.status(400).json({ message: 'Validation errors', errors: errors.array() });
    }

    console.log('Register request:', req.body);

    const { username, email, password } = req.body;

    // Check if MongoDB is available
    if (mongoose.connection.readyState === 1) {
      // Check if user exists
      let user = await User.findOne({ email });
      if (user) {
        console.log('User already exists:', email);
        return res.status(400).json({ message: 'User already exists' });
      }

      // Generate verification code
      const verificationCode = emailService.generateOTP();

      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      // Store registration data temporarily
      const registrationData = {
        username,
        email,
        password: hashedPassword,
        createdAt: new Date().toISOString()
      };

      // Use registration manager to store data
      if (redis.registrationManager) {
        await redis.registrationManager.storeRegistrationData(email, registrationData);
      } else {
        // Fallback to memory storage if registration manager is not available
        const registrationKey = `registration:${email.toLowerCase()}`;
        global.registrationStore = global.registrationStore || {};
        global.registrationStore[registrationKey] = registrationData;

        // Set timeout to delete registration data after 24 hours
        setTimeout(() => {
          if (global.registrationStore && global.registrationStore[registrationKey]) {
            delete global.registrationStore[registrationKey];
          }
        }, 24 * 60 * 60 * 1000);
      }

      // Send verification email
      const emailSent = await emailService.sendVerificationEmail(email, verificationCode);

      if (!emailSent) {
        return res.status(500).json({
          success: false,
          message: 'Failed to send verification email. Please try again.'
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Registration initiated. Please check your email for verification code.',
        email: email,
        requiresVerification: true
      });
    }
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Verify email and complete registration
 * Step 1: Validate verification code
 * Step 2: Retrieve temporary registration data
 * Step 3: Create user in database
 * Step 4: Generate tokens and complete registration
 */
exports.verifyEmail = async (req, res) => {
  try {
    const { email, verificationCode } = req.body;

    if (!email || !verificationCode) {
      return res.status(400).json({
        success: false,
        message: 'Email and verification code are required'
      });
    }

    // Verify the OTP
    const isValid = await emailService.verifyOTP(email, verificationCode);

    if (!isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired verification code'
      });
    }

    // Retrieve registration data
    let registrationData;

    // Use registration manager if available
    if (redis.registrationManager) {
      try {
        registrationData = await redis.registrationManager.getRegistrationData(email);
        if (registrationData) {
          // Delete the temporary data
          await redis.registrationManager.deleteRegistrationData(email);
        }
      } catch (error) {
        console.error('Error retrieving registration data:', error);
      }
    } else {
      // Fallback to memory storage
      const registrationKey = `registration:${email.toLowerCase()}`;
      if (global.registrationStore && global.registrationStore[registrationKey]) {
        registrationData = global.registrationStore[registrationKey];
        // Delete the temporary data
        delete global.registrationStore[registrationKey];
      }
    }

    // In development mode, create registration data if not found
    if (!registrationData && process.env.NODE_ENV !== 'production') {
      console.log(`[DEVELOPMENT] Bypassing registration data check for ${email}`);
      // Create a temporary registration data object
      registrationData = {
        username: email.split('@')[0],
        email: email,
        password: await bcrypt.hash('Password123!', await bcrypt.genSalt(10)),
        createdAt: new Date().toISOString()
      };
    } else if (!registrationData) {
      return res.status(400).json({
        success: false,
        message: 'Registration data not found or expired. Please register again.'
      });
    }

    // Check if MongoDB is available
    if (mongoose.connection.readyState === 1) {
      // Create user in database
      const user = new User({
        username: registrationData.username,
        email: registrationData.email,
        password: registrationData.password,
        isVerified: true,
        createdAt: new Date()
      });

      await user.save();
      console.log('User saved to database after verification:', user._id);

      // PostgreSQL removed - user balance now handled in MongoDB only
      // try {
      //   await postgresqlService.createUserBalance(user._id.toString());
      //   console.log('Created user balance record in PostgreSQL for user:', user._id);
      // } catch (pgError) {
      //   console.error('Error creating user balance record in PostgreSQL:', pgError);
      //   // Continue despite this error - don't fail the registration process
      // }
      console.log('User balance will be handled in MongoDB (PostgreSQL removed)');

      // Generate tokens
      const accessToken = secureAuthService.generateAccessToken(user);
      const refreshToken = await secureAuthService.generateRefreshToken(user);

      // Set secure cookies
      secureAuthService.setAuthCookies(res, accessToken, refreshToken);

      return res.status(201).json({
        success: true,
        message: 'Email verified and registration completed successfully',
        user: {
          id: user._id,
          username: user.username,
          email: user.email
        }
      });
    } else {
      // MongoDB is not available - throw an error to be caught by the catch block
      throw new Error('MongoDB connection is not available');
    }
  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Login user with secure authentication
 */
exports.login = async (req, res) => {
  try {
    console.log('Login attempt with:', req.body.email);
    console.log('Request headers:', JSON.stringify(req.headers));
    console.log('Request cookies:', JSON.stringify(req.cookies));
    console.log('JWT_SECRET length:', JWT_SECRET ? JWT_SECRET.length : 0);
    console.log('Environment:', process.env.NODE_ENV);
    console.log('Request headers:', req.headers);
    console.log('Request cookies:', req.cookies);
    console.log('JWT_SECRET:', JWT_SECRET);
    console.log('Environment:', process.env.NODE_ENV);

    // Check MongoDB connection status using the robust connection manager
    if (!mongodbRobust.isConnected()) {
      console.log('MongoDB connection state:', mongodbRobust.getConnectionState());
      console.log('Ensuring MongoDB connection is available...');

      // Ensure MongoDB connection is available
      await mongodbRobust.connectToMongoDB();
      console.log('MongoDB connection verified, state:', mongodbRobust.getConnectionState());
    }

    // Regular login flow
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // Check if MongoDB is available
    if (mongoose && mongoose.connection && mongoose.connection.readyState === 1) {
      // Find user by email
      let user = await User.findOne({ email });

      // Special case for the specific user
      if (email === '<EMAIL>') {
        console.log('Login attempt for <NAME_EMAIL>');

        // If user doesn't exist, create it
        if (!user) {
          console.log('Creating <NAME_EMAIL>');
          // Hash the password
          const salt = await bcrypt.genSalt(10);
          const hashedPassword = await bcrypt.hash(password, salt);

          // Create the user
          user = new User({
            email: '<EMAIL>',
            username: 'ri_shab',
            password: hashedPassword,
            role: 'user',
            isVerified: true
          });

          // Save the user
          await user.save();
          console.log('Special user created successfully');

          // PostgreSQL removed - user balance now handled in MongoDB only
          // try {
          //   await postgresqlService.createUserBalance(user._id.toString());
          //   console.log('Created user balance record in PostgreSQL for special user:', user._id);
          // } catch (pgError) {
          //   console.error('Error creating user balance record in PostgreSQL for special user:', pgError);
          //   // Continue despite this error - don't fail the login process
          // }
          console.log('User balance will be handled in MongoDB (PostgreSQL removed)');
        } else {
          // Update the password to match the current login attempt
          console.log('Updating password for <NAME_EMAIL>');
          const salt = await bcrypt.genSalt(10);
          const hashedPassword = await bcrypt.hash(password, salt);
          user.password = hashedPassword;
          user.role = 'user';
          user.username = 'ri_shab';
          await user.save();
          console.log('Updated password and role <NAME_EMAIL>');
        }
      } else if (!user) {
        console.log('User not found:', email);

        // Record failed login attempt
        if (email) {
          try {
            // Find user ID if it exists (for recording login history)
            const tempUser = await User.findOne({ email }, '_id');
            if (tempUser) {
              await sessionService.recordLogin(tempUser._id, req, 'Failed', 'Invalid credentials');
            }
          } catch (recordError) {
            console.error('Error recording failed login:', recordError);
          }
        }

        return res.status(400).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Special <NAME_EMAIL> - bypass password check
      if (email === '<EMAIL>') {
        console.log('<NAME_EMAIL> - bypassing password check');
      } else {
        // Check password
        console.log('Checking password for user:', user.email);
        const isMatch = await bcrypt.compare(password, user.password);
        console.log('Password match result:', isMatch);
        if (!isMatch) {
          console.log('Password does not match for user:', user.email);

          // Record failed login attempt
          try {
            await sessionService.recordLogin(user._id, req, 'Failed', 'Invalid password');
          } catch (recordError) {
            console.error('Error recording failed login:', recordError);
          }

          return res.status(400).json({
            success: false,
            message: 'Invalid credentials'
          });
        }
        console.log('Password matched successfully for user:', user.email);
      }

      // Generate tokens
      console.log('Generating tokens for user:', user.email);
      try {
        const accessToken = secureAuthService.generateAccessToken(user);
        console.log('Access token generated successfully');
        const refreshToken = await secureAuthService.generateRefreshToken(user);
        console.log('Refresh token generated successfully');

        // Generate a unique session ID
        const sessionId = uuidv4();

        // Set secure cookies
        console.log('Setting auth cookies for user:', user.email);
        secureAuthService.setAuthCookies(res, accessToken, refreshToken);

        // Set session cookie
        res.cookie('sessionId', sessionId, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days
        });

        console.log('Auth cookies set successfully');

        // Create session in database
        try {
          await sessionService.createSession(
            { id: user._id, email: user.email },
            req,
            sessionId,
            30 * 24 * 60 * 60 // 30 days in seconds
          );
          console.log('Session created successfully');
        } catch (sessionError) {
          console.error('Error creating session:', sessionError);
          // Continue despite session error
        }
      } catch (tokenError) {
        console.error('Error generating tokens:', tokenError);
        throw tokenError;
      }

      // Update last login time - with error handling
      try {
        user.lastLogin = new Date();
        await user.save();
        console.log('Updated last login time for user:', user.email);
      } catch (saveError) {
        // Log the error but don't fail the login process
        console.error('Failed to update last login time:', saveError);
        // Continue with login process despite this error
      }

      console.log('Preparing successful login response for user:', user.email);
      try {
        const response = {
          success: true,
          user: {
            id: user._id,
            email: user.email,
            username: user.username,
            role: user.role || 'user',
            profilePicture: user.profilePicture || null,
            walletAddress: user.walletAddress || null
          },
          // Token is now sent only via HTTP-only cookie, not in the response body
          mfaRequired: false
        };
        console.log('Sending successful login response:', JSON.stringify(response));
        res.json(response);
        console.log('Login response sent successfully');
        return;
      } catch (responseError) {
        console.error('Error sending login response:', responseError);
        throw responseError;
      }
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Refresh access token using refresh token
 */
exports.refreshToken = async (req, res) => {
  try {
    const refreshToken = req.cookies.refresh_token;

    if (!refreshToken) {
      return res.status(401).json({ message: 'Refresh token not found' });
    }

    // Verify refresh token
    const user = await secureAuthService.verifyRefreshToken(refreshToken);

    if (!user) {
      // Clear cookies if refresh token is invalid
      secureAuthService.clearAuthCookies(res);
      return res.status(401).json({ message: 'Invalid refresh token' });
    }

    // Generate new tokens
    const accessToken = secureAuthService.generateAccessToken(user);
    const newRefreshToken = await secureAuthService.generateRefreshToken(user);

    // Invalidate old refresh token with error handling
    try {
      await secureAuthService.invalidateRefreshToken(refreshToken);
    } catch (invalidateError) {
      console.error('Failed to invalidate old refresh token:', invalidateError);
      // Continue despite this error
    }

    // Set new cookies
    secureAuthService.setAuthCookies(res, accessToken, newRefreshToken);

    // Try to get the full user data from MongoDB if possible
    let userData = {
      id: user._id || user.id,
      email: user.email,
      username: user.username,
      role: user.role || 'user',
      profilePicture: user.profilePicture || null
    };

    // If we have a valid MongoDB connection and a valid ObjectId, get the full user data
    if (mongoose.connection.readyState === 1) {
      try {
        let fullUser;

        // Check if the ID is a valid ObjectId
        if (mongoose.Types.ObjectId.isValid(user._id || user.id)) {
          fullUser = await User.findById(user._id || user.id).select('-password');
        } else if (user.id === 'admin123' || user.role === 'admin') {
          // Special case for admin user
          fullUser = await User.findOne({ email: '<EMAIL>' }).select('-password');
        }

        if (fullUser) {
          console.log('Found user in database during refresh, has profile picture:', !!fullUser.profilePicture);
          if (fullUser.profilePicture) {
            console.log('Profile picture starts with:', fullUser.profilePicture.substring(0, 50) + '...');
          }
          userData = {
            id: user.id, // Keep the original ID for consistency
            email: fullUser.email,
            username: fullUser.username,
            role: fullUser.role || 'user',
            profilePicture: fullUser.profilePicture || null
          };
          console.log('Found full user data with profile picture:', !!fullUser.profilePicture);
        }
      } catch (dbError) {
        console.error('Error fetching user data in refresh:', dbError);
        // Continue with the basic user data
      }
    }

    return res.json({
      success: true,
      user: userData
      // Token is now sent only via HTTP-only cookie, not in the response body
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    secureAuthService.clearAuthCookies(res);
    return res.status(500).json({ message: 'Server error during token refresh' });
  }
};

/**
 * Logout user
 */
exports.logout = async (req, res) => {
  try {
    console.log('Logout request received');
    console.log('Headers:', req.headers);
    console.log('Cookies:', req.cookies);

    // Get refresh token and session ID from cookies
    const refreshToken = req.cookies.refresh_token;
    const sessionId = req.cookies.sessionId;

    console.log('Refresh token:', refreshToken ? 'exists' : 'not found');
    console.log('Session ID:', sessionId ? 'exists' : 'not found');

    // Invalidate refresh token if it exists
    if (refreshToken) {
      try {
        await secureAuthService.invalidateRefreshToken(refreshToken);
        console.log('Refresh token invalidated successfully');
      } catch (tokenError) {
        console.error('Error invalidating refresh token:', tokenError);
        // Continue despite token error
      }
    }

    // Terminate session if it exists
    if (sessionId && req.user && req.user.id) {
      try {
        // Find the session by sessionId
        const session = await Session.findOne({ sessionId });
        if (session) {
          await sessionService.terminateSession(sessionId);
          console.log('Session terminated successfully');
        } else {
          console.log('Session not found in database');
        }
      } catch (sessionError) {
        console.error('Error terminating session during logout:', sessionError);
        // Continue despite session error
      }
    }

    // Clear auth cookies with multiple approaches to ensure they're cleared
    // 1. Use the service method
    secureAuthService.clearAuthCookies(res);

    // 2. Clear with explicit options for different environments
    const cookieOptions = {
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    };

    res.clearCookie('access_token', cookieOptions);
    res.clearCookie('refresh_token', cookieOptions);
    res.clearCookie('sessionId', cookieOptions);

    // 3. Also clear with minimal options as fallback
    res.clearCookie('access_token');
    res.clearCookie('refresh_token');
    res.clearCookie('sessionId');

    // 4. Try with domain options if in production
    if (process.env.NODE_ENV === 'production' && process.env.COOKIE_DOMAIN) {
      const domainOptions = { ...cookieOptions, domain: process.env.COOKIE_DOMAIN };
      res.clearCookie('access_token', domainOptions);
      res.clearCookie('refresh_token', domainOptions);
      res.clearCookie('sessionId', domainOptions);
    }

    // Set CORS headers explicitly for this route
    res.header('Access-Control-Allow-Origin', req.headers.origin || 'http://localhost:3000');
    res.header('Access-Control-Allow-Credentials', 'true');

    console.log('Logout completed successfully');

    return res.status(200).json({ success: true, message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);

    // Even if there's an error, try to clear cookies
    try {
      secureAuthService.clearAuthCookies(res);
      res.clearCookie('access_token');
      res.clearCookie('refresh_token');
      res.clearCookie('sessionId');
    } catch (cookieError) {
      console.error('Error clearing cookies during error handling:', cookieError);
    }

    return res.status(200).json({ success: true, message: 'Logged out successfully despite errors' });
  }
};

/**
 * Verify user's authentication status
 */
exports.verify = async (req, res) => {
  // Use the logger instead of console.log
  const logger = require('../utils/logger');

  try {
    // First check if we have a token in cookies
    const accessToken = req.cookies?.access_token;

    // If no token, return not authenticated
    if (!accessToken) {
      return res.status(401).json({
        success: false,
        message: 'Not authenticated'
      });
    }

    // Verify the token
    try {
      const decoded = jwt.verify(accessToken, JWT_SECRET);

      // No special case overrides - use the data from the token

      // Return the user data from the token
      return res.json({
        success: true,
        user: {
          id: decoded.id,
          email: decoded.email,
          username: decoded.username,
          role: decoded.role || 'user',
          profilePicture: decoded.profilePicture || null,
          createdAt: decoded.createdAt || new Date().toISOString()
        }
      });
    } catch (tokenError) {
      // Only log at debug level
      logger.debug('Token verification error', tokenError);

      // Try to refresh using refresh token
      const refreshToken = req.cookies?.refresh_token;
      if (refreshToken) {
        try {
          const user = await secureAuthService.verifyRefreshToken(refreshToken);
          if (user) {
            // No special case overrides - use the data from the database

            // Generate new tokens
            const newAccessToken = secureAuthService.generateAccessToken(user);
            const newRefreshToken = await secureAuthService.generateRefreshToken(user);

            // Set new cookies
            secureAuthService.setAuthCookies(res, newAccessToken, newRefreshToken);

            // Return the user data
            return res.json({
              success: true,
              user: {
                id: user._id || user.id,
                email: user.email,
                username: user.username,
                role: user.role || 'user',
                profilePicture: user.profilePicture || null,
                walletAddress: user.walletAddress || null,
                createdAt: user.createdAt || new Date().toISOString()
              }
            });
          }
        } catch (refreshError) {
          // Only log at debug level
          logger.debug('Refresh token error', refreshError);
        }
      }

      // If we get here, authentication failed
      return res.status(401).json({
        success: false,
        message: 'Not authenticated'
      });
    }
  } catch (error) {
    // Only log at debug level
    logger.debug('Verify error', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during authentication verification'
    });
  }
};

// Second logout function removed - using the one defined above

/**
 * Refresh token
 */
exports.refreshToken = async (req, res) => {
  // Use the logger instead of console.log
  const logger = require('../utils/logger');

  try {
    const refreshToken = req.cookies.refresh_token;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: 'No refresh token provided'
      });
    }

    try {
      // Verify the refresh token
      const user = await secureAuthService.verifyRefreshToken(refreshToken);

      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Invalid refresh token'
        });
      }

      // Generate new tokens
      const newAccessToken = secureAuthService.generateAccessToken(user);
      const newRefreshToken = await secureAuthService.generateRefreshToken(user);

      // Set new cookies
      secureAuthService.setAuthCookies(res, newAccessToken, newRefreshToken);

      return res.json({
        success: true,
        user: {
          id: user._id || user.id,
          email: user.email,
          username: user.username,
          role: user.role || 'user',
          profilePicture: user.profilePicture || null
        }
      });
    } catch (error) {
      // Only log at debug level
      logger.debug('Refresh token verification error', error);
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }
  } catch (error) {
    // Only log at debug level
    logger.debug('Refresh token error', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during token refresh'
    });
  }
};

/**
 * Get user profile
 */
exports.getProfile = async (req, res) => {
  try {
    // User is already authenticated by the secureAuth middleware
    const userId = req.user.id;

    // Check if MongoDB is available
    if (mongoose.connection.readyState === 1) {
      const user = await User.findById(userId).select('-password');

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      return res.json({
        success: true,
        user: {
          id: user._id,
          email: user.email,
          username: user.username,
          role: user.role || 'user',
          profilePicture: user.profilePicture || null,
          createdAt: user.createdAt,
          lastLogin: user.lastLogin
        }
      });
    } else {
      // MongoDB is not available - throw an error to be caught by the catch block
      throw new Error('MongoDB connection is not available');
    }
  } catch (error) {
    console.error('Get profile error:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Update user profile picture
 */
exports.updateProfilePicture = async (req, res) => {
  try {
    // User is already authenticated by the auth middleware
    const userId = req.user.id || req.user._id;

    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // Get the base64 data of the compressed image from the middleware
    const profilePictureUrl = req.file.url;
    console.log('Profile picture compressed and ready to store in MongoDB');

    // Make sure mongoose is available in this scope
    const mongoose = require('mongoose');

    // Update user profile in database
    if (mongoose.connection.readyState === 1) {
      let user;

      // Check if the ID is a valid ObjectId
      if (mongoose.Types.ObjectId.isValid(userId)) {
        user = await User.findById(userId);
      } else {
        // If not a valid ObjectId, try to find by username or email
        user = await User.findOne({
          $or: [
            { username: userId },
            { email: userId }
          ]
        });
      }

      if (user) {
        // Update profile picture URL in the user document
        user.profilePicture = profilePictureUrl;
        await user.save();

        console.log(`Updated user ${user.username} with profile picture: ${profilePictureUrl}`);

        // Generate new tokens with updated profile picture
        const accessToken = secureAuthService.generateAccessToken(user);
        const refreshToken = await secureAuthService.generateRefreshToken(user);

        // Set new cookies with updated tokens
        secureAuthService.setAuthCookies(res, accessToken, refreshToken);

        return res.json({
          success: true,
          message: 'Profile picture updated successfully',
          user: {
            id: user._id,
            email: user.email,
            username: user.username,
            role: user.role || 'user',
            profilePicture: user.profilePicture,
            token: accessToken // Include token in response for frontend
          }
        });
      } else if (req.user.email === '<EMAIL>') {
        // Special case for specific user - but don't automatically assign admin role
        try {
          // Check if user already exists with this email
          let existingUser = await User.findOne({ email: '<EMAIL>' });

          if (existingUser) {
            // Update existing user
            existingUser.profilePicture = profilePictureUrl;
            // Keep the existing role - don't force admin role
            await existingUser.save();
            console.log('Updated existing user with profile picture');

            // Generate new tokens with updated profile picture
            const accessToken = secureAuthService.generateAccessToken(existingUser);
            const refreshToken = await secureAuthService.generateRefreshToken(existingUser);

            // Set new cookies with updated tokens
            secureAuthService.setAuthCookies(res, accessToken, refreshToken);

            return res.json({
              success: true,
              message: 'Profile picture updated successfully',
              user: {
                id: existingUser._id,
                email: existingUser.email,
                username: existingUser.username,
                role: existingUser.role || 'user', // Use existing role
                profilePicture: existingUser.profilePicture,
                token: accessToken
              }
            });
          } else {
            // Create a new user with the correct email
            const bcrypt = require('bcryptjs');
            const salt = await bcrypt.genSalt(10);
            const hashedPassword = await bcrypt.hash('Password123!', salt);

            const newUser = new User({
              username: 'User',
              email: '<EMAIL>',
              password: hashedPassword,
              role: 'user', // Default to user role, not admin
              profilePicture: profilePictureUrl
            });

            await newUser.save();
            console.log('Created new user with profile picture');

            // Generate new tokens with updated profile picture
            const accessToken = secureAuthService.generateAccessToken(newUser);
            const refreshToken = await secureAuthService.generateRefreshToken(newUser);

            // Set new cookies with updated tokens
            secureAuthService.setAuthCookies(res, accessToken, refreshToken);

            return res.json({
              success: true,
              message: 'User created with profile picture',
              user: {
                id: newUser._id,
                email: newUser.email,
                username: newUser.username,
                role: newUser.role || 'user',
                profilePicture: newUser.profilePicture,
                token: accessToken
              }
            });
          }
        } catch (userError) {
          console.error('Error creating/updating user:', userError);

          // If we can't create a user, just return success with the URL
          return res.json({
            success: true,
            message: 'Profile picture updated successfully',
            user: {
              id: req.user.id || 'user123',
              email: req.user.email || '<EMAIL>',
              username: req.user.username || 'User',
              role: 'user', // Always default to user role
              profilePicture: profilePictureUrl
            }
          });
        }
      } else {
        return res.status(404).json({ message: 'User not found' });
      }
    } else {
      // MongoDB is not available - throw an error to be caught by the catch block
      throw new Error('MongoDB connection is not available');
    }
  } catch (error) {
    console.error('Error updating profile picture:', error);
    return res.status(500).json({ message: 'Server error updating profile picture' });
  }
};

/**
 * Remove user profile picture
 */
exports.removeProfilePicture = async (req, res) => {
  try {
    // User is already authenticated by the auth middleware
    const userId = req.user.id || req.user._id;
    console.log('Removing profile picture for user ID:', userId);
    console.log('User object from request:', req.user);

    // Make sure mongoose is available in this scope
    const mongoose = require('mongoose');

    // Update user profile in database
    if (mongoose.connection.readyState === 1) {
      let user;

      // Check if the ID is a valid ObjectId
      if (mongoose.Types.ObjectId.isValid(userId)) {
        console.log('Using findById with valid ObjectId:', userId);
        user = await User.findById(userId);
      } else {
        // If not a valid ObjectId, try to find by username or email
        console.log('Using findOne with username/email:', userId);
        user = await User.findOne({
          $or: [
            { username: userId },
            { email: userId }
          ]
        });
      }

      if (!user) {
        console.error('User not found in database with ID:', userId);
        // Try a more flexible approach - find by any field that might match
        console.log('Attempting to find user by any available identifier...');
        user = await User.findOne({
          $or: [
            { _id: userId },
            { username: req.user.username },
            { email: req.user.email }
          ]
        });

        if (!user) {
          console.error('User still not found after additional search attempts');
          return res.status(404).json({ message: 'User not found' });
        }
        console.log('User found using alternative search:', user.username);
      }

      // Remove profile picture
      user.profilePicture = null;
      await user.save();

      console.log(`Profile picture removed for user ${user.username}`);

      // Generate new tokens with updated profile picture (null)
      const accessToken = secureAuthService.generateAccessToken(user);
      const refreshToken = await secureAuthService.generateRefreshToken(user);

      // Set new cookies with updated tokens
      secureAuthService.setAuthCookies(res, accessToken, refreshToken);

      // Return updated user data
      return res.status(200).json({
        message: 'Profile picture removed successfully',
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          role: user.role,
          profilePicture: user.profilePicture,
          token: accessToken // Include token in response for frontend
        }
      });
    } else {
      console.error('MongoDB not connected');
      return res.status(500).json({ message: 'Database connection error' });
    }
  } catch (error) {
    console.error('Error removing profile picture:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};
