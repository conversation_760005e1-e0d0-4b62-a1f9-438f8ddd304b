-- Assets and Transactions Schema for PostgreSQL

-- Create assets table
CREATE TABLE IF NOT EXISTS assets (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    balance DECIMAL(24, 8) NOT NULL DEFAULT 0,
    value DECIMAL(24, 2) NOT NULL DEFAULT 0,
    change_24h DECIMAL(8, 2),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, symbol)
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_assets_user_id ON assets(user_id);

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'buy', 'sell', 'swap', 'deposit', 'withdrawal'
    asset VARCHAR(50) NOT NULL,
    amount DECIMAL(24, 8) NOT NULL,
    value DECIMAL(24, 2) NOT NULL,
    fee DECIMAL(24, 8),
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- 'pending', 'completed', 'failed', 'cancelled'
    transaction_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);

-- Create index on transaction_date for faster sorting
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date);

-- Create asset_price_history table to track price changes
CREATE TABLE IF NOT EXISTS asset_price_history (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    price DECIMAL(24, 8) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on symbol and timestamp for faster lookups
CREATE INDEX IF NOT EXISTS idx_price_history_symbol_timestamp ON asset_price_history(symbol, timestamp);
