{"version": 3, "sources": ["../../@fractalwagmi/solana-wallet-adapter/node_modules/base-x/src/index.js", "../../@fractalwagmi/solana-wallet-adapter/node_modules/bs58/index.js", "../../@fractalwagmi/popup-connection/src/core/types.ts", "../../@fractalwagmi/popup-connection/src/core/connection.ts", "../../@fractalwagmi/popup-connection/src/core/constants.ts", "../../@fractalwagmi/popup-connection/src/core/utils.ts", "../../@fractalwagmi/popup-connection/src/connection-manager.ts", "../../@fractalwagmi/popup-connection/src/use-popup-connection.ts", "../../@fractalwagmi/popup-connection/src/lib/guards.ts", "../../@fractalwagmi/popup-connection/src/payloads/solana-wallet-adapter-approved.ts", "../../@fractalwagmi/popup-connection/src/payloads/transaction-signature-needed-response.ts", "../../@fractalwagmi/popup-connection/src/payloads/message-signature-needed-response.ts", "../../@fractalwagmi/solana-wallet-adapter/src/core/fractal-wallet-adapter-impl.ts", "../../@fractalwagmi/solana-wallet-adapter/src/core/nonce.ts"], "sourcesContent": ["'use strict'\n// base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\nfunction base (ALPHABET) {\n  if (ALPHABET.length >= 255) { throw new TypeError('Alphabet too long') }\n  var BASE_MAP = new Uint8Array(256)\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i)\n    var xc = x.charCodeAt(0)\n    if (BASE_MAP[xc] !== 255) { throw new TypeError(x + ' is ambiguous') }\n    BASE_MAP[xc] = i\n  }\n  var BASE = ALPHABET.length\n  var LEADER = ALPHABET.charAt(0)\n  var FACTOR = Math.log(BASE) / Math.log(256) // log(BASE) / log(256), rounded up\n  var iFACTOR = Math.log(256) / Math.log(BASE) // log(256) / log(BASE), rounded up\n  function encode (source) {\n    if (source instanceof Uint8Array) {\n    } else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength)\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source)\n    }\n    if (!(source instanceof Uint8Array)) { throw new TypeError('Expected Uint8Array') }\n    if (source.length === 0) { return '' }\n        // Skip & count leading zeroes.\n    var zeroes = 0\n    var length = 0\n    var pbegin = 0\n    var pend = source.length\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++\n      zeroes++\n    }\n        // Allocate enough space in big-endian base58 representation.\n    var size = ((pend - pbegin) * iFACTOR + 1) >>> 0\n    var b58 = new Uint8Array(size)\n        // Process the bytes.\n    while (pbegin !== pend) {\n      var carry = source[pbegin]\n            // Apply \"b58 = b58 * 256 + ch\".\n      var i = 0\n      for (var it1 = size - 1; (carry !== 0 || i < length) && (it1 !== -1); it1--, i++) {\n        carry += (256 * b58[it1]) >>> 0\n        b58[it1] = (carry % BASE) >>> 0\n        carry = (carry / BASE) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      pbegin++\n    }\n        // Skip leading zeroes in base58 result.\n    var it2 = size - length\n    while (it2 !== size && b58[it2] === 0) {\n      it2++\n    }\n        // Translate the result into a string.\n    var str = LEADER.repeat(zeroes)\n    for (; it2 < size; ++it2) { str += ALPHABET.charAt(b58[it2]) }\n    return str\n  }\n  function decodeUnsafe (source) {\n    if (typeof source !== 'string') { throw new TypeError('Expected String') }\n    if (source.length === 0) { return new Uint8Array() }\n    var psz = 0\n        // Skip and count leading '1's.\n    var zeroes = 0\n    var length = 0\n    while (source[psz] === LEADER) {\n      zeroes++\n      psz++\n    }\n        // Allocate enough space in big-endian base256 representation.\n    var size = (((source.length - psz) * FACTOR) + 1) >>> 0 // log(58) / log(256), rounded up.\n    var b256 = new Uint8Array(size)\n        // Process the characters.\n    while (source[psz]) {\n            // Find code of next character\n      var charCode = source.charCodeAt(psz)\n            // Base map can not be indexed using char code\n      if (charCode > 255) { return }\n            // Decode character\n      var carry = BASE_MAP[charCode]\n            // Invalid character\n      if (carry === 255) { return }\n      var i = 0\n      for (var it3 = size - 1; (carry !== 0 || i < length) && (it3 !== -1); it3--, i++) {\n        carry += (BASE * b256[it3]) >>> 0\n        b256[it3] = (carry % 256) >>> 0\n        carry = (carry / 256) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      psz++\n    }\n        // Skip leading zeroes in b256.\n    var it4 = size - length\n    while (it4 !== size && b256[it4] === 0) {\n      it4++\n    }\n    var vch = new Uint8Array(zeroes + (size - it4))\n    var j = zeroes\n    while (it4 !== size) {\n      vch[j++] = b256[it4++]\n    }\n    return vch\n  }\n  function decode (string) {\n    var buffer = decodeUnsafe(string)\n    if (buffer) { return buffer }\n    throw new Error('Non-base' + BASE + ' character')\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  }\n}\nmodule.exports = base\n", "const basex = require('base-x')\nconst ALPHABET = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz'\n\nmodule.exports = basex(ALPHABET)\n", null, null, null, null, null, null, null, null, null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAMA,aAAS,KAAM,UAAU;AACvB,UAAI,SAAS,UAAU,KAAK;AAAE,cAAM,IAAI,UAAU,mBAAmB;AAAA,MAAE;AACvE,UAAI,WAAW,IAAI,WAAW,GAAG;AACjC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,iBAAS,CAAC,IAAI;AAAA,MAChB;AACA,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,IAAI,SAAS,OAAO,CAAC;AACzB,YAAI,KAAK,EAAE,WAAW,CAAC;AACvB,YAAI,SAAS,EAAE,MAAM,KAAK;AAAE,gBAAM,IAAI,UAAU,IAAI,eAAe;AAAA,QAAE;AACrE,iBAAS,EAAE,IAAI;AAAA,MACjB;AACA,UAAI,OAAO,SAAS;AACpB,UAAI,SAAS,SAAS,OAAO,CAAC;AAC9B,UAAI,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAC1C,UAAI,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAC3C,eAAS,OAAQ,QAAQ;AACvB,YAAI,kBAAkB,YAAY;AAAA,QAClC,WAAW,YAAY,OAAO,MAAM,GAAG;AACrC,mBAAS,IAAI,WAAW,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;AAAA,QAC7E,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,mBAAS,WAAW,KAAK,MAAM;AAAA,QACjC;AACA,YAAI,EAAE,kBAAkB,aAAa;AAAE,gBAAM,IAAI,UAAU,qBAAqB;AAAA,QAAE;AAClF,YAAI,OAAO,WAAW,GAAG;AAAE,iBAAO;AAAA,QAAG;AAErC,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,OAAO,OAAO;AAClB,eAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,GAAG;AAC9C;AACA;AAAA,QACF;AAEA,YAAI,QAAS,OAAO,UAAU,UAAU,MAAO;AAC/C,YAAI,MAAM,IAAI,WAAW,IAAI;AAE7B,eAAO,WAAW,MAAM;AACtB,cAAI,QAAQ,OAAO,MAAM;AAEzB,cAAIA,KAAI;AACR,mBAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAI,WAAY,QAAQ,IAAK,OAAOA,MAAK;AAChF,qBAAU,MAAM,IAAI,GAAG,MAAO;AAC9B,gBAAI,GAAG,IAAK,QAAQ,SAAU;AAC9B,oBAAS,QAAQ,SAAU;AAAA,UAC7B;AACA,cAAI,UAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAAE;AACrD,mBAASA;AACT;AAAA,QACF;AAEA,YAAI,MAAM,OAAO;AACjB,eAAO,QAAQ,QAAQ,IAAI,GAAG,MAAM,GAAG;AACrC;AAAA,QACF;AAEA,YAAI,MAAM,OAAO,OAAO,MAAM;AAC9B,eAAO,MAAM,MAAM,EAAE,KAAK;AAAE,iBAAO,SAAS,OAAO,IAAI,GAAG,CAAC;AAAA,QAAE;AAC7D,eAAO;AAAA,MACT;AACA,eAAS,aAAc,QAAQ;AAC7B,YAAI,OAAO,WAAW,UAAU;AAAE,gBAAM,IAAI,UAAU,iBAAiB;AAAA,QAAE;AACzE,YAAI,OAAO,WAAW,GAAG;AAAE,iBAAO,IAAI,WAAW;AAAA,QAAE;AACnD,YAAI,MAAM;AAEV,YAAI,SAAS;AACb,YAAI,SAAS;AACb,eAAO,OAAO,GAAG,MAAM,QAAQ;AAC7B;AACA;AAAA,QACF;AAEA,YAAI,QAAU,OAAO,SAAS,OAAO,SAAU,MAAO;AACtD,YAAI,OAAO,IAAI,WAAW,IAAI;AAE9B,eAAO,OAAO,GAAG,GAAG;AAElB,cAAI,WAAW,OAAO,WAAW,GAAG;AAEpC,cAAI,WAAW,KAAK;AAAE;AAAA,UAAO;AAE7B,cAAI,QAAQ,SAAS,QAAQ;AAE7B,cAAI,UAAU,KAAK;AAAE;AAAA,UAAO;AAC5B,cAAIA,KAAI;AACR,mBAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAI,WAAY,QAAQ,IAAK,OAAOA,MAAK;AAChF,qBAAU,OAAO,KAAK,GAAG,MAAO;AAChC,iBAAK,GAAG,IAAK,QAAQ,QAAS;AAC9B,oBAAS,QAAQ,QAAS;AAAA,UAC5B;AACA,cAAI,UAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAAE;AACrD,mBAASA;AACT;AAAA,QACF;AAEA,YAAI,MAAM,OAAO;AACjB,eAAO,QAAQ,QAAQ,KAAK,GAAG,MAAM,GAAG;AACtC;AAAA,QACF;AACA,YAAI,MAAM,IAAI,WAAW,UAAU,OAAO,IAAI;AAC9C,YAAIC,KAAI;AACR,eAAO,QAAQ,MAAM;AACnB,cAAIA,IAAG,IAAI,KAAK,KAAK;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,eAAS,OAAQ,QAAQ;AACvB,YAAI,SAAS,aAAa,MAAM;AAChC,YAAI,QAAQ;AAAE,iBAAO;AAAA,QAAO;AAC5B,cAAM,IAAI,MAAM,aAAa,OAAO,YAAY;AAAA,MAClD;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;AC5HjB;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,WAAW;AAEjB,WAAO,UAAU,MAAM,QAAQ;AAAA;AAAA;;;ACH/B,IAAY;CAAZ,SAAYC,aAAU;AACpB,EAAAA,YAAA,kBAAA,IAAA;AACA,EAAAA,YAAA,WAAA,IAAA;AACA,EAAAA,YAAA,eAAA,IAAA;AAEA,EAAAA,YAAA,oBAAA,IAAA;AAEA,EAAAA,YAAA,4BAAA,IAAA;AACA,EAAAA,YAAA,4BAAA,IAAA;AACA,EAAAA,YAAA,oBAAA,IAAA;AACA,EAAAA,YAAA,gCAAA,IAAA;AACA,EAAAA,YAAA,8BAAA,IAAA;AACA,EAAAA,YAAA,cAAA,IAAA;AACA,EAAAA,YAAA,8BAAA,IAAA;AACA,EAAAA,YAAA,uCAAA,IAAA;AACA,EAAAA,YAAA,aAAA,IAAA;AACA,EAAAA,YAAA,0BAAA,IAAA;AACA,EAAAA,YAAA,mCAAA,IAAA;AACA,EAAAA,YAAA,6BAAA,IAAA;AACA,EAAAA,YAAA,iBAAA,IAAA;AACF,GApBY,eAAA,aAAU,CAAA,EAAA;AAwBtB,IAAY;CAAZ,SAAYC,WAAQ;AAClB,EAAAA,UAAA,SAAA,IAAA;AACA,EAAAA,UAAA,WAAA,IAAA;AACA,EAAAA,UAAA,uBAAA,IAAA;AACF,GAJY,aAAA,WAAQ,CAAA,EAAA;;;ACfd,IAAO,aAAP,MAAiB;EAGrB,YACW,iBACA,cAAoB;AADpB,SAAA,kBAAA;AACA,SAAA,eAAA;AAJM,SAAA,WAAW,oBAAI,IAAG;EAKhC;EAEH,IAAI,OAAmB,UAAuB;AAC5C,UAAM,iBAAiB,KAAK,SAAS,IAAI,KAAK;AAC9C,uBAAc,QAAd,mBAAc,SAAA,SAAd,eAAgB,OAAO,QAAQ;EACjC;EAEA,GAAG,OAAmB,UAAuB;;AAC3C,UAAM,kBAAiB,KAAA,KAAK,SAAS,IAAI,KAAK,OAAC,QAAA,OAAA,SAAA,KAAI,oBAAI,IAAG;AAC1D,mBAAe,IAAI,QAAQ;AAC3B,SAAK,SAAS,IAAI,OAAO,cAAc;EACzC;EAEA,KAAK,EAAE,OAAO,QAAO,GAA0C;AAC7D,SAAK,aAAa,YAChB;MACE;MACA;OAEF,KAAK,eAAe;EAExB;EAEA,oBAAoB,OAAmB,SAAgB;AACrD,UAAM,iBAAiB,KAAK,SAAS,IAAI,KAAK;AAC9C,QAAI,CAAC,gBAAgB;AACnB;;AAEF,eAAW,YAAY,gBAAgB;AACrC,eAAS,OAAO;;EAEpB;EAEA,gBAAa;AACX,SAAK,SAAS,MAAK;EACrB;EAEA,SAAM;AACJ,WAAO;MACL,KAAK,KAAK,IAAI,KAAK,IAAI;MACvB,IAAI,KAAK,GAAG,KAAK,IAAI;MACrB,MAAM,KAAK,KAAK,KAAK,IAAI;MACzB,iBAAiB,KAAK;;EAE1B;;;;AC1DK,IAAM,uBAAuB;AAC7B,IAAM,2BAA2B;AACjC,IAAM,yBAAyB;AAC/B,IAAM,0BAA0B;;;ACEjC,SAAU,eAAe,QAAc;AAC3C,SAAO,WAAW,4BAA4B,WAAW;AAC3D;AAEA,IAAM,SAAS;AACf,IAAM,wBAAwB,CAAC,aAAa,gBAAgB,UAAU;AAYhE,SAAU,UAAU,EACxB,OAAO,GACP,QAAQ,QACR,MAAM,GACN,QAAQ,yBACR,SAAS,yBACT,IAAG,GACa;AAChB,SAAO,MAAM,KACX,KACA,QACA,iBAAiB,EAAE,QAAQ,MAAM,KAAK,MAAK,CAAE,CAAC;AAElD;AASA,SAAS,iBAAiB,EACxB,QACA,MACA,KACA,MAAK,GACkB;AACvB,SAAO;IACL;IACA,QAAQ,IAAI;IACZ,OAAO,GAAG;IACV,SAAS,KAAK;IACd,UAAU,MAAM;IAChB,GAAG;IACH,KAAK,GAAG;AACZ;;;AC5CM,IAAO,oBAAP,MAAwB;EAoD5B,YAA6B,UAAkB;AAAlB,SAAA,WAAA;AAnDrB,SAAA,aAAgC;AAChC,SAAA,cAA6B;AAIpB,SAAA,gBAAgB,CAAC,MAAmB;;AAEnD,UAAI,CAAC,eAAe,EAAE,MAAM,GAAG;AAC7B;;AAEF,YAAM,kBAAkB,EAAE;AAE1B,UAAI,CAAC,KAAK,aAAa;AAErB;;AAGF,UAAI,EAAE,KAAK,UAAU,WAAW,aAAa,CAAC,KAAK,YAAY;AAC7D,YAAI,CAAC,KAAK,qBAAoB,KAAA,EAAE,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK,GAAG;AACpD;;AAGF,aAAK,YAAY,YACf;UACE,OAAO,WAAW;UAClB,SAAS;YACP,UAAU,KAAK;;WAGnB,eAAe;AAGjB,aAAK,aAAa,IAAI,WAAW,iBAAiB,KAAK,WAAW;AAClE,SAAA,KAAA,KAAK,+BAAyB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,KAAK,UAAU;;AAGlD,UAAI,CAAC,KAAK,YAAY;AACpB;;AAGF,WAAK,WAAW,oBAAoB,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO;AAKhE,UAAI,EAAE,KAAK,UAAU,WAAW,gBAAgB,KAAK,YAAY;AAC/D,aAAK,gBAAe;AACpB,aAAK,cAAc;;IAEvB;EAEkD;EAElD,aAAU;AACR,WAAO,iBAAiB,WAAW,KAAK,aAAa;AACrD,WAAO;EACT;EAEA,WAAQ;AACN,WAAO,oBAAoB,WAAW,KAAK,aAAa;AACxD,SAAK,gBAAe;AACpB,WAAO;EACT;EAEA,MAAM,KAAK,EACT,KACA,UAAU,wBACV,WAAW,yBACX,MAAK,GACuB;;AAG5B,SAAI,KAAA,KAAK,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ;AAC5B,WAAK,8BAA6B;;AAGpC,QAAI,KAAK,aAAa;AACpB;;AAGF,SAAK,WAAU;AAIf,QAAI,OAAO;AACT,WAAK,QAAQ;;AAGf,UAAM,OAAO,OAAO,WAAW,OAAO,aAAa,WAAW;AAC9D,UAAM,MAAM,OAAO,WAAW,OAAO,cAAc,YAAY;AAC/D,SAAK,cAAc,UAAU;MAC3B,QAAQ;MACR;MACA;MACA,KAAK,OAAO,QAAQ,WAAW,MAAM;MACrC,OAAO;KACR;AACD,QAAI,eAAe,SAAS;AAC1B,WAAK,OAAO,MAAM,GAAG;;EAEzB;EAEA,QAAK;AACH,QAAI,CAAC,KAAK,aAAa;AACrB;;AAEF,SAAK,YAAY,MAAK;AACtB,SAAK,8BAA6B;EACpC;EAEA,oBAAoB,UAAiD;AACnE,SAAK,4BAA4B;AACjC,WAAO;EACT;EAEA,gBAAa;AACX,WAAO,KAAK;EACd;EAEQ,OAAO,KAAW;AACxB,QAAI,CAAC,KAAK,aAAa;AACrB;;AAEF,SAAK,YAAY,WAAW;EAC9B;EAEQ,gCAA6B;AACnC,SAAK,gBAAe;AACpB,SAAK,cAAc;EACrB;EAEQ,kBAAe;;AACrB,KAAA,KAAA,KAAK,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,cAAa;AAC9B,SAAK,aAAa;AAClB,KAAA,KAAA,KAAK,+BAAyB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,KAAK,UAAU;EAClD;EAEQ,oBAAoB,gBAAuB;AACjD,QAAI,CAAC,KAAK,OAAO;AAEf,aAAO;;AAWT,UAAM,SAAS,mBAAmB,KAAK;AAIvC,QAAI,QAAQ;AACV,WAAK,QAAQ;;AAGf,WAAO;EACT;;;;ACzKF,mBAAyD;;;ACPnD,SAAU,SAAS,OAAc;AACrC,MAAI,UAAU,MAAM;AAClB,WAAO;;AAET,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;;AAET,SAAO;AACT;;;ACFM,SAAU,2CACd,SAAgB;AAEhB,MAAI,CAAC,SAAS,OAAO,GAAG;AACtB,WAAO;;AAET,MAAI,EAAE,qBAAqB,UAAU;AACnC,WAAO;;AAET,MAAI,OAAO,QAAQ,oBAAoB,UAAU;AAC/C,WAAO;;AAET,SAAO;AACT;;;ACZM,SAAU,yDACd,SAAgB;AAEhB,MAAI,CAAC,SAAS,OAAO,GAAG;AACtB,WAAO;;AAET,MAAI,EAAE,2BAA2B,UAAU;AACzC,WAAO;;AAET,MAAI,CAAC,MAAM,QAAQ,QAAQ,qBAAqB,GAAG;AACjD,WAAO;;AAET,SAAO,QAAQ,sBAAsB,MACnC,WAAS,OAAO,UAAU,QAAQ;AAEtC;;;ACfM,SAAU,qDACd,SAAgB;AAEhB,MAAI,CAAC,SAAS,OAAO,GAAG;AACtB,WAAO;;AAET,MAAI,EAAE,sBAAsB,UAAU;AACpC,WAAO;;AAET,SAAO,OAAO,QAAQ,qBAAqB;AAC7C;;;ACEA;AACA,kBAAmB;;;ACpBb,SAAU,cAAW;AACzB,SAAO,GAAG,aAAY,CAAE,GAAG,aAAY,CAAE,GAAG,aAAY,CAAE;AAC5D;AAGA,SAAS,eAAY;AACnB,UAAQ,KAAK,OAAM,IAAK,GAAG,SAAS,EAAE,EAAE,UAAU,CAAC;AACrD;;;ADgBA,IAAM,wBAAwB;AAC9B,IAAMC,wBAAuB;AAC7B,IAAM,mBAAmB,GAAGA,qBAAoB;AAChD,IAAM,gBAAgB,GAAGA,qBAAoB;AAC7C,IAAM,wBAAwB,GAAGA,qBAAoB;AACrD,IAAM,sBAAsB;AAC5B,IAAM,qBAAqB;AAC3B,IAAM,mCAAmC;AAEnC,IAAO,2BAAP,MAA+B;EAArC,cAAA;AACmB,SAAA,eAAe,IAAI,kBAClC,SAAS,qBAAqB;AAGxB,SAAA,YAA8B;AAC9B,SAAA,aAAa;EAkSvB;EAhSE,eAAY;AACV,WAAO,KAAK;EACd;EAEA,MAAM,UAAO;AACX,QAAI;AACJ,QAAI;AAEJ,UAAM,0BAA0B,OAAO,aAAa,QAClD,gCAAgC;AAElC,QAAI,yBAAyB;AAC3B,WAAK,YAAY,IAAI,UAAU,uBAAuB;AACtD,aAAO,QAAQ,QAAO;;AAGxB,UAAM,QAAQ,YAAW;AACzB,SAAK,aAAa,KAAK;MACrB;MACA,KAAK,GAAG,gBAAgB,IAAI,KAAK;KAClC;AAED,UAAM,oCAAoC,CAAC,YAAoB;AAC7D,UAAI,CAAC,2CAA2C,OAAO,GAAG;AACxD,eACE,IAAI,sBACF,mGAEc,KAAK,UAAU,OAAO,CAAC,EAAE,CACxC;AAEH,aAAK,aAAa,MAAK;AACvB;;AAEF,UAAI;AACF,aAAK,YAAY,IAAI,UAAU,QAAQ,eAAe;AACtD,eAAO,aAAa,QAClB,kCACA,QAAQ,eAAe;AAEzB,gBAAO;eACA,OAAgB;AACvB,cAAM,iBAAiB,IAAI,qBACzB,iBAAiB,QAAQ,MAAM,UAAU,uBACzC,KAAK;AAEP,eAAO,cAAc;;AAEvB,WAAK,aAAa,MAAK;IACzB;AAEA,UAAM,6BAA6B,MAAK;AACtC,aAAO,IAAI,sBAAsB,iCAAiC,CAAC;AACnE,WAAK,aAAa,MAAK;IACzB;AAEA,UAAM,qBAAqB,MAAK;AAC9B,aAAO,IAAI,sBAAsB,iCAAiC,CAAC;AACnE,WAAK,aAAa,MAAK;IACzB;AAEA,SAAK,aAAa,oBAAoB,gBAAa;AACjD,UAAI,CAAC,YAAY;AACf;;AAEF,iBAAW,GACT,WAAW,gCACX,iCAAiC;AAEnC,iBAAW,GACT,WAAW,8BACX,0BAA0B;AAE5B,iBAAW,GAAG,WAAW,cAAc,kBAAkB;IAC3D,CAAC;AAED,WAAO,IAAI,QAAQ,CAAC,iBAAiB,oBAAmB;AACtD,gBAAU;AACV,eAAS;IACX,CAAC;EACH;EAEA,MAAM,aAAU;AACd,SAAK,aAAa,SAAQ;AAC1B,SAAK,YAAY;AACjB,WAAO,aAAa,WAAW,gCAAgC;EACjE;EAEA,MAAM,gBAAuC,aAAc;AACzD,QAAI;AACF,WAAK,qBAAoB;AACzB,YAAM,SAAS,MAAM,KAAK,iBAAiB,CAAC,WAAW,CAAC;AACxD,aAAO,OAAO,CAAC;aACR,OAAgB;AACvB,UAAI,eAAe;AACnB,UAAI,EAAE,iBAAiB,cAAc;AACnC,uBAAe,IAAI,2BACjB,iBAAiB,QAAQ,MAAM,UAAU,uBACzC,KAAK;;AAGT,YAAM;;EAEV;EAEA,MAAM,oBACJ,cAAiB;AAEjB,QAAI;AACF,WAAK,qBAAoB;AACzB,YAAM,SAAS,MAAM,KAAK,iBAAiB,YAAY;AACvD,aAAO;aACA,OAAgB;AACvB,UAAI,eAAe;AACnB,UAAI,EAAE,iBAAiB,cAAc;AACnC,uBAAe,IAAI,2BACjB,iBAAiB,QAAQ,MAAM,UAAU,uBACzC,KAAK;;AAGT,YAAM;;EAEV;EAEA,MAAM,YAAY,gBAA0B;AAC1C,UAAM,iBAAiB,IAAI,YAAW,EAAG,OAAO,cAAc;AAE9D,QAAI;AACJ,QAAI;AAEJ,UAAM,uCAAuC,CAAC,YAAoB;AAChE,UAAI,CAAC,qDAAqD,OAAO,GAAG;AAClE,cAAM,QAAQ,IAAI,uBAChB,8FAEkB,KAAK,UAAU,OAAO,CAAC,EAAE;AAE7C,eAAO,KAAK;AACZ,aAAK,aAAa,MAAK;AACvB;;AAGF,YAAM,mBAAmB,WAAW,KAClC,QAAQ,iBAAiB,MAAM,GAAG,EAAE,IAAI,OAAK,OAAO,CAAC,CAAC,CAAC;AAEzD,cAAQ,gBAAgB;AACxB,WAAK,aAAa,MAAK;IACzB;AAEA,UAAM,6BAA6B,MAAK;AACtC,aACE,IAAI,uBAAuB,sCAAsC,CAAC;AAEpE,WAAK,aAAa,MAAK;IACzB;AAEA,UAAM,mBAAmB,MAAK;;AAC5B,YAAM,UAAyC;QAC7C;;AAEF,OAAA,KAAA,KAAK,aAAa,cAAa,OAAE,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK;QACtC,OAAO,WAAW;QAClB;OACD;IACH;AAEA,UAAM,QAAQ,YAAW;AACzB,SAAK,aAAa,KAAK;MACrB,UAAU,KAAK,IACb,qBACA,KAAK,MAAM,OAAO,cAAc,GAAG,CAAC;MAEtC;MACA,KAAK,GAAG,qBAAqB,IAAI,KAAK;MACtC,SAAS,KAAK,IACZ,oBACA,KAAK,MAAM,OAAO,aAAa,GAAG,CAAC;KAEtC;AACD,SAAK,aAAa,oBAAoB,gBAAa;AACjD,UAAI,CAAC,YAAY;AACf;;AAGF,iBAAW,GACT,WAAW,mCACX,oCAAoC;AAEtC,iBAAW,GAAG,WAAW,oBAAoB,0BAA0B;AACvE,iBAAW,GAAG,WAAW,cAAc,0BAA0B;AACjE,iBAAW,GAAG,WAAW,aAAa,gBAAgB;IACxD,CAAC;AAED,WAAO,IAAI,QAAoB,CAAC,iBAAiB,oBAAmB;AAClE,gBAAU;AACV,eAAS;IACX,CAAC;EACH;EAEQ,MAAM,iBACZ,cAAiB;AAEjB,QAAI;AACJ,QAAI;AAEJ,UAAM,2CAA2C,CAAC,YAAoB;AACpE,UAAI,CAAC,yDAAyD,OAAO,GAAG;AACtE,cAAM,QAAQ,IAAI,2BAChB,0GAEkB,KAAK,UAAU,OAAO,CAAC,EAAE;AAE7C,eAAO,KAAK;AACZ,aAAK,aAAa,MAAK;AACvB;;AAGF,YAAM,qBAAqB,QAAQ,sBAAsB,IACvD,0BAAuB;AACrB,eAAO,YAAY,KAAK,YAAAC,QAAO,OAAO,oBAAoB,CAAC;MAC7D,CAAC;AAGH,cAAQ,kBAAkB;AAC1B,WAAK,aAAa,MAAK;IACzB;AAEA,UAAM,6BAA6B,MAAK;AACtC,aACE,IAAI,2BACF,0CAA0C,CAC3C;AAEH,WAAK,aAAa,MAAK;IACzB;AAEA,UAAM,mBAAmB,MAAK;;AAC5B,YAAM,UAA6C;QACjD,yBAAyB,aAAa,IAAI,OACxC,YAAAA,QAAO,OAAO,EAAE,iBAAgB,CAAE,CAAC;;AAGvC,OAAA,KAAA,KAAK,aAAa,cAAa,OAAE,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK;QACtC,OAAO,WAAW;QAClB;OACD;IACH;AAEA,UAAM,QAAQ,YAAW;AACzB,SAAK,aAAa,KAAK;MACrB,UAAU,KAAK,IACb,qBACA,KAAK,MAAM,OAAO,cAAc,GAAG,CAAC;MAEtC;MACA,KAAK,GAAG,aAAa,IAAI,KAAK;MAC9B,SAAS,KAAK,IACZ,oBACA,KAAK,MAAM,OAAO,aAAa,GAAG,CAAC;KAEtC;AACD,SAAK,aAAa,oBAAoB,gBAAa;AACjD,UAAI,CAAC,YAAY;AACf;;AAGF,iBAAW,GACT,WAAW,uCACX,wCAAwC;AAE1C,iBAAW,GAAG,WAAW,oBAAoB,0BAA0B;AACvE,iBAAW,GAAG,WAAW,cAAc,0BAA0B;AACjE,iBAAW,GAAG,WAAW,aAAa,gBAAgB;IACxD,CAAC;AAED,WAAO,IAAI,QAAa,CAAC,iBAAiB,oBAAmB;AAC3D,gBAAU;AACV,eAAS;IACX,CAAC;EACH;EAEQ,uBAAoB;AAC1B,QAAI,KAAK,cAAc,MAAM;AAC3B,YAAM,IAAI,wBACR,2DAA2D;;EAGjE;;", "names": ["i", "j", "PopupEvent", "Platform", "FRACTAL_DOMAIN_HTTPS", "base58"]}