// Test PostgreSQL connection
require('dotenv').config();
const { Pool } = require('pg');

async function testConnection() {
  console.log('Testing PostgreSQL connection...');
  
  // Get credentials from environment variables
  const connectionString = process.env.POSTGRESQL_URI;
  const user = process.env.POSTGRES_USER;
  const password = process.env.POSTGRES_PASSWORD;
  const host = process.env.POSTGRES_HOST;
  const database = process.env.POSTGRES_DB;
  
  console.log(`Connection string: ${connectionString.replace(/:[^:]*@/, ':****@')}`);
  console.log(`User: ${user}`);
  console.log(`Host: ${host}`);
  console.log(`Database: ${database}`);
  
  try {
    // Create a new pool with the connection string
    const pool = new Pool({
      connectionString,
      ssl: { rejectUnauthorized: false }
    });
    
    // Try to connect
    console.log('Attempting to connect...');
    const client = await pool.connect();
    
    // If we get here, connection was successful
    console.log('✅ Connection successful!');
    
    // Get PostgreSQL version
    const result = await client.query('SELECT version()');
    console.log(`PostgreSQL version: ${result.rows[0].version}`);
    
    // Release client back to pool
    client.release();
    
    // End pool
    await pool.end();
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('Error details:', JSON.stringify(error, null, 2));
  }
}

// Run the test
testConnection()
  .then(() => console.log('\nTest completed'))
  .catch(error => console.error('Test failed:', error));
