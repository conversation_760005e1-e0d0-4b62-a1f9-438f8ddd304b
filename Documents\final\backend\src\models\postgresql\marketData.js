/**
 * Market Data Model (PostgreSQL)
 * 
 * Stores market data including:
 * - Price history
 * - Volume data
 * - Market statistics
 */

const { sequelize, Sequelize } = require('../../config/postgresql');

const MarketData = sequelize.define('MarketData', {
  // ID (primary key)
  id: {
    type: Sequelize.UUID,
    defaultValue: Sequelize.UUIDV4,
    primaryKey: true
  },
  
  // Token mint address
  tokenMint: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Data type (price, volume, marketCap, etc.)
  dataType: {
    type: Sequelize.ENUM('price', 'volume', 'marketCap', 'liquidity', 'supply'),
    allowNull: false
  },
  
  // Timestamp for the data point
  timestamp: {
    type: Sequelize.DATE,
    allowNull: false
  },
  
  // Value (as string to preserve precision)
  value: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Time interval (1m, 5m, 15m, 1h, 4h, 1d, 1w)
  interval: {
    type: Sequelize.ENUM('1m', '5m', '15m', '1h', '4h', '1d', '1w'),
    allowNull: false
  },
  
  // Additional data (JSON)
  metadata: {
    type: Sequelize.JSONB,
    defaultValue: {},
    allowNull: false
  }
}, {
  // Table configuration
  tableName: 'market_data',
  timestamps: true,
  indexes: [
    {
      fields: ['tokenMint']
    },
    {
      fields: ['dataType']
    },
    {
      fields: ['timestamp']
    },
    {
      fields: ['interval']
    },
    {
      fields: ['tokenMint', 'dataType', 'interval', 'timestamp'],
      unique: true
    }
  ]
});

module.exports = MarketData;
