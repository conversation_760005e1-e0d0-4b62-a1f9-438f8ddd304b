const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/swapDB')
  .then(async () => {
    console.log('Connected to MongoDB');
    
    try {
      // Get the users collection
      const db = mongoose.connection.db;
      const usersCollection = db.collection('users');
      
      // Get current indexes
      const indexes = await usersCollection.indexes();
      console.log('Current indexes:', indexes);
      
      // Find the username index
      const usernameIndex = indexes.find(index => 
        index.key && index.key.username === 1 && index.unique === true
      );
      
      if (usernameIndex) {
        console.log('Found username unique index:', usernameIndex.name);
        
        // Drop the index
        await usersCollection.dropIndex(usernameIndex.name);
        console.log('Username unique index dropped successfully');
      } else {
        console.log('No unique username index found');
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      // Close the connection
      mongoose.connection.close();
      console.log('MongoDB connection closed');
    }
  })
  .catch(err => {
    console.error('MongoDB connection error:', err);
  }); 