const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const Update = require('../models/Update');
const Token = require('../models/Token');
const User = require('../models/User');

// Get all updates (latest first)
router.get('/', async (req, res) => {
  try {
    const updates = await Update.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .populate({
        path: 'tokenId',
        select: 'name symbol imageUrl currentPrice priceChange24h'
      })
      .populate({
        path: 'userId',
        select: 'username profileImage'
      });

    // Format the updates for frontend display
    const formattedUpdates = updates.map(update => {
      let formattedUpdate = {
        id: update._id,
        type: update.type,
        message: update.message,
        timestamp: update.createdAt,
        details: {}
      };

      // Add specific details based on update type
      if (update.type === 'token_created' || update.type === 'trending_token') {
        if (update.tokenId) {
          formattedUpdate.details.token = {
            id: update.tokenId._id,
            name: update.tokenId.name,
            symbol: update.tokenId.symbol,
            imageUrl: update.tokenId.imageUrl,
            price: update.tokenId.currentPrice,
            priceChange: update.tokenId.priceChange24h
          };
        }
      }

      if (update.type === 'user_registered' || update.type === 'token_created') {
        if (update.userId) {
          formattedUpdate.details.user = {
            id: update.userId._id,
            username: update.userId.username,
            profileImage: update.userId.profileImage
          };
        }
      }

      return formattedUpdate;
    });

    res.json(formattedUpdates);
  } catch (error) {
    console.error('Error fetching updates:', error);
    res.status(500).json({ error: 'Failed to fetch updates' });
  }
});

// Get latest token created
router.get('/latest-token', async (req, res) => {
  try {
    const latestToken = await Token.findOne()
      .sort({ createdAt: -1 })
      .populate({
        path: 'creator',
        select: 'username profileImage'
      });

    if (!latestToken) {
      console.log('No latest token found, returning empty data');
      return res.status(200).json({
        name: 'No Token Yet',
        symbol: 'NONE',
        imageUrl: 'https://via.placeholder.com/40',
        price: 0,
        creator: {
          username: 'System',
          profileImage: 'https://via.placeholder.com/40'
        },
        createdAt: new Date()
      });
    }

    res.json({
      id: latestToken._id,
      name: latestToken.name,
      symbol: latestToken.symbol,
      imageUrl: latestToken.imageUrl,
      price: latestToken.currentPrice,
      creator: {
        id: latestToken.creator._id,
        username: latestToken.creator.username,
        profileImage: latestToken.creator.profileImage
      },
      createdAt: latestToken.createdAt
    });
  } catch (error) {
    console.error('Error fetching latest token:', error);
    // Return default data instead of error
    res.status(200).json({
      name: 'Error Loading Token',
      symbol: 'ERR',
      imageUrl: 'https://via.placeholder.com/40',
      price: 0,
      creator: {
        username: 'System',
        profileImage: 'https://via.placeholder.com/40'
      },
      createdAt: new Date()
    });
  }
});

// Get trending token
router.get('/trending-token', async (req, res) => {
  try {
    const trendingToken = await Token.findOne({ trending: true })
      .sort({ trendingScore: -1 })
      .populate({
        path: 'creator',
        select: 'username profileImage'
      });

    if (!trendingToken) {
      console.log('No trending token found, returning empty data');
      return res.status(200).json({
        name: 'No Trending Token',
        symbol: 'TREND',
        imageUrl: 'https://via.placeholder.com/40',
        price: 0,
        priceChange: 0,
        creator: {
          username: 'System',
          profileImage: 'https://via.placeholder.com/40'
        },
        trendingScore: 0
      });
    }

    res.json({
      id: trendingToken._id,
      name: trendingToken.name,
      symbol: trendingToken.symbol,
      imageUrl: trendingToken.imageUrl,
      price: trendingToken.currentPrice,
      priceChange: trendingToken.priceChange24h,
      creator: {
        id: trendingToken.creator._id,
        username: trendingToken.creator.username,
        profileImage: trendingToken.creator.profileImage
      },
      trendingScore: trendingToken.trendingScore
    });
  } catch (error) {
    console.error('Error fetching trending token:', error);
    // Return default data instead of error
    res.status(200).json({
      name: 'Error Loading Token',
      symbol: 'ERR',
      imageUrl: 'https://via.placeholder.com/40',
      price: 0,
      priceChange: 0,
      creator: {
        username: 'System',
        profileImage: 'https://via.placeholder.com/40'
      },
      trendingScore: 0
    });
  }
});

// Get latest registered user
router.get('/latest-user', async (req, res) => {
  try {
    const latestUser = await User.findOne()
      .sort({ createdAt: -1 })
      .select('username profileImage createdAt');

    if (!latestUser) {
      console.log('No latest user found, returning empty data');
      return res.status(200).json({
        username: 'No User Yet',
        // Profile image removed
        createdAt: new Date()
      });
    }

    // We no longer need to handle profile images

    res.json({
      id: latestUser._id,
      username: latestUser.username,
      // Profile image removed
      createdAt: latestUser.createdAt
    });
  } catch (error) {
    console.error('Error fetching latest user:', error);
    // Return default data instead of error
    res.status(200).json({
      username: 'Error Loading User',
      // Profile image removed
      createdAt: new Date()
    });
  }
});

// Create a new update (internal use only)
router.post('/', async (req, res) => {
  try {
    const { type, tokenId, userId, message, details } = req.body;

    // Validate required fields
    if (!type || !message) {
      return res.status(400).json({ error: 'Type and message are required' });
    }

    // Validate token ID if provided
    if (tokenId && !mongoose.Types.ObjectId.isValid(tokenId)) {
      return res.status(400).json({ error: 'Invalid token ID' });
    }

    // Validate user ID if provided
    if (userId && !mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    // Create the update
    const update = new Update({
      type,
      tokenId: tokenId || null,
      userId: userId || null,
      message,
      details: details || {}
    });

    await update.save();

    // Emit socket event for real-time updates
    const io = req.app.get('io');
    if (io) {
      io.to('global-updates').emit('new-update', {
        id: update._id,
        type: update.type,
        message: update.message,
        timestamp: update.createdAt,
        details: update.details
      });
    }

    res.status(201).json(update);
  } catch (error) {
    console.error('Error creating update:', error);
    res.status(500).json({ error: 'Failed to create update' });
  }
});

module.exports = router;
