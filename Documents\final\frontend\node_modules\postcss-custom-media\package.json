{"name": "postcss-custom-media", "description": "Use Custom Media Queries in CSS", "version": "8.0.2", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>"}], "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^12 || ^14 || >=16"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.3"}, "scripts": {"build": "rollup -c ../../rollup/default.js", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true });\"", "docs": "node ../../.github/bin/generate-docs/install.mjs && node ../../.github/bin/generate-docs/readme.mjs", "lint": "npm run lint:eslint && npm run lint:package-json", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "lint:package-json": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "test": "node .tape.cjs && npm run test:exports", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.cjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-custom-media#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-custom-media"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["at-rule", "at<PERSON>le", "css", "csswg", "custom", "media", "postcss", "postcss-plugin", "queries", "query", "specification", "w3c"], "csstools": {"cssdbId": "custom-media-queries", "exportName": "postcssCustomMedia", "humanReadableName": "PostCSS Custom Media", "specUrl": "https://www.w3.org/TR/mediaqueries-5/#at-ruledef-custom-media"}, "volta": {"extends": "../../package.json"}}