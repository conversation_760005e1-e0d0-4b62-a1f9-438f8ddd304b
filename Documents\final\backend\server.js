const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
const http = require('http');
const socketIo = require('socket.io');
const cookieParser = require('cookie-parser');
const morgan = require('morgan');
const path = require('path');
require('dotenv').config();

// Order matching engine removed - using bonding curve smart contracts

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const tokenRoutes = require('./routes/tokens');
const statsRoutes = require('./routes/stats');
const updatesRoutes = require('./routes/updates');
const storiesRoutes = require('./routes/stories');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: ['http://localhost:3000', 'http://localhost:3001'],
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));
app.use(express.json());
app.use(cookieParser());
app.use(morgan('dev'));

// Database connection
// Note: MongoDB connection is now handled by the robust connection manager in src/config/mongodb_robust.js

// Socket.io connection
io.on('connection', (socket) => {
  console.log('New client connected');

  // Join a room based on user ID if authenticated
  socket.on('join', (userId) => {
    if (userId) {
      socket.join(`user-${userId}`);
      console.log(`User ${userId} joined their room`);
    }
    // Everyone joins the global updates room
    socket.join('global-updates');
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected');
  });
});

// Make io accessible to routes
app.set('io', io);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/tokens', tokenRoutes);
app.use('/api/stats', statsRoutes);
app.use('/api/updates', updatesRoutes);
app.use('/api/stories', storiesRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: 'Server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

const PORT = process.env.DASHBOARD_PORT || 3003;

server.listen(PORT, async () => {
  console.log(`Server running on port ${PORT}`);

  // Order matching engine removed - using bonding curve smart contracts
  console.log('Using bonding curve smart contracts for trading');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');

  // Stop the Order Matching Engine
  try {
    await orderMatchingEngineManager.stop();
    console.log('Order Matching Engine stopped successfully');
  } catch (error) {
    console.error('Error stopping Order Matching Engine:', error.message);
  }

  // Close the server
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

module.exports = { app, io, orderMatchingEngineManager };
