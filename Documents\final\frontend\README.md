# Solana Token Platform

A React.js application for creating and trading Solana-based cryptocurrency tokens.

## Features

- Create custom Solana tokens
- Trade tokens on the platform
- View token statistics and performance
- Connect with Solana wallet

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
   or
   ```
   yarn
   ```

3. Start the development server:
   ```
   npm run dev
   ```
   or
   ```
   yarn dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

## Project Structure

- `src/components`: Reusable UI components
- `src/pages`: Application pages
- `src/styles`: CSS styles
- `src/assets`: Images, fonts, and other static assets

## Adding Your Assets

You need to add the following assets to the `src/assets` directory:
- Background image for the landing page
- Company logo
- Any other images used in the application
