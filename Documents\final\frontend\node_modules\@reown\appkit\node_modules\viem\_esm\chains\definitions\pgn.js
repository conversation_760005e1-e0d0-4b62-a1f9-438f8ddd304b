import { formatters } from '../../op-stack/formatters.js';
import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
const sourceId = 1; // mainnet
export const pgn = /*#__PURE__*/ defineChain({
    id: 424,
    network: 'pgn',
    name: 'PG<PERSON>',
    nativeCurrency: { name: 'E<PERSON>', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.publicgoods.network'],
        },
    },
    blockExplorers: {
        default: {
            name: 'PGN Explorer',
            url: 'https://explorer.publicgoods.network',
            apiUrl: 'https://explorer.publicgoods.network/api',
        },
    },
    contracts: {
        l2OutputOracle: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        multicall3: {
            address: '******************************************',
            blockCreated: 3380209,
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
            },
        },
    },
    formatters,
    sourceId,
});
//# sourceMappingURL=pgn.js.map