import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      include: "**/*.{jsx,js}",
      babel: {
        parserOpts: {
          plugins: ['jsx']
        }
      }
    })
  ],
  server: {
    port: 3000,
  },
  define: {
    global: 'globalThis',
    'process.env': {
      REACT_APP_API_URL: JSON.stringify('http://localhost:3001/api'),
      REACT_APP_SOCKETIO_URL: JSON.stringify('http://localhost:3001'),
      REACT_APP_SOLANA_RPC_URL: JSON.stringify('https://api.devnet.solana.com'),
    },
  },
  resolve: {
    alias: {
      crypto: 'crypto-browserify',
      stream: 'stream-browserify',
      buffer: 'buffer',
      process: 'process/browser.js',
      util: 'util',
    },
  },
  optimizeDeps: {
    include: ['buffer', 'process'],
  },
})
