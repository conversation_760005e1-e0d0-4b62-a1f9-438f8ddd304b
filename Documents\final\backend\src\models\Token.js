const mongoose = require('mongoose');

const bondingCurveSchema = new mongoose.Schema({
  address: {
    type: String,
    required: true
  },
  curveType: {
    type: String,
    enum: ['linear', 'exponential', 'logarithmic', 'bancor'],
    required: true
  },
  basePrice: {
    type: Number,
    required: true
  },
  slope: {
    type: Number,
    required: true
  },
  reserveRatio: {
    type: Number,
    default: 0.2
  },
  maxSupply: {
    type: Number,
    default: 0 // 0 means unlimited
  }
});

const tokenSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  symbol: {
    type: String,
    required: true,
    unique: true
  },
  totalSupply: {
    type: Number,
    required: true
  },
  currentSupply: {
    type: Number,
    required: true
  },
  decimals: {
    type: Number,
    default: 9
  },
  mintAddress: {
    type: String,
    required: true,
    unique: true
  },
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  description: String,
  website: String,
  social: {
    twitter: String,
    telegram: String,
    discord: String
  },
  logo: String,
  createdAt: {
    type: Date,
    default: Date.now
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['ACTIVE', 'SUSPENDED', 'DELISTED'],
    default: 'ACTIVE'
  },
  bondingCurve: bondingCurveSchema,
  metadata: {
    type: mongoose.Schema.Types.Mixed
  }
});

// Indexes for efficient querying
tokenSchema.index({ symbol: 1 });
tokenSchema.index({ mintAddress: 1 });
tokenSchema.index({ creator: 1 });
tokenSchema.index({ createdAt: -1 });

// Static method to get hot tokens with error handling
tokenSchema.statics.getHotTokens = async function(limit = 10) {
  try {
    return await this.find({ status: 'ACTIVE' })
      .sort({ createdAt: -1 })
      .limit(limit)
      .populate('creator', 'username')
      .maxTimeMS(15000); // Set maximum execution time to 15 seconds
  } catch (error) {
    console.error('Error getting hot tokens:', error.message);
    // Return empty array instead of throwing
    return [];
  }
};

// Static method to get user's tokens with error handling
tokenSchema.statics.getUserTokens = async function(userId) {
  try {
    return await this.find({ creator: userId })
      .sort({ createdAt: -1 })
      .maxTimeMS(15000); // Set maximum execution time to 15 seconds
  } catch (error) {
    console.error(`Error getting tokens for user ${userId}:`, error.message);
    // Return empty array instead of throwing
    return [];
  }
};

// Static method to get token by mint address with error handling
tokenSchema.statics.getTokenByMint = async function(mintAddress) {
  try {
    return await this.findOne({ mintAddress }).maxTimeMS(15000);
  } catch (error) {
    console.error(`Error getting token by mint address ${mintAddress}:`, error.message);
    // Return null instead of throwing
    return null;
  }
};

// Static method to get token by symbol with error handling
tokenSchema.statics.getTokenBySymbol = async function(symbol) {
  try {
    return await this.findOne({ symbol: symbol.toUpperCase() }).maxTimeMS(15000);
  } catch (error) {
    console.error(`Error getting token by symbol ${symbol}:`, error.message);
    // Return null instead of throwing
    return null;
  }
};

const Token = mongoose.model('Token', tokenSchema);

module.exports = Token;