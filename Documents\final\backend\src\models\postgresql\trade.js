/**
 * Trade Model (PostgreSQL)
 * 
 * Stores all trade data including:
 * - Matched orders
 * - Trade execution details
 * - Settlement information
 */

const { sequelize, Sequelize } = require('../../config/postgresql');

const Trade = sequelize.define('Trade', {
  // Trade ID (primary key)
  id: {
    type: Sequelize.UUID,
    defaultValue: Sequelize.UUIDV4,
    primaryKey: true
  },
  
  // Buy order ID
  buyOrderId: {
    type: Sequelize.UUID,
    allowNull: false
  },
  
  // Sell order ID
  sellOrderId: {
    type: Sequelize.UUID,
    allowNull: false
  },
  
  // Token mint address
  tokenMint: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Buyer wallet address
  buyerAddress: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Seller wallet address
  sellerAddress: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Token amount (as string to preserve precision)
  tokenAmount: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Price per token (in SOL, as string)
  price: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Total trade value (in SOL, as string)
  totalValue: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Platform fee amount (as string)
  feeAmount: {
    type: Sequelize.STRING,
    allowNull: true
  },
  
  // Solana transaction signature
  signature: {
    type: Sequelize.STRING,
    allowNull: true
  },
  
  // Trade status
  status: {
    type: Sequelize.ENUM('pending', 'completed', 'failed'),
    defaultValue: 'pending',
    allowNull: false
  },
  
  // Settlement time
  settledAt: {
    type: Sequelize.DATE,
    allowNull: true
  },
  
  // Additional data (JSON)
  metadata: {
    type: Sequelize.JSONB,
    defaultValue: {},
    allowNull: false
  }
}, {
  // Table configuration
  tableName: 'trades',
  timestamps: true,
  indexes: [
    {
      fields: ['buyOrderId']
    },
    {
      fields: ['sellOrderId']
    },
    {
      fields: ['tokenMint']
    },
    {
      fields: ['buyerAddress']
    },
    {
      fields: ['sellerAddress']
    },
    {
      fields: ['status']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['price']
    }
  ]
});

module.exports = Trade;
