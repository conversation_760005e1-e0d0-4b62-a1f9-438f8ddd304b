{"version": 3, "file": "index.cjs.js", "sources": ["../src/constants/core.ts", "../src/constants/crypto.ts", "../src/constants/keychain.ts", "../src/constants/messages.ts", "../src/constants/publisher.ts", "../src/constants/relayer.ts", "../src/constants/store.ts", "../src/constants/subscriber.ts", "../src/constants/pairing.ts", "../src/constants/history.ts", "../src/constants/expirer.ts", "../src/constants/verify.ts", "../src/constants/echo.ts", "../src/constants/events.ts", "../../../node_modules/multiformats/esm/vendor/base-x.js", "../../../node_modules/multiformats/esm/src/bytes.js", "../../../node_modules/multiformats/esm/src/bases/base.js", "../../../node_modules/multiformats/esm/src/bases/identity.js", "../../../node_modules/multiformats/esm/src/bases/base2.js", "../../../node_modules/multiformats/esm/src/bases/base8.js", "../../../node_modules/multiformats/esm/src/bases/base10.js", "../../../node_modules/multiformats/esm/src/bases/base16.js", "../../../node_modules/multiformats/esm/src/bases/base32.js", "../../../node_modules/multiformats/esm/src/bases/base36.js", "../../../node_modules/multiformats/esm/src/bases/base58.js", "../../../node_modules/multiformats/esm/src/bases/base64.js", "../../../node_modules/multiformats/esm/src/bases/base256emoji.js", "../../../node_modules/multiformats/esm/vendor/varint.js", "../../../node_modules/multiformats/esm/src/varint.js", "../../../node_modules/multiformats/esm/src/hashes/digest.js", "../../../node_modules/multiformats/esm/src/hashes/hasher.js", "../../../node_modules/multiformats/esm/src/hashes/sha2-browser.js", "../../../node_modules/multiformats/esm/src/hashes/identity.js", "../../../node_modules/multiformats/esm/src/codecs/json.js", "../../../node_modules/multiformats/esm/src/basics.js", "../../../node_modules/uint8arrays/esm/src/alloc.js", "../../../node_modules/uint8arrays/esm/src/util/bases.js", "../../../node_modules/uint8arrays/esm/src/from-string.js", "../src/controllers/keychain.ts", "../src/controllers/crypto.ts", "../src/controllers/messages.ts", "../src/controllers/publisher.ts", "../src/controllers/topicmap.ts", "../src/controllers/subscriber.ts", "../src/controllers/relayer.ts", "../src/controllers/store.ts", "../src/controllers/pairing.ts", "../src/controllers/history.ts", "../src/controllers/expirer.ts", "../src/controllers/verify.ts", "../src/controllers/echo.ts", "../src/controllers/events.ts", "../src/core.ts", "../src/index.ts"], "sourcesContent": ["export const CORE_PROTOCOL = \"wc\";\nexport const CORE_VERSION = 2;\nexport const CORE_CONTEXT = \"core\";\n\nexport const CORE_STORAGE_PREFIX = `${CORE_PROTOCOL}@${CORE_VERSION}:${CORE_CONTEXT}:`;\n\nexport const CORE_DEFAULT = {\n  name: CORE_CONTEXT,\n  logger: \"error\",\n};\n\nexport const CORE_STORAGE_OPTIONS = {\n  database: \":memory:\",\n};\n", "import { ONE_DAY } from \"@walletconnect/time\";\n\nexport const CRYPTO_CONTEXT = \"crypto\";\n\nexport const CRYPTO_CLIENT_SEED = \"client_ed25519_seed\";\n\nexport const CRYPTO_JWT_TTL = ONE_DAY;\n", "export const KEYCHAIN_CONTEXT = \"keychain\";\n\nexport const KEYCHAIN_STORAGE_VERSION = \"0.3\";\n", "export const MESSAGES_CONTEXT = \"messages\";\n\nexport const MESSAGES_STORAGE_VERSION = \"0.3\";\n", "import { SIX_HOURS } from \"@walletconnect/time\";\n\nexport const PUBLISHER_DEFAULT_TTL = SIX_HOURS;\n\nexport const PUBLISHER_CONTEXT = \"publisher\";\n", "export const RELAYER_DEFAULT_PROTOCOL = \"irn\";\n\nexport const RELAYER_DEFAULT_LOGGER = \"error\";\n\nexport const RELAYER_DEFAULT_RELAY_URL = \"wss://relay.walletconnect.org\";\n\nexport const RELAYER_CONTEXT = \"relayer\";\n\nexport const RELAYER_EVENTS = {\n  message: \"relayer_message\",\n  message_ack: \"relayer_message_ack\",\n  connect: \"relayer_connect\",\n  disconnect: \"relayer_disconnect\",\n  error: \"relayer_error\",\n  connection_stalled: \"relayer_connection_stalled\",\n  transport_closed: \"relayer_transport_closed\",\n  publish: \"relayer_publish\",\n};\n\nexport const RELAYER_SUBSCRIBER_SUFFIX = \"_subscription\";\n\nexport const RELAYER_PROVIDER_EVENTS = {\n  payload: \"payload\",\n  connect: \"connect\",\n  disconnect: \"disconnect\",\n  error: \"error\",\n};\n\nexport const RELAYER_RECONNECT_TIMEOUT = 0.1;\n\nexport const RELAYER_STORAGE_OPTIONS = {\n  database: \":memory:\",\n};\n\n// Updated automatically via `new-version` npm script.\n\nexport const RELAYER_SDK_VERSION = \"2.19.0\";\n\n// delay to wait before closing the transport connection after init if not active\nexport const RELAYER_TRANSPORT_CUTOFF = 10_000;\n\nexport const TRANSPORT_TYPES = {\n  link_mode: \"link_mode\",\n  relay: \"relay\",\n} as const;\n", "export const STORE_STORAGE_VERSION = \"0.3\";\n\nexport const WALLETCONNECT_CLIENT_ID = \"WALLETCONNECT_CLIENT_ID\";\nexport const WALLETCONNECT_LINK_MODE_APPS = \"WALLETCONNECT_LINK_MODE_APPS\";\n", "import { THIRTY_DAYS, FIVE_SECONDS } from \"@walletconnect/time\";\n\nexport const SUBSCRIBER_EVENTS = {\n  created: \"subscription_created\",\n  deleted: \"subscription_deleted\",\n  expired: \"subscription_expired\",\n  disabled: \"subscription_disabled\",\n  sync: \"subscription_sync\",\n  resubscribed: \"subscription_resubscribed\",\n};\n\nexport const SUBSCRIBER_DEFAULT_TTL = THIRTY_DAYS;\n\nexport const SUBSCRIBER_CONTEXT = \"subscription\";\n\nexport const SUBSCRIBER_STORAGE_VERSION = \"0.3\";\n\nexport const PENDING_SUB_RESOLUTION_TIMEOUT = FIVE_SECONDS * 1000;\n", "import { THIRTY_DAYS, ONE_DAY, THIRTY_SECONDS } from \"@walletconnect/time\";\nimport { RelayerTypes, PairingJsonRpcTypes } from \"@walletconnect/types\";\n\nexport const PAIRING_CONTEXT = \"pairing\";\n\nexport const PAIRING_STORAGE_VERSION = \"0.3\";\n\nexport const PAIRING_DEFAULT_TTL = THIRTY_DAYS;\n\nexport const PAIRING_RPC_OPTS: Record<\n  PairingJsonRpcTypes.WcMethod | \"unregistered_method\",\n  {\n    req: RelayerTypes.PublishOptions;\n    res: RelayerTypes.PublishOptions;\n  }\n> = {\n  wc_pairingDelete: {\n    req: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1000,\n    },\n    res: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1001,\n    },\n  },\n  wc_pairingPing: {\n    req: {\n      ttl: THIRTY_SECONDS,\n      prompt: false,\n      tag: 1002,\n    },\n    res: {\n      ttl: THIRTY_SECONDS,\n      prompt: false,\n      tag: 1003,\n    },\n  },\n  unregistered_method: {\n    req: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 0,\n    },\n    res: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 0,\n    },\n  },\n};\n\nexport const PAIRING_EVENTS = {\n  create: \"pairing_create\",\n  expire: \"pairing_expire\",\n  delete: \"pairing_delete\",\n  ping: \"pairing_ping\",\n};\n", "export const HISTORY_EVENTS = {\n  created: \"history_created\",\n  updated: \"history_updated\",\n  deleted: \"history_deleted\",\n  sync: \"history_sync\",\n};\n\nexport const HISTORY_CONTEXT = \"history\";\n\nexport const HISTORY_STORAGE_VERSION = \"0.3\";\n", "import { ONE_DAY } from \"@walletconnect/time\";\n\nexport const EXPIRER_CONTEXT = \"expirer\";\n\nexport const EXPIRER_EVENTS = {\n  created: \"expirer_created\",\n  deleted: \"expirer_deleted\",\n  expired: \"expirer_expired\",\n  sync: \"expirer_sync\",\n};\n\nexport const EXPIRER_STORAGE_VERSION = \"0.3\";\n\nexport const EXPIRER_DEFAULT_TTL = ONE_DAY;\n", "export const VERIFY_CONTEXT = \"verify-api\";\n\nconst VERIFY_SERVER_COM = \"https://verify.walletconnect.com\";\nconst VERIFY_SERVER_ORG = \"https://verify.walletconnect.org\";\nexport const VERIFY_SERVER = VERIFY_SERVER_ORG;\nexport const VERIFY_SERVER_V3 = `${VERIFY_SERVER}/v3`;\n\nexport const TRUSTED_VERIFY_URLS = [VERIFY_SERVER_COM, VERIFY_SERVER_ORG];\n", "export const ECHO_CONTEXT = \"echo\";\n\nexport const ECHO_URL = \"https://echo.walletconnect.com\";\n", "export const EVENT_CLIENT_CONTEXT = \"event-client\";\n\nexport const EVENT_CLIENT_PAIRING_TRACES = {\n  pairing_started: \"pairing_started\",\n  pairing_uri_validation_success: \"pairing_uri_validation_success\",\n  pairing_uri_not_expired: \"pairing_uri_not_expired\",\n  store_new_pairing: \"store_new_pairing\",\n  subscribing_pairing_topic: \"subscribing_pairing_topic\",\n  subscribe_pairing_topic_success: \"subscribe_pairing_topic_success\",\n  existing_pairing: \"existing_pairing\",\n  pairing_not_expired: \"pairing_not_expired\",\n  emit_inactive_pairing: \"emit_inactive_pairing\",\n  emit_session_proposal: \"emit_session_proposal\",\n  subscribing_to_pairing_topic: \"subscribing_to_pairing_topic\",\n};\n\nexport const EVENT_CLIENT_PAIRING_ERRORS = {\n  no_wss_connection: \"no_wss_connection\",\n  no_internet_connection: \"no_internet_connection\",\n  malformed_pairing_uri: \"malformed_pairing_uri\",\n  active_pairing_already_exists: \"active_pairing_already_exists\",\n  subscribe_pairing_topic_failure: \"subscribe_pairing_topic_failure\",\n  pairing_expired: \"pairing_expired\",\n  proposal_expired: \"proposal_expired\",\n  proposal_listener_not_found: \"proposal_listener_not_found\",\n};\n\nexport const EVENT_CLIENT_SESSION_TRACES = {\n  session_approve_started: \"session_approve_started\",\n  proposal_not_expired: \"proposal_not_expired\",\n  session_namespaces_validation_success: \"session_namespaces_validation_success\",\n  create_session_topic: \"create_session_topic\",\n  subscribing_session_topic: \"subscribing_session_topic\",\n  subscribe_session_topic_success: \"subscribe_session_topic_success\",\n  publishing_session_approve: \"publishing_session_approve\",\n  session_approve_publish_success: \"session_approve_publish_success\",\n  store_session: \"store_session\",\n  publishing_session_settle: \"publishing_session_settle\",\n  session_settle_publish_success: \"session_settle_publish_success\",\n};\n\nexport const EVENT_CLIENT_SESSION_ERRORS = {\n  no_internet_connection: \"no_internet_connection\",\n  no_wss_connection: \"no_wss_connection\",\n  proposal_expired: \"proposal_expired\",\n  subscribe_session_topic_failure: \"subscribe_session_topic_failure\",\n  session_approve_publish_failure: \"session_approve_publish_failure\",\n  session_settle_publish_failure: \"session_settle_publish_failure\",\n  session_approve_namespace_validation_failure: \"session_approve_namespace_validation_failure\",\n  proposal_not_found: \"proposal_not_found\",\n};\n\nexport const EVENT_CLIENT_AUTHENTICATE_TRACES = {\n  authenticated_session_approve_started: \"authenticated_session_approve_started\",\n  authenticated_session_not_expired: \"authenticated_session_not_expired\",\n  chains_caip2_compliant: \"chains_caip2_compliant\",\n  chains_evm_compliant: \"chains_evm_compliant\",\n  create_authenticated_session_topic: \"create_authenticated_session_topic\",\n  cacaos_verified: \"cacaos_verified\",\n  store_authenticated_session: \"store_authenticated_session\",\n  subscribing_authenticated_session_topic: \"subscribing_authenticated_session_topic\",\n  subscribe_authenticated_session_topic_success: \"subscribe_authenticated_session_topic_success\",\n  publishing_authenticated_session_approve: \"publishing_authenticated_session_approve\",\n  authenticated_session_approve_publish_success: \"authenticated_session_approve_publish_success\",\n};\n\nexport const EVENT_CLIENT_AUTHENTICATE_ERRORS = {\n  no_internet_connection: \"no_internet_connection\",\n  no_wss_connection: \"no_wss_connection\",\n  missing_session_authenticate_request: \"missing_session_authenticate_request\",\n  session_authenticate_request_expired: \"session_authenticate_request_expired\",\n  chains_caip2_compliant_failure: \"chains_caip2_compliant_failure\",\n  chains_evm_compliant_failure: \"chains_evm_compliant_failure\",\n  invalid_cacao: \"invalid_cacao\",\n  subscribe_authenticated_session_topic_failure: \"subscribe_authenticated_session_topic_failure\",\n  authenticated_session_approve_publish_failure: \"authenticated_session_approve_publish_failure\",\n  authenticated_session_pending_request_not_found:\n    \"authenticated_session_pending_request_not_found\",\n};\n\nexport const EVENTS_STORAGE_VERSION = 0.1;\n\nexport const EVENTS_STORAGE_CONTEXT = \"event-client\";\n\nexport const EVENTS_STORAGE_CLEANUP_INTERVAL = 86400;\n\nexport const EVENTS_CLIENT_API_URL = \"https://pulse.walletconnect.org/batch\";\n", "function base(ALPHABET, name) {\n  if (ALPHABET.length >= 255) {\n    throw new TypeError('Alphabet too long');\n  }\n  var BASE_MAP = new Uint8Array(256);\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255;\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i);\n    var xc = x.charCodeAt(0);\n    if (BASE_MAP[xc] !== 255) {\n      throw new TypeError(x + ' is ambiguous');\n    }\n    BASE_MAP[xc] = i;\n  }\n  var BASE = ALPHABET.length;\n  var LEADER = ALPHABET.charAt(0);\n  var FACTOR = Math.log(BASE) / Math.log(256);\n  var iFACTOR = Math.log(256) / Math.log(BASE);\n  function encode(source) {\n    if (source instanceof Uint8Array);\n    else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength);\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source);\n    }\n    if (!(source instanceof Uint8Array)) {\n      throw new TypeError('Expected Uint8Array');\n    }\n    if (source.length === 0) {\n      return '';\n    }\n    var zeroes = 0;\n    var length = 0;\n    var pbegin = 0;\n    var pend = source.length;\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++;\n      zeroes++;\n    }\n    var size = (pend - pbegin) * iFACTOR + 1 >>> 0;\n    var b58 = new Uint8Array(size);\n    while (pbegin !== pend) {\n      var carry = source[pbegin];\n      var i = 0;\n      for (var it1 = size - 1; (carry !== 0 || i < length) && it1 !== -1; it1--, i++) {\n        carry += 256 * b58[it1] >>> 0;\n        b58[it1] = carry % BASE >>> 0;\n        carry = carry / BASE >>> 0;\n      }\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n      length = i;\n      pbegin++;\n    }\n    var it2 = size - length;\n    while (it2 !== size && b58[it2] === 0) {\n      it2++;\n    }\n    var str = LEADER.repeat(zeroes);\n    for (; it2 < size; ++it2) {\n      str += ALPHABET.charAt(b58[it2]);\n    }\n    return str;\n  }\n  function decodeUnsafe(source) {\n    if (typeof source !== 'string') {\n      throw new TypeError('Expected String');\n    }\n    if (source.length === 0) {\n      return new Uint8Array();\n    }\n    var psz = 0;\n    if (source[psz] === ' ') {\n      return;\n    }\n    var zeroes = 0;\n    var length = 0;\n    while (source[psz] === LEADER) {\n      zeroes++;\n      psz++;\n    }\n    var size = (source.length - psz) * FACTOR + 1 >>> 0;\n    var b256 = new Uint8Array(size);\n    while (source[psz]) {\n      var carry = BASE_MAP[source.charCodeAt(psz)];\n      if (carry === 255) {\n        return;\n      }\n      var i = 0;\n      for (var it3 = size - 1; (carry !== 0 || i < length) && it3 !== -1; it3--, i++) {\n        carry += BASE * b256[it3] >>> 0;\n        b256[it3] = carry % 256 >>> 0;\n        carry = carry / 256 >>> 0;\n      }\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n      length = i;\n      psz++;\n    }\n    if (source[psz] === ' ') {\n      return;\n    }\n    var it4 = size - length;\n    while (it4 !== size && b256[it4] === 0) {\n      it4++;\n    }\n    var vch = new Uint8Array(zeroes + (size - it4));\n    var j = zeroes;\n    while (it4 !== size) {\n      vch[j++] = b256[it4++];\n    }\n    return vch;\n  }\n  function decode(string) {\n    var buffer = decodeUnsafe(string);\n    if (buffer) {\n      return buffer;\n    }\n    throw new Error(`Non-${ name } character`);\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  };\n}\nvar src = base;\nvar _brrp__multiformats_scope_baseX = src;\nexport default _brrp__multiformats_scope_baseX;", "const empty = new Uint8Array(0);\nconst toHex = d => d.reduce((hex, byte) => hex + byte.toString(16).padStart(2, '0'), '');\nconst fromHex = hex => {\n  const hexes = hex.match(/../g);\n  return hexes ? new Uint8Array(hexes.map(b => parseInt(b, 16))) : empty;\n};\nconst equals = (aa, bb) => {\n  if (aa === bb)\n    return true;\n  if (aa.byteLength !== bb.byteLength) {\n    return false;\n  }\n  for (let ii = 0; ii < aa.byteLength; ii++) {\n    if (aa[ii] !== bb[ii]) {\n      return false;\n    }\n  }\n  return true;\n};\nconst coerce = o => {\n  if (o instanceof Uint8Array && o.constructor.name === 'Uint8Array')\n    return o;\n  if (o instanceof ArrayBuffer)\n    return new Uint8Array(o);\n  if (ArrayBuffer.isView(o)) {\n    return new Uint8Array(o.buffer, o.byteOffset, o.byteLength);\n  }\n  throw new Error('Unknown type, must be binary type');\n};\nconst isBinary = o => o instanceof ArrayBuffer || ArrayBuffer.isView(o);\nconst fromString = str => new TextEncoder().encode(str);\nconst toString = b => new TextDecoder().decode(b);\nexport {\n  equals,\n  coerce,\n  isBinary,\n  fromHex,\n  toHex,\n  fromString,\n  toString,\n  empty\n};", "import basex from '../../vendor/base-x.js';\nimport { coerce } from '../bytes.js';\nclass Encoder {\n  constructor(name, prefix, baseEncode) {\n    this.name = name;\n    this.prefix = prefix;\n    this.baseEncode = baseEncode;\n  }\n  encode(bytes) {\n    if (bytes instanceof Uint8Array) {\n      return `${ this.prefix }${ this.baseEncode(bytes) }`;\n    } else {\n      throw Error('Unknown type, must be binary type');\n    }\n  }\n}\nclass Decoder {\n  constructor(name, prefix, baseDecode) {\n    this.name = name;\n    this.prefix = prefix;\n    if (prefix.codePointAt(0) === undefined) {\n      throw new Error('Invalid prefix character');\n    }\n    this.prefixCodePoint = prefix.codePointAt(0);\n    this.baseDecode = baseDecode;\n  }\n  decode(text) {\n    if (typeof text === 'string') {\n      if (text.codePointAt(0) !== this.prefixCodePoint) {\n        throw Error(`Unable to decode multibase string ${ JSON.stringify(text) }, ${ this.name } decoder only supports inputs prefixed with ${ this.prefix }`);\n      }\n      return this.baseDecode(text.slice(this.prefix.length));\n    } else {\n      throw Error('Can only multibase decode strings');\n    }\n  }\n  or(decoder) {\n    return or(this, decoder);\n  }\n}\nclass ComposedDecoder {\n  constructor(decoders) {\n    this.decoders = decoders;\n  }\n  or(decoder) {\n    return or(this, decoder);\n  }\n  decode(input) {\n    const prefix = input[0];\n    const decoder = this.decoders[prefix];\n    if (decoder) {\n      return decoder.decode(input);\n    } else {\n      throw RangeError(`Unable to decode multibase string ${ JSON.stringify(input) }, only inputs prefixed with ${ Object.keys(this.decoders) } are supported`);\n    }\n  }\n}\nexport const or = (left, right) => new ComposedDecoder({\n  ...left.decoders || { [left.prefix]: left },\n  ...right.decoders || { [right.prefix]: right }\n});\nexport class Codec {\n  constructor(name, prefix, baseEncode, baseDecode) {\n    this.name = name;\n    this.prefix = prefix;\n    this.baseEncode = baseEncode;\n    this.baseDecode = baseDecode;\n    this.encoder = new Encoder(name, prefix, baseEncode);\n    this.decoder = new Decoder(name, prefix, baseDecode);\n  }\n  encode(input) {\n    return this.encoder.encode(input);\n  }\n  decode(input) {\n    return this.decoder.decode(input);\n  }\n}\nexport const from = ({name, prefix, encode, decode}) => new Codec(name, prefix, encode, decode);\nexport const baseX = ({prefix, name, alphabet}) => {\n  const {encode, decode} = basex(alphabet, name);\n  return from({\n    prefix,\n    name,\n    encode,\n    decode: text => coerce(decode(text))\n  });\n};\nconst decode = (string, alphabet, bitsPerChar, name) => {\n  const codes = {};\n  for (let i = 0; i < alphabet.length; ++i) {\n    codes[alphabet[i]] = i;\n  }\n  let end = string.length;\n  while (string[end - 1] === '=') {\n    --end;\n  }\n  const out = new Uint8Array(end * bitsPerChar / 8 | 0);\n  let bits = 0;\n  let buffer = 0;\n  let written = 0;\n  for (let i = 0; i < end; ++i) {\n    const value = codes[string[i]];\n    if (value === undefined) {\n      throw new SyntaxError(`Non-${ name } character`);\n    }\n    buffer = buffer << bitsPerChar | value;\n    bits += bitsPerChar;\n    if (bits >= 8) {\n      bits -= 8;\n      out[written++] = 255 & buffer >> bits;\n    }\n  }\n  if (bits >= bitsPerChar || 255 & buffer << 8 - bits) {\n    throw new SyntaxError('Unexpected end of data');\n  }\n  return out;\n};\nconst encode = (data, alphabet, bitsPerChar) => {\n  const pad = alphabet[alphabet.length - 1] === '=';\n  const mask = (1 << bitsPerChar) - 1;\n  let out = '';\n  let bits = 0;\n  let buffer = 0;\n  for (let i = 0; i < data.length; ++i) {\n    buffer = buffer << 8 | data[i];\n    bits += 8;\n    while (bits > bitsPerChar) {\n      bits -= bitsPerChar;\n      out += alphabet[mask & buffer >> bits];\n    }\n  }\n  if (bits) {\n    out += alphabet[mask & buffer << bitsPerChar - bits];\n  }\n  if (pad) {\n    while (out.length * bitsPerChar & 7) {\n      out += '=';\n    }\n  }\n  return out;\n};\nexport const rfc4648 = ({name, prefix, bitsPerChar, alphabet}) => {\n  return from({\n    prefix,\n    name,\n    encode(input) {\n      return encode(input, alphabet, bitsPerChar);\n    },\n    decode(input) {\n      return decode(input, alphabet, bitsPerChar, name);\n    }\n  });\n};", "import { from } from './base.js';\nimport {\n  fromString,\n  toString\n} from '../bytes.js';\nexport const identity = from({\n  prefix: '\\0',\n  name: 'identity',\n  encode: buf => toString(buf),\n  decode: str => fromString(str)\n});", "import { rfc4648 } from './base.js';\nexport const base2 = rfc4648({\n  prefix: '0',\n  name: 'base2',\n  alphabet: '01',\n  bitsPerChar: 1\n});", "import { rfc4648 } from './base.js';\nexport const base8 = rfc4648({\n  prefix: '7',\n  name: 'base8',\n  alphabet: '01234567',\n  bitsPerChar: 3\n});", "import { baseX } from './base.js';\nexport const base10 = baseX({\n  prefix: '9',\n  name: 'base10',\n  alphabet: '0123456789'\n});", "import { rfc4648 } from './base.js';\nexport const base16 = rfc4648({\n  prefix: 'f',\n  name: 'base16',\n  alphabet: '0123456789abcdef',\n  bitsPerChar: 4\n});\nexport const base16upper = rfc4648({\n  prefix: 'F',\n  name: 'base16upper',\n  alphabet: '0123456789ABCDEF',\n  bitsPerChar: 4\n});", "import { rfc4648 } from './base.js';\nexport const base32 = rfc4648({\n  prefix: 'b',\n  name: 'base32',\n  alphabet: 'abcdefghijklmnopqrstuvwxyz234567',\n  bitsPerChar: 5\n});\nexport const base32upper = rfc4648({\n  prefix: 'B',\n  name: 'base32upper',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567',\n  bitsPerChar: 5\n});\nexport const base32pad = rfc4648({\n  prefix: 'c',\n  name: 'base32pad',\n  alphabet: 'abcdefghijklmnopqrstuvwxyz234567=',\n  bitsPerChar: 5\n});\nexport const base32padupper = rfc4648({\n  prefix: 'C',\n  name: 'base32padupper',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=',\n  bitsPerChar: 5\n});\nexport const base32hex = rfc4648({\n  prefix: 'v',\n  name: 'base32hex',\n  alphabet: '0123456789abcdefghijklmnopqrstuv',\n  bitsPerChar: 5\n});\nexport const base32hexupper = rfc4648({\n  prefix: 'V',\n  name: 'base32hexupper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUV',\n  bitsPerChar: 5\n});\nexport const base32hexpad = rfc4648({\n  prefix: 't',\n  name: 'base32hexpad',\n  alphabet: '0123456789abcdefghijklmnopqrstuv=',\n  bitsPerChar: 5\n});\nexport const base32hexpadupper = rfc4648({\n  prefix: 'T',\n  name: 'base32hexpadupper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUV=',\n  bitsPerChar: 5\n});\nexport const base32z = rfc4648({\n  prefix: 'h',\n  name: 'base32z',\n  alphabet: 'ybndrfg8ejkmcpqxot1uwisza345h769',\n  bitsPerChar: 5\n});", "import { baseX } from './base.js';\nexport const base36 = baseX({\n  prefix: 'k',\n  name: 'base36',\n  alphabet: '0123456789abcdefghijklmnopqrstuvwxyz'\n});\nexport const base36upper = baseX({\n  prefix: 'K',\n  name: 'base36upper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'\n});", "import { baseX } from './base.js';\nexport const base58btc = baseX({\n  name: 'base58btc',\n  prefix: 'z',\n  alphabet: '**********************************************************'\n});\nexport const base58flickr = baseX({\n  name: 'base58flickr',\n  prefix: 'Z',\n  alphabet: '**********************************************************'\n});", "import { rfc4648 } from './base.js';\nexport const base64 = rfc4648({\n  prefix: 'm',\n  name: 'base64',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',\n  bitsPerChar: 6\n});\nexport const base64pad = rfc4648({\n  prefix: 'M',\n  name: 'base64pad',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',\n  bitsPerChar: 6\n});\nexport const base64url = rfc4648({\n  prefix: 'u',\n  name: 'base64url',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_',\n  bitsPerChar: 6\n});\nexport const base64urlpad = rfc4648({\n  prefix: 'U',\n  name: 'base64urlpad',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=',\n  bitsPerChar: 6\n});", "import { from } from './base.js';\nconst alphabet = Array.from('\\uD83D\\uDE80\\uD83E\\uDE90\\u2604\\uD83D\\uDEF0\\uD83C\\uDF0C\\uD83C\\uDF11\\uD83C\\uDF12\\uD83C\\uDF13\\uD83C\\uDF14\\uD83C\\uDF15\\uD83C\\uDF16\\uD83C\\uDF17\\uD83C\\uDF18\\uD83C\\uDF0D\\uD83C\\uDF0F\\uD83C\\uDF0E\\uD83D\\uDC09\\u2600\\uD83D\\uDCBB\\uD83D\\uDDA5\\uD83D\\uDCBE\\uD83D\\uDCBF\\uD83D\\uDE02\\u2764\\uD83D\\uDE0D\\uD83E\\uDD23\\uD83D\\uDE0A\\uD83D\\uDE4F\\uD83D\\uDC95\\uD83D\\uDE2D\\uD83D\\uDE18\\uD83D\\uDC4D\\uD83D\\uDE05\\uD83D\\uDC4F\\uD83D\\uDE01\\uD83D\\uDD25\\uD83E\\uDD70\\uD83D\\uDC94\\uD83D\\uDC96\\uD83D\\uDC99\\uD83D\\uDE22\\uD83E\\uDD14\\uD83D\\uDE06\\uD83D\\uDE44\\uD83D\\uDCAA\\uD83D\\uDE09\\u263A\\uD83D\\uDC4C\\uD83E\\uDD17\\uD83D\\uDC9C\\uD83D\\uDE14\\uD83D\\uDE0E\\uD83D\\uDE07\\uD83C\\uDF39\\uD83E\\uDD26\\uD83C\\uDF89\\uD83D\\uDC9E\\u270C\\u2728\\uD83E\\uDD37\\uD83D\\uDE31\\uD83D\\uDE0C\\uD83C\\uDF38\\uD83D\\uDE4C\\uD83D\\uDE0B\\uD83D\\uDC97\\uD83D\\uDC9A\\uD83D\\uDE0F\\uD83D\\uDC9B\\uD83D\\uDE42\\uD83D\\uDC93\\uD83E\\uDD29\\uD83D\\uDE04\\uD83D\\uDE00\\uD83D\\uDDA4\\uD83D\\uDE03\\uD83D\\uDCAF\\uD83D\\uDE48\\uD83D\\uDC47\\uD83C\\uDFB6\\uD83D\\uDE12\\uD83E\\uDD2D\\u2763\\uD83D\\uDE1C\\uD83D\\uDC8B\\uD83D\\uDC40\\uD83D\\uDE2A\\uD83D\\uDE11\\uD83D\\uDCA5\\uD83D\\uDE4B\\uD83D\\uDE1E\\uD83D\\uDE29\\uD83D\\uDE21\\uD83E\\uDD2A\\uD83D\\uDC4A\\uD83E\\uDD73\\uD83D\\uDE25\\uD83E\\uDD24\\uD83D\\uDC49\\uD83D\\uDC83\\uD83D\\uDE33\\u270B\\uD83D\\uDE1A\\uD83D\\uDE1D\\uD83D\\uDE34\\uD83C\\uDF1F\\uD83D\\uDE2C\\uD83D\\uDE43\\uD83C\\uDF40\\uD83C\\uDF37\\uD83D\\uDE3B\\uD83D\\uDE13\\u2B50\\u2705\\uD83E\\uDD7A\\uD83C\\uDF08\\uD83D\\uDE08\\uD83E\\uDD18\\uD83D\\uDCA6\\u2714\\uD83D\\uDE23\\uD83C\\uDFC3\\uD83D\\uDC90\\u2639\\uD83C\\uDF8A\\uD83D\\uDC98\\uD83D\\uDE20\\u261D\\uD83D\\uDE15\\uD83C\\uDF3A\\uD83C\\uDF82\\uD83C\\uDF3B\\uD83D\\uDE10\\uD83D\\uDD95\\uD83D\\uDC9D\\uD83D\\uDE4A\\uD83D\\uDE39\\uD83D\\uDDE3\\uD83D\\uDCAB\\uD83D\\uDC80\\uD83D\\uDC51\\uD83C\\uDFB5\\uD83E\\uDD1E\\uD83D\\uDE1B\\uD83D\\uDD34\\uD83D\\uDE24\\uD83C\\uDF3C\\uD83D\\uDE2B\\u26BD\\uD83E\\uDD19\\u2615\\uD83C\\uDFC6\\uD83E\\uDD2B\\uD83D\\uDC48\\uD83D\\uDE2E\\uD83D\\uDE46\\uD83C\\uDF7B\\uD83C\\uDF43\\uD83D\\uDC36\\uD83D\\uDC81\\uD83D\\uDE32\\uD83C\\uDF3F\\uD83E\\uDDE1\\uD83C\\uDF81\\u26A1\\uD83C\\uDF1E\\uD83C\\uDF88\\u274C\\u270A\\uD83D\\uDC4B\\uD83D\\uDE30\\uD83E\\uDD28\\uD83D\\uDE36\\uD83E\\uDD1D\\uD83D\\uDEB6\\uD83D\\uDCB0\\uD83C\\uDF53\\uD83D\\uDCA2\\uD83E\\uDD1F\\uD83D\\uDE41\\uD83D\\uDEA8\\uD83D\\uDCA8\\uD83E\\uDD2C\\u2708\\uD83C\\uDF80\\uD83C\\uDF7A\\uD83E\\uDD13\\uD83D\\uDE19\\uD83D\\uDC9F\\uD83C\\uDF31\\uD83D\\uDE16\\uD83D\\uDC76\\uD83E\\uDD74\\u25B6\\u27A1\\u2753\\uD83D\\uDC8E\\uD83D\\uDCB8\\u2B07\\uD83D\\uDE28\\uD83C\\uDF1A\\uD83E\\uDD8B\\uD83D\\uDE37\\uD83D\\uDD7A\\u26A0\\uD83D\\uDE45\\uD83D\\uDE1F\\uD83D\\uDE35\\uD83D\\uDC4E\\uD83E\\uDD32\\uD83E\\uDD20\\uD83E\\uDD27\\uD83D\\uDCCC\\uD83D\\uDD35\\uD83D\\uDC85\\uD83E\\uDDD0\\uD83D\\uDC3E\\uD83C\\uDF52\\uD83D\\uDE17\\uD83E\\uDD11\\uD83C\\uDF0A\\uD83E\\uDD2F\\uD83D\\uDC37\\u260E\\uD83D\\uDCA7\\uD83D\\uDE2F\\uD83D\\uDC86\\uD83D\\uDC46\\uD83C\\uDFA4\\uD83D\\uDE47\\uD83C\\uDF51\\u2744\\uD83C\\uDF34\\uD83D\\uDCA3\\uD83D\\uDC38\\uD83D\\uDC8C\\uD83D\\uDCCD\\uD83E\\uDD40\\uD83E\\uDD22\\uD83D\\uDC45\\uD83D\\uDCA1\\uD83D\\uDCA9\\uD83D\\uDC50\\uD83D\\uDCF8\\uD83D\\uDC7B\\uD83E\\uDD10\\uD83E\\uDD2E\\uD83C\\uDFBC\\uD83E\\uDD75\\uD83D\\uDEA9\\uD83C\\uDF4E\\uD83C\\uDF4A\\uD83D\\uDC7C\\uD83D\\uDC8D\\uD83D\\uDCE3\\uD83E\\uDD42');\nconst alphabetBytesToChars = alphabet.reduce((p, c, i) => {\n  p[i] = c;\n  return p;\n}, []);\nconst alphabetCharsToBytes = alphabet.reduce((p, c, i) => {\n  p[c.codePointAt(0)] = i;\n  return p;\n}, []);\nfunction encode(data) {\n  return data.reduce((p, c) => {\n    p += alphabetBytesToChars[c];\n    return p;\n  }, '');\n}\nfunction decode(str) {\n  const byts = [];\n  for (const char of str) {\n    const byt = alphabetCharsToBytes[char.codePointAt(0)];\n    if (byt === undefined) {\n      throw new Error(`Non-base256emoji character: ${ char }`);\n    }\n    byts.push(byt);\n  }\n  return new Uint8Array(byts);\n}\nexport const base256emoji = from({\n  prefix: '\\uD83D\\uDE80',\n  name: 'base256emoji',\n  encode,\n  decode\n});", "var encode_1 = encode;\nvar MSB = 128, REST = 127, MSBALL = ~REST, INT = Math.pow(2, 31);\nfunction encode(num, out, offset) {\n  out = out || [];\n  offset = offset || 0;\n  var oldOffset = offset;\n  while (num >= INT) {\n    out[offset++] = num & 255 | MSB;\n    num /= 128;\n  }\n  while (num & MSBALL) {\n    out[offset++] = num & 255 | MSB;\n    num >>>= 7;\n  }\n  out[offset] = num | 0;\n  encode.bytes = offset - oldOffset + 1;\n  return out;\n}\nvar decode = read;\nvar MSB$1 = 128, REST$1 = 127;\nfunction read(buf, offset) {\n  var res = 0, offset = offset || 0, shift = 0, counter = offset, b, l = buf.length;\n  do {\n    if (counter >= l) {\n      read.bytes = 0;\n      throw new RangeError('Could not decode varint');\n    }\n    b = buf[counter++];\n    res += shift < 28 ? (b & REST$1) << shift : (b & REST$1) * Math.pow(2, shift);\n    shift += 7;\n  } while (b >= MSB$1);\n  read.bytes = counter - offset;\n  return res;\n}\nvar N1 = Math.pow(2, 7);\nvar N2 = Math.pow(2, 14);\nvar N3 = Math.pow(2, 21);\nvar N4 = Math.pow(2, 28);\nvar N5 = Math.pow(2, 35);\nvar N6 = Math.pow(2, 42);\nvar N7 = Math.pow(2, 49);\nvar N8 = Math.pow(2, 56);\nvar N9 = Math.pow(2, 63);\nvar length = function (value) {\n  return value < N1 ? 1 : value < N2 ? 2 : value < N3 ? 3 : value < N4 ? 4 : value < N5 ? 5 : value < N6 ? 6 : value < N7 ? 7 : value < N8 ? 8 : value < N9 ? 9 : 10;\n};\nvar varint = {\n  encode: encode_1,\n  decode: decode,\n  encodingLength: length\n};\nvar _brrp_varint = varint;\nexport default _brrp_varint;", "import varint from '../vendor/varint.js';\nexport const decode = (data, offset = 0) => {\n  const code = varint.decode(data, offset);\n  return [\n    code,\n    varint.decode.bytes\n  ];\n};\nexport const encodeTo = (int, target, offset = 0) => {\n  varint.encode(int, target, offset);\n  return target;\n};\nexport const encodingLength = int => {\n  return varint.encodingLength(int);\n};", "import {\n  coerce,\n  equals as equalBytes\n} from '../bytes.js';\nimport * as varint from '../varint.js';\nexport const create = (code, digest) => {\n  const size = digest.byteLength;\n  const sizeOffset = varint.encodingLength(code);\n  const digestOffset = sizeOffset + varint.encodingLength(size);\n  const bytes = new Uint8Array(digestOffset + size);\n  varint.encodeTo(code, bytes, 0);\n  varint.encodeTo(size, bytes, sizeOffset);\n  bytes.set(digest, digestOffset);\n  return new Digest(code, size, digest, bytes);\n};\nexport const decode = multihash => {\n  const bytes = coerce(multihash);\n  const [code, sizeOffset] = varint.decode(bytes);\n  const [size, digestOffset] = varint.decode(bytes.subarray(sizeOffset));\n  const digest = bytes.subarray(sizeOffset + digestOffset);\n  if (digest.byteLength !== size) {\n    throw new Error('Incorrect length');\n  }\n  return new Digest(code, size, digest, bytes);\n};\nexport const equals = (a, b) => {\n  if (a === b) {\n    return true;\n  } else {\n    return a.code === b.code && a.size === b.size && equalBytes(a.bytes, b.bytes);\n  }\n};\nexport class Digest {\n  constructor(code, size, digest, bytes) {\n    this.code = code;\n    this.size = size;\n    this.digest = digest;\n    this.bytes = bytes;\n  }\n}", "import * as Digest from './digest.js';\nexport const from = ({name, code, encode}) => new Hasher(name, code, encode);\nexport class Hasher {\n  constructor(name, code, encode) {\n    this.name = name;\n    this.code = code;\n    this.encode = encode;\n  }\n  digest(input) {\n    if (input instanceof Uint8Array) {\n      const result = this.encode(input);\n      return result instanceof Uint8Array ? Digest.create(this.code, result) : result.then(digest => Digest.create(this.code, digest));\n    } else {\n      throw Error('Unknown type, must be binary type');\n    }\n  }\n}", "import { from } from './hasher.js';\nconst sha = name => async data => new Uint8Array(await crypto.subtle.digest(name, data));\nexport const sha256 = from({\n  name: 'sha2-256',\n  code: 18,\n  encode: sha('SHA-256')\n});\nexport const sha512 = from({\n  name: 'sha2-512',\n  code: 19,\n  encode: sha('SHA-512')\n});", "import { coerce } from '../bytes.js';\nimport * as Digest from './digest.js';\nconst code = 0;\nconst name = 'identity';\nconst encode = coerce;\nconst digest = input => Digest.create(code, encode(input));\nexport const identity = {\n  code,\n  name,\n  encode,\n  digest\n};", "const textEncoder = new TextEncoder();\nconst textDecoder = new TextDecoder();\nexport const name = 'json';\nexport const code = 512;\nexport const encode = node => textEncoder.encode(JSON.stringify(node));\nexport const decode = data => JSON.parse(textDecoder.decode(data));", "import * as identityBase from './bases/identity.js';\nimport * as base2 from './bases/base2.js';\nimport * as base8 from './bases/base8.js';\nimport * as base10 from './bases/base10.js';\nimport * as base16 from './bases/base16.js';\nimport * as base32 from './bases/base32.js';\nimport * as base36 from './bases/base36.js';\nimport * as base58 from './bases/base58.js';\nimport * as base64 from './bases/base64.js';\nimport * as base256emoji from './bases/base256emoji.js';\nimport * as sha2 from './hashes/sha2.js';\nimport * as identity from './hashes/identity.js';\nimport * as raw from './codecs/raw.js';\nimport * as json from './codecs/json.js';\nimport {\n  CID,\n  hasher,\n  digest,\n  varint,\n  bytes\n} from './index.js';\nconst bases = {\n  ...identityBase,\n  ...base2,\n  ...base8,\n  ...base10,\n  ...base16,\n  ...base32,\n  ...base36,\n  ...base58,\n  ...base64,\n  ...base256emoji\n};\nconst hashes = {\n  ...sha2,\n  ...identity\n};\nconst codecs = {\n  raw,\n  json\n};\nexport {\n  CID,\n  hasher,\n  digest,\n  varint,\n  bytes,\n  hashes,\n  bases,\n  codecs\n};", "export function alloc(size = 0) {\n  if (globalThis.Buffer != null && globalThis.Buffer.alloc != null) {\n    return globalThis.Buffer.alloc(size);\n  }\n  return new Uint8Array(size);\n}\nexport function allocUnsafe(size = 0) {\n  if (globalThis.Buffer != null && globalThis.Buffer.allocUnsafe != null) {\n    return globalThis.Buffer.allocUnsafe(size);\n  }\n  return new Uint8Array(size);\n}", "import { bases } from 'multiformats/basics';\nimport { allocUnsafe } from '../alloc.js';\nfunction createCodec(name, prefix, encode, decode) {\n  return {\n    name,\n    prefix,\n    encoder: {\n      name,\n      prefix,\n      encode\n    },\n    decoder: { decode }\n  };\n}\nconst string = createCodec('utf8', 'u', buf => {\n  const decoder = new TextDecoder('utf8');\n  return 'u' + decoder.decode(buf);\n}, str => {\n  const encoder = new TextEncoder();\n  return encoder.encode(str.substring(1));\n});\nconst ascii = createCodec('ascii', 'a', buf => {\n  let string = 'a';\n  for (let i = 0; i < buf.length; i++) {\n    string += String.fromCharCode(buf[i]);\n  }\n  return string;\n}, str => {\n  str = str.substring(1);\n  const buf = allocUnsafe(str.length);\n  for (let i = 0; i < str.length; i++) {\n    buf[i] = str.charCodeAt(i);\n  }\n  return buf;\n});\nconst BASES = {\n  utf8: string,\n  'utf-8': string,\n  hex: bases.base16,\n  latin1: ascii,\n  ascii: ascii,\n  binary: ascii,\n  ...bases\n};\nexport default BASES;", "import bases from './util/bases.js';\nexport function fromString(string, encoding = 'utf8') {\n  const base = bases[encoding];\n  if (!base) {\n    throw new Error(`Unsupported encoding \"${ encoding }\"`);\n  }\n  if ((encoding === 'utf8' || encoding === 'utf-8') && globalThis.Buffer != null && globalThis.Buffer.from != null) {\n    return globalThis.Buffer.from(string, 'utf8');\n  }\n  return base.decoder.decode(`${ base.prefix }${ string }`);\n}", "import { generateChild<PERSON>ogger, getLog<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from \"@walletconnect/logger\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>hai<PERSON> } from \"@walletconnect/types\";\nimport { getInternalError, mapToObj, objToMap } from \"@walletconnect/utils\";\n\nimport { CORE_STORAGE_PREFIX, KEYCHAIN_CONTEXT, KEYCHAIN_STORAGE_VERSION } from \"../constants\";\n\nexport class Key<PERSON>hain implements IKeyChain {\n  public keychain = new Map<string, string>();\n  public name = KEYCHAIN_CONTEXT;\n  public version = KEYCHAIN_STORAGE_VERSION;\n\n  private initialized = false;\n  private storagePrefix = CORE_STORAGE_PREFIX;\n\n  constructor(public core: ICore, public logger: Logger) {\n    this.core = core;\n    this.logger = generateChildLogger(logger, this.name);\n  }\n\n  public init: IKeyChain[\"init\"] = async () => {\n    if (!this.initialized) {\n      const keychain = await this.getKeyChain();\n      if (typeof keychain !== \"undefined\") {\n        this.keychain = keychain;\n      }\n      this.initialized = true;\n    }\n  };\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  get storageKey() {\n    return this.storagePrefix + this.version + this.core.customStoragePrefix + \"//\" + this.name;\n  }\n\n  public has: IKeyChain[\"has\"] = (tag) => {\n    this.isInitialized();\n    return this.keychain.has(tag);\n  };\n\n  public set: IKeyChain[\"set\"] = async (tag, key) => {\n    this.isInitialized();\n    this.keychain.set(tag, key);\n    await this.persist();\n  };\n\n  public get: IKeyChain[\"get\"] = (tag) => {\n    this.isInitialized();\n    const key = this.keychain.get(tag);\n    if (typeof key === \"undefined\") {\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `${this.name}: ${tag}`);\n      throw new Error(message);\n    }\n    return key;\n  };\n\n  public del: IKeyChain[\"del\"] = async (tag) => {\n    this.isInitialized();\n    this.keychain.delete(tag);\n    await this.persist();\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async setKeyChain(keychain: Map<string, string>) {\n    await this.core.storage.setItem<Record<string, string>>(this.storageKey, mapToObj(keychain));\n  }\n\n  private async getKeyChain() {\n    const keychain = await this.core.storage.getItem<Record<string, string>>(this.storageKey);\n    return typeof keychain !== \"undefined\" ? objToMap(keychain) : undefined;\n  }\n\n  private async persist() {\n    await this.setKeyChain(this.keychain);\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n}\n", "import { generateChil<PERSON><PERSON>og<PERSON>, getLog<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from \"@walletconnect/logger\";\nimport { safeJsonParse, safeJsonStringify } from \"@walletconnect/safe-json\";\nimport { ICore, ICrypto, I<PERSON>ey<PERSON>hain } from \"@walletconnect/types\";\nimport * as relayAuth from \"@walletconnect/relay-auth\";\nimport { fromString } from \"uint8arrays/from-string\";\nimport {\n  decrypt,\n  deriveSymKey,\n  encrypt,\n  generateKeyPair as generateKeyPairUtil,\n  hashKey,\n  getInternalError,\n  generateRandomBytes32,\n  validateEncoding,\n  validateDecoding,\n  isTypeOneEnvelope,\n  isTypeTwoEnvelope,\n  encodeTypeTwoEnvelope,\n  decodeTypeTwoEnvelope,\n  deserialize,\n  decodeTypeByte,\n  BASE16,\n  BASE64,\n} from \"@walletconnect/utils\";\nimport { toString } from \"uint8arrays\";\n\nimport { CRYPTO_CONTEXT, CRYPTO_CLIENT_SEED, CRYPTO_JWT_TTL } from \"../constants\";\nimport { <PERSON><PERSON><PERSON><PERSON> } from \"./keychain\";\n\nexport class Crypto implements ICrypto {\n  public name = CRYPTO_CONTEXT;\n  public keychain: ICrypto[\"keychain\"];\n  public readonly randomSessionIdentifier = generateRandomBytes32();\n\n  private initialized = false;\n\n  constructor(public core: ICore, public logger: Logger, keychain?: IKeyChain) {\n    this.core = core;\n    this.logger = generateChildLogger(logger, this.name);\n    this.keychain = keychain || new KeyChain(this.core, this.logger);\n  }\n\n  public init: ICrypto[\"init\"] = async () => {\n    if (!this.initialized) {\n      await this.keychain.init();\n      this.initialized = true;\n    }\n  };\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  public hasKeys: ICrypto[\"hasKeys\"] = (tag) => {\n    this.isInitialized();\n    return this.keychain.has(tag);\n  };\n\n  public getClientId: ICrypto[\"getClientId\"] = async () => {\n    this.isInitialized();\n    const seed = await this.getClientSeed();\n    const keyPair = relayAuth.generateKeyPair(seed);\n    const clientId = relayAuth.encodeIss(keyPair.publicKey);\n    return clientId;\n  };\n\n  public generateKeyPair: ICrypto[\"generateKeyPair\"] = () => {\n    this.isInitialized();\n    const keyPair = generateKeyPairUtil();\n    return this.setPrivateKey(keyPair.publicKey, keyPair.privateKey);\n  };\n\n  public signJWT: ICrypto[\"signJWT\"] = async (aud) => {\n    this.isInitialized();\n    const seed = await this.getClientSeed();\n    const keyPair = relayAuth.generateKeyPair(seed);\n    const sub = this.randomSessionIdentifier;\n    const ttl = CRYPTO_JWT_TTL;\n    const jwt = await relayAuth.signJWT(sub, aud, ttl, keyPair);\n    return jwt;\n  };\n\n  public generateSharedKey: ICrypto[\"generateSharedKey\"] = (\n    selfPublicKey,\n    peerPublicKey,\n    overrideTopic,\n  ) => {\n    this.isInitialized();\n    const selfPrivateKey = this.getPrivateKey(selfPublicKey);\n    const symKey = deriveSymKey(selfPrivateKey, peerPublicKey);\n    return this.setSymKey(symKey, overrideTopic);\n  };\n\n  public setSymKey: ICrypto[\"setSymKey\"] = async (symKey, overrideTopic) => {\n    this.isInitialized();\n    const topic = overrideTopic || hashKey(symKey);\n    await this.keychain.set(topic, symKey);\n    return topic;\n  };\n\n  public deleteKeyPair: ICrypto[\"deleteKeyPair\"] = async (publicKey: string) => {\n    this.isInitialized();\n    await this.keychain.del(publicKey);\n  };\n\n  public deleteSymKey: ICrypto[\"deleteSymKey\"] = async (topic: string) => {\n    this.isInitialized();\n    await this.keychain.del(topic);\n  };\n\n  public encode: ICrypto[\"encode\"] = async (topic, payload, opts) => {\n    this.isInitialized();\n    const params = validateEncoding(opts);\n    const message = safeJsonStringify(payload);\n\n    if (isTypeTwoEnvelope(params)) {\n      return encodeTypeTwoEnvelope(message, opts?.encoding);\n    }\n\n    if (isTypeOneEnvelope(params)) {\n      const selfPublicKey = params.senderPublicKey;\n      const peerPublicKey = params.receiverPublicKey;\n      topic = await this.generateSharedKey(selfPublicKey, peerPublicKey);\n    }\n    const symKey = this.getSymKey(topic);\n    const { type, senderPublicKey } = params;\n    const result = encrypt({ type, symKey, message, senderPublicKey, encoding: opts?.encoding });\n    return result;\n  };\n\n  public decode: ICrypto[\"decode\"] = async (topic, encoded, opts) => {\n    this.isInitialized();\n    const params = validateDecoding(encoded, opts);\n    if (isTypeTwoEnvelope(params)) {\n      const message = decodeTypeTwoEnvelope(encoded, opts?.encoding);\n      return safeJsonParse(message);\n    }\n    if (isTypeOneEnvelope(params)) {\n      const selfPublicKey = params.receiverPublicKey;\n      const peerPublicKey = params.senderPublicKey;\n      topic = await this.generateSharedKey(selfPublicKey, peerPublicKey);\n    }\n    try {\n      const symKey = this.getSymKey(topic);\n      const message = decrypt({ symKey, encoded, encoding: opts?.encoding });\n      const payload = safeJsonParse(message);\n      return payload;\n    } catch (error) {\n      this.logger.error(\n        `Failed to decode message from topic: '${topic}', clientId: '${await this.getClientId()}'`,\n      );\n      this.logger.error(error);\n    }\n  };\n\n  public getPayloadType: ICrypto[\"getPayloadType\"] = (encoded, encoding = BASE64) => {\n    const deserialized = deserialize({ encoded, encoding });\n    return decodeTypeByte(deserialized.type);\n  };\n\n  public getPayloadSenderPublicKey: ICrypto[\"getPayloadSenderPublicKey\"] = (\n    encoded,\n    encoding = BASE64,\n  ) => {\n    const deserialized = deserialize({ encoded, encoding });\n    return deserialized.senderPublicKey\n      ? toString(deserialized.senderPublicKey, BASE16)\n      : undefined;\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async setPrivateKey(publicKey: string, privateKey: string): Promise<string> {\n    await this.keychain.set(publicKey, privateKey);\n    return publicKey;\n  }\n\n  private getPrivateKey(publicKey: string) {\n    const privateKey = this.keychain.get(publicKey);\n    return privateKey;\n  }\n\n  private async getClientSeed(): Promise<Uint8Array> {\n    let seed = \"\";\n    try {\n      seed = this.keychain.get(CRYPTO_CLIENT_SEED);\n    } catch {\n      seed = generateRandomBytes32();\n      await this.keychain.set(CRYPTO_CLIENT_SEED, seed);\n    }\n    return fromString(seed, \"base16\");\n  }\n\n  private getSymKey(topic: string) {\n    const symKey = this.keychain.get(topic);\n    return symKey;\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n}\n", "import { generateChildLogger, getLogger<PERSON>ontex<PERSON>, Logger } from \"@walletconnect/logger\";\nimport { I<PERSON><PERSON>, IMessageTracker, MessageRecord } from \"@walletconnect/types\";\nimport { hashMessage, mapToObj, objToMap, getInternalError } from \"@walletconnect/utils\";\nimport { CORE_STORAGE_PREFIX, MESSAGES_CONTEXT, MESSAGES_STORAGE_VERSION } from \"../constants\";\n\nexport class MessageTracker extends IMessageTracker {\n  public messages = new Map<string, MessageRecord>();\n  public name = MESSAGES_CONTEXT;\n  public version = MESSAGES_STORAGE_VERSION;\n\n  private initialized = false;\n  private storagePrefix = CORE_STORAGE_PREFIX;\n\n  constructor(public logger: Logger, public core: ICore) {\n    super(logger, core);\n    this.logger = generateChildLogger(logger, this.name);\n    this.core = core;\n  }\n\n  public init: IMessageTracker[\"init\"] = async () => {\n    if (!this.initialized) {\n      this.logger.trace(`Initialized`);\n      try {\n        const messages = await this.getRelayerMessages();\n        if (typeof messages !== \"undefined\") {\n          this.messages = messages;\n        }\n\n        this.logger.debug(`Successfully Restored records for ${this.name}`);\n        this.logger.trace({ type: \"method\", method: \"restore\", size: this.messages.size });\n      } catch (e) {\n        this.logger.debug(`Failed to Restore records for ${this.name}`);\n        this.logger.error(e as any);\n      } finally {\n        this.initialized = true;\n      }\n    }\n  };\n\n  get context(): string {\n    return getLoggerContext(this.logger);\n  }\n\n  get storageKey() {\n    return this.storagePrefix + this.version + this.core.customStoragePrefix + \"//\" + this.name;\n  }\n\n  public set: IMessageTracker[\"set\"] = async (topic, message) => {\n    this.isInitialized();\n    const hash = hashMessage(message);\n    let messages = this.messages.get(topic);\n    if (typeof messages === \"undefined\") {\n      messages = {};\n    }\n    if (typeof messages[hash] !== \"undefined\") {\n      return hash;\n    }\n    messages[hash] = message;\n    this.messages.set(topic, messages);\n    await this.persist();\n    return hash;\n  };\n\n  public get: IMessageTracker[\"get\"] = (topic) => {\n    this.isInitialized();\n    let messages = this.messages.get(topic);\n    if (typeof messages === \"undefined\") {\n      messages = {};\n    }\n    return messages;\n  };\n\n  public has: IMessageTracker[\"has\"] = (topic, message) => {\n    this.isInitialized();\n    const messages = this.get(topic);\n    const hash = hashMessage(message);\n    return typeof messages[hash] !== \"undefined\";\n  };\n\n  public del: IMessageTracker[\"del\"] = async (topic) => {\n    this.isInitialized();\n    this.messages.delete(topic);\n    await this.persist();\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async setRelayerMessages(messages: Map<string, MessageRecord>): Promise<void> {\n    await this.core.storage.setItem<Record<string, MessageRecord>>(\n      this.storageKey,\n      mapToObj(messages),\n    );\n  }\n\n  private async getRelayerMessages(): Promise<Map<string, MessageRecord> | undefined> {\n    const messages = await this.core.storage.getItem<Record<string, MessageRecord>>(\n      this.storageKey,\n    );\n    return typeof messages !== \"undefined\" ? objToMap(messages) : undefined;\n  }\n\n  private async persist() {\n    await this.setRelayerMessages(this.messages);\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n}\n", "import { HEARTBEAT_EVENTS } from \"@walletconnect/heartbeat\";\nimport { JsonRpcPayload, RequestArguments } from \"@walletconnect/jsonrpc-types\";\nimport { generateChildLogger, getLoggerContext, Logger } from \"@walletconnect/logger\";\nimport { RelayJsonRpc } from \"@walletconnect/relay-api\";\nimport { IPublisher, I<PERSON><PERSON>yer, PublisherTypes, RelayerTypes } from \"@walletconnect/types\";\nimport {\n  getRelayProtocolApi,\n  getRelayProtocolName,\n  isUndefined,\n  createExpiringPromise,\n} from \"@walletconnect/utils\";\nimport { EventEmitter } from \"events\";\n\nimport { PUBLISHER_CONTEXT, PUBLISHER_DEFAULT_TTL, RELAYER_EVENTS } from \"../constants\";\nimport { getBigIntRpcId } from \"@walletconnect/jsonrpc-utils\";\nimport { ONE_MINUTE, ONE_SECOND, toMiliseconds } from \"@walletconnect/time\";\n\ntype IPublishType = PublisherTypes.Params & {\n  attestation?: string;\n  attempt: number;\n};\nexport class Publisher extends IPublisher {\n  public events = new EventEmitter();\n  public name = PUBLISHER_CONTEXT;\n  public queue = new Map<string, IPublishType>();\n\n  private publishTimeout = toMiliseconds(ONE_MINUTE);\n  private initialPublishTimeout = toMiliseconds(ONE_SECOND * 15);\n  private needsTransportRestart = false;\n\n  constructor(public relayer: IRelayer, public logger: Logger) {\n    super(relayer, logger);\n    this.relayer = relayer;\n    this.logger = generateChildLogger(logger, this.name);\n    this.registerEventListeners();\n  }\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  public publish: IPublisher[\"publish\"] = async (topic, message, opts) => {\n    this.logger.debug(`Publishing Payload`);\n    this.logger.trace({ type: \"method\", method: \"publish\", params: { topic, message, opts } });\n\n    const ttl = opts?.ttl || PUBLISHER_DEFAULT_TTL;\n    const relay = getRelayProtocolName(opts);\n    const prompt = opts?.prompt || false;\n    const tag = opts?.tag || 0;\n    const id = opts?.id || (getBigIntRpcId().toString() as any);\n    const params = {\n      topic,\n      message,\n      opts: {\n        ttl,\n        relay,\n        prompt,\n        tag,\n        id,\n        attestation: opts?.attestation,\n        tvf: opts?.tvf,\n      },\n    };\n    const failedPublishMessage = `Failed to publish payload, please try again. id:${id} tag:${tag}`;\n\n    try {\n      /**\n       * attempt to publish the payload for <initialPublishTimeout> seconds,\n       * if the publish fails, add the payload to the queue and it will be retried on every pulse\n       * until it is successfully published or <publishTimeout> seconds have passed\n       */\n      const publishPromise = new Promise(async (resolve) => {\n        const onPublish = ({ id }: { id: string }) => {\n          if (params.opts.id === id) {\n            this.removeRequestFromQueue(id);\n            this.relayer.events.removeListener(RELAYER_EVENTS.publish, onPublish);\n            resolve(params);\n          }\n        };\n        this.relayer.events.on(RELAYER_EVENTS.publish, onPublish);\n        const initialPublish = createExpiringPromise(\n          new Promise((resolve, reject) => {\n            this.rpcPublish({\n              topic,\n              message,\n              ttl,\n              prompt,\n              tag,\n              id,\n              attestation: opts?.attestation,\n              tvf: opts?.tvf,\n            })\n              .then(resolve)\n              .catch((e) => {\n                this.logger.warn(e, e?.message);\n                reject(e);\n              });\n          }),\n          this.initialPublishTimeout,\n          `Failed initial publish, retrying.... id:${id} tag:${tag}`,\n        );\n        try {\n          await initialPublish;\n          this.events.removeListener(RELAYER_EVENTS.publish, onPublish);\n        } catch (e) {\n          this.queue.set(id, { ...params, attempt: 1 });\n          this.logger.warn(e, (e as Error)?.message);\n        }\n      });\n      this.logger.trace({\n        type: \"method\",\n        method: \"publish\",\n        params: { id, topic, message, opts },\n      });\n\n      await createExpiringPromise(publishPromise, this.publishTimeout, failedPublishMessage);\n    } catch (e) {\n      this.logger.debug(`Failed to Publish Payload`);\n      this.logger.error(e as any);\n      if (opts?.internal?.throwOnFailedPublish) {\n        throw e;\n      }\n    } finally {\n      this.queue.delete(id);\n    }\n  };\n\n  public on: IPublisher[\"on\"] = (event, listener) => {\n    this.events.on(event, listener);\n  };\n\n  public once: IPublisher[\"once\"] = (event, listener) => {\n    this.events.once(event, listener);\n  };\n\n  public off: IPublisher[\"off\"] = (event, listener) => {\n    this.events.off(event, listener);\n  };\n\n  public removeListener: IPublisher[\"removeListener\"] = (event, listener) => {\n    this.events.removeListener(event, listener);\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async rpcPublish(params: {\n    topic: string;\n    message: string;\n    ttl?: number;\n    prompt?: boolean;\n    tag?: number;\n    id?: number;\n    attestation?: string;\n    tvf?: RelayerTypes.ITVF;\n  }) {\n    const {\n      topic,\n      message,\n      ttl = PUBLISHER_DEFAULT_TTL,\n      prompt,\n      tag,\n      id,\n      attestation,\n      tvf,\n    } = params;\n    const api = getRelayProtocolApi(getRelayProtocolName().protocol);\n    const request: RequestArguments<RelayJsonRpc.PublishParams> = {\n      method: api.publish,\n      params: {\n        topic,\n        message,\n        ttl,\n        prompt,\n        tag,\n        attestation,\n        ...tvf,\n      },\n      id,\n    };\n    if (isUndefined(request.params?.prompt)) delete request.params?.prompt;\n    if (isUndefined(request.params?.tag)) delete request.params?.tag;\n    this.logger.debug(`Outgoing Relay Payload`);\n    this.logger.trace({ type: \"message\", direction: \"outgoing\", request });\n    const result = await this.relayer.request(request);\n    this.relayer.events.emit(RELAYER_EVENTS.publish, params);\n    this.logger.debug(`Successfully Published Payload`);\n    return result;\n  }\n\n  private removeRequestFromQueue(id: string) {\n    this.queue.delete(id);\n  }\n\n  private checkQueue() {\n    this.queue.forEach(async (params, id) => {\n      const attempt = params.attempt + 1;\n      this.queue.set(id, { ...params, attempt });\n      const { topic, message, opts, attestation } = params;\n      this.logger.warn(\n        {},\n        `Publisher: queue->publishing: ${params.opts.id}, tag: ${params.opts.tag}, attempt: ${attempt}`,\n      );\n      await this.rpcPublish({\n        ...params,\n        topic,\n        message,\n        ttl: opts.ttl,\n        prompt: opts.prompt,\n        tag: opts.tag,\n        id: opts.id,\n        attestation,\n        tvf: opts.tvf,\n      });\n      this.logger.warn({}, `Publisher: queue->published: ${params.opts.id}`);\n    });\n  }\n\n  private registerEventListeners() {\n    this.relayer.core.heartbeat.on(HEARTBEAT_EVENTS.pulse, () => {\n      // restart the transport if needed\n      // queue will be processed on the next pulse\n      if (this.needsTransportRestart) {\n        this.needsTransportRestart = false;\n        this.relayer.events.emit(RELAYER_EVENTS.connection_stalled);\n        return;\n      }\n      this.checkQueue();\n    });\n    this.relayer.on(RELAYER_EVENTS.message_ack, (event: JsonRpcPayload) => {\n      this.removeRequestFromQueue(event.id.toString());\n    });\n  }\n}\n", "import { ISubscriberTopicMap } from \"@walletconnect/types\";\n\nexport class SubscriberTopicMap implements ISubscriberTopicMap {\n  public map = new Map<string, string[]>();\n\n  get topics(): string[] {\n    return Array.from(this.map.keys());\n  }\n\n  public set: ISubscriberTopicMap[\"set\"] = (topic, id) => {\n    const ids = this.get(topic);\n    if (this.exists(topic, id)) return;\n    this.map.set(topic, [...ids, id]);\n  };\n\n  public get: ISubscriberTopicMap[\"get\"] = (topic) => {\n    const ids = this.map.get(topic);\n    return ids || [];\n  };\n\n  public exists: ISubscriberTopicMap[\"exists\"] = (topic, id) => {\n    const ids = this.get(topic);\n    return ids.includes(id);\n  };\n\n  public delete: ISubscriberTopicMap[\"delete\"] = (topic, id) => {\n    if (typeof id === \"undefined\") {\n      this.map.delete(topic);\n      return;\n    }\n    if (!this.map.has(topic)) return;\n    const ids = this.get(topic);\n    if (!this.exists(topic, id)) return;\n    const remaining = ids.filter((x) => x !== id);\n    if (!remaining.length) {\n      this.map.delete(topic);\n      return;\n    }\n    this.map.set(topic, remaining);\n  };\n\n  public clear: ISubscriberTopicMap[\"clear\"] = () => {\n    this.map.clear();\n  };\n}\n", "import { EventEmitter } from \"events\";\nimport { HEARTBEAT_EVENTS } from \"@walletconnect/heartbeat\";\nimport { ErrorResponse, RequestArguments } from \"@walletconnect/jsonrpc-types\";\nimport { generateChildLogger, getLoggerContext, Logger } from \"@walletconnect/logger\";\nimport { RelayJsonRpc } from \"@walletconnect/relay-api\";\nimport { ONE_SECOND, ONE_MINUTE, Watch, toMiliseconds } from \"@walletconnect/time\";\nimport {\n  IRelayer,\n  ISubscriber,\n  RelayerTypes,\n  SubscriberEvents,\n  SubscriberTypes,\n} from \"@walletconnect/types\";\nimport {\n  getSdkError,\n  getInternalError,\n  getRelayProtocolApi,\n  getRelayProtocolName,\n  createExpiringPromise,\n  hashMessage,\n  sleep,\n} from \"@walletconnect/utils\";\nimport {\n  CORE_STORAGE_PREFIX,\n  SUBSCRIBER_CONTEXT,\n  SUBSCRIBER_EVENTS,\n  SUBSCRIBER_STORAGE_VERSION,\n  PENDING_SUB_RESOLUTION_TIMEOUT,\n  RELAYER_EVENTS,\n  TRANSPORT_TYPES,\n} from \"../constants\";\nimport { SubscriberTopicMap } from \"./topicmap\";\n\nexport class Subscriber extends ISubscriber {\n  public subscriptions = new Map<string, SubscriberTypes.Active>();\n  public topicMap = new SubscriberTopicMap();\n  public events = new EventEmitter();\n  public name = SUBSCRIBER_CONTEXT;\n  public version = SUBSCRIBER_STORAGE_VERSION;\n  public pending = new Map<string, SubscriberTypes.Params>();\n\n  private cached: SubscriberTypes.Active[] = [];\n  private initialized = false;\n  private pendingSubscriptionWatchLabel = \"pending_sub_watch_label\";\n  private pollingInterval = 20;\n  private storagePrefix = CORE_STORAGE_PREFIX;\n  private subscribeTimeout = toMiliseconds(ONE_MINUTE);\n  private initialSubscribeTimeout = toMiliseconds(ONE_SECOND * 15);\n  private clientId: string;\n  private batchSubscribeTopicsLimit = 500;\n\n  constructor(public relayer: IRelayer, public logger: Logger) {\n    super(relayer, logger);\n    this.relayer = relayer;\n    this.logger = generateChildLogger(logger, this.name);\n    this.clientId = \"\"; // assigned when calling this.getClientId()\n  }\n\n  public init: ISubscriber[\"init\"] = async () => {\n    if (!this.initialized) {\n      this.logger.trace(`Initialized`);\n      this.registerEventListeners();\n      await this.restore();\n    }\n    this.initialized = true;\n  };\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  get storageKey() {\n    return (\n      this.storagePrefix + this.version + this.relayer.core.customStoragePrefix + \"//\" + this.name\n    );\n  }\n\n  get length() {\n    return this.subscriptions.size;\n  }\n\n  get ids() {\n    return Array.from(this.subscriptions.keys());\n  }\n\n  get values() {\n    return Array.from(this.subscriptions.values());\n  }\n\n  get topics() {\n    return this.topicMap.topics;\n  }\n\n  get hasAnyTopics() {\n    return (\n      this.topicMap.topics.length > 0 ||\n      this.pending.size > 0 ||\n      this.cached.length > 0 ||\n      this.subscriptions.size > 0\n    );\n  }\n\n  public subscribe: ISubscriber[\"subscribe\"] = async (topic, opts) => {\n    this.isInitialized();\n    this.logger.debug(`Subscribing Topic`);\n    this.logger.trace({ type: \"method\", method: \"subscribe\", params: { topic, opts } });\n    try {\n      const relay = getRelayProtocolName(opts);\n      const params = { topic, relay, transportType: opts?.transportType };\n      this.pending.set(topic, params);\n      const id = await this.rpcSubscribe(topic, relay, opts);\n      if (typeof id === \"string\") {\n        this.onSubscribe(id, params);\n        this.logger.debug(`Successfully Subscribed Topic`);\n        this.logger.trace({ type: \"method\", method: \"subscribe\", params: { topic, opts } });\n      }\n      return id;\n    } catch (e) {\n      this.logger.debug(`Failed to Subscribe Topic`);\n      this.logger.error(e as any);\n      throw e;\n    }\n  };\n\n  public unsubscribe: ISubscriber[\"unsubscribe\"] = async (topic, opts) => {\n    this.isInitialized();\n    if (typeof opts?.id !== \"undefined\") {\n      await this.unsubscribeById(topic, opts.id, opts);\n    } else {\n      await this.unsubscribeByTopic(topic, opts);\n    }\n  };\n\n  public isSubscribed: ISubscriber[\"isSubscribed\"] = async (topic: string) => {\n    // topic subscription is already resolved\n    if (this.topics.includes(topic)) return true;\n    const label = `${this.pendingSubscriptionWatchLabel}_${topic}`;\n    // wait for the subscription to resolve\n    const exists = await new Promise<boolean>((resolve, reject) => {\n      const watch = new Watch();\n      watch.start(label);\n      const interval = setInterval(() => {\n        if (\n          (!this.pending.has(topic) && this.topics.includes(topic)) ||\n          this.cached.some((s) => s.topic === topic)\n        ) {\n          clearInterval(interval);\n          watch.stop(label);\n          resolve(true);\n        }\n        if (watch.elapsed(label) >= PENDING_SUB_RESOLUTION_TIMEOUT) {\n          clearInterval(interval);\n          watch.stop(label);\n          reject(new Error(\"Subscription resolution timeout\"));\n        }\n      }, this.pollingInterval);\n    }).catch(() => false);\n    return exists;\n  };\n\n  public on: ISubscriber[\"on\"] = (event, listener) => {\n    this.events.on(event, listener);\n  };\n\n  public once: ISubscriber[\"once\"] = (event, listener) => {\n    this.events.once(event, listener);\n  };\n\n  public off: ISubscriber[\"off\"] = (event, listener) => {\n    this.events.off(event, listener);\n  };\n\n  public removeListener: ISubscriber[\"removeListener\"] = (event, listener) => {\n    this.events.removeListener(event, listener);\n  };\n\n  public start: ISubscriber[\"start\"] = async () => {\n    await this.onConnect();\n  };\n\n  public stop: ISubscriber[\"stop\"] = async () => {\n    await this.onDisconnect();\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private hasSubscription(id: string, topic: string) {\n    let result = false;\n    try {\n      const subscription = this.getSubscription(id);\n      result = subscription.topic === topic;\n    } catch (e) {\n      // ignore error\n    }\n    return result;\n  }\n\n  private reset() {\n    this.cached = [];\n    this.initialized = true;\n  }\n\n  private onDisable() {\n    this.cached = this.values;\n    this.subscriptions.clear();\n    this.topicMap.clear();\n  }\n\n  private async unsubscribeByTopic(topic: string, opts?: RelayerTypes.UnsubscribeOptions) {\n    const ids = this.topicMap.get(topic);\n    await Promise.all(ids.map(async (id) => await this.unsubscribeById(topic, id, opts)));\n  }\n\n  private async unsubscribeById(topic: string, id: string, opts?: RelayerTypes.UnsubscribeOptions) {\n    this.logger.debug(`Unsubscribing Topic`);\n    this.logger.trace({ type: \"method\", method: \"unsubscribe\", params: { topic, id, opts } });\n\n    try {\n      const relay = getRelayProtocolName(opts);\n      await this.restartToComplete({ topic, id, relay });\n      await this.rpcUnsubscribe(topic, id, relay);\n      const reason = getSdkError(\"USER_DISCONNECTED\", `${this.name}, ${topic}`);\n      await this.onUnsubscribe(topic, id, reason);\n      this.logger.debug(`Successfully Unsubscribed Topic`);\n      this.logger.trace({ type: \"method\", method: \"unsubscribe\", params: { topic, id, opts } });\n    } catch (e) {\n      this.logger.debug(`Failed to Unsubscribe Topic`);\n      this.logger.error(e as any);\n      throw e;\n    }\n  }\n\n  private async rpcSubscribe(\n    topic: string,\n    relay: RelayerTypes.ProtocolOptions,\n    opts?: RelayerTypes.SubscribeOptions,\n  ) {\n    if (!opts || opts?.transportType === TRANSPORT_TYPES.relay) {\n      await this.restartToComplete({ topic, id: topic, relay });\n    }\n    const api = getRelayProtocolApi(relay.protocol);\n    const request: RequestArguments<RelayJsonRpc.SubscribeParams> = {\n      method: api.subscribe,\n      params: {\n        topic,\n      },\n    };\n    this.logger.debug(`Outgoing Relay Payload`);\n    this.logger.trace({ type: \"payload\", direction: \"outgoing\", request });\n    const shouldThrow = opts?.internal?.throwOnFailedPublish;\n    try {\n      const subId = await this.getSubscriptionId(topic);\n      // in link mode, allow the app to update its network state (i.e. active airplane mode) with small delay before attempting to subscribe\n      if (opts?.transportType === TRANSPORT_TYPES.link_mode) {\n        setTimeout(() => {\n          if (this.relayer.connected || this.relayer.connecting) {\n            this.relayer.request(request).catch((e) => this.logger.warn(e));\n          }\n        }, toMiliseconds(ONE_SECOND));\n        return subId;\n      }\n      const subscribePromise = new Promise(async (resolve) => {\n        const onSubscribe = (subscription: SubscriberEvents.Created) => {\n          if (subscription.topic === topic) {\n            this.events.removeListener(SUBSCRIBER_EVENTS.created, onSubscribe);\n            resolve(subscription.id);\n          }\n        };\n        this.events.on(SUBSCRIBER_EVENTS.created, onSubscribe);\n        try {\n          const result = await createExpiringPromise(\n            new Promise((resolve, reject) => {\n              this.relayer\n                .request(request)\n                .catch((e) => {\n                  this.logger.warn(e, e?.message);\n                  reject(e);\n                })\n                .then(resolve);\n            }),\n            this.initialSubscribeTimeout,\n            `Subscribing to ${topic} failed, please try again`,\n          );\n          this.events.removeListener(SUBSCRIBER_EVENTS.created, onSubscribe);\n          resolve(result);\n        } catch (err) {}\n      });\n\n      const subscribe = createExpiringPromise(\n        subscribePromise,\n        this.subscribeTimeout,\n        `Subscribing to ${topic} failed, please try again`,\n      );\n\n      const result = await subscribe;\n      if (!result && shouldThrow) {\n        throw new Error(`Subscribing to ${topic} failed, please try again`);\n      }\n      // return null to indicate that the subscription failed\n      return result ? subId : null;\n    } catch (err) {\n      this.logger.debug(`Outgoing Relay Subscribe Payload stalled`);\n      this.relayer.events.emit(RELAYER_EVENTS.connection_stalled);\n      if (shouldThrow) {\n        throw err;\n      }\n    }\n    return null;\n  }\n\n  private async rpcBatchSubscribe(subscriptions: SubscriberTypes.Params[]) {\n    if (!subscriptions.length) return;\n    const relay = subscriptions[0].relay;\n    const api = getRelayProtocolApi(relay!.protocol);\n    const request: RequestArguments<RelayJsonRpc.BatchSubscribeParams> = {\n      method: api.batchSubscribe,\n      params: {\n        topics: subscriptions.map((s) => s.topic),\n      },\n    };\n    this.logger.debug(`Outgoing Relay Payload`);\n    this.logger.trace({ type: \"payload\", direction: \"outgoing\", request });\n    try {\n      const subscribe = await createExpiringPromise(\n        new Promise((resolve) => {\n          this.relayer\n            .request(request)\n            .catch((e) => this.logger.warn(e))\n            .then(resolve);\n        }),\n        this.subscribeTimeout,\n        \"rpcBatchSubscribe failed, please try again\",\n      );\n      await subscribe;\n    } catch (err) {\n      this.relayer.events.emit(RELAYER_EVENTS.connection_stalled);\n    }\n  }\n\n  private async rpcBatchFetchMessages(subscriptions: SubscriberTypes.Params[]) {\n    if (!subscriptions.length) return;\n    const relay = subscriptions[0].relay;\n    const api = getRelayProtocolApi(relay!.protocol);\n    const request: RequestArguments<RelayJsonRpc.BatchFetchMessagesParams> = {\n      method: api.batchFetchMessages,\n      params: {\n        topics: subscriptions.map((s) => s.topic),\n      },\n    };\n    this.logger.debug(`Outgoing Relay Payload`);\n    this.logger.trace({ type: \"payload\", direction: \"outgoing\", request });\n    let result;\n    try {\n      const fetchMessagesPromise = await createExpiringPromise(\n        new Promise((resolve, reject) => {\n          this.relayer\n            .request(request)\n            .catch((e) => {\n              this.logger.warn(e);\n              reject(e);\n            })\n            .then(resolve);\n        }),\n        this.subscribeTimeout,\n        \"rpcBatchFetchMessages failed, please try again\",\n      );\n      result = (await fetchMessagesPromise) as {\n        messages: RelayerTypes.MessageEvent[];\n      };\n    } catch (err) {\n      this.relayer.events.emit(RELAYER_EVENTS.connection_stalled);\n    }\n    return result;\n  }\n\n  private rpcUnsubscribe(topic: string, id: string, relay: RelayerTypes.ProtocolOptions) {\n    const api = getRelayProtocolApi(relay.protocol);\n    const request: RequestArguments<RelayJsonRpc.UnsubscribeParams> = {\n      method: api.unsubscribe,\n      params: {\n        topic,\n        id,\n      },\n    };\n    this.logger.debug(`Outgoing Relay Payload`);\n    this.logger.trace({ type: \"payload\", direction: \"outgoing\", request });\n    return this.relayer.request(request);\n  }\n\n  private onSubscribe(id: string, params: SubscriberTypes.Params) {\n    this.setSubscription(id, { ...params, id });\n    this.pending.delete(params.topic);\n  }\n\n  private onBatchSubscribe(subscriptions: SubscriberTypes.Active[]) {\n    if (!subscriptions.length) return;\n    subscriptions.forEach((subscription) => {\n      this.setSubscription(subscription.id, { ...subscription });\n      this.pending.delete(subscription.topic);\n    });\n  }\n\n  private async onUnsubscribe(topic: string, id: string, reason: ErrorResponse) {\n    this.events.removeAllListeners(id);\n    if (this.hasSubscription(id, topic)) {\n      this.deleteSubscription(id, reason);\n    }\n    await this.relayer.messages.del(topic);\n  }\n\n  private async setRelayerSubscriptions(subscriptions: SubscriberTypes.Active[]) {\n    await this.relayer.core.storage.setItem<SubscriberTypes.Active[]>(\n      this.storageKey,\n      subscriptions,\n    );\n  }\n\n  private async getRelayerSubscriptions() {\n    const subscriptions = await this.relayer.core.storage.getItem<SubscriberTypes.Active[]>(\n      this.storageKey,\n    );\n    return subscriptions;\n  }\n\n  private setSubscription(id: string, subscription: SubscriberTypes.Active) {\n    this.logger.debug(`Setting subscription`);\n    this.logger.trace({ type: \"method\", method: \"setSubscription\", id, subscription });\n    this.addSubscription(id, subscription);\n  }\n\n  private addSubscription(id: string, subscription: SubscriberTypes.Active) {\n    this.subscriptions.set(id, { ...subscription });\n    this.topicMap.set(subscription.topic, id);\n    this.events.emit(SUBSCRIBER_EVENTS.created, subscription);\n  }\n\n  private getSubscription(id: string) {\n    this.logger.debug(`Getting subscription`);\n    this.logger.trace({ type: \"method\", method: \"getSubscription\", id });\n    const subscription = this.subscriptions.get(id);\n    if (!subscription) {\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `${this.name}: ${id}`);\n      throw new Error(message);\n    }\n    return subscription;\n  }\n\n  private deleteSubscription(id: string, reason: ErrorResponse) {\n    this.logger.debug(`Deleting subscription`);\n    this.logger.trace({ type: \"method\", method: \"deleteSubscription\", id, reason });\n    const subscription = this.getSubscription(id);\n    this.subscriptions.delete(id);\n    this.topicMap.delete(subscription.topic, id);\n    this.events.emit(SUBSCRIBER_EVENTS.deleted, {\n      ...subscription,\n      reason,\n    } as SubscriberEvents.Deleted);\n  }\n\n  private restart = async () => {\n    await this.restore();\n    await this.onRestart();\n  };\n\n  private async persist() {\n    await this.setRelayerSubscriptions(this.values);\n    this.events.emit(SUBSCRIBER_EVENTS.sync);\n  }\n\n  private async onRestart() {\n    if (this.cached.length) {\n      const subs = [...this.cached];\n      const numOfBatches = Math.ceil(this.cached.length / this.batchSubscribeTopicsLimit);\n      for (let i = 0; i < numOfBatches; i++) {\n        const batch = subs.splice(0, this.batchSubscribeTopicsLimit);\n        await this.batchSubscribe(batch);\n      }\n    }\n    this.events.emit(SUBSCRIBER_EVENTS.resubscribed);\n  }\n\n  private async restore() {\n    try {\n      const persisted = await this.getRelayerSubscriptions();\n      if (typeof persisted === \"undefined\") return;\n      if (!persisted.length) return;\n      if (this.subscriptions.size) {\n        const { message } = getInternalError(\"RESTORE_WILL_OVERRIDE\", this.name);\n        this.logger.error(message);\n        this.logger.error(`${this.name}: ${JSON.stringify(this.values)}`);\n        throw new Error(message);\n      }\n      this.cached = persisted;\n      this.logger.debug(`Successfully Restored subscriptions for ${this.name}`);\n      this.logger.trace({ type: \"method\", method: \"restore\", subscriptions: this.values });\n    } catch (e) {\n      this.logger.debug(`Failed to Restore subscriptions for ${this.name}`);\n      this.logger.error(e as any);\n    }\n  }\n\n  private async batchSubscribe(subscriptions: SubscriberTypes.Params[]) {\n    if (!subscriptions.length) return;\n\n    await this.rpcBatchSubscribe(subscriptions);\n    this.onBatchSubscribe(\n      await Promise.all(\n        subscriptions.map(async (s) => {\n          return { ...s, id: await this.getSubscriptionId(s.topic) };\n        }),\n      ),\n    );\n  }\n\n  // @ts-ignore\n  private async batchFetchMessages(subscriptions: SubscriberTypes.Params[]) {\n    if (!subscriptions.length) return;\n    this.logger.trace(`Fetching batch messages for ${subscriptions.length} subscriptions`);\n    const response = await this.rpcBatchFetchMessages(subscriptions);\n    if (response && response.messages) {\n      await sleep(toMiliseconds(ONE_SECOND));\n      await this.relayer.handleBatchMessageEvents(response.messages);\n    }\n  }\n\n  private async onConnect() {\n    await this.restart();\n    this.reset();\n  }\n\n  private onDisconnect() {\n    this.onDisable();\n  }\n\n  private checkPending = async () => {\n    if (this.pending.size === 0 && (!this.initialized || !this.relayer.connected)) {\n      return;\n    }\n    const pendingSubscriptions: SubscriberTypes.Params[] = [];\n    this.pending.forEach((params) => {\n      pendingSubscriptions.push(params);\n    });\n\n    await this.batchSubscribe(pendingSubscriptions);\n  };\n\n  private registerEventListeners = () => {\n    this.relayer.core.heartbeat.on(HEARTBEAT_EVENTS.pulse, async () => {\n      await this.checkPending();\n    });\n    this.events.on(SUBSCRIBER_EVENTS.created, async (createdEvent: SubscriberEvents.Created) => {\n      const eventName = SUBSCRIBER_EVENTS.created;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, data: createdEvent });\n      await this.persist();\n    });\n    this.events.on(SUBSCRIBER_EVENTS.deleted, async (deletedEvent: SubscriberEvents.Deleted) => {\n      const eventName = SUBSCRIBER_EVENTS.deleted;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, data: deletedEvent });\n      await this.persist();\n    });\n  };\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n\n  private async restartToComplete(subscription: SubscriberTypes.Active) {\n    if (!this.relayer.connected && !this.relayer.connecting) {\n      this.cached.push(subscription);\n      await this.relayer.transportOpen();\n    }\n  }\n\n  private async getClientId() {\n    if (!this.clientId) {\n      this.clientId = await this.relayer.core.crypto.getClientId();\n    }\n    return this.clientId;\n  }\n\n  private async getSubscriptionId(topic: string) {\n    return hashMessage(topic + (await this.getClientId()));\n  }\n}\n", "import { EventEmitter } from \"events\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport {\n  formatJsonRpcR<PERSON>ult,\n  getBigIntRpcId,\n  IJsonRpcProvider,\n  isJsonRpcRequest,\n  isJsonRpcResponse,\n  JsonRpcPayload,\n  JsonRpcRequest,\n  RequestArguments,\n} from \"@walletconnect/jsonrpc-utils\";\nimport WsConnection from \"@walletconnect/jsonrpc-ws-connection\";\nimport {\n  generateChildLogger,\n  getDefaultLoggerOptions,\n  getLoggerContext,\n  pino,\n  Logger,\n} from \"@walletconnect/logger\";\nimport { RelayJsonRpc } from \"@walletconnect/relay-api\";\nimport {\n  FIVE_MINUTES,\n  ONE_SECOND,\n  FIVE_SECONDS,\n  THIRTY_SECONDS,\n  toMiliseconds,\n} from \"@walletconnect/time\";\nimport {\n  ICore,\n  IMessageTracker,\n  IPublisher,\n  IR<PERSON>yer,\n  ISubscriber,\n  RelayerOptions,\n  RelayerTypes,\n  SubscriberTypes,\n} from \"@walletconnect/types\";\nimport {\n  createExpiringPromise,\n  formatRelayRpcUrl,\n  isOnline,\n  subscribeToNetworkChange,\n  getAppId,\n  isAndroid,\n  isIos,\n  getInternalError,\n  isNode,\n  calcExpiry,\n} from \"@walletconnect/utils\";\n\nimport {\n  RELAYER_SDK_VERSION,\n  RELAYER_CONTEXT,\n  RELAYER_DEFAULT_LOGGER,\n  RELAYER_EVENTS,\n  RELAYER_PROVIDER_EVENTS,\n  RELAYER_SUBSCRIBER_SUFFIX,\n  RELAYER_DEFAULT_RELAY_URL,\n  SUBSCRIBER_EVENTS,\n  RELAYER_RECONNECT_TIMEOUT,\n  TRANSPORT_TYPES,\n} from \"../constants\";\nimport { MessageTracker } from \"./messages\";\nimport { Publisher } from \"./publisher\";\nimport { Subscriber } from \"./subscriber\";\n\nexport class Relayer extends IRelayer {\n  public protocol = \"wc\";\n  public version = 2;\n\n  public core: ICore;\n  public logger: Logger;\n  public events = new EventEmitter();\n  public provider: IJsonRpcProvider;\n  public messages: IMessageTracker;\n  public subscriber: ISubscriber;\n  public publisher: IPublisher;\n  public name = RELAYER_CONTEXT;\n  public transportExplicitlyClosed = false;\n\n  private initialized = false;\n  private connectionAttemptInProgress = false;\n\n  private relayUrl: string;\n  private projectId: string | undefined;\n  private packageName: string | undefined;\n  private bundleId: string | undefined;\n  private hasExperiencedNetworkDisruption = false;\n  private pingTimeout: NodeJS.Timeout | undefined;\n  /**\n   * the relay pings the client 30 seconds after the last message was received\n   * meaning if we don't receive a message in 30 seconds, the connection can be considered dead\n   */\n  private heartBeatTimeout = toMiliseconds(THIRTY_SECONDS + FIVE_SECONDS);\n  private reconnectTimeout: NodeJS.Timeout | undefined;\n  private connectPromise: Promise<void> | undefined;\n  private reconnectInProgress = false;\n  private requestsInFlight: string[] = [];\n  private connectTimeout = toMiliseconds(ONE_SECOND * 15);\n  constructor(opts: RelayerOptions) {\n    super(opts);\n    this.core = opts.core;\n    this.logger =\n      typeof opts.logger !== \"undefined\" && typeof opts.logger !== \"string\"\n        ? generateChildLogger(opts.logger, this.name)\n        : pino(getDefaultLoggerOptions({ level: opts.logger || RELAYER_DEFAULT_LOGGER }));\n    this.messages = new MessageTracker(this.logger, opts.core);\n    this.subscriber = new Subscriber(this, this.logger);\n    this.publisher = new Publisher(this, this.logger);\n\n    this.relayUrl = opts?.relayUrl || RELAYER_DEFAULT_RELAY_URL;\n    this.projectId = opts.projectId;\n\n    if (isAndroid()) {\n      this.packageName = getAppId();\n    } else if (isIos()) {\n      this.bundleId = getAppId();\n    }\n\n    // re-assigned during init()\n    this.provider = {} as IJsonRpcProvider;\n  }\n\n  public async init() {\n    this.logger.trace(`Initialized`);\n    this.registerEventListeners();\n    await Promise.all([this.messages.init(), this.subscriber.init()]);\n    this.initialized = true;\n    if (this.subscriber.hasAnyTopics) {\n      try {\n        await this.transportOpen();\n      } catch (e) {\n        this.logger.warn(e, (e as Error)?.message);\n      }\n    }\n  }\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  get connected() {\n    // @ts-expect-error\n    return this.provider?.connection?.socket?.readyState === 1 || false;\n  }\n\n  get connecting() {\n    return (\n      // @ts-expect-error\n      this.provider?.connection?.socket?.readyState === 0 ||\n      this.connectPromise !== undefined ||\n      false\n    );\n  }\n\n  public async publish(topic: string, message: string, opts?: RelayerTypes.PublishOptions) {\n    this.isInitialized();\n    await this.publisher.publish(topic, message, opts);\n    await this.recordMessageEvent({\n      topic,\n      message,\n      // We don't have `publishedAt` from the relay server on outgoing, so use current time to satisfy type.\n      publishedAt: Date.now(),\n      transportType: TRANSPORT_TYPES.relay,\n    });\n  }\n\n  public async subscribe(topic: string, opts?: RelayerTypes.SubscribeOptions) {\n    this.isInitialized();\n    if (!opts?.transportType || opts?.transportType === \"relay\") {\n      await this.toEstablishConnection();\n    }\n    // throw unless explicitly set to false\n    const shouldThrowOnFailure =\n      typeof opts?.internal?.throwOnFailedPublish === \"undefined\"\n        ? true\n        : opts?.internal?.throwOnFailedPublish;\n\n    let id = this.subscriber.topicMap.get(topic)?.[0] || \"\";\n    let resolvePromise: () => void;\n    const onSubCreated = (subscription: SubscriberTypes.Active) => {\n      if (subscription.topic === topic) {\n        this.subscriber.off(SUBSCRIBER_EVENTS.created, onSubCreated);\n        resolvePromise();\n      }\n    };\n\n    await Promise.all([\n      new Promise<void>((resolve) => {\n        resolvePromise = resolve;\n        this.subscriber.on(SUBSCRIBER_EVENTS.created, onSubCreated);\n      }),\n      new Promise<void>(async (resolve, reject) => {\n        const result = await this.subscriber\n          .subscribe(topic, {\n            internal: {\n              throwOnFailedPublish: shouldThrowOnFailure,\n            },\n            ...opts,\n          })\n          .catch((error) => {\n            if (shouldThrowOnFailure) {\n              reject(error);\n            }\n          });\n        id = result || id;\n        resolve();\n      }),\n    ]);\n    return id;\n  }\n\n  public request = async (request: RequestArguments<RelayJsonRpc.SubscribeParams>) => {\n    this.logger.debug(`Publishing Request Payload`);\n    const id = request.id || (getBigIntRpcId().toString() as any);\n    await this.toEstablishConnection();\n    try {\n      this.logger.trace(\n        {\n          id,\n          method: request.method,\n          topic: request.params?.topic,\n        },\n        \"relayer.request - publishing...\",\n      );\n      const tag = `${id}:${(request.params as any)?.tag || \"\"}`;\n      this.requestsInFlight.push(tag);\n      const result = await this.provider.request(request);\n      this.requestsInFlight = this.requestsInFlight.filter((i) => i !== tag);\n      return result;\n    } catch (e) {\n      this.logger.debug(`Failed to Publish Request: ${id}`);\n      throw e;\n    }\n  };\n\n  public async unsubscribe(topic: string, opts?: RelayerTypes.UnsubscribeOptions) {\n    this.isInitialized();\n    await this.subscriber.unsubscribe(topic, opts);\n  }\n\n  public on(event: string, listener: any) {\n    this.events.on(event, listener);\n  }\n\n  public once(event: string, listener: any) {\n    this.events.once(event, listener);\n  }\n\n  public off(event: string, listener: any) {\n    this.events.off(event, listener);\n  }\n\n  public removeListener(event: string, listener: any) {\n    this.events.removeListener(event, listener);\n  }\n\n  public async transportDisconnect() {\n    if (this.provider.disconnect && (this.hasExperiencedNetworkDisruption || this.connected)) {\n      await createExpiringPromise(this.provider.disconnect(), 2000, \"provider.disconnect()\").catch(\n        () => this.onProviderDisconnect(),\n      );\n    } else {\n      this.onProviderDisconnect();\n    }\n  }\n\n  public async transportClose() {\n    this.transportExplicitlyClosed = true;\n    await this.transportDisconnect();\n  }\n\n  async transportOpen(relayUrl?: string) {\n    if (!this.subscriber.hasAnyTopics) {\n      this.logger.warn(\n        \"Starting WS connection skipped because the client has no topics to work with.\",\n      );\n      return;\n    }\n\n    if (this.connectPromise) {\n      this.logger.debug({}, `Waiting for existing connection attempt to resolve...`);\n      await this.connectPromise;\n      this.logger.debug({}, `Existing connection attempt resolved`);\n    } else {\n      this.connectPromise = new Promise(async (resolve, reject) => {\n        await this.connect(relayUrl)\n          .then(resolve)\n          .catch(reject)\n          .finally(() => {\n            this.connectPromise = undefined;\n          });\n      });\n      await this.connectPromise;\n    }\n    if (!this.connected) {\n      throw new Error(`Couldn't establish socket connection to the relay server: ${this.relayUrl}`);\n    }\n  }\n\n  public async restartTransport(relayUrl?: string) {\n    this.logger.debug({}, \"Restarting transport...\");\n    if (this.connectionAttemptInProgress) return;\n    this.relayUrl = relayUrl || this.relayUrl;\n    await this.confirmOnlineStateOrThrow();\n    await this.transportClose();\n    await this.transportOpen();\n  }\n\n  public async confirmOnlineStateOrThrow() {\n    if (await isOnline()) return;\n    throw new Error(\"No internet connection detected. Please restart your network and try again.\");\n  }\n\n  public async handleBatchMessageEvents(messages: RelayerTypes.MessageEvent[]) {\n    if (messages?.length === 0) {\n      this.logger.trace(\"Batch message events is empty. Ignoring...\");\n      return;\n    }\n    const sortedMessages = messages.sort((a, b) => a.publishedAt - b.publishedAt);\n    this.logger.debug(`Batch of ${sortedMessages.length} message events sorted`);\n    for (const message of sortedMessages) {\n      try {\n        await this.onMessageEvent(message);\n      } catch (e) {\n        this.logger.warn(e, \"Error while processing batch message event: \" + (e as Error)?.message);\n      }\n    }\n    this.logger.trace(`Batch of ${sortedMessages.length} message events processed`);\n  }\n\n  public async onLinkMessageEvent(\n    messageEvent: RelayerTypes.MessageEvent,\n    opts: { sessionExists: boolean },\n  ) {\n    const { topic } = messageEvent;\n\n    if (!opts.sessionExists) {\n      const expiry = calcExpiry(FIVE_MINUTES);\n      const pairing = { topic, expiry, relay: { protocol: \"irn\" }, active: false };\n      await this.core.pairing.pairings.set(topic, pairing);\n    }\n\n    this.events.emit(RELAYER_EVENTS.message, messageEvent);\n    await this.recordMessageEvent(messageEvent);\n  }\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async connect(relayUrl?: string) {\n    await this.confirmOnlineStateOrThrow();\n    if (relayUrl && relayUrl !== this.relayUrl) {\n      this.relayUrl = relayUrl;\n      await this.transportDisconnect();\n    }\n\n    this.connectionAttemptInProgress = true;\n    this.transportExplicitlyClosed = false;\n    let attempt = 1;\n    while (attempt < 6) {\n      try {\n        if (this.transportExplicitlyClosed) {\n          break;\n        }\n        this.logger.debug({}, `Connecting to ${this.relayUrl}, attempt: ${attempt}...`);\n        // Always create new socket instance when trying to connect because if the socket was dropped due to `socket hang up` exception\n        // It wont be able to reconnect\n        await this.createProvider();\n\n        await new Promise<void>(async (resolve, reject) => {\n          const onDisconnect = () => {\n            reject(new Error(`Connection interrupted while trying to subscribe`));\n          };\n          this.provider.once(RELAYER_PROVIDER_EVENTS.disconnect, onDisconnect);\n\n          await createExpiringPromise(\n            new Promise((resolve, reject) => {\n              this.provider.connect().then(resolve).catch(reject);\n            }),\n            this.connectTimeout,\n            `Socket stalled when trying to connect to ${this.relayUrl}`,\n          )\n            .catch((e) => {\n              reject(e);\n            })\n            .finally(() => {\n              this.provider.off(RELAYER_PROVIDER_EVENTS.disconnect, onDisconnect);\n              clearTimeout(this.reconnectTimeout);\n            });\n          await new Promise(async (resolve, reject) => {\n            const onDisconnect = () => {\n              reject(new Error(`Connection interrupted while trying to subscribe`));\n            };\n            this.provider.once(RELAYER_PROVIDER_EVENTS.disconnect, onDisconnect);\n            await this.subscriber\n              .start()\n              .then(resolve)\n              .catch(reject)\n              .finally(() => {\n                this.provider.off(RELAYER_PROVIDER_EVENTS.disconnect, onDisconnect);\n              });\n          });\n          this.hasExperiencedNetworkDisruption = false;\n          resolve();\n        });\n      } catch (e) {\n        await this.subscriber.stop();\n        const error = e as Error;\n        this.logger.warn({}, error.message);\n        this.hasExperiencedNetworkDisruption = true;\n      } finally {\n        this.connectionAttemptInProgress = false;\n      }\n\n      if (this.connected) {\n        this.logger.debug({}, `Connected to ${this.relayUrl} successfully on attempt: ${attempt}`);\n        break;\n      }\n\n      await new Promise((resolve) => setTimeout(resolve, toMiliseconds(attempt * 1)));\n      attempt++;\n    }\n  }\n\n  /*\n   * In Node, we must detect when the connection is stalled and terminate it.\n   * The logic is, if we don't receive ping from the relay within a certain time, we terminate the connection.\n   * The timer is refreshed on every message received from the relay.\n   *\n   * In the browser, ping/pong events are not exposed, so the above behaviour is handled by `subscribeToNetworkChange` and `isOnline` functions.\n   */\n  private startPingTimeout() {\n    if (!isNode()) return;\n    try {\n      //@ts-expect-error - Types are divergent between the node and browser WS API\n      if (this.provider?.connection?.socket) {\n        //@ts-expect-error\n        this.provider?.connection?.socket?.on(\"ping\", () => {\n          this.resetPingTimeout();\n        });\n      }\n      this.resetPingTimeout();\n    } catch (e) {\n      this.logger.warn(e, (e as Error)?.message);\n    }\n  }\n\n  private resetPingTimeout = () => {\n    if (!isNode()) return;\n    try {\n      clearTimeout(this.pingTimeout);\n      this.pingTimeout = setTimeout(() => {\n        this.logger.debug({}, \"pingTimeout: Connection stalled, terminating...\");\n        //@ts-expect-error\n        this.provider?.connection?.socket?.terminate();\n      }, this.heartBeatTimeout);\n    } catch (e) {\n      this.logger.warn(e, (e as Error)?.message);\n    }\n  };\n\n  private async createProvider() {\n    if (this.provider.connection) {\n      this.unregisterProviderListeners();\n    }\n    const auth = await this.core.crypto.signJWT(this.relayUrl);\n\n    this.provider = new JsonRpcProvider(\n      new WsConnection(\n        formatRelayRpcUrl({\n          sdkVersion: RELAYER_SDK_VERSION,\n          protocol: this.protocol,\n          version: this.version,\n          relayUrl: this.relayUrl,\n          projectId: this.projectId,\n          auth,\n          useOnCloseEvent: true,\n          bundleId: this.bundleId,\n          packageName: this.packageName,\n        }),\n      ),\n    );\n    this.registerProviderListeners();\n  }\n\n  private async recordMessageEvent(messageEvent: RelayerTypes.MessageEvent) {\n    const { topic, message } = messageEvent;\n    await this.messages.set(topic, message);\n  }\n\n  private async shouldIgnoreMessageEvent(\n    messageEvent: RelayerTypes.MessageEvent,\n  ): Promise<boolean> {\n    const { topic, message } = messageEvent;\n\n    // Ignore if incoming `message` is clearly invalid.\n    if (!message || message.length === 0) {\n      this.logger.warn(`Ignoring invalid/empty message: ${message}`);\n      return true;\n    }\n\n    // Ignore if `topic` is not subscribed to.\n    if (!(await this.subscriber.isSubscribed(topic))) {\n      this.logger.warn(`Ignoring message for non-subscribed topic ${topic}`);\n      return true;\n    }\n\n    // Ignore if `message` is a duplicate.\n    const exists = this.messages.has(topic, message);\n    if (exists) {\n      this.logger.warn(`Ignoring duplicate message: ${message}`);\n    }\n    return exists;\n  }\n\n  private async onProviderPayload(payload: JsonRpcPayload) {\n    this.logger.debug(`Incoming Relay Payload`);\n    this.logger.trace({ type: \"payload\", direction: \"incoming\", payload });\n    if (isJsonRpcRequest(payload)) {\n      if (!payload.method.endsWith(RELAYER_SUBSCRIBER_SUFFIX)) return;\n      const event = (payload as JsonRpcRequest<RelayJsonRpc.SubscriptionParams>).params;\n      const { topic, message, publishedAt, attestation } = event.data;\n      const messageEvent: RelayerTypes.MessageEvent = {\n        topic,\n        message,\n        publishedAt,\n        transportType: TRANSPORT_TYPES.relay,\n        attestation,\n      };\n      this.logger.debug(`Emitting Relayer Payload`);\n      this.logger.trace({ type: \"event\", event: event.id, ...messageEvent });\n      this.events.emit(event.id, messageEvent);\n      await this.acknowledgePayload(payload);\n      await this.onMessageEvent(messageEvent);\n    } else if (isJsonRpcResponse(payload)) {\n      this.events.emit(RELAYER_EVENTS.message_ack, payload);\n    }\n  }\n\n  private async onMessageEvent(messageEvent: RelayerTypes.MessageEvent) {\n    if (await this.shouldIgnoreMessageEvent(messageEvent)) {\n      return;\n    }\n    this.events.emit(RELAYER_EVENTS.message, messageEvent);\n    await this.recordMessageEvent(messageEvent);\n  }\n\n  private async acknowledgePayload(payload: JsonRpcPayload) {\n    const response = formatJsonRpcResult(payload.id, true);\n    await this.provider.connection.send(response);\n  }\n\n  // ---------- Events Handlers ----------------------------------------------- //\n  private onPayloadHandler = (payload: JsonRpcPayload) => {\n    this.onProviderPayload(payload);\n    this.resetPingTimeout();\n  };\n\n  private onConnectHandler = () => {\n    this.logger.warn({}, \"Relayer connected 🛜\");\n    this.startPingTimeout();\n    this.events.emit(RELAYER_EVENTS.connect);\n  };\n\n  private onDisconnectHandler = () => {\n    this.logger.warn({}, `Relayer disconnected 🛑`);\n    this.requestsInFlight = [];\n    this.onProviderDisconnect();\n  };\n\n  private onProviderErrorHandler = (error: Error) => {\n    this.logger.fatal(`Fatal socket error: ${error.message}`);\n    this.events.emit(RELAYER_EVENTS.error, error);\n    // close the transport when a fatal error is received as there's no way to recover from it\n    // usual cases are missing/invalid projectId, expired jwt token, invalid origin etc\n    this.logger.fatal(\"Fatal socket error received, closing transport\");\n    this.transportClose();\n  };\n\n  private registerProviderListeners = () => {\n    this.provider.on(RELAYER_PROVIDER_EVENTS.payload, this.onPayloadHandler);\n    this.provider.on(RELAYER_PROVIDER_EVENTS.connect, this.onConnectHandler);\n    this.provider.on(RELAYER_PROVIDER_EVENTS.disconnect, this.onDisconnectHandler);\n    this.provider.on(RELAYER_PROVIDER_EVENTS.error, this.onProviderErrorHandler);\n  };\n\n  private unregisterProviderListeners() {\n    this.provider.off(RELAYER_PROVIDER_EVENTS.payload, this.onPayloadHandler);\n    this.provider.off(RELAYER_PROVIDER_EVENTS.connect, this.onConnectHandler);\n    this.provider.off(RELAYER_PROVIDER_EVENTS.disconnect, this.onDisconnectHandler);\n    this.provider.off(RELAYER_PROVIDER_EVENTS.error, this.onProviderErrorHandler);\n    clearTimeout(this.pingTimeout);\n  }\n\n  private async registerEventListeners() {\n    let lastConnectedState = await isOnline();\n    subscribeToNetworkChange(async (connected: boolean) => {\n      // sometimes the network change event is triggered multiple times so avoid reacting to the samFe value\n      if (lastConnectedState === connected) return;\n\n      lastConnectedState = connected;\n      if (!connected) {\n        // when the device network is restarted, the socket might stay in false `connected` state\n        this.hasExperiencedNetworkDisruption = true;\n        await this.transportDisconnect();\n        this.transportExplicitlyClosed = false;\n      } else {\n        await this.transportOpen().catch((error) =>\n          this.logger.error(error, (error as Error)?.message),\n        );\n      }\n    });\n  }\n\n  private async onProviderDisconnect() {\n    clearTimeout(this.pingTimeout);\n    this.events.emit(RELAYER_EVENTS.disconnect);\n    this.connectionAttemptInProgress = false;\n    if (this.reconnectInProgress) return;\n\n    this.reconnectInProgress = true;\n    await this.subscriber.stop();\n\n    if (!this.subscriber.hasAnyTopics) return;\n    if (this.transportExplicitlyClosed) return;\n\n    this.reconnectTimeout = setTimeout(async () => {\n      await this.transportOpen().catch((error) =>\n        this.logger.error(error, (error as Error)?.message),\n      );\n      this.reconnectTimeout = undefined;\n      this.reconnectInProgress = false;\n    }, toMiliseconds(RELAYER_RECONNECT_TIMEOUT));\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n\n  private async toEstablishConnection() {\n    await this.confirmOnlineStateOrThrow();\n    if (this.connected) return;\n    await this.connect();\n  }\n}\n", "import { generateChildLogger, getLogger<PERSON>ontext, Logger } from \"@walletconnect/logger\";\nimport { ICore, IStore } from \"@walletconnect/types\";\nimport {\n  getInternalError,\n  isProposalStruct,\n  isSessionStruct,\n  isUndefined,\n} from \"@walletconnect/utils\";\nimport { CORE_STORAGE_PREFIX, STORE_STORAGE_VERSION } from \"../constants\";\nimport isEqual from \"lodash.isequal\";\n\nexport class Store<Key, Data extends Record<string, any>> extends IStore<Key, Data> {\n  public map = new Map<Key, Data>();\n  public version = STORE_STORAGE_VERSION;\n\n  private cached: Data[] = [];\n  private initialized = false;\n\n  /**\n   * Regenerates the value key to retrieve it from cache\n   */\n  private getKey: ((data: Data) => Key) | undefined;\n\n  private storagePrefix = CORE_STORAGE_PREFIX;\n\n  // stores recently deleted key to return different rejection message when key is not found\n  private recentlyDeleted: Key[] = [];\n  private recentlyDeletedLimit = 200;\n\n  /**\n   * @param {ICore} core Core\n   * @param {Logger} logger Logger\n   * @param {string} name Store's name\n   * @param {Store<Key, Data>[\"getKey\"]} get<PERSON><PERSON> Regenerates the value key to retrieve it from cache\n   * @param {string} storagePrefix Prefixes value keys\n   */\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n    public name: string,\n    storagePrefix: string = CORE_STORAGE_PREFIX,\n    getKey: Store<Key, Data>[\"getKey\"] = undefined,\n  ) {\n    super(core, logger, name, storagePrefix);\n    this.logger = generateChildLogger(logger, this.name);\n    this.storagePrefix = storagePrefix;\n    this.getKey = getKey;\n  }\n\n  public init: IStore<Key, Data>[\"init\"] = async () => {\n    if (!this.initialized) {\n      this.logger.trace(`Initialized`);\n\n      await this.restore();\n\n      this.cached.forEach((value) => {\n        if (this.getKey && value !== null && !isUndefined(value)) {\n          this.map.set(this.getKey(value), value);\n        } else if (isProposalStruct(value)) {\n          // TODO(pedro) revert type casting as any\n          this.map.set(value.id as any, value);\n        } else if (isSessionStruct(value)) {\n          // TODO(pedro) revert type casting as any\n          this.map.set(value.topic as any, value);\n        }\n      });\n\n      this.cached = [];\n      this.initialized = true;\n    }\n  };\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  get storageKey() {\n    return this.storagePrefix + this.version + this.core.customStoragePrefix + \"//\" + this.name;\n  }\n\n  get length() {\n    return this.map.size;\n  }\n\n  get keys() {\n    return Array.from(this.map.keys());\n  }\n\n  get values() {\n    return Array.from(this.map.values());\n  }\n\n  public set: IStore<Key, Data>[\"set\"] = async (key, value) => {\n    this.isInitialized();\n    if (this.map.has(key)) {\n      await this.update(key, value);\n    } else {\n      this.logger.debug(`Setting value`);\n      this.logger.trace({ type: \"method\", method: \"set\", key, value });\n      this.map.set(key, value);\n      await this.persist();\n    }\n  };\n\n  public get: IStore<Key, Data>[\"get\"] = (key) => {\n    this.isInitialized();\n    this.logger.debug(`Getting value`);\n    this.logger.trace({ type: \"method\", method: \"get\", key });\n    const value = this.getData(key);\n    return value;\n  };\n\n  public getAll: IStore<Key, Data>[\"getAll\"] = (filter) => {\n    this.isInitialized();\n    if (!filter) return this.values;\n\n    return this.values.filter((value) =>\n      Object.keys(filter).every((key) => isEqual(value[key], filter[key])),\n    );\n  };\n\n  public update: IStore<Key, Data>[\"update\"] = async (key, update) => {\n    this.isInitialized();\n    this.logger.debug(`Updating value`);\n    this.logger.trace({ type: \"method\", method: \"update\", key, update });\n    const value = { ...this.getData(key), ...update };\n    this.map.set(key, value);\n    await this.persist();\n  };\n\n  public delete: IStore<Key, Data>[\"delete\"] = async (key, reason) => {\n    this.isInitialized();\n    if (!this.map.has(key)) return;\n    this.logger.debug(`Deleting value`);\n    this.logger.trace({ type: \"method\", method: \"delete\", key, reason });\n    this.map.delete(key);\n    this.addToRecentlyDeleted(key);\n    await this.persist();\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private addToRecentlyDeleted(key: Key) {\n    this.recentlyDeleted.push(key);\n    // limit the size of the recentlyDeleted array, truncate the 100 oldest entries.\n    if (this.recentlyDeleted.length >= this.recentlyDeletedLimit) {\n      this.recentlyDeleted.splice(0, this.recentlyDeletedLimit / 2);\n    }\n  }\n\n  private async setDataStore(value: Data[]) {\n    await this.core.storage.setItem<Data[]>(this.storageKey, value);\n  }\n\n  private async getDataStore() {\n    const value = await this.core.storage.getItem<Data[]>(this.storageKey);\n    return value;\n  }\n\n  private getData(key: Key) {\n    const value = this.map.get(key);\n    if (!value) {\n      if (this.recentlyDeleted.includes(key)) {\n        const { message } = getInternalError(\n          \"MISSING_OR_INVALID\",\n          `Record was recently deleted - ${this.name}: ${key}`,\n        );\n        this.logger.error(message);\n        throw new Error(message);\n      }\n\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `${this.name}: ${key}`);\n      this.logger.error(message);\n      throw new Error(message);\n    }\n    return value;\n  }\n\n  private async persist() {\n    await this.setDataStore(this.values);\n  }\n\n  private async restore() {\n    try {\n      const persisted = await this.getDataStore();\n      if (typeof persisted === \"undefined\") return;\n      if (!persisted.length) return;\n      if (this.map.size) {\n        const { message } = getInternalError(\"RESTORE_WILL_OVERRIDE\", this.name);\n        this.logger.error(message);\n        throw new Error(message);\n      }\n      this.cached = persisted;\n      this.logger.debug(`Successfully Restored value for ${this.name}`);\n      this.logger.trace({ type: \"method\", method: \"restore\", value: this.values });\n    } catch (e) {\n      this.logger.debug(`Failed to Restore value for ${this.name}`);\n      this.logger.error(e as any);\n    }\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n}\n", "import { generateChil<PERSON><PERSON>ogger, getLog<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from \"@walletconnect/logger\";\nimport {\n  ICore,\n  PairingTypes,\n  IPairing,\n  IPairingPrivate,\n  IStore,\n  RelayerTypes,\n  PairingJsonRpcTypes,\n  ExpirerTypes,\n  EventClientTypes,\n} from \"@walletconnect/types\";\nimport {\n  getInternalError,\n  parseUri,\n  calcExpiry,\n  generateRandomBytes32,\n  formatUri,\n  getSdkError,\n  engineEvent,\n  createDelayedPromise,\n  isValidParams,\n  isValidUrl,\n  isValidString,\n  isExpired,\n  parseExpirerTarget,\n  TYPE_1,\n} from \"@walletconnect/utils\";\nimport {\n  formatJsonRpcRequest,\n  formatJsonRpcResult,\n  formatJsonRpcError,\n  isJsonRpcRequest,\n  isJsonRpcResponse,\n  isJsonRpcResult,\n  isJsonRpcError,\n} from \"@walletconnect/jsonrpc-utils\";\nimport { FIVE_MINUTES, toMiliseconds } from \"@walletconnect/time\";\nimport EventEmitter from \"events\";\nimport {\n  PAIRING_CONTEXT,\n  PAIRING_STORAGE_VERSION,\n  CORE_STORAGE_PREFIX,\n  RELAYER_DEFAULT_PROTOCOL,\n  PAIRING_RPC_OPTS,\n  RELAYER_EVENTS,\n  EXPIRER_EVENTS,\n  PAIRING_EVENTS,\n  EVENT_CLIENT_PAIRING_TRACES,\n  EVENT_CLIENT_PAIRING_ERRORS,\n  TRANSPORT_TYPES,\n} from \"../constants\";\nimport { Store } from \"../controllers/store\";\n\nexport class Pairing implements IPairing {\n  public name = PAIRING_CONTEXT;\n  public version = PAIRING_STORAGE_VERSION;\n\n  public events = new EventEmitter();\n  public pairings: IStore<string, PairingTypes.Struct>;\n\n  private initialized = false;\n  private storagePrefix = CORE_STORAGE_PREFIX;\n  private ignoredPayloadTypes = [TYPE_1];\n  private registeredMethods: string[] = [];\n\n  constructor(public core: ICore, public logger: Logger) {\n    this.core = core;\n    this.logger = generateChildLogger(logger, this.name);\n    this.pairings = new Store(this.core, this.logger, this.name, this.storagePrefix);\n  }\n\n  public init: IPairing[\"init\"] = async () => {\n    if (!this.initialized) {\n      await this.pairings.init();\n      await this.cleanup();\n      this.registerRelayerEvents();\n      this.registerExpirerEvents();\n      this.initialized = true;\n      this.logger.trace(`Initialized`);\n    }\n  };\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  public register: IPairing[\"register\"] = ({ methods }) => {\n    this.isInitialized();\n    this.registeredMethods = [...new Set([...this.registeredMethods, ...methods])];\n  };\n\n  public create: IPairing[\"create\"] = async (params) => {\n    this.isInitialized();\n    const symKey = generateRandomBytes32();\n    const topic = await this.core.crypto.setSymKey(symKey);\n    const expiry = calcExpiry(FIVE_MINUTES);\n    const relay = { protocol: RELAYER_DEFAULT_PROTOCOL };\n    const pairing = { topic, expiry, relay, active: false, methods: params?.methods };\n    const uri = formatUri({\n      protocol: this.core.protocol,\n      version: this.core.version,\n      topic,\n      symKey,\n      relay,\n      expiryTimestamp: expiry,\n      methods: params?.methods,\n    });\n    this.events.emit(PAIRING_EVENTS.create, pairing);\n    this.core.expirer.set(topic, expiry);\n    await this.pairings.set(topic, pairing);\n    await this.core.relayer.subscribe(topic, { transportType: params?.transportType });\n\n    return { topic, uri };\n  };\n\n  public pair: IPairing[\"pair\"] = async (params) => {\n    this.isInitialized();\n\n    const event = this.core.eventClient.createEvent({\n      properties: {\n        topic: params?.uri,\n        trace: [EVENT_CLIENT_PAIRING_TRACES.pairing_started],\n      },\n    });\n\n    this.isValidPair(params, event);\n\n    const { topic, symKey, relay, expiryTimestamp, methods } = parseUri(params.uri);\n\n    event.props.properties.topic = topic;\n    event.addTrace(EVENT_CLIENT_PAIRING_TRACES.pairing_uri_validation_success);\n    event.addTrace(EVENT_CLIENT_PAIRING_TRACES.pairing_uri_not_expired);\n\n    let existingPairing;\n    if (this.pairings.keys.includes(topic)) {\n      existingPairing = this.pairings.get(topic);\n      event.addTrace(EVENT_CLIENT_PAIRING_TRACES.existing_pairing);\n      if (existingPairing.active) {\n        event.setError(EVENT_CLIENT_PAIRING_ERRORS.active_pairing_already_exists);\n        throw new Error(\n          `Pairing already exists: ${topic}. Please try again with a new connection URI.`,\n        );\n      } else {\n        event.addTrace(EVENT_CLIENT_PAIRING_TRACES.pairing_not_expired);\n      }\n    }\n\n    const expiry = expiryTimestamp || calcExpiry(FIVE_MINUTES);\n    const pairing = { topic, relay, expiry, active: false, methods };\n    this.core.expirer.set(topic, expiry);\n    await this.pairings.set(topic, pairing);\n\n    event.addTrace(EVENT_CLIENT_PAIRING_TRACES.store_new_pairing);\n\n    if (params.activatePairing) {\n      await this.activate({ topic });\n    }\n\n    this.events.emit(PAIRING_EVENTS.create, pairing);\n\n    event.addTrace(EVENT_CLIENT_PAIRING_TRACES.emit_inactive_pairing);\n\n    // avoid overwriting keychain pairing already exists\n    if (!this.core.crypto.keychain.has(topic)) {\n      await this.core.crypto.setSymKey(symKey, topic);\n    }\n    event.addTrace(EVENT_CLIENT_PAIRING_TRACES.subscribing_pairing_topic);\n\n    try {\n      await this.core.relayer.confirmOnlineStateOrThrow();\n    } catch (error) {\n      event.setError(EVENT_CLIENT_PAIRING_ERRORS.no_internet_connection);\n    }\n\n    try {\n      await this.core.relayer.subscribe(topic, { relay });\n    } catch (error) {\n      event.setError(EVENT_CLIENT_PAIRING_ERRORS.subscribe_pairing_topic_failure);\n      throw error;\n    }\n\n    event.addTrace(EVENT_CLIENT_PAIRING_TRACES.subscribe_pairing_topic_success);\n\n    return pairing;\n  };\n\n  public activate: IPairing[\"activate\"] = async ({ topic }) => {\n    this.isInitialized();\n    const expiry = calcExpiry(FIVE_MINUTES);\n    this.core.expirer.set(topic, expiry);\n    await this.pairings.update(topic, { active: true, expiry });\n  };\n\n  /**\n   * @deprecated Ping will be removed in the next major release.\n   */\n  public ping: IPairing[\"ping\"] = async (params) => {\n    this.isInitialized();\n    await this.isValidPing(params);\n    this.logger.warn(\"ping() is deprecated and will be removed in the next major release.\");\n    const { topic } = params;\n    if (this.pairings.keys.includes(topic)) {\n      const id = await this.sendRequest(topic, \"wc_pairingPing\", {});\n      const { done, resolve, reject } = createDelayedPromise<void>();\n      this.events.once(engineEvent(\"pairing_ping\", id), ({ error }) => {\n        if (error) reject(error);\n        else resolve();\n      });\n      await done();\n    }\n  };\n\n  public updateExpiry: IPairing[\"updateExpiry\"] = async ({ topic, expiry }) => {\n    this.isInitialized();\n    await this.pairings.update(topic, { expiry });\n  };\n\n  public updateMetadata: IPairing[\"updateMetadata\"] = async ({ topic, metadata }) => {\n    this.isInitialized();\n    await this.pairings.update(topic, { peerMetadata: metadata });\n  };\n\n  public getPairings: IPairing[\"getPairings\"] = () => {\n    this.isInitialized();\n    return this.pairings.values;\n  };\n\n  public disconnect: IPairing[\"disconnect\"] = async (params) => {\n    this.isInitialized();\n    await this.isValidDisconnect(params);\n    const { topic } = params;\n    if (this.pairings.keys.includes(topic)) {\n      await this.sendRequest(topic, \"wc_pairingDelete\", getSdkError(\"USER_DISCONNECTED\"));\n      await this.deletePairing(topic);\n    }\n  };\n\n  public formatUriFromPairing: IPairing[\"formatUriFromPairing\"] = (pairing) => {\n    this.isInitialized();\n    const { topic, relay, expiry, methods } = pairing;\n    const symKey = this.core.crypto.keychain.get(topic);\n    return formatUri({\n      protocol: this.core.protocol,\n      version: this.core.version,\n      topic,\n      symKey,\n      relay,\n      expiryTimestamp: expiry,\n      methods,\n    });\n  };\n\n  // ---------- Private Helpers ----------------------------------------------- //\n\n  private sendRequest: IPairingPrivate[\"sendRequest\"] = async (topic, method, params) => {\n    const payload = formatJsonRpcRequest(method, params);\n    const message = await this.core.crypto.encode(topic, payload);\n    const opts = PAIRING_RPC_OPTS[method].req;\n    this.core.history.set(topic, payload);\n    this.core.relayer.publish(topic, message, opts);\n    return payload.id;\n  };\n\n  private sendResult: IPairingPrivate[\"sendResult\"] = async (id, topic, result) => {\n    const payload = formatJsonRpcResult(id, result);\n    const message = await this.core.crypto.encode(topic, payload);\n    const record = await this.core.history.get(topic, id);\n    const method = record.request.method as PairingJsonRpcTypes.WcMethod;\n    const opts = PAIRING_RPC_OPTS[method].res;\n    await this.core.relayer.publish(topic, message, opts);\n    await this.core.history.resolve(payload);\n  };\n\n  private sendError: IPairingPrivate[\"sendError\"] = async (id, topic, error) => {\n    const payload = formatJsonRpcError(id, error);\n    const message = await this.core.crypto.encode(topic, payload);\n    const record = await this.core.history.get(topic, id);\n    const method = record.request.method as PairingJsonRpcTypes.WcMethod;\n\n    const opts = PAIRING_RPC_OPTS[method]\n      ? PAIRING_RPC_OPTS[method].res\n      : PAIRING_RPC_OPTS.unregistered_method.res;\n\n    await this.core.relayer.publish(topic, message, opts);\n    await this.core.history.resolve(payload);\n  };\n\n  private deletePairing: IPairingPrivate[\"deletePairing\"] = async (topic, expirerHasDeleted) => {\n    // Await the unsubscribe first to avoid deleting the symKey too early below.\n    await this.core.relayer.unsubscribe(topic);\n    await Promise.all([\n      this.pairings.delete(topic, getSdkError(\"USER_DISCONNECTED\")),\n      this.core.crypto.deleteSymKey(topic),\n      expirerHasDeleted ? Promise.resolve() : this.core.expirer.del(topic),\n    ]);\n  };\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n\n  private cleanup = async () => {\n    const expiredPairings = this.pairings.getAll().filter((pairing) => isExpired(pairing.expiry));\n    await Promise.all(expiredPairings.map((pairing) => this.deletePairing(pairing.topic)));\n  };\n\n  // ---------- Relay Events Router ----------------------------------- //\n\n  private registerRelayerEvents() {\n    this.core.relayer.on(RELAYER_EVENTS.message, async (event: RelayerTypes.MessageEvent) => {\n      const { topic, message, transportType } = event;\n\n      // Do not handle if the topic is not related to known pairing topics.\n      if (!this.pairings.keys.includes(topic)) return;\n\n      // Do not handle link-mode messages\n      if (transportType === TRANSPORT_TYPES.link_mode) return;\n\n      // messages of certain types should be ignored as they are handled by their respective SDKs\n      if (this.ignoredPayloadTypes.includes(this.core.crypto.getPayloadType(message))) return;\n\n      const payload = await this.core.crypto.decode(topic, message);\n\n      try {\n        if (isJsonRpcRequest(payload)) {\n          this.core.history.set(topic, payload);\n          this.onRelayEventRequest({ topic, payload });\n        } else if (isJsonRpcResponse(payload)) {\n          await this.core.history.resolve(payload);\n          await this.onRelayEventResponse({ topic, payload });\n          this.core.history.delete(topic, payload.id);\n        }\n      } catch (error) {\n        this.logger.error(error);\n      }\n    });\n  }\n\n  private onRelayEventRequest: IPairingPrivate[\"onRelayEventRequest\"] = (event) => {\n    const { topic, payload } = event;\n    const reqMethod = payload.method as PairingJsonRpcTypes.WcMethod;\n\n    switch (reqMethod) {\n      case \"wc_pairingPing\":\n        return this.onPairingPingRequest(topic, payload);\n      case \"wc_pairingDelete\":\n        return this.onPairingDeleteRequest(topic, payload);\n      default:\n        return this.onUnknownRpcMethodRequest(topic, payload);\n    }\n  };\n\n  private onRelayEventResponse: IPairingPrivate[\"onRelayEventResponse\"] = async (event) => {\n    const { topic, payload } = event;\n    const record = await this.core.history.get(topic, payload.id);\n    const resMethod = record.request.method as PairingJsonRpcTypes.WcMethod;\n\n    switch (resMethod) {\n      case \"wc_pairingPing\":\n        return this.onPairingPingResponse(topic, payload);\n      default:\n        return this.onUnknownRpcMethodResponse(resMethod);\n    }\n  };\n\n  private onPairingPingRequest: IPairingPrivate[\"onPairingPingRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    try {\n      this.isValidPing({ topic });\n      await this.sendResult<\"wc_pairingPing\">(id, topic, true);\n      this.events.emit(PAIRING_EVENTS.ping, { id, topic });\n    } catch (err: any) {\n      await this.sendError(id, topic, err);\n      this.logger.error(err);\n    }\n  };\n\n  private onPairingPingResponse: IPairingPrivate[\"onPairingPingResponse\"] = (_topic, payload) => {\n    const { id } = payload;\n    // put at the end of the stack to avoid a race condition\n    // where pairing_ping listener is not yet initialized\n    setTimeout(() => {\n      if (isJsonRpcResult(payload)) {\n        this.events.emit(engineEvent(\"pairing_ping\", id), {});\n      } else if (isJsonRpcError(payload)) {\n        this.events.emit(engineEvent(\"pairing_ping\", id), { error: payload.error });\n      }\n    }, 500);\n  };\n\n  private onPairingDeleteRequest: IPairingPrivate[\"onPairingDeleteRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    try {\n      this.isValidDisconnect({ topic });\n      await this.deletePairing(topic);\n      this.events.emit(PAIRING_EVENTS.delete, { id, topic });\n    } catch (err: any) {\n      await this.sendError(id, topic, err);\n      this.logger.error(err);\n    }\n  };\n\n  private onUnknownRpcMethodRequest: IPairingPrivate[\"onUnknownRpcMethodRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id, method } = payload;\n\n    try {\n      // Ignore if the implementing client has registered this method as known.\n      if (this.registeredMethods.includes(method)) return;\n      const error = getSdkError(\"WC_METHOD_UNSUPPORTED\", method);\n      await this.sendError(id, topic, error);\n      this.logger.error(error);\n    } catch (err: any) {\n      await this.sendError(id, topic, err);\n      this.logger.error(err);\n    }\n  };\n\n  private onUnknownRpcMethodResponse: IPairingPrivate[\"onUnknownRpcMethodResponse\"] = (method) => {\n    // Ignore if the implementing client has registered this method as known.\n    if (this.registeredMethods.includes(method)) return;\n    this.logger.error(getSdkError(\"WC_METHOD_UNSUPPORTED\", method));\n  };\n\n  // ---------- Expirer Events ---------------------------------------- //\n\n  private registerExpirerEvents() {\n    this.core.expirer.on(EXPIRER_EVENTS.expired, async (event: ExpirerTypes.Expiration) => {\n      const { topic } = parseExpirerTarget(event.target);\n      if (!topic) return;\n      if (!this.pairings.keys.includes(topic)) return;\n      await this.deletePairing(topic, true);\n      this.events.emit(PAIRING_EVENTS.expire, { topic });\n    });\n  }\n\n  // ---------- Validation Helpers ----------------------------------- //\n\n  private isValidPair = (params: { uri: string }, event: EventClientTypes.Event) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `pair() params: ${params}`);\n      event.setError(EVENT_CLIENT_PAIRING_ERRORS.malformed_pairing_uri);\n      throw new Error(message);\n    }\n    if (!isValidUrl(params.uri)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `pair() uri: ${params.uri}`);\n      event.setError(EVENT_CLIENT_PAIRING_ERRORS.malformed_pairing_uri);\n      throw new Error(message);\n    }\n    const uri = parseUri(params?.uri);\n    if (!uri?.relay?.protocol) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `pair() uri#relay-protocol`);\n      event.setError(EVENT_CLIENT_PAIRING_ERRORS.malformed_pairing_uri);\n      throw new Error(message);\n    }\n    if (!uri?.symKey) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `pair() uri#symKey`);\n      event.setError(EVENT_CLIENT_PAIRING_ERRORS.malformed_pairing_uri);\n      throw new Error(message);\n    }\n    if (uri?.expiryTimestamp) {\n      const expiration = toMiliseconds(uri?.expiryTimestamp);\n      if (expiration < Date.now()) {\n        event.setError(EVENT_CLIENT_PAIRING_ERRORS.pairing_expired);\n        const { message } = getInternalError(\n          \"EXPIRED\",\n          `pair() URI has expired. Please try again with a new connection URI.`,\n        );\n        throw new Error(message);\n      }\n    }\n  };\n\n  private isValidPing = async (params: { topic: string }) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `ping() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic } = params;\n    await this.isValidPairingTopic(topic);\n  };\n\n  private isValidDisconnect = async (params: { topic: string }) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `disconnect() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic } = params;\n    await this.isValidPairingTopic(topic);\n  };\n\n  private isValidPairingTopic = async (topic: any) => {\n    if (!isValidString(topic, false)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `pairing topic should be a string: ${topic}`,\n      );\n      throw new Error(message);\n    }\n    if (!this.pairings.keys.includes(topic)) {\n      const { message } = getInternalError(\n        \"NO_MATCHING_KEY\",\n        `pairing topic doesn't exist: ${topic}`,\n      );\n      throw new Error(message);\n    }\n    if (isExpired(this.pairings.get(topic).expiry)) {\n      await this.deletePairing(topic);\n      const { message } = getInternalError(\"EXPIRED\", `pairing topic: ${topic}`);\n      throw new Error(message);\n    }\n  };\n}\n", "import { formatJsonRpcRequest, isJsonRpcError } from \"@walletconnect/jsonrpc-utils\";\nimport { generateChildLogger, getLoggerContext, Logger } from \"@walletconnect/logger\";\nimport { IJsonRpcHistory, JsonRpcRecord, RequestEvent, ICore } from \"@walletconnect/types\";\nimport { calcExpiry, getInternalError } from \"@walletconnect/utils\";\nimport { EventEmitter } from \"events\";\nimport { THIRTY_DAYS, toMiliseconds } from \"@walletconnect/time\";\nimport { HEARTBEAT_EVENTS } from \"@walletconnect/heartbeat\";\nimport {\n  CORE_STORAGE_PREFIX,\n  HISTORY_CONTEXT,\n  HISTORY_EVENTS,\n  HISTORY_STORAGE_VERSION,\n} from \"../constants\";\n\nexport class JsonRpcHistory extends IJsonRpcHistory {\n  public records = new Map<number, JsonRpcRecord>();\n  public events = new EventEmitter();\n  public name = HISTORY_CONTEXT;\n  public version = HISTORY_STORAGE_VERSION;\n\n  private cached: JsonRpcRecord[] = [];\n  private initialized = false;\n  private storagePrefix = CORE_STORAGE_PREFIX;\n\n  constructor(public core: ICore, public logger: Logger) {\n    super(core, logger);\n    this.logger = generateChildLogger(logger, this.name);\n  }\n\n  public init: IJsonRpcHistory[\"init\"] = async () => {\n    if (!this.initialized) {\n      this.logger.trace(`Initialized`);\n      await this.restore();\n      this.cached.forEach((record) => this.records.set(record.id, record));\n      this.cached = [];\n      this.registerEventListeners();\n      this.initialized = true;\n    }\n  };\n\n  get context(): string {\n    return getLoggerContext(this.logger);\n  }\n\n  get storageKey() {\n    return this.storagePrefix + this.version + this.core.customStoragePrefix + \"//\" + this.name;\n  }\n\n  get size(): number {\n    return this.records.size;\n  }\n\n  get keys(): number[] {\n    return Array.from(this.records.keys());\n  }\n\n  get values() {\n    return Array.from(this.records.values());\n  }\n\n  get pending(): RequestEvent[] {\n    const requests: RequestEvent[] = [];\n    this.values.forEach((record) => {\n      if (typeof record.response !== \"undefined\") return;\n      const requestEvent: RequestEvent = {\n        topic: record.topic,\n        request: formatJsonRpcRequest(record.request.method, record.request.params, record.id),\n        chainId: record.chainId,\n      };\n      return requests.push(requestEvent);\n    });\n    return requests;\n  }\n\n  public set: IJsonRpcHistory[\"set\"] = (topic, request, chainId) => {\n    this.isInitialized();\n    this.logger.debug(`Setting JSON-RPC request history record`);\n    this.logger.trace({ type: \"method\", method: \"set\", topic, request, chainId });\n    if (this.records.has(request.id)) return;\n    const record: JsonRpcRecord = {\n      id: request.id,\n      topic,\n      request: { method: request.method, params: request.params || null },\n      chainId,\n      expiry: calcExpiry(THIRTY_DAYS),\n    };\n    this.records.set(record.id, record);\n    this.persist();\n    this.events.emit(HISTORY_EVENTS.created, record);\n  };\n\n  public resolve: IJsonRpcHistory[\"resolve\"] = async (response) => {\n    this.isInitialized();\n    this.logger.debug(`Updating JSON-RPC response history record`);\n    this.logger.trace({ type: \"method\", method: \"update\", response });\n    if (!this.records.has(response.id)) return;\n    const record = await this.getRecord(response.id);\n    if (typeof record.response !== \"undefined\") return;\n    record.response = isJsonRpcError(response)\n      ? { error: response.error }\n      : { result: response.result };\n    this.records.set(record.id, record);\n    this.persist();\n    this.events.emit(HISTORY_EVENTS.updated, record);\n  };\n\n  public get: IJsonRpcHistory[\"get\"] = async (topic, id) => {\n    this.isInitialized();\n    this.logger.debug(`Getting record`);\n    this.logger.trace({ type: \"method\", method: \"get\", topic, id });\n    const record = await this.getRecord(id);\n    return record;\n  };\n\n  public delete: IJsonRpcHistory[\"delete\"] = (topic, id) => {\n    this.isInitialized();\n    this.logger.debug(`Deleting record`);\n    this.logger.trace({ type: \"method\", method: \"delete\", id });\n    this.values.forEach((record: JsonRpcRecord) => {\n      if (record.topic === topic) {\n        if (typeof id !== \"undefined\" && record.id !== id) return;\n        this.records.delete(record.id);\n        this.events.emit(HISTORY_EVENTS.deleted, record);\n      }\n    });\n    this.persist();\n  };\n\n  public exists: IJsonRpcHistory[\"exists\"] = async (topic, id) => {\n    this.isInitialized();\n    if (!this.records.has(id)) return false;\n    const record = await this.getRecord(id);\n    return record.topic === topic;\n  };\n\n  public on: IJsonRpcHistory[\"on\"] = (event, listener) => {\n    this.events.on(event, listener);\n  };\n\n  public once: IJsonRpcHistory[\"once\"] = (event, listener) => {\n    this.events.once(event, listener);\n  };\n\n  public off: IJsonRpcHistory[\"off\"] = (event, listener) => {\n    this.events.off(event, listener);\n  };\n\n  public removeListener: IJsonRpcHistory[\"removeListener\"] = (event, listener) => {\n    this.events.removeListener(event, listener);\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async setJsonRpcRecords(records: JsonRpcRecord[]): Promise<void> {\n    await this.core.storage.setItem<JsonRpcRecord[]>(this.storageKey, records);\n  }\n\n  private async getJsonRpcRecords(): Promise<JsonRpcRecord[] | undefined> {\n    const records = await this.core.storage.getItem<JsonRpcRecord[]>(this.storageKey);\n    return records;\n  }\n\n  private getRecord(id: number) {\n    this.isInitialized();\n    const record = this.records.get(id);\n    if (!record) {\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `${this.name}: ${id}`);\n      throw new Error(message);\n    }\n    return record;\n  }\n\n  private async persist() {\n    await this.setJsonRpcRecords(this.values);\n    this.events.emit(HISTORY_EVENTS.sync);\n  }\n\n  private async restore() {\n    try {\n      const persisted = await this.getJsonRpcRecords();\n      if (typeof persisted === \"undefined\") return;\n      if (!persisted.length) return;\n      if (this.records.size) {\n        const { message } = getInternalError(\"RESTORE_WILL_OVERRIDE\", this.name);\n        this.logger.error(message);\n        throw new Error(message);\n      }\n      this.cached = persisted;\n      this.logger.debug(`Successfully Restored records for ${this.name}`);\n      this.logger.trace({ type: \"method\", method: \"restore\", records: this.values });\n    } catch (e) {\n      this.logger.debug(`Failed to Restore records for ${this.name}`);\n      this.logger.error(e as any);\n    }\n  }\n\n  private registerEventListeners(): void {\n    this.events.on(HISTORY_EVENTS.created, (record: JsonRpcRecord) => {\n      const eventName = HISTORY_EVENTS.created;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, record });\n    });\n    this.events.on(HISTORY_EVENTS.updated, (record: JsonRpcRecord) => {\n      const eventName = HISTORY_EVENTS.updated;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, record });\n    });\n\n    this.events.on(HISTORY_EVENTS.deleted, (record: JsonRpcRecord) => {\n      const eventName = HISTORY_EVENTS.deleted;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, record });\n    });\n\n    this.core.heartbeat.on(HEARTBEAT_EVENTS.pulse, () => {\n      this.cleanup();\n    });\n  }\n\n  private cleanup() {\n    try {\n      this.isInitialized();\n      let deleted = false;\n      this.records.forEach((record: JsonRpcRecord) => {\n        const msToExpiry = toMiliseconds(record.expiry || 0) - Date.now();\n        if (msToExpiry <= 0) {\n          this.logger.info(`Deleting expired history log: ${record.id}`);\n          this.records.delete(record.id);\n          this.events.emit(HISTORY_EVENTS.deleted, record, false);\n          deleted = true;\n        }\n      });\n      if (deleted) {\n        this.persist();\n      }\n    } catch (e) {\n      this.logger.warn(e);\n    }\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n}\n", "import { HEARTBEAT_EVENTS } from \"@walletconnect/heartbeat\";\nimport { generateChildLogger, getLoggerContext, Logger } from \"@walletconnect/logger\";\nimport { toMiliseconds } from \"@walletconnect/time\";\nimport { ExpirerTypes, ICore, IExpirer } from \"@walletconnect/types\";\nimport { getInternalError, formatIdTarget, formatTopicTarget } from \"@walletconnect/utils\";\nimport { EventEmitter } from \"events\";\nimport {\n  CORE_STORAGE_PREFIX,\n  EXPIRER_CONTEXT,\n  EXPIRER_EVENTS,\n  EXPIRER_STORAGE_VERSION,\n} from \"../constants\";\n\nexport class Expirer extends IExpirer {\n  public expirations = new Map<string, ExpirerTypes.Expiration>();\n  public events = new EventEmitter();\n  public name = EXPIRER_CONTEXT;\n  public version = EXPIRER_STORAGE_VERSION;\n\n  private cached: ExpirerTypes.Expiration[] = [];\n  private initialized = false;\n\n  private storagePrefix = CORE_STORAGE_PREFIX;\n\n  constructor(public core: I<PERSON>ore, public logger: Logger) {\n    super(core, logger);\n    this.logger = generateChildLogger(logger, this.name);\n  }\n\n  public init: IExpirer[\"init\"] = async () => {\n    if (!this.initialized) {\n      this.logger.trace(`Initialized`);\n      await this.restore();\n      this.cached.forEach((expiration) => this.expirations.set(expiration.target, expiration));\n      this.cached = [];\n      this.registerEventListeners();\n      this.initialized = true;\n    }\n  };\n\n  get context(): string {\n    return getLoggerContext(this.logger);\n  }\n\n  get storageKey() {\n    return this.storagePrefix + this.version + this.core.customStoragePrefix + \"//\" + this.name;\n  }\n\n  get length(): number {\n    return this.expirations.size;\n  }\n\n  get keys(): string[] {\n    return Array.from(this.expirations.keys());\n  }\n\n  get values(): ExpirerTypes.Expiration[] {\n    return Array.from(this.expirations.values());\n  }\n\n  public has: IExpirer[\"has\"] = (key) => {\n    try {\n      const target = this.formatTarget(key);\n      const expiration = this.getExpiration(target);\n      return typeof expiration !== \"undefined\";\n    } catch (e) {\n      // ignore\n      return false;\n    }\n  };\n\n  public set: IExpirer[\"set\"] = (key, expiry) => {\n    this.isInitialized();\n    const target = this.formatTarget(key);\n    const expiration = { target, expiry };\n    this.expirations.set(target, expiration);\n    this.checkExpiry(target, expiration);\n    this.events.emit(EXPIRER_EVENTS.created, {\n      target,\n      expiration,\n    } as ExpirerTypes.Created);\n  };\n\n  public get: IExpirer[\"get\"] = (key) => {\n    this.isInitialized();\n    const target = this.formatTarget(key);\n    return this.getExpiration(target);\n  };\n\n  public del: IExpirer[\"del\"] = (key) => {\n    this.isInitialized();\n    const exists = this.has(key);\n    if (exists) {\n      const target = this.formatTarget(key);\n      const expiration = this.getExpiration(target);\n      this.expirations.delete(target);\n      this.events.emit(EXPIRER_EVENTS.deleted, {\n        target,\n        expiration,\n      } as ExpirerTypes.Deleted);\n    }\n  };\n\n  public on: IExpirer[\"on\"] = (event, listener) => {\n    this.events.on(event, listener);\n  };\n\n  public once: IExpirer[\"once\"] = (event, listener) => {\n    this.events.once(event, listener);\n  };\n\n  public off: IExpirer[\"off\"] = (event, listener) => {\n    this.events.off(event, listener);\n  };\n\n  public removeListener: IExpirer[\"removeListener\"] = (event, listener) => {\n    this.events.removeListener(event, listener);\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private formatTarget(key: string | number) {\n    if (typeof key === \"string\") {\n      return formatTopicTarget(key);\n    } else if (typeof key === \"number\") {\n      return formatIdTarget(key);\n    }\n    const { message } = getInternalError(\"UNKNOWN_TYPE\", `Target type: ${typeof key}`);\n    throw new Error(message);\n  }\n\n  private async setExpirations(expirations: ExpirerTypes.Expiration[]): Promise<void> {\n    await this.core.storage.setItem<ExpirerTypes.Expiration[]>(this.storageKey, expirations);\n  }\n\n  private async getExpirations(): Promise<ExpirerTypes.Expiration[] | undefined> {\n    const expirations = await this.core.storage.getItem<ExpirerTypes.Expiration[]>(this.storageKey);\n    return expirations;\n  }\n\n  private async persist() {\n    await this.setExpirations(this.values);\n    this.events.emit(EXPIRER_EVENTS.sync);\n  }\n\n  private async restore() {\n    try {\n      const persisted = await this.getExpirations();\n      if (typeof persisted === \"undefined\") return;\n      if (!persisted.length) return;\n      if (this.expirations.size) {\n        const { message } = getInternalError(\"RESTORE_WILL_OVERRIDE\", this.name);\n        this.logger.error(message);\n        throw new Error(message);\n      }\n      this.cached = persisted;\n      this.logger.debug(`Successfully Restored expirations for ${this.name}`);\n      this.logger.trace({ type: \"method\", method: \"restore\", expirations: this.values });\n    } catch (e) {\n      this.logger.debug(`Failed to Restore expirations for ${this.name}`);\n      this.logger.error(e as any);\n    }\n  }\n\n  private getExpiration(target: string): ExpirerTypes.Expiration {\n    const expiration = this.expirations.get(target);\n    if (!expiration) {\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `${this.name}: ${target}`);\n      this.logger.warn(message);\n      throw new Error(message);\n    }\n    return expiration;\n  }\n\n  private checkExpiry(target: string, expiration: ExpirerTypes.Expiration): void {\n    const { expiry } = expiration;\n    const msToTimeout = toMiliseconds(expiry) - Date.now();\n    if (msToTimeout <= 0) this.expire(target, expiration);\n  }\n\n  private expire(target: string, expiration: ExpirerTypes.Expiration): void {\n    this.expirations.delete(target);\n    this.events.emit(EXPIRER_EVENTS.expired, {\n      target,\n      expiration,\n    } as ExpirerTypes.Expired);\n  }\n\n  private checkExpirations(): void {\n    // avoid auto expiring if the relayer is not connected\n    if (!this.core.relayer.connected) return;\n    this.expirations.forEach((expiration, target) => this.checkExpiry(target, expiration));\n  }\n\n  private registerEventListeners(): void {\n    this.core.heartbeat.on(HEARTBEAT_EVENTS.pulse, () => this.checkExpirations());\n    this.events.on(EXPIRER_EVENTS.created, (createdEvent: ExpirerTypes.Created) => {\n      const eventName = EXPIRER_EVENTS.created;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, data: createdEvent });\n      this.persist();\n    });\n    this.events.on(EXPIRER_EVENTS.expired, (expiredEvent: ExpirerTypes.Expired) => {\n      const eventName = EXPIRER_EVENTS.expired;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, data: expiredEvent });\n      this.persist();\n    });\n    this.events.on(EXPIRER_EVENTS.deleted, (deletedEvent: ExpirerTypes.Deleted) => {\n      const eventName = EXPIRER_EVENTS.deleted;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, data: deletedEvent });\n      this.persist();\n    });\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n}\n", "import { generateChildLogger, getLogger<PERSON>ontext, Logger } from \"@walletconnect/logger\";\nimport { ICore, IVerify } from \"@walletconnect/types\";\nimport { isBrowser, isTestRun, P256KeyDataType, verifyP256Jwt } from \"@walletconnect/utils\";\nimport { FIVE_SECONDS, ONE_SECOND, toMiliseconds } from \"@walletconnect/time\";\nimport { getDocument } from \"@walletconnect/window-getters\";\nimport { decodeJWT } from \"@walletconnect/relay-auth\";\n\nimport {\n  CORE_STORAGE_PREFIX,\n  CORE_VERSION,\n  TRUSTED_VERIFY_URLS,\n  VERIFY_CONTEXT,\n  VERIFY_SERVER,\n  VERIFY_SERVER_V3,\n} from \"../constants\";\nimport { IKeyValueStorage } from \"@walletconnect/keyvaluestorage\";\n\ntype Jwk = {\n  publicKey: P256KeyDataType;\n  expiresAt: number;\n};\ntype JwkPayload = {\n  exp: number;\n  id: string;\n  origin: string;\n  isScam: boolean;\n  isVerified: boolean;\n};\nexport class Verify extends IVerify {\n  public name = VERIFY_CONTEXT;\n  private abortController: AbortController;\n  private isDevEnv;\n  private verifyUrlV3 = VERIFY_SERVER_V3;\n  private storagePrefix = CORE_STORAGE_PREFIX;\n  private version = CORE_VERSION;\n  private publicKey?: Jwk;\n  private fetchPromise?: Promise<Jwk>;\n\n  constructor(public core: ICore, public logger: Logger, public store: IKeyValueStorage) {\n    super(core, logger, store);\n    this.logger = generateChildLogger(logger, this.name);\n    this.abortController = new AbortController();\n    this.isDevEnv = isTestRun();\n    this.init();\n  }\n\n  get storeKey(): string {\n    return (\n      this.storagePrefix + this.version + this.core.customStoragePrefix + \"//\" + `verify:public:key`\n    );\n  }\n\n  public init = async () => {\n    if (this.isDevEnv) return;\n    this.publicKey = await this.store.getItem(this.storeKey);\n    if (this.publicKey && toMiliseconds(this.publicKey?.expiresAt) < Date.now()) {\n      this.logger.debug(\"verify v2 public key expired\");\n      await this.removePublicKey();\n    }\n  };\n\n  public register: IVerify[\"register\"] = async (params) => {\n    if (!isBrowser() || this.isDevEnv) return;\n    const origin = window.location.origin;\n    const { id, decryptedId } = params;\n    const src = `${this.verifyUrlV3}/attestation?projectId=${this.core.projectId}&origin=${origin}&id=${id}&decryptedId=${decryptedId}`;\n    try {\n      const document = getDocument() as Document;\n      const abortTimeout = this.startAbortTimer(ONE_SECOND * 5);\n      const attestationJwt = await new Promise((resolve, reject) => {\n        const abortListener = () => {\n          window.removeEventListener(\"message\", listener);\n          document.body.removeChild(iframe);\n          reject(\"attestation aborted\");\n        };\n        this.abortController.signal.addEventListener(\"abort\", abortListener);\n        const iframe = document.createElement(\"iframe\");\n        iframe.src = src;\n        iframe.style.display = \"none\";\n        iframe.addEventListener(\"error\", abortListener, { signal: this.abortController.signal });\n        const listener = (event: MessageEvent) => {\n          if (!event.data) return;\n          if (typeof event.data !== \"string\") return;\n          try {\n            const data = JSON.parse(event.data);\n            if (data.type === \"verify_attestation\") {\n              const decoded = decodeJWT(data.attestation) as unknown as { payload: JwkPayload };\n              if (decoded.payload.id !== id) return;\n\n              clearInterval(abortTimeout);\n              document.body.removeChild(iframe);\n              this.abortController.signal.removeEventListener(\"abort\", abortListener);\n              window.removeEventListener(\"message\", listener);\n              resolve(data.attestation === null ? \"\" : data.attestation);\n            }\n          } catch (e) {\n            this.logger.warn(e);\n          }\n        };\n        document.body.appendChild(iframe);\n        window.addEventListener(\"message\", listener, { signal: this.abortController.signal });\n      });\n      this.logger.debug(\"jwt attestation\", attestationJwt);\n      return attestationJwt as string;\n    } catch (e) {\n      this.logger.warn(e);\n    }\n    return \"\";\n  };\n\n  public resolve: IVerify[\"resolve\"] = async (params) => {\n    if (this.isDevEnv) return \"\";\n    const { attestationId, hash, encryptedId } = params;\n    if (attestationId === \"\") {\n      this.logger.debug(\"resolve: attestationId is empty, skipping\");\n      return;\n    }\n\n    if (attestationId) {\n      const decoded = decodeJWT(attestationId) as unknown as { payload: JwkPayload };\n      if (decoded.payload.id !== encryptedId) return;\n      const validation = await this.isValidJwtAttestation(attestationId);\n      if (validation) {\n        if (!validation.isVerified) {\n          this.logger.warn(\"resolve: jwt attestation: origin url not verified\");\n          return;\n        }\n        return validation;\n      }\n    }\n    if (!hash) return;\n    const verifyUrl = this.getVerifyUrl(params?.verifyUrl);\n    return this.fetchAttestation(hash, verifyUrl);\n  };\n\n  get context(): string {\n    return getLoggerContext(this.logger);\n  }\n\n  private fetchAttestation = async (attestationId: string, url: string) => {\n    this.logger.debug(`resolving attestation: ${attestationId} from url: ${url}`);\n    // set artificial timeout to prevent hanging\n    const timeout = this.startAbortTimer(ONE_SECOND * 5);\n    const result = await fetch(`${url}/attestation/${attestationId}?v2Supported=true`, {\n      signal: this.abortController.signal,\n    });\n    clearTimeout(timeout);\n    return result.status === 200 ? await result.json() : undefined;\n  };\n\n  private startAbortTimer(timer: number) {\n    this.abortController = new AbortController();\n    return setTimeout(() => this.abortController.abort(), toMiliseconds(timer));\n  }\n\n  private getVerifyUrl = (verifyUrl?: string) => {\n    let url = verifyUrl || VERIFY_SERVER;\n    if (!TRUSTED_VERIFY_URLS.includes(url)) {\n      this.logger.info(\n        `verify url: ${url}, not included in trusted list, assigning default: ${VERIFY_SERVER}`,\n      );\n      url = VERIFY_SERVER;\n    }\n    return url;\n  };\n\n  private fetchPublicKey = async () => {\n    try {\n      this.logger.debug(`fetching public key from: ${this.verifyUrlV3}`);\n      const timeout = this.startAbortTimer(FIVE_SECONDS);\n      const result = await fetch(`${this.verifyUrlV3}/public-key`, {\n        signal: this.abortController.signal,\n      });\n      clearTimeout(timeout);\n      return (await result.json()) as Jwk;\n    } catch (e) {\n      this.logger.warn(e);\n    }\n    return undefined;\n  };\n\n  private persistPublicKey = async (publicKey: Jwk) => {\n    this.logger.debug(`persisting public key to local storage`, publicKey);\n    await this.store.setItem(this.storeKey, publicKey);\n    this.publicKey = publicKey;\n  };\n\n  private removePublicKey = async () => {\n    this.logger.debug(`removing verify v2 public key from storage`);\n    await this.store.removeItem(this.storeKey);\n    this.publicKey = undefined;\n  };\n\n  private isValidJwtAttestation = async (attestation: string) => {\n    const key = await this.getPublicKey();\n    try {\n      if (key) {\n        const validation = this.validateAttestation(attestation, key);\n        return validation;\n      }\n    } catch (e) {\n      this.logger.error(e);\n      this.logger.warn(\"error validating attestation\");\n    }\n    const newKey = await this.fetchAndPersistPublicKey();\n    try {\n      if (newKey) {\n        const validation = this.validateAttestation(attestation, newKey);\n        return validation;\n      }\n    } catch (e) {\n      this.logger.error(e);\n      this.logger.warn(\"error validating attestation\");\n    }\n    return undefined;\n  };\n\n  private getPublicKey = async () => {\n    if (this.publicKey) return this.publicKey;\n    return await this.fetchAndPersistPublicKey();\n  };\n\n  private fetchAndPersistPublicKey = async () => {\n    if (this.fetchPromise) {\n      await this.fetchPromise;\n      return this.publicKey;\n    }\n    this.fetchPromise = new Promise(async (resolve) => {\n      const key = await this.fetchPublicKey();\n      if (!key) return;\n      await this.persistPublicKey(key);\n      resolve(key);\n    });\n    const key = await this.fetchPromise;\n    this.fetchPromise = undefined;\n    return key;\n  };\n\n  private validateAttestation = (attestation: string, key: Jwk) => {\n    const result = verifyP256Jwt<JwkPayload>(attestation, key.publicKey);\n    const validation = {\n      hasExpired: toMiliseconds(result.exp) < Date.now(),\n      payload: result,\n    };\n\n    if (validation.hasExpired) {\n      this.logger.warn(\"resolve: jwt attestation expired\");\n      throw new Error(\"JWT attestation expired\");\n    }\n\n    return {\n      origin: validation.payload.origin,\n      isScam: validation.payload.isScam,\n      isVerified: validation.payload.isVerified,\n    };\n  };\n}\n", "import { generateChild<PERSON>ogger, Logger } from \"@walletconnect/logger\";\nimport { IEchoClient } from \"@walletconnect/types\";\nimport { ECHO_CONTEXT, ECHO_URL } from \"../constants\";\n\nexport class EchoClient extends IEchoClient {\n  public readonly context = ECHO_CONTEXT;\n  constructor(public projectId: string, public logger: Logger) {\n    super(projectId, logger);\n    this.logger = generateChildLogger(logger, this.context);\n  }\n\n  public registerDeviceToken: IEchoClient[\"registerDeviceToken\"] = async (params) => {\n    const { clientId, token, notificationType, enableEncrypted = false } = params;\n\n    const echoUrl = `${ECHO_URL}/${this.projectId}/clients`;\n\n    await fetch(echoUrl, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({\n        client_id: clientId,\n        type: notificationType,\n        token,\n        always_raw: enableEncrypted,\n      }),\n    });\n  };\n}\n", "import { generateChild<PERSON>ogger, Lo<PERSON> } from \"@walletconnect/logger\";\nimport { ICore, IEventClient, EventClientTypes } from \"@walletconnect/types\";\nimport { formatUA, isTestRun, uuidv4, getAppMetadata } from \"@walletconnect/utils\";\nimport {\n  CORE_STORAGE_PREFIX,\n  EVENTS_CLIENT_API_URL,\n  EVENTS_STORAGE_CLEANUP_INTERVAL,\n  EVENTS_STORAGE_CONTEXT,\n  EVENTS_STORAGE_VERSION,\n  RELAYER_SDK_VERSION,\n} from \"../constants\";\nimport { HEARTBEAT_EVENTS } from \"@walletconnect/heartbeat\";\nimport { fromMiliseconds } from \"@walletconnect/time\";\n\nexport class EventClient extends IEventClient {\n  public readonly context = EVENTS_STORAGE_CONTEXT;\n  private readonly storagePrefix = CORE_STORAGE_PREFIX;\n  private readonly storageVersion = EVENTS_STORAGE_VERSION;\n  private events = new Map<string, EventClientTypes.Event>();\n  private shouldPersist = false;\n  constructor(public core: I<PERSON>ore, public logger: Logger, telemetryEnabled = true) {\n    super(core, logger, telemetryEnabled);\n    this.logger = generateChildLogger(logger, this.context);\n    this.telemetryEnabled = telemetryEnabled;\n    if (telemetryEnabled) {\n      this.restore().then(async () => {\n        await this.submit();\n        this.setEventListeners();\n      });\n    } else {\n      // overwrite any persisted events with an empty array\n      this.persist();\n    }\n  }\n\n  get storageKey() {\n    return (\n      this.storagePrefix + this.storageVersion + this.core.customStoragePrefix + \"//\" + this.context\n    );\n  }\n\n  public init: IEventClient[\"init\"] = async () => {\n    if (isTestRun()) return;\n    try {\n      const initEvent = {\n        eventId: uuidv4(),\n        timestamp: Date.now(),\n        domain: this.getAppDomain(),\n        props: {\n          event: \"INIT\",\n          type: \"\",\n          properties: {\n            client_id: await this.core.crypto.getClientId(),\n            user_agent: formatUA(\n              this.core.relayer.protocol,\n              this.core.relayer.version,\n              RELAYER_SDK_VERSION,\n            ),\n          },\n        },\n      };\n      await this.sendEvent([initEvent] as unknown as EventClientTypes.Event[]);\n    } catch (error) {\n      this.logger.warn(error);\n    }\n  };\n\n  public createEvent: IEventClient[\"createEvent\"] = (params) => {\n    const {\n      event = \"ERROR\",\n      type = \"\",\n      properties: { topic, trace },\n    } = params;\n    const eventId = uuidv4();\n    const bundleId = this.core.projectId || \"\";\n    const timestamp = Date.now();\n    const props = {\n      event,\n      type,\n      properties: {\n        topic,\n        trace,\n      },\n    };\n    const eventObj = {\n      eventId,\n      timestamp,\n      props,\n      bundleId,\n      domain: this.getAppDomain(),\n      ...this.setMethods(eventId),\n    };\n    if (this.telemetryEnabled) {\n      this.events.set(eventId, eventObj);\n      this.shouldPersist = true;\n    }\n\n    return eventObj;\n  };\n\n  public getEvent: IEventClient[\"getEvent\"] = (params) => {\n    const { eventId, topic } = params;\n    if (eventId) {\n      return this.events.get(eventId);\n    }\n    const event = Array.from(this.events.values()).find(\n      (event) => event.props.properties.topic === topic,\n    );\n\n    if (!event) return;\n\n    return {\n      ...event,\n      ...this.setMethods(event.eventId),\n    };\n  };\n\n  public deleteEvent: IEventClient[\"deleteEvent\"] = (params) => {\n    const { eventId } = params;\n    this.events.delete(eventId);\n    this.shouldPersist = true;\n  };\n\n  private setEventListeners = () => {\n    this.core.heartbeat.on(HEARTBEAT_EVENTS.pulse, async () => {\n      if (this.shouldPersist) await this.persist();\n      // cleanup events older than EVENTS_STORAGE_CLEANUP_INTERVAL\n      this.events.forEach((event) => {\n        if (\n          fromMiliseconds(Date.now()) - fromMiliseconds(event.timestamp) >\n          EVENTS_STORAGE_CLEANUP_INTERVAL\n        ) {\n          this.events.delete(event.eventId);\n          this.shouldPersist = true;\n        }\n      });\n    });\n  };\n\n  private setMethods = (eventId: string) => {\n    return {\n      addTrace: (trace: string) => this.addTrace(eventId, trace),\n      setError: (errorType: string) => this.setError(eventId, errorType),\n    };\n  };\n\n  private addTrace = (eventId: string, trace: string) => {\n    const event = this.events.get(eventId);\n    if (!event) return;\n    event.props.properties.trace.push(trace);\n    this.events.set(eventId, event);\n    this.shouldPersist = true;\n  };\n\n  private setError = (eventId: string, errorType: string) => {\n    const event = this.events.get(eventId);\n    if (!event) return;\n    event.props.type = errorType;\n    event.timestamp = Date.now();\n    this.events.set(eventId, event);\n    this.shouldPersist = true;\n  };\n\n  private persist = async () => {\n    await this.core.storage.setItem(this.storageKey, Array.from(this.events.values()));\n    this.shouldPersist = false;\n  };\n\n  private restore = async () => {\n    try {\n      const events =\n        (await this.core.storage.getItem<EventClientTypes.Event[]>(this.storageKey)) || [];\n      if (!events.length) return;\n      events.forEach((event) => {\n        this.events.set(event.eventId, {\n          ...event,\n          ...this.setMethods(event.eventId),\n        });\n      });\n    } catch (error) {\n      this.logger.warn(error);\n    }\n  };\n\n  private submit = async () => {\n    if (!this.telemetryEnabled) return;\n\n    if (this.events.size === 0) return;\n\n    const eventsToSend: EventClientTypes.Event[] = [];\n    // exclude events without type as they can be considered `in progress`\n    for (const [_, event] of this.events) {\n      if (event.props.type) {\n        eventsToSend.push(event);\n      }\n    }\n\n    if (eventsToSend.length === 0) return;\n\n    try {\n      const response = await this.sendEvent(eventsToSend);\n      if (response.ok) {\n        for (const event of eventsToSend) {\n          this.events.delete(event.eventId);\n          this.shouldPersist = true;\n        }\n      }\n    } catch (error) {\n      this.logger.warn(error);\n    }\n  };\n\n  private sendEvent = async (events: EventClientTypes.Event[]) => {\n    // if domain isn't available, set `sp` as `desktop` so data would be extracted on api side\n    const platform = this.getAppDomain() ? \"\" : \"&sp=desktop\";\n    const response = await fetch(\n      `${EVENTS_CLIENT_API_URL}?projectId=${this.core.projectId}&st=events_sdk&sv=js-${RELAYER_SDK_VERSION}${platform}`,\n      {\n        method: \"POST\",\n        body: JSON.stringify(events),\n      },\n    );\n    return response;\n  };\n\n  private getAppDomain = () => {\n    return getAppMetadata().url;\n  };\n}\n", "import { EventEmitter } from \"events\";\n\nimport { HeartBeat } from \"@walletconnect/heartbeat\";\nimport KeyValueStorage from \"@walletconnect/keyvaluestorage\";\nimport {\n  Chunk<PERSON>oggerController,\n  generateChildLogger,\n  generatePlatformLogger,\n  getDefaultLoggerOptions,\n  getLoggerContext,\n} from \"@walletconnect/logger\";\nimport { CoreTypes, ICore } from \"@walletconnect/types\";\n\nimport {\n  CORE_CONTEXT,\n  CORE_DEFAULT,\n  CORE_PROTOCOL,\n  CORE_STORAGE_OPTIONS,\n  CORE_VERSION,\n  RELAYER_DEFAULT_RELAY_URL,\n  TRANSPORT_TYPES,\n  WALLETCONNECT_CLIENT_ID,\n  WALLETCONNECT_LINK_MODE_APPS,\n} from \"./constants\";\nimport {\n  Crypto,\n  EchoClient,\n  EventClient,\n  Expirer,\n  JsonRpcHistory,\n  Pairing,\n  Relayer,\n  Verify,\n} from \"./controllers\";\n\nexport class Core extends ICore {\n  public readonly protocol = CORE_PROTOCOL;\n  public readonly version = CORE_VERSION;\n\n  public readonly name: ICore[\"name\"] = CORE_CONTEXT;\n  public readonly relayUrl: ICore[\"relayUrl\"];\n  public readonly projectId: ICore[\"projectId\"];\n  public readonly customStoragePrefix: ICore[\"customStoragePrefix\"];\n  public events: ICore[\"events\"] = new EventEmitter();\n  public logger: ICore[\"logger\"];\n  public heartbeat: ICore[\"heartbeat\"];\n  public relayer: ICore[\"relayer\"];\n  public crypto: ICore[\"crypto\"];\n  public storage: ICore[\"storage\"];\n  public history: ICore[\"history\"];\n  public expirer: ICore[\"expirer\"];\n  public pairing: ICore[\"pairing\"];\n  public verify: ICore[\"verify\"];\n  public echoClient: ICore[\"echoClient\"];\n  public linkModeSupportedApps: ICore[\"linkModeSupportedApps\"];\n  public eventClient: ICore[\"eventClient\"];\n\n  private initialized = false;\n  private logChunkController: ChunkLoggerController | null;\n\n  static async init(opts?: CoreTypes.Options) {\n    const core = new Core(opts);\n    await core.initialize();\n    const clientId = await core.crypto.getClientId();\n    await core.storage.setItem(WALLETCONNECT_CLIENT_ID, clientId);\n\n    return core;\n  }\n\n  constructor(opts?: CoreTypes.Options) {\n    super(opts);\n    this.projectId = opts?.projectId;\n    this.relayUrl = opts?.relayUrl || RELAYER_DEFAULT_RELAY_URL;\n    this.customStoragePrefix = opts?.customStoragePrefix ? `:${opts.customStoragePrefix}` : \"\";\n\n    const loggerOptions = getDefaultLoggerOptions({\n      level: typeof opts?.logger === \"string\" && opts.logger ? opts.logger : CORE_DEFAULT.logger,\n      name: CORE_CONTEXT,\n    });\n\n    const { logger, chunkLoggerController } = generatePlatformLogger({\n      opts: loggerOptions,\n      maxSizeInBytes: opts?.maxLogBlobSizeInBytes,\n      loggerOverride: opts?.logger,\n    });\n\n    this.logChunkController = chunkLoggerController;\n\n    if (this.logChunkController?.downloadLogsBlobInBrowser) {\n      // @ts-ignore\n      window.downloadLogsBlobInBrowser = async () => {\n        // Have to null check twice becquse there is no guarantee\n        // this.logChunkController.downloadLogsBlobInBrowser is always truthy\n        if (this.logChunkController?.downloadLogsBlobInBrowser) {\n          this.logChunkController?.downloadLogsBlobInBrowser({\n            clientId: await this.crypto.getClientId(),\n          });\n        }\n      };\n    }\n\n    this.logger = generateChildLogger(logger, this.name);\n    this.heartbeat = new HeartBeat();\n    this.crypto = new Crypto(this, this.logger, opts?.keychain);\n    this.history = new JsonRpcHistory(this, this.logger);\n    this.expirer = new Expirer(this, this.logger);\n    this.storage = opts?.storage\n      ? opts.storage\n      : new KeyValueStorage({ ...CORE_STORAGE_OPTIONS, ...opts?.storageOptions });\n    this.relayer = new Relayer({\n      core: this,\n      logger: this.logger,\n      relayUrl: this.relayUrl,\n      projectId: this.projectId,\n    });\n    this.pairing = new Pairing(this, this.logger);\n    this.verify = new Verify(this, this.logger, this.storage);\n    this.echoClient = new EchoClient(this.projectId || \"\", this.logger);\n    this.linkModeSupportedApps = [];\n    this.eventClient = new EventClient(this, this.logger, opts?.telemetryEnabled);\n  }\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  // ---------- Public ----------------------------------------------- //\n\n  public async start() {\n    if (this.initialized) return;\n    await this.initialize();\n  }\n\n  public async getLogsBlob() {\n    return this.logChunkController?.logsToBlob({\n      clientId: await this.crypto.getClientId(),\n    });\n  }\n\n  public async addLinkModeSupportedApp(universalLink: string) {\n    if (this.linkModeSupportedApps.includes(universalLink)) return;\n    this.linkModeSupportedApps.push(universalLink);\n    await this.storage.setItem(WALLETCONNECT_LINK_MODE_APPS, this.linkModeSupportedApps);\n  }\n\n  // ---------- Events ----------------------------------------------- //\n\n  public on = (name: any, listener: any) => {\n    return this.events.on(name, listener);\n  };\n\n  public once = (name: any, listener: any) => {\n    return this.events.once(name, listener);\n  };\n\n  public off = (name: any, listener: any) => {\n    return this.events.off(name, listener);\n  };\n\n  public removeListener = (name: any, listener: any) => {\n    return this.events.removeListener(name, listener);\n  };\n\n  // ---------- Link-mode ----------------------------------------------- //\n\n  public dispatchEnvelope = ({\n    topic,\n    message,\n    sessionExists,\n  }: {\n    topic: string;\n    message: string;\n    sessionExists: boolean;\n  }) => {\n    if (!topic || !message) return;\n\n    const payload = {\n      topic,\n      message,\n      publishedAt: Date.now(),\n      transportType: TRANSPORT_TYPES.link_mode,\n    };\n\n    this.relayer.onLinkMessageEvent(payload, { sessionExists });\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async initialize() {\n    this.logger.trace(`Initialized`);\n    try {\n      await this.crypto.init();\n      await this.history.init();\n      await this.expirer.init();\n      await this.relayer.init();\n      await this.heartbeat.init();\n      await this.pairing.init();\n      this.linkModeSupportedApps = (await this.storage.getItem(WALLETCONNECT_LINK_MODE_APPS)) || [];\n\n      this.initialized = true;\n      this.logger.info(`Core Initialization Success`);\n    } catch (error) {\n      this.logger.warn(`Core Initialization Failure at epoch ${Date.now()}`, error);\n      this.logger.error((error as any).message);\n      throw error;\n    }\n  }\n}\n", "import { Core as WalletConnectCore } from \"./core\";\n\nexport * from \"./constants\";\nexport * from \"./controllers\";\n\nexport const Core = WalletConnectCore;\nexport default WalletConnectCore;\n"], "names": ["ONE_DAY", "SIX_HOURS", "THIRTY_DAYS", "FIVE_SECONDS", "THIRTY_SECONDS", "VERIFY_SERVER_COM", "VERIFY_SERVER_ORG", "fromString", "from", "basex", "decode", "encode", "identity", "varint", "varint.encoding<PERSON>ength", "varint.encodeTo", "Digest.create", "base2", "base8", "base10", "base16", "base32", "base36", "base64", "base256emoji", "bases", "r", "h", "t", "core", "logger", "__publicField", "KEYCHAIN_CONTEXT", "KEYCHAIN_STORAGE_VERSION", "CORE_STORAGE_PREFIX", "keychain", "tag", "key", "message", "getInternalError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getLoggerContext", "mapToObj", "objToMap", "C", "CRYPTO_CONTEXT", "generateRandomBytes32", "seed", "keyPair", "relayAuth", "generateKeyPairUtil", "aud", "sub", "ttl", "CRYPTO_JWT_TTL", "selfPublicKey", "peerPublic<PERSON>ey", "overrideTopic", "selfPrivateKey", "sym<PERSON>ey", "derive<PERSON><PERSON><PERSON>ey", "topic", "hash<PERSON><PERSON>", "public<PERSON>ey", "payload", "opts", "params", "validateEncoding", "safeJsonStringify", "isTypeTwoEnvelope", "encodeTypeTwoEnvelope", "isTypeOneEnvelope", "type", "sender<PERSON><PERSON><PERSON><PERSON><PERSON>", "encrypt", "encoded", "validateDecoding", "decodeTypeTwoEnvelope", "safeJsonParse", "decrypt", "error", "encoding", "BASE64", "deserialized", "deserialize", "decodeTypeByte", "toString", "BASE16", "<PERSON><PERSON><PERSON><PERSON>", "privateKey", "CRYPTO_CLIENT_SEED", "e", "n", "IMessageTracker", "MESSAGES_CONTEXT", "MESSAGES_STORAGE_VERSION", "messages", "hash", "hashMessage", "IPublisher", "relayer", "EventEmitter", "PUBLISHER_CONTEXT", "toMiliseconds", "ONE_MINUTE", "ONE_SECOND", "_a", "PUBLISHER_DEFAULT_TTL", "relay", "getRelayProtocolName", "prompt", "id", "getBigIntRpcId", "failedPublishMessage", "publishPromise", "resolve", "onPublish", "RELAYER_EVENTS", "initialPublish", "createExpiringPromise", "reject", "__spreadProps", "__spreadValues", "event", "listener", "_b", "_c", "_d", "attestation", "tvf", "request", "getRelayProtocolApi", "isUndefined", "result", "attempt", "HEARTBEAT_EVENTS", "a", "i", "ids", "remaining", "x", "O", "l", "m", "ISubscriber", "SubscriberTopicMap", "SUBSCRIBER_CONTEXT", "SUBSCRIBER_STORAGE_VERSION", "label", "watch", "Watch", "interval", "s", "PENDING_SUB_RESOLUTION_TIMEOUT", "pendingSubscriptions", "SUBSCRIBER_EVENTS", "createdEvent", "eventName", "deletedEvent", "reason", "getSdkError", "TRANSPORT_TYPES", "shouldThrow", "subId", "subscribePromise", "onSubscribe", "subscription", "err", "subscriptions", "subs", "numOfBatches", "batch", "persisted", "response", "sleep", "<PERSON><PERSON><PERSON><PERSON>", "RELAYER_CONTEXT", "isNode", "RELAYER_PROVIDER_EVENTS", "pino", "getDefaultLoggerOptions", "RELAYER_DEFAULT_LOGGER", "MessageTracker", "Subscriber", "Publisher", "RELAYER_DEFAULT_RELAY_URL", "isAndroid", "getAppId", "isIos", "shouldThrowOnFailure", "resolvePromise", "onSubCreated", "relayUrl", "isOnline", "sortedMessages", "b", "messageEvent", "expiry", "calcExpiry", "FIVE_MINUTES", "pairing", "onDisconnect", "_e", "auth", "JsonRpcProvider", "WsConnection", "formatRelayRpcUrl", "RELAYER_SDK_VERSION", "exists", "isJsonRpcRequest", "RELAYER_SUBSCRIBER_SUFFIX", "publishedAt", "isJsonRpcResponse", "formatJsonRpcResult", "lastConnectedState", "subscribeToNetworkChange", "connected", "RELAYER_RECONNECT_TIMEOUT", "p", "g", "u", "IStore", "name", "storagePrefix", "<PERSON><PERSON><PERSON>", "STORE_STORAGE_VERSION", "value", "isProposalStruct", "isSessionStruct", "filter", "isEqual", "update", "PAIRING_CONTEXT", "PAIRING_STORAGE_VERSION", "TYPE_1", "methods", "RELAYER_DEFAULT_PROTOCOL", "uri", "formatUri", "PAIRING_EVENTS", "EVENT_CLIENT_PAIRING_TRACES", "expiryTimestamp", "parseUri", "existingPairing", "EVENT_CLIENT_PAIRING_ERRORS", "done", "createDelayedPromise", "engineEvent", "metadata", "method", "formatJsonRpcRequest", "PAIRING_RPC_OPTS", "formatJsonRpcError", "expirer<PERSON><PERSON><PERSON><PERSON><PERSON>", "expiredPairings", "isExpired", "resMethod", "_topic", "isJsonRpcResult", "isJsonRpcError", "isValidParams", "isValidUrl", "isValidString", "Store", "transportType", "EXPIRER_EVENTS", "parseExpirer<PERSON>arget", "IJsonRpcHistory", "HISTORY_CONTEXT", "HISTORY_STORAGE_VERSION", "record", "chainId", "HISTORY_EVENTS", "requests", "requestEvent", "records", "deleted", "IExpirer", "EXPIRER_CONTEXT", "EXPIRER_STORAGE_VERSION", "expiration", "target", "formatTopicTarget", "formatIdTarget", "expirations", "expiredEvent", "IVerify", "store", "VERIFY_CONTEXT", "VERIFY_SERVER_V3", "CORE_VERSION", "<PERSON><PERSON><PERSON><PERSON>", "origin", "decryptedId", "src", "document", "getDocument", "abortTimeout", "attestationJwt", "abortListener", "iframe", "data", "decodeJWT", "attestationId", "encryptedId", "validation", "verifyUrl", "url", "timeout", "VERIFY_SERVER", "TRUSTED_VERIFY_URLS", "new<PERSON>ey", "verifyP256Jwt", "isTestRun", "timer", "IEchoClient", "projectId", "ECHO_CONTEXT", "clientId", "token", "notificationType", "enableEncrypted", "echoUrl", "ECHO_URL", "I", "IEventClient", "telemetryEnabled", "EVENTS_STORAGE_CONTEXT", "EVENTS_STORAGE_VERSION", "initEvent", "uuidv4", "formatUA", "trace", "eventId", "bundleId", "timestamp", "eventObj", "fromMiliseconds", "EVENTS_STORAGE_CLEANUP_INTERVAL", "errorType", "events", "eventsToSend", "_", "platform", "EVENTS_CLIENT_API_URL", "getAppMetadata", "Core", "ICore", "CORE_PROTOCOL", "CORE_CONTEXT", "sessionExists", "loggerOptions", "CORE_DEFAULT", "chunkLoggerController", "generatePlatformLogger", "HeartBeat", "Crypto", "JsonRpcHistory", "Expirer", "KeyValueStorage", "CORE_STORAGE_OPTIONS", "Relayer", "Pairing", "Verify", "EchoClient", "EventClient", "WALLETCONNECT_CLIENT_ID", "universalLink", "WALLETCONNECT_LINK_MODE_APPS", "WalletConnectCore"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAa,MAAA,aAAA,CAAgB,KAChB,YAAe,CAAA,CAAA,CACf,aAAe,MAEf,CAAA,mBAAA,CAAsB,CAAG,EAAA,aAAa,CAAoB,GAAA,EAAA,YAAY,IAEtE,YAAe,CAAA,CAC1B,KAAM,YACN,CAAA,MAAA,CAAQ,OACV,CAEa,CAAA,oBAAA,CAAuB,CAClC,QAAA,CAAU,UACZ;;ACXO,MAAM,eAAiB,QAEjB,CAAA,kBAAA,CAAqB,sBAErB,cAAiBA,CAAAA;;ACNjB,MAAA,gBAAA,CAAmB,WAEnB,wBAA2B,CAAA;;ACF3B,MAAA,gBAAA,CAAmB,WAEnB,wBAA2B,CAAA;;ACA3B,MAAA,qBAAA,CAAwBC,eAExB,iBAAoB,CAAA;;ACJpB,MAAA,wBAAA,CAA2B,KAE3B,CAAA,sBAAA,CAAyB,OAEzB,CAAA,yBAAA,CAA4B,+BAE5B,CAAA,eAAA,CAAkB,SAElB,CAAA,cAAA,CAAiB,CAC5B,OAAA,CAAS,iBACT,CAAA,WAAA,CAAa,sBACb,OAAS,CAAA,iBAAA,CACT,UAAY,CAAA,oBAAA,CACZ,KAAO,CAAA,eAAA,CACP,kBAAoB,CAAA,4BAAA,CACpB,gBAAkB,CAAA,0BAAA,CAClB,OAAS,CAAA,iBACX,CAEa,CAAA,yBAAA,CAA4B,gBAE5B,uBAA0B,CAAA,CACrC,OAAS,CAAA,SAAA,CACT,OAAS,CAAA,SAAA,CACT,UAAY,CAAA,YAAA,CACZ,KAAO,CAAA,OACT,CAEa,CAAA,yBAAA,CAA4B,EAE5B,CAAA,uBAAA,CAA0B,CACrC,QAAU,CAAA,UACZ,CAIa,CAAA,mBAAA,CAAsB,QAGtB,CAAA,wBAAA,CAA2B,GAE3B,CAAA,eAAA,CAAkB,CAC7B,SAAA,CAAW,WACX,CAAA,KAAA,CAAO,OACT;;AC5CO,MAAM,qBAAwB,CAAA,KAAA,CAExB,uBAA0B,CAAA,yBAAA,CAC1B,4BAA+B,CAAA;;ACDrC,MAAM,iBAAoB,CAAA,CAC/B,OAAS,CAAA,sBAAA,CACT,OAAS,CAAA,sBAAA,CACT,OAAS,CAAA,sBAAA,CACT,QAAU,CAAA,uBAAA,CACV,IAAM,CAAA,mBAAA,CACN,YAAc,CAAA,2BAChB,CAEa,CAAA,sBAAA,CAAyBC,gBAEzB,CAAA,kBAAA,CAAqB,cAErB,CAAA,0BAAA,CAA6B,KAE7B,CAAA,8BAAA,CAAiCC,iBAAe,CAAA;;ACdhD,MAAA,eAAA,CAAkB,UAElB,uBAA0B,CAAA,KAAA,CAE1B,oBAAsBD,gBAEtB,CAAA,gBAAA,CAMT,CACF,gBAAkB,CAAA,CAChB,IAAK,CACH,GAAA,CAAKF,YACL,CAAA,MAAA,CAAQ,GACR,GAAK,CAAA,GACP,EACA,GAAK,CAAA,CACH,IAAKA,YACL,CAAA,MAAA,CAAQ,GACR,GAAK,CAAA,IACP,CACF,CACA,CAAA,cAAA,CAAgB,CACd,GAAK,CAAA,CACH,IAAKI,mBACL,CAAA,MAAA,CAAQ,CACR,CAAA,CAAA,GAAA,CAAK,IACP,CACA,CAAA,GAAA,CAAK,CACH,GAAKA,CAAAA,mBAAAA,CACL,OAAQ,CACR,CAAA,CAAA,GAAA,CAAK,IACP,CACF,CAAA,CACA,oBAAqB,CACnB,GAAA,CAAK,CACH,GAAKJ,CAAAA,YAAAA,CACL,OAAQ,CACR,CAAA,CAAA,GAAA,CAAK,CACP,CACA,CAAA,GAAA,CAAK,CACH,GAAKA,CAAAA,YAAAA,CACL,OAAQ,CACR,CAAA,CAAA,GAAA,CAAK,CACP,CACF,CACF,EAEa,cAAiB,CAAA,CAC5B,OAAQ,gBACR,CAAA,MAAA,CAAQ,iBACR,MAAQ,CAAA,gBAAA,CACR,KAAM,cACR;;AC3DO,MAAM,cAAiB,CAAA,CAC5B,OAAS,CAAA,iBAAA,CACT,OAAS,CAAA,iBAAA,CACT,OAAS,CAAA,iBAAA,CACT,IAAM,CAAA,cACR,CAEa,CAAA,eAAA,CAAkB,UAElB,uBAA0B,CAAA;;MCP1B,eAAkB,CAAA,SAAA,CAElB,eAAiB,CAC5B,OAAA,CAAS,kBACT,OAAS,CAAA,iBAAA,CACT,QAAS,iBACT,CAAA,IAAA,CAAM,cACR,CAEa,CAAA,uBAAA,CAA0B,MAE1B,mBAAsBA,CAAAA;;ACb5B,MAAM,eAAiB,aAE9B,MAAMK,GAAoB,CAAA,kCAAA,CACpBC,IAAoB,kCACnB,CAAA,MAAM,aAAgBA,CAAAA,GAAAA,CAChB,iBAAmB,CAAG,EAAA,aAAa,MAEnC,mBAAsB,CAAA,CAACD,IAAmBC,GAAiB;;ACP3D,MAAA,YAAA,CAAe,OAEf,QAAW,CAAA;;ACFjB,MAAM,oBAAuB,CAAA,cAAA,CAEvB,2BAA8B,CAAA,CACzC,gBAAiB,iBACjB,CAAA,8BAAA,CAAgC,gCAChC,CAAA,uBAAA,CAAyB,yBACzB,CAAA,iBAAA,CAAmB,mBACnB,CAAA,yBAAA,CAA2B,4BAC3B,+BAAiC,CAAA,iCAAA,CACjC,gBAAkB,CAAA,kBAAA,CAClB,mBAAqB,CAAA,qBAAA,CACrB,qBAAuB,CAAA,uBAAA,CACvB,sBAAuB,uBACvB,CAAA,4BAAA,CAA8B,8BAChC,CAAA,CAEa,4BAA8B,CACzC,iBAAA,CAAmB,mBACnB,CAAA,sBAAA,CAAwB,yBACxB,qBAAuB,CAAA,uBAAA,CACvB,6BAA+B,CAAA,+BAAA,CAC/B,+BAAiC,CAAA,iCAAA,CACjC,eAAiB,CAAA,iBAAA,CACjB,iBAAkB,kBAClB,CAAA,2BAAA,CAA6B,6BAC/B,CAAA,CAEa,2BAA8B,CAAA,CACzC,uBAAyB,CAAA,yBAAA,CACzB,qBAAsB,sBACtB,CAAA,qCAAA,CAAuC,uCACvC,CAAA,oBAAA,CAAsB,sBACtB,CAAA,yBAAA,CAA2B,2BAC3B,CAAA,+BAAA,CAAiC,kCACjC,0BAA4B,CAAA,4BAAA,CAC5B,+BAAiC,CAAA,iCAAA,CACjC,cAAe,eACf,CAAA,yBAAA,CAA2B,2BAC3B,CAAA,8BAAA,CAAgC,gCAClC,CAEa,CAAA,2BAAA,CAA8B,CACzC,sBAAA,CAAwB,wBACxB,CAAA,iBAAA,CAAmB,mBACnB,CAAA,gBAAA,CAAkB,mBAClB,+BAAiC,CAAA,iCAAA,CACjC,+BAAiC,CAAA,iCAAA,CACjC,8BAAgC,CAAA,gCAAA,CAChC,4CAA8C,CAAA,8CAAA,CAC9C,mBAAoB,oBACtB,CAAA,CAEa,gCAAmC,CAAA,CAC9C,qCAAuC,CAAA,uCAAA,CACvC,iCAAmC,CAAA,mCAAA,CACnC,uBAAwB,wBACxB,CAAA,oBAAA,CAAsB,sBACtB,CAAA,kCAAA,CAAoC,qCACpC,eAAiB,CAAA,iBAAA,CACjB,2BAA6B,CAAA,6BAAA,CAC7B,wCAAyC,yCACzC,CAAA,6CAAA,CAA+C,+CAC/C,CAAA,wCAAA,CAA0C,0CAC1C,CAAA,6CAAA,CAA+C,+CACjD,CAAA,CAEa,iCAAmC,CAC9C,sBAAA,CAAwB,wBACxB,CAAA,iBAAA,CAAmB,mBACnB,CAAA,oCAAA,CAAsC,sCACtC,CAAA,oCAAA,CAAsC,uCACtC,8BAAgC,CAAA,gCAAA,CAChC,4BAA8B,CAAA,8BAAA,CAC9B,aAAe,CAAA,eAAA,CACf,6CAA+C,CAAA,+CAAA,CAC/C,8CAA+C,+CAC/C,CAAA,+CAAA,CACE,iDACJ,CAAA,CAEa,uBAAyB,EAEzB,CAAA,sBAAA,CAAyB,cAEzB,CAAA,+BAAA,CAAkC,MAElC,qBAAwB,CAAA;;ACtFrC,SAAS,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE;AAC9B,EAAE,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE;AAC9B,IAAI,MAAM,IAAI,SAAS,CAAC,mBAAmB,CAAC,CAAC;AAC7C,GAAG;AACH,EAAE,IAAI,QAAQ,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AACrC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,GAAG;AACH,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,IAAI,QAAQ,CAAC,EAAE,CAAC,KAAK,GAAG,EAAE;AAC9B,MAAM,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AACrB,GAAG;AACH,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC7B,EAAE,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClC,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/C,EAAE,SAAS,MAAM,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,MAAM,YAAY,UAAU,CAAC,CAAC;AACtC,SAAS,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;AACzC,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;AACnF,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACtC,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,KAAK;AACL,IAAI,IAAI,EAAE,MAAM,YAAY,UAAU,CAAC,EAAE;AACzC,MAAM,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;AACjD,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;AAC7B,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,IAAI,OAAO,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;AACpD,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,MAAM,EAAE,CAAC;AACf,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,GAAG,MAAM,IAAI,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC;AACnD,IAAI,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AACnC,IAAI,OAAO,MAAM,KAAK,IAAI,EAAE;AAC5B,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACjC,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,MAAM,KAAK,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACtF,QAAQ,KAAK,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACtC,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC;AACtC,QAAQ,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC;AACnC,OAAO;AACP,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE;AACvB,QAAQ,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAC1C,OAAO;AACP,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,MAAM,MAAM,EAAE,CAAC;AACf,KAAK;AACL,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,MAAM,CAAC;AAC5B,IAAI,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC3C,MAAM,GAAG,EAAE,CAAC;AACZ,KAAK;AACL,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACpC,IAAI,OAAO,GAAG,GAAG,IAAI,EAAE,EAAE,GAAG,EAAE;AAC9B,MAAM,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACvC,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH,EAAE,SAAS,YAAY,CAAC,MAAM,EAAE;AAChC,IAAI,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AACpC,MAAM,MAAM,IAAI,SAAS,CAAC,iBAAiB,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;AAC7B,MAAM,OAAO,IAAI,UAAU,EAAE,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;AAC7B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE;AACnC,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,GAAG,EAAE,CAAC;AACZ,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC;AACxD,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AACpC,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,EAAE;AACxB,MAAM,IAAI,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AACnD,MAAM,IAAI,KAAK,KAAK,GAAG,EAAE;AACzB,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,MAAM,KAAK,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACtF,QAAQ,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACxC,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,CAAC;AACtC,QAAQ,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,CAAC;AAClC,OAAO;AACP,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE;AACvB,QAAQ,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAC1C,OAAO;AACP,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,MAAM,GAAG,EAAE,CAAC;AACZ,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;AAC7B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,MAAM,CAAC;AAC5B,IAAI,OAAO,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC5C,MAAM,GAAG,EAAE,CAAC;AACZ,KAAK;AACL,IAAI,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC;AACnB,IAAI,OAAO,GAAG,KAAK,IAAI,EAAE;AACzB,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH,EAAE,SAAS,MAAM,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;AACtC,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,IAAI,GAAG,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;AAC/C,GAAG;AACH,EAAE,OAAO;AACT,IAAI,MAAM,EAAE,MAAM;AAClB,IAAI,YAAY,EAAE,YAAY;AAC9B,IAAI,MAAM,EAAE,MAAM;AAClB,GAAG,CAAC;AACJ,CAAC;AACD,IAAI,GAAG,GAAG,IAAI,CAAC;AACf,IAAI,+BAA+B,GAAG,GAAG;;AChHzC,MAAM,MAAM,GAAG,CAAC,IAAI;AACpB,EAAE,IAAI,CAAC,YAAY,UAAU,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,YAAY;AACpE,IAAI,OAAO,CAAC,CAAC;AACb,EAAE,IAAI,CAAC,YAAY,WAAW;AAC9B,IAAI,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,EAAE,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;AAC7B,IAAI,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC;AAChE,GAAG;AACH,EAAE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACvD,CAAC,CAAC;AAEF,MAAMC,YAAU,GAAG,GAAG,IAAI,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACxD,MAAM,QAAQ,GAAG,CAAC,IAAI,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;;AC7BjD,MAAM,OAAO,CAAC;AACd,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE;AACxC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,GAAG;AACH,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,IAAI,KAAK,YAAY,UAAU,EAAE;AACrC,MAAM,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC3D,KAAK,MAAM;AACX,MAAM,MAAM,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACvD,KAAK;AACL,GAAG;AACH,CAAC;AACD,MAAM,OAAO,CAAC;AACd,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE;AACxC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;AAC7C,MAAM,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,GAAG;AACH,EAAE,MAAM,CAAC,IAAI,EAAE;AACf,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,eAAe,EAAE;AACxD,QAAQ,MAAM,KAAK,CAAC,CAAC,kCAAkC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,4CAA4C,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC/J,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7D,KAAK,MAAM;AACX,MAAM,MAAM,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACvD,KAAK;AACL,GAAG;AACH,EAAE,EAAE,CAAC,OAAO,EAAE;AACd,IAAI,OAAO,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7B,GAAG;AACH,CAAC;AACD,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH,EAAE,EAAE,CAAC,OAAO,EAAE;AACd,IAAI,OAAO,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7B,GAAG;AACH,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5B,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC1C,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACnC,KAAK,MAAM;AACX,MAAM,MAAM,UAAU,CAAC,CAAC,kCAAkC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,4BAA4B,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;AAChK,KAAK;AACL,GAAG;AACH,CAAC;AACM,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK,IAAI,eAAe,CAAC;AACvD,EAAE,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE;AAC7C,EAAE,GAAG,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,EAAE;AAChD,CAAC,CAAC,CAAC;AACI,MAAM,KAAK,CAAC;AACnB,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE;AACpD,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACtC,GAAG;AACH,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACtC,GAAG;AACH,CAAC;AACM,MAAMC,MAAI,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACzF,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAK;AACnD,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAGC,+BAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACjD,EAAE,OAAOD,MAAI,CAAC;AACd,IAAI,MAAM;AACV,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,MAAM,EAAE,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACxC,GAAG,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAME,QAAM,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,KAAK;AACxD,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;AACnB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AAC5C,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,GAAG;AACH,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;AAC1B,EAAE,OAAO,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;AAClC,IAAI,EAAE,GAAG,CAAC;AACV,GAAG;AACH,EAAE,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,GAAG,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACxD,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC;AACf,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC;AAClB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;AAChC,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AAC7B,MAAM,MAAM,IAAI,WAAW,CAAC,CAAC,IAAI,GAAG,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;AACvD,KAAK;AACL,IAAI,MAAM,GAAG,MAAM,IAAI,WAAW,GAAG,KAAK,CAAC;AAC3C,IAAI,IAAI,IAAI,WAAW,CAAC;AACxB,IAAI,IAAI,IAAI,IAAI,CAAC,EAAE;AACnB,MAAM,IAAI,IAAI,CAAC,CAAC;AAChB,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,MAAM,IAAI,IAAI,CAAC;AAC5C,KAAK;AACL,GAAG;AACH,EAAE,IAAI,IAAI,IAAI,WAAW,IAAI,GAAG,GAAG,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE;AACvD,IAAI,MAAM,IAAI,WAAW,CAAC,wBAAwB,CAAC,CAAC;AACpD,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACF,MAAMC,QAAM,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,KAAK;AAChD,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC;AACpD,EAAE,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,WAAW,IAAI,CAAC,CAAC;AACtC,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC;AACf,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC;AACf,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACxC,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACnC,IAAI,IAAI,IAAI,CAAC,CAAC;AACd,IAAI,OAAO,IAAI,GAAG,WAAW,EAAE;AAC/B,MAAM,IAAI,IAAI,WAAW,CAAC;AAC1B,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC;AAC7C,KAAK;AACL,GAAG;AACH,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,GAAG,MAAM,IAAI,WAAW,GAAG,IAAI,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,IAAI,GAAG,EAAE;AACX,IAAI,OAAO,GAAG,CAAC,MAAM,GAAG,WAAW,GAAG,CAAC,EAAE;AACzC,MAAM,GAAG,IAAI,GAAG,CAAC;AACjB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACK,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,CAAC,KAAK;AAClE,EAAE,OAAOH,MAAI,CAAC;AACd,IAAI,MAAM;AACV,IAAI,IAAI;AACR,IAAI,MAAM,CAAC,KAAK,EAAE;AAClB,MAAM,OAAOG,QAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,MAAM,CAAC,KAAK,EAAE;AAClB,MAAM,OAAOD,QAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACxD,KAAK;AACL,GAAG,CAAC,CAAC;AACL,CAAC;;ACnJM,MAAME,UAAQ,GAAGJ,MAAI,CAAC;AAC7B,EAAE,MAAM,EAAE,IAAI;AACd,EAAE,IAAI,EAAE,UAAU;AAClB,EAAE,MAAM,EAAE,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC;AAC9B,EAAE,MAAM,EAAE,GAAG,IAAID,YAAU,CAAC,GAAG,CAAC;AAChC,CAAC,CAAC;;;;;;;ACTK,MAAM,KAAK,GAAG,OAAO,CAAC;AAC7B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,QAAQ,EAAE,IAAI;AAChB,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;;;;;;;ACLK,MAAM,KAAK,GAAG,OAAO,CAAC;AAC7B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,QAAQ,EAAE,UAAU;AACtB,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;;;;;;;ACLK,MAAM,MAAM,GAAG,KAAK,CAAC;AAC5B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,YAAY;AACxB,CAAC,CAAC;;;;;;;ACJK,MAAM,MAAM,GAAG,OAAO,CAAC;AAC9B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,kBAAkB;AAC9B,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,WAAW,GAAG,OAAO,CAAC;AACnC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,QAAQ,EAAE,kBAAkB;AAC9B,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;;;;;;;;ACXK,MAAM,MAAM,GAAG,OAAO,CAAC;AAC9B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,kCAAkC;AAC9C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,WAAW,GAAG,OAAO,CAAC;AACnC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,QAAQ,EAAE,kCAAkC;AAC9C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,SAAS,GAAG,OAAO,CAAC;AACjC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,mCAAmC;AAC/C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,cAAc,GAAG,OAAO,CAAC;AACtC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,gBAAgB;AACxB,EAAE,QAAQ,EAAE,mCAAmC;AAC/C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,SAAS,GAAG,OAAO,CAAC;AACjC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,kCAAkC;AAC9C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,cAAc,GAAG,OAAO,CAAC;AACtC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,gBAAgB;AACxB,EAAE,QAAQ,EAAE,kCAAkC;AAC9C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,YAAY,GAAG,OAAO,CAAC;AACpC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,cAAc;AACtB,EAAE,QAAQ,EAAE,mCAAmC;AAC/C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,iBAAiB,GAAG,OAAO,CAAC;AACzC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,mBAAmB;AAC3B,EAAE,QAAQ,EAAE,mCAAmC;AAC/C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,OAAO,GAAG,OAAO,CAAC;AAC/B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,QAAQ,EAAE,kCAAkC;AAC9C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;;;;;;;;;;;;;;;ACrDK,MAAM,MAAM,GAAG,KAAK,CAAC;AAC5B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,sCAAsC;AAClD,CAAC,CAAC,CAAC;AACI,MAAM,WAAW,GAAG,KAAK,CAAC;AACjC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,QAAQ,EAAE,sCAAsC;AAClD,CAAC,CAAC;;;;;;;;ACTK,MAAM,SAAS,GAAG,KAAK,CAAC;AAC/B,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,QAAQ,EAAE,4DAA4D;AACxE,CAAC,CAAC,CAAC;AACI,MAAM,YAAY,GAAG,KAAK,CAAC;AAClC,EAAE,IAAI,EAAE,cAAc;AACtB,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,QAAQ,EAAE,4DAA4D;AACxE,CAAC,CAAC;;;;;;;;ACTK,MAAM,MAAM,GAAG,OAAO,CAAC;AAC9B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,kEAAkE;AAC9E,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,SAAS,GAAG,OAAO,CAAC;AACjC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,mEAAmE;AAC/E,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,SAAS,GAAG,OAAO,CAAC;AACjC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,kEAAkE;AAC9E,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,YAAY,GAAG,OAAO,CAAC;AACpC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,cAAc;AACtB,EAAE,QAAQ,EAAE,mEAAmE;AAC/E,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;;;;;;;;;;ACvBF,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,s2FAAs2F,CAAC,CAAC;AACp4F,MAAM,oBAAoB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;AAC1D,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,EAAE,OAAO,CAAC,CAAC;AACX,CAAC,EAAE,EAAE,CAAC,CAAC;AACP,MAAM,oBAAoB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;AAC1D,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1B,EAAE,OAAO,CAAC,CAAC;AACX,CAAC,EAAE,EAAE,CAAC,CAAC;AACP,SAASI,QAAM,CAAC,IAAI,EAAE;AACtB,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAC/B,IAAI,CAAC,IAAI,oBAAoB,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,OAAO,CAAC,CAAC;AACb,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AACD,SAASD,QAAM,CAAC,GAAG,EAAE;AACrB,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC;AAClB,EAAE,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;AAC1B,IAAI,MAAM,GAAG,GAAG,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;AAC3B,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,4BAA4B,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;AAC/D,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,GAAG;AACH,EAAE,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC;AACM,MAAM,YAAY,GAAGF,MAAI,CAAC;AACjC,EAAE,MAAM,EAAE,cAAc;AACxB,EAAE,IAAI,EAAE,cAAc;AACtB,UAAEG,QAAM;AACR,UAAED,QAAM;AACR,CAAC,CAAC;;;;;;;AChCF,IAAI,QAAQ,GAAGC,QAAM,CAAC;AACtB,IAAI,GAAG,GAAG,GAAG,EAAE,IAAI,GAAG,GAAG,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACjE,SAASA,QAAM,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE;AAClC,EAAE,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;AAClB,EAAE,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;AACvB,EAAE,IAAI,SAAS,GAAG,MAAM,CAAC;AACzB,EAAE,OAAO,GAAG,IAAI,GAAG,EAAE;AACrB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACpC,IAAI,GAAG,IAAI,GAAG,CAAC;AACf,GAAG;AACH,EAAE,OAAO,GAAG,GAAG,MAAM,EAAE;AACvB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACpC,IAAI,GAAG,MAAM,CAAC,CAAC;AACf,GAAG;AACH,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACxB,EAAEA,QAAM,CAAC,KAAK,GAAG,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC;AACxC,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,IAAI,MAAM,GAAG,IAAI,CAAC;AAClB,IAAI,KAAK,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;AAC9B,SAAS,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE;AAC3B,EAAE,IAAI,GAAG,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;AACpF,EAAE,GAAG;AACL,IAAI,IAAI,OAAO,IAAI,CAAC,EAAE;AACtB,MAAM,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACrB,MAAM,MAAM,IAAI,UAAU,CAAC,yBAAyB,CAAC,CAAC;AACtD,KAAK;AACL,IAAI,CAAC,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;AACvB,IAAI,GAAG,IAAI,KAAK,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAClF,IAAI,KAAK,IAAI,CAAC,CAAC;AACf,GAAG,QAAQ,CAAC,IAAI,KAAK,EAAE;AACvB,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC;AAChC,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,MAAM,GAAG,UAAU,KAAK,EAAE;AAC9B,EAAE,OAAO,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AACrK,CAAC,CAAC;AACF,IAAI,MAAM,GAAG;AACb,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,cAAc,EAAE,MAAM;AACxB,CAAC,CAAC;AACF,IAAI,YAAY,GAAG,MAAM;;AC3ClB,MAAM,QAAQ,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,KAAK;AACrD,EAAEE,YAAM,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACrC,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AACK,MAAM,cAAc,GAAG,GAAG,IAAI;AACrC,EAAE,OAAOA,YAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACpC,CAAC;;ACTM,MAAM,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK;AACxC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC;AACjC,EAAE,MAAM,UAAU,GAAGC,cAAqB,CAAC,IAAI,CAAC,CAAC;AACjD,EAAE,MAAM,YAAY,GAAG,UAAU,GAAGA,cAAqB,CAAC,IAAI,CAAC,CAAC;AAChE,EAAE,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC;AACpD,EAAEC,QAAe,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AAClC,EAAEA,QAAe,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAC3C,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AAClC,EAAE,OAAO,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAC/C,CAAC,CAAC;AAkBK,MAAM,MAAM,CAAC;AACpB,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE;AACzC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,GAAG;AACH;;ACtCO,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACtE,MAAM,MAAM,CAAC;AACpB,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAClC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,GAAG;AACH,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,IAAI,KAAK,YAAY,UAAU,EAAE;AACrC,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACxC,MAAM,OAAO,MAAM,YAAY,UAAU,GAAGC,MAAa,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,IAAIA,MAAa,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;AACvI,KAAK,MAAM;AACX,MAAM,MAAM,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACvD,KAAK;AACL,GAAG;AACH;;ACfA,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,UAAU,CAAC,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAClF,MAAM,MAAM,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,EAAE,UAAU;AAClB,EAAE,IAAI,EAAE,EAAE;AACV,EAAE,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC;AACxB,CAAC,CAAC,CAAC;AACI,MAAM,MAAM,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,EAAE,UAAU;AAClB,EAAE,IAAI,EAAE,EAAE;AACV,EAAE,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC;AACxB,CAAC,CAAC;;;;;;;;ACTF,MAAM,IAAI,GAAG,CAAC,CAAC;AACf,MAAM,IAAI,GAAG,UAAU,CAAC;AACxB,MAAM,MAAM,GAAG,MAAM,CAAC;AACtB,MAAM,MAAM,GAAG,KAAK,IAAIA,MAAa,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACpD,MAAM,QAAQ,GAAG;AACxB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,MAAM;AACR,EAAE,MAAM;AACR,CAAC;;;;;;;ACXmB,IAAI,WAAW,GAAG;AAClB,IAAI,WAAW;;ACoBnC,MAAM,KAAK,GAAG;AACd,EAAE,GAAG,YAAY;AACjB,EAAE,GAAGC,OAAK;AACV,EAAE,GAAGC,OAAK;AACV,EAAE,GAAGC,QAAM;AACX,EAAE,GAAGC,QAAM;AACX,EAAE,GAAGC,QAAM;AACX,EAAE,GAAGC,QAAM;AACX,EAAE,GAAG,MAAM;AACX,EAAE,GAAGC,QAAM;AACX,EAAE,GAAGC,cAAY;AACjB,CAAC,CAAC;CACa;AACf,EAAE,GAAG,IAAI;AACT,EAAE,GAAGZ,UAAQ;AACb;;AC9BO,SAAS,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE;AACtC,EAAE,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,IAAI,UAAU,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,EAAE;AAC1E,IAAI,OAAO,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC/C,GAAG;AACH,EAAE,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAC9B;;ACTA,SAAS,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AACnD,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,OAAO,EAAE;AACb,MAAM,IAAI;AACV,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,MAAM,EAAE;AACvB,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;AAC/C,EAAE,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;AAC1C,EAAE,OAAO,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACnC,CAAC,EAAE,GAAG,IAAI;AACV,EAAE,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;AACpC,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI;AAC/C,EAAE,IAAI,MAAM,GAAG,GAAG,CAAC;AACnB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,IAAI,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC,EAAE,GAAG,IAAI;AACV,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACtC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/B,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,OAAO,EAAE,MAAM;AACjB,EAAE,GAAG,EAAE,KAAK,CAAC,MAAM;AACnB,EAAE,MAAM,EAAE,KAAK;AACf,EAAE,KAAK,EAAE,KAAK;AACd,EAAE,MAAM,EAAE,KAAK;AACf,EAAE,GAAG,KAAK;AACV,CAAC;;AC1CM,SAAS,UAAU,CAAC,MAAM,EAAE,QAAQ,GAAG,MAAM,EAAE;AACtD,EAAE,MAAM,IAAI,GAAGa,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC/B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,sBAAsB,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5D,GAAG;AACH,EAAE,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO,KAAK,UAAU,CAAC,MAAM,IAAI,IAAI,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE;AACpH,IAAI,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAClD,GAAG;AACH,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;AAC5D;;ACVA,IAAAC,GAAA,CAAA,MAAA,CAAA,cAAA,CAAA,IAAAC,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAAD,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAAE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAAD,GAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAMa,QAA8B,CAQzC,WAAmBE,CAAAA,CAAAA,CAAoBC,CAAgB,CAAA,CAApC,IAAAD,CAAAA,IAAAA,CAAAA,CAAAA,CAAoB,YAAAC,CAPvCC,CAAAA,GAAAA,CAAA,IAAO,CAAA,UAAA,CAAW,IAAI,GAAA,CAAA,CACtBA,GAAA,CAAA,IAAA,CAAO,OAAOC,gBACdD,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,SAAA,CAAUE,wBAEjBF,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,aAAA,CAAc,IACtBA,GAAA,CAAA,IAAA,CAAQ,eAAgBG,CAAAA,mBAAAA,CAAAA,CAOxBH,GAAA,CAAA,IAAA,CAAO,MAA0B,CAAA,SAAY,CAC3C,GAAI,CAAC,IAAK,CAAA,WAAA,CAAa,CACrB,MAAMI,CAAW,CAAA,MAAM,KAAK,WAAY,EAAA,CACpC,OAAOA,CAAAA,EAAa,WACtB,GAAA,IAAA,CAAK,QAAWA,CAAAA,CAAAA,CAAAA,CAElB,IAAK,CAAA,WAAA,CAAc,CACrB,EAAA,CACF,CAUAJ,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,KAAA,CAAyBK,IAC9B,IAAK,CAAA,aAAA,EACE,CAAA,IAAA,CAAK,QAAS,CAAA,GAAA,CAAIA,CAAG,CAAA,CAAA,CAAA,CAG9BL,IAAA,IAAO,CAAA,KAAA,CAAwB,MAAOK,CAAAA,CAAKC,CAAQ,GAAA,CACjD,IAAK,CAAA,aAAA,GACL,IAAK,CAAA,QAAA,CAAS,GAAID,CAAAA,CAAAA,CAAKC,CAAG,CAAA,CAC1B,MAAM,IAAA,CAAK,UACb,CAAA,CAAA,CAEAN,GAAA,CAAA,IAAA,CAAO,KAAyBK,CAAAA,CAAAA,EAAQ,CACtC,IAAA,CAAK,eACL,CAAA,MAAMC,CAAM,CAAA,IAAA,CAAK,QAAS,CAAA,GAAA,CAAID,CAAG,CAAA,CACjC,GAAI,OAAOC,CAAAA,EAAQ,WAAa,CAAA,CAC9B,KAAM,CAAE,OAAAC,CAAAA,CAAQ,EAAIC,sBAAiB,CAAA,iBAAA,CAAmB,CAAG,EAAA,IAAA,CAAK,IAAI,CAAA,EAAA,EAAKH,CAAG,CAAA,CAAE,CAC9E,CAAA,MAAM,IAAI,KAAA,CAAME,CAAO,CACzB,CACA,OAAOD,CACT,CAEAN,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,KAAA,CAAwB,MAAOK,CAAAA,EAAQ,CAC5C,IAAA,CAAK,eACL,CAAA,IAAA,CAAK,QAAS,CAAA,MAAA,CAAOA,CAAG,CAAA,CACxB,MAAM,IAAA,CAAK,UACb,CAAA,CAAA,CA/CE,IAAK,CAAA,IAAA,CAAOP,CACZ,CAAA,IAAA,CAAK,MAASW,CAAAA,0BAAAA,CAAoBV,EAAQ,IAAK,CAAA,IAAI,EACrD,CAYA,IAAI,OAAA,EAAU,CACZ,OAAOW,wBAAiB,IAAK,CAAA,MAAM,CACrC,CAEA,IAAI,UAAA,EAAa,CACf,OAAO,KAAK,aAAgB,CAAA,IAAA,CAAK,OAAU,CAAA,IAAA,CAAK,IAAK,CAAA,mBAAA,CAAsB,IAAO,CAAA,IAAA,CAAK,IACzF,CA+BA,MAAc,WAAYN,CAAAA,CAAAA,CAA+B,CACvD,MAAM,IAAK,CAAA,IAAA,CAAK,QAAQ,OAAgC,CAAA,IAAA,CAAK,UAAYO,CAAAA,cAAAA,CAASP,CAAQ,CAAC,EAC7F,CAEA,MAAc,WAAc,EAAA,CAC1B,MAAMA,CAAAA,CAAW,MAAM,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,QAAgC,IAAK,CAAA,UAAU,CACxF,CAAA,OAAO,OAAOA,CAAAA,EAAa,WAAcQ,CAAAA,cAAAA,CAASR,CAAQ,CAAI,CAAA,KAAA,CAChE,CAEA,MAAc,OAAU,EAAA,CACtB,MAAM,IAAA,CAAK,YAAY,IAAK,CAAA,QAAQ,EACtC,CAEQ,aAAgB,EAAA,CACtB,GAAI,CAAC,KAAK,WAAa,CAAA,CACrB,KAAM,CAAE,OAAAG,CAAAA,CAAQ,CAAIC,CAAAA,sBAAAA,CAAiB,kBAAmB,IAAK,CAAA,IAAI,CACjE,CAAA,MAAM,IAAI,KAAA,CAAMD,CAAO,CACzB,CACF,CACF;;ACrFA,IAAAM,GAAA,CAAA,MAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAAlB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CA6BO,MAAM,MAA0B,CAOrC,WAAA,CAAmBG,EAAoBC,CAAgBK,CAAAA,CAAAA,CAAsB,CAA1D,IAAAN,CAAAA,IAAAA,CAAAA,CAAAA,CAAoB,IAAAC,CAAAA,MAAAA,CAAAA,CAAAA,CANvCC,IAAA,IAAO,CAAA,MAAA,CAAOc,cACdd,CAAAA,CAAAA,GAAAA,CAAA,KAAO,UACPA,CAAAA,CAAAA,GAAAA,CAAA,IAAgB,CAAA,yBAAA,CAA0Be,6BAE1Cf,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,aAAA,CAAc,IAQtBA,GAAA,CAAA,IAAA,CAAO,MAAwB,CAAA,SAAY,CACpC,IAAK,CAAA,WAAA,GACR,MAAM,IAAA,CAAK,SAAS,IAAK,EAAA,CACzB,IAAK,CAAA,WAAA,CAAc,IAEvB,CAMAA,CAAAA,CAAAA,GAAAA,CAAA,KAAO,SAA+BK,CAAAA,CAAAA,GACpC,KAAK,aAAc,EAAA,CACZ,IAAK,CAAA,QAAA,CAAS,IAAIA,CAAG,CAAA,CAAA,CAAA,CAG9BL,GAAA,CAAA,IAAA,CAAO,cAAsC,SAAY,CACvD,IAAK,CAAA,aAAA,GACL,MAAMgB,CAAAA,CAAO,MAAM,IAAA,CAAK,eAClBC,CAAAA,CAAAA,CAAUC,YAAU,CAAA,eAAA,CAAgBF,CAAI,CAE9C,CAAA,OADiBE,YAAU,CAAA,SAAA,CAAUD,EAAQ,SAAS,CAExD,CAEAjB,CAAAA,CAAAA,GAAAA,CAAA,KAAO,iBAA8C,CAAA,IAAM,CACzD,IAAK,CAAA,aAAA,GACL,MAAMiB,CAAAA,CAAUE,qBAAoB,EAAA,CACpC,OAAO,IAAK,CAAA,aAAA,CAAcF,CAAQ,CAAA,SAAA,CAAWA,EAAQ,UAAU,CACjE,CAEAjB,CAAAA,CAAAA,GAAAA,CAAA,KAAO,SAA8B,CAAA,MAAOoB,CAAQ,EAAA,CAClD,KAAK,aAAc,EAAA,CACnB,MAAMJ,CAAAA,CAAO,MAAM,IAAK,CAAA,aAAA,EAClBC,CAAAA,CAAAA,CAAUC,aAAU,eAAgBF,CAAAA,CAAI,CACxCK,CAAAA,CAAAA,CAAM,KAAK,uBACXC,CAAAA,CAAAA,CAAMC,eAEZ,OADY,MAAML,aAAU,OAAQG,CAAAA,CAAAA,CAAKD,CAAKE,CAAAA,CAAAA,CAAKL,CAAO,CAE5D,CAAA,CAAA,CAEAjB,GAAA,CAAA,IAAA,CAAO,oBAAkD,CACvDwB,CAAAA,CACAC,CACAC,CAAAA,CAAAA,GACG,CACH,IAAK,CAAA,aAAA,EACL,CAAA,MAAMC,EAAiB,IAAK,CAAA,aAAA,CAAcH,CAAa,CAAA,CACjDI,EAASC,kBAAaF,CAAAA,CAAAA,CAAgBF,CAAa,CAAA,CACzD,OAAO,IAAK,CAAA,SAAA,CAAUG,CAAQF,CAAAA,CAAa,CAC7C,CAEA1B,CAAAA,CAAAA,GAAAA,CAAA,KAAO,WAAkC,CAAA,MAAO4B,EAAQF,CAAkB,GAAA,CACxE,IAAK,CAAA,aAAA,GACL,MAAMI,CAAAA,CAAQJ,CAAiBK,EAAAA,aAAAA,CAAQH,CAAM,CAC7C,CAAA,OAAA,MAAM,IAAK,CAAA,QAAA,CAAS,IAAIE,CAAOF,CAAAA,CAAM,CAC9BE,CAAAA,CACT,GAEA9B,GAAA,CAAA,IAAA,CAAO,eAA0C,CAAA,MAAOgC,GAAsB,CAC5E,IAAA,CAAK,aAAc,EAAA,CACnB,MAAM,IAAK,CAAA,QAAA,CAAS,GAAIA,CAAAA,CAAS,EACnC,CAEAhC,CAAAA,CAAAA,GAAAA,CAAA,KAAO,cAAwC,CAAA,MAAO8B,GAAkB,CACtE,IAAA,CAAK,aAAc,EAAA,CACnB,MAAM,IAAK,CAAA,QAAA,CAAS,GAAIA,CAAAA,CAAK,EAC/B,CAEA9B,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,QAAA,CAA4B,MAAO8B,CAAOG,CAAAA,CAAAA,CAASC,IAAS,CACjE,IAAA,CAAK,eACL,CAAA,MAAMC,CAASC,CAAAA,sBAAAA,CAAiBF,CAAI,CAC9B3B,CAAAA,CAAAA,CAAU8B,0BAAkBJ,CAAAA,CAAO,EAEzC,GAAIK,uBAAAA,CAAkBH,CAAM,CAAA,CAC1B,OAAOI,2BAAsBhC,CAAAA,CAAAA,CAAS2B,GAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,QAAQ,CAGtD,CAAA,GAAIM,uBAAkBL,CAAAA,CAAM,EAAG,CAC7B,MAAMX,CAAgBW,CAAAA,CAAAA,CAAO,gBACvBV,CAAgBU,CAAAA,CAAAA,CAAO,iBAC7BL,CAAAA,CAAAA,CAAQ,MAAM,IAAK,CAAA,iBAAA,CAAkBN,CAAeC,CAAAA,CAAa,EACnE,CACA,MAAMG,CAAS,CAAA,IAAA,CAAK,UAAUE,CAAK,CAAA,CAC7B,CAAE,IAAA,CAAAW,EAAM,eAAAC,CAAAA,CAAgB,CAAIP,CAAAA,CAAAA,CAElC,OADeQ,aAAQ,CAAA,CAAE,KAAAF,CAAM,CAAA,MAAA,CAAAb,EAAQ,OAAArB,CAAAA,CAAAA,CAAS,eAAAmC,CAAAA,CAAAA,CAAiB,SAAUR,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAM,CAAA,QAAS,CAAC,CAE7F,CAAA,CAAA,CAEAlC,GAAA,CAAA,IAAA,CAAO,SAA4B,MAAO8B,CAAAA,CAAOc,CAASV,CAAAA,CAAAA,GAAS,CACjE,IAAK,CAAA,aAAA,EACL,CAAA,MAAMC,EAASU,sBAAiBD,CAAAA,CAAAA,CAASV,CAAI,CAAA,CAC7C,GAAII,uBAAkBH,CAAAA,CAAM,CAAG,CAAA,CAC7B,MAAM5B,CAAUuC,CAAAA,2BAAAA,CAAsBF,EAASV,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,EAAM,QAAQ,CAAA,CAC7D,OAAOa,sBAAAA,CAAcxC,CAAO,CAC9B,CACA,GAAIiC,uBAAAA,CAAkBL,CAAM,CAAG,CAAA,CAC7B,MAAMX,CAAAA,CAAgBW,EAAO,iBACvBV,CAAAA,CAAAA,CAAgBU,CAAO,CAAA,eAAA,CAC7BL,EAAQ,MAAM,IAAA,CAAK,iBAAkBN,CAAAA,CAAAA,CAAeC,CAAa,EACnE,CACA,GAAI,CACF,MAAMG,CAAS,CAAA,IAAA,CAAK,SAAUE,CAAAA,CAAK,EAC7BvB,CAAUyC,CAAAA,aAAAA,CAAQ,CAAE,MAAApB,CAAAA,CAAAA,CAAQ,QAAAgB,CAAS,CAAA,QAAA,CAAUV,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,EAAM,QAAS,CAAC,CAErE,CAAA,OADgBa,uBAAcxC,CAAO,CAEvC,CAAS0C,MAAAA,CAAAA,CAAO,CACd,IAAK,CAAA,MAAA,CAAO,MACV,CAAyCnB,sCAAAA,EAAAA,CAAK,iBAAiB,MAAM,IAAA,CAAK,WAAY,EAAC,GACzF,CACA,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAMmB,CAAK,EACzB,CACF,CAEAjD,CAAAA,CAAAA,GAAAA,CAAA,KAAO,gBAA4C,CAAA,CAAC4C,EAASM,CAAWC,CAAAA,YAAAA,GAAW,CACjF,MAAMC,CAAAA,CAAeC,iBAAY,CAAA,CAAE,QAAAT,CAAS,CAAA,QAAA,CAAAM,CAAS,CAAC,EACtD,OAAOI,oBAAAA,CAAeF,CAAa,CAAA,IAAI,CACzC,CAEApD,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,2BAAA,CAAkE,CACvE4C,CACAM,CAAAA,CAAAA,CAAWC,YACR,GAAA,CACH,MAAMC,CAAeC,CAAAA,iBAAAA,CAAY,CAAE,OAAA,CAAAT,EAAS,QAAAM,CAAAA,CAAS,CAAC,CAAA,CACtD,OAAOE,CAAa,CAAA,eAAA,CAChBG,qBAASH,CAAa,CAAA,eAAA,CAAiBI,YAAM,CAC7C,CAAA,KAAA,CACN,CAnIE,CAAA,CAAA,IAAA,CAAK,KAAO1D,CACZ,CAAA,IAAA,CAAK,MAASW,CAAAA,0BAAAA,CAAoBV,EAAQ,IAAK,CAAA,IAAI,CACnD,CAAA,IAAA,CAAK,SAAWK,CAAY,EAAA,IAAIqD,QAAS,CAAA,IAAA,CAAK,KAAM,IAAK,CAAA,MAAM,EACjE,CASA,IAAI,OAAU,EAAA,CACZ,OAAO/C,uBAAAA,CAAiB,KAAK,MAAM,CACrC,CAyHA,MAAc,cAAcsB,CAAmB0B,CAAAA,CAAAA,CAAqC,CAClF,OAAM,MAAA,IAAA,CAAK,SAAS,GAAI1B,CAAAA,CAAAA,CAAW0B,CAAU,CAAA,CACtC1B,CACT,CAEQ,aAAA,CAAcA,CAAmB,CAAA,CAEvC,OADmB,IAAK,CAAA,QAAA,CAAS,GAAIA,CAAAA,CAAS,CAEhD,CAEA,MAAc,aAAqC,EAAA,CACjD,IAAIhB,CAAO,CAAA,EAAA,CACX,GAAI,CACFA,EAAO,IAAK,CAAA,QAAA,CAAS,GAAI2C,CAAAA,kBAAkB,EAC7C,CAAQC,MAAAA,CAAAA,CAAA,CACN5C,CAAAA,CAAOD,6BACP,CAAA,MAAM,KAAK,QAAS,CAAA,GAAA,CAAI4C,mBAAoB3C,CAAI,EAClD,CACA,OAAOxC,WAAWwC,CAAM,CAAA,QAAQ,CAClC,CAEQ,UAAUc,CAAe,CAAA,CAE/B,OADe,IAAA,CAAK,SAAS,GAAIA,CAAAA,CAAK,CAExC,CAEQ,aAAA,EAAgB,CACtB,GAAI,CAAC,IAAK,CAAA,WAAA,CAAa,CACrB,KAAM,CAAE,OAAAvB,CAAAA,CAAQ,EAAIC,sBAAiB,CAAA,iBAAA,CAAmB,IAAK,CAAA,IAAI,EACjE,MAAM,IAAI,MAAMD,CAAO,CACzB,CACF,CACF;;AC5MA,IAAAsD,GAAA,CAAA,MAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAKa,MAAA,cAAA,SAAuBC,qBAAgB,CAQlD,WAAA,CAAmB/D,EAAuBD,CAAa,CAAA,CACrD,MAAMC,CAAQD,CAAAA,CAAI,EADD,IAAAC,CAAAA,MAAAA,CAAAA,CAAAA,CAAuB,UAAAD,CAP1CE,CAAAA,CAAAA,CAAA,IAAO,CAAA,UAAA,CAAW,IAAI,GACtBA,CAAAA,CAAAA,CAAAA,CAAA,KAAO,MAAO+D,CAAAA,gBAAAA,CAAAA,CACd/D,EAAA,IAAO,CAAA,SAAA,CAAUgE,0BAEjBhE,CAAA,CAAA,IAAA,CAAQ,cAAc,CACtBA,CAAAA,CAAAA,CAAAA,CAAAA,CAAA,KAAQ,eAAgBG,CAAAA,mBAAAA,CAAAA,CAQxBH,EAAA,IAAO,CAAA,MAAA,CAAgC,SAAY,CACjD,GAAI,CAAC,IAAA,CAAK,YAAa,CACrB,IAAA,CAAK,OAAO,KAAM,CAAA,aAAa,EAC/B,GAAI,CACF,MAAMiE,CAAW,CAAA,MAAM,KAAK,kBAAmB,EAAA,CAC3C,OAAOA,CAAa,EAAA,WAAA,GACtB,IAAK,CAAA,QAAA,CAAWA,GAGlB,IAAK,CAAA,MAAA,CAAO,MAAM,CAAqC,kCAAA,EAAA,IAAA,CAAK,IAAI,CAAE,CAAA,CAAA,CAClE,KAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAM,CAAA,QAAA,CAAU,OAAQ,SAAW,CAAA,IAAA,CAAM,KAAK,QAAS,CAAA,IAAK,CAAC,EACnF,OAAS,CAAG,CAAA,CACV,KAAK,MAAO,CAAA,KAAA,CAAM,iCAAiC,IAAK,CAAA,IAAI,EAAE,CAC9D,CAAA,IAAA,CAAK,OAAO,KAAM,CAAA,CAAQ,EAC5B,CAAE,OAAA,CACA,KAAK,WAAc,CAAA,CAAA,EACrB,CACF,CACF,GAUAjE,CAAA,CAAA,IAAA,CAAO,MAA8B,MAAO8B,CAAAA,CAAOvB,IAAY,CAC7D,IAAA,CAAK,eACL,CAAA,MAAM2D,EAAOC,iBAAY5D,CAAAA,CAAO,EAChC,IAAI0D,CAAAA,CAAW,KAAK,QAAS,CAAA,GAAA,CAAInC,CAAK,CAAA,CAItC,OAHI,OAAOmC,CAAAA,EAAa,cACtBA,CAAW,CAAA,IAET,OAAOA,CAAAA,CAASC,CAAI,CAAM,EAAA,WAAA,GAG9BD,EAASC,CAAI,CAAA,CAAI3D,EACjB,IAAK,CAAA,QAAA,CAAS,IAAIuB,CAAOmC,CAAAA,CAAQ,CACjC,CAAA,MAAM,KAAK,OAAQ,EAAA,CAAA,CACZC,CACT,CAEAlE,CAAAA,CAAAA,CAAAA,CAAA,KAAO,KAA+B8B,CAAAA,CAAAA,EAAU,CAC9C,IAAK,CAAA,aAAA,GACL,IAAImC,CAAAA,CAAW,KAAK,QAAS,CAAA,GAAA,CAAInC,CAAK,CACtC,CAAA,OAAI,OAAOmC,CAAAA,EAAa,cACtBA,CAAW,CAAA,IAENA,CACT,CAAA,CAAA,CAEAjE,EAAA,IAAO,CAAA,KAAA,CAA8B,CAAC8B,CAAOvB,CAAAA,CAAAA,GAAY,CACvD,IAAK,CAAA,aAAA,GACL,MAAM0D,CAAAA,CAAW,KAAK,GAAInC,CAAAA,CAAK,CACzBoC,CAAAA,CAAAA,CAAOC,kBAAY5D,CAAO,CAAA,CAChC,OAAO,OAAO0D,CAAAA,CAASC,CAAI,CAAM,EAAA,WACnC,GAEAlE,CAAA,CAAA,IAAA,CAAO,MAA8B,MAAO8B,CAAAA,EAAU,CACpD,IAAK,CAAA,aAAA,GACL,IAAK,CAAA,QAAA,CAAS,MAAOA,CAAAA,CAAK,EAC1B,MAAM,IAAA,CAAK,UACb,CAAA,CAAA,CApEE,KAAK,MAASrB,CAAAA,0BAAAA,CAAoBV,EAAQ,IAAK,CAAA,IAAI,EACnD,IAAK,CAAA,IAAA,CAAOD,EACd,CAsBA,IAAI,SAAkB,CACpB,OAAOY,uBAAiB,CAAA,IAAA,CAAK,MAAM,CACrC,CAEA,IAAI,UAAa,EAAA,CACf,OAAO,IAAK,CAAA,aAAA,CAAgB,KAAK,OAAU,CAAA,IAAA,CAAK,KAAK,mBAAsB,CAAA,IAAA,CAAO,KAAK,IACzF,CA0CA,MAAc,kBAAmBuD,CAAAA,CAAAA,CAAqD,CACpF,MAAM,KAAK,IAAK,CAAA,OAAA,CAAQ,QACtB,IAAK,CAAA,UAAA,CACLtD,eAASsD,CAAQ,CACnB,EACF,CAEA,MAAc,oBAAsE,CAClF,MAAMA,EAAW,MAAM,IAAA,CAAK,KAAK,OAAQ,CAAA,OAAA,CACvC,IAAK,CAAA,UACP,EACA,OAAO,OAAOA,GAAa,WAAcrD,CAAAA,cAAAA,CAASqD,CAAQ,CAAI,CAAA,KAAA,CAChE,CAEA,MAAc,OAAA,EAAU,CACtB,MAAM,IAAA,CAAK,mBAAmB,IAAK,CAAA,QAAQ,EAC7C,CAEQ,aAAA,EAAgB,CACtB,GAAI,CAAC,KAAK,WAAa,CAAA,CACrB,KAAM,CAAE,OAAA,CAAA1D,CAAQ,CAAIC,CAAAA,sBAAAA,CAAiB,kBAAmB,IAAK,CAAA,IAAI,EACjE,MAAM,IAAI,MAAMD,CAAO,CACzB,CACF,CACF;;ufC1Fa,MAAA,SAAA,SAAkB6D,gBAAW,CASxC,WAAA,CAAmBC,EAA0BtE,CAAgB,CAAA,CAC3D,MAAMsE,CAAStE,CAAAA,CAAM,EADJ,IAAAsE,CAAAA,OAAAA,CAAAA,CAAAA,CAA0B,YAAAtE,CAR7CC,CAAAA,GAAAA,CAAA,KAAO,QAAS,CAAA,IAAIsE,kBACpBtE,GAAA,CAAA,IAAA,CAAO,OAAOuE,iBACdvE,CAAAA,CAAAA,GAAAA,CAAA,KAAO,OAAQ,CAAA,IAAI,KAEnBA,GAAA,CAAA,IAAA,CAAQ,iBAAiBwE,kBAAcC,CAAAA,eAAU,GACjDzE,GAAA,CAAA,IAAA,CAAQ,wBAAwBwE,kBAAcE,CAAAA,eAAAA,CAAa,EAAE,CAC7D1E,CAAAA,CAAAA,GAAAA,CAAA,KAAQ,uBAAwB,CAAA,CAAA,CAAA,CAAA,CAahCA,IAAA,IAAO,CAAA,SAAA,CAAiC,MAAO8B,CAAOvB,CAAAA,CAAAA,CAAS2B,IAAS,CAzC1E,IAAAyC,EA0CI,IAAK,CAAA,MAAA,CAAO,MAAM,oBAAoB,CAAA,CACtC,KAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAM,CAAA,QAAA,CAAU,OAAQ,SAAW,CAAA,MAAA,CAAQ,CAAE,KAAA7C,CAAAA,CAAAA,CAAO,QAAAvB,CAAS,CAAA,IAAA,CAAA2B,CAAK,CAAE,CAAC,EAEzF,MAAMZ,CAAAA,CAAAA,CAAMY,GAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,MAAO0C,qBACnBC,CAAAA,CAAAA,CAAQC,2BAAqB5C,CAAI,CAAA,CACjC6C,GAAS7C,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,EAAM,MAAU,GAAA,CAAA,CAAA,CACzB7B,GAAM6B,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,EAAM,GAAO,GAAA,CAAA,CACnB8C,GAAK9C,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,EAAM,EAAO+C,GAAAA,2BAAAA,GAAiB,QAAS,EAAA,CAC5C9C,EAAS,CACb,KAAA,CAAAL,EACA,OAAAvB,CAAAA,CAAAA,CACA,KAAM,CACJ,GAAA,CAAAe,EACA,KAAAuD,CAAAA,CAAAA,CACA,OAAAE,CACA,CAAA,GAAA,CAAA1E,EACA,EAAA2E,CAAAA,CAAAA,CACA,YAAa9C,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,EAAM,WACnB,CAAA,GAAA,CAAKA,GAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,GACb,CACF,CACMgD,CAAAA,CAAAA,CAAuB,mDAAmDF,CAAE,CAAA,KAAA,EAAQ3E,CAAG,CAE7F,CAAA,CAAA,GAAI,CAMF,MAAM8E,CAAAA,CAAiB,IAAI,OAAQ,CAAA,MAAOC,GAAY,CACpD,MAAMC,EAAY,CAAC,CAAE,GAAAL,CAAG,CAAA,GAAsB,CACxC7C,CAAO,CAAA,IAAA,CAAK,KAAO6C,CACrB,GAAA,IAAA,CAAK,uBAAuBA,CAAE,CAAA,CAC9B,KAAK,OAAQ,CAAA,MAAA,CAAO,eAAeM,cAAe,CAAA,OAAA,CAASD,CAAS,CACpED,CAAAA,CAAAA,CAAQjD,CAAM,CAElB,EAAA,CAAA,CACA,KAAK,OAAQ,CAAA,MAAA,CAAO,GAAGmD,cAAe,CAAA,OAAA,CAASD,CAAS,CACxD,CAAA,MAAME,EAAiBC,2BACrB,CAAA,IAAI,QAAQ,CAACJ,CAAAA,CAASK,IAAW,CAC/B,IAAA,CAAK,WAAW,CACd,KAAA,CAAA3D,EACA,OAAAvB,CAAAA,CAAAA,CACA,IAAAe,CACA,CAAA,MAAA,CAAAyD,EACA,GAAA1E,CAAAA,CAAAA,CACA,GAAA2E,CACA,CAAA,WAAA,CAAa9C,GAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,YACnB,GAAKA,CAAAA,CAAAA,EAAA,YAAAA,CAAM,CAAA,GACb,CAAC,CACE,CAAA,IAAA,CAAKkD,CAAO,CACZ,CAAA,KAAA,CAAOxB,GAAM,CACZ,IAAA,CAAK,MAAO,CAAA,IAAA,CAAKA,CAAGA,CAAAA,CAAAA,EAAA,YAAAA,CAAG,CAAA,OAAO,EAC9B6B,CAAO7B,CAAAA,CAAC,EACV,CAAC,EACL,CAAC,CACD,CAAA,IAAA,CAAK,sBACL,CAA2CoB,wCAAAA,EAAAA,CAAE,QAAQ3E,CAAG,CAAA,CAC1D,EACA,GAAI,CACF,MAAMkF,CACN,CAAA,IAAA,CAAK,OAAO,cAAeD,CAAAA,cAAAA,CAAe,QAASD,CAAS,EAC9D,OAASzB,CAAG,CAAA,CACV,KAAK,KAAM,CAAA,GAAA,CAAIoB,EAAIU,CAAAC,CAAAA,GAAAA,CAAA,GAAKxD,CAAL,CAAA,CAAA,CAAa,QAAS,CAAE,CAAA,CAAC,EAC5C,IAAK,CAAA,MAAA,CAAO,KAAKyB,CAAIA,CAAAA,CAAAA,EAAA,YAAAA,CAAa,CAAA,OAAO,EAC3C,CACF,CAAC,EACD,IAAK,CAAA,MAAA,CAAO,MAAM,CAChB,IAAA,CAAM,SACN,MAAQ,CAAA,SAAA,CACR,OAAQ,CAAE,EAAA,CAAAoB,EAAI,KAAAlD,CAAAA,CAAAA,CAAO,QAAAvB,CAAS,CAAA,IAAA,CAAA2B,CAAK,CACrC,CAAC,EAED,MAAMsD,2BAAAA,CAAsBL,EAAgB,IAAK,CAAA,cAAA,CAAgBD,CAAoB,EACvF,CAAA,MAAStB,EAAG,CAGV,GAFA,KAAK,MAAO,CAAA,KAAA,CAAM,2BAA2B,CAAA,CAC7C,IAAK,CAAA,MAAA,CAAO,MAAMA,CAAQ,CAAA,CAAA,CACtBe,EAAAzC,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,EAAM,QAAN,GAAA,IAAA,EAAAyC,EAAgB,oBAClB,CAAA,MAAMf,CAEV,CAAE,OAAA,CACA,KAAK,KAAM,CAAA,MAAA,CAAOoB,CAAE,EACtB,CACF,GAEAhF,GAAA,CAAA,IAAA,CAAO,KAAuB,CAAC4F,CAAAA,CAAOC,IAAa,CACjD,IAAA,CAAK,OAAO,EAAGD,CAAAA,CAAAA,CAAOC,CAAQ,EAChC,CAAA,CAAA,CAEA7F,IAAA,IAAO,CAAA,MAAA,CAA2B,CAAC4F,CAAOC,CAAAA,CAAAA,GAAa,CACrD,IAAK,CAAA,MAAA,CAAO,KAAKD,CAAOC,CAAAA,CAAQ,EAClC,CAEA7F,CAAAA,CAAAA,GAAAA,CAAA,KAAO,KAAyB,CAAA,CAAC4F,EAAOC,CAAa,GAAA,CACnD,KAAK,MAAO,CAAA,GAAA,CAAID,EAAOC,CAAQ,EACjC,GAEA7F,GAAA,CAAA,IAAA,CAAO,iBAA+C,CAAC4F,CAAAA,CAAOC,IAAa,CACzE,IAAA,CAAK,OAAO,cAAeD,CAAAA,CAAAA,CAAOC,CAAQ,EAC5C,CAAA,CAAA,CA7GE,KAAK,OAAUxB,CAAAA,CAAAA,CACf,KAAK,MAAS5D,CAAAA,0BAAAA,CAAoBV,EAAQ,IAAK,CAAA,IAAI,EACnD,IAAK,CAAA,sBAAA,GACP,CAEA,IAAI,SAAU,CACZ,OAAOW,wBAAiB,IAAK,CAAA,MAAM,CACrC,CA0GA,MAAc,WAAWyB,CAStB,CAAA,CA1JL,IAAAwC,CAAAmB,CAAAA,CAAAA,CAAAC,EAAAC,CA2JI,CAAA,KAAM,CACJ,KAAAlE,CAAAA,CAAAA,CACA,QAAAvB,CACA,CAAA,GAAA,CAAAe,EAAMsD,qBACN,CAAA,MAAA,CAAAG,EACA,GAAA1E,CAAAA,CAAAA,CACA,GAAA2E,CACA,CAAA,WAAA,CAAAiB,EACA,GAAAC,CAAAA,CACF,EAAI/D,CAEEgE,CAAAA,CAAAA,CAAwD,CAC5D,MAFUC,CAAAA,yBAAAA,CAAoBtB,4BAAuB,CAAA,QAAQ,EAEjD,OACZ,CAAA,MAAA,CAAQa,IAAA,CACN,KAAA,CAAA7D,EACA,OAAAvB,CAAAA,CAAAA,CACA,IAAAe,CACA,CAAA,MAAA,CAAAyD,EACA,GAAA1E,CAAAA,CAAAA,CACA,YAAA4F,CACGC,CAAAA,CAAAA,CAAAA,CAAAA,CAEL,GAAAlB,CACF,CAAA,CACIqB,mBAAY1B,CAAAwB,CAAAA,CAAAA,CAAQ,SAAR,IAAAxB,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAgB,MAAM,CAAGmB,GAAAA,CAAAA,CAAAA,CAAOK,EAAQ,MAAf,GAAA,IAAA,EAAA,OAAAL,EAAuB,MAC5DO,CAAAA,CAAAA,iBAAAA,CAAAA,CAAYN,EAAAI,CAAQ,CAAA,MAAA,GAAR,YAAAJ,CAAgB,CAAA,GAAG,KAAGC,CAAOG,CAAAA,CAAAA,CAAQ,SAAf,IAAAH,EAAAA,OAAAA,CAAAA,CAAuB,KAC7D,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,wBAAwB,CAC1C,CAAA,IAAA,CAAK,OAAO,KAAM,CAAA,CAAE,KAAM,SAAW,CAAA,SAAA,CAAW,WAAY,OAAAG,CAAAA,CAAQ,CAAC,CACrE,CAAA,MAAMG,EAAS,MAAM,IAAA,CAAK,QAAQ,OAAQH,CAAAA,CAAO,EACjD,OAAK,IAAA,CAAA,OAAA,CAAQ,OAAO,IAAKb,CAAAA,cAAAA,CAAe,QAASnD,CAAM,CAAA,CACvD,KAAK,MAAO,CAAA,KAAA,CAAM,gCAAgC,CAC3CmE,CAAAA,CACT,CAEQ,sBAAuBtB,CAAAA,CAAAA,CAAY,CACzC,IAAK,CAAA,KAAA,CAAM,OAAOA,CAAE,EACtB,CAEQ,UAAa,EAAA,CACnB,KAAK,KAAM,CAAA,OAAA,CAAQ,MAAO7C,CAAQ6C,CAAAA,CAAAA,GAAO,CACvC,MAAMuB,CAAAA,CAAUpE,EAAO,OAAU,CAAA,CAAA,CACjC,KAAK,KAAM,CAAA,GAAA,CAAI6C,EAAIU,CAAAC,CAAAA,GAAAA,CAAA,GAAKxD,CAAL,CAAA,CAAA,CAAa,QAAAoE,CAAQ,CAAA,CAAC,EACzC,KAAM,CAAE,MAAAzE,CAAO,CAAA,OAAA,CAAAvB,EAAS,IAAA2B,CAAAA,CAAAA,CAAM,YAAA+D,CAAY,CAAA,CAAI9D,EAC9C,IAAK,CAAA,MAAA,CAAO,KACV,EAAC,CACD,iCAAiCA,CAAO,CAAA,IAAA,CAAK,EAAE,CAAUA,OAAAA,EAAAA,CAAAA,CAAO,IAAK,CAAA,GAAG,CAAcoE,WAAAA,EAAAA,CAAO,EAC/F,CACA,CAAA,MAAM,KAAK,UAAWb,CAAAA,CAAAA,CAAAC,IAAA,EACjBxD,CAAAA,CAAAA,CAAAA,CADiB,CAEpB,KAAAL,CAAAA,CAAAA,CACA,QAAAvB,CACA,CAAA,GAAA,CAAK2B,EAAK,GACV,CAAA,MAAA,CAAQA,EAAK,MACb,CAAA,GAAA,CAAKA,EAAK,GACV,CAAA,EAAA,CAAIA,EAAK,EACT,CAAA,WAAA,CAAA+D,EACA,GAAK/D,CAAAA,CAAAA,CAAK,GACZ,CAAC,CAAA,CAAA,CACD,KAAK,MAAO,CAAA,IAAA,CAAK,EAAI,CAAA,CAAA,6BAAA,EAAgCC,EAAO,IAAK,CAAA,EAAE,EAAE,EACvE,CAAC,EACH,CAEQ,sBAAA,EAAyB,CAC/B,IAAK,CAAA,OAAA,CAAQ,KAAK,SAAU,CAAA,EAAA,CAAGqE,2BAAiB,KAAO,CAAA,IAAM,CAG3D,GAAI,IAAA,CAAK,sBAAuB,CAC9B,IAAA,CAAK,sBAAwB,CAC7B,CAAA,CAAA,IAAA,CAAK,QAAQ,MAAO,CAAA,IAAA,CAAKlB,eAAe,kBAAkB,CAAA,CAC1D,MACF,CACA,IAAA,CAAK,aACP,CAAC,EACD,IAAK,CAAA,OAAA,CAAQ,GAAGA,cAAe,CAAA,WAAA,CAAcM,GAA0B,CACrE,IAAA,CAAK,uBAAuBA,CAAM,CAAA,EAAA,CAAG,UAAU,EACjD,CAAC,EACH,CACF;;ACtOO,IAAAa,GAAA,CAAA,MAAA,CAAA,cAAA,CAAA,IAAA5C,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA4C,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAAC,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA7C,GAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAM,kBAAkD,CAAxD,WACL7D,EAAAA,CAAAA,GAAAA,CAAA,KAAO,KAAM,CAAA,IAAI,GAMjBA,CAAAA,CAAAA,GAAAA,CAAA,KAAO,KAAkC,CAAA,CAAC8B,CAAOkD,CAAAA,CAAAA,GAAO,CACtD,MAAM2B,CAAAA,CAAM,IAAK,CAAA,GAAA,CAAI7E,CAAK,CAAA,CACtB,IAAK,CAAA,MAAA,CAAOA,EAAOkD,CAAE,CAAA,EACzB,IAAK,CAAA,GAAA,CAAI,IAAIlD,CAAO,CAAA,CAAC,GAAG6E,CAAAA,CAAK3B,CAAE,CAAC,EAClC,CAEAhF,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,KAAA,CAAmC8B,CAC5B,EAAA,IAAA,CAAK,IAAI,GAAIA,CAAAA,CAAK,CAChB,EAAA,IAGhB9B,GAAA,CAAA,IAAA,CAAO,QAAwC,CAAA,CAAC8B,EAAOkD,CACzC,GAAA,IAAA,CAAK,GAAIlD,CAAAA,CAAK,EACf,QAASkD,CAAAA,CAAE,CAGxBhF,CAAAA,CAAAA,GAAAA,CAAA,KAAO,QAAwC,CAAA,CAAC8B,CAAOkD,CAAAA,CAAAA,GAAO,CAC5D,GAAI,OAAOA,CAAO,EAAA,WAAA,CAAa,CAC7B,IAAK,CAAA,GAAA,CAAI,MAAOlD,CAAAA,CAAK,CACrB,CAAA,MACF,CACA,GAAI,CAAC,IAAK,CAAA,GAAA,CAAI,GAAIA,CAAAA,CAAK,EAAG,OAC1B,MAAM6E,CAAM,CAAA,IAAA,CAAK,IAAI7E,CAAK,CAAA,CAC1B,GAAI,CAAC,IAAK,CAAA,MAAA,CAAOA,CAAOkD,CAAAA,CAAE,EAAG,OAC7B,MAAM4B,CAAYD,CAAAA,CAAAA,CAAI,OAAQE,CAAMA,EAAAA,CAAAA,GAAM7B,CAAE,CAAA,CAC5C,GAAI,CAAC4B,CAAAA,CAAU,MAAQ,CAAA,CACrB,IAAK,CAAA,GAAA,CAAI,MAAO9E,CAAAA,CAAK,EACrB,MACF,CACA,IAAK,CAAA,GAAA,CAAI,IAAIA,CAAO8E,CAAAA,CAAS,EAC/B,CAAA,CAAA,CAEA5G,IAAA,IAAO,CAAA,OAAA,CAAsC,IAAM,CACjD,KAAK,GAAI,CAAA,KAAA,GACX,CAAA,EAAA,CAtCA,IAAI,MAAmB,EAAA,CACrB,OAAO,KAAA,CAAM,KAAK,IAAK,CAAA,GAAA,CAAI,IAAK,EAAC,CACnC,CAqCF;;AC5CA,IAAA,CAAA,CAAA,MAAA,CAAA,cAAA,CAAA,CAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,IAAAa,GAAA,CAAA,MAAA,CAAA,yBAAA,CAAA,IAAAiG,GAAA,CAAA,MAAA,CAAA,qBAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,cAAA,CAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAAC,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAAD,GAAA,CAAA,IAAA,IAAA,CAAA,IAAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAAE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAAnG,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA6F,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAiCO,MAAM,UAAmBO,SAAAA,iBAAY,CAkB1C,WAAA,CAAmB5C,CAA0BtE,CAAAA,CAAAA,CAAgB,CAC3D,KAAA,CAAMsE,CAAStE,CAAAA,CAAM,CADJ,CAAA,IAAA,CAAA,OAAA,CAAAsE,CAA0B,CAAA,IAAA,CAAA,MAAA,CAAAtE,CAjB7CC,CAAAA,GAAAA,CAAA,IAAO,CAAA,eAAA,CAAgB,IAAI,GAAA,CAAA,CAC3BA,GAAA,CAAA,IAAA,CAAO,UAAW,CAAA,IAAIkH,kBACtBlH,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,QAAA,CAAS,IAAIsE,gBAAAA,CAAAA,CACpBtE,GAAA,CAAA,IAAA,CAAO,MAAOmH,CAAAA,kBAAAA,CAAAA,CACdnH,GAAA,CAAA,IAAA,CAAO,SAAUoH,CAAAA,0BAAAA,CAAAA,CACjBpH,GAAA,CAAA,IAAA,CAAO,SAAU,CAAA,IAAI,GAErBA,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,QAAA,CAAmC,EAAC,CAAA,CAC5CA,GAAA,CAAA,IAAA,CAAQ,aAAc,CAAA,CAAA,CAAA,CAAA,CACtBA,GAAA,CAAA,IAAA,CAAQ,+BAAgC,CAAA,yBAAA,CAAA,CACxCA,GAAA,CAAA,IAAA,CAAQ,iBAAkB,CAAA,EAAA,CAAA,CAC1BA,GAAA,CAAA,IAAA,CAAQ,eAAgBG,CAAAA,mBAAAA,CAAAA,CACxBH,GAAA,CAAA,IAAA,CAAQ,kBAAmBwE,CAAAA,kBAAAA,CAAcC,eAAU,CAAA,CAAA,CACnDzE,GAAA,CAAA,IAAA,CAAQ,yBAA0BwE,CAAAA,kBAAAA,CAAcE,eAAa,CAAA,EAAE,CAC/D1E,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,UAAA,CAAA,CACRA,GAAA,CAAA,IAAA,CAAQ,2BAA4B,CAAA,GAAA,CAAA,CASpCA,GAAA,CAAA,IAAA,CAAO,MAA4B,CAAA,SAAY,CACxC,IAAA,CAAK,WACR,GAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,aAAa,CAAA,CAC/B,IAAK,CAAA,sBAAA,EACL,CAAA,MAAM,IAAK,CAAA,OAAA,EAEb,CAAA,CAAA,IAAA,CAAK,WAAc,CAAA,CAAA,EACrB,CAqCAA,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,WAAA,CAAsC,MAAO8B,CAAAA,CAAOI,CAAS,GAAA,CAClE,IAAK,CAAA,aAAA,EACL,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,mBAAmB,CAAA,CACrC,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,IAAM,CAAA,QAAA,CAAU,MAAQ,CAAA,WAAA,CAAa,MAAQ,CAAA,CAAE,KAAAJ,CAAAA,CAAAA,CAAO,IAAAI,CAAAA,CAAK,CAAE,CAAC,CAClF,CAAA,GAAI,CACF,MAAM2C,CAAQC,CAAAA,0BAAAA,CAAqB5C,CAAI,CAAA,CACjCC,CAAS,CAAA,CAAE,KAAAL,CAAAA,CAAAA,CAAO,KAAA+C,CAAAA,CAAAA,CAAO,aAAe3C,CAAAA,CAAAA,EAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,aAAc,CAAA,CAClE,IAAK,CAAA,OAAA,CAAQ,GAAIJ,CAAAA,CAAAA,CAAOK,CAAM,CAAA,CAC9B,MAAM6C,CAAAA,CAAK,MAAM,IAAA,CAAK,YAAalD,CAAAA,CAAAA,CAAO+C,CAAO3C,CAAAA,CAAI,CACrD,CAAA,OAAI,OAAO8C,CAAAA,EAAO,QAChB,GAAA,IAAA,CAAK,WAAYA,CAAAA,CAAAA,CAAI7C,CAAM,CAAA,CAC3B,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,+BAA+B,CACjD,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAA,CAAM,QAAU,CAAA,MAAA,CAAQ,WAAa,CAAA,MAAA,CAAQ,CAAE,KAAA,CAAAL,CAAO,CAAA,IAAA,CAAAI,CAAK,CAAE,CAAC,CAAA,CAAA,CAE7E8C,CACT,CAAA,MAASpB,CAAG,CAAA,CACV,MAAK,IAAA,CAAA,MAAA,CAAO,KAAM,CAAA,2BAA2B,CAC7C,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAMA,CAAQ,CAAA,CACpBA,CACR,CACF,CAEA5D,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,aAAA,CAA0C,MAAO8B,CAAAA,CAAOI,CAAS,GAAA,CACtE,IAAK,CAAA,aAAA,EACD,CAAA,OAAOA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAM,CAAA,EAAA,CAAA,EAAO,WACtB,CAAA,MAAM,IAAK,CAAA,eAAA,CAAgBJ,CAAOI,CAAAA,CAAAA,CAAK,EAAIA,CAAAA,CAAI,CAE/C,CAAA,MAAM,IAAK,CAAA,kBAAA,CAAmBJ,CAAOI,CAAAA,CAAI,EAE7C,CAAA,CAAA,CAEAlC,GAAA,CAAA,IAAA,CAAO,cAA4C,CAAA,MAAO8B,CAAkB,EAAA,CAE1E,GAAI,IAAA,CAAK,MAAO,CAAA,QAAA,CAASA,CAAK,CAAA,CAAG,OAAO,CAAA,CAAA,CACxC,MAAMuF,CAAAA,CAAQ,CAAG,EAAA,IAAA,CAAK,6BAA6B,CAAA,CAAA,EAAIvF,CAAK,CAAA,CAAA,CAqB5D,OAnBe,MAAM,IAAI,OAAA,CAAiB,CAACsD,CAAAA,CAASK,CAAW,GAAA,CAC7D,MAAM6B,CAAAA,CAAQ,IAAIC,UAAAA,CAClBD,CAAM,CAAA,KAAA,CAAMD,CAAK,CAAA,CACjB,MAAMG,CAAAA,CAAW,WAAY,CAAA,IAAM,CAE9B,CAAA,CAAC,IAAK,CAAA,OAAA,CAAQ,GAAI1F,CAAAA,CAAK,CAAK,EAAA,IAAA,CAAK,MAAO,CAAA,QAAA,CAASA,CAAK,CAAA,EACvD,IAAK,CAAA,MAAA,CAAO,IAAM2F,CAAAA,CAAAA,EAAMA,CAAE,CAAA,KAAA,GAAU3F,CAAK,CAAA,IAEzC,aAAc0F,CAAAA,CAAQ,CACtBF,CAAAA,CAAAA,CAAM,IAAKD,CAAAA,CAAK,CAChBjC,CAAAA,CAAAA,CAAQ,CAAI,CAAA,CAAA,CAAA,CAEVkC,CAAM,CAAA,OAAA,CAAQD,CAAK,CAAA,EAAKK,8BAC1B,GAAA,aAAA,CAAcF,CAAQ,CAAA,CACtBF,CAAM,CAAA,IAAA,CAAKD,CAAK,CAAA,CAChB5B,CAAO,CAAA,IAAI,KAAM,CAAA,iCAAiC,CAAC,CAAA,EAEvD,CAAG,CAAA,IAAA,CAAK,eAAe,EACzB,CAAC,CAAA,CAAE,KAAM,CAAA,IAAM,CAAK,CAAA,CAEtB,CAEAzF,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,IAAA,CAAwB,CAAC4F,CAAAA,CAAOC,CAAa,GAAA,CAClD,IAAK,CAAA,MAAA,CAAO,EAAGD,CAAAA,CAAAA,CAAOC,CAAQ,EAChC,CAEA7F,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,MAAA,CAA4B,CAAC4F,CAAAA,CAAOC,CAAa,GAAA,CACtD,IAAK,CAAA,MAAA,CAAO,IAAKD,CAAAA,CAAAA,CAAOC,CAAQ,EAClC,CAEA7F,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,KAAA,CAA0B,CAAC4F,CAAAA,CAAOC,CAAa,GAAA,CACpD,IAAK,CAAA,MAAA,CAAO,GAAID,CAAAA,CAAAA,CAAOC,CAAQ,EACjC,CAEA7F,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,gBAAA,CAAgD,CAAC4F,CAAAA,CAAOC,CAAa,GAAA,CAC1E,IAAK,CAAA,MAAA,CAAO,cAAeD,CAAAA,CAAAA,CAAOC,CAAQ,EAC5C,CAEA7F,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,OAAA,CAA8B,SAAY,CAC/C,MAAM,IAAA,CAAK,SAAU,GACvB,CAEAA,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,MAAA,CAA4B,SAAY,CAC7C,MAAM,IAAA,CAAK,YAAa,GAC1B,CAqRAA,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,SAAA,CAAU,SAAY,CAC5B,MAAM,IAAA,CAAK,OAAQ,EAAA,CACnB,MAAM,IAAA,CAAK,SAAU,GACvB,CAwEAA,CAAAA,CAAAA,GAAAA,CAAA,KAAQ,cAAe,CAAA,SAAY,CACjC,GAAI,IAAK,CAAA,OAAA,CAAQ,IAAS,GAAA,CAAA,GAAM,CAAC,IAAA,CAAK,WAAe,EAAA,CAAC,IAAK,CAAA,OAAA,CAAQ,SACjE,CAAA,CAAA,OAEF,MAAM2H,CAAAA,CAAiD,EAAC,CACxD,IAAK,CAAA,OAAA,CAAQ,OAASxF,CAAAA,CAAAA,EAAW,CAC/BwF,CAAAA,CAAqB,IAAKxF,CAAAA,CAAM,EAClC,CAAC,CAED,CAAA,MAAM,IAAK,CAAA,cAAA,CAAewF,CAAoB,EAChD,CAEA3H,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,wBAAA,CAAyB,IAAM,CACrC,IAAK,CAAA,OAAA,CAAQ,IAAK,CAAA,SAAA,CAAU,EAAGwG,CAAAA,0BAAAA,CAAiB,KAAO,CAAA,SAAY,CACjE,MAAM,IAAK,CAAA,YAAA,GACb,CAAC,CACD,CAAA,IAAA,CAAK,MAAO,CAAA,EAAA,CAAGoB,iBAAkB,CAAA,OAAA,CAAS,MAAOC,CAAAA,EAA2C,CAC1F,MAAMC,CAAYF,CAAAA,iBAAAA,CAAkB,OACpC,CAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAK,CAAYE,SAAAA,EAAAA,CAAS,CAAE,CAAA,CAAA,CACxC,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,IAAM,CAAA,OAAA,CAAS,KAAOA,CAAAA,CAAAA,CAAW,IAAMD,CAAAA,CAAa,CAAC,CAAA,CACzE,MAAM,IAAA,CAAK,OAAQ,GACrB,CAAC,CAAA,CACD,IAAK,CAAA,MAAA,CAAO,EAAGD,CAAAA,iBAAAA,CAAkB,OAAS,CAAA,MAAOG,CAA2C,EAAA,CAC1F,MAAMD,CAAAA,CAAYF,iBAAkB,CAAA,OAAA,CACpC,IAAK,CAAA,MAAA,CAAO,IAAK,CAAA,CAAA,SAAA,EAAYE,CAAS,CAAA,CAAE,CACxC,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAA,CAAM,OAAS,CAAA,KAAA,CAAOA,CAAW,CAAA,IAAA,CAAMC,CAAa,CAAC,CACzE,CAAA,MAAM,IAAK,CAAA,OAAA,GACb,CAAC,EACH,CAAA,CAAA,CA7fE,IAAK,CAAA,OAAA,CAAU1D,CACf,CAAA,IAAA,CAAK,MAAS5D,CAAAA,0BAAAA,CAAoBV,CAAQ,CAAA,IAAA,CAAK,IAAI,CAAA,CACnD,IAAK,CAAA,QAAA,CAAW,GAClB,CAWA,IAAI,OAAA,EAAU,CACZ,OAAOW,uBAAiB,CAAA,IAAA,CAAK,MAAM,CACrC,CAEA,IAAI,UAAa,EAAA,CACf,OACE,IAAA,CAAK,aAAgB,CAAA,IAAA,CAAK,OAAU,CAAA,IAAA,CAAK,OAAQ,CAAA,IAAA,CAAK,mBAAsB,CAAA,IAAA,CAAO,IAAK,CAAA,IAE5F,CAEA,IAAI,MAAS,EAAA,CACX,OAAO,IAAA,CAAK,aAAc,CAAA,IAC5B,CAEA,IAAI,GAAM,EAAA,CACR,OAAO,KAAA,CAAM,IAAK,CAAA,IAAA,CAAK,aAAc,CAAA,IAAA,EAAM,CAC7C,CAEA,IAAI,MAAS,EAAA,CACX,OAAO,KAAA,CAAM,IAAK,CAAA,IAAA,CAAK,aAAc,CAAA,MAAA,EAAQ,CAC/C,CAEA,IAAI,MAAS,EAAA,CACX,OAAO,IAAA,CAAK,QAAS,CAAA,MACvB,CAEA,IAAI,YAAe,EAAA,CACjB,OACE,IAAA,CAAK,QAAS,CAAA,MAAA,CAAO,MAAS,CAAA,CAAA,EAC9B,IAAK,CAAA,OAAA,CAAQ,IAAO,CAAA,CAAA,EACpB,IAAK,CAAA,MAAA,CAAO,MAAS,CAAA,CAAA,EACrB,IAAK,CAAA,aAAA,CAAc,IAAO,CAAA,CAE9B,CAsFQ,eAAA,CAAgBsE,CAAYlD,CAAAA,CAAAA,CAAe,CACjD,IAAIwE,CAAS,CAAA,CAAA,CAAA,CACb,GAAI,CAEFA,CADqB,CAAA,IAAA,CAAK,eAAgBtB,CAAAA,CAAE,CACtB,CAAA,KAAA,GAAUlD,EAClC,CAAA,MAAS8B,CAAG,CAAA,EAGZ,OAAO0C,CACT,CAEQ,KAAQ,EAAA,CACd,IAAK,CAAA,MAAA,CAAS,EAAC,CACf,IAAK,CAAA,WAAA,CAAc,CACrB,EAAA,CAEQ,SAAY,EAAA,CAClB,IAAK,CAAA,MAAA,CAAS,IAAK,CAAA,MAAA,CACnB,IAAK,CAAA,aAAA,CAAc,KAAM,EAAA,CACzB,IAAK,CAAA,QAAA,CAAS,KAAM,GACtB,CAEA,MAAc,kBAAmBxE,CAAAA,CAAAA,CAAeI,CAAwC,CAAA,CACtF,MAAMyE,CAAAA,CAAM,IAAK,CAAA,QAAA,CAAS,GAAI7E,CAAAA,CAAK,CACnC,CAAA,MAAM,OAAQ,CAAA,GAAA,CAAI6E,CAAI,CAAA,GAAA,CAAI,MAAO3B,CAAAA,EAAO,MAAM,IAAA,CAAK,eAAgBlD,CAAAA,CAAAA,CAAOkD,CAAI9C,CAAAA,CAAI,CAAC,CAAC,EACtF,CAEA,MAAc,eAAA,CAAgBJ,CAAekD,CAAAA,CAAAA,CAAY9C,CAAwC,CAAA,CAC/F,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,qBAAqB,CACvC,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAA,CAAM,QAAU,CAAA,MAAA,CAAQ,aAAe,CAAA,MAAA,CAAQ,CAAE,KAAA,CAAAJ,CAAO,CAAA,EAAA,CAAAkD,CAAI,CAAA,IAAA,CAAA9C,CAAK,CAAE,CAAC,CAAA,CAExF,GAAI,CACF,MAAM2C,CAAAA,CAAQC,0BAAqB5C,CAAAA,CAAI,CACvC,CAAA,MAAM,IAAK,CAAA,iBAAA,CAAkB,CAAE,KAAA,CAAAJ,CAAO,CAAA,EAAA,CAAAkD,CAAI,CAAA,KAAA,CAAAH,CAAM,CAAC,CACjD,CAAA,MAAM,IAAK,CAAA,cAAA,CAAe/C,CAAOkD,CAAAA,CAAAA,CAAIH,CAAK,CAAA,CAC1C,MAAMmD,CAAAA,CAASC,iBAAY,CAAA,mBAAA,CAAqB,CAAG,EAAA,IAAA,CAAK,IAAI,CAAA,EAAA,EAAKnG,CAAK,CAAA,CAAE,CACxE,CAAA,MAAM,IAAK,CAAA,aAAA,CAAcA,CAAOkD,CAAAA,CAAAA,CAAIgD,CAAM,CAAA,CAC1C,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,iCAAiC,CACnD,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAA,CAAM,QAAU,CAAA,MAAA,CAAQ,aAAe,CAAA,MAAA,CAAQ,CAAE,KAAA,CAAAlG,CAAO,CAAA,EAAA,CAAAkD,CAAI,CAAA,IAAA,CAAA9C,CAAK,CAAE,CAAC,EAC1F,CAAS0B,MAAAA,CAAAA,CAAG,CACV,MAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,6BAA6B,CAAA,CAC/C,IAAK,CAAA,MAAA,CAAO,KAAMA,CAAAA,CAAQ,CACpBA,CAAAA,CACR,CACF,CAEA,MAAc,YAAA,CACZ9B,CACA+C,CAAAA,CAAAA,CACA3C,CACA,CAAA,CA5OJ,IAAAyC,CAAAA,CAAAA,CA6OQ,CAACzC,CAAAA,EAAAA,CAAQA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAM,CAAA,aAAA,IAAkBgG,eAAgB,CAAA,KAAA,GACnD,MAAM,IAAA,CAAK,iBAAkB,CAAA,CAAE,KAAApG,CAAAA,CAAAA,CAAO,EAAIA,CAAAA,CAAAA,CAAO,KAAA+C,CAAAA,CAAM,CAAC,CAAA,CAG1D,MAAMsB,CAAAA,CAA0D,CAC9D,MAAA,CAFUC,yBAAoBvB,CAAAA,CAAAA,CAAM,QAAQ,CAAA,CAEhC,SACZ,CAAA,MAAA,CAAQ,CACN,KAAA,CAAA/C,CACF,CACF,CACA,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,wBAAwB,CAAA,CAC1C,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,IAAM,CAAA,SAAA,CAAW,SAAW,CAAA,UAAA,CAAY,OAAAqE,CAAAA,CAAQ,CAAC,CAAA,CACrE,MAAMgC,CAAAA,CAAAA,CAAcxD,CAAAzC,CAAAA,CAAAA,EAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,QAAN,GAAA,IAAA,CAAA,KAAA,CAAA,CAAAyC,CAAgB,CAAA,oBAAA,CACpC,GAAI,CACF,MAAMyD,CAAAA,CAAQ,MAAM,IAAA,CAAK,iBAAkBtG,CAAAA,CAAK,CAEhD,CAAA,GAAA,CAAII,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAM,CAAA,aAAA,IAAkBgG,eAAgB,CAAA,SAAA,CAC1C,OAAW,UAAA,CAAA,IAAM,EACX,IAAK,CAAA,OAAA,CAAQ,SAAa,EAAA,IAAA,CAAK,OAAQ,CAAA,UAAA,GACzC,IAAK,CAAA,OAAA,CAAQ,OAAQ/B,CAAAA,CAAO,CAAE,CAAA,KAAA,CAAOvC,CAAM,EAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAKA,CAAC,CAAC,EAElE,CAAA,CAAGY,kBAAcE,CAAAA,eAAU,CAAC,CAAA,CACrB0D,CAET,CAAA,MAAMC,CAAmB,CAAA,IAAI,OAAQ,CAAA,MAAOjD,CAAY,EAAA,CACtD,MAAMkD,CAAAA,CAAeC,CAA2C,EAAA,CAC1DA,CAAa,CAAA,KAAA,GAAUzG,CACzB,GAAA,IAAA,CAAK,MAAO,CAAA,cAAA,CAAe8F,iBAAkB,CAAA,OAAA,CAASU,CAAW,CAAA,CACjElD,CAAQmD,CAAAA,CAAAA,CAAa,EAAE,CAAA,EAE3B,CACA,CAAA,IAAA,CAAK,MAAO,CAAA,EAAA,CAAGX,iBAAkB,CAAA,OAAA,CAASU,CAAW,CAAA,CACrD,GAAI,CACF,MAAMhC,CAAAA,CAAS,MAAMd,2BAAAA,CACnB,IAAI,OAAA,CAAQ,CAACJ,CAAAA,CAASK,CAAW,GAAA,CAC/B,IAAK,CAAA,OAAA,CACF,OAAQU,CAAAA,CAAO,CACf,CAAA,KAAA,CAAOvC,CAAM,EAAA,CACZ,IAAK,CAAA,MAAA,CAAO,IAAKA,CAAAA,CAAAA,CAAGA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAG,CAAA,OAAO,CAC9B6B,CAAAA,CAAAA,CAAO7B,CAAC,EACV,CAAC,CAAA,CACA,IAAKwB,CAAAA,CAAO,EACjB,CAAC,CACD,CAAA,IAAA,CAAK,uBACL,CAAA,CAAA,eAAA,EAAkBtD,CAAK,CAAA,yBAAA,CACzB,CACA,CAAA,IAAA,CAAK,MAAO,CAAA,cAAA,CAAe8F,iBAAkB,CAAA,OAAA,CAASU,CAAW,CAAA,CACjElD,CAAQkB,CAAAA,CAAM,EAChB,CAAA,MAASkC,CAAK,CAAA,EAChB,CAAC,CAQKlC,CAAAA,CAAAA,CAAS,MANGd,2BAAAA,CAChB6C,CACA,CAAA,IAAA,CAAK,gBACL,CAAA,CAAA,eAAA,EAAkBvG,CAAK,CAAA,yBAAA,CACzB,CAGA,CAAA,GAAI,CAACwE,CAAAA,EAAU6B,CACb,CAAA,MAAM,IAAI,KAAA,CAAM,CAAkBrG,eAAAA,EAAAA,CAAK,CAA2B,yBAAA,CAAA,CAAA,CAGpE,OAAOwE,CAAAA,CAAS8B,CAAQ,CAAA,IAC1B,CAASI,MAAAA,CAAAA,CAAK,CAGZ,GAFA,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,0CAA0C,CAC5D,CAAA,IAAA,CAAK,OAAQ,CAAA,MAAA,CAAO,IAAKlD,CAAAA,cAAAA,CAAe,kBAAkB,CAAA,CACtD6C,CACF,CAAA,MAAMK,CAEV,CACA,OAAO,IACT,CAEA,MAAc,iBAAkBC,CAAAA,CAAAA,CAAyC,CACvE,GAAI,CAACA,CAAAA,CAAc,MAAQ,CAAA,OAC3B,MAAM5D,CAAAA,CAAQ4D,CAAc,CAAA,CAAC,CAAE,CAAA,KAAA,CAEzBtC,CAA+D,CAAA,CACnE,MAFUC,CAAAA,yBAAAA,CAAoBvB,CAAO,CAAA,QAAQ,CAEjC,CAAA,cAAA,CACZ,MAAQ,CAAA,CACN,MAAQ4D,CAAAA,CAAAA,CAAc,GAAKhB,CAAAA,CAAAA,EAAMA,CAAE,CAAA,KAAK,CAC1C,CACF,CACA,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,wBAAwB,CAAA,CAC1C,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,IAAM,CAAA,SAAA,CAAW,SAAW,CAAA,UAAA,CAAY,OAAAtB,CAAAA,CAAQ,CAAC,CAAA,CACrE,GAAI,CAWF,MAVkB,MAAMX,2BACtB,CAAA,IAAI,OAASJ,CAAAA,CAAAA,EAAY,CACvB,IAAA,CAAK,OACF,CAAA,OAAA,CAAQe,CAAO,CAAA,CACf,KAAOvC,CAAAA,CAAAA,EAAM,IAAK,CAAA,MAAA,CAAO,IAAKA,CAAAA,CAAC,CAAC,CAAA,CAChC,IAAKwB,CAAAA,CAAO,EACjB,CAAC,CACD,CAAA,IAAA,CAAK,gBACL,CAAA,4CACF,EAEF,CAAA,MAASoD,CAAK,CAAA,CACZ,IAAK,CAAA,OAAA,CAAQ,MAAO,CAAA,IAAA,CAAKlD,cAAe,CAAA,kBAAkB,EAC5D,CACF,CAEA,MAAc,qBAAsBmD,CAAAA,CAAAA,CAAyC,CAC3E,GAAI,CAACA,CAAAA,CAAc,MAAQ,CAAA,OAC3B,MAAM5D,CAAAA,CAAQ4D,CAAc,CAAA,CAAC,CAAE,CAAA,KAAA,CAEzBtC,CAAmE,CAAA,CACvE,MAFUC,CAAAA,yBAAAA,CAAoBvB,CAAO,CAAA,QAAQ,CAEjC,CAAA,kBAAA,CACZ,MAAQ,CAAA,CACN,MAAQ4D,CAAAA,CAAAA,CAAc,GAAKhB,CAAAA,CAAAA,EAAMA,CAAE,CAAA,KAAK,CAC1C,CACF,CACA,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,wBAAwB,CAAA,CAC1C,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,IAAM,CAAA,SAAA,CAAW,SAAW,CAAA,UAAA,CAAY,OAAAtB,CAAAA,CAAQ,CAAC,CAAA,CACrE,IAAIG,CAAAA,CACJ,GAAI,CAcFA,CAAU,CAAA,MAbmB,MAAMd,2BAAAA,CACjC,IAAI,OAAA,CAAQ,CAACJ,CAAAA,CAASK,CAAW,GAAA,CAC/B,IAAK,CAAA,OAAA,CACF,OAAQU,CAAAA,CAAO,CACf,CAAA,KAAA,CAAOvC,CAAM,EAAA,CACZ,IAAK,CAAA,MAAA,CAAO,IAAKA,CAAAA,CAAC,CAClB6B,CAAAA,CAAAA,CAAO7B,CAAC,EACV,CAAC,CAAA,CACA,IAAKwB,CAAAA,CAAO,EACjB,CAAC,CACD,CAAA,IAAA,CAAK,gBACL,CAAA,gDACF,EAIF,CAAA,MAASoD,CAAK,CAAA,CACZ,IAAK,CAAA,OAAA,CAAQ,MAAO,CAAA,IAAA,CAAKlD,cAAe,CAAA,kBAAkB,EAC5D,CACA,OAAOgB,CACT,CAEQ,cAAA,CAAexE,CAAekD,CAAAA,CAAAA,CAAYH,CAAqC,CAAA,CAErF,MAAMsB,CAAAA,CAA4D,CAChE,MAAA,CAFUC,yBAAoBvB,CAAAA,CAAAA,CAAM,QAAQ,CAAA,CAEhC,WACZ,CAAA,MAAA,CAAQ,CACN,KAAA,CAAA/C,CACA,CAAA,EAAA,CAAAkD,CACF,CACF,CACA,CAAA,OAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,wBAAwB,CAAA,CAC1C,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,IAAM,CAAA,SAAA,CAAW,SAAW,CAAA,UAAA,CAAY,OAAAmB,CAAAA,CAAQ,CAAC,CAAA,CAC9D,IAAK,CAAA,OAAA,CAAQ,OAAQA,CAAAA,CAAO,CACrC,CAEQ,WAAYnB,CAAAA,CAAAA,CAAY7C,CAAgC,CAAA,CAC9D,IAAK,CAAA,eAAA,CAAgB6C,CAAIU,CAAAA,GAAAA,CAAAC,GAAA,CAAA,EAAA,CAAKxD,CAAL,CAAA,CAAA,CAAa,EAAA6C,CAAAA,CAAG,CAAC,CAAA,CAAA,CAC1C,IAAK,CAAA,OAAA,CAAQ,MAAO7C,CAAAA,CAAAA,CAAO,KAAK,EAClC,CAEQ,gBAAA,CAAiBsG,CAAyC,CAAA,CAC3DA,CAAc,CAAA,MAAA,EACnBA,CAAc,CAAA,OAAA,CAASF,CAAiB,EAAA,CACtC,IAAK,CAAA,eAAA,CAAgBA,CAAa,CAAA,EAAA,CAAI5C,GAAA,CAAA,EAAA,CAAK4C,CAAc,CAAA,CAAA,CACzD,IAAK,CAAA,OAAA,CAAQ,MAAOA,CAAAA,CAAAA,CAAa,KAAK,EACxC,CAAC,EACH,CAEA,MAAc,aAAczG,CAAAA,CAAAA,CAAekD,CAAYgD,CAAAA,CAAAA,CAAuB,CAC5E,IAAA,CAAK,MAAO,CAAA,kBAAA,CAAmBhD,CAAE,CAAA,CAC7B,IAAK,CAAA,eAAA,CAAgBA,CAAIlD,CAAAA,CAAK,CAChC,EAAA,IAAA,CAAK,kBAAmBkD,CAAAA,CAAAA,CAAIgD,CAAM,CAAA,CAEpC,MAAM,IAAA,CAAK,OAAQ,CAAA,QAAA,CAAS,GAAIlG,CAAAA,CAAK,EACvC,CAEA,MAAc,uBAAA,CAAwB2G,CAAyC,CAAA,CAC7E,MAAM,IAAA,CAAK,OAAQ,CAAA,IAAA,CAAK,OAAQ,CAAA,OAAA,CAC9B,IAAK,CAAA,UAAA,CACLA,CACF,EACF,CAEA,MAAc,uBAA0B,EAAA,CAItC,OAHsB,MAAM,IAAK,CAAA,OAAA,CAAQ,KAAK,OAAQ,CAAA,OAAA,CACpD,IAAK,CAAA,UACP,CAEF,CAEQ,eAAgBzD,CAAAA,CAAAA,CAAYuD,CAAsC,CAAA,CACxE,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,sBAAsB,CACxC,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAA,CAAM,QAAU,CAAA,MAAA,CAAQ,iBAAmB,CAAA,EAAA,CAAAvD,CAAI,CAAA,YAAA,CAAAuD,CAAa,CAAC,CACjF,CAAA,IAAA,CAAK,eAAgBvD,CAAAA,CAAAA,CAAIuD,CAAY,EACvC,CAEQ,eAAA,CAAgBvD,CAAYuD,CAAAA,CAAAA,CAAsC,CACxE,IAAA,CAAK,aAAc,CAAA,GAAA,CAAIvD,CAAIW,CAAAA,GAAAA,CAAA,EAAK4C,CAAAA,CAAAA,CAAc,CAC9C,CAAA,IAAA,CAAK,QAAS,CAAA,GAAA,CAAIA,CAAa,CAAA,KAAA,CAAOvD,CAAE,CAAA,CACxC,IAAK,CAAA,MAAA,CAAO,IAAK4C,CAAAA,iBAAAA,CAAkB,OAASW,CAAAA,CAAY,EAC1D,CAEQ,eAAgBvD,CAAAA,CAAAA,CAAY,CAClC,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,sBAAsB,CAAA,CACxC,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,IAAM,CAAA,QAAA,CAAU,MAAQ,CAAA,iBAAA,CAAmB,EAAAA,CAAAA,CAAG,CAAC,CAAA,CACnE,MAAMuD,CAAAA,CAAe,IAAK,CAAA,aAAA,CAAc,GAAIvD,CAAAA,CAAE,CAC9C,CAAA,GAAI,CAACuD,CAAAA,CAAc,CACjB,KAAM,CAAE,OAAA,CAAAhI,CAAQ,CAAA,CAAIC,sBAAiB,CAAA,iBAAA,CAAmB,CAAG,EAAA,IAAA,CAAK,IAAI,CAAA,EAAA,EAAKwE,CAAE,CAAA,CAAE,CAC7E,CAAA,MAAM,IAAI,KAAA,CAAMzE,CAAO,CACzB,CACA,OAAOgI,CACT,CAEQ,kBAAmBvD,CAAAA,CAAAA,CAAYgD,CAAuB,CAAA,CAC5D,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,uBAAuB,CACzC,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAA,CAAM,QAAU,CAAA,MAAA,CAAQ,oBAAsB,CAAA,EAAA,CAAAhD,CAAI,CAAA,MAAA,CAAAgD,CAAO,CAAC,CAC9E,CAAA,MAAMO,CAAe,CAAA,IAAA,CAAK,eAAgBvD,CAAAA,CAAE,CAC5C,CAAA,IAAA,CAAK,aAAc,CAAA,MAAA,CAAOA,CAAE,CAAA,CAC5B,IAAK,CAAA,QAAA,CAAS,MAAOuD,CAAAA,CAAAA,CAAa,KAAOvD,CAAAA,CAAE,CAC3C,CAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAK4C,iBAAkB,CAAA,OAAA,CAASlC,GAAAC,CAAAA,GAAAA,CAAA,EACvC4C,CAAAA,CAAAA,CAAAA,CADuC,CAE1C,MAAA,CAAAP,CACF,CAAA,CAA6B,EAC/B,CAOA,MAAc,OAAA,EAAU,CACtB,MAAM,IAAK,CAAA,uBAAA,CAAwB,IAAK,CAAA,MAAM,CAC9C,CAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAKJ,iBAAkB,CAAA,IAAI,EACzC,CAEA,MAAc,SAAA,EAAY,CACxB,GAAI,IAAK,CAAA,MAAA,CAAO,MAAQ,CAAA,CACtB,MAAMc,CAAAA,CAAO,CAAC,GAAG,IAAK,CAAA,MAAM,CACtBC,CAAAA,CAAAA,CAAe,IAAK,CAAA,IAAA,CAAK,IAAK,CAAA,MAAA,CAAO,MAAS,CAAA,IAAA,CAAK,yBAAyB,CAAA,CAClF,IAASjC,IAAAA,CAAAA,CAAI,CAAGA,CAAAA,CAAAA,CAAIiC,CAAcjC,CAAAA,CAAAA,EAAAA,CAAK,CACrC,MAAMkC,CAAQF,CAAAA,CAAAA,CAAK,MAAO,CAAA,CAAA,CAAG,IAAK,CAAA,yBAAyB,CAC3D,CAAA,MAAM,IAAK,CAAA,cAAA,CAAeE,CAAK,EACjC,CACF,CACA,IAAK,CAAA,MAAA,CAAO,IAAKhB,CAAAA,iBAAAA,CAAkB,YAAY,EACjD,CAEA,MAAc,OAAU,EAAA,CACtB,GAAI,CACF,MAAMiB,CAAAA,CAAY,MAAM,IAAA,CAAK,uBAAwB,EAAA,CAErD,GADI,OAAOA,CAAc,EAAA,WAAA,EACrB,CAACA,CAAAA,CAAU,MAAQ,CAAA,OACvB,GAAI,IAAA,CAAK,aAAc,CAAA,IAAA,CAAM,CAC3B,KAAM,CAAE,OAAA,CAAAtI,CAAQ,CAAA,CAAIC,sBAAiB,CAAA,uBAAA,CAAyB,IAAK,CAAA,IAAI,CACvE,CAAA,MAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAMD,CAAO,CAAA,CACzB,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAA,EAAG,IAAK,CAAA,IAAI,CAAK,EAAA,EAAA,IAAA,CAAK,SAAU,CAAA,IAAA,CAAK,MAAM,CAAC,CAAE,CAAA,CAAA,CAC1D,IAAI,KAAA,CAAMA,CAAO,CACzB,CACA,IAAA,CAAK,MAASsI,CAAAA,CAAAA,CACd,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAA,wCAAA,EAA2C,IAAK,CAAA,IAAI,CAAE,CAAA,CAAA,CACxE,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,IAAM,CAAA,QAAA,CAAU,MAAQ,CAAA,SAAA,CAAW,aAAe,CAAA,IAAA,CAAK,MAAO,CAAC,EACrF,CAAA,MAAS,CAAG,CAAA,CACV,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAA,oCAAA,EAAuC,IAAK,CAAA,IAAI,CAAE,CAAA,CAAA,CACpE,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAQ,EAC5B,CACF,CAEA,MAAc,cAAeJ,CAAAA,CAAAA,CAAyC,CAC/DA,CAAAA,CAAc,MAEnB,GAAA,MAAM,IAAK,CAAA,iBAAA,CAAkBA,CAAa,CAAA,CAC1C,IAAK,CAAA,gBAAA,CACH,MAAM,OAAA,CAAQ,GACZA,CAAAA,CAAAA,CAAc,GAAI,CAAA,MAAOhB,CAChB/B,EAAAA,GAAAA,CAAAC,GAAA,CAAA,EAAA,CAAK8B,CAAL,CAAA,CAAA,CAAQ,EAAI,CAAA,MAAM,IAAK,CAAA,iBAAA,CAAkBA,CAAE,CAAA,KAAK,CAAE,CAAA,CAC1D,CACH,CACF,CACF,EAAA,CAGA,MAAc,kBAAA,CAAmBgB,CAAyC,CAAA,CACxE,GAAI,CAACA,CAAc,CAAA,MAAA,CAAQ,OAC3B,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAA+BA,4BAAAA,EAAAA,CAAAA,CAAc,MAAM,CAAA,cAAA,CAAgB,CACrF,CAAA,MAAMK,CAAW,CAAA,MAAM,IAAK,CAAA,qBAAA,CAAsBL,CAAa,CAAA,CAC3DK,CAAYA,EAAAA,CAAAA,CAAS,QACvB,GAAA,MAAMC,WAAMvE,CAAAA,kBAAAA,CAAcE,eAAU,CAAC,CACrC,CAAA,MAAM,IAAK,CAAA,OAAA,CAAQ,wBAAyBoE,CAAAA,CAAAA,CAAS,QAAQ,CAAA,EAEjE,CAEA,MAAc,SAAY,EAAA,CACxB,MAAM,IAAA,CAAK,OAAQ,EAAA,CACnB,IAAK,CAAA,KAAA,GACP,CAEQ,YAAe,EAAA,CACrB,IAAK,CAAA,SAAA,GACP,CAgCQ,aAAgB,EAAA,CACtB,GAAI,CAAC,IAAK,CAAA,WAAA,CAAa,CACrB,KAAM,CAAE,OAAA,CAAAvI,CAAQ,CAAA,CAAIC,sBAAiB,CAAA,iBAAA,CAAmB,IAAK,CAAA,IAAI,CACjE,CAAA,MAAM,IAAI,KAAA,CAAMD,CAAO,CACzB,CACF,CAEA,MAAc,iBAAA,CAAkBgI,CAAsC,CAAA,CAChE,CAAC,IAAA,CAAK,OAAQ,CAAA,SAAA,EAAa,CAAC,IAAA,CAAK,OAAQ,CAAA,UAAA,GAC3C,IAAK,CAAA,MAAA,CAAO,IAAKA,CAAAA,CAAY,CAC7B,CAAA,MAAM,IAAK,CAAA,OAAA,CAAQ,aAAc,EAAA,EAErC,CAEA,MAAc,WAAc,EAAA,CAC1B,OAAK,IAAA,CAAK,QACR,GAAA,IAAA,CAAK,QAAW,CAAA,MAAM,IAAK,CAAA,OAAA,CAAQ,IAAK,CAAA,MAAA,CAAO,WAAY,EAAA,CAAA,CAEtD,IAAK,CAAA,QACd,CAEA,MAAc,iBAAkBzG,CAAAA,CAAAA,CAAe,CAC7C,OAAOqC,iBAAYrC,CAAAA,CAAAA,CAAS,MAAM,IAAA,CAAK,WAAY,EAAE,CACvD,CACF;;2ZCzgBa,MAAA,OAAA,SAAgBkH,cAAS,CAiCpC,WAAY9G,CAAAA,CAAAA,CAAsB,CAChC,KAAA,CAAMA,CAAI,CAAA,CAjCZlC,GAAA,CAAA,IAAA,CAAO,UAAW,CAAA,IAAA,CAAA,CAClBA,GAAA,CAAA,IAAA,CAAO,SAAU,CAAA,CAAA,CAAA,CAEjBA,GAAA,CAAA,IAAA,CAAO,QACPA,GAAA,CAAA,IAAA,CAAO,QACPA,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,QAAA,CAAS,IAAIsE,gBAAAA,CAAAA,CACpBtE,GAAA,CAAA,IAAA,CAAO,UACPA,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,UAAA,CAAA,CACPA,GAAA,CAAA,IAAA,CAAO,YACPA,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,WAAA,CAAA,CACPA,GAAA,CAAA,IAAA,CAAO,MAAOiJ,CAAAA,eAAAA,CAAAA,CACdjJ,GAAA,CAAA,IAAA,CAAO,2BAA4B,CAAA,CAAA,CAAA,CAAA,CAEnCA,GAAA,CAAA,IAAA,CAAQ,cAAc,CACtBA,CAAAA,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,6BAAA,CAA8B,CAEtCA,CAAAA,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,UAAA,CAAA,CACRA,GAAA,CAAA,IAAA,CAAQ,WACRA,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,aAAA,CAAA,CACRA,GAAA,CAAA,IAAA,CAAQ,UACRA,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,iCAAA,CAAkC,CAC1CA,CAAAA,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,aAAA,CAAA,CAKRA,GAAA,CAAA,IAAA,CAAQ,kBAAmBwE,CAAAA,kBAAAA,CAAcnG,mBAAiBD,CAAAA,iBAAY,GACtE4B,GAAA,CAAA,IAAA,CAAQ,kBACRA,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,gBAAA,CAAA,CACRA,GAAA,CAAA,IAAA,CAAQ,qBAAsB,CAAA,CAAA,CAAA,CAAA,CAC9BA,GAAA,CAAA,IAAA,CAAQ,kBAA6B,CAAA,EACrCA,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,gBAAA,CAAiBwE,kBAAcE,CAAAA,eAAAA,CAAa,EAAE,CAAA,CAAA,CAkHtD1E,GAAA,CAAA,IAAA,CAAO,SAAU,CAAA,MAAOmG,CAA4D,EAAA,CArNtF,IAAAxB,CAAAA,CAAAmB,CAsNI,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,4BAA4B,CAAA,CAC9C,MAAMd,CAAAA,CAAKmB,CAAQ,CAAA,EAAA,EAAOlB,2BAAe,EAAA,CAAE,QAAS,EAAA,CACpD,MAAM,IAAA,CAAK,qBAAsB,EAAA,CACjC,GAAI,CACF,IAAK,CAAA,MAAA,CAAO,KACV,CAAA,CACE,EAAAD,CAAAA,CAAAA,CACA,MAAQmB,CAAAA,CAAAA,CAAQ,MAChB,CAAA,KAAA,CAAA,CAAOxB,CAAAwB,CAAAA,CAAAA,CAAQ,SAAR,IAAAxB,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAgB,KACzB,CAAA,CACA,iCACF,CAAA,CACA,MAAMtE,CAAAA,CAAM,CAAG2E,EAAAA,CAAE,CAAKc,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,CAAAK,CAAQ,CAAA,MAAA,GAAR,IAAAL,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAwB,GAAO,GAAA,EAAE,CACvD,CAAA,CAAA,IAAA,CAAK,gBAAiB,CAAA,IAAA,CAAKzF,CAAG,CAAA,CAC9B,MAAMiG,CAAAA,CAAS,MAAM,IAAA,CAAK,QAAS,CAAA,OAAA,CAAQH,CAAO,CAClD,CAAA,OAAA,IAAA,CAAK,gBAAmB,CAAA,IAAA,CAAK,gBAAiB,CAAA,MAAA,CAAQO,CAAMA,EAAAA,CAAAA,GAAMrG,CAAG,CAAA,CAC9DiG,CACT,CAAA,MAAS1C,CAAG,CAAA,CACV,MAAK,IAAA,CAAA,MAAA,CAAO,KAAM,CAAA,CAAA,2BAAA,EAA8BoB,CAAE,CAAA,CAAE,CAC9CpB,CAAAA,CACR,CACF,CAAA,CAAA,CAqNA5D,GAAA,CAAA,IAAA,CAAQ,kBAAmB,CAAA,IAAM,CAC/B,GAAKkJ,cACL,CAAA,GAAI,CACF,YAAA,CAAa,IAAK,CAAA,WAAW,CAC7B,CAAA,IAAA,CAAK,WAAc,CAAA,UAAA,CAAW,IAAM,CApc1C,IAAAvE,CAAAA,CAAAmB,CAAAC,CAAAA,CAAAA,CAqcQ,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,EAAI,CAAA,iDAAiD,CAEvEA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAD,CAAAnB,CAAAA,CAAAA,CAAAA,CAAA,IAAK,CAAA,QAAA,GAAL,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAe,aAAf,IAAAmB,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAA2B,MAA3B,GAAA,IAAA,EAAAC,CAAmC,CAAA,SAAA,GACrC,CAAG,CAAA,IAAA,CAAK,gBAAgB,EAC1B,CAAS,MAAA,CAAA,CAAG,CACV,IAAA,CAAK,MAAO,CAAA,IAAA,CAAK,CAAI,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAa,OAAO,EAC3C,CACF,CAAA,CAAA,CA8FA/F,GAAA,CAAA,IAAA,CAAQ,kBAAoBiC,CAAAA,CAAAA,EAA4B,CACtD,IAAA,CAAK,iBAAkBA,CAAAA,CAAO,EAC9B,IAAK,CAAA,gBAAA,GACP,CAAA,CAAA,CAEAjC,GAAA,CAAA,IAAA,CAAQ,kBAAmB,CAAA,IAAM,CAC/B,IAAA,CAAK,MAAO,CAAA,IAAA,CAAK,EAAC,CAAG,6BAAsB,CAAA,CAC3C,IAAK,CAAA,gBAAA,EACL,CAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAKsF,cAAe,CAAA,OAAO,EACzC,CAAA,CAAA,CAEAtF,GAAA,CAAA,IAAA,CAAQ,qBAAsB,CAAA,IAAM,CAClC,IAAK,CAAA,MAAA,CAAO,IAAK,CAAA,EAAI,CAAA,gCAAyB,CAC9C,CAAA,IAAA,CAAK,gBAAmB,CAAA,EACxB,CAAA,IAAA,CAAK,oBAAqB,GAC5B,CAEAA,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,wBAAA,CAA0BiD,CAAiB,EAAA,CACjD,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAA,oBAAA,EAAuBA,CAAM,CAAA,OAAO,CAAE,CAAA,CAAA,CACxD,IAAK,CAAA,MAAA,CAAO,KAAKqC,cAAe,CAAA,KAAA,CAAOrC,CAAK,CAAA,CAG5C,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,gDAAgD,CAClE,CAAA,IAAA,CAAK,cAAe,GACtB,CAEAjD,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,2BAAA,CAA4B,IAAM,CACxC,IAAK,CAAA,QAAA,CAAS,EAAGmJ,CAAAA,uBAAAA,CAAwB,OAAS,CAAA,IAAA,CAAK,gBAAgB,CAAA,CACvE,IAAK,CAAA,QAAA,CAAS,EAAGA,CAAAA,uBAAAA,CAAwB,OAAS,CAAA,IAAA,CAAK,gBAAgB,CAAA,CACvE,IAAK,CAAA,QAAA,CAAS,EAAGA,CAAAA,uBAAAA,CAAwB,UAAY,CAAA,IAAA,CAAK,mBAAmB,CAAA,CAC7E,IAAK,CAAA,QAAA,CAAS,EAAGA,CAAAA,uBAAAA,CAAwB,KAAO,CAAA,IAAA,CAAK,sBAAsB,EAC7E,CAneE,CAAA,CAAA,IAAA,CAAK,IAAOjH,CAAAA,CAAAA,CAAK,IACjB,CAAA,IAAA,CAAK,MACH,CAAA,OAAOA,CAAK,CAAA,MAAA,EAAW,WAAe,EAAA,OAAOA,EAAK,MAAW,EAAA,QAAA,CACzDzB,0BAAoByB,CAAAA,CAAAA,CAAK,MAAQ,CAAA,IAAA,CAAK,IAAI,CAAA,CAC1CkH,WAAKC,CAAAA,8BAAAA,CAAwB,CAAE,KAAA,CAAOnH,CAAK,CAAA,MAAA,EAAUoH,sBAAuB,CAAC,CAAC,CAAA,CACpF,IAAK,CAAA,QAAA,CAAW,IAAIC,cAAAA,CAAe,IAAK,CAAA,MAAA,CAAQrH,CAAK,CAAA,IAAI,CACzD,CAAA,IAAA,CAAK,UAAa,CAAA,IAAIsH,WAAW,IAAM,CAAA,IAAA,CAAK,MAAM,CAAA,CAClD,IAAK,CAAA,SAAA,CAAY,IAAIC,SAAAA,CAAU,IAAM,CAAA,IAAA,CAAK,MAAM,CAAA,CAEhD,IAAK,CAAA,QAAA,CAAA,CAAWvH,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAM,CAAA,QAAA,GAAYwH,yBAClC,CAAA,IAAA,CAAK,SAAYxH,CAAAA,CAAAA,CAAK,SAElByH,CAAAA,eAAAA,EACF,CAAA,IAAA,CAAK,WAAcC,CAAAA,cAAAA,EACVC,CAAAA,WAAAA,KACT,IAAK,CAAA,QAAA,CAAWD,cAAS,EAAA,CAAA,CAI3B,IAAK,CAAA,QAAA,CAAW,GAClB,CAEA,MAAa,IAAO,EAAA,CAKlB,GAJA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,aAAa,CAAA,CAC/B,IAAK,CAAA,sBAAA,EACL,CAAA,MAAM,OAAQ,CAAA,GAAA,CAAI,CAAC,IAAA,CAAK,QAAS,CAAA,IAAA,EAAQ,CAAA,IAAA,CAAK,WAAW,IAAK,EAAC,CAAC,CAAA,CAChE,IAAK,CAAA,WAAA,CAAc,CACf,CAAA,CAAA,IAAA,CAAK,UAAW,CAAA,YAAA,CAClB,GAAI,CACF,MAAM,IAAA,CAAK,aAAc,GAC3B,CAAS,MAAA,CAAA,CAAG,CACV,IAAA,CAAK,MAAO,CAAA,IAAA,CAAK,CAAI,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAa,OAAO,EAC3C,CAEJ,CAEA,IAAI,OAAA,EAAU,CACZ,OAAOlJ,uBAAAA,CAAiB,IAAK,CAAA,MAAM,CACrC,CAEA,IAAI,SAAA,EAAY,CA9IlB,IAAAiE,CAAAmB,CAAAA,CAAAA,CAAAC,CAgJI,CAAA,OAAA,CAAA,CAAOA,CAAAD,CAAAA,CAAAA,CAAAA,CAAAA,CAAAnB,CAAA,CAAA,IAAA,CAAK,QAAL,GAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAe,CAAA,UAAA,GAAf,IAAAmB,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAA2B,MAA3B,GAAA,IAAA,CAAA,KAAA,CAAA,CAAAC,CAAmC,CAAA,UAAA,IAAe,CAAK,EAAA,CAAA,CAChE,CAEA,IAAI,UAAA,EAAa,CAnJnB,IAAApB,CAAAmB,CAAAA,CAAAA,CAAAC,CAoJI,CAAA,OAAA,CAAA,CAEEA,CAAAD,CAAAA,CAAAA,CAAAA,CAAAA,CAAAnB,CAAA,CAAA,IAAA,CAAK,QAAL,GAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAe,CAAA,UAAA,GAAf,IAAAmB,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAA2B,MAA3B,GAAA,IAAA,CAAA,KAAA,CAAA,CAAAC,CAAmC,CAAA,UAAA,IAAe,CAClD,EAAA,IAAA,CAAK,cAAmB,GAAA,KAAA,CAAA,EACxB,CAEJ,CAAA,CAEA,MAAa,OAAA,CAAQjE,EAAevB,CAAiB2B,CAAAA,CAAAA,CAAoC,CACvF,IAAA,CAAK,aAAc,EAAA,CACnB,MAAM,IAAA,CAAK,SAAU,CAAA,OAAA,CAAQJ,CAAOvB,CAAAA,CAAAA,CAAS2B,CAAI,CAAA,CACjD,MAAM,IAAA,CAAK,kBAAmB,CAAA,CAC5B,KAAAJ,CAAAA,CAAAA,CACA,OAAAvB,CAAAA,CAAAA,CAEA,WAAa,CAAA,IAAA,CAAK,GAAI,EAAA,CACtB,aAAe2H,CAAAA,eAAAA,CAAgB,KACjC,CAAC,EACH,CAEA,MAAa,SAAA,CAAUpG,CAAeI,CAAAA,CAAAA,CAAsC,CAxK9E,IAAAyC,CAAAmB,CAAAA,CAAAA,CAAAC,CAyKI,CAAA,IAAA,CAAK,aAAc,EAAA,CAAA,CACf,EAAC7D,CAAAA,EAAA,IAAAA,EAAAA,CAAAA,CAAM,aAAiBA,CAAAA,EAAAA,CAAAA,CAAAA,EAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,aAAkB,IAAA,OAAA,GAClD,MAAM,IAAA,CAAK,qBAAsB,EAAA,CAGnC,MAAM4H,CAAAA,CACJ,OAAOnF,CAAAA,CAAAA,CAAAzC,GAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,QAAN,GAAA,IAAA,CAAA,KAAA,CAAA,CAAAyC,CAAgB,CAAA,oBAAA,CAAA,EAAyB,WAC5C,CAAA,CAAA,CAAA,CAAA,CACAmB,CAAA5D,CAAAA,CAAAA,EAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,QAAN,GAAA,IAAA,CAAA,KAAA,CAAA,CAAA4D,CAAgB,CAAA,oBAAA,CAEtB,IAAId,CAAAA,CAAAA,CAAAA,CAAKe,CAAA,CAAA,IAAA,CAAK,UAAW,CAAA,QAAA,CAAS,GAAIjE,CAAAA,CAAK,CAAlC,GAAA,IAAA,CAAA,KAAA,CAAA,CAAAiE,CAAsC,CAAA,CAAA,CAAA,GAAM,EACjDgE,CAAAA,CAAAA,CACJ,MAAMC,CAAgBzB,CAAAA,CAAAA,EAAyC,CACzDA,CAAAA,CAAa,KAAUzG,GAAAA,CAAAA,GACzB,IAAK,CAAA,UAAA,CAAW,GAAI8F,CAAAA,iBAAAA,CAAkB,OAASoC,CAAAA,CAAY,CAC3DD,CAAAA,CAAAA,EAEJ,EAAA,CAAA,CAEA,OAAM,MAAA,OAAA,CAAQ,GAAI,CAAA,CAChB,IAAI,OAAA,CAAe3E,CAAY,EAAA,CAC7B2E,CAAiB3E,CAAAA,CAAAA,CACjB,IAAK,CAAA,UAAA,CAAW,EAAGwC,CAAAA,iBAAAA,CAAkB,QAASoC,CAAY,EAC5D,CAAC,CAAA,CACD,IAAI,OAAA,CAAc,MAAO5E,CAAAA,CAASK,CAAW,GAAA,CAa3CT,CAZe,CAAA,MAAM,IAAK,CAAA,UAAA,CACvB,SAAUlD,CAAAA,CAAAA,CAAO6D,GAAA,CAAA,CAChB,QAAU,CAAA,CACR,oBAAsBmE,CAAAA,CACxB,CACG5H,CAAAA,CAAAA,CAAAA,CACJ,CACA,CAAA,KAAA,CAAOe,CAAU,EAAA,CACZ6G,CACFrE,EAAAA,CAAAA,CAAOxC,CAAK,EAEhB,CAAC,CAAA,EACY+B,CACfI,CAAAA,CAAAA,GACF,CAAC,CACH,CAAC,CACMJ,CAAAA,CACT,CA0BA,MAAa,WAAYlD,CAAAA,CAAAA,CAAeI,CAAwC,CAAA,CAC9E,IAAK,CAAA,aAAA,EACL,CAAA,MAAM,IAAK,CAAA,UAAA,CAAW,WAAYJ,CAAAA,CAAAA,CAAOI,CAAI,EAC/C,CAEO,EAAA,CAAG0D,CAAeC,CAAAA,CAAAA,CAAe,CACtC,IAAK,CAAA,MAAA,CAAO,EAAGD,CAAAA,CAAAA,CAAOC,CAAQ,EAChC,CAEO,IAAA,CAAKD,CAAeC,CAAAA,CAAAA,CAAe,CACxC,IAAA,CAAK,MAAO,CAAA,IAAA,CAAKD,CAAOC,CAAAA,CAAQ,EAClC,CAEO,GAAID,CAAAA,CAAAA,CAAeC,CAAe,CAAA,CACvC,IAAK,CAAA,MAAA,CAAO,GAAID,CAAAA,CAAAA,CAAOC,CAAQ,EACjC,CAEO,cAAA,CAAeD,EAAeC,CAAe,CAAA,CAClD,IAAK,CAAA,MAAA,CAAO,cAAeD,CAAAA,CAAAA,CAAOC,CAAQ,EAC5C,CAEA,MAAa,mBAAsB,EAAA,CAC7B,IAAK,CAAA,QAAA,CAAS,UAAe,GAAA,IAAA,CAAK,+BAAmC,EAAA,IAAA,CAAK,SAC5E,CAAA,CAAA,MAAML,2BAAsB,CAAA,IAAA,CAAK,QAAS,CAAA,UAAA,EAAc,CAAA,GAAA,CAAM,uBAAuB,CAAA,CAAE,KACrF,CAAA,IAAM,KAAK,oBAAqB,EAClC,CAEA,CAAA,IAAA,CAAK,oBAAqB,GAE9B,CAEA,MAAa,cAAiB,EAAA,CAC5B,IAAK,CAAA,yBAAA,CAA4B,CACjC,CAAA,CAAA,MAAM,IAAK,CAAA,mBAAA,GACb,CAEA,MAAM,aAAA,CAAcyE,CAAmB,CAAA,CACrC,GAAI,CAAC,IAAK,CAAA,UAAA,CAAW,YAAc,CAAA,CACjC,IAAK,CAAA,MAAA,CAAO,IACV,CAAA,+EACF,CACA,CAAA,MACF,CAiBA,GAfI,IAAK,CAAA,cAAA,EACP,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,EAAI,CAAA,uDAAuD,CAC7E,CAAA,MAAM,IAAK,CAAA,cAAA,CACX,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,EAAI,CAAA,sCAAsC,CAE5D,GAAA,IAAA,CAAK,cAAiB,CAAA,IAAI,OAAQ,CAAA,MAAO7E,CAASK,CAAAA,CAAAA,GAAW,CAC3D,MAAM,IAAA,CAAK,OAAQwE,CAAAA,CAAQ,CACxB,CAAA,IAAA,CAAK7E,CAAO,CAAA,CACZ,KAAMK,CAAAA,CAAM,CACZ,CAAA,OAAA,CAAQ,IAAM,CACb,IAAK,CAAA,cAAA,CAAiB,KACxB,EAAA,CAAC,EACL,CAAC,CACD,CAAA,MAAM,IAAK,CAAA,cAAA,CAAA,CAET,CAAC,IAAA,CAAK,SACR,CAAA,MAAM,IAAI,KAAA,CAAM,6DAA6D,IAAK,CAAA,QAAQ,CAAE,CAAA,CAEhG,CAEA,MAAa,gBAAiBwE,CAAAA,CAAAA,CAAmB,CAC/C,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,EAAC,CAAG,yBAAyB,CAAA,CAC3C,CAAK,IAAA,CAAA,2BAAA,GACT,IAAK,CAAA,QAAA,CAAWA,CAAY,EAAA,IAAA,CAAK,QACjC,CAAA,MAAM,IAAK,CAAA,yBAAA,EACX,CAAA,MAAM,IAAK,CAAA,cAAA,GACX,MAAM,IAAA,CAAK,aAAc,EAAA,EAC3B,CAEA,MAAa,yBAA4B,EAAA,CACvC,GAAI,CAAA,MAAMC,cAAS,EAAA,CACnB,MAAM,IAAI,KAAM,CAAA,6EAA6E,CAC/F,CAEA,MAAa,wBAAA,CAAyBjG,CAAuC,CAAA,CAC3E,GAAIA,CAAAA,CAAAA,EAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAU,MAAW,IAAA,CAAA,CAAG,CAC1B,IAAA,CAAK,OAAO,KAAM,CAAA,4CAA4C,CAC9D,CAAA,MACF,CACA,MAAMkG,CAAiBlG,CAAAA,CAAAA,CAAS,IAAK,CAAA,CAACwC,CAAG2D,CAAAA,CAAAA,GAAM3D,CAAE,CAAA,WAAA,CAAc2D,CAAE,CAAA,WAAW,CAC5E,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAYD,SAAAA,EAAAA,CAAAA,CAAe,MAAM,CAAA,sBAAA,CAAwB,CAC3E,CAAA,IAAA,MAAW5J,CAAW4J,IAAAA,CAAAA,CACpB,GAAI,CACF,MAAM,IAAK,CAAA,cAAA,CAAe5J,CAAO,EACnC,CAASqD,MAAAA,CAAAA,CAAG,CACV,IAAA,CAAK,MAAO,CAAA,IAAA,CAAKA,CAAG,CAAA,8CAAA,EAAkDA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAa,CAAA,OAAA,CAAO,EAC5F,CAEF,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAA,SAAA,EAAYuG,CAAe,CAAA,MAAM,CAA2B,yBAAA,CAAA,EAChF,CAEA,MAAa,kBACXE,CAAAA,CAAAA,CACAnI,EACA,CACA,KAAM,CAAE,KAAA,CAAAJ,CAAM,CAAA,CAAIuI,CAElB,CAAA,GAAI,CAACnI,CAAAA,CAAK,aAAe,CAAA,CACvB,MAAMoI,CAAAA,CAASC,gBAAWC,CAAAA,iBAAY,CAChCC,CAAAA,CAAAA,CAAU,CAAE,KAAA,CAAA3I,CAAO,CAAA,MAAA,CAAAwI,CAAQ,CAAA,KAAA,CAAO,CAAE,QAAA,CAAU,KAAM,CAAA,CAAG,MAAQ,CAAA,CAAA,CAAM,EAC3E,MAAM,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,QAAS,CAAA,GAAA,CAAIxI,CAAO2I,CAAAA,CAAO,EACrD,CAEA,IAAK,CAAA,MAAA,CAAO,IAAKnF,CAAAA,cAAAA,CAAe,OAAS+E,CAAAA,CAAY,CACrD,CAAA,MAAM,IAAK,CAAA,kBAAA,CAAmBA,CAAY,EAC5C,CAIA,MAAc,OAAQJ,CAAAA,CAAAA,CAAmB,CACvC,MAAM,IAAK,CAAA,yBAAA,EACPA,CAAAA,CAAAA,EAAYA,CAAa,GAAA,IAAA,CAAK,QAChC,GAAA,IAAA,CAAK,QAAWA,CAAAA,CAAAA,CAChB,MAAM,IAAA,CAAK,mBAAoB,EAAA,CAAA,CAGjC,IAAK,CAAA,2BAAA,CAA8B,CACnC,CAAA,CAAA,IAAA,CAAK,yBAA4B,CAAA,CAAA,CAAA,CACjC,IAAI1D,CAAAA,CAAU,CACd,CAAA,KAAOA,CAAU,CAAA,CAAA,EAAG,CAClB,GAAI,CACF,GAAI,IAAK,CAAA,yBAAA,CACP,MAEF,IAAA,CAAK,OAAO,KAAM,CAAA,EAAI,CAAA,CAAA,cAAA,EAAiB,IAAK,CAAA,QAAQ,CAAcA,WAAAA,EAAAA,CAAO,CAAK,GAAA,CAAA,CAAA,CAG9E,MAAM,IAAA,CAAK,cAAe,EAAA,CAE1B,MAAM,IAAI,OAAc,CAAA,MAAOnB,CAASK,CAAAA,CAAAA,GAAW,CACjD,MAAMiF,CAAe,CAAA,IAAM,CACzBjF,CAAAA,CAAO,IAAI,KAAA,CAAM,kDAAkD,CAAC,EACtE,CACA,CAAA,IAAA,CAAK,QAAS,CAAA,IAAA,CAAK0D,uBAAwB,CAAA,UAAA,CAAYuB,CAAY,CAAA,CAEnE,MAAMlF,2BAAAA,CACJ,IAAI,OAAA,CAAQ,CAACJ,CAAAA,CAASK,CAAW,GAAA,CAC/B,IAAK,CAAA,QAAA,CAAS,OAAQ,EAAA,CAAE,IAAKL,CAAAA,CAAO,CAAE,CAAA,KAAA,CAAMK,CAAM,EACpD,CAAC,CAAA,CACD,IAAK,CAAA,cAAA,CACL,4CAA4C,IAAK,CAAA,QAAQ,CAC3D,CAAA,CAAA,CACG,KAAO7B,CAAAA,CAAAA,EAAM,CACZ6B,CAAAA,CAAO7B,CAAC,EACV,CAAC,CAAA,CACA,OAAQ,CAAA,IAAM,CACb,IAAA,CAAK,QAAS,CAAA,GAAA,CAAIuF,uBAAwB,CAAA,UAAA,CAAYuB,CAAY,CAAA,CAClE,YAAa,CAAA,IAAA,CAAK,gBAAgB,EACpC,CAAC,CAAA,CACH,MAAM,IAAI,OAAQ,CAAA,MAAOtF,CAASK,CAAAA,CAAAA,GAAW,CAC3C,MAAMiF,CAAe,CAAA,IAAM,CACzBjF,CAAAA,CAAO,IAAI,KAAA,CAAM,kDAAkD,CAAC,EACtE,CAAA,CACA,IAAK,CAAA,QAAA,CAAS,IAAK0D,CAAAA,uBAAAA,CAAwB,UAAYuB,CAAAA,CAAY,CACnE,CAAA,MAAM,IAAK,CAAA,UAAA,CACR,KAAM,EAAA,CACN,IAAKtF,CAAAA,CAAO,CACZ,CAAA,KAAA,CAAMK,CAAM,CACZ,CAAA,OAAA,CAAQ,IAAM,CACb,IAAK,CAAA,QAAA,CAAS,GAAI0D,CAAAA,uBAAAA,CAAwB,UAAYuB,CAAAA,CAAY,EACpE,CAAC,EACL,CAAC,CACD,CAAA,IAAA,CAAK,+BAAkC,CAAA,CAAA,CAAA,CACvCtF,CAAQ,GACV,CAAC,EACH,CAASxB,MAAAA,CAAAA,CAAG,CACV,MAAM,IAAK,CAAA,UAAA,CAAW,IAAK,EAAA,CAC3B,MAAMX,CAAQW,CAAAA,CAAAA,CACd,IAAK,CAAA,MAAA,CAAO,IAAK,CAAA,EAAIX,CAAAA,CAAAA,CAAM,OAAO,CAAA,CAClC,IAAK,CAAA,+BAAA,CAAkC,CACzC,EAAA,CAAA,OAAE,CACA,IAAA,CAAK,2BAA8B,CAAA,CAAA,EACrC,CAEA,GAAI,IAAK,CAAA,SAAA,CAAW,CAClB,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,EAAC,CAAG,CAAgB,aAAA,EAAA,IAAA,CAAK,QAAQ,CAA6BsD,0BAAAA,EAAAA,CAAO,CAAE,CAAA,CAAA,CACzF,KACF,CAEA,MAAM,IAAI,OAASnB,CAAAA,CAAAA,EAAY,UAAWA,CAAAA,CAAAA,CAASZ,kBAAc+B,CAAAA,CAAAA,CAAU,CAAC,CAAC,CAAC,CAAA,CAC9EA,CACF,GAAA,CACF,CASQ,gBAAA,EAAmB,CAhb7B,IAAA5B,CAAAmB,CAAAA,CAAAA,CAAAC,CAAAC,CAAAA,CAAAA,CAAA2E,CAibI,CAAA,GAAKzB,YAAO,EAAA,CACZ,GAAI,CAAA,CAEEpD,CAAAnB,CAAAA,CAAAA,CAAAA,CAAA,IAAK,CAAA,QAAA,GAAL,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAe,UAAf,GAAA,IAAA,EAAAmB,CAA2B,CAAA,MAAA,GAAA,CAE7B6E,CAAA3E,CAAAA,CAAAA,CAAAA,CAAAA,CAAAD,CAAA,CAAA,IAAA,CAAK,QAAL,GAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAe,CAAA,UAAA,GAAf,IAAAC,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAA2B,MAA3B,GAAA,IAAA,EAAA2E,CAAmC,CAAA,EAAA,CAAG,MAAQ,CAAA,IAAM,CAClD,IAAA,CAAK,mBACP,CAAA,CAAA,CAAA,CAEF,IAAK,CAAA,gBAAA,GACP,CAAA,MAAS/G,CAAG,CAAA,CACV,IAAK,CAAA,MAAA,CAAO,IAAKA,CAAAA,CAAAA,CAAIA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAa,CAAA,OAAO,EAC3C,CACF,CAgBA,MAAc,cAAiB,EAAA,CACzB,IAAK,CAAA,QAAA,CAAS,UAChB,EAAA,IAAA,CAAK,2BAA4B,EAAA,CAEnC,MAAMgH,CAAAA,CAAO,MAAM,IAAK,CAAA,IAAA,CAAK,MAAO,CAAA,OAAA,CAAQ,IAAK,CAAA,QAAQ,CAEzD,CAAA,IAAA,CAAK,QAAW,CAAA,IAAIC,+BAClB,CAAA,IAAIC,qBACFC,CAAAA,uBAAAA,CAAkB,CAChB,UAAA,CAAYC,mBACZ,CAAA,QAAA,CAAU,IAAK,CAAA,QAAA,CACf,OAAS,CAAA,IAAA,CAAK,OACd,CAAA,QAAA,CAAU,IAAK,CAAA,QAAA,CACf,SAAW,CAAA,IAAA,CAAK,SAChB,CAAA,IAAA,CAAAJ,EACA,eAAiB,CAAA,CAAA,CAAA,CACjB,QAAU,CAAA,IAAA,CAAK,QACf,CAAA,WAAA,CAAa,IAAK,CAAA,WACpB,CAAC,CACH,CACF,CAAA,CACA,IAAK,CAAA,yBAAA,GACP,CAEA,MAAc,kBAAA,CAAmBP,CAAyC,CAAA,CACxE,KAAM,CAAE,KAAAvI,CAAAA,CAAAA,CAAO,OAAAvB,CAAAA,CAAQ,CAAI8J,CAAAA,CAAAA,CAC3B,MAAM,IAAA,CAAK,SAAS,GAAIvI,CAAAA,CAAAA,CAAOvB,CAAO,EACxC,CAEA,MAAc,wBACZ8J,CAAAA,CAAAA,CACkB,CAClB,KAAM,CAAE,KAAA,CAAAvI,CAAO,CAAA,OAAA,CAAAvB,CAAQ,CAAA,CAAI8J,CAG3B,CAAA,GAAI,CAAC9J,CAAAA,EAAWA,CAAQ,CAAA,MAAA,GAAW,CACjC,CAAA,OAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAK,CAAmCA,gCAAAA,EAAAA,CAAO,CAAE,CAAA,CAAA,CACtD,GAIT,GAAI,CAAE,MAAM,IAAA,CAAK,UAAW,CAAA,YAAA,CAAauB,CAAK,CAAA,CAC5C,OAAK,IAAA,CAAA,MAAA,CAAO,IAAK,CAAA,CAAA,0CAAA,EAA6CA,CAAK,CAAA,CAAE,CAC9D,CAAA,CAAA,CAAA,CAIT,MAAMmJ,CAAAA,CAAS,IAAK,CAAA,QAAA,CAAS,GAAInJ,CAAAA,CAAAA,CAAOvB,CAAO,CAAA,CAC/C,OAAI0K,CAAAA,EACF,IAAK,CAAA,MAAA,CAAO,IAAK,CAAA,CAAA,4BAAA,EAA+B1K,CAAO,CAAE,CAAA,CAAA,CAEpD0K,CACT,CAEA,MAAc,iBAAA,CAAkBhJ,CAAyB,CAAA,CAGvD,GAFA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,wBAAwB,CAAA,CAC1C,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,IAAM,CAAA,SAAA,CAAW,SAAW,CAAA,UAAA,CAAY,OAAAA,CAAAA,CAAQ,CAAC,CAAA,CACjEiJ,6BAAiBjJ,CAAAA,CAAO,CAAG,CAAA,CAC7B,GAAI,CAACA,CAAAA,CAAQ,MAAO,CAAA,QAAA,CAASkJ,yBAAyB,CAAA,CAAG,OACzD,MAAMvF,CAAS3D,CAAAA,CAAAA,CAA4D,MACrE,CAAA,CAAE,KAAAH,CAAAA,CAAAA,CAAO,OAAAvB,CAAAA,CAAAA,CAAS,WAAA6K,CAAAA,CAAAA,CAAa,WAAAnF,CAAAA,CAAY,CAAIL,CAAAA,CAAAA,CAAM,IACrDyE,CAAAA,CAAAA,CAA0C,CAC9C,KAAA,CAAAvI,CACA,CAAA,OAAA,CAAAvB,CACA,CAAA,WAAA,CAAA6K,CACA,CAAA,aAAA,CAAelD,eAAgB,CAAA,KAAA,CAC/B,WAAAjC,CAAAA,CACF,CACA,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,0BAA0B,CAAA,CAC5C,IAAK,CAAA,MAAA,CAAO,KAAMN,CAAAA,GAAAA,CAAA,CAAE,IAAA,CAAM,OAAS,CAAA,KAAA,CAAOC,CAAM,CAAA,EAAA,CAAA,CAAOyE,CAAc,CAAA,CAAA,CACrE,IAAK,CAAA,MAAA,CAAO,IAAKzE,CAAAA,CAAAA,CAAM,EAAIyE,CAAAA,CAAY,CACvC,CAAA,MAAM,KAAK,kBAAmBpI,CAAAA,CAAO,CACrC,CAAA,MAAM,IAAK,CAAA,cAAA,CAAeoI,CAAY,EACxC,CAAWgB,KAAAA,8BAAAA,CAAkBpJ,CAAO,CAAA,EAClC,IAAK,CAAA,MAAA,CAAO,IAAKqD,CAAAA,cAAAA,CAAe,WAAarD,CAAAA,CAAO,EAExD,CAEA,MAAc,cAAA,CAAeoI,CAAyC,CAAA,CAChE,MAAM,IAAA,CAAK,wBAAyBA,CAAAA,CAAY,CAGpD,GAAA,IAAA,CAAK,OAAO,IAAK/E,CAAAA,cAAAA,CAAe,OAAS+E,CAAAA,CAAY,CACrD,CAAA,MAAM,IAAK,CAAA,kBAAA,CAAmBA,CAAY,CAAA,EAC5C,CAEA,MAAc,kBAAmBpI,CAAAA,CAAAA,CAAyB,CACxD,MAAM6G,CAAWwC,CAAAA,gCAAAA,CAAoBrJ,CAAQ,CAAA,EAAA,CAAI,CAAI,CAAA,CAAA,CACrD,MAAM,IAAA,CAAK,QAAS,CAAA,UAAA,CAAW,IAAK6G,CAAAA,CAAQ,EAC9C,CAoCQ,6BAA8B,CACpC,IAAA,CAAK,QAAS,CAAA,GAAA,CAAIK,uBAAwB,CAAA,OAAA,CAAS,IAAK,CAAA,gBAAgB,CACxE,CAAA,IAAA,CAAK,QAAS,CAAA,GAAA,CAAIA,uBAAwB,CAAA,OAAA,CAAS,IAAK,CAAA,gBAAgB,CACxE,CAAA,IAAA,CAAK,QAAS,CAAA,GAAA,CAAIA,uBAAwB,CAAA,UAAA,CAAY,IAAK,CAAA,mBAAmB,CAC9E,CAAA,IAAA,CAAK,QAAS,CAAA,GAAA,CAAIA,uBAAwB,CAAA,KAAA,CAAO,IAAK,CAAA,sBAAsB,CAC5E,CAAA,YAAA,CAAa,IAAK,CAAA,WAAW,EAC/B,CAEA,MAAc,sBAAA,EAAyB,CACrC,IAAIoC,CAAqB,CAAA,MAAMrB,cAAS,EAAA,CACxCsB,8BAAyB,CAAA,MAAOC,CAAuB,EAAA,CAEjDF,CAAuBE,GAAAA,CAAAA,GAE3BF,CAAqBE,CAAAA,CAAAA,CAChBA,CAMH,CAAA,MAAM,IAAK,CAAA,aAAA,EAAgB,CAAA,KAAA,CAAOxI,CAChC,EAAA,IAAA,CAAK,OAAO,KAAMA,CAAAA,CAAAA,CAAQA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAiB,CAAA,OAAO,CACpD,CAAA,EANA,IAAK,CAAA,+BAAA,CAAkC,CACvC,CAAA,CAAA,MAAM,IAAK,CAAA,mBAAA,EACX,CAAA,IAAA,CAAK,yBAA4B,CAAA,CAAA,CAAA,CAAA,EAMrC,CAAC,EACH,CAEA,MAAc,oBAAuB,EAAA,CACnC,YAAa,CAAA,IAAA,CAAK,WAAW,CAAA,CAC7B,IAAK,CAAA,MAAA,CAAO,KAAKqC,cAAe,CAAA,UAAU,CAC1C,CAAA,IAAA,CAAK,2BAA8B,CAAA,CAAA,CAAA,CAC/B,CAAK,IAAA,CAAA,mBAAA,GAET,IAAK,CAAA,mBAAA,CAAsB,CAC3B,CAAA,CAAA,MAAM,IAAK,CAAA,UAAA,CAAW,IAAK,EAAA,CAEtB,IAAK,CAAA,UAAA,CAAW,YACjB,GAAA,IAAA,CAAK,yBAET,GAAA,IAAA,CAAK,gBAAmB,CAAA,UAAA,CAAW,SAAY,CAC7C,MAAM,IAAA,CAAK,aAAc,EAAA,CAAE,MAAOrC,CAChC,EAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAMA,CAAQA,CAAAA,CAAAA,EAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAiB,OAAO,CACpD,CACA,CAAA,IAAA,CAAK,gBAAmB,CAAA,KAAA,CAAA,CACxB,IAAK,CAAA,mBAAA,CAAsB,CAC7B,EAAA,CAAA,CAAGuB,kBAAckH,CAAAA,yBAAyB,CAAC,CAAA,CAAA,CAAA,EAC7C,CAEQ,aAAA,EAAgB,CACtB,GAAI,CAAC,IAAA,CAAK,WAAa,CAAA,CACrB,KAAM,CAAE,OAAAnL,CAAAA,CAAQ,CAAIC,CAAAA,sBAAAA,CAAiB,iBAAmB,CAAA,IAAA,CAAK,IAAI,CAAA,CACjE,MAAM,IAAI,KAAMD,CAAAA,CAAO,CACzB,CACF,CAEA,MAAc,qBAAwB,EAAA,CACpC,MAAM,IAAA,CAAK,yBAA0B,EAAA,CACjC,CAAK,IAAA,CAAA,SAAA,EACT,MAAM,IAAA,CAAK,OAAQ,GACrB,CACF;;ACxoBA,IAAAoL,GAAA,CAAA,MAAA,CAAA,cAAA,CAAA,IAAAC,GAAA,CAAA,MAAA,CAAA,qBAAA,CAAA,IAAAC,GAAA,CAAA,MAAA,CAAA,SAAA,CAAA,cAAA,CAAA7E,GAAA,CAAA,MAAA,CAAA,SAAA,CAAA,oBAAA,CAAA,IAAAnD,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA8H,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAAE,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAAhI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA+H,GAAA,CAAA,IAAA,IAAA,CAAA,IAAAA,GAAA,CAAA,CAAA,CAAA,CAAA5E,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAAnD,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,IAAA4D,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA5D,GAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAWO,MAAM,KAAqDiI,SAAAA,YAAkB,CAyBlF,WACShM,CAAAA,CAAAA,CACAC,EACAgM,CACPC,CAAAA,CAAAA,CAAwB7L,oBACxB8L,CAAqC,CAAA,KAAA,CAAA,CACrC,CACA,KAAA,CAAMnM,EAAMC,CAAQgM,CAAAA,CAAAA,CAAMC,CAAa,CAAA,CANhC,UAAAlM,CACA,CAAA,IAAA,CAAA,MAAA,CAAAC,CACA,CAAA,IAAA,CAAA,IAAA,CAAAgM,EA3BT/L,GAAA,CAAA,IAAA,CAAO,MAAM,IAAI,GAAA,CAAA,CACjBA,IAAA,IAAO,CAAA,SAAA,CAAUkM,qBAEjBlM,CAAAA,CAAAA,GAAAA,CAAA,KAAQ,QAAiB,CAAA,EACzBA,CAAAA,CAAAA,GAAAA,CAAA,KAAQ,aAAc,CAAA,CAAA,CAAA,CAAA,CAKtBA,GAAA,CAAA,IAAA,CAAQ,UAERA,GAAA,CAAA,IAAA,CAAQ,gBAAgBG,mBAGxBH,CAAAA,CAAAA,GAAAA,CAAA,KAAQ,iBAAyB,CAAA,EACjCA,CAAAA,CAAAA,GAAAA,CAAA,KAAQ,sBAAuB,CAAA,GAAA,CAAA,CAsB/BA,GAAA,CAAA,IAAA,CAAO,OAAkC,SAAY,CAC9C,IAAK,CAAA,WAAA,GACR,KAAK,MAAO,CAAA,KAAA,CAAM,aAAa,CAE/B,CAAA,MAAM,KAAK,OAAQ,EAAA,CAEnB,IAAK,CAAA,MAAA,CAAO,QAASmM,CAAU,EAAA,CACzB,IAAK,CAAA,MAAA,EAAUA,IAAU,IAAQ,EAAA,CAAC9F,iBAAY8F,CAAAA,CAAK,EACrD,IAAK,CAAA,GAAA,CAAI,IAAI,IAAK,CAAA,MAAA,CAAOA,CAAK,CAAGA,CAAAA,CAAK,CAC7BC,CAAAA,sBAAAA,CAAiBD,CAAK,CAE/B,CAAA,IAAA,CAAK,GAAI,CAAA,GAAA,CAAIA,EAAM,EAAWA,CAAAA,CAAK,CAC1BE,CAAAA,qBAAAA,CAAgBF,CAAK,CAE9B,EAAA,IAAA,CAAK,IAAI,GAAIA,CAAAA,CAAAA,CAAM,MAAcA,CAAK,EAE1C,CAAC,CAAA,CAED,KAAK,MAAS,CAAA,EACd,CAAA,IAAA,CAAK,YAAc,CAEvB,CAAA,EAAA,CAAA,CAAA,CAsBAnM,GAAA,CAAA,IAAA,CAAO,MAAgC,MAAOM,CAAAA,CAAK6L,IAAU,CAC3D,IAAA,CAAK,eACD,CAAA,IAAA,CAAK,GAAI,CAAA,GAAA,CAAI7L,CAAG,CAClB,CAAA,MAAM,IAAK,CAAA,MAAA,CAAOA,EAAK6L,CAAK,CAAA,EAE5B,IAAK,CAAA,MAAA,CAAO,MAAM,eAAe,CAAA,CACjC,KAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAM,CAAA,QAAA,CAAU,MAAQ,CAAA,KAAA,CAAO,IAAA7L,CAAK,CAAA,KAAA,CAAA6L,CAAM,CAAC,EAC/D,IAAK,CAAA,GAAA,CAAI,GAAI7L,CAAAA,CAAAA,CAAK6L,CAAK,CACvB,CAAA,MAAM,KAAK,OAAQ,EAAA,EAEvB,GAEAnM,GAAA,CAAA,IAAA,CAAO,KAAiCM,CAAAA,CAAAA,GACtC,KAAK,aAAc,EAAA,CACnB,IAAK,CAAA,MAAA,CAAO,MAAM,eAAe,CAAA,CACjC,IAAK,CAAA,MAAA,CAAO,MAAM,CAAE,IAAA,CAAM,SAAU,MAAQ,CAAA,KAAA,CAAO,IAAAA,CAAI,CAAC,CAC1C,CAAA,IAAA,CAAK,QAAQA,CAAG,CAAA,CAAA,CAAA,CAIhCN,GAAA,CAAA,IAAA,CAAO,SAAuCsM,CAC5C,GAAA,IAAA,CAAK,aAAc,EAAA,CACdA,EAEE,IAAK,CAAA,MAAA,CAAO,OAAQH,CACzB,EAAA,MAAA,CAAO,KAAKG,CAAM,CAAA,CAAE,KAAOhM,CAAAA,CAAAA,EAAQiM,sBAAQJ,CAAM7L,CAAAA,CAAG,CAAGgM,CAAAA,CAAAA,CAAOhM,CAAG,CAAC,CAAC,CACrE,CAAA,CAJoB,KAAK,MAO3BN,CAAAA,CAAAA,CAAAA,GAAAA,CAAA,KAAO,QAAsC,CAAA,MAAOM,EAAKkM,CAAW,GAAA,CAClE,IAAK,CAAA,aAAA,GACL,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,gBAAgB,EAClC,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,KAAM,QAAU,CAAA,MAAA,CAAQ,SAAU,GAAAlM,CAAAA,CAAAA,CAAK,OAAAkM,CAAO,CAAC,CACnE,CAAA,MAAML,EAAQxG,CAAA,CAAA,CAAA,CAAA,EAAA,CAAK,IAAK,CAAA,OAAA,CAAQrF,CAAG,CAAMkM,CAAAA,CAAAA,CAAAA,CAAAA,CACzC,IAAK,CAAA,GAAA,CAAI,IAAIlM,CAAK6L,CAAAA,CAAK,EACvB,MAAM,IAAA,CAAK,UACb,CAAA,CAAA,CAEAnM,GAAA,CAAA,IAAA,CAAO,SAAsC,MAAOM,CAAAA,CAAK0H,IAAW,CAClE,IAAA,CAAK,eACA,CAAA,IAAA,CAAK,GAAI,CAAA,GAAA,CAAI1H,CAAG,CACrB,GAAA,IAAA,CAAK,OAAO,KAAM,CAAA,gBAAgB,EAClC,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,KAAM,QAAU,CAAA,MAAA,CAAQ,QAAU,CAAA,GAAA,CAAAA,EAAK,MAAA0H,CAAAA,CAAO,CAAC,CAAA,CACnE,KAAK,GAAI,CAAA,MAAA,CAAO1H,CAAG,CACnB,CAAA,IAAA,CAAK,qBAAqBA,CAAG,CAAA,CAC7B,MAAM,IAAA,CAAK,SACb,EAAA,CAAA,CAAA,CA9FE,IAAK,CAAA,MAAA,CAASG,2BAAoBV,CAAQ,CAAA,IAAA,CAAK,IAAI,CAAA,CACnD,KAAK,aAAgBiM,CAAAA,CAAAA,CACrB,KAAK,MAASC,CAAAA,EAChB,CAyBA,IAAI,OAAA,EAAU,CACZ,OAAOvL,wBAAiB,IAAK,CAAA,MAAM,CACrC,CAEA,IAAI,UAAa,EAAA,CACf,OAAO,IAAA,CAAK,cAAgB,IAAK,CAAA,OAAA,CAAU,KAAK,IAAK,CAAA,mBAAA,CAAsB,KAAO,IAAK,CAAA,IACzF,CAEA,IAAI,QAAS,CACX,OAAO,IAAK,CAAA,GAAA,CAAI,IAClB,CAEA,IAAI,IAAO,EAAA,CACT,OAAO,KAAM,CAAA,IAAA,CAAK,KAAK,GAAI,CAAA,IAAA,EAAM,CACnC,CAEA,IAAI,MAAA,EAAS,CACX,OAAO,KAAA,CAAM,IAAK,CAAA,IAAA,CAAK,IAAI,MAAO,EAAC,CACrC,CAoDQ,qBAAqBJ,CAAU,CAAA,CACrC,KAAK,eAAgB,CAAA,IAAA,CAAKA,CAAG,CAEzB,CAAA,IAAA,CAAK,eAAgB,CAAA,MAAA,EAAU,KAAK,oBACtC,EAAA,IAAA,CAAK,eAAgB,CAAA,MAAA,CAAO,EAAG,IAAK,CAAA,oBAAA,CAAuB,CAAC,EAEhE,CAEA,MAAc,YAAA,CAAa6L,EAAe,CACxC,MAAM,KAAK,IAAK,CAAA,OAAA,CAAQ,OAAgB,CAAA,IAAA,CAAK,WAAYA,CAAK,EAChE,CAEA,MAAc,cAAe,CAE3B,OADc,MAAM,IAAA,CAAK,KAAK,OAAQ,CAAA,OAAA,CAAgB,KAAK,UAAU,CAEvE,CAEQ,OAAQ7L,CAAAA,CAAAA,CAAU,CACxB,MAAM6L,EAAQ,IAAK,CAAA,GAAA,CAAI,IAAI7L,CAAG,CAAA,CAC9B,GAAI,CAAC6L,CAAAA,CAAO,CACV,GAAI,KAAK,eAAgB,CAAA,QAAA,CAAS7L,CAAG,CAAG,CAAA,CACtC,KAAM,CAAE,OAAA,CAAAC,CAAQ,CAAA,CAAIC,uBAClB,oBACA,CAAA,CAAA,8BAAA,EAAiC,IAAK,CAAA,IAAI,KAAKF,CAAG,CAAA,CACpD,CACA,CAAA,MAAA,IAAA,CAAK,OAAO,KAAMC,CAAAA,CAAO,EACnB,IAAI,KAAA,CAAMA,CAAO,CACzB,CAEA,KAAM,CAAE,QAAAA,CAAQ,CAAA,CAAIC,sBAAiB,CAAA,iBAAA,CAAmB,GAAG,IAAK,CAAA,IAAI,CAAKF,EAAAA,EAAAA,CAAG,EAAE,CAC9E,CAAA,MAAA,IAAA,CAAK,OAAO,KAAMC,CAAAA,CAAO,EACnB,IAAI,KAAA,CAAMA,CAAO,CACzB,CACA,OAAO4L,CACT,CAEA,MAAc,SAAU,CACtB,MAAM,IAAK,CAAA,YAAA,CAAa,KAAK,MAAM,EACrC,CAEA,MAAc,OAAA,EAAU,CACtB,GAAI,CACF,MAAMtD,CAAAA,CAAY,MAAM,IAAK,CAAA,YAAA,EAE7B,CAAA,GADI,OAAOA,CAAc,EAAA,WAAA,EACrB,CAACA,CAAAA,CAAU,OAAQ,OACvB,GAAI,KAAK,GAAI,CAAA,IAAA,CAAM,CACjB,KAAM,CAAE,OAAAtI,CAAAA,CAAQ,EAAIC,sBAAiB,CAAA,uBAAA,CAAyB,IAAK,CAAA,IAAI,EACvE,MAAK,IAAA,CAAA,MAAA,CAAO,KAAMD,CAAAA,CAAO,EACnB,IAAI,KAAA,CAAMA,CAAO,CACzB,CACA,KAAK,MAASsI,CAAAA,CAAAA,CACd,IAAK,CAAA,MAAA,CAAO,MAAM,CAAmC,gCAAA,EAAA,IAAA,CAAK,IAAI,CAAA,CAAE,EAChE,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,KAAM,QAAU,CAAA,MAAA,CAAQ,UAAW,KAAO,CAAA,IAAA,CAAK,MAAO,CAAC,EAC7E,CAAS,MAAA,CAAA,CAAG,CACV,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAA,4BAAA,EAA+B,KAAK,IAAI,CAAA,CAAE,CAC5D,CAAA,IAAA,CAAK,OAAO,KAAM,CAAA,CAAQ,EAC5B,CACF,CAEQ,eAAgB,CACtB,GAAI,CAAC,IAAA,CAAK,YAAa,CACrB,KAAM,CAAE,OAAAtI,CAAAA,CAAQ,EAAIC,sBAAiB,CAAA,iBAAA,CAAmB,IAAK,CAAA,IAAI,EACjE,MAAM,IAAI,MAAMD,CAAO,CACzB,CACF,CACF;;4KCzJa,OAA4B,CAYvC,WAAmBT,CAAAA,CAAAA,CAAoBC,CAAgB,CAAA,CAApC,IAAAD,CAAAA,IAAAA,CAAAA,CAAAA,CAAoB,IAAAC,CAAAA,MAAAA,CAAAA,CAAAA,CAXvCC,GAAA,CAAA,IAAA,CAAO,MAAOyM,CAAAA,eAAAA,CAAAA,CACdzM,GAAA,CAAA,IAAA,CAAO,SAAU0M,CAAAA,uBAAAA,CAAAA,CAEjB1M,GAAA,CAAA,IAAA,CAAO,QAAS,CAAA,IAAIsE,qBACpBtE,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,UAAA,CAAA,CAEPA,GAAA,CAAA,IAAA,CAAQ,aAAc,CAAA,CAAA,CAAA,CAAA,CACtBA,IAAA,IAAQ,CAAA,eAAA,CAAgBG,mBACxBH,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,qBAAA,CAAsB,CAAC2M,YAAM,CACrC3M,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,mBAAA,CAA8B,EAAC,CAAA,CAQvCA,IAAA,IAAO,CAAA,MAAA,CAAyB,SAAY,CACrC,IAAK,CAAA,WAAA,GACR,MAAM,IAAA,CAAK,QAAS,CAAA,IAAA,EACpB,CAAA,MAAM,IAAK,CAAA,OAAA,GACX,IAAK,CAAA,qBAAA,EACL,CAAA,IAAA,CAAK,qBAAsB,EAAA,CAC3B,IAAK,CAAA,WAAA,CAAc,CACnB,CAAA,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,aAAa,CAAA,EAEnC,CAMAA,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,UAAA,CAAiC,CAAC,CAAE,OAAA4M,CAAAA,CAAQ,CAAM,GAAA,CACvD,IAAK,CAAA,aAAA,EACL,CAAA,IAAA,CAAK,iBAAoB,CAAA,CAAC,GAAG,IAAI,GAAA,CAAI,CAAC,GAAG,IAAK,CAAA,iBAAA,CAAmB,GAAGA,CAAO,CAAC,CAAC,EAC/E,CAAA,CAAA,CAEA5M,GAAA,CAAA,IAAA,CAAO,SAA6B,MAAOmC,CAAAA,EAAW,CACpD,IAAA,CAAK,aAAc,EAAA,CACnB,MAAMP,CAAAA,CAASb,2BAAsB,EAAA,CAC/Be,CAAQ,CAAA,MAAM,IAAK,CAAA,IAAA,CAAK,OAAO,SAAUF,CAAAA,CAAM,CAC/C0I,CAAAA,CAAAA,CAASC,gBAAWC,CAAAA,iBAAY,CAChC3F,CAAAA,CAAAA,CAAQ,CAAE,QAAA,CAAUgI,wBAAyB,CAAA,CAC7CpC,CAAU,CAAA,CAAE,KAAA3I,CAAAA,CAAAA,CAAO,MAAAwI,CAAAA,CAAAA,CAAQ,KAAAzF,CAAAA,CAAAA,CAAO,MAAQ,CAAA,CAAA,CAAA,CAAO,OAAS1C,CAAAA,CAAAA,EAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAQ,OAAQ,CAAA,CAC1E2K,CAAMC,CAAAA,eAAAA,CAAU,CACpB,QAAU,CAAA,IAAA,CAAK,IAAK,CAAA,QAAA,CACpB,OAAS,CAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CACnB,KAAAjL,CAAAA,CAAAA,CACA,MAAAF,CAAAA,CAAAA,CACA,KAAAiD,CAAAA,CAAAA,CACA,gBAAiByF,CACjB,CAAA,OAAA,CAASnI,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAQ,CAAA,OACnB,CAAC,CAAA,CACD,OAAK,IAAA,CAAA,MAAA,CAAO,IAAK6K,CAAAA,cAAAA,CAAe,MAAQvC,CAAAA,CAAO,EAC/C,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,GAAA,CAAI3I,CAAOwI,CAAAA,CAAM,CACnC,CAAA,MAAM,IAAK,CAAA,QAAA,CAAS,GAAIxI,CAAAA,CAAAA,CAAO2I,CAAO,CAAA,CACtC,MAAM,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,SAAA,CAAU3I,CAAO,CAAA,CAAE,aAAeK,CAAAA,CAAAA,EAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAQ,aAAc,CAAC,CAE1E,CAAA,CAAE,MAAAL,CAAO,CAAA,GAAA,CAAAgL,CAAI,CACtB,CAEA9M,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,MAAA,CAAyB,MAAOmC,CAAAA,EAAW,CAChD,IAAA,CAAK,aAAc,EAAA,CAEnB,MAAMyD,CAAQ,CAAA,IAAA,CAAK,IAAK,CAAA,WAAA,CAAY,WAAY,CAAA,CAC9C,UAAY,CAAA,CACV,KAAOzD,CAAAA,CAAAA,EAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAQ,GACf,CAAA,KAAA,CAAO,CAAC8K,2BAA4B,CAAA,eAAe,CACrD,CACF,CAAC,CAAA,CAED,IAAK,CAAA,WAAA,CAAY9K,CAAQyD,CAAAA,CAAK,CAE9B,CAAA,KAAM,CAAE,KAAA,CAAA9D,CAAO,CAAA,MAAA,CAAAF,CAAQ,CAAA,KAAA,CAAAiD,CAAO,CAAA,eAAA,CAAAqI,CAAiB,CAAA,OAAA,CAAAN,CAAQ,CAAA,CAAIO,cAAShL,CAAAA,CAAAA,CAAO,GAAG,CAAA,CAE9EyD,CAAM,CAAA,KAAA,CAAM,WAAW,KAAQ9D,CAAAA,CAAAA,CAC/B8D,CAAM,CAAA,QAAA,CAASqH,2BAA4B,CAAA,8BAA8B,CACzErH,CAAAA,CAAAA,CAAM,QAASqH,CAAAA,2BAAAA,CAA4B,uBAAuB,CAAA,CAElE,IAAIG,CAAAA,CACJ,GAAI,IAAK,CAAA,QAAA,CAAS,IAAK,CAAA,QAAA,CAAStL,CAAK,CAAA,CAAG,CAGtC,GAFAsL,CAAkB,CAAA,IAAA,CAAK,QAAS,CAAA,GAAA,CAAItL,CAAK,CAAA,CACzC8D,EAAM,QAASqH,CAAAA,2BAAAA,CAA4B,gBAAgB,CAAA,CACvDG,CAAgB,CAAA,MAAA,CAClB,MAAAxH,CAAAA,CAAM,QAASyH,CAAAA,2BAAAA,CAA4B,6BAA6B,CAAA,CAClE,IAAI,KAAA,CACR,CAA2BvL,wBAAAA,EAAAA,CAAK,CAClC,6CAAA,CAAA,CAAA,CAEA8D,CAAM,CAAA,QAAA,CAASqH,2BAA4B,CAAA,mBAAmB,EAElE,CAEA,MAAM3C,CAAAA,CAAS4C,CAAmB3C,EAAAA,gBAAAA,CAAWC,iBAAY,CAAA,CACnDC,EAAU,CAAE,KAAA,CAAA3I,CAAO,CAAA,KAAA,CAAA+C,CAAO,CAAA,MAAA,CAAAyF,CAAQ,CAAA,MAAA,CAAQ,CAAO,CAAA,CAAA,OAAA,CAAAsC,CAAQ,CAAA,CAC/D,IAAK,CAAA,IAAA,CAAK,QAAQ,GAAI9K,CAAAA,CAAAA,CAAOwI,CAAM,CAAA,CACnC,MAAM,IAAA,CAAK,QAAS,CAAA,GAAA,CAAIxI,CAAO2I,CAAAA,CAAO,CAEtC7E,CAAAA,CAAAA,CAAM,QAASqH,CAAAA,2BAAAA,CAA4B,iBAAiB,CAExD9K,CAAAA,CAAAA,CAAO,eACT,EAAA,MAAM,IAAK,CAAA,QAAA,CAAS,CAAE,KAAA,CAAAL,CAAM,CAAC,CAG/B,CAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAKkL,cAAe,CAAA,MAAA,CAAQvC,CAAO,CAAA,CAE/C7E,CAAM,CAAA,QAAA,CAASqH,2BAA4B,CAAA,qBAAqB,CAG3D,CAAA,IAAA,CAAK,IAAK,CAAA,MAAA,CAAO,QAAS,CAAA,GAAA,CAAInL,CAAK,CAAA,EACtC,MAAM,IAAK,CAAA,IAAA,CAAK,MAAO,CAAA,SAAA,CAAUF,CAAQE,CAAAA,CAAK,CAEhD8D,CAAAA,CAAAA,CAAM,QAASqH,CAAAA,2BAAAA,CAA4B,yBAAyB,CAAA,CAEpE,GAAI,CACF,MAAM,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,yBAAA,GAC1B,CAAA,MAAShK,CAAO,CAAA,CACd2C,CAAM,CAAA,QAAA,CAASyH,2BAA4B,CAAA,sBAAsB,EACnE,CAEA,GAAI,CACF,MAAM,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,SAAA,CAAUvL,CAAO,CAAA,CAAE,KAAA+C,CAAAA,CAAM,CAAC,EACpD,CAAS5B,MAAAA,CAAAA,CAAO,CACd,MAAA2C,CAAAA,CAAM,QAASyH,CAAAA,2BAAAA,CAA4B,+BAA+B,CAAA,CACpEpK,CACR,CAEA,OAAA2C,CAAAA,CAAM,QAASqH,CAAAA,2BAAAA,CAA4B,+BAA+B,CAAA,CAEnExC,CACT,CAEAzK,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,UAAA,CAAiC,MAAO,CAAE,KAAA8B,CAAAA,CAAM,CAAM,GAAA,CAC3D,IAAK,CAAA,aAAA,EACL,CAAA,MAAMwI,EAASC,gBAAWC,CAAAA,iBAAY,CACtC,CAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,GAAI1I,CAAAA,CAAAA,CAAOwI,CAAM,CAAA,CACnC,MAAM,IAAA,CAAK,QAAS,CAAA,MAAA,CAAOxI,EAAO,CAAE,MAAA,CAAQ,CAAM,CAAA,CAAA,MAAA,CAAAwI,CAAO,CAAC,EAC5D,CAAA,CAAA,CAKAtK,GAAA,CAAA,IAAA,CAAO,MAAyB,CAAA,MAAOmC,CAAW,EAAA,CAChD,IAAK,CAAA,aAAA,EACL,CAAA,MAAM,IAAK,CAAA,WAAA,CAAYA,CAAM,CAAA,CAC7B,IAAK,CAAA,MAAA,CAAO,IAAK,CAAA,qEAAqE,CACtF,CAAA,KAAM,CAAE,KAAA,CAAAL,CAAM,CAAIK,CAAAA,CAAAA,CAClB,GAAI,IAAA,CAAK,QAAS,CAAA,IAAA,CAAK,QAASL,CAAAA,CAAK,CAAG,CAAA,CACtC,MAAMkD,CAAAA,CAAK,MAAM,IAAA,CAAK,YAAYlD,CAAO,CAAA,gBAAA,CAAkB,EAAE,CACvD,CAAA,CAAE,IAAAwL,CAAAA,CAAAA,CAAM,OAAAlI,CAAAA,CAAAA,CAAS,MAAAK,CAAAA,CAAO,CAAI8H,CAAAA,0BAAAA,GAClC,IAAK,CAAA,MAAA,CAAO,IAAKC,CAAAA,iBAAAA,CAAY,cAAgBxI,CAAAA,CAAE,CAAG,CAAA,CAAC,CAAE,KAAA,CAAA/B,CAAM,CAAA,GAAM,CAC3DA,CAAAA,CAAOwC,CAAOxC,CAAAA,CAAK,CAClBmC,CAAAA,CAAAA,GACP,CAAC,CACD,CAAA,MAAMkI,CAAK,GACb,CACF,CAAA,CAAA,CAEAtN,GAAA,CAAA,IAAA,CAAO,cAAyC,CAAA,MAAO,CAAE,KAAA8B,CAAAA,CAAAA,CAAO,MAAAwI,CAAAA,CAAO,CAAM,GAAA,CAC3E,IAAK,CAAA,aAAA,EACL,CAAA,MAAM,IAAK,CAAA,QAAA,CAAS,MAAOxI,CAAAA,CAAAA,CAAO,CAAE,MAAAwI,CAAAA,CAAO,CAAC,EAC9C,CAEAtK,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,gBAAA,CAA6C,MAAO,CAAE,KAAA8B,CAAAA,CAAAA,CAAO,QAAA2L,CAAAA,CAAS,IAAM,CACjF,IAAA,CAAK,aAAc,EAAA,CACnB,MAAM,IAAA,CAAK,QAAS,CAAA,MAAA,CAAO3L,CAAO,CAAA,CAAE,YAAc2L,CAAAA,CAAS,CAAC,EAC9D,CAEAzN,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,aAAA,CAAuC,KAC5C,IAAA,CAAK,aAAc,EAAA,CACZ,IAAK,CAAA,QAAA,CAAS,MAGvBA,CAAAA,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,YAAA,CAAqC,MAAOmC,CAAAA,EAAW,CAC5D,IAAK,CAAA,aAAA,EACL,CAAA,MAAM,IAAK,CAAA,iBAAA,CAAkBA,CAAM,CAAA,CACnC,KAAM,CAAE,KAAAL,CAAAA,CAAM,CAAIK,CAAAA,CAAAA,CACd,KAAK,QAAS,CAAA,IAAA,CAAK,QAASL,CAAAA,CAAK,CACnC,GAAA,MAAM,IAAK,CAAA,WAAA,CAAYA,CAAO,CAAA,kBAAA,CAAoBmG,iBAAY,CAAA,mBAAmB,CAAC,CAAA,CAClF,MAAM,IAAK,CAAA,aAAA,CAAcnG,CAAK,CAAA,EAElC,CAEA9B,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,sBAAA,CAA0DyK,CAAY,EAAA,CAC3E,IAAK,CAAA,aAAA,EACL,CAAA,KAAM,CAAE,KAAA3I,CAAAA,CAAAA,CAAO,KAAA+C,CAAAA,CAAAA,CAAO,MAAAyF,CAAAA,CAAAA,CAAQ,OAAAsC,CAAAA,CAAQ,CAAInC,CAAAA,CAAAA,CACpC7I,CAAS,CAAA,IAAA,CAAK,IAAK,CAAA,MAAA,CAAO,SAAS,GAAIE,CAAAA,CAAK,CAClD,CAAA,OAAOiL,eAAU,CAAA,CACf,QAAU,CAAA,IAAA,CAAK,IAAK,CAAA,QAAA,CACpB,OAAS,CAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CACnB,MAAAjL,CACA,CAAA,MAAA,CAAAF,CACA,CAAA,KAAA,CAAAiD,CACA,CAAA,eAAA,CAAiByF,CACjB,CAAA,OAAA,CAAAsC,CACF,CAAC,CACH,CAAA,CAAA,CAIA5M,GAAA,CAAA,IAAA,CAAQ,cAA8C,MAAO8B,CAAAA,CAAO4L,CAAQvL,CAAAA,CAAAA,GAAW,CACrF,MAAMF,CAAU0L,CAAAA,iCAAAA,CAAqBD,CAAQvL,CAAAA,CAAM,CAC7C5B,CAAAA,CAAAA,CAAU,MAAM,IAAA,CAAK,IAAK,CAAA,MAAA,CAAO,MAAOuB,CAAAA,CAAAA,CAAOG,CAAO,CAAA,CACtDC,CAAO0L,CAAAA,gBAAAA,CAAiBF,CAAM,CAAA,CAAE,GACtC,CAAA,OAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,GAAI5L,CAAAA,CAAAA,CAAOG,CAAO,CACpC,CAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,OAAQH,CAAAA,CAAAA,CAAOvB,CAAS2B,CAAAA,CAAI,CACvCD,CAAAA,CAAAA,CAAQ,EACjB,CAAA,CAAA,CAEAjC,GAAA,CAAA,IAAA,CAAQ,aAA4C,MAAOgF,CAAAA,CAAIlD,CAAOwE,CAAAA,CAAAA,GAAW,CAC/E,MAAMrE,CAAUqJ,CAAAA,gCAAAA,CAAoBtG,CAAIsB,CAAAA,CAAM,CACxC/F,CAAAA,CAAAA,CAAU,MAAM,IAAA,CAAK,KAAK,MAAO,CAAA,MAAA,CAAOuB,CAAOG,CAAAA,CAAO,CAEtDyL,CAAAA,CAAAA,CAAAA,CADS,MAAM,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,GAAI5L,CAAAA,CAAAA,CAAOkD,CAAE,CAAA,EAC9B,OAAQ,CAAA,MAAA,CACxB9C,CAAO0L,CAAAA,gBAAAA,CAAiBF,CAAM,CAAA,CAAE,GACtC,CAAA,MAAM,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,OAAA,CAAQ5L,CAAOvB,CAAAA,CAAAA,CAAS2B,CAAI,CAAA,CACpD,MAAM,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,OAAA,CAAQD,CAAO,EACzC,CAEAjC,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,WAAA,CAA0C,MAAOgF,CAAAA,CAAIlD,CAAOmB,CAAAA,CAAAA,GAAU,CAC5E,MAAMhB,CAAAA,CAAU4L,+BAAmB7I,CAAAA,CAAAA,CAAI/B,CAAK,CAAA,CACtC1C,CAAU,CAAA,MAAM,IAAK,CAAA,IAAA,CAAK,MAAO,CAAA,MAAA,CAAOuB,CAAOG,CAAAA,CAAO,EAEtDyL,CADS,CAAA,CAAA,MAAM,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,GAAA,CAAI5L,CAAOkD,CAAAA,CAAE,CAC9B,EAAA,OAAA,CAAQ,MAExB9C,CAAAA,CAAAA,CAAO0L,gBAAiBF,CAAAA,CAAM,CAChCE,CAAAA,gBAAAA,CAAiBF,CAAM,CAAA,CAAE,GACzBE,CAAAA,gBAAAA,CAAiB,mBAAoB,CAAA,GAAA,CAEzC,MAAM,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,OAAQ9L,CAAAA,CAAAA,CAAOvB,CAAS2B,CAAAA,CAAI,EACpD,MAAM,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,OAAQD,CAAAA,CAAO,EACzC,CAAA,CAAA,CAEAjC,GAAA,CAAA,IAAA,CAAQ,eAAkD,CAAA,MAAO8B,CAAOgM,CAAAA,CAAAA,GAAsB,CAE5F,MAAM,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,WAAYhM,CAAAA,CAAK,CACzC,CAAA,MAAM,OAAQ,CAAA,GAAA,CAAI,CAChB,IAAA,CAAK,QAAS,CAAA,MAAA,CAAOA,EAAOmG,iBAAY,CAAA,mBAAmB,CAAC,CAAA,CAC5D,IAAK,CAAA,IAAA,CAAK,MAAO,CAAA,YAAA,CAAanG,CAAK,CAAA,CACnCgM,CAAoB,CAAA,OAAA,CAAQ,OAAQ,EAAA,CAAI,KAAK,IAAK,CAAA,OAAA,CAAQ,GAAIhM,CAAAA,CAAK,CACrE,CAAC,EACH,CAAA,CAAA,CASA9B,GAAA,CAAA,IAAA,CAAQ,SAAU,CAAA,SAAY,CAC5B,MAAM+N,EAAkB,IAAK,CAAA,QAAA,CAAS,MAAO,EAAA,CAAE,MAAQtD,CAAAA,CAAAA,EAAYuD,eAAUvD,CAAAA,CAAAA,CAAQ,MAAM,CAAC,CAC5F,CAAA,MAAM,OAAQ,CAAA,GAAA,CAAIsD,EAAgB,GAAKtD,CAAAA,CAAAA,EAAY,IAAK,CAAA,aAAA,CAAcA,CAAQ,CAAA,KAAK,CAAC,CAAC,EACvF,CAAA,CAAA,CAkCAzK,GAAA,CAAA,IAAA,CAAQ,qBAA+D4F,CAAAA,CAAAA,EAAU,CAC/E,KAAM,CAAE,KAAA9D,CAAAA,CAAAA,CAAO,OAAAG,CAAAA,CAAQ,CAAI2D,CAAAA,CAAAA,CAG3B,OAFkB3D,CAAAA,CAAQ,MAEP,EACjB,IAAK,gBAAA,CACH,OAAO,IAAA,CAAK,oBAAqBH,CAAAA,CAAAA,CAAOG,CAAO,CAAA,CACjD,IAAK,kBAAA,CACH,OAAO,IAAA,CAAK,sBAAuBH,CAAAA,CAAAA,CAAOG,CAAO,CAAA,CACnD,QACE,OAAO,KAAK,yBAA0BH,CAAAA,CAAAA,CAAOG,CAAO,CACxD,CACF,CAAA,CAAA,CAEAjC,GAAA,CAAA,IAAA,CAAQ,sBAAgE,CAAA,MAAO4F,CAAU,EAAA,CACvF,KAAM,CAAE,MAAA9D,CAAO,CAAA,OAAA,CAAAG,CAAQ,CAAA,CAAI2D,CAErBqI,CAAAA,CAAAA,CAAAA,CADS,MAAM,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,GAAInM,CAAAA,CAAAA,CAAOG,CAAQ,CAAA,EAAE,GACnC,OAAQ,CAAA,MAAA,CAEjC,OAAQgM,CAAAA,EACN,IAAK,gBACH,CAAA,OAAO,IAAK,CAAA,qBAAA,CAAsBnM,CAAOG,CAAAA,CAAO,CAClD,CAAA,QACE,OAAO,IAAA,CAAK,0BAA2BgM,CAAAA,CAAS,CACpD,CACF,CAEAjO,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,sBAAA,CAAgE,MACtE8B,CAAAA,CACAG,CACG,GAAA,CACH,KAAM,CAAE,GAAA+C,CAAG,CAAA,CAAI/C,CACf,CAAA,GAAI,CACF,IAAA,CAAK,WAAY,CAAA,CAAE,KAAAH,CAAAA,CAAM,CAAC,CAAA,CAC1B,MAAM,IAAA,CAAK,WAA6BkD,CAAIlD,CAAAA,CAAAA,CAAO,CAAI,CAAA,CAAA,CACvD,IAAK,CAAA,MAAA,CAAO,IAAKkL,CAAAA,cAAAA,CAAe,IAAM,CAAA,CAAE,EAAAhI,CAAAA,CAAAA,CAAI,KAAAlD,CAAAA,CAAM,CAAC,EACrD,CAAA,MAAS0G,CAAU,CAAA,CACjB,MAAM,IAAA,CAAK,SAAUxD,CAAAA,CAAAA,CAAIlD,CAAO0G,CAAAA,CAAG,CACnC,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAMA,CAAG,EACvB,CACF,CAAA,CAAA,CAEAxI,GAAA,CAAA,IAAA,CAAQ,uBAAkE,CAAA,CAACkO,CAAQjM,CAAAA,CAAAA,GAAY,CAC7F,KAAM,CAAE,EAAA,CAAA+C,CAAG,CAAA,CAAI/C,EAGf,UAAW,CAAA,IAAM,CACXkM,4BAAAA,CAAgBlM,CAAO,CAAA,CACzB,IAAK,CAAA,MAAA,CAAO,IAAKuL,CAAAA,iBAAAA,CAAY,cAAgBxI,CAAAA,CAAE,CAAG,CAAA,EAAE,CAC3CoJ,CAAAA,2BAAAA,CAAenM,CAAO,CAAA,EAC/B,IAAK,CAAA,MAAA,CAAO,IAAKuL,CAAAA,iBAAAA,CAAY,cAAgBxI,CAAAA,CAAE,CAAG,CAAA,CAAE,KAAO/C,CAAAA,CAAAA,CAAQ,KAAM,CAAC,EAE9E,CAAG,CAAA,GAAG,EACR,CAAA,CAAA,CAEAjC,GAAA,CAAA,IAAA,CAAQ,wBAAoE,CAAA,MAC1E8B,CACAG,CAAAA,CAAAA,GACG,CACH,KAAM,CAAE,EAAA+C,CAAAA,CAAG,CAAI/C,CAAAA,CAAAA,CACf,GAAI,CACF,IAAK,CAAA,iBAAA,CAAkB,CAAE,KAAA,CAAAH,CAAM,CAAC,CAChC,CAAA,MAAM,KAAK,aAAcA,CAAAA,CAAK,CAC9B,CAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAKkL,cAAe,CAAA,MAAA,CAAQ,CAAE,EAAA,CAAAhI,CAAI,CAAA,KAAA,CAAAlD,CAAM,CAAC,EACvD,CAAS0G,MAAAA,CAAAA,CAAU,CACjB,MAAM,IAAK,CAAA,SAAA,CAAUxD,CAAIlD,CAAAA,CAAAA,CAAO0G,CAAG,CAAA,CACnC,IAAK,CAAA,MAAA,CAAO,KAAMA,CAAAA,CAAG,EACvB,CACF,CAAA,CAAA,CAEAxI,GAAA,CAAA,IAAA,CAAQ,2BAA0E,CAAA,MAChF8B,CACAG,CAAAA,CAAAA,GACG,CACH,KAAM,CAAE,EAAA,CAAA+C,CAAI,CAAA,MAAA,CAAA0I,CAAO,CAAA,CAAIzL,CAEvB,CAAA,GAAI,CAEF,GAAI,IAAK,CAAA,iBAAA,CAAkB,QAASyL,CAAAA,CAAM,CAAG,CAAA,OAC7C,MAAMzK,CAAAA,CAAQgF,iBAAY,CAAA,uBAAA,CAAyByF,CAAM,CACzD,CAAA,MAAM,IAAK,CAAA,SAAA,CAAU1I,CAAIlD,CAAAA,CAAAA,CAAOmB,CAAK,CAAA,CACrC,IAAK,CAAA,MAAA,CAAO,KAAMA,CAAAA,CAAK,EACzB,CAAA,MAASuF,EAAU,CACjB,MAAM,IAAK,CAAA,SAAA,CAAUxD,CAAIlD,CAAAA,CAAAA,CAAO0G,CAAG,CAAA,CACnC,IAAK,CAAA,MAAA,CAAO,KAAMA,CAAAA,CAAG,EACvB,CACF,GAEAxI,GAAA,CAAA,IAAA,CAAQ,4BAA6E0N,CAAAA,CAAAA,EAAW,CAE1F,IAAA,CAAK,iBAAkB,CAAA,QAAA,CAASA,CAAM,CAAA,EAC1C,IAAK,CAAA,MAAA,CAAO,KAAMzF,CAAAA,iBAAAA,CAAY,uBAAyByF,CAAAA,CAAM,CAAC,EAChE,CAgBA1N,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,aAAA,CAAc,CAACmC,CAAAA,CAAyByD,CAAkC,GAAA,CAlcpF,IAAAjB,CAAAA,CAmcI,GAAI,CAAC0J,oBAAclM,CAAM,CAAA,CAAG,CAC1B,KAAM,CAAE,OAAA,CAAA5B,CAAQ,CAAA,CAAIC,sBAAiB,CAAA,oBAAA,CAAsB,CAAkB2B,eAAAA,EAAAA,CAAM,CAAE,CAAA,CAAA,CACrF,MAAAyD,CAAM,CAAA,QAAA,CAASyH,2BAA4B,CAAA,qBAAqB,CAC1D,CAAA,IAAI,KAAM9M,CAAAA,CAAO,CACzB,CACA,GAAI,CAAC+N,gBAAWnM,CAAAA,CAAAA,CAAO,GAAG,CAAG,CAAA,CAC3B,KAAM,CAAE,OAAA5B,CAAAA,CAAQ,CAAIC,CAAAA,sBAAAA,CAAiB,oBAAsB,CAAA,CAAA,YAAA,EAAe2B,CAAO,CAAA,GAAG,CAAE,CAAA,CAAA,CACtF,MAAAyD,CAAAA,CAAM,QAASyH,CAAAA,2BAAAA,CAA4B,qBAAqB,CAAA,CAC1D,IAAI,KAAA,CAAM9M,CAAO,CACzB,CACA,MAAMuM,CAAMK,CAAAA,cAAAA,CAAShL,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,EAAQ,GAAG,CAAA,CAChC,GAAI,EAAA,CAACwC,CAAAmI,CAAAA,CAAAA,EAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAK,KAAL,GAAA,IAAA,EAAAnI,CAAY,CAAA,QAAA,CAAA,CAAU,CACzB,KAAM,CAAE,OAAApE,CAAAA,CAAQ,CAAIC,CAAAA,sBAAAA,CAAiB,oBAAsB,CAAA,2BAA2B,CACtF,CAAA,MAAAoF,CAAM,CAAA,QAAA,CAASyH,2BAA4B,CAAA,qBAAqB,CAC1D,CAAA,IAAI,MAAM9M,CAAO,CACzB,CACA,GAAI,EAACuM,CAAAA,EAAA,IAAAA,EAAAA,CAAAA,CAAK,MAAQ,CAAA,CAAA,CAChB,KAAM,CAAE,OAAAvM,CAAAA,CAAQ,EAAIC,sBAAiB,CAAA,oBAAA,CAAsB,mBAAmB,CAAA,CAC9E,MAAAoF,CAAAA,CAAM,QAASyH,CAAAA,2BAAAA,CAA4B,qBAAqB,CAAA,CAC1D,IAAI,KAAA,CAAM9M,CAAO,CACzB,CACA,GAAIuM,CAAAA,EAAA,IAAAA,EAAAA,CAAAA,CAAK,eACYtI,EAAAA,kBAAAA,CAAcsI,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAK,CAAA,eAAe,CACpC,CAAA,IAAA,CAAK,GAAI,EAAA,CAAG,CAC3BlH,CAAM,CAAA,QAAA,CAASyH,2BAA4B,CAAA,eAAe,CAC1D,CAAA,KAAM,CAAE,OAAA,CAAA9M,CAAQ,CAAA,CAAIC,sBAClB,CAAA,SAAA,CACA,qEACF,CAAA,CACA,MAAM,IAAI,KAAA,CAAMD,CAAO,CACzB,CAEJ,CAAA,CAAA,CAEAP,GAAA,CAAA,IAAA,CAAQ,aAAc,CAAA,MAAOmC,CAA8B,EAAA,CACzD,GAAI,CAACkM,mBAAclM,CAAAA,CAAM,CAAG,CAAA,CAC1B,KAAM,CAAE,OAAA5B,CAAAA,CAAQ,CAAIC,CAAAA,sBAAAA,CAAiB,oBAAsB,CAAA,CAAA,eAAA,EAAkB2B,CAAM,CAAA,CAAE,CACrF,CAAA,MAAM,IAAI,KAAM5B,CAAAA,CAAO,CACzB,CACA,KAAM,CAAE,KAAAuB,CAAAA,CAAM,CAAIK,CAAAA,CAAAA,CAClB,MAAM,IAAA,CAAK,mBAAoBL,CAAAA,CAAK,EACtC,CAEA9B,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,mBAAA,CAAoB,MAAOmC,CAAAA,EAA8B,CAC/D,GAAI,CAACkM,mBAAAA,CAAclM,CAAM,CAAA,CAAG,CAC1B,KAAM,CAAE,OAAA5B,CAAAA,CAAQ,CAAIC,CAAAA,sBAAAA,CAAiB,oBAAsB,CAAA,CAAA,qBAAA,EAAwB2B,CAAM,CAAA,CAAE,CAC3F,CAAA,MAAM,IAAI,KAAA,CAAM5B,CAAO,CACzB,CACA,KAAM,CAAE,KAAA,CAAAuB,CAAM,CAAA,CAAIK,CAClB,CAAA,MAAM,IAAK,CAAA,mBAAA,CAAoBL,CAAK,EACtC,CAEA9B,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,qBAAA,CAAsB,MAAO8B,CAAe,EAAA,CAClD,GAAI,CAACyM,mBAAczM,CAAAA,CAAAA,CAAO,CAAK,CAAA,CAAA,CAAG,CAChC,KAAM,CAAE,OAAA,CAAAvB,CAAQ,CAAA,CAAIC,uBAClB,oBACA,CAAA,CAAA,kCAAA,EAAqCsB,CAAK,CAAA,CAC5C,CACA,CAAA,MAAM,IAAI,KAAA,CAAMvB,CAAO,CACzB,CACA,GAAI,CAAC,IAAA,CAAK,SAAS,IAAK,CAAA,QAAA,CAASuB,CAAK,CAAA,CAAG,CACvC,KAAM,CAAE,OAAA,CAAAvB,CAAQ,CAAA,CAAIC,sBAClB,CAAA,iBAAA,CACA,CAAgCsB,6BAAAA,EAAAA,CAAK,CACvC,CAAA,CAAA,CACA,MAAM,IAAI,KAAMvB,CAAAA,CAAO,CACzB,CACA,GAAIyN,eAAAA,CAAU,IAAK,CAAA,QAAA,CAAS,GAAIlM,CAAAA,CAAK,CAAE,CAAA,MAAM,EAAG,CAC9C,MAAM,IAAK,CAAA,aAAA,CAAcA,CAAK,CAAA,CAC9B,KAAM,CAAE,OAAAvB,CAAAA,CAAQ,CAAIC,CAAAA,sBAAAA,CAAiB,SAAW,CAAA,CAAA,eAAA,EAAkBsB,CAAK,CAAE,CAAA,CAAA,CACzE,MAAM,IAAI,KAAMvB,CAAAA,CAAO,CACzB,CACF,CAxcE,CAAA,CAAA,IAAA,CAAK,IAAOT,CAAAA,CAAAA,CACZ,IAAK,CAAA,MAAA,CAASW,2BAAoBV,CAAQ,CAAA,IAAA,CAAK,IAAI,CAAA,CACnD,IAAK,CAAA,QAAA,CAAW,IAAIyO,KAAAA,CAAM,IAAK,CAAA,IAAA,CAAM,IAAK,CAAA,MAAA,CAAQ,IAAK,CAAA,IAAA,CAAM,KAAK,aAAa,EACjF,CAaA,IAAI,OAAU,EAAA,CACZ,OAAO9N,uBAAAA,CAAiB,IAAK,CAAA,MAAM,CACrC,CAqNQ,aAAgB,EAAA,CACtB,GAAI,CAAC,IAAA,CAAK,WAAa,CAAA,CACrB,KAAM,CAAE,OAAAH,CAAAA,CAAQ,CAAIC,CAAAA,sBAAAA,CAAiB,iBAAmB,CAAA,IAAA,CAAK,IAAI,CAAA,CACjE,MAAM,IAAI,KAAA,CAAMD,CAAO,CACzB,CACF,CASQ,qBAAwB,EAAA,CAC9B,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,EAAA,CAAG+E,cAAe,CAAA,OAAA,CAAS,MAAOM,CAAqC,EAAA,CACvF,KAAM,CAAE,KAAA9D,CAAAA,CAAAA,CAAO,OAAAvB,CAAAA,CAAAA,CAAS,aAAAkO,CAAAA,CAAc,CAAI7I,CAAAA,CAAAA,CAS1C,GANI,CAAC,IAAK,CAAA,QAAA,CAAS,IAAK,CAAA,QAAA,CAAS9D,CAAK,CAAA,EAGlC2M,CAAkBvG,GAAAA,eAAAA,CAAgB,SAGlC,EAAA,IAAA,CAAK,mBAAoB,CAAA,QAAA,CAAS,IAAK,CAAA,IAAA,CAAK,MAAO,CAAA,cAAA,CAAe3H,CAAO,CAAC,CAAA,CAAG,OAEjF,MAAM0B,CAAU,CAAA,MAAM,IAAK,CAAA,IAAA,CAAK,MAAO,CAAA,MAAA,CAAOH,CAAOvB,CAAAA,CAAO,CAE5D,CAAA,GAAI,CACE2K,6BAAiBjJ,CAAAA,CAAO,CAC1B,EAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,GAAIH,CAAAA,CAAAA,CAAOG,CAAO,CAAA,CACpC,IAAK,CAAA,mBAAA,CAAoB,CAAE,KAAA,CAAAH,EAAO,OAAAG,CAAAA,CAAQ,CAAC,CAAA,EAClCoJ,8BAAkBpJ,CAAAA,CAAO,CAClC,GAAA,MAAM,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,OAAA,CAAQA,CAAO,CAAA,CACvC,MAAM,IAAA,CAAK,oBAAqB,CAAA,CAAE,KAAAH,CAAAA,CAAAA,CAAO,OAAAG,CAAAA,CAAQ,CAAC,CAAA,CAClD,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,MAAA,CAAOH,CAAOG,CAAAA,CAAAA,CAAQ,EAAE,CAE9C,EAAA,CAAA,MAASgB,CAAO,CAAA,CACd,IAAK,CAAA,MAAA,CAAO,KAAMA,CAAAA,CAAK,EACzB,CACF,CAAC,EACH,CAkGQ,qBAAA,EAAwB,CAC9B,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,EAAA,CAAGyL,cAAe,CAAA,OAAA,CAAS,MAAO9I,CAAAA,EAAmC,CACrF,KAAM,CAAE,KAAA,CAAA9D,CAAM,CAAA,CAAI6M,yBAAmB/I,CAAM,CAAA,MAAM,CAC5C9D,CAAAA,CAAAA,EACA,IAAK,CAAA,QAAA,CAAS,IAAK,CAAA,QAAA,CAASA,CAAK,CAAA,GACtC,MAAM,IAAA,CAAK,aAAcA,CAAAA,CAAAA,CAAO,CAAI,CAAA,CAAA,CACpC,IAAK,CAAA,MAAA,CAAO,IAAKkL,CAAAA,cAAAA,CAAe,MAAQ,CAAA,CAAE,KAAAlL,CAAAA,CAAM,CAAC,CAAA,EACnD,CAAC,EACH,CA8EF;;4KC9fa,MAAA,cAAA,SAAuB8M,qBAAgB,CAUlD,WAAmB9O,CAAAA,CAAAA,CAAoBC,CAAgB,CAAA,CACrD,KAAMD,CAAAA,CAAAA,CAAMC,CAAM,CAAA,CADD,UAAAD,CAAoB,CAAA,IAAA,CAAA,MAAA,CAAAC,CATvCC,CAAAA,CAAAA,CAAA,IAAO,CAAA,SAAA,CAAU,IAAI,GAAA,CAAA,CACrBA,CAAA,CAAA,IAAA,CAAO,QAAS,CAAA,IAAIsE,gBACpBtE,CAAAA,CAAAA,CAAAA,CAAA,KAAO,MAAO6O,CAAAA,eAAAA,CAAAA,CACd7O,CAAA,CAAA,IAAA,CAAO,SAAU8O,CAAAA,uBAAAA,CAAAA,CAEjB9O,CAAA,CAAA,IAAA,CAAQ,QAA0B,CAAA,EAClCA,CAAAA,CAAAA,CAAAA,CAAA,IAAQ,CAAA,aAAA,CAAc,IACtBA,CAAA,CAAA,IAAA,CAAQ,eAAgBG,CAAAA,mBAAAA,CAAAA,CAOxBH,CAAA,CAAA,IAAA,CAAO,MAAgC,CAAA,SAAY,CAC5C,IAAA,CAAK,WACR,GAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,aAAa,CAC/B,CAAA,MAAM,IAAK,CAAA,OAAA,EACX,CAAA,IAAA,CAAK,MAAO,CAAA,OAAA,CAAS+O,CAAW,EAAA,IAAA,CAAK,OAAQ,CAAA,GAAA,CAAIA,CAAO,CAAA,EAAA,CAAIA,CAAM,CAAC,CAAA,CACnE,IAAK,CAAA,MAAA,CAAS,EAAC,CACf,KAAK,sBAAuB,EAAA,CAC5B,IAAK,CAAA,WAAA,CAAc,CAEvB,CAAA,EAAA,CAAA,CAAA,CAoCA/O,EAAA,IAAO,CAAA,KAAA,CAA8B,CAAC8B,CAAAA,CAAOqE,CAAS6I,CAAAA,CAAAA,GAAY,CAIhE,GAHA,IAAK,CAAA,aAAA,EACL,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,yCAAyC,CAC3D,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAA,CAAM,QAAU,CAAA,MAAA,CAAQ,KAAO,CAAA,KAAA,CAAAlN,CAAO,CAAA,OAAA,CAAAqE,CAAS,CAAA,OAAA,CAAA6I,CAAQ,CAAC,CAAA,CACxE,IAAK,CAAA,OAAA,CAAQ,GAAI7I,CAAAA,CAAAA,CAAQ,EAAE,CAAA,CAAG,OAClC,MAAM4I,CAAwB,CAAA,CAC5B,EAAI5I,CAAAA,CAAAA,CAAQ,GACZ,KAAArE,CAAAA,CAAAA,CACA,OAAS,CAAA,CAAE,MAAQqE,CAAAA,CAAAA,CAAQ,MAAQ,CAAA,MAAA,CAAQA,CAAQ,CAAA,MAAA,EAAU,IAAK,CAAA,CAClE,OAAA6I,CAAAA,CAAAA,CACA,OAAQzE,gBAAWpM,CAAAA,gBAAW,CAChC,CAAA,CACA,IAAK,CAAA,OAAA,CAAQ,GAAI4Q,CAAAA,CAAAA,CAAO,EAAIA,CAAAA,CAAM,CAClC,CAAA,IAAA,CAAK,OAAQ,EAAA,CACb,KAAK,MAAO,CAAA,IAAA,CAAKE,cAAe,CAAA,OAAA,CAASF,CAAM,EACjD,CAEA/O,CAAAA,CAAAA,CAAAA,CAAA,IAAO,CAAA,SAAA,CAAsC,MAAO8I,CAAAA,EAAa,CAI/D,GAHA,KAAK,aAAc,EAAA,CACnB,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,2CAA2C,CAC7D,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAA,CAAM,QAAU,CAAA,MAAA,CAAQ,SAAU,QAAAA,CAAAA,CAAS,CAAC,CAAA,CAC5D,CAAC,IAAA,CAAK,QAAQ,GAAIA,CAAAA,CAAAA,CAAS,EAAE,CAAA,CAAG,OACpC,MAAMiG,EAAS,MAAM,IAAA,CAAK,SAAUjG,CAAAA,CAAAA,CAAS,EAAE,CAAA,CAC3C,OAAOiG,CAAAA,CAAO,QAAa,EAAA,WAAA,GAC/BA,CAAO,CAAA,QAAA,CAAWX,2BAAetF,CAAAA,CAAQ,EACrC,CAAE,KAAA,CAAOA,CAAS,CAAA,KAAM,CACxB,CAAA,CAAE,MAAQA,CAAAA,CAAAA,CAAS,MAAO,CAAA,CAC9B,IAAK,CAAA,OAAA,CAAQ,GAAIiG,CAAAA,CAAAA,CAAO,GAAIA,CAAM,CAAA,CAClC,IAAK,CAAA,OAAA,EACL,CAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAKE,cAAe,CAAA,OAAA,CAASF,CAAM,CAAA,EACjD,CAEA/O,CAAAA,CAAAA,CAAAA,CAAA,KAAO,KAA8B,CAAA,MAAO8B,CAAOkD,CAAAA,CAAAA,IACjD,IAAK,CAAA,aAAA,EACL,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,gBAAgB,CAAA,CAClC,IAAK,CAAA,MAAA,CAAO,MAAM,CAAE,IAAA,CAAM,QAAU,CAAA,MAAA,CAAQ,KAAO,CAAA,KAAA,CAAAlD,CAAO,CAAA,EAAA,CAAAkD,CAAG,CAAC,CAC/C,CAAA,MAAM,IAAK,CAAA,SAAA,CAAUA,CAAE,CAIxChF,CAAAA,CAAAA,CAAAA,CAAAA,CAAA,IAAO,CAAA,QAAA,CAAoC,CAAC8B,CAAAA,CAAOkD,CAAO,GAAA,CACxD,IAAK,CAAA,aAAA,EACL,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,iBAAiB,CACnC,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAA,CAAM,QAAU,CAAA,MAAA,CAAQ,QAAU,CAAA,EAAA,CAAAA,CAAG,CAAC,CAC1D,CAAA,IAAA,CAAK,OAAO,OAAS+J,CAAAA,CAAAA,EAA0B,CAC7C,GAAIA,CAAO,CAAA,KAAA,GAAUjN,CAAO,CAAA,CAC1B,GAAI,OAAOkD,CAAO,EAAA,WAAA,EAAe+J,CAAO,CAAA,EAAA,GAAO/J,EAAI,OACnD,IAAA,CAAK,OAAQ,CAAA,MAAA,CAAO+J,CAAO,CAAA,EAAE,CAC7B,CAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAKE,cAAe,CAAA,OAAA,CAASF,CAAM,EACjD,CACF,CAAC,CAAA,CACD,IAAK,CAAA,OAAA,GACP,CAAA,CAAA,CAEA/O,CAAA,CAAA,IAAA,CAAO,QAAoC,CAAA,MAAO8B,CAAOkD,CAAAA,CAAAA,IACvD,IAAK,CAAA,aAAA,GACA,IAAK,CAAA,OAAA,CAAQ,GAAIA,CAAAA,CAAE,CACT,CAAA,CAAA,MAAM,IAAK,CAAA,SAAA,CAAUA,CAAE,CAAA,EACxB,KAAUlD,GAAAA,CAAAA,CAFU,CAKpC9B,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAA,KAAO,IAA4B,CAAA,CAAC4F,CAAOC,CAAAA,CAAAA,GAAa,CACtD,IAAA,CAAK,MAAO,CAAA,EAAA,CAAGD,CAAOC,CAAAA,CAAQ,EAChC,CAAA,CAAA,CAEA7F,CAAA,CAAA,IAAA,CAAO,OAAgC,CAAC4F,CAAAA,CAAOC,CAAa,GAAA,CAC1D,IAAK,CAAA,MAAA,CAAO,IAAKD,CAAAA,CAAAA,CAAOC,CAAQ,EAClC,CAEA7F,CAAAA,CAAAA,CAAAA,CAAA,IAAO,CAAA,KAAA,CAA8B,CAAC4F,CAAOC,CAAAA,CAAAA,GAAa,CACxD,IAAA,CAAK,MAAO,CAAA,GAAA,CAAID,CAAOC,CAAAA,CAAQ,EACjC,CAAA,CAAA,CAEA7F,CAAA,CAAA,IAAA,CAAO,gBAAoD,CAAA,CAAC4F,EAAOC,CAAa,GAAA,CAC9E,IAAK,CAAA,MAAA,CAAO,cAAeD,CAAAA,CAAAA,CAAOC,CAAQ,EAC5C,CA3HE,CAAA,CAAA,IAAA,CAAK,MAASpF,CAAAA,0BAAAA,CAAoBV,CAAQ,CAAA,IAAA,CAAK,IAAI,EACrD,CAaA,IAAI,OAAA,EAAkB,CACpB,OAAOW,wBAAiB,IAAK,CAAA,MAAM,CACrC,CAEA,IAAI,UAAA,EAAa,CACf,OAAO,IAAA,CAAK,aAAgB,CAAA,IAAA,CAAK,OAAU,CAAA,IAAA,CAAK,IAAK,CAAA,mBAAA,CAAsB,IAAO,CAAA,IAAA,CAAK,IACzF,CAEA,IAAI,IAAA,EAAe,CACjB,OAAO,IAAA,CAAK,OAAQ,CAAA,IACtB,CAEA,IAAI,IAAiB,EAAA,CACnB,OAAO,KAAA,CAAM,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,IAAA,EAAM,CACvC,CAEA,IAAI,MAAA,EAAS,CACX,OAAO,KAAM,CAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,MAAO,EAAC,CACzC,CAEA,IAAI,OAA0B,EAAA,CAC5B,MAAMwO,CAAAA,CAA2B,EAAC,CAClC,OAAK,IAAA,CAAA,MAAA,CAAO,OAASH,CAAAA,CAAAA,EAAW,CAC9B,GAAI,OAAOA,CAAAA,CAAO,UAAa,WAAa,CAAA,OAC5C,MAAMI,CAAAA,CAA6B,CACjC,KAAA,CAAOJ,CAAO,CAAA,KAAA,CACd,OAASpB,CAAAA,iCAAAA,CAAqBoB,CAAO,CAAA,OAAA,CAAQ,MAAQA,CAAAA,CAAAA,CAAO,QAAQ,MAAQA,CAAAA,CAAAA,CAAO,EAAE,CAAA,CACrF,OAASA,CAAAA,CAAAA,CAAO,OAClB,CAAA,CACA,OAAOG,CAAAA,CAAS,IAAKC,CAAAA,CAAY,CACnC,CAAC,EACMD,CACT,CAiFA,MAAc,iBAAA,CAAkBE,CAAyC,CAAA,CACvE,MAAM,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,OAAyB,CAAA,IAAA,CAAK,UAAYA,CAAAA,CAAO,EAC3E,CAEA,MAAc,iBAA0D,EAAA,CAEtE,OADgB,MAAM,KAAK,IAAK,CAAA,OAAA,CAAQ,OAAyB,CAAA,IAAA,CAAK,UAAU,CAElF,CAEQ,SAAUpK,CAAAA,CAAAA,CAAY,CAC5B,IAAA,CAAK,aAAc,EAAA,CACnB,MAAM+J,CAAAA,CAAS,IAAK,CAAA,OAAA,CAAQ,GAAI/J,CAAAA,CAAE,CAClC,CAAA,GAAI,CAAC+J,CAAQ,CAAA,CACX,KAAM,CAAE,OAAAxO,CAAAA,CAAQ,CAAIC,CAAAA,sBAAAA,CAAiB,iBAAmB,CAAA,CAAA,EAAG,IAAK,CAAA,IAAI,CAAKwE,EAAAA,EAAAA,CAAE,EAAE,CAC7E,CAAA,MAAM,IAAI,KAAA,CAAMzE,CAAO,CACzB,CACA,OAAOwO,CACT,CAEA,MAAc,OAAA,EAAU,CACtB,MAAM,KAAK,iBAAkB,CAAA,IAAA,CAAK,MAAM,CAAA,CACxC,IAAK,CAAA,MAAA,CAAO,IAAKE,CAAAA,cAAAA,CAAe,IAAI,EACtC,CAEA,MAAc,OAAU,EAAA,CACtB,GAAI,CACF,MAAMpG,CAAY,CAAA,MAAM,IAAK,CAAA,iBAAA,EAE7B,CAAA,GADI,OAAOA,CAAAA,EAAc,WACrB,EAAA,CAACA,CAAU,CAAA,MAAA,CAAQ,OACvB,GAAI,IAAA,CAAK,OAAQ,CAAA,IAAA,CAAM,CACrB,KAAM,CAAE,OAAA,CAAAtI,CAAQ,CAAA,CAAIC,sBAAiB,CAAA,uBAAA,CAAyB,IAAK,CAAA,IAAI,EACvE,MAAK,IAAA,CAAA,MAAA,CAAO,KAAMD,CAAAA,CAAO,CACnB,CAAA,IAAI,KAAMA,CAAAA,CAAO,CACzB,CACA,IAAK,CAAA,MAAA,CAASsI,CACd,CAAA,IAAA,CAAK,OAAO,KAAM,CAAA,CAAA,kCAAA,EAAqC,IAAK,CAAA,IAAI,CAAE,CAAA,CAAA,CAClE,KAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAA,CAAM,QAAU,CAAA,MAAA,CAAQ,UAAW,OAAS,CAAA,IAAA,CAAK,MAAO,CAAC,EAC/E,CAAA,MAAS,CAAG,CAAA,CACV,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAA,8BAAA,EAAiC,IAAK,CAAA,IAAI,EAAE,CAC9D,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAQ,EAC5B,CACF,CAEQ,sBAA+B,EAAA,CACrC,IAAK,CAAA,MAAA,CAAO,EAAGoG,CAAAA,cAAAA,CAAe,QAAUF,CAA0B,EAAA,CAChE,MAAMjH,CAAAA,CAAYmH,cAAe,CAAA,OAAA,CACjC,IAAK,CAAA,MAAA,CAAO,IAAK,CAAA,CAAA,SAAA,EAAYnH,CAAS,CAAA,CAAE,CACxC,CAAA,IAAA,CAAK,OAAO,KAAM,CAAA,CAAE,IAAM,CAAA,OAAA,CAAS,KAAOA,CAAAA,CAAAA,CAAW,MAAAiH,CAAAA,CAAO,CAAC,EAC/D,CAAC,CAAA,CACD,IAAK,CAAA,MAAA,CAAO,GAAGE,cAAe,CAAA,OAAA,CAAUF,CAA0B,EAAA,CAChE,MAAMjH,CAAAA,CAAYmH,cAAe,CAAA,OAAA,CACjC,IAAK,CAAA,MAAA,CAAO,IAAK,CAAA,CAAA,SAAA,EAAYnH,CAAS,CAAA,CAAE,EACxC,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,IAAM,CAAA,OAAA,CAAS,KAAOA,CAAAA,CAAAA,CAAW,MAAAiH,CAAAA,CAAO,CAAC,EAC/D,CAAC,CAAA,CAED,KAAK,MAAO,CAAA,EAAA,CAAGE,cAAe,CAAA,OAAA,CAAUF,CAA0B,EAAA,CAChE,MAAMjH,CAAAA,CAAYmH,cAAe,CAAA,OAAA,CACjC,IAAK,CAAA,MAAA,CAAO,IAAK,CAAA,CAAA,SAAA,EAAYnH,CAAS,CAAE,CAAA,CAAA,CACxC,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAE,IAAM,CAAA,OAAA,CAAS,KAAOA,CAAAA,CAAAA,CAAW,MAAAiH,CAAAA,CAAO,CAAC,EAC/D,CAAC,CAED,CAAA,IAAA,CAAK,IAAK,CAAA,SAAA,CAAU,EAAGvI,CAAAA,0BAAAA,CAAiB,KAAO,CAAA,IAAM,CACnD,IAAA,CAAK,OAAQ,GACf,CAAC,EACH,CAEQ,OAAU,EAAA,CAChB,GAAI,CACF,IAAK,CAAA,aAAA,EACL,CAAA,IAAI6I,CAAU,CAAA,CAAA,CAAA,CACd,IAAK,CAAA,OAAA,CAAQ,OAASN,CAAAA,CAAAA,EAA0B,CAC3BvK,kBAAcuK,CAAAA,CAAAA,CAAO,MAAU,EAAA,CAAC,CAAI,CAAA,IAAA,CAAK,GAAI,EAAA,EAC9C,CAChB,GAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAK,CAAiCA,8BAAAA,EAAAA,CAAAA,CAAO,EAAE,CAAE,CAAA,CAAA,CAC7D,IAAK,CAAA,OAAA,CAAQ,MAAOA,CAAAA,CAAAA,CAAO,EAAE,CAAA,CAC7B,IAAK,CAAA,MAAA,CAAO,IAAKE,CAAAA,cAAAA,CAAe,OAASF,CAAAA,CAAAA,CAAQ,EAAK,CACtDM,CAAAA,CAAAA,CAAU,CAEd,CAAA,EAAA,CAAC,CACGA,CAAAA,CAAAA,EACF,IAAK,CAAA,OAAA,GAET,CAAA,MAAS,CAAG,CAAA,CACV,IAAK,CAAA,MAAA,CAAO,KAAK,CAAC,EACpB,CACF,CAEQ,aAAgB,EAAA,CACtB,GAAI,CAAC,IAAK,CAAA,WAAA,CAAa,CACrB,KAAM,CAAE,OAAA,CAAA9O,CAAQ,CAAIC,CAAAA,sBAAAA,CAAiB,iBAAmB,CAAA,IAAA,CAAK,IAAI,CAAA,CACjE,MAAM,IAAI,KAAMD,CAAAA,CAAO,CACzB,CACF,CACF;;0KCzOa,MAAA,OAAA,SAAgB+O,cAAS,CAWpC,WAAA,CAAmBxP,CAAoBC,CAAAA,CAAAA,CAAgB,CACrD,KAAA,CAAMD,EAAMC,CAAM,CAAA,CADD,UAAAD,CAAoB,CAAA,IAAA,CAAA,MAAA,CAAAC,EAVvCC,GAAA,CAAA,IAAA,CAAO,cAAc,IAAI,GAAA,CAAA,CACzBA,IAAA,IAAO,CAAA,QAAA,CAAS,IAAIsE,gBACpBtE,CAAAA,CAAAA,GAAAA,CAAA,KAAO,MAAOuP,CAAAA,eAAAA,CAAAA,CACdvP,GAAA,CAAA,IAAA,CAAO,SAAUwP,CAAAA,uBAAAA,CAAAA,CAEjBxP,IAAA,IAAQ,CAAA,QAAA,CAAoC,EAC5CA,CAAAA,CAAAA,GAAAA,CAAA,KAAQ,aAAc,CAAA,CAAA,CAAA,CAAA,CAEtBA,IAAA,IAAQ,CAAA,eAAA,CAAgBG,qBAOxBH,GAAA,CAAA,IAAA,CAAO,OAAyB,SAAY,CACrC,KAAK,WACR,GAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,aAAa,CAAA,CAC/B,MAAM,IAAK,CAAA,OAAA,GACX,IAAK,CAAA,MAAA,CAAO,QAASyP,CAAe,EAAA,IAAA,CAAK,YAAY,GAAIA,CAAAA,CAAAA,CAAW,OAAQA,CAAU,CAAC,EACvF,IAAK,CAAA,MAAA,CAAS,EACd,CAAA,IAAA,CAAK,sBAAuB,EAAA,CAC5B,IAAK,CAAA,WAAA,CAAc,IAEvB,CAsBAzP,CAAAA,CAAAA,GAAAA,CAAA,KAAO,KAAwBM,CAAAA,CAAAA,EAAQ,CACrC,GAAI,CACF,MAAMoP,CAAS,CAAA,IAAA,CAAK,aAAapP,CAAG,CAAA,CAEpC,OAAO,OADY,IAAA,CAAK,cAAcoP,CAAM,CAAA,EACf,WAC/B,CAAA,MAAS9L,CAAG,CAAA,CAEV,OAAO,CACT,CAAA,CACF,GAEA5D,GAAA,CAAA,IAAA,CAAO,MAAuB,CAACM,CAAAA,CAAKgK,IAAW,CAC7C,IAAA,CAAK,eACL,CAAA,MAAMoF,EAAS,IAAK,CAAA,YAAA,CAAapP,CAAG,CAC9BmP,CAAAA,CAAAA,CAAa,CAAE,MAAA,CAAAC,CAAQ,CAAA,MAAA,CAAApF,CAAO,CACpC,CAAA,IAAA,CAAK,YAAY,GAAIoF,CAAAA,CAAAA,CAAQD,CAAU,CACvC,CAAA,IAAA,CAAK,YAAYC,CAAQD,CAAAA,CAAU,EACnC,IAAK,CAAA,MAAA,CAAO,KAAKf,cAAe,CAAA,OAAA,CAAS,CACvC,MAAAgB,CAAAA,CAAAA,CACA,UAAAD,CAAAA,CACF,CAAyB,EAC3B,GAEAzP,GAAA,CAAA,IAAA,CAAO,MAAwBM,CAAQ,EAAA,CACrC,KAAK,aAAc,EAAA,CACnB,MAAMoP,CAAS,CAAA,IAAA,CAAK,aAAapP,CAAG,CAAA,CACpC,OAAO,IAAK,CAAA,aAAA,CAAcoP,CAAM,CAClC,CAAA,CAAA,CAEA1P,GAAA,CAAA,IAAA,CAAO,KAAwBM,CAAAA,CAAAA,EAAQ,CAGrC,GAFA,IAAA,CAAK,eACU,CAAA,IAAA,CAAK,IAAIA,CAAG,CAAA,CACf,CACV,MAAMoP,CAAAA,CAAS,KAAK,YAAapP,CAAAA,CAAG,EAC9BmP,CAAa,CAAA,IAAA,CAAK,cAAcC,CAAM,CAAA,CAC5C,IAAK,CAAA,WAAA,CAAY,MAAOA,CAAAA,CAAM,EAC9B,IAAK,CAAA,MAAA,CAAO,KAAKhB,cAAe,CAAA,OAAA,CAAS,CACvC,MAAAgB,CAAAA,CAAAA,CACA,WAAAD,CACF,CAAyB,EAC3B,CACF,CAAA,CAAA,CAEAzP,IAAA,IAAO,CAAA,IAAA,CAAqB,CAAC4F,CAAOC,CAAAA,CAAAA,GAAa,CAC/C,IAAA,CAAK,MAAO,CAAA,EAAA,CAAGD,EAAOC,CAAQ,EAChC,GAEA7F,GAAA,CAAA,IAAA,CAAO,OAAyB,CAAC4F,CAAAA,CAAOC,IAAa,CACnD,IAAA,CAAK,OAAO,IAAKD,CAAAA,CAAAA,CAAOC,CAAQ,EAClC,CAAA,CAAA,CAEA7F,IAAA,IAAO,CAAA,KAAA,CAAuB,CAAC4F,CAAAA,CAAOC,CAAa,GAAA,CACjD,KAAK,MAAO,CAAA,GAAA,CAAID,EAAOC,CAAQ,EACjC,GAEA7F,GAAA,CAAA,IAAA,CAAO,iBAA6C,CAAC4F,CAAAA,CAAOC,IAAa,CACvE,IAAA,CAAK,OAAO,cAAeD,CAAAA,CAAAA,CAAOC,CAAQ,EAC5C,CAAA,CAAA,CA3FE,IAAK,CAAA,MAAA,CAASpF,0BAAoBV,CAAAA,CAAAA,CAAQ,KAAK,IAAI,EACrD,CAaA,IAAI,OAAA,EAAkB,CACpB,OAAOW,uBAAAA,CAAiB,KAAK,MAAM,CACrC,CAEA,IAAI,UAAA,EAAa,CACf,OAAO,IAAA,CAAK,cAAgB,IAAK,CAAA,OAAA,CAAU,IAAK,CAAA,IAAA,CAAK,mBAAsB,CAAA,IAAA,CAAO,KAAK,IACzF,CAEA,IAAI,MAAiB,EAAA,CACnB,OAAO,IAAK,CAAA,WAAA,CAAY,IAC1B,CAEA,IAAI,MAAiB,CACnB,OAAO,MAAM,IAAK,CAAA,IAAA,CAAK,YAAY,IAAK,EAAC,CAC3C,CAEA,IAAI,MAAA,EAAoC,CACtC,OAAO,KAAA,CAAM,KAAK,IAAK,CAAA,WAAA,CAAY,QAAQ,CAC7C,CA+DQ,YAAaJ,CAAAA,CAAAA,CAAsB,CACzC,GAAI,OAAOA,GAAQ,QACjB,CAAA,OAAOqP,wBAAkBrP,CAAG,CAAA,CACvB,GAAI,OAAOA,CAAQ,EAAA,QAAA,CACxB,OAAOsP,oBAAetP,CAAAA,CAAG,EAE3B,KAAM,CAAE,QAAAC,CAAQ,CAAA,CAAIC,uBAAiB,cAAgB,CAAA,CAAA,aAAA,EAAgB,OAAOF,CAAG,CAAA,CAAE,EACjF,MAAM,IAAI,MAAMC,CAAO,CACzB,CAEA,MAAc,cAAesP,CAAAA,CAAAA,CAAuD,CAClF,MAAM,IAAA,CAAK,KAAK,OAAQ,CAAA,OAAA,CAAmC,KAAK,UAAYA,CAAAA,CAAW,EACzF,CAEA,MAAc,gBAAiE,CAE7E,OADoB,MAAM,IAAK,CAAA,IAAA,CAAK,QAAQ,OAAmC,CAAA,IAAA,CAAK,UAAU,CAEhG,CAEA,MAAc,SAAU,CACtB,MAAM,KAAK,cAAe,CAAA,IAAA,CAAK,MAAM,CACrC,CAAA,IAAA,CAAK,OAAO,IAAKnB,CAAAA,cAAAA,CAAe,IAAI,EACtC,CAEA,MAAc,OAAU,EAAA,CACtB,GAAI,CACF,MAAM7F,CAAY,CAAA,MAAM,IAAK,CAAA,cAAA,GAE7B,GADI,OAAOA,GAAc,WACrB,EAAA,CAACA,EAAU,MAAQ,CAAA,OACvB,GAAI,IAAK,CAAA,WAAA,CAAY,KAAM,CACzB,KAAM,CAAE,OAAAtI,CAAAA,CAAQ,EAAIC,sBAAiB,CAAA,uBAAA,CAAyB,IAAK,CAAA,IAAI,CACvE,CAAA,MAAA,IAAA,CAAK,OAAO,KAAMD,CAAAA,CAAO,EACnB,IAAI,KAAA,CAAMA,CAAO,CACzB,CACA,KAAK,MAASsI,CAAAA,CAAAA,CACd,KAAK,MAAO,CAAA,KAAA,CAAM,yCAAyC,IAAK,CAAA,IAAI,EAAE,CACtE,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAA,CAAM,SAAU,MAAQ,CAAA,SAAA,CAAW,YAAa,IAAK,CAAA,MAAO,CAAC,EACnF,CAAA,MAAS,EAAG,CACV,IAAA,CAAK,OAAO,KAAM,CAAA,CAAA,kCAAA,EAAqC,KAAK,IAAI,CAAA,CAAE,EAClE,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,CAAQ,EAC5B,CACF,CAEQ,aAAc6G,CAAAA,CAAAA,CAAyC,CAC7D,MAAMD,CAAAA,CAAa,KAAK,WAAY,CAAA,GAAA,CAAIC,CAAM,CAC9C,CAAA,GAAI,CAACD,CAAY,CAAA,CACf,KAAM,CAAE,OAAA,CAAAlP,CAAQ,CAAIC,CAAAA,sBAAAA,CAAiB,iBAAmB,CAAA,CAAA,EAAG,IAAK,CAAA,IAAI,KAAKkP,CAAM,CAAA,CAAE,EACjF,MAAK,IAAA,CAAA,MAAA,CAAO,KAAKnP,CAAO,CAAA,CAClB,IAAI,KAAMA,CAAAA,CAAO,CACzB,CACA,OAAOkP,CACT,CAEQ,WAAA,CAAYC,EAAgBD,CAA2C,CAAA,CAC7E,KAAM,CAAE,MAAAnF,CAAAA,CAAO,EAAImF,CACCjL,CAAAA,kBAAAA,CAAc8F,CAAM,CAAI,CAAA,IAAA,CAAK,KAC9B,EAAA,CAAA,EAAG,KAAK,MAAOoF,CAAAA,CAAAA,CAAQD,CAAU,EACtD,CAEQ,OAAOC,CAAgBD,CAAAA,CAAAA,CAA2C,CACxE,IAAK,CAAA,WAAA,CAAY,MAAOC,CAAAA,CAAM,CAC9B,CAAA,IAAA,CAAK,OAAO,IAAKhB,CAAAA,cAAAA,CAAe,QAAS,CACvC,MAAA,CAAAgB,EACA,UAAAD,CAAAA,CACF,CAAyB,EAC3B,CAEQ,kBAAyB,CAE1B,IAAA,CAAK,KAAK,OAAQ,CAAA,SAAA,EACvB,KAAK,WAAY,CAAA,OAAA,CAAQ,CAACA,CAAAA,CAAYC,CAAW,GAAA,IAAA,CAAK,YAAYA,CAAQD,CAAAA,CAAU,CAAC,EACvF,CAEQ,wBAA+B,CACrC,IAAA,CAAK,KAAK,SAAU,CAAA,EAAA,CAAGjJ,2BAAiB,KAAO,CAAA,IAAM,KAAK,gBAAiB,EAAC,EAC5E,IAAK,CAAA,MAAA,CAAO,EAAGkI,CAAAA,cAAAA,CAAe,OAAU7G,CAAAA,CAAAA,EAAuC,CAC7E,MAAMC,CAAAA,CAAY4G,eAAe,OACjC,CAAA,IAAA,CAAK,OAAO,IAAK,CAAA,CAAA,SAAA,EAAY5G,CAAS,CAAE,CAAA,CAAA,CACxC,KAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAM,CAAA,OAAA,CAAS,MAAOA,CAAW,CAAA,IAAA,CAAMD,CAAa,CAAC,CACzE,CAAA,IAAA,CAAK,UACP,CAAC,EACD,IAAK,CAAA,MAAA,CAAO,GAAG6G,cAAe,CAAA,OAAA,CAAUoB,GAAuC,CAC7E,MAAMhI,EAAY4G,cAAe,CAAA,OAAA,CACjC,KAAK,MAAO,CAAA,IAAA,CAAK,YAAY5G,CAAS,CAAA,CAAE,CACxC,CAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,CAAE,IAAM,CAAA,OAAA,CAAS,MAAOA,CAAW,CAAA,IAAA,CAAMgI,CAAa,CAAC,CAAA,CACzE,KAAK,OAAQ,GACf,CAAC,CACD,CAAA,IAAA,CAAK,OAAO,EAAGpB,CAAAA,cAAAA,CAAe,QAAU3G,CAAuC,EAAA,CAC7E,MAAMD,CAAAA,CAAY4G,cAAe,CAAA,OAAA,CACjC,KAAK,MAAO,CAAA,IAAA,CAAK,YAAY5G,CAAS,CAAA,CAAE,EACxC,IAAK,CAAA,MAAA,CAAO,MAAM,CAAE,IAAA,CAAM,QAAS,KAAOA,CAAAA,CAAAA,CAAW,KAAMC,CAAa,CAAC,EACzE,IAAK,CAAA,OAAA,GACP,CAAC,EACH,CAEQ,eAAgB,CACtB,GAAI,CAAC,IAAK,CAAA,WAAA,CAAa,CACrB,KAAM,CAAE,QAAAxH,CAAQ,CAAA,CAAIC,uBAAiB,iBAAmB,CAAA,IAAA,CAAK,IAAI,CACjE,CAAA,MAAM,IAAI,KAAMD,CAAAA,CAAO,CACzB,CACF,CACF;;oKClMa,MAAA,MAAA,SAAewP,aAAQ,CAUlC,YAAmBjQ,CAAoBC,CAAAA,CAAAA,CAAuBiQ,EAAyB,CACrF,KAAA,CAAMlQ,EAAMC,CAAQiQ,CAAAA,CAAK,CADR,CAAA,IAAA,CAAA,IAAA,CAAAlQ,CAAoB,CAAA,IAAA,CAAA,MAAA,CAAAC,EAAuB,IAAAiQ,CAAAA,KAAAA,CAAAA,CAAAA,CAT9DhQ,CAAA,CAAA,IAAA,CAAO,MAAOiQ,CAAAA,cAAAA,CAAAA,CACdjQ,EAAA,IAAQ,CAAA,iBAAA,CAAA,CACRA,CAAA,CAAA,IAAA,CAAQ,UACRA,CAAAA,CAAAA,CAAAA,CAAA,KAAQ,aAAckQ,CAAAA,gBAAAA,CAAAA,CACtBlQ,EAAA,IAAQ,CAAA,eAAA,CAAgBG,qBACxBH,CAAA,CAAA,IAAA,CAAQ,SAAUmQ,CAAAA,YAAAA,CAAAA,CAClBnQ,CAAA,CAAA,IAAA,CAAQ,aACRA,CAAA,CAAA,IAAA,CAAQ,cAgBRA,CAAAA,CAAAA,CAAAA,CAAA,IAAO,CAAA,MAAA,CAAO,SAAY,CApD5B,IAAA2E,CAqDQ,CAAA,IAAA,CAAK,QACT,GAAA,IAAA,CAAK,UAAY,MAAM,IAAA,CAAK,MAAM,OAAQ,CAAA,IAAA,CAAK,QAAQ,CACnD,CAAA,IAAA,CAAK,SAAaH,EAAAA,kBAAAA,CAAAA,CAAcG,CAAA,CAAA,IAAA,CAAK,YAAL,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAgB,SAAS,CAAA,CAAI,IAAK,CAAA,GAAA,KACpE,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,8BAA8B,CAChD,CAAA,MAAM,KAAK,eAAgB,EAAA,CAAA,EAE/B,GAEA3E,CAAA,CAAA,IAAA,CAAO,WAAgC,MAAOmC,CAAAA,EAAW,CACvD,GAAI,CAACiO,eAAAA,IAAe,IAAK,CAAA,QAAA,CAAU,OACnC,MAAMC,CAAS,CAAA,MAAA,CAAO,SAAS,MACzB,CAAA,CAAE,EAAArL,CAAAA,CAAAA,CAAI,WAAAsL,CAAAA,CAAY,EAAInO,CACtBoO,CAAAA,CAAAA,CAAM,GAAG,IAAK,CAAA,WAAW,0BAA0B,IAAK,CAAA,IAAA,CAAK,SAAS,CAAA,QAAA,EAAWF,CAAM,CAAA,IAAA,EAAOrL,CAAE,CAAgBsL,aAAAA,EAAAA,CAAW,CACjI,CAAA,CAAA,GAAI,CACF,MAAME,EAAWC,yBAAY,EAAA,CACvBC,CAAe,CAAA,IAAA,CAAK,eAAgBhM,CAAAA,eAAAA,CAAa,CAAC,CAClDiM,CAAAA,CAAAA,CAAiB,MAAM,IAAI,OAAA,CAAQ,CAACvL,CAASK,CAAAA,CAAAA,GAAW,CAC5D,MAAMmL,CAAgB,CAAA,IAAM,CAC1B,MAAO,CAAA,mBAAA,CAAoB,SAAW/K,CAAAA,CAAQ,CAC9C2K,CAAAA,CAAAA,CAAS,KAAK,WAAYK,CAAAA,CAAM,CAChCpL,CAAAA,CAAAA,CAAO,qBAAqB,EAC9B,EACA,IAAK,CAAA,eAAA,CAAgB,OAAO,gBAAiB,CAAA,OAAA,CAASmL,CAAa,CACnE,CAAA,MAAMC,CAASL,CAAAA,CAAAA,CAAS,aAAc,CAAA,QAAQ,EAC9CK,CAAO,CAAA,GAAA,CAAMN,EACbM,CAAO,CAAA,KAAA,CAAM,QAAU,MACvBA,CAAAA,CAAAA,CAAO,gBAAiB,CAAA,OAAA,CAASD,CAAe,CAAA,CAAE,OAAQ,IAAK,CAAA,eAAA,CAAgB,MAAO,CAAC,CAAA,CACvF,MAAM/K,CAAYD,CAAAA,CAAAA,EAAwB,CACxC,GAAKA,CAAM,CAAA,IAAA,EACP,OAAOA,CAAM,CAAA,IAAA,EAAS,QAC1B,CAAA,GAAI,CACF,MAAMkL,EAAO,IAAK,CAAA,KAAA,CAAMlL,CAAM,CAAA,IAAI,CAClC,CAAA,GAAIkL,EAAK,IAAS,GAAA,oBAAA,CAAsB,CAEtC,GADgBC,aAAAA,CAAUD,EAAK,WAAW,CAAA,CAC9B,OAAQ,CAAA,EAAA,GAAO9L,CAAI,CAAA,OAE/B,cAAc0L,CAAY,CAAA,CAC1BF,CAAS,CAAA,IAAA,CAAK,WAAYK,CAAAA,CAAM,EAChC,IAAK,CAAA,eAAA,CAAgB,MAAO,CAAA,mBAAA,CAAoB,OAASD,CAAAA,CAAa,EACtE,MAAO,CAAA,mBAAA,CAAoB,UAAW/K,CAAQ,CAAA,CAC9CT,EAAQ0L,CAAK,CAAA,WAAA,GAAgB,IAAO,CAAA,EAAA,CAAKA,CAAK,CAAA,WAAW,EAC3D,CACF,CAAA,MAASlN,CAAG,CAAA,CACV,IAAK,CAAA,MAAA,CAAO,KAAKA,CAAC,EACpB,CACF,CAAA,CACA4M,CAAS,CAAA,IAAA,CAAK,YAAYK,CAAM,CAAA,CAChC,OAAO,gBAAiB,CAAA,SAAA,CAAWhL,EAAU,CAAE,MAAA,CAAQ,IAAK,CAAA,eAAA,CAAgB,MAAO,CAAC,EACtF,CAAC,CAAA,CACD,OAAK,IAAA,CAAA,MAAA,CAAO,KAAM,CAAA,iBAAA,CAAmB8K,CAAc,CAC5CA,CAAAA,CACT,CAAS/M,MAAAA,CAAAA,CAAG,CACV,IAAA,CAAK,OAAO,IAAKA,CAAAA,CAAC,EACpB,CACA,OAAO,EACT,CAEA5D,CAAAA,CAAAA,CAAAA,CAAA,IAAO,CAAA,SAAA,CAA8B,MAAOmC,CAAAA,EAAW,CACrD,GAAI,IAAA,CAAK,QAAU,CAAA,OAAO,EAC1B,CAAA,KAAM,CAAE,aAAA6O,CAAAA,CAAAA,CAAe,IAAA9M,CAAAA,CAAAA,CAAM,WAAA+M,CAAAA,CAAY,EAAI9O,CAC7C,CAAA,GAAI6O,IAAkB,EAAI,CAAA,CACxB,KAAK,MAAO,CAAA,KAAA,CAAM,2CAA2C,CAAA,CAC7D,MACF,CAEA,GAAIA,CAAe,CAAA,CAEjB,GADgBD,aAAAA,CAAUC,CAAa,CAAA,CAC3B,QAAQ,EAAOC,GAAAA,CAAAA,CAAa,OACxC,MAAMC,CAAa,CAAA,MAAM,KAAK,qBAAsBF,CAAAA,CAAa,EACjE,GAAIE,CAAAA,CAAY,CACd,GAAI,CAACA,CAAW,CAAA,UAAA,CAAY,CAC1B,IAAA,CAAK,OAAO,IAAK,CAAA,mDAAmD,CACpE,CAAA,MACF,CACA,OAAOA,CACT,CACF,CACA,GAAI,CAAChN,CAAM,CAAA,OACX,MAAMiN,CAAY,CAAA,IAAA,CAAK,aAAahP,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,EAAQ,SAAS,CAAA,CACrD,OAAO,IAAA,CAAK,gBAAiB+B,CAAAA,CAAAA,CAAMiN,CAAS,CAC9C,CAAA,CAAA,CAMAnR,EAAA,IAAQ,CAAA,kBAAA,CAAmB,MAAOgR,CAAuBI,CAAAA,CAAAA,GAAgB,CACvE,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,0BAA0BJ,CAAa,CAAA,WAAA,EAAcI,CAAG,CAAE,CAAA,CAAA,CAE5E,MAAMC,CAAU,CAAA,IAAA,CAAK,eAAgB3M,CAAAA,eAAAA,CAAa,CAAC,CAAA,CAC7C4B,EAAS,MAAM,KAAA,CAAM,CAAG8K,EAAAA,CAAG,CAAgBJ,aAAAA,EAAAA,CAAa,oBAAqB,CACjF,MAAA,CAAQ,IAAK,CAAA,eAAA,CAAgB,MAC/B,CAAC,EACD,OAAaK,YAAAA,CAAAA,CAAO,EACb/K,CAAO,CAAA,MAAA,GAAW,IAAM,MAAMA,CAAAA,CAAO,IAAK,EAAA,CAAI,KACvD,CAAA,CAAA,CAAA,CAOAtG,EAAA,IAAQ,CAAA,cAAA,CAAgBmR,CAAuB,EAAA,CAC7C,IAAIC,CAAAA,CAAMD,GAAaG,aACvB,CAAA,OAAKC,mBAAoB,CAAA,QAAA,CAASH,CAAG,CAAA,GACnC,KAAK,MAAO,CAAA,IAAA,CACV,eAAeA,CAAG,CAAA,mDAAA,EAAsDE,aAAa,CACvF,CAAA,CAAA,CACAF,CAAME,CAAAA,aAAAA,CAAAA,CAEDF,CACT,CAAA,CAAA,CAEApR,EAAA,IAAQ,CAAA,gBAAA,CAAiB,SAAY,CACnC,GAAI,CACF,KAAK,MAAO,CAAA,KAAA,CAAM,CAA6B,0BAAA,EAAA,IAAA,CAAK,WAAW,CAAA,CAAE,EACjE,MAAMqR,CAAAA,CAAU,KAAK,eAAgBjT,CAAAA,iBAAY,EAC3CkI,CAAS,CAAA,MAAM,KAAM,CAAA,CAAA,EAAG,IAAK,CAAA,WAAW,cAAe,CAC3D,MAAA,CAAQ,IAAK,CAAA,eAAA,CAAgB,MAC/B,CAAC,EACD,OAAa+K,YAAAA,CAAAA,CAAO,CACZ,CAAA,MAAM/K,CAAO,CAAA,IAAA,EACvB,CAAS1C,MAAAA,CAAAA,CAAG,CACV,IAAK,CAAA,MAAA,CAAO,KAAKA,CAAC,EACpB,CAEF,CAAA,CAAA,CAEA5D,CAAA,CAAA,IAAA,CAAQ,mBAAmB,MAAOgC,CAAAA,EAAmB,CACnD,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,yCAA0CA,CAAS,CAAA,CACrE,MAAM,IAAA,CAAK,KAAM,CAAA,OAAA,CAAQ,KAAK,QAAUA,CAAAA,CAAS,EACjD,IAAK,CAAA,SAAA,CAAYA,EACnB,CAEAhC,CAAAA,CAAAA,CAAAA,CAAA,IAAQ,CAAA,iBAAA,CAAkB,SAAY,CACpC,KAAK,MAAO,CAAA,KAAA,CAAM,4CAA4C,CAAA,CAC9D,MAAM,IAAA,CAAK,MAAM,UAAW,CAAA,IAAA,CAAK,QAAQ,CAAA,CACzC,IAAK,CAAA,SAAA,CAAY,OACnB,CAEAA,CAAAA,CAAAA,CAAAA,CAAA,KAAQ,uBAAwB,CAAA,MAAOiG,GAAwB,CAC7D,MAAM3F,CAAM,CAAA,MAAM,IAAK,CAAA,YAAA,GACvB,GAAI,CACF,GAAIA,CAAAA,CAEF,OADmB,IAAA,CAAK,oBAAoB2F,CAAa3F,CAAAA,CAAG,CAGhE,CAAA,MAASsD,CAAG,CAAA,CACV,KAAK,MAAO,CAAA,KAAA,CAAMA,CAAC,CACnB,CAAA,IAAA,CAAK,OAAO,IAAK,CAAA,8BAA8B,EACjD,CACA,MAAM4N,CAAAA,CAAS,MAAM,IAAK,CAAA,wBAAA,EAC1B,CAAA,GAAI,CACF,GAAIA,EAEF,OADmB,IAAA,CAAK,mBAAoBvL,CAAAA,CAAAA,CAAauL,CAAM,CAGnE,OAAS5N,CAAG,CAAA,CACV,KAAK,MAAO,CAAA,KAAA,CAAMA,CAAC,CACnB,CAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAK,8BAA8B,EACjD,CAEF,CAEA5D,CAAAA,CAAAA,CAAAA,CAAA,IAAQ,CAAA,cAAA,CAAe,SACjB,IAAA,CAAK,UAAkB,IAAK,CAAA,SAAA,CACzB,MAAM,IAAA,CAAK,wBAAyB,EAAA,CAAA,CAG7CA,EAAA,IAAQ,CAAA,0BAAA,CAA2B,SAAY,CAC7C,GAAI,KAAK,YACP,CAAA,OAAA,MAAM,IAAK,CAAA,YAAA,CACJ,IAAK,CAAA,SAAA,CAEd,KAAK,YAAe,CAAA,IAAI,OAAQ,CAAA,MAAOoF,CAAY,EAAA,CACjD,MAAM9E,CAAM,CAAA,MAAM,IAAK,CAAA,cAAA,EAClBA,CAAAA,CAAAA,GACL,MAAM,IAAK,CAAA,gBAAA,CAAiBA,CAAG,CAC/B8E,CAAAA,CAAAA,CAAQ9E,CAAG,CACb,EAAA,CAAC,CACD,CAAA,MAAMA,CAAM,CAAA,MAAM,KAAK,YACvB,CAAA,OAAA,IAAA,CAAK,YAAe,CAAA,KAAA,CAAA,CACbA,CACT,CAAA,CAAA,CAEAN,EAAA,IAAQ,CAAA,qBAAA,CAAsB,CAACiG,CAAAA,CAAqB3F,CAAa,GAAA,CAC/D,MAAMgG,CAASmL,CAAAA,mBAAAA,CAA0BxL,EAAa3F,CAAI,CAAA,SAAS,EAC7D4Q,CAAa,CAAA,CACjB,UAAY1M,CAAAA,kBAAAA,CAAc8B,CAAO,CAAA,GAAG,EAAI,IAAK,CAAA,GAAA,EAC7C,CAAA,OAAA,CAASA,CACX,CAAA,CAEA,GAAI4K,CAAW,CAAA,UAAA,CACb,MAAK,IAAA,CAAA,MAAA,CAAO,IAAK,CAAA,kCAAkC,EAC7C,IAAI,KAAA,CAAM,yBAAyB,CAG3C,CAAA,OAAO,CACL,MAAQA,CAAAA,CAAAA,CAAW,OAAQ,CAAA,MAAA,CAC3B,MAAQA,CAAAA,CAAAA,CAAW,QAAQ,MAC3B,CAAA,UAAA,CAAYA,CAAW,CAAA,OAAA,CAAQ,UACjC,CACF,GAvNE,IAAK,CAAA,MAAA,CAASzQ,0BAAoBV,CAAAA,CAAAA,CAAQ,IAAK,CAAA,IAAI,EACnD,IAAK,CAAA,eAAA,CAAkB,IAAI,eAC3B,CAAA,IAAA,CAAK,SAAW2R,eAAU,EAAA,CAC1B,IAAK,CAAA,IAAA,GACP,CAEA,IAAI,QAAmB,EAAA,CACrB,OACE,IAAA,CAAK,aAAgB,CAAA,IAAA,CAAK,QAAU,IAAK,CAAA,IAAA,CAAK,mBAAsB,CAAA,qBAExE,CAqFA,IAAI,SAAkB,CACpB,OAAOhR,wBAAiB,IAAK,CAAA,MAAM,CACrC,CAaQ,eAAA,CAAgBiR,CAAe,CAAA,CACrC,OAAK,IAAA,CAAA,eAAA,CAAkB,IAAI,eACpB,CAAA,UAAA,CAAW,IAAM,IAAA,CAAK,eAAgB,CAAA,KAAA,GAASnN,kBAAcmN,CAAAA,CAAK,CAAC,CAC5E,CAuGF;;kLC5Pa,UAAmBC,SAAAA,iBAAY,CAE1C,WAAmBC,CAAAA,CAAAA,CAA0B9R,EAAgB,CAC3D,KAAA,CAAM8R,EAAW9R,CAAM,CAAA,CADN,eAAA8R,CAA0B,CAAA,IAAA,CAAA,MAAA,CAAA9R,EAD7CC,CAAA,CAAA,IAAA,CAAgB,UAAU8R,YAM1B9R,CAAAA,CAAAA,CAAAA,CAAA,KAAO,qBAA0D,CAAA,MAAOmC,GAAW,CACjF,KAAM,CAAE,QAAA4P,CAAAA,CAAAA,CAAU,KAAAC,CAAAA,CAAAA,CAAO,gBAAAC,CAAAA,CAAAA,CAAkB,gBAAAC,CAAkB,CAAA,CAAA,CAAM,EAAI/P,CAEjEgQ,CAAAA,CAAAA,CAAU,GAAGC,QAAQ,CAAA,CAAA,EAAI,KAAK,SAAS,CAAA,QAAA,CAAA,CAE7C,MAAM,KAAMD,CAAAA,CAAAA,CAAS,CACnB,MAAQ,CAAA,MAAA,CACR,QAAS,CACP,cAAA,CAAgB,kBAClB,CACA,CAAA,IAAA,CAAM,KAAK,SAAU,CAAA,CACnB,UAAWJ,CACX,CAAA,IAAA,CAAME,EACN,KAAAD,CAAAA,CAAAA,CACA,WAAYE,CACd,CAAC,CACH,CAAC,EACH,GApBE,IAAK,CAAA,MAAA,CAASzR,2BAAoBV,CAAQ,CAAA,IAAA,CAAK,OAAO,EACxD,CAoBF;;AC7BA,IAAA,CAAA,CAAA,MAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,qBAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,cAAA,CAAAsS,GAAA,CAAA,MAAA,CAAA,SAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,IAAA1S,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAcO,MAAM,WAAoB2S,SAAAA,kBAAa,CAM5C,WAAmBxS,CAAAA,CAAAA,CAAoBC,CAAgBwS,CAAAA,CAAAA,CAAmB,GAAM,CAC9E,KAAA,CAAMzS,CAAMC,CAAAA,CAAAA,CAAQwS,CAAgB,CADnB,CAAA,IAAA,CAAA,IAAA,CAAAzS,EAAoB,IAAAC,CAAAA,MAAAA,CAAAA,CAAAA,CALvCC,IAAA,IAAgB,CAAA,SAAA,CAAUwS,sBAC1BxS,CAAAA,CAAAA,GAAAA,CAAA,KAAiB,eAAgBG,CAAAA,mBAAAA,CAAAA,CACjCH,GAAA,CAAA,IAAA,CAAiB,iBAAiByS,sBAClCzS,CAAAA,CAAAA,GAAAA,CAAA,IAAQ,CAAA,QAAA,CAAS,IAAI,GACrBA,CAAAA,CAAAA,GAAAA,CAAA,KAAQ,eAAgB,CAAA,CAAA,CAAA,CAAA,CAsBxBA,IAAA,IAAO,CAAA,MAAA,CAA6B,SAAY,CAC9C,GAAI,CAAA0R,eAAAA,EACJ,CAAA,GAAI,CACF,MAAMgB,CAAAA,CAAY,CAChB,OAAA,CAASC,cACT,CAAA,SAAA,CAAW,KAAK,GAAI,EAAA,CACpB,OAAQ,IAAK,CAAA,YAAA,EACb,CAAA,KAAA,CAAO,CACL,KAAO,CAAA,MAAA,CACP,IAAM,CAAA,EAAA,CACN,WAAY,CACV,SAAA,CAAW,MAAM,IAAA,CAAK,KAAK,MAAO,CAAA,WAAA,GAClC,UAAYC,CAAAA,cAAAA,CACV,KAAK,IAAK,CAAA,OAAA,CAAQ,QAClB,CAAA,IAAA,CAAK,KAAK,OAAQ,CAAA,OAAA,CAClB5H,mBACF,CACF,CACF,CACF,CAAA,CACA,MAAM,IAAA,CAAK,UAAU,CAAC0H,CAAS,CAAwC,EACzE,CAAA,MAASzP,EAAO,CACd,IAAA,CAAK,MAAO,CAAA,IAAA,CAAKA,CAAK,EACxB,CACF,CAEAjD,CAAAA,CAAAA,GAAAA,CAAA,KAAO,aAA4CmC,CAAAA,CAAAA,EAAW,CAC5D,KAAM,CACJ,KAAAyD,CAAAA,CAAAA,CAAQ,QACR,IAAAnD,CAAAA,CAAAA,CAAO,GACP,UAAY,CAAA,CAAE,KAAAX,CAAAA,CAAAA,CAAO,MAAA+Q,CAAM,CAC7B,CAAI1Q,CAAAA,CAAAA,CACE2Q,EAAUH,YAAO,EAAA,CACjBI,CAAW,CAAA,IAAA,CAAK,KAAK,SAAa,EAAA,EAAA,CAClCC,EAAY,IAAK,CAAA,GAAA,GASjBC,CAAWtN,CAAAA,CAAAA,CAAA,CACf,OAAA,CAAAmN,EACA,SAAAE,CAAAA,CAAAA,CACA,KAXY,CAAA,CACZ,MAAApN,CACA,CAAA,IAAA,CAAAnD,CACA,CAAA,UAAA,CAAY,CACV,KAAAX,CAAAA,CAAAA,CACA,MAAA+Q,CACF,CACF,EAKE,QAAAE,CAAAA,CAAAA,CACA,MAAQ,CAAA,IAAA,CAAK,cACV,CAAA,CAAA,IAAA,CAAK,UAAWD,CAAAA,CAAO,GAE5B,OAAI,IAAA,CAAK,gBACP,GAAA,IAAA,CAAK,OAAO,GAAIA,CAAAA,CAAAA,CAASG,CAAQ,CACjC,CAAA,IAAA,CAAK,cAAgB,CAGhBA,CAAAA,CAAAA,CAAAA,CACT,CAEAjT,CAAAA,CAAAA,GAAAA,CAAA,KAAO,UAAsCmC,CAAAA,CAAAA,EAAW,CACtD,KAAM,CAAE,OAAA2Q,CAAAA,CAAAA,CAAS,KAAAhR,CAAAA,CAAM,EAAIK,CAC3B,CAAA,GAAI2Q,EACF,OAAO,IAAA,CAAK,OAAO,GAAIA,CAAAA,CAAO,CAEhC,CAAA,MAAMlN,EAAQ,KAAM,CAAA,IAAA,CAAK,IAAK,CAAA,MAAA,CAAO,QAAQ,CAAA,CAAE,IAC5CA,CAAAA,CAAAA,EAAUA,EAAM,KAAM,CAAA,UAAA,CAAW,QAAU9D,CAC9C,CAAA,CAEA,GAAK8D,CAEL,CAAA,OAAOD,CAAA,CAAA,CAAA,CAAA,EAAA,CACFC,GACA,IAAK,CAAA,UAAA,CAAWA,CAAM,CAAA,OAAO,EAEpC,CAEA5F,CAAAA,CAAAA,GAAAA,CAAA,IAAO,CAAA,aAAA,CAA4CmC,GAAW,CAC5D,KAAM,CAAE,OAAA2Q,CAAAA,CAAQ,EAAI3Q,CACpB,CAAA,IAAA,CAAK,MAAO,CAAA,MAAA,CAAO2Q,CAAO,CAC1B,CAAA,IAAA,CAAK,aAAgB,CAAA,CAAA,EACvB,GAEA9S,GAAA,CAAA,IAAA,CAAQ,mBAAoB,CAAA,IAAM,CAChC,IAAK,CAAA,IAAA,CAAK,UAAU,EAAGwG,CAAAA,0BAAAA,CAAiB,MAAO,SAAY,CACrD,IAAK,CAAA,aAAA,EAAe,MAAM,IAAK,CAAA,OAAA,EAEnC,CAAA,IAAA,CAAK,OAAO,OAASZ,CAAAA,CAAAA,EAAU,CAE3BsN,oBAAAA,CAAgB,KAAK,GAAI,EAAC,EAAIA,oBAAgBtN,CAAAA,CAAAA,CAAM,SAAS,CAC7DuN,CAAAA,+BAAAA,GAEA,IAAK,CAAA,MAAA,CAAO,OAAOvN,CAAM,CAAA,OAAO,CAChC,CAAA,IAAA,CAAK,cAAgB,CAEzB,CAAA,EAAA,CAAC,EACH,CAAC,EACH,CAEA5F,CAAAA,CAAAA,GAAAA,CAAA,KAAQ,YAAc8S,CAAAA,CAAAA,GACb,CACL,QAAWD,CAAAA,CAAAA,EAAkB,IAAK,CAAA,QAAA,CAASC,EAASD,CAAK,CAAA,CACzD,QAAWO,CAAAA,CAAAA,EAAsB,KAAK,QAASN,CAAAA,CAAAA,CAASM,CAAS,CACnE,IAGFpT,GAAA,CAAA,IAAA,CAAQ,WAAW,CAAC8S,CAAAA,CAAiBD,IAAkB,CACrD,MAAMjN,CAAQ,CAAA,IAAA,CAAK,OAAO,GAAIkN,CAAAA,CAAO,CAChClN,CAAAA,CAAAA,GACLA,EAAM,KAAM,CAAA,UAAA,CAAW,KAAM,CAAA,IAAA,CAAKiN,CAAK,CACvC,CAAA,IAAA,CAAK,OAAO,GAAIC,CAAAA,CAAAA,CAASlN,CAAK,CAC9B,CAAA,IAAA,CAAK,aAAgB,CAAA,CAAA,CAAA,EACvB,GAEA5F,GAAA,CAAA,IAAA,CAAQ,UAAW,CAAA,CAAC8S,EAAiBM,CAAsB,GAAA,CACzD,MAAMxN,CAAAA,CAAQ,KAAK,MAAO,CAAA,GAAA,CAAIkN,CAAO,CAChClN,CAAAA,CAAAA,GACLA,EAAM,KAAM,CAAA,IAAA,CAAOwN,CACnBxN,CAAAA,CAAAA,CAAM,UAAY,IAAK,CAAA,GAAA,EACvB,CAAA,IAAA,CAAK,OAAO,GAAIkN,CAAAA,CAAAA,CAASlN,CAAK,CAAA,CAC9B,KAAK,aAAgB,CAAA,CAAA,CAAA,EACvB,GAEA5F,GAAA,CAAA,IAAA,CAAQ,UAAU,SAAY,CAC5B,MAAM,IAAA,CAAK,KAAK,OAAQ,CAAA,OAAA,CAAQ,IAAK,CAAA,UAAA,CAAY,MAAM,IAAK,CAAA,IAAA,CAAK,MAAO,CAAA,MAAA,EAAQ,CAAC,CAAA,CACjF,KAAK,aAAgB,CAAA,CAAA,EACvB,GAEAA,GAAA,CAAA,IAAA,CAAQ,SAAU,CAAA,SAAY,CAC5B,GAAI,CACF,MAAMqT,CAAAA,CACH,MAAM,IAAK,CAAA,IAAA,CAAK,OAAQ,CAAA,OAAA,CAAkC,KAAK,UAAU,CAAA,EAAM,EAClF,CAAA,GAAI,CAACA,CAAO,CAAA,MAAA,CAAQ,OACpBA,CAAAA,CAAO,QAASzN,CAAU,EAAA,CACxB,IAAK,CAAA,MAAA,CAAO,IAAIA,CAAM,CAAA,OAAA,CAASD,CAAA,CAAA,CAAA,CAAA,EAAA,CAC1BC,GACA,IAAK,CAAA,UAAA,CAAWA,EAAM,OAAO,CAAA,CACjC,EACH,CAAC,EACH,CAAS3C,MAAAA,CAAAA,CAAO,CACd,IAAK,CAAA,MAAA,CAAO,IAAKA,CAAAA,CAAK,EACxB,CACF,CAAA,CAAA,CAEAjD,GAAA,CAAA,IAAA,CAAQ,SAAS,SAAY,CAG3B,GAFI,CAAC,IAAA,CAAK,kBAEN,IAAK,CAAA,MAAA,CAAO,IAAS,GAAA,CAAA,CAAG,OAE5B,MAAMsT,CAAAA,CAAyC,EAAC,CAEhD,SAAW,CAACC,CAAAA,CAAG3N,CAAK,CAAA,GAAK,KAAK,MACxBA,CAAAA,CAAAA,CAAM,MAAM,IACd0N,EAAAA,CAAAA,CAAa,KAAK1N,CAAK,CAAA,CAI3B,GAAI0N,CAAAA,CAAa,SAAW,CAE5B,CAAA,GAAI,CAEF,GAAA,CADiB,MAAM,IAAK,CAAA,SAAA,CAAUA,CAAY,CAAA,EACrC,GACX,IAAW1N,MAAAA,CAAAA,IAAS0N,EAClB,IAAK,CAAA,MAAA,CAAO,OAAO1N,CAAM,CAAA,OAAO,CAChC,CAAA,IAAA,CAAK,cAAgB,CAG3B,EAAA,CAAA,MAAS3C,CAAO,CAAA,CACd,KAAK,MAAO,CAAA,IAAA,CAAKA,CAAK,EACxB,CACF,CAEAjD,CAAAA,CAAAA,GAAAA,CAAA,KAAQ,WAAY,CAAA,MAAOqT,GAAqC,CAE9D,MAAMG,CAAW,CAAA,IAAA,CAAK,cAAiB,CAAA,EAAA,CAAK,aAQ5C,CAAA,OAPiB,MAAM,KACrB,CAAA,CAAA,EAAGC,qBAAqB,CAAA,WAAA,EAAc,KAAK,IAAK,CAAA,SAAS,wBAAwBzI,mBAAmB,CAAA,EAAGwI,CAAQ,CAC/G,CAAA,CAAA,CACE,MAAQ,CAAA,MAAA,CACR,KAAM,IAAK,CAAA,SAAA,CAAUH,CAAM,CAC7B,CACF,CAEF,CAAA,CAAA,CAEArT,GAAA,CAAA,IAAA,CAAQ,eAAe,IACd0T,oBAAAA,GAAiB,GA5MxB,CAAA,CAAA,IAAA,CAAK,OAASjT,0BAAoBV,CAAAA,CAAAA,CAAQ,IAAK,CAAA,OAAO,EACtD,IAAK,CAAA,gBAAA,CAAmBwS,CACpBA,CAAAA,CAAAA,CACF,KAAK,OAAQ,EAAA,CAAE,IAAK,CAAA,SAAY,CAC9B,MAAM,IAAA,CAAK,QACX,CAAA,IAAA,CAAK,oBACP,CAAC,CAGD,CAAA,IAAA,CAAK,UAET,CAEA,IAAI,UAAA,EAAa,CACf,OACE,IAAA,CAAK,aAAgB,CAAA,IAAA,CAAK,eAAiB,IAAK,CAAA,IAAA,CAAK,oBAAsB,IAAO,CAAA,IAAA,CAAK,OAE3F,CA6LF;;ACpOA,IAAA,CAAA,CAAA,MAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,qBAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,cAAA,CAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAmCa,MAAAoB,MAAA,SAAaC,WAAM,CAkC9B,WAAA,CAAY1R,CAA0B,CAAA,CArExC,IAAAyC,CAsEI,CAAA,KAAA,CAAMzC,CAAI,CAAA,CAlCZlC,EAAA,IAAgB,CAAA,UAAA,CAAW6T,aAC3B7T,CAAAA,CAAAA,CAAAA,CAAA,IAAgB,CAAA,SAAA,CAAUmQ,YAE1BnQ,CAAAA,CAAAA,CAAAA,CAAA,KAAgB,MAAsB8T,CAAAA,YAAAA,CAAAA,CACtC9T,CAAA,CAAA,IAAA,CAAgB,YAChBA,CAAA,CAAA,IAAA,CAAgB,WAChBA,CAAAA,CAAAA,CAAAA,CAAA,KAAgB,qBAChBA,CAAAA,CAAAA,CAAAA,CAAA,IAAO,CAAA,QAAA,CAA0B,IAAIsE,gBACrCtE,CAAAA,CAAAA,CAAAA,CAAA,IAAO,CAAA,QAAA,CAAA,CACPA,EAAA,IAAO,CAAA,WAAA,CAAA,CACPA,CAAA,CAAA,IAAA,CAAO,WACPA,CAAA,CAAA,IAAA,CAAO,QACPA,CAAAA,CAAAA,CAAAA,CAAA,KAAO,SACPA,CAAAA,CAAAA,CAAAA,CAAA,IAAO,CAAA,SAAA,CAAA,CACPA,EAAA,IAAO,CAAA,SAAA,CAAA,CACPA,CAAA,CAAA,IAAA,CAAO,WACPA,CAAA,CAAA,IAAA,CAAO,QACPA,CAAAA,CAAAA,CAAAA,CAAA,KAAO,YACPA,CAAAA,CAAAA,CAAAA,CAAA,IAAO,CAAA,uBAAA,CAAA,CACPA,EAAA,IAAO,CAAA,aAAA,CAAA,CAEPA,CAAA,CAAA,IAAA,CAAQ,cAAc,CACtBA,CAAAA,CAAAA,CAAAA,CAAAA,CAAA,IAAQ,CAAA,oBAAA,CAAA,CAyFRA,EAAA,IAAO,CAAA,IAAA,CAAK,CAAC+L,CAAAA,CAAWlG,IACf,IAAK,CAAA,MAAA,CAAO,EAAGkG,CAAAA,CAAAA,CAAMlG,CAAQ,CAGtC7F,CAAAA,CAAAA,CAAAA,CAAA,IAAO,CAAA,MAAA,CAAO,CAAC+L,CAAAA,CAAWlG,CACjB,GAAA,IAAA,CAAK,OAAO,IAAKkG,CAAAA,CAAAA,CAAMlG,CAAQ,CAAA,CAAA,CAGxC7F,EAAA,IAAO,CAAA,KAAA,CAAM,CAAC+L,CAAAA,CAAWlG,IAChB,IAAK,CAAA,MAAA,CAAO,GAAIkG,CAAAA,CAAAA,CAAMlG,CAAQ,CAGvC7F,CAAAA,CAAAA,CAAAA,CAAA,IAAO,CAAA,gBAAA,CAAiB,CAAC+L,CAAWlG,CAAAA,CAAAA,GAC3B,IAAK,CAAA,MAAA,CAAO,eAAekG,CAAMlG,CAAAA,CAAQ,CAKlD7F,CAAAA,CAAAA,CAAAA,CAAA,KAAO,kBAAmB,CAAA,CAAC,CACzB,KAAA,CAAA8B,EACA,OAAAvB,CAAAA,CAAAA,CACA,aAAAwT,CAAAA,CACF,IAIM,CACJ,GAAI,CAACjS,CAAAA,EAAS,CAACvB,CAAS,CAAA,OAExB,MAAM0B,CAAAA,CAAU,CACd,KAAAH,CAAAA,CAAAA,CACA,OAAAvB,CAAAA,CAAAA,CACA,YAAa,IAAK,CAAA,GAAA,EAClB,CAAA,aAAA,CAAe2H,gBAAgB,SACjC,CAAA,CAEA,IAAK,CAAA,OAAA,CAAQ,mBAAmBjG,CAAS,CAAA,CAAE,aAAA8R,CAAAA,CAAc,CAAC,EAC5D,CAAA,CAAA,CAjHE,IAAK,CAAA,SAAA,CAAY7R,GAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,SACvB,CAAA,IAAA,CAAK,UAAWA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAM,CAAA,QAAA,GAAYwH,0BAClC,IAAK,CAAA,mBAAA,CAAsBxH,CAAA,EAAA,IAAA,EAAAA,EAAM,mBAAsB,CAAA,CAAA,CAAA,EAAIA,CAAK,CAAA,mBAAmB,GAAK,EAExF,CAAA,MAAM8R,CAAgB3K,CAAAA,8BAAAA,CAAwB,CAC5C,KAAO,CAAA,OAAOnH,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,EAAM,MAAW,CAAA,EAAA,QAAA,EAAYA,CAAK,CAAA,MAAA,CAASA,EAAK,MAAS+R,CAAAA,YAAAA,CAAa,MACpF,CAAA,IAAA,CAAMH,YACR,CAAC,CAAA,CAEK,CAAE,MAAA,CAAA/T,EAAQ,qBAAAmU,CAAAA,CAAsB,CAAIC,CAAAA,6BAAAA,CAAuB,CAC/D,IAAMH,CAAAA,CAAAA,CACN,cAAgB9R,CAAAA,CAAAA,EAAA,YAAAA,CAAM,CAAA,qBAAA,CACtB,cAAgBA,CAAAA,CAAAA,EAAA,YAAAA,CAAM,CAAA,MACxB,CAAC,CAAA,CAED,KAAK,kBAAqBgS,CAAAA,CAAAA,CAAAA,CAEtBvP,CAAA,CAAA,IAAA,CAAK,qBAAL,IAAAA,EAAAA,CAAAA,CAAyB,yBAE3B,GAAA,MAAA,CAAO,0BAA4B,SAAY,CA1FrD,IAAAA,CAAAA,CAAAmB,CA6FYnB,CAAAA,CAAAA,CAAAA,CAAA,IAAK,CAAA,kBAAA,GAAL,MAAAA,CAAyB,CAAA,yBAAA,GAAA,CAC3BmB,CAAA,CAAA,IAAA,CAAK,qBAAL,IAAAA,EAAAA,CAAAA,CAAyB,yBAA0B,CAAA,CACjD,SAAU,MAAM,IAAA,CAAK,MAAO,CAAA,WAAA,EAC9B,CAEJ,CAAA,EAAA,CAAA,CAAA,CAGF,IAAK,CAAA,MAAA,CAASrF,2BAAoBV,CAAQ,CAAA,IAAA,CAAK,IAAI,CAAA,CACnD,KAAK,SAAY,CAAA,IAAIqU,mBACrB,CAAA,IAAA,CAAK,OAAS,IAAIC,MAAAA,CAAO,IAAM,CAAA,IAAA,CAAK,OAAQnS,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAM,CAAA,QAAQ,EAC1D,IAAK,CAAA,OAAA,CAAU,IAAIoS,cAAAA,CAAe,KAAM,IAAK,CAAA,MAAM,CACnD,CAAA,IAAA,CAAK,QAAU,IAAIC,OAAAA,CAAQ,IAAM,CAAA,IAAA,CAAK,MAAM,CAC5C,CAAA,IAAA,CAAK,OAAUrS,CAAAA,CAAAA,EAAA,MAAAA,CAAM,CAAA,OAAA,CACjBA,CAAK,CAAA,OAAA,CACL,IAAIsS,qBAAgB7O,CAAAA,CAAAA,CAAAA,CAAAA,CAAA,EAAK8O,CAAAA,oBAAAA,CAAAA,CAAyBvS,GAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,cAAgB,CAAA,CAAA,CAC5E,KAAK,OAAU,CAAA,IAAIwS,OAAQ,CAAA,CACzB,KAAM,IACN,CAAA,MAAA,CAAQ,IAAK,CAAA,MAAA,CACb,SAAU,IAAK,CAAA,QAAA,CACf,SAAW,CAAA,IAAA,CAAK,SAClB,CAAC,CAAA,CACD,IAAK,CAAA,OAAA,CAAU,IAAIC,OAAQ,CAAA,IAAA,CAAM,IAAK,CAAA,MAAM,EAC5C,IAAK,CAAA,MAAA,CAAS,IAAIC,MAAAA,CAAO,KAAM,IAAK,CAAA,MAAA,CAAQ,IAAK,CAAA,OAAO,EACxD,IAAK,CAAA,UAAA,CAAa,IAAIC,UAAAA,CAAW,KAAK,SAAa,EAAA,EAAA,CAAI,IAAK,CAAA,MAAM,EAClE,IAAK,CAAA,qBAAA,CAAwB,EAAC,CAC9B,KAAK,WAAc,CAAA,IAAIC,WAAY,CAAA,IAAA,CAAM,KAAK,MAAQ5S,CAAAA,CAAAA,EAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,gBAAgB,EAC9E,CA5DA,aAAa,IAAA,CAAKA,EAA0B,CAC1C,MAAMpC,CAAO,CAAA,IAAI6T,OAAKzR,CAAI,CAAA,CAC1B,MAAMpC,CAAAA,CAAK,YACX,CAAA,MAAMiS,CAAW,CAAA,MAAMjS,CAAK,CAAA,MAAA,CAAO,WAAY,EAAA,CAC/C,aAAMA,CAAK,CAAA,OAAA,CAAQ,OAAQiV,CAAAA,uBAAAA,CAAyBhD,CAAQ,CAErDjS,CAAAA,CACT,CAuDA,IAAI,SAAU,CACZ,OAAOY,uBAAiB,CAAA,IAAA,CAAK,MAAM,CACrC,CAIA,MAAa,KAAA,EAAQ,CACf,IAAK,CAAA,WAAA,EACT,MAAM,IAAA,CAAK,aACb,CAEA,MAAa,WAAA,EAAc,CArI7B,IAAAiE,CAAAA,CAsII,OAAOA,CAAAA,CAAAA,CAAA,KAAK,kBAAL,GAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAyB,CAAA,UAAA,CAAW,CACzC,QAAU,CAAA,MAAM,IAAK,CAAA,MAAA,CAAO,aAC9B,CAAA,CACF,CAEA,MAAa,wBAAwBqQ,CAAuB,CAAA,CACtD,IAAK,CAAA,qBAAA,CAAsB,SAASA,CAAa,CAAA,GACrD,IAAK,CAAA,qBAAA,CAAsB,KAAKA,CAAa,CAAA,CAC7C,MAAM,IAAA,CAAK,QAAQ,OAAQC,CAAAA,4BAAAA,CAA8B,IAAK,CAAA,qBAAqB,GACrF,CA6CA,MAAc,UAAa,EAAA,CACzB,KAAK,MAAO,CAAA,KAAA,CAAM,aAAa,CAAA,CAC/B,GAAI,CACF,MAAM,IAAK,CAAA,MAAA,CAAO,MAClB,CAAA,MAAM,IAAK,CAAA,OAAA,CAAQ,MACnB,CAAA,MAAM,IAAK,CAAA,OAAA,CAAQ,MACnB,CAAA,MAAM,IAAK,CAAA,OAAA,CAAQ,MACnB,CAAA,MAAM,IAAK,CAAA,SAAA,CAAU,MACrB,CAAA,MAAM,IAAK,CAAA,OAAA,CAAQ,MACnB,CAAA,IAAA,CAAK,qBAAyB,CAAA,MAAM,KAAK,OAAQ,CAAA,OAAA,CAAQA,4BAA4B,CAAA,EAAM,EAE3F,CAAA,IAAA,CAAK,WAAc,CAAA,CAAA,CAAA,CACnB,KAAK,MAAO,CAAA,IAAA,CAAK,6BAA6B,EAChD,OAAShS,CAAO,CAAA,CACd,MAAK,IAAA,CAAA,MAAA,CAAO,KAAK,CAAwC,qCAAA,EAAA,IAAA,CAAK,GAAI,EAAC,GAAIA,CAAK,CAAA,CAC5E,IAAK,CAAA,MAAA,CAAO,MAAOA,CAAc,CAAA,OAAO,CAClCA,CAAAA,CACR,CACF,CACF;;AC1MO,MAAM,IAAOiS,CAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}