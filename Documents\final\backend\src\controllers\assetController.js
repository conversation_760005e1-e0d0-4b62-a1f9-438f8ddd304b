/**
 * Asset Controller
 * Handles API requests for user assets
 */
const assetsDb = require('../db/assets');
const { getErrorResponse } = require('../utils/responseUtils');

/**
 * Get all assets for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getUserAssets(req, res) {
  try {
    const userId = req.user.id;
    
    // Get assets from database
    const assets = await assetsDb.getUserAssets(userId);
    
    return res.json({
      success: true,
      assets
    });
  } catch (error) {
    console.error('Error fetching user assets:', error);
    return getErrorResponse(res, 'Failed to fetch assets', error);
  }
}

/**
 * Get a specific asset for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getUserAsset(req, res) {
  try {
    const userId = req.user.id;
    const { symbol } = req.params;
    
    if (!symbol) {
      return res.status(400).json({
        success: false,
        message: 'Asset symbol is required'
      });
    }
    
    // Get asset from database
    const asset = await assetsDb.getUserAsset(userId, symbol);
    
    if (!asset) {
      return res.status(404).json({
        success: false,
        message: `Asset ${symbol} not found for user`
      });
    }
    
    return res.json({
      success: true,
      asset
    });
  } catch (error) {
    console.error('Error fetching user asset:', error);
    return getErrorResponse(res, 'Failed to fetch asset', error);
  }
}

/**
 * Create or update an asset for the authenticated user (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function upsertAsset(req, res) {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Only administrators can manually update assets'
      });
    }
    
    const { userId, symbol, balance, value, change_24h } = req.body;
    
    if (!userId || !symbol) {
      return res.status(400).json({
        success: false,
        message: 'User ID and asset symbol are required'
      });
    }
    
    // Create or update asset
    const asset = await assetsDb.upsertAsset({
      user_id: userId,
      symbol,
      balance: balance || 0,
      value: value || 0,
      change_24h: change_24h || 0
    });
    
    return res.json({
      success: true,
      asset,
      message: 'Asset updated successfully'
    });
  } catch (error) {
    console.error('Error upserting asset:', error);
    return getErrorResponse(res, 'Failed to update asset', error);
  }
}

/**
 * Update asset balances based on price changes (admin/system only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function updateAssetPrices(req, res) {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Only administrators can update asset prices'
      });
    }
    
    const { priceUpdates } = req.body;
    
    if (!priceUpdates || !Array.isArray(priceUpdates) || priceUpdates.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Price updates array is required'
      });
    }
    
    // Update asset prices
    const updatedAssets = await assetsDb.updateAssetPrices(priceUpdates);
    
    return res.json({
      success: true,
      updatedCount: updatedAssets.length,
      message: 'Asset prices updated successfully'
    });
  } catch (error) {
    console.error('Error updating asset prices:', error);
    return getErrorResponse(res, 'Failed to update asset prices', error);
  }
}

/**
 * Delete an asset (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function deleteAsset(req, res) {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Only administrators can delete assets'
      });
    }
    
    const { userId, symbol } = req.params;
    
    if (!userId || !symbol) {
      return res.status(400).json({
        success: false,
        message: 'User ID and asset symbol are required'
      });
    }
    
    // Delete asset
    const deleted = await assetsDb.deleteAsset(userId, symbol);
    
    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: `Asset ${symbol} not found for user ${userId}`
      });
    }
    
    return res.json({
      success: true,
      message: `Asset ${symbol} deleted successfully for user ${userId}`
    });
  } catch (error) {
    console.error('Error deleting asset:', error);
    return getErrorResponse(res, 'Failed to delete asset', error);
  }
}

module.exports = {
  getUserAssets,
  getUserAsset,
  upsertAsset,
  updateAssetPrices,
  deleteAsset
};
