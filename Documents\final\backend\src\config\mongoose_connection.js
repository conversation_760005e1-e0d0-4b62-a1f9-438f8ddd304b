/**
 * Mongoose Connection Manager
 *
 * This module provides a robust connection to MongoDB using Mongoose.
 * It ensures that all Mongoose operations wait for the connection to be established.
 */

const mongoose = require('mongoose');
const mongodbRobust = require('./mongodb_robust');
const EventEmitter = require('events');

// Create a connection event emitter
class MongooseConnectionEmitter extends EventEmitter {}
const connectionEvents = new MongooseConnectionEmitter();

// Connection state
let isConnected = false;
let isConnecting = false;

/**
 * Initialize Mongoose connection using the robust MongoDB connection
 * @returns {Promise<boolean>} - True if connected successfully
 */
async function initializeMongoose() {
  if (isConnected) {
    return true;
  }

  if (isConnecting) {
    return new Promise((resolve) => {
      connectionEvents.once('connected', () => {
        resolve(true);
      });
      connectionEvents.once('connection_failed', () => {
        resolve(false);
      });
    });
  }

  isConnecting = true;

  try {
    console.log('Initializing Mongoose connection...');

    // Get the MongoDB connection URI from the robust connection manager
    const uri = mongodbRobust.mongodbConfig.uri;

    // Configure Mongoose
    mongoose.set('strictQuery', false);

    // Set up connection event listeners
    mongoose.connection.on('connected', () => {
      console.log('Mongoose connected to MongoDB');
      isConnected = true;
      isConnecting = false;
      connectionEvents.emit('connected');
    });

    mongoose.connection.on('error', (err) => {
      console.error('Mongoose connection error:', err);
      isConnected = false;
      connectionEvents.emit('error', err);
    });

    mongoose.connection.on('disconnected', () => {
      console.log('Mongoose disconnected from MongoDB');
      isConnected = false;

      // Try to reconnect
      setTimeout(() => {
        if (!isConnecting) {
          console.log('Attempting to reconnect Mongoose...');
          initializeMongoose().catch(err => {
            console.error('Failed to reconnect Mongoose:', err);
          });
        }
      }, 5000);
    });

    // Connect to MongoDB
    await mongoose.connect(uri, {
      serverSelectionTimeoutMS: 60000,
      socketTimeoutMS: 90000,
      connectTimeoutMS: 60000,
      maxPoolSize: 20,
      minPoolSize: 5,
      bufferCommands: true, // Buffer commands until connection is established
      // Note: bufferTimeoutMS is not supported in MongoDB driver v4+
      autoIndex: true,
      retryWrites: true,
      retryReads: true,
      keepAlive: true,
      keepAliveInitialDelay: 300000 // 5 minutes
    });

    console.log('Mongoose connected successfully');
    isConnected = true;
    isConnecting = false;
    connectionEvents.emit('connected');

    return true;
  } catch (error) {
    console.error('Mongoose connection failed:', error);
    isConnected = false;
    isConnecting = false;
    connectionEvents.emit('connection_failed', error);

    throw error;
  }
}

/**
 * Check if Mongoose is connected
 * @returns {boolean} - True if connected
 */
function isMongooseConnected() {
  return mongoose.connection.readyState === 1;
}

/**
 * Get the Mongoose connection state
 * @returns {number} - Connection state (0: disconnected, 1: connected, 2: connecting, 3: disconnecting)
 */
function getConnectionState() {
  return mongoose.connection.readyState;
}

/**
 * Close the Mongoose connection
 * @returns {Promise<void>}
 */
async function closeConnection() {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
    console.log('Mongoose connection closed');
    isConnected = false;
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('Received SIGINT signal, closing Mongoose connection...');
  await closeConnection();
  process.exit(0);
});

module.exports = {
  initializeMongoose,
  isMongooseConnected,
  getConnectionState,
  closeConnection,
  connectionEvents
};
