const mongoose = require('mongoose');
const path = require('path');
const User = require('../models/User');
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

async function createDevUser() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    const userData = {
      name: '<PERSON><PERSON><PERSON> Yadav',
      email: '<EMAIL>',
      phoneNumber: '+91797425726',
      password: '7973425726Rishab',
      isPhoneVerified: true
    };

    const existingUser = await User.findOne({ 
      $or: [
        { email: userData.email },
        { phoneNumber: userData.phoneNumber }
      ]
    });

    if (existingUser) {
      console.log('User already exists');
      process.exit(0);
    }

    const user = new User(userData);
    await user.save();
    console.log('Dev user created successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error creating dev user:', error);
    process.exit(1);
  }
}

createDevUser(); 