/**
 * Custom server starter
 * This script starts the server with the correct Node.js options
 */

// Set Node.js options for compatibility with MongoDB Atlas
process.env.NODE_OPTIONS = '--tls-min-v1.2';

// Disable TLS certificate verification for development only
// WARNING: This is insecure and should not be used in production
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

// Start the server
require('./src/index.js');
