@echo off
echo Checking Node.js version...

for /f "tokens=*" %%i in ('node -v') do set NODE_VERSION=%%i

echo Current Node.js version: %NODE_VERSION%

if "%NODE_VERSION:~0,2%"=="v1" (
    if "%NODE_VERSION:~0,3%"=="v18" (
        echo Using Node.js v18, which is compatible with MongoDB Atlas
        npm run start:node18
    ) else if "%NODE_VERSION:~0,3%"=="v16" (
        echo Using Node.js v16, which is compatible with MongoDB Atlas
        npm run start:node18
    ) else (
        echo WARNING: You are using Node.js %NODE_VERSION%, which may have compatibility issues with MongoDB Atlas
        echo Please consider switching to Node.js v18 for better compatibility
        echo See README-NODE18.md for instructions
        
        set /p CONTINUE=Do you want to continue with the current Node.js version? (y/n): 
        if /i "%CONTINUE%"=="y" (
            npm run start
        ) else (
            echo Exiting...
        )
    )
) else (
    echo WARNING: You are using Node.js %NODE_VERSION%, which may have compatibility issues with MongoDB Atlas
    echo Please consider switching to Node.js v18 for better compatibility
    echo See README-NODE18.md for instructions
    
    set /p CONTINUE=Do you want to continue with the current Node.js version? (y/n): 
    if /i "%CONTINUE%"=="y" (
        npm run start
    ) else (
        echo Exiting...
    )
)

pause
