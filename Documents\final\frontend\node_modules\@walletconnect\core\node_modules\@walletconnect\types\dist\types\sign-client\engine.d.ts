/// <reference types="node" />
import { JsonRpcResponse, JsonRpcRequest, ErrorResponse, JsonR<PERSON><PERSON><PERSON><PERSON><PERSON>, JsonRpc<PERSON>rror } from "@walletconnect/jsonrpc-types";
import { ISignClient } from "./client";
import { RelayerTypes } from "../core/relayer";
import { SessionTypes } from "./session";
import { ProposalTypes } from "./proposal";
import { PairingTypes } from "../core/pairing";
import { JsonRpcTypes } from "./jsonrpc";
import { EventEmitter } from "events";
import { PendingRequestTypes } from "./pendingRequest";
import { AuthTypes } from "./auth";
import { CryptoTypes } from "../core";
export declare namespace EngineTypes {
    type Event = "session_connect" | "session_approve" | "session_update" | "session_extend" | "session_ping" | "pairing_ping" | "session_request";
    interface EventArguments {
        session_connect: {
            error?: ErrorResponse;
            session?: SessionTypes.Struct;
        };
        session_approve: {
            error?: ErrorResponse;
        };
        session_update: {
            error?: ErrorResponse;
        };
        session_extend: {
            error?: ErrorResponse;
        };
        session_ping: {
            error?: ErrorResponse;
        };
        pairing_ping: {
            error?: ErrorResponse;
        };
        session_request: {
            error?: ErrorResponse;
            result?: any;
        };
    }
    interface UriParameters {
        protocol: string;
        version: number;
        topic: string;
        symKey: string;
        relay: RelayerTypes.ProtocolOptions;
        methods?: string[];
        expiryTimestamp?: number;
    }
    interface EventCallback<T extends JsonRpcRequest | JsonRpcResponse> {
        topic: string;
        payload: T;
        transportType?: RelayerTypes.MessageEvent["transportType"];
        attestation?: string;
        encryptedId?: string;
    }
    interface ConnectParams {
        requiredNamespaces?: ProposalTypes.RequiredNamespaces;
        optionalNamespaces?: ProposalTypes.OptionalNamespaces;
        sessionProperties?: ProposalTypes.SessionProperties;
        pairingTopic?: string;
        relays?: RelayerTypes.ProtocolOptions[];
    }
    interface PairParams {
        uri: string;
    }
    interface ApproveParams {
        id: number;
        namespaces: SessionTypes.Namespaces;
        sessionProperties?: ProposalTypes.SessionProperties;
        sessionConfig?: SessionTypes.SessionConfig;
        relayProtocol?: string;
    }
    interface RejectParams {
        id: number;
        reason: ErrorResponse;
    }
    interface UpdateParams {
        topic: string;
        namespaces: SessionTypes.Namespaces;
    }
    interface ExtendParams {
        topic: string;
    }
    interface RequestParams {
        topic: string;
        request: {
            method: string;
            params: any;
        };
        chainId: string;
        expiry?: number;
    }
    interface RespondParams {
        topic: string;
        response: JsonRpcResponse;
    }
    interface EmitParams {
        topic: string;
        event: {
            name: string;
            data: any;
        };
        chainId: string;
    }
    interface PingParams {
        topic: string;
    }
    interface DisconnectParams {
        topic: string;
        reason: ErrorResponse;
    }
    interface FindParams {
        requiredNamespaces: ProposalTypes.RequiredNamespaces;
    }
    type AcknowledgedPromise = Promise<{
        acknowledged: () => Promise<void>;
    }>;
    type SessionAuthenticateResponsePromise = {
        uri: string;
        response: () => Promise<AuthTypes.AuthenticateResponseResult>;
    };
    interface RpcOpts {
        req: RelayerTypes.PublishOptions & {
            ttl: number;
        };
        res: RelayerTypes.PublishOptions & {
            ttl: number;
        };
        reject?: RelayerTypes.PublishOptions & {
            ttl: number;
        };
        autoReject?: RelayerTypes.PublishOptions & {
            ttl: number;
        };
    }
    type RpcOptsMap = Record<JsonRpcTypes.WcMethod, RpcOpts>;
    type EngineQueue<T> = {
        state: "IDLE" | "ACTIVE";
        queue: T[];
    };
}
export declare abstract class IEngineEvents extends EventEmitter {
    constructor();
    abstract emit: <E extends EngineTypes.Event>(event: string, args: EngineTypes.EventArguments[E]) => boolean;
    abstract once: <E extends EngineTypes.Event>(event: string, listener: (args: EngineTypes.EventArguments[E]) => any) => this;
}
export interface EnginePrivate {
    sendRequest<M extends JsonRpcTypes.WcMethod>(args: {
        topic: string;
        method: M;
        params: JsonRpcTypes.RequestParams[M];
        expiry?: number;
        relayRpcId?: number;
        clientRpcId?: number;
        throwOnFailedPublish?: boolean;
        appLink?: string;
        tvf?: RelayerTypes.ITVF;
    }): Promise<number>;
    sendResult<M extends JsonRpcTypes.WcMethod>(args: {
        id: number;
        topic: string;
        result: JsonRpcTypes.Results[M];
        throwOnFailedPublish?: boolean;
        encodeOpts?: CryptoTypes.EncodeOptions;
        appLink?: string;
    }): Promise<void>;
    sendError(params: {
        id: number;
        topic: string;
        error: JsonRpcTypes.Error;
        encodeOpts?: CryptoTypes.EncodeOptions;
        rpcOpts?: RelayerTypes.PublishOptions;
        appLink?: string;
    }): Promise<void>;
    onRelayEventRequest(event: EngineTypes.EventCallback<JsonRpcRequest>): Promise<void>;
    onRelayEventResponse(event: EngineTypes.EventCallback<JsonRpcResponse>): Promise<void>;
    onRelayEventUnknownPayload(event: EngineTypes.EventCallback<any>): Promise<void>;
    shouldIgnorePairingRequest(params: {
        topic: string;
        requestMethod: string;
    }): boolean;
    deleteSession(params: {
        topic: string;
        expirerHasDeleted?: boolean;
        id?: number;
        emitEvent?: boolean;
    }): Promise<void>;
    deleteProposal(id: number, expirerHasDeleted?: boolean): Promise<void>;
    setExpiry(topic: string, expiry: number): Promise<void>;
    setProposal(id: number, proposal: ProposalTypes.Struct): Promise<void>;
    setAuthRequest(id: number, params: {
        request: AuthTypes.SessionAuthenticateRequest;
        pairingTopic: string;
        transportType?: RelayerTypes.MessageEvent["transportType"];
    }): Promise<void>;
    setPendingSessionRequest(pendingRequest: PendingRequestTypes.Struct): Promise<void>;
    deletePendingSessionRequest(id: number, reason: ErrorResponse, expirerHasDeleted?: boolean): Promise<void>;
    deletePendingAuthRequest(id: number, reason: ErrorResponse, expirerHasDeleted?: boolean): Promise<void>;
    cleanupDuplicatePairings(session: SessionTypes.Struct): Promise<void>;
    cleanup(): Promise<void>;
    onSessionProposeRequest(params: {
        topic: string;
        payload: JsonRpcRequest<JsonRpcTypes.RequestParams["wc_sessionPropose"]>;
        attestation?: string;
        encryptedId?: string;
    }): Promise<void>;
    onSessionProposeResponse(topic: string, payload: JsonRpcResult<JsonRpcTypes.Results["wc_sessionPropose"]> | JsonRpcError, transportType?: RelayerTypes.MessageEvent["transportType"]): Promise<void>;
    onSessionSettleRequest(topic: string, payload: JsonRpcRequest<JsonRpcTypes.RequestParams["wc_sessionSettle"]>): Promise<void>;
    onSessionSettleResponse(topic: string, payload: JsonRpcResult<JsonRpcTypes.Results["wc_sessionSettle"]> | JsonRpcError): Promise<void>;
    onSessionUpdateRequest(topic: string, payload: JsonRpcRequest<JsonRpcTypes.RequestParams["wc_sessionUpdate"]>): Promise<void>;
    onSessionUpdateResponse(topic: string, payload: JsonRpcResult<JsonRpcTypes.Results["wc_sessionUpdate"]> | JsonRpcError): void;
    onSessionExtendRequest(topic: string, payload: JsonRpcRequest<JsonRpcTypes.RequestParams["wc_sessionExtend"]>): Promise<void>;
    onSessionExtendResponse(topic: string, payload: JsonRpcResult<JsonRpcTypes.Results["wc_sessionExtend"]> | JsonRpcError): void;
    onSessionPingRequest(topic: string, payload: JsonRpcRequest<JsonRpcTypes.RequestParams["wc_sessionPing"]>): Promise<void>;
    onSessionPingResponse(topic: string, payload: JsonRpcResult<JsonRpcTypes.Results["wc_sessionPing"]> | JsonRpcError): void;
    onSessionDeleteRequest(topic: string, payload: JsonRpcRequest<JsonRpcTypes.RequestParams["wc_sessionDelete"]>): Promise<void>;
    onSessionRequest(params: {
        topic: string;
        payload: JsonRpcRequest<JsonRpcTypes.RequestParams["wc_sessionRequest"]>;
        transportType?: RelayerTypes.MessageEvent["transportType"];
        attestation?: string;
        encryptedId?: string;
    }): Promise<void>;
    onSessionRequestResponse(topic: string, payload: JsonRpcResult<JsonRpcTypes.Results["wc_sessionRequest"]> | JsonRpcError): void;
    onSessionEventRequest(topic: string, payload: JsonRpcRequest<JsonRpcTypes.RequestParams["wc_sessionEvent"]>): Promise<void>;
    onSessionAuthenticateRequest(params: {
        topic: string;
        payload: JsonRpcRequest<JsonRpcTypes.RequestParams["wc_sessionAuthenticate"]>;
        transportType?: RelayerTypes.MessageEvent["transportType"];
        attestation?: string;
        encryptedId?: string;
    }): Promise<void>;
    onSessionAuthenticateResponse(topic: string, payload: JsonRpcResult<JsonRpcTypes.Results["wc_sessionAuthenticate"]> | JsonRpcError): void;
    isValidConnect(params: EngineTypes.ConnectParams): Promise<void>;
    isValidSessionSettleRequest(params: JsonRpcTypes.RequestParams["wc_sessionSettle"]): void;
    isValidApprove(params: EngineTypes.ApproveParams): Promise<void>;
    isValidReject(params: EngineTypes.RejectParams): Promise<void>;
    isValidUpdate(params: EngineTypes.UpdateParams): Promise<void>;
    isValidExtend(params: EngineTypes.ExtendParams): Promise<void>;
    isValidRequest(params: EngineTypes.RequestParams): Promise<void>;
    isValidRespond(params: EngineTypes.RespondParams): Promise<void>;
    isValidPing(params: EngineTypes.PingParams): Promise<void>;
    isValidEmit(params: EngineTypes.EmitParams): Promise<void>;
    isValidDisconnect(params: EngineTypes.DisconnectParams): Promise<void>;
}
export declare abstract class IEngine {
    client: ISignClient;
    constructor(client: ISignClient);
    abstract init(): Promise<void>;
    abstract connect(params: EngineTypes.ConnectParams): Promise<{
        uri?: string;
        approval: () => Promise<SessionTypes.Struct>;
    }>;
    abstract pair(params: EngineTypes.PairParams): Promise<PairingTypes.Struct>;
    abstract approve(params: EngineTypes.ApproveParams): Promise<{
        topic: string;
        acknowledged: () => Promise<SessionTypes.Struct>;
    }>;
    abstract reject(params: EngineTypes.RejectParams): Promise<void>;
    abstract update(params: EngineTypes.UpdateParams): EngineTypes.AcknowledgedPromise;
    abstract extend(params: EngineTypes.ExtendParams): EngineTypes.AcknowledgedPromise;
    abstract request<T>(params: EngineTypes.RequestParams): Promise<T>;
    abstract respond(params: EngineTypes.RespondParams): Promise<void>;
    abstract emit(params: EngineTypes.EmitParams): Promise<void>;
    abstract ping(params: EngineTypes.PingParams): Promise<void>;
    abstract disconnect(params: EngineTypes.DisconnectParams): Promise<void>;
    abstract find: (params: EngineTypes.FindParams) => SessionTypes.Struct[];
    abstract getPendingSessionRequests: () => PendingRequestTypes.Struct[];
    abstract authenticate: (params: AuthTypes.SessionAuthenticateParams, walletUniversalLink?: string) => Promise<EngineTypes.SessionAuthenticateResponsePromise>;
    abstract approveSessionAuthenticate: (params: AuthTypes.ApproveSessionAuthenticateParams) => Promise<{
        session: SessionTypes.Struct | undefined;
    }>;
    abstract formatAuthMessage: (params: {
        request: AuthTypes.BaseAuthRequestParams;
        iss: string;
    }) => string;
    abstract rejectSessionAuthenticate(params: EngineTypes.RejectParams): Promise<void>;
    abstract processRelayMessageCache(): void;
}
//# sourceMappingURL=engine.d.ts.map