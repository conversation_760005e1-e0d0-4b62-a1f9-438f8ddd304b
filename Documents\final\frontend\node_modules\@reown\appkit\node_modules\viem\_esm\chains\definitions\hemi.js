import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js';
export const hemi = /*#__PURE__*/ define<PERSON>hain({
    id: 43111,
    name: '<PERSON><PERSON>',
    network: '<PERSON><PERSON>',
    nativeCurrency: {
        name: '<PERSON><PERSON>',
        symbol: 'ETH',
        decimals: 18,
    },
    rpcUrls: {
        default: {
            http: ['https://rpc.hemi.network/rpc'],
        },
    },
    blockExplorers: {
        default: {
            name: 'blockscout',
            url: 'https://explorer.hemi.xyz',
        },
    },
    testnet: false,
});
//# sourceMappingURL=hemi.js.map