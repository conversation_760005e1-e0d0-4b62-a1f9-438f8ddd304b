/**
 * Transaction Model (PostgreSQL)
 *
 * Stores all transaction data including:
 * - Token purchases and sales
 * - Platform fees
 * - Token transfers
 * - Deposits and withdrawals
 * - Blockchain transactions
 */

const { sequelize, Sequelize } = require('../../config/postgresql');

const Transaction = sequelize.define('Transaction', {
  // Transaction ID (primary key)
  id: {
    type: Sequelize.UUID,
    defaultValue: Sequelize.UUIDV4,
    primaryKey: true
  },

  // Transaction type (buy, sell, transfer, fee, deposit, withdrawal)
  type: {
    type: Sequelize.ENUM('buy', 'sell', 'transfer', 'fee', 'deposit', 'withdrawal', 'unknown'),
    allowNull: false
  },

  // Solana transaction signature/ID
  txId: {
    type: Sequelize.STRING,
    allowNull: true,
    unique: true,
    field: 'tx_id' // Map to the actual column name in the database
  },

  // Legacy field for backward compatibility
  signature: {
    type: Sequelize.STRING,
    allowNull: true
  },

  // User wallet address (sender or recipient)
  walletAddress: {
    type: Sequelize.STRING,
    allowNull: true,
    field: 'wallet_address' // Map to the actual column name in the database
  },

  // User ID (for internal balance system)
  userId: {
    type: Sequelize.STRING,
    allowNull: true,
    field: 'user_id' // Map to the actual column name in the database
  },

  // Token mint address (if applicable)
  tokenMint: {
    type: Sequelize.STRING,
    allowNull: true,
    field: 'token_mint' // Map to the actual column name in the database
  },

  // Token symbol (SOL, USDC, etc.)
  token: {
    type: Sequelize.STRING,
    allowNull: true
  },

  // Amount (as decimal number)
  amount: {
    type: Sequelize.DECIMAL(24, 9),
    allowNull: true
  },

  // Legacy fields for backward compatibility
  tokenAmount: {
    type: Sequelize.STRING,
    allowNull: true,
    field: 'token_amount' // Map to the actual column name in the database
  },

  solAmount: {
    type: Sequelize.STRING,
    allowNull: true,
    field: 'sol_amount' // Map to the actual column name in the database
  },

  // Platform fee amount (as string)
  feeAmount: {
    type: Sequelize.STRING,
    allowNull: true,
    field: 'fee_amount' // Map to the actual column name in the database
  },

  // Transaction status
  status: {
    type: Sequelize.ENUM('pending', 'completed', 'confirmed', 'failed'),
    defaultValue: 'pending',
    allowNull: false
  },

  // Block time (from Solana)
  blockTime: {
    type: Sequelize.DATE,
    allowNull: true,
    field: 'block_time' // Map to the actual column name in the database
  },

  // Additional data (JSON)
  metadata: {
    type: Sequelize.JSONB,
    defaultValue: {},
    allowNull: false
  }
}, {
  // Table configuration
  tableName: 'transactions',
  timestamps: true,
  underscored: true, // Use snake_case for column names
  indexes: [
    {
      fields: ['wallet_address']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['tx_id']
    },
    {
      fields: ['token_mint']
    },
    {
      fields: ['type']
    },
    {
      fields: ['status']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = Transaction;
