/**
 * Transaction Controller
 * Handles API requests for user transactions
 */
const transactionsDb = require('../db/transactions'); // Legacy MongoDB transactions DB
const postgresqlService = require('../services/postgresqlService'); // PostgreSQL service
const { getErrorResponse } = require('../utils/responseUtils');
const { v4: uuidv4 } = require('uuid');

/**
 * Get transactions for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getUserTransactions(req, res) {
  try {
    const userId = req.user.id;
    const { limit, offset, sort } = req.query;

    // Get user wallet address from user object
    const walletAddress = req.user.walletAddress;

    if (!walletAddress) {
      return res.status(400).json({
        success: false,
        message: 'User wallet address not found'
      });
    }

    // Get transactions from PostgreSQL database
    const { rows, count } = await postgresqlService.getTransactionsByWallet(walletAddress, {
      limit: limit ? parseInt(limit, 10) : 10,
      offset: offset ? parseInt(offset, 10) : 0,
      startDate: req.query.startDate,
      endDate: req.query.endDate,
      type: req.query.type,
      status: req.query.status
    });

    // Get total count for pagination
    const totalCount = count;

    return res.json({
      success: true,
      transactions: rows,
      pagination: {
        total: totalCount,
        limit: limit ? parseInt(limit, 10) : 10,
        offset: offset ? parseInt(offset, 10) : 0
      }
    });
  } catch (error) {
    console.error('Error fetching user transactions:', error);
    return getErrorResponse(res, 'Failed to fetch transactions', error);
  }
}

/**
 * Get a specific transaction
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getTransaction(req, res) {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Transaction ID is required'
      });
    }

    // Get transaction from PostgreSQL database
    const transaction = await postgresqlService.getTransactionById(id);

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: `Transaction with ID ${id} not found`
      });
    }

    // Check if user is authorized to view this transaction
    if (transaction.walletAddress !== req.user.walletAddress && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to view this transaction'
      });
    }

    return res.json({
      success: true,
      transaction
    });
  } catch (error) {
    console.error('Error fetching transaction:', error);
    return getErrorResponse(res, 'Failed to fetch transaction', error);
  }
}

/**
 * Create a new transaction
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function createTransaction(req, res) {
  try {
    const { type, tokenMint, tokenAmount, solAmount, feeAmount, signature, status } = req.body;

    // Validate required fields
    if (!type || !tokenMint || (!tokenAmount && !solAmount)) {
      return res.status(400).json({
        success: false,
        message: 'Transaction type, token mint, and amount are required'
      });
    }

    // Validate transaction type
    const validTypes = ['buy', 'sell', 'transfer', 'fee'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        message: `Invalid transaction type. Must be one of: ${validTypes.join(', ')}`
      });
    }

    // Get user wallet address
    const walletAddress = req.user.walletAddress;

    if (!walletAddress) {
      return res.status(400).json({
        success: false,
        message: 'User wallet address not found'
      });
    }

    // Create transaction in PostgreSQL
    const transaction = await postgresqlService.createTransaction({
      id: uuidv4(),
      type,
      walletAddress,
      tokenMint,
      tokenAmount: tokenAmount ? tokenAmount.toString() : null,
      solAmount: solAmount ? solAmount.toString() : null,
      feeAmount: feeAmount ? feeAmount.toString() : null,
      signature,
      status: status || 'pending',
      metadata: {
        userId: req.user.id,
        createdAt: new Date()
      }
    });

    return res.status(201).json({
      success: true,
      transaction,
      message: 'Transaction created successfully'
    });
  } catch (error) {
    console.error('Error creating transaction:', error);
    return getErrorResponse(res, 'Failed to create transaction', error);
  }
}

/**
 * Update a transaction's status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function updateTransactionStatus(req, res) {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!id || !status) {
      return res.status(400).json({
        success: false,
        message: 'Transaction ID and status are required'
      });
    }

    // Validate status
    const validStatuses = ['pending', 'completed', 'failed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
      });
    }

    // Get the transaction first to check authorization
    const currentTx = await postgresqlService.getTransactionById(id);

    if (!currentTx) {
      return res.status(404).json({
        success: false,
        message: `Transaction with ID ${id} not found`
      });
    }

    // Check if user is authorized to update this transaction
    if (currentTx.walletAddress !== req.user.walletAddress && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to update this transaction'
      });
    }

    // Additional data to update
    const additionalData = req.body.blockTime ? { blockTime: new Date(req.body.blockTime) } : {};

    // Update transaction status in PostgreSQL
    const transaction = await postgresqlService.updateTransactionStatus(id, status, additionalData);

    return res.json({
      success: true,
      transaction,
      message: 'Transaction status updated successfully'
    });
  } catch (error) {
    console.error('Error updating transaction status:', error);
    return getErrorResponse(res, 'Failed to update transaction status', error);
  }
}

/**
 * Get transaction statistics for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getTransactionStats(req, res) {
  try {
    const walletAddress = req.user.walletAddress;

    if (!walletAddress) {
      return res.status(400).json({
        success: false,
        message: 'User wallet address not found'
      });
    }

    // Get transaction stats from database
    // For now, we'll use a simple approach to calculate stats
    const buyTransactions = await postgresqlService.getTransactionsByWallet(walletAddress, { type: 'buy' });
    const sellTransactions = await postgresqlService.getTransactionsByWallet(walletAddress, { type: 'sell' });

    // Calculate total buy and sell amounts
    const totalBuy = buyTransactions.rows.reduce((sum, tx) => sum + parseFloat(tx.solAmount || '0'), 0);
    const totalSell = sellTransactions.rows.reduce((sum, tx) => sum + parseFloat(tx.solAmount || '0'), 0);

    const stats = {
      totalTransactions: buyTransactions.count + sellTransactions.count,
      totalBuy,
      totalSell,
      netPosition: totalBuy - totalSell,
      lastTransaction: [...buyTransactions.rows, ...sellTransactions.rows]
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0] || null
    };

    return res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Error fetching transaction stats:', error);
    return getErrorResponse(res, 'Failed to fetch transaction statistics', error);
  }
}

module.exports = {
  getUserTransactions,
  getTransaction,
  createTransaction,
  updateTransactionStatus,
  getTransactionStats
};
