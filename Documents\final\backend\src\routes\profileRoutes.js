/**
 * Profile Routes
 */

const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { auth } = require('../middleware/auth');

/**
 * Update user profile
 * @route PUT /api/profile
 * @access Private
 */
router.put('/',
  auth,
  [
    body('username').optional().isLength({ min: 3 }).withMessage('Username must be at least 3 characters'),
    body('email').optional().isEmail().withMessage('Please include a valid email'),
    body('phone').optional(),
    body('countryCode').optional(),
    body('country').optional(),
    body('city').optional(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const userId = req.user.id;
      const { username, email, phone, countryCode, country, city } = req.body;

      // Find user by ID
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Update user fields if provided
      if (username) user.username = username;
      if (email) user.email = email;
      if (phone !== undefined) user.phone = phone;
      if (countryCode) user.countryCode = countryCode;
      if (country) user.country = country;
      if (city) user.city = city;

      // Save updated user
      await user.save();

      // Return updated user data (excluding password)
      const updatedUser = {
        id: user._id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        countryCode: user.countryCode,
        country: user.country,
        city: user.city,
        profilePicture: user.profilePicture,
        role: user.role
      };

      res.json({
        success: true,
        message: 'Profile updated successfully',
        user: updatedUser
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      res.status(500).json({
        success: false,
        message: 'Server error',
        error: error.message
      });
    }
  }
);

/**
 * Get user profile
 * @route GET /api/profile
 * @access Private
 */
router.get('/', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('Fetching profile for user ID:', userId);

    // For tokens with non-ObjectId format, return the user data from the token
    // But ensure we're not automatically assigning admin role
    if (userId && userId.length !== 24) {
      console.log('Using user data from token for non-ObjectId user ID');
      return res.json({
        success: true,
        user: {
          id: userId,
          username: req.user.username || 'User',
          email: req.user.email || '',
          role: 'user', // Always default to user role for non-DB users
          profilePicture: req.user.profilePicture || null,
          createdAt: req.user.createdAt || new Date().toISOString()
        }
      });
    }

    // Find user by ID for valid ObjectIds
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Return user data (excluding password)
    const userData = {
      id: user._id,
      username: user.username,
      email: user.email,
      phone: user.phone,
      countryCode: user.countryCode,
      country: user.country,
      city: user.city,
      profilePicture: user.profilePicture,
      role: user.role,
      createdAt: user.createdAt
    };

    res.json({
      success: true,
      user: userData
    });
  } catch (error) {
    console.error('Error fetching profile:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

module.exports = router;
