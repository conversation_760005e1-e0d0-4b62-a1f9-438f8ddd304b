/**
 * PostgreSQL Configuration
 *
 * This file configures the connection to PostgreSQL Render
 * for trading and transaction related data.
 */

const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');

// Load environment variables
require('dotenv').config();

// PostgreSQL connection string from environment variables
const POSTGRESQL_URI = process.env.POSTGRESQL_URI ||
  'postgres://postgres:postgres@localhost:5432/wb_swap';

// Create Sequelize instance
const sequelize = new Sequelize(POSTGRESQL_URI, {
  dialect: 'postgres',
  logging: (msg) => logger.debug(msg),
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  dialectOptions: {
    ssl: process.env.NODE_ENV === 'production'
      ? { require: true } // In production, properly verify SSL certificates
      : {
          require: true,
          rejectUnauthorized: false // Only disable SSL verification in development
        }
  },
  // Additional configuration for Render PostgreSQL
  retry: {
    max: 5, // Maximum retry attempts
    timeout: 60000 // Timeout in milliseconds
  }
});

// Test the connection
async function testConnection() {
  try {
    await sequelize.authenticate();
    logger.info('PostgreSQL connection has been established successfully.');
    logger.info(`Connected to: ${POSTGRESQL_URI.split('@')[1].split('/')[0]}`);
    return true;
  } catch (error) {
    logger.error(`Unable to connect to PostgreSQL: ${error.message}`);
    logger.error(`Connection string: ${POSTGRESQL_URI.replace(/:[^:]*@/, ':****@')}`);
    logger.error(`Error details: ${JSON.stringify(error, null, 2)}`);
    return false;
  }
}

// Initialize the database (create tables if they don't exist)
async function initializeDatabase() {
  try {
    // Import models
    require('../models/postgresql/transaction');
    require('../models/postgresql/order');
    require('../models/postgresql/trade');
    require('../models/postgresql/marketData');
    require('../models/postgresql/userBalance');
    require('../models/postgresql/balanceTransaction');

    // Sync all models with database, but don't try to create indexes on columns that don't exist
    await sequelize.sync({ alter: true });
    logger.info('PostgreSQL database synchronized successfully');
    return true;
  } catch (error) {
    logger.error(`Failed to initialize PostgreSQL database: ${error.message}`);

    // If the error is about a column not existing, we can continue anyway
    if (error.message.includes('column') && error.message.includes('does not exist')) {
      logger.warn('Continuing despite column error - some features may be limited');
      return true;
    }

    return false;
  }
}

module.exports = {
  sequelize,
  testConnection,
  initializeDatabase,
  Sequelize
};
