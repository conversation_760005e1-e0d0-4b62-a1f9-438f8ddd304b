const jwt = require('jsonwebtoken');
const User = require('../models/User');
const secureAuthService = require('../services/secureAuthService');

// Get JWT secret from environment variables
const JWT_SECRET = process.env.JWT_SECRET;
const isDevelopment = process.env.NODE_ENV !== 'production';

// Validate JWT secret in production
if (!JWT_SECRET && process.env.NODE_ENV === 'production') {
  console.error('CRITICAL: JWT_SECRET environment variable is not set in production mode!');
  process.exit(1); // Exit the process to prevent insecure operation
} else if (!JWT_SECRET) {
  console.warn('WARNING: JWT_SECRET not set. Using insecure default for development only.');
  // Only use default in development
  JWT_SECRET = 'insecure-development-key-do-not-use-in-production';
}

/**
 * Function to refresh token and retry the request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function refreshTokenAndRetry(req, res, next) {
  try {
    const refreshToken = req.cookies?.refresh_token;

    if (!refreshToken) {
      return res.status(401).json({ message: 'No refresh token provided' });
    }

    // Verify the refresh token
    const user = await secureAuthService.verifyRefreshToken(refreshToken);

    if (!user) {
      return res.status(401).json({ message: 'Invalid refresh token' });
    }

    // No special cases in production mode
    if (process.env.NODE_ENV !== 'production' && user.email === '<EMAIL>') {
      console.log('Development mode: Special case handling would occur here');
      // No role overrides in production
    }

    // Generate new tokens
    const accessToken = secureAuthService.generateAccessToken(user);
    const newRefreshToken = await secureAuthService.generateRefreshToken(user);

    // Set new cookies
    secureAuthService.setAuthCookies(res, accessToken, newRefreshToken);

    // Set the user in the request object
    req.user = jwt.verify(accessToken, JWT_SECRET);

    // Continue to the next middleware
    return next();
  } catch (error) {
    console.error('Error refreshing token:', error);
    return res.status(401).json({ message: 'Authentication failed' });
  }
}

/**
 * Authentication middleware using HTTP-only cookies
 */
const secureAuth = async (req, res, next) => {
  // Set CORS headers for all auth-protected routes
  res.header('Access-Control-Allow-Origin', req.headers.origin || 'http://localhost:3000');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');

  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  try {
    // Special case for login and register routes - bypass authentication
    const publicRoutes = [
      '/api/auth/login',
      '/api/auth/register',
      '/api/auth/refresh',
      '/api/auth/verify',
      '/api/market/statistics',
      '/api/market/ticker',
      '/api/market/pairs',
      '/api/market/recent-trades',
      '/api/market/orderbook',
      '/api/market/chart',
      '/api/market/depth',
      '/api/market/history',
      '/api/market/trades',
      '/api/market/orders',
      '/api/market/candles'
    ];

    // Check if the current route is a public route
    const isPublicRoute = publicRoutes.some(route => req.originalUrl.includes(route));

    if (isPublicRoute) {
      console.log('Bypassing authentication for public route:', req.originalUrl);
      return next();
    }

    // First try to get token from cookie
    const accessToken = req.cookies?.access_token;

    // Fallback to Authorization header for backward compatibility
    const headerToken = req.header('Authorization')?.replace('Bearer ', '');

    // Use cookie token if available, otherwise use header token
    const token = accessToken || headerToken;

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'No authentication token provided'
      });
    }

    try {
      try {
        // Verify the token
        const decoded = jwt.verify(token, JWT_SECRET);

        // No special cases in production mode
        if (process.env.NODE_ENV !== 'production' && decoded.email === '<EMAIL>') {
          console.log('Development mode: Special case handling would occur here');
          // No role overrides in production
        }

        // Set the user in the request object
        req.user = decoded;
      } catch (error) {
        console.error('Token verification error:', error.message);
        // Try to refresh the token if verification fails
        return refreshTokenAndRetry(req, res, next);
      }

      // Continue to the next middleware or route handler
      next();
    } catch (error) {
      console.error('Token verification error:', error);

      // Try to refresh using refresh token
      const refreshToken = req.cookies?.refresh_token;
      if (refreshToken) {
        try {
          const user = await secureAuthService.verifyRefreshToken(refreshToken);
          if (user) {
            // Generate new tokens
            const newAccessToken = secureAuthService.generateAccessToken(user);
            const newRefreshToken = await secureAuthService.generateRefreshToken(user);

            // Set new cookies
            secureAuthService.setAuthCookies(res, newAccessToken, newRefreshToken);

            // No special cases in production mode
            if (process.env.NODE_ENV !== 'production' && user.email === '<EMAIL>') {
              console.log('Development mode: Special case handling would occur here');
              // No role overrides in production
            }

            // Set the user in the request object
            req.user = user;

            // Continue to the next middleware or route handler
            return next();
          }
        } catch (refreshError) {
          console.error('Refresh token error:', refreshError);
        }
      }

      // If we get here, authentication failed
      return res.status(401).json({
        success: false,
        message: 'Invalid authentication token'
      });
    }
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during authentication'
    });
  }
};

// For backward compatibility
const auth = secureAuth;
const checkSession = (req, res, next) => next();

module.exports = { secureAuth, auth, checkSession };
