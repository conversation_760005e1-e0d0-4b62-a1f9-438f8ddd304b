{"version": 3, "file": "adapter.d.ts", "sourceRoot": "", "sources": ["../../src/adapter.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAe,UAAU,EAAE,MAAM,6BAA6B,CAAA;AAC1E,OAAO,EACL,uBAAuB,EACvB,oBAAoB,EAIpB,gBAAgB,EAIjB,MAAM,6BAA6B,CAAA;AACpC,OAAO,KAAK,EACV,SAAS,EACT,WAAW,EACX,kBAAkB,EAClB,oBAAoB,EACrB,MAAM,iBAAiB,CAAA;AAIxB,OAAO,KAAK,EAAE,gCAAgC,IAAI,oCAAoC,EAAE,MAAM,YAAY,CAAA;AAE1G,eAAO,MAAM,uBAAuB,EAAsB,UAAU,CAAC,eAAe,CAAC,CAAA;AAErF,MAAM,MAAM,gCAAgC,GAAG;IAC7C,OAAO,EAAE,oBAAoB,CAAC,OAAO,GAAG,oBAAoB,CAAC,MAAM,CAAA;CACpE,GAAG,IAAI,CAAC,oCAAoC,EAAE,SAAS,CAAC,CAAA;AAEzD,qBAAa,0BAA2B,SAAQ,uBAAuB;IACrE,IAAI,8BAA0B;IAC9B,GAAG,SAA8B;IACjC,IAAI,SACshE;IAK1hE,QAAQ,CAAC,4BAA4B,EAAE,WAAW,CAAC,kBAAkB,CAAC,CAAyB;IAE/F,OAAO,CAAC,UAAU,CAAkB;IACpC,OAAO,CAAC,WAAW,CAAS;IAC5B,OAAO,CAAC,OAAO,CAA4B;IAC3C,OAAO,CAAC,OAAO,CAAkC;IACjD,OAAO,CAAC,WAAW,CAAkB;IAErC,OAAO,CAAC,aAAa,CAAsD;gBAE/D,MAAM,EAAE,gCAAgC;IAapD,IAAI,SAAS,qBAEZ;IAED,IAAI,UAAU,YAEb;IAED,IAAI,UAAU,qBAEb;IAEK,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAiCxB,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAmB3B,eAAe,CAAC,CAAC,SAAS,WAAW,GAAG,oBAAoB,EAAE,WAAW,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAkBzF,WAAW,CAAC,OAAO,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IAkBrD,sBAAsB,CAAC,CAAC,SAAS,WAAW,GAAG,oBAAoB,EACvE,WAAW,EAAE,CAAC,GACb,OAAO,CAAC,MAAM,CAAC;IAkBH,mBAAmB,CAAC,CAAC,SAAS,WAAW,GAAG,oBAAoB,EAC7E,YAAY,EAAE,CAAC,EAAE,GAChB,OAAO,CAAC,CAAC,EAAE,CAAC;CAiBhB"}