/**
 * <PERSON><PERSON><PERSON> to create balance records for existing users
 *
 * This script will check all users in MongoDB and create balance records
 * for them in PostgreSQL if they don't already have one.
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const User = require('../models/User');
const postgresqlService = require('../services/postgresqlService');
const { sequelize } = require('../config/postgresql');
const logger = require('../utils/logger');

// Create a log file
const logFile = path.join(__dirname, 'balance_records_log.txt');
fs.writeFileSync(logFile, `Balance Records Log - ${new Date().toISOString()}\n\n`);

// Function to append to log file
function logToFile(message) {
  fs.appendFileSync(logFile, message + '\n');
  console.log(message);
}

async function createBalanceRecords() {
  try {
    logToFile('Starting balance record creation for existing users');
    logger.info('Starting balance record creation for existing users');

    // Initialize PostgreSQL service
    await postgresqlService.initialize();
    logToFile('PostgreSQL service initialized');
    logger.info('PostgreSQL service initialized');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    logToFile('Connected to MongoDB');
    logger.info('Connected to MongoDB');

    // Get all users from MongoDB
    const users = await User.find({});
    logToFile(`Found ${users.length} users in MongoDB`);
    logger.info(`Found ${users.length} users in MongoDB`);

    // Create balance records for each user
    let createdCount = 0;
    let existingCount = 0;
    let errorCount = 0;

    for (const user of users) {
      try {
        // Check if user already has a balance record
        const userId = user._id.toString();
        logToFile(`Processing user ${userId}`);
        const existingBalance = await postgresqlService.getUserBalance(userId);

        if (existingBalance) {
          logToFile(`User ${userId} already has a balance record`);
          logger.info(`User ${userId} already has a balance record`);
          existingCount++;
        } else {
          // Create balance record
          await postgresqlService.createUserBalance(userId);
          logToFile(`Created balance record for user ${userId}`);
          logger.info(`Created balance record for user ${userId}`);
          createdCount++;
        }
      } catch (error) {
        logToFile(`Error creating balance record for user ${user._id}: ${error.message}`);
        logger.error(`Error creating balance record for user ${user._id}: ${error.message}`);
        errorCount++;
      }
    }

    logToFile(`Balance record creation complete`);
    logToFile(`Created: ${createdCount}, Already existing: ${existingCount}, Errors: ${errorCount}`);
    logger.info(`Balance record creation complete`);
    logger.info(`Created: ${createdCount}, Already existing: ${existingCount}, Errors: ${errorCount}`);

    // Close connections
    await mongoose.disconnect();
    await sequelize.close();

    logToFile('Database connections closed');
    logger.info('Database connections closed');

    logToFile(`Log file written to: ${logFile}`);
  } catch (error) {
    logger.error(`Error in createBalanceRecords: ${error.message}`);

    // Close connections
    try {
      await mongoose.disconnect();
      await sequelize.close();
    } catch (closeError) {
      logger.error(`Error closing database connections: ${closeError.message}`);
    }

    process.exit(1);
  }
}

// Run the script
logToFile('Starting script...');

try {
  createBalanceRecords()
    .then(() => {
      logToFile('Script completed successfully');
      logger.info('Script completed successfully');
      process.exit(0);
    })
    .catch(error => {
      logToFile(`Script failed: ${error.message}`);
      logger.error(`Script failed: ${error.message}`);
      process.exit(1);
    });
} catch (error) {
  logToFile(`Uncaught error: ${error.message}`);
  logToFile(error.stack);
  process.exit(1);
}
