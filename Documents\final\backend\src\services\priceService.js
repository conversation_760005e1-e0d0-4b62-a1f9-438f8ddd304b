/**
 * Price Service
 * Handles fetching and caching token prices
 */

const axios = require('axios');
const redis = require('../config/redis');

// Cache TTL in seconds
const PRICE_CACHE_TTL = 60; // 1 minute

class PriceService {
  constructor() {
    // In-memory cache as fallback
    this.priceCache = new Map();
    this.lastUpdated = new Map();
  }

  /**
   * Get token price and 24h change
   * @param {string} symbol - Token symbol
   * @returns {Promise<Object>} - Price data with current price and 24h change
   */
  async getTokenPrice(symbol) {
    try {
      // Try to get from Redis cache first
      const cacheKey = `price:${symbol}`;
      let priceData;
      
      if (redis.client) {
        const cachedData = await redis.client.get(cacheKey);
        if (cachedData) {
          return JSON.parse(cachedData);
        }
      }
      
      // Check in-memory cache if Redis is not available
      if (this.priceCache.has(symbol)) {
        const cacheTime = this.lastUpdated.get(symbol);
        const now = Date.now();
        
        // Use cache if it's less than TTL seconds old
        if (now - cacheTime < PRICE_CACHE_TTL * 1000) {
          return this.priceCache.get(symbol);
        }
      }
      
      // Fetch fresh price data
      priceData = await this._fetchTokenPrice(symbol);
      
      // Cache the result
      if (redis.client) {
        await redis.client.set(cacheKey, JSON.stringify(priceData), 'EX', PRICE_CACHE_TTL);
      }
      
      // Update in-memory cache
      this.priceCache.set(symbol, priceData);
      this.lastUpdated.set(symbol, Date.now());
      
      return priceData;
    } catch (error) {
      console.error(`Error getting price for ${symbol}:`, error);
      
      // Return last cached value if available
      if (this.priceCache.has(symbol)) {
        return this.priceCache.get(symbol);
      }
      
      // Return default values if no cache available
      return {
        price: 0,
        change24h: 0
      };
    }
  }

  /**
   * Fetch token price from external API
   * @param {string} symbol - Token symbol
   * @returns {Promise<Object>} - Price data
   * @private
   */
  async _fetchTokenPrice(symbol) {
    // In a real implementation, this would call an external API
    // For now, return mock data based on the symbol
    
    // Mock price data for common tokens
    const mockPrices = {
      'SOL': { price: 107.50, change24h: 5.2 },
      'ETH': { price: 2413.00, change24h: -1.8 },
      'USDC': { price: 1.00, change24h: 0.0 },
      'MEME': { price: 0.01, change24h: 10.2 },
      'BTC': { price: 63250.00, change24h: 2.3 },
      'DOGE': { price: 0.12, change24h: -3.5 },
      'SHIB': { price: 0.00002, change24h: 15.7 },
      'AVAX': { price: 28.75, change24h: 4.1 },
      'MATIC': { price: 0.65, change24h: -2.4 },
      'DOT': { price: 6.82, change24h: 1.3 }
    };
    
    // Return mock data or default values
    return mockPrices[symbol] || { price: 1.00, change24h: 0.0 };
  }
}

module.exports = new PriceService();
