/**
 * Direct MongoDB models using the MongoDB driver
 * This provides an alternative to Mongoose models when needed
 */

const { getDB } = require('../config/mongodb_direct');

/**
 * User model for direct MongoDB access
 */
const UserDirect = {
  /**
   * Find a user by email
   * @param {string} email User email
   * @returns {Promise<Object>} User object
   */
  findByEmail: async (email) => {
    const db = getDB();
    return db.collection('users').findOne({ email });
  },
  
  /**
   * Find a user by ID
   * @param {string} id User ID
   * @returns {Promise<Object>} User object
   */
  findById: async (id) => {
    const db = getDB();
    return db.collection('users').findOne({ _id: id });
  },
  
  /**
   * Create a new user
   * @param {Object} userData User data
   * @returns {Promise<Object>} Created user
   */
  create: async (userData) => {
    const db = getDB();
    const result = await db.collection('users').insertOne(userData);
    return { ...userData, _id: result.insertedId };
  },
  
  /**
   * Count users
   * @param {Object} filter Filter criteria
   * @returns {Promise<number>} Count of users
   */
  count: async (filter = {}) => {
    const db = getDB();
    return db.collection('users').countDocuments(filter);
  },
  
  /**
   * Update a user
   * @param {string} id User ID
   * @param {Object} userData User data to update
   * @returns {Promise<Object>} Updated user
   */
  update: async (id, userData) => {
    const db = getDB();
    await db.collection('users').updateOne(
      { _id: id },
      { $set: userData }
    );
    return UserDirect.findById(id);
  }
};

/**
 * Transaction model for direct MongoDB access
 */
const TransactionDirect = {
  /**
   * Find transactions by filter
   * @param {Object} filter Filter criteria
   * @returns {Promise<Array>} Array of transactions
   */
  find: async (filter = {}) => {
    const db = getDB();
    return db.collection('transactions').find(filter).toArray();
  },
  
  /**
   * Get distinct values for a field
   * @param {string} field Field name
   * @param {Object} filter Filter criteria
   * @returns {Promise<Array>} Array of distinct values
   */
  distinct: async (field, filter = {}) => {
    const db = getDB();
    return db.collection('transactions').distinct(field, filter);
  },
  
  /**
   * Create a new transaction
   * @param {Object} transactionData Transaction data
   * @returns {Promise<Object>} Created transaction
   */
  create: async (transactionData) => {
    const db = getDB();
    const result = await db.collection('transactions').insertOne(transactionData);
    return { ...transactionData, _id: result.insertedId };
  }
};

module.exports = {
  UserDirect,
  TransactionDirect
};
