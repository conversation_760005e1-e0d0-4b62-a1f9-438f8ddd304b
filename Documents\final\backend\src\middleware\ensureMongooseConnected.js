/**
 * Middleware to ensure Mongoose is connected before handling requests
 * This prevents the "Operation buffering timed out after 10000ms" error
 */

const mongooseConnection = require('../config/mongoose_connection');

/**
 * Middleware that ensures Mongoose is connected before proceeding
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function ensureMongooseConnected(req, res, next) {
  try {
    // Check if Mongoose is already connected
    if (mongooseConnection.isMongooseConnected()) {
      return next();
    }

    console.log('Mongoose not connected, initializing connection...');
    console.log('MongoDB connection state:', mongooseConnection.getConnectionState());

    // Initialize Mongoose connection with retry logic
    let retries = 3;
    let connected = false;

    while (retries > 0 && !connected) {
      try {
        await mongooseConnection.initializeMongoose();
        connected = mongooseConnection.isMongooseConnected();
        if (connected) break;
      } catch (connError) {
        console.error(`Connection attempt failed, retries left: ${retries-1}`, connError);
        retries--;
        if (retries > 0) {
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    // Check connection state after initialization
    if (!mongooseConnection.isMongooseConnected()) {
      console.error('Failed to establish Mongoose connection after multiple attempts');
      return res.status(503).json({
        success: false,
        error: 'Database connection unavailable. Please try again later.'
      });
    }

    console.log('Mongoose connection established successfully');
    next();
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    return res.status(503).json({
      success: false,
      error: 'Database connection error. Please try again later.'
    });
  }
}

module.exports = ensureMongooseConnected;
