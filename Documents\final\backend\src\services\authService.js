const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const { promisify } = require('util');
const User = require('../models/User');
const RateLimit = require('../models/RateLimit');
const { validationResult } = require('express-validator');

// In production, JWT_SECRET must be set in environment variables
const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET && process.env.NODE_ENV === 'production') {
  console.error('ERROR: JWT_SECRET environment variable is not set in production mode!');
  process.exit(1);
}
const JWT_EXPIRES_IN = '24h';
const SALT_ROUNDS = 12;
const MAX_LOGIN_ATTEMPTS = 5;
const LOCKOUT_TIME = 15 * 60 * 1000; // 15 minutes in milliseconds

class AuthService {
  constructor() {
    this.generateToken = promisify(speakeasy.generateSecret);
    this.verifyToken = promisify(speakeasy.totp.verify);
  }

  // Input validation
  validateInput(req) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return {
        isValid: false,
        errors: errors.array()
      };
    }
    return { isValid: true };
  }

  // Rate limiting
  async checkRateLimit(ip, endpoint) {
    const now = Date.now();
    const rateLimit = await RateLimit.findOne({ ip, endpoint });

    if (!rateLimit) {
      await RateLimit.create({
        ip,
        endpoint,
        attempts: 1,
        lastAttempt: now
      });
      return { isLimited: false };
    }

    if (now - rateLimit.lastAttempt > LOCKOUT_TIME) {
      rateLimit.attempts = 1;
      rateLimit.lastAttempt = now;
      await rateLimit.save();
      return { isLimited: false };
    }

    if (rateLimit.attempts >= MAX_LOGIN_ATTEMPTS) {
      const timeLeft = Math.ceil((LOCKOUT_TIME - (now - rateLimit.lastAttempt)) / 1000 / 60);
      return {
        isLimited: true,
        message: `Too many attempts. Please try again in ${timeLeft} minutes.`
      };
    }

    rateLimit.attempts++;
    rateLimit.lastAttempt = now;
    await rateLimit.save();
    return { isLimited: false };
  }

  // Password hashing
  async hashPassword(password) {
    return bcrypt.hash(password, SALT_ROUNDS);
  }

  // Password verification
  async verifyPassword(password, hash) {
    return bcrypt.compare(password, hash);
  }

  // JWT token generation
  generateJWT(user) {
    return jwt.sign(
      {
        id: user._id,
        email: user.email,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );
  }

  // JWT token verification
  verifyJWT(token) {
    return new Promise((resolve, reject) => {
      try {
        const decoded = jwt.verify(token, JWT_SECRET);

        // Fetch user from database to ensure it still exists and is active
        User.findById(decoded.id)
          .then(user => {
            if (!user) {
              return reject(new Error('User not found'));
            }

            // Add user data to the decoded token
            decoded.username = user.username;
            decoded.walletAddress = user.walletAddress;

            resolve(decoded);
          })
          .catch(err => {
            reject(new Error('Failed to authenticate token'));
          });
      } catch (error) {
        reject(new Error('Invalid token'));
      }
    });
  }

  // MFA setup
  async setupMFA(userId) {
    const secret = await this.generateToken({
      length: 20,
      name: 'SWAP Token'
    });

    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    user.mfaSecret = secret.base32;
    user.mfaEnabled = false; // Will be enabled after verification
    await user.save();

    const qrCode = await QRCode.toDataURL(secret.otpauth_url);

    return {
      secret: secret.base32,
      qrCode
    };
  }

  // MFA verification
  async verifyMFA(userId, code) {
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const isValid = await this.verifyToken({
      secret: user.mfaSecret,
      encoding: 'base32',
      token: code
    });

    if (isValid && !user.mfaEnabled) {
      user.mfaEnabled = true;
      await user.save();
    }

    return isValid;
  }

  // Registration
  async register(userData) {
    const { email, password, username } = userData;

    // Check if user already exists
    const existingUser = await User.findOne({ $or: [{ email }, { username }] });
    if (existingUser) {
      throw new Error('Email or username already registered');
    }

    // Hash password
    const hashedPassword = await this.hashPassword(password);

    // Create user
    const user = await User.create({
      email,
      password: hashedPassword,
      username,
      mfaEnabled: false
    });

    // Generate JWT
    const token = this.generateJWT(user);

    return {
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        mfaEnabled: user.mfaEnabled
      },
      token
    };
  }

  // Login
  async login(credentials) {
    const { email, password } = credentials;

    // Find user
    const user = await User.findOne({ email });
    if (!user) {
      throw new Error('Invalid credentials');
    }

    // Verify password
    const isValidPassword = await this.verifyPassword(password, user.password);
    if (!isValidPassword) {
      throw new Error('Invalid credentials');
    }

    // Check if MFA is required
    if (user.mfaEnabled) {
      return {
        requiresMFA: true,
        userId: user._id
      };
    }

    // Generate JWT
    const token = this.generateJWT(user);

    return {
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        mfaEnabled: user.mfaEnabled
      },
      token
    };
  }

  // Logout
  async logout(userId) {
    // In a real application, you might want to:
    // 1. Invalidate the JWT token
    // 2. Clear any session data
    // 3. Update last logout time
    return true;
  }

  // Password reset request
  async requestPasswordReset(email) {
    const user = await User.findOne({ email });
    if (!user) {
      // Don't reveal if email exists
      return { success: true };
    }

    const resetToken = jwt.sign(
      { id: user._id },
      JWT_SECRET,
      { expiresIn: '1h' }
    );

    user.resetPasswordToken = resetToken;
    user.resetPasswordExpires = Date.now() + 3600000; // 1 hour
    await user.save();

    // In a real application, send reset email
    // await sendResetEmail(user.email, resetToken);

    return { success: true };
  }

  // Password reset
  async resetPassword(token, newPassword) {
    const decoded = this.verifyJWT(token);
    if (!decoded) {
      throw new Error('Invalid or expired reset token');
    }

    const user = await User.findOne({
      _id: decoded.id,
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() }
    });

    if (!user) {
      throw new Error('Invalid or expired reset token');
    }

    user.password = await this.hashPassword(newPassword);
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();

    return { success: true };
  }

  // Session management
  async updateLastActivity(userId) {
    await User.findByIdAndUpdate(userId, {
      lastActivity: Date.now()
    });
  }

  // Security audit logging
  async logSecurityEvent(userId, eventType, details) {
    // In a real application, implement security event logging
    console.log(`Security Event: ${eventType}`, {
      userId,
      timestamp: Date.now(),
      details
    });
  }
}

module.exports = new AuthService();