const multer = require('multer');
const path = require('path');
const fs = require('fs');
const mongoose = require('mongoose');
const User = require('../models/User');
const sharp = require('sharp');
const bcrypt = require('bcryptjs');

// Configure multer for memory storage (for image processing)
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only image files
    if (!file.mimetype.startsWith('image/')) {
      return cb(new Error('Only image files are allowed'), false);
    }
    cb(null, true);
  }
});

// Middleware to handle profile picture uploads with compression
const uploadProfilePicture = (req, res, next) => {
  // Use multer to handle the file upload
  upload.single('profilePicture')(req, res, async (err) => {
    if (err) {
      console.error('File upload error:', err);
      return res.status(400).json({ message: err.message || 'Error uploading file' });
    }

    try {
      // Check if user is authenticated
      if (!req.user || !req.user.id) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Check if file was uploaded
      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      console.log('Processing profile picture for user:', req.user.id);

      // Compress and resize the image
      try {
        // Resize and compress the image
        const compressedImageBuffer = await sharp(req.file.buffer)
          .resize(200, 200, { // Resize to 200x200 pixels for profile picture
            fit: 'cover',
            position: 'center'
          })
          .jpeg({ quality: 80 }) // Convert to JPEG with 80% quality
          .toBuffer();

        console.log('Image compressed successfully');

        // Convert the compressed image to base64 for storing in MongoDB
        const base64Image = `data:${req.file.mimetype};base64,${compressedImageBuffer.toString('base64')}`;

        // Find the user in the database
        let user;

        // Check if the ID is a valid ObjectId
        if (mongoose.Types.ObjectId.isValid(req.user.id)) {
          user = await User.findById(req.user.id);
        } else {
          // If not a valid ObjectId, try to find by username or email
          user = await User.findOne({
            $or: [
              { username: req.user.id },
              { email: req.user.id }
            ]
          });
        }

        if (!user) {
          // Handle admin user case
          if (req.user.id === 'admin123' || req.user.role === 'admin') {
            console.log('Creating temporary admin user object for file upload');

            // Try to find an existing admin user by email
            const adminUser = await User.findOne({ email: '<EMAIL>' });

            if (adminUser) {
              // Update existing admin user
              adminUser.profilePicture = base64Image;
              await adminUser.save();
              user = adminUser;
              console.log('Updated existing admin user with profile picture');
            } else {
              // Create a new admin user
              try {
                // Hash the password
                const salt = await bcrypt.genSalt(10);
                const hashedPassword = await bcrypt.hash('7973425726Rishab', salt);

                const newAdminUser = new User({
                  username: 'Admin',
                  email: '<EMAIL>',
                  password: hashedPassword,
                  role: 'admin',
                  profilePicture: base64Image
                });

                await newAdminUser.save();
                user = newAdminUser;
                console.log('Created new admin user with profile picture');
              } catch (adminError) {
                console.error('Error creating admin user:', adminError);
                return res.status(500).json({ message: 'Error creating admin user' });
              }
            }
          } else {
            return res.status(401).json({ message: 'User not found' });
          }
        } else {
          // Update the user's profile picture in the database
          user.profilePicture = base64Image;
          await user.save();
          console.log('Updated user profile picture in database');
        }

        // Add the base64 image to the request for the controller
        req.file.url = base64Image;

        // Attach full user object to request
        req.user = user;
        next();
      } catch (compressionError) {
        console.error('Error compressing image:', compressionError);
        return res.status(500).json({ message: 'Error processing image' });
      }
    } catch (error) {
      console.error('Profile picture middleware error:', error);
      return res.status(500).json({ message: 'Server error in profile picture middleware' });
    }
  });
};

module.exports = { uploadProfilePicture };
