const sessionService = require('../services/sessionService');
const Session = require('../models/Session');
const LoginHistory = require('../models/LoginHistory');

/**
 * Get active sessions for the authenticated user
 */
exports.getActiveSessions = async (req, res) => {
  try {
    const userId = req.user.id;
    const sessions = await sessionService.getActiveSessions(userId);
    
    // Format sessions for frontend
    const formattedSessions = sessions.map(session => ({
      id: session._id,
      device: session.device,
      browser: session.browser,
      location: session.location,
      ipAddress: session.ipAddress,
      lastActive: session.lastActive,
      isCurrentSession: session.sessionId === req.cookies.sessionId
    }));
    
    return res.json({
      success: true,
      sessions: formattedSessions
    });
  } catch (error) {
    console.error('Error getting active sessions:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve active sessions',
      error: error.message
    });
  }
};

/**
 * Terminate a specific session (sign out)
 */
exports.terminateSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const userId = req.user.id;
    
    // Verify the session belongs to the user
    const session = await Session.findOne({ _id: sessionId, userId });
    if (!session) {
      return res.status(404).json({
        success: false,
        message: 'Session not found'
      });
    }
    
    // Check if trying to terminate current session
    if (session.sessionId === req.cookies.sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot terminate current session. Use logout instead.'
      });
    }
    
    const success = await sessionService.terminateSession(session.sessionId);
    
    return res.json({
      success,
      message: success ? 'Session terminated successfully' : 'Failed to terminate session'
    });
  } catch (error) {
    console.error('Error terminating session:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to terminate session',
      error: error.message
    });
  }
};

/**
 * Terminate all sessions except the current one
 */
exports.terminateAllOtherSessions = async (req, res) => {
  try {
    const userId = req.user.id;
    const currentSessionId = req.cookies.sessionId;
    
    const count = await sessionService.terminateAllOtherSessions(userId, currentSessionId);
    
    return res.json({
      success: true,
      message: `${count} sessions terminated successfully`
    });
  } catch (error) {
    console.error('Error terminating all other sessions:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to terminate other sessions',
      error: error.message
    });
  }
};

/**
 * Get login history for the authenticated user
 */
exports.getLoginHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    const [loginHistory, total] = await Promise.all([
      sessionService.getLoginHistory(userId, limit, skip),
      sessionService.getLoginHistoryCount(userId)
    ]);
    
    // Format login history for frontend
    const formattedHistory = loginHistory.map(entry => ({
      id: entry._id,
      device: entry.device,
      browser: entry.browser,
      location: entry.location,
      ipAddress: entry.ipAddress,
      date: entry.timestamp,
      status: entry.status,
      failureReason: entry.failureReason
    }));
    
    return res.json({
      success: true,
      loginHistory: formattedHistory,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting login history:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve login history',
      error: error.message
    });
  }
};

/**
 * Update the last active time for the current session
 */
exports.updateSessionActivity = async (req, res) => {
  try {
    const sessionId = req.cookies.sessionId;
    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'No active session'
      });
    }
    
    const success = await sessionService.updateSessionActivity(sessionId);
    
    return res.json({
      success,
      message: success ? 'Session activity updated' : 'Failed to update session activity'
    });
  } catch (error) {
    console.error('Error updating session activity:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update session activity',
      error: error.message
    });
  }
};
