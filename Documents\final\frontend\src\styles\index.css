/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Lalezar&family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap');

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Lato', 'Arial', sans-serif;
  background-color: #000000;
  color: #ffffff;
  line-height: 1.6;
  overflow-x: hidden;
}

a {
  text-decoration: none;
  color: inherit;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navbar styles */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: rgba(18, 18, 18, 0.8);
  backdrop-filter: blur(10px);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 70px;
  align-items: center;
}

.navbar-logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #FF6B00;
  display: flex;
  align-items: center;
}

.navbar-right {
  display: flex;
  content: space-between;
  align-items: center;
  justify-content: flex-end;
}

.logo-image {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.navbar-links {
  display: flex;
  gap: 2rem;
  margin-left: 2rem;
}

.navbar-links a {
  color: #ffffff;
  transition: color 0.3s ease;
  font-weight: 500;
  position: relative;
  text-decoration: none;
}

.navbar-links a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 0;
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
  transition: width 0.3s ease;
}

.navbar-links a:hover {
  color: #FF6B00;
}

.navbar-links a:hover::after {
  width: 100%;
}

.navbar-wallet {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-left: 1.5rem;
}

.wallet-button {
  background: linear-gradient(90deg, #00FE83, #5131DE);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.wallet-button:hover {
  background: linear-gradient(90deg, #5131DE, #00FE83);
}

.icon-button {
  background-color: transparent;
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.icon-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.icon-button svg {
  stroke: white;
  transition: all 0.3s ease;
}

.icon-button:hover svg path,
.icon-button:hover svg circle {
  stroke: url(#icon-gradient);
}

.user-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Notification Overlay Styles */
.notification-overlay-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.notification-overlay {
  position: absolute;
  top: 70px;
  right: 20px;
  width: 300px;
  background-color: #1a1a1a;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  animation: slideDown 0.3s ease;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #333;
}

.notification-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: #fff;
}

.close-button {
  background: transparent;
  border: none;
  color: #aaa;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #fff;
}

.notification-content {
  padding: 15px;
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.no-notifications {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #777;
  font-size: 0.9rem;
}

.notification-item {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background-color: #222;
  transition: background-color 0.2s;
  cursor: pointer;
}

.notification-item:hover {
  background-color: #2a2a2a;
}

.notification-item.unread {
  border-left: 3px solid #FF6B00;
}

.notification-item.read {
  opacity: 0.7;
}

.notification-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-icon.transaction {
  background-color: rgba(0, 200, 83, 0.2);
  color: #00C853;
}

.notification-icon.alert {
  background-color: rgba(255, 61, 0, 0.2);
  color: #FF3D00;
}

.notification-icon.system {
  background-color: rgba(33, 150, 243, 0.2);
  color: #2196F3;
}

.notification-details {
  flex: 1;
}

.notification-title {
  font-weight: 500;
  margin-bottom: 5px;
  color: white;
}

.notification-message {
  font-size: 0.85rem;
  color: #aaa;
  margin-bottom: 5px;
  line-height: 1.4;
}

.notification-time {
  font-size: 0.75rem;
  color: #777;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Landing page styles */
.landing-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.landing-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
  background-image: url('../assets/background.jpg');
  background-position: 95% center; /* Move the background image much more to the right */
  background-size: 100% auto; /* Make the background image fill the width */
  background-repeat: no-repeat;
  background-blend-mode: normal;
  background-attachment: fixed;  /* Prevent background from scrolling */
  opacity: 1; /* Full opacity */
  z-index: -1;
}

.landing-content {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.landing-text {
  max-width: 500px;
  margin-left: -5%;  /* Move text slightly to the left */
}

.landing-title {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 1rem;
  line-height: 1;
  text-align: left;
}

.create-text {
  color: #FF9D00;
  text-shadow: 0 0 10px rgba(255, 157, 0, 0.7), 0 0 20px rgba(255, 157, 0, 0.5);
  font-size: 5rem;
  font-weight: bold;
  letter-spacing: 0.1em;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.trade-text {
  color: #ffffff;
  font-size: 5rem;
  font-weight: bold;
  letter-spacing: 0.1em;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.swap-text {
  color: #FF9D00;
  text-shadow: 0 0 10px rgba(255, 157, 0, 0.7), 0 0 20px rgba(255, 157, 0, 0.5);
  font-size: 5rem;
  font-weight: bold;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.get-started-btn {
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
  color: white;
  padding: 1rem 3rem;
  border-radius: 10px;
  font-weight: bold;
  font-size: 1.2rem;
  margin-top: 3rem;
  display: inline-block;
  transition: all 0.3s ease;
  text-align: center;
  box-shadow: 0 0 15px rgba(255, 107, 0, 0.5);
  width: 100%;
  max-width: 300px;
  height: 60px;
}

.get-started-btn:hover {
  background: white;
  color: black;
  transform: translateY(-3px);
  box-shadow: none;
}

.landing-image {
  position: relative;
}

.swap-logo-image {
  width: 200px;
  height: 200px;
  object-fit: contain;
}

.stats-box {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  background-color: rgba(30, 30, 30, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 1.5rem;
  width: 200px;
}

.stats-title {
  text-align: center;
  color: #999;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.stats-item {
  text-align: center;
  margin-bottom: 1rem;
}

.stats-value {
  font-size: 3rem;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 0.2rem;
}

.stats-label {
  font-size: 0.8rem;
  color: #999;
}

/* HomePage styles */
.home-page {
  padding-top: 70px;
  min-height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  overflow-x: hidden;
  position: relative;
}

.home-content {
  max-width: 100vw;
  margin: 0 auto;
  padding: 20px;
  overflow-x: hidden;
}

.home-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.home-header h1 {
  font-size: 1.5rem;
  font-weight: 500;
  color: #ccc;
}

.filter-buttons {
  display: flex;
  gap: 10px;
}

.filter-button {
  background-color: #1e1e1e;
  color: #fff;
  border: none;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.filter-button.active {
  background-color: #00C853;
}

.filter-button:hover:not(.active) {
  background-color: #333;
}

.trending-section {
  position: relative;
}

.trending-header {
  font-size: 1rem;
  font-weight: 500;
  padding-left: 5px;
  margin-bottom: 10px;
}

.navigation-buttons {
  display: flex;
  gap: 10px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
}

.nav-button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #1e1e1e00;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  z-index: 10;
}

.nav-button:hover {
  background-color: #333;
  transform: scale(1.1);
}

.nav-button.prev::before,
.nav-button.next::before {
  content: '';
  display: block;
  width: 10px;
  height: 10px;
  border-top: 2px solid #fff;
  border-right: 2px solid #fff;
}

.nav-button.prev::before {
  transform: rotate(-135deg);
}

.nav-button.next::before {
  transform: rotate(45deg);
}

/* Trending Section Styles */
.trending-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.trending-header {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #FF6B00, #FF2D78);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.carousel-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.carousel-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.05);
}

.carousel-indicators {
  display: flex;
  gap: 8px;
}

.carousel-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.carousel-indicator.active {
  background: linear-gradient(135deg, #FF6B00, #FF2D78);
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(255, 107, 0, 0.5);
}

.trending-carousel {
  display: flex;
  gap: 20px;
  overflow: hidden;
  padding: 10px 0;
  margin-bottom: 60px;
  /* Performance optimizations */
  will-change: transform;
  transform: translateZ(0);
}

.trending-carousel-track {
  display: flex;
  gap: 20px;
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.trending-carousel::-webkit-scrollbar {
  display: none;
}

/* Global Performance Optimizations */
* {
  box-sizing: border-box;
}

/* Reduce paint and layout operations */
.trending-card,
.explore-token-card {
  contain: layout style paint;
}

/* Optimize image rendering */
.trending-card-image img,
.explore-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Responsive Design for Trending Section */
@media (max-width: 768px) {
  .trending-header-container {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .carousel-controls {
    align-self: flex-end;
  }

  .trending-card {
    width: 280px;
  }

  .explore-token-card {
    max-width: 100%;
  }

  .explore-tokens {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 30px;
}

.create-token-btn,
.manage-tokens-btn {
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.create-token-btn {
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
  color: white;
  width: 200px;
  text-align: center;
}

.manage-tokens-btn {
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
  color: white;
  width: 200px;
  text-align: center;
}

.create-token-btn:hover,
.manage-tokens-btn:hover {
  background: white;
  color: black;
  opacity: 0.9;
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  margin-top: 50px;
}

.search-container {
  position: relative;
  width: 300px;
}

.search-input {
  width: 100%;
  padding: 8px 15px;
  border-radius: 20px;
  border: 1px solid #424242;
  background-color: #1e1e1e;
  color: white;
  font-size: 0.9rem;
}

.search-input::placeholder {
  color: #777;
}

.filter-options {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-top: 10px;
  padding-right: 20px;
}

.filter-select {
  padding: 6px 10px;
  background-color: #1e1e1e;
  color: white;
  border: none;
  border-radius: 5px;
}

.filter-option-btn {
  padding: 5px;
  background-color: #C25B45;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 0.6rem;
  font-weight: bold;
  cursor: pointer;
  font-family: 'Lato', sans-serif;
  text-transform: none;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.filter-option-btn:hover {
  background-color: #da6b52;
  color: white;
}

.explore-section h2 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 10px;
  padding-left: 5px;
}

.explore-tokens {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

/* New Explore Token Card Styles */
.explore-token-card {
  background: linear-gradient(145deg, #2a2a2a, #1e1e1e);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  max-width: 320px;
}

.explore-token-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 0, 0.05), rgba(255, 45, 120, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.explore-token-card:hover {
  transform: translateY(-8px);
  border-color: rgba(255, 107, 0, 0.3);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.explore-token-card:hover::before {
  opacity: 1;
}

.explore-card-header {
  padding: 20px 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 1;
}

.explore-card-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(145deg, #333, #1a1a1a);
}

.explore-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.explore-card-created {
  background: rgba(255, 255, 255, 0.1);
  color: #aaa;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: 500;
}

.explore-card-content {
  padding: 15px 20px;
  position: relative;
  z-index: 1;
}

.explore-card-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.explore-card-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin: 0;
}

.explore-card-symbol {
  background: linear-gradient(135deg, #FF6B00, #FF2D78);
  color: white;
  padding: 4px 10px;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.explore-card-creator {
  font-size: 0.85rem;
  color: #888;
  margin-bottom: 12px;
}

.creator-name {
  color: #FF6B00;
  font-weight: 600;
}

.explore-card-description {
  font-size: 0.9rem;
  color: #bbb;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.explore-card-footer {
  padding: 0 20px 20px;
  position: relative;
  z-index: 1;
}

.explore-card-price {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  text-align: center;
  margin-bottom: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.explore-card-stats {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  margin-bottom: 10px;
}

.explore-stat {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.explore-stat-label {
  font-size: 0.7rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.explore-stat-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #00ff88;
}

.explore-trending-badge {
  background: linear-gradient(135deg, #FF6B00, #FF2D78);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  margin-top: 10px;
}

/* New Trending Token Card Styles */
.trending-card {
  background: linear-gradient(145deg, #2a2a2a, #1e1e1e);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  width: 300px;
  height: 140px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.trending-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 0, 0.1), rgba(255, 45, 120, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.trending-card:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 107, 0, 0.3);
  box-shadow: 0 10px 30px rgba(255, 107, 0, 0.2);
}

.trending-card:hover::before {
  opacity: 1;
}

.trending-card-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.trending-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.trending-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.trending-card-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.trending-card-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.trending-card-symbol {
  background: rgba(255, 255, 255, 0.1);
  color: #ccc;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.trending-card-stats {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.trending-stat {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.trending-stat-label {
  font-size: 0.7rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.trending-stat-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #00ff88;
}


