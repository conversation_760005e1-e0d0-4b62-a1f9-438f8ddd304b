/**
 * Token Holdings Routes
 * API routes for token holdings data
 */
const express = require('express');
const router = express.Router();
const tokenHoldingsController = require('../controllers/tokenHoldingsController');
const { validateRequest } = require('../middleware/validationMiddleware');
const { authenticateToken } = require('../middleware/authMiddleware');

// Validation schemas
const topHoldersSchema = {
  params: {
    symbol: { type: 'string', required: true }
  },
  query: {
    limit: { type: 'number', min: 1, max: 100 },
    offset: { type: 'number', min: 0 }
  }
};

const userHoldingsSchema = {
  params: {
    userId: { type: 'string', required: true }
  }
};

const updateHoldingSchema = {
  params: {
    userId: { type: 'string', required: true },
    symbol: { type: 'string', required: true }
  },
  body: {
    balance: { type: 'number', required: true },
    value: { type: 'number', required: true },
    percentage: { type: 'number' },
    username: { type: 'string' }
  }
};

const updateTokenSupplySchema = {
  params: {
    symbol: { type: 'string', required: true }
  },
  body: {
    totalSupply: { type: 'number', required: true },
    circulatingSupply: { type: 'number', required: true }
  }
};

const deleteHoldingSchema = {
  params: {
    userId: { type: 'string', required: true },
    symbol: { type: 'string', required: true }
  }
};

// Public routes
router.get('/token/:symbol/holders', validateRequest(topHoldersSchema), tokenHoldingsController.getTopHolders);
router.get('/user/:userId/holdings', validateRequest(userHoldingsSchema), tokenHoldingsController.getUserHoldings);

// Protected routes (require authentication)
router.post('/token/:symbol/supply', authenticateToken, validateRequest(updateTokenSupplySchema), tokenHoldingsController.updateTokenSupply);
router.put('/user/:userId/holdings/:symbol', authenticateToken, validateRequest(updateHoldingSchema), tokenHoldingsController.updateHolding);
router.delete('/user/:userId/holdings/:symbol', authenticateToken, validateRequest(deleteHoldingSchema), tokenHoldingsController.deleteHolding);

// Development routes
if (process.env.NODE_ENV !== 'production') {
  router.post('/seed', tokenHoldingsController.seedSampleData);
}

module.exports = router;
