{"version": 3, "sources": ["../../@reown/appkit-utils/src/ConstantsUtil.ts", "../../@reown/appkit-utils/src/PresetsUtil.ts", "../../@reown/appkit-utils/src/HelpersUtil.ts", "../../@reown/appkit-utils/src/ErrorUtil.ts", "../../@reown/appkit-utils/src/LoggerUtil.ts", "../../@reown/appkit-utils/node_modules/viem/utils/formatters/transaction.ts", "../../@reown/appkit-utils/node_modules/viem/utils/formatters/block.ts", "../../@reown/appkit-utils/node_modules/viem/actions/public/getTransactionCount.ts", "../../@reown/appkit-utils/node_modules/viem/constants/blob.ts", "../../@reown/appkit-utils/node_modules/viem/utils/formatters/log.ts", "../../@reown/appkit-utils/node_modules/viem/utils/wait.ts", "../../@reown/appkit-utils/node_modules/viem/actions/wallet/sendTransaction.ts", "../../@reown/appkit-utils/node_modules/viem/utils/formatters/transactionReceipt.ts", "../../@reown/appkit-utils/node_modules/viem/utils/uid.ts", "../../@reown/appkit-utils/node_modules/viem/utils/promise/withDedupe.ts", "../../@reown/appkit-utils/node_modules/viem/utils/promise/withRetry.ts", "../../@reown/appkit-utils/node_modules/viem/utils/buildRequest.ts", "../../@reown/appkit-utils/node_modules/viem/clients/transports/createTransport.ts", "../../@reown/appkit-utils/node_modules/viem/clients/transports/fallback.ts", "../../@reown/appkit-utils/node_modules/viem/errors/transport.ts", "../../@reown/appkit-utils/node_modules/viem/utils/promise/withTimeout.ts", "../../@reown/appkit-utils/node_modules/viem/utils/rpc/id.ts", "../../@reown/appkit-utils/node_modules/viem/utils/rpc/http.ts", "../../@reown/appkit-utils/node_modules/viem/clients/transports/http.ts", "../../@reown/appkit-utils/node_modules/viem/node_modules/@noble/hashes/src/ripemd160.ts", "../../@reown/appkit-utils/node_modules/viem/utils/nonceManager.ts", "../../@reown/appkit-utils/node_modules/@noble/hashes/src/_assert.ts", "../../@reown/appkit-utils/node_modules/@noble/hashes/src/crypto.ts", "../../@reown/appkit-utils/node_modules/@noble/hashes/src/utils.ts", "../../@reown/appkit-utils/node_modules/@noble/hashes/src/_md.ts", "../../@reown/appkit-utils/node_modules/@noble/hashes/src/ripemd160.ts", "../../@reown/appkit-utils/node_modules/@noble/hashes/src/_u64.ts", "../../@reown/appkit-utils/node_modules/@noble/hashes/src/sha3.ts", "../../@reown/appkit-utils/node_modules/@noble/hashes/src/sha256.ts", "../../@reown/appkit-utils/node_modules/ox/core/Hash.ts", "../../@reown/appkit-utils/node_modules/ox/core/internal/lru.ts", "../../@reown/appkit-utils/node_modules/ox/core/Caches.ts", "../../@reown/appkit-utils/node_modules/ox/core/Address.ts", "../../@reown/appkit-utils/node_modules/ox/core/Solidity.ts", "../../@reown/appkit-utils/node_modules/ox/core/internal/cursor.ts", "../../@reown/appkit-utils/node_modules/ox/core/AbiParameters.ts", "../../@reown/appkit-utils/src/CaipNetworkUtil.ts", "../../@reown/appkit-utils/src/ProviderUtil.ts", "../../@reown/appkit-utils/src/TypeUtil.ts", "../../@reown/appkit-scaffold-ui/src/utils/ConnectorUtil.ts", "../../@reown/appkit-scaffold-ui/src/utils/WalletUtil.ts"], "sourcesContent": [null, null, null, null, null, "import type { ErrorType } from '../../errors/utils.js'\nimport type { SignedAuthorizationList } from '../../types/authorization.js'\nimport type { BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type {\n  ExtractChainFormatterExclude,\n  ExtractChainFormatterReturnType,\n} from '../../types/chain.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { RpcAuthorizationList, RpcTransaction } from '../../types/rpc.js'\nimport type { Transaction, TransactionType } from '../../types/transaction.js'\nimport type { ExactPartial, UnionLooseOmit } from '../../types/utils.js'\nimport { hexToNumber } from '../encoding/fromHex.js'\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\n\ntype TransactionPendingDependencies =\n  | 'blockHash'\n  | 'blockNumber'\n  | 'transactionIndex'\n\nexport type FormattedTransaction<\n  chain extends Chain | undefined = undefined,\n  blockTag extends BlockTag = BlockTag,\n  _FormatterReturnType = ExtractChainFormatterReturnType<\n    chain,\n    'transaction',\n    Transaction\n  >,\n  _ExcludedPendingDependencies extends string = TransactionPendingDependencies &\n    ExtractChainFormatterExclude<chain, 'transaction'>,\n> = UnionLooseOmit<_FormatterReturnType, TransactionPendingDependencies> & {\n  [_K in _ExcludedPendingDependencies]: never\n} & Pick<\n    Transaction<bigint, number, blockTag extends 'pending' ? true : false>,\n    TransactionPendingDependencies\n  >\n\nexport const transactionType = {\n  '0x0': 'legacy',\n  '0x1': 'eip2930',\n  '0x2': 'eip1559',\n  '0x3': 'eip4844',\n  '0x4': 'eip7702',\n} as const satisfies Record<Hex, TransactionType>\n\nexport type FormatTransactionErrorType = ErrorType\n\nexport function formatTransaction(transaction: ExactPartial<RpcTransaction>) {\n  const transaction_ = {\n    ...transaction,\n    blockHash: transaction.blockHash ? transaction.blockHash : null,\n    blockNumber: transaction.blockNumber\n      ? BigInt(transaction.blockNumber)\n      : null,\n    chainId: transaction.chainId ? hexToNumber(transaction.chainId) : undefined,\n    gas: transaction.gas ? BigInt(transaction.gas) : undefined,\n    gasPrice: transaction.gasPrice ? BigInt(transaction.gasPrice) : undefined,\n    maxFeePerBlobGas: transaction.maxFeePerBlobGas\n      ? BigInt(transaction.maxFeePerBlobGas)\n      : undefined,\n    maxFeePerGas: transaction.maxFeePerGas\n      ? BigInt(transaction.maxFeePerGas)\n      : undefined,\n    maxPriorityFeePerGas: transaction.maxPriorityFeePerGas\n      ? BigInt(transaction.maxPriorityFeePerGas)\n      : undefined,\n    nonce: transaction.nonce ? hexToNumber(transaction.nonce) : undefined,\n    to: transaction.to ? transaction.to : null,\n    transactionIndex: transaction.transactionIndex\n      ? Number(transaction.transactionIndex)\n      : null,\n    type: transaction.type\n      ? (transactionType as any)[transaction.type]\n      : undefined,\n    typeHex: transaction.type ? transaction.type : undefined,\n    value: transaction.value ? BigInt(transaction.value) : undefined,\n    v: transaction.v ? BigInt(transaction.v) : undefined,\n  } as Transaction\n\n  if (transaction.authorizationList)\n    transaction_.authorizationList = formatAuthorizationList(\n      transaction.authorizationList,\n    )\n\n  transaction_.yParity = (() => {\n    // If `yParity` is provided, we will use it.\n    if (transaction.yParity) return Number(transaction.yParity)\n\n    // If no `yParity` provided, try derive from `v`.\n    if (typeof transaction_.v === 'bigint') {\n      if (transaction_.v === 0n || transaction_.v === 27n) return 0\n      if (transaction_.v === 1n || transaction_.v === 28n) return 1\n      if (transaction_.v >= 35n) return transaction_.v % 2n === 0n ? 1 : 0\n    }\n\n    return undefined\n  })()\n\n  if (transaction_.type === 'legacy') {\n    delete transaction_.accessList\n    delete transaction_.maxFeePerBlobGas\n    delete transaction_.maxFeePerGas\n    delete transaction_.maxPriorityFeePerGas\n    delete transaction_.yParity\n  }\n  if (transaction_.type === 'eip2930') {\n    delete transaction_.maxFeePerBlobGas\n    delete transaction_.maxFeePerGas\n    delete transaction_.maxPriorityFeePerGas\n  }\n  if (transaction_.type === 'eip1559') {\n    delete transaction_.maxFeePerBlobGas\n  }\n  return transaction_\n}\n\nexport type DefineTransactionErrorType = DefineFormatterErrorType | ErrorType\n\nexport const defineTransaction = /*#__PURE__*/ defineFormatter(\n  'transaction',\n  formatTransaction,\n)\n\n//////////////////////////////////////////////////////////////////////////////\n\nfunction formatAuthorizationList(\n  authorizationList: RpcAuthorizationList,\n): SignedAuthorizationList {\n  return authorizationList.map((authorization) => ({\n    address: (authorization as any).address,\n    chainId: Number(authorization.chainId),\n    nonce: Number(authorization.nonce),\n    r: authorization.r,\n    s: authorization.s,\n    yParity: Number(authorization.yParity),\n  })) as SignedAuthorizationList\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Block, BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type {\n  ExtractChainFormatterExclude,\n  ExtractChainFormatterReturnType,\n} from '../../types/chain.js'\nimport type { Hash } from '../../types/misc.js'\nimport type { RpcBlock } from '../../types/rpc.js'\nimport type { ExactPartial, Prettify } from '../../types/utils.js'\n\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\nimport { type FormattedTransaction, formatTransaction } from './transaction.js'\n\ntype BlockPendingDependencies = 'hash' | 'logsBloom' | 'nonce' | 'number'\n\nexport type FormattedBlock<\n  chain extends Chain | undefined = undefined,\n  includeTransactions extends boolean = boolean,\n  blockTag extends BlockTag = BlockTag,\n  _FormatterReturnType = ExtractChainFormatterReturnType<\n    chain,\n    'block',\n    Block<bigint, includeTransactions>\n  >,\n  _ExcludedPendingDependencies extends string = BlockPendingDependencies &\n    ExtractChainFormatterExclude<chain, 'block'>,\n  _Formatted = Omit<_FormatterReturnType, BlockPendingDependencies> & {\n    [_key in _ExcludedPendingDependencies]: never\n  } & Pick<\n      Block<bigint, includeTransactions, blockTag>,\n      BlockPendingDependencies\n    >,\n  _Transactions = includeTransactions extends true\n    ? Prettify<FormattedTransaction<chain, blockTag>>[]\n    : Hash[],\n> = Omit<_Formatted, 'transactions'> & {\n  transactions: _Transactions\n}\n\nexport type FormatBlockErrorType = ErrorType\n\nexport function formatBlock(block: ExactPartial<RpcBlock>) {\n  const transactions = (block.transactions ?? []).map((transaction) => {\n    if (typeof transaction === 'string') return transaction\n    return formatTransaction(transaction)\n  })\n  return {\n    ...block,\n    baseFeePerGas: block.baseFeePerGas ? BigInt(block.baseFeePerGas) : null,\n    blobGasUsed: block.blobGasUsed ? BigInt(block.blobGasUsed) : undefined,\n    difficulty: block.difficulty ? BigInt(block.difficulty) : undefined,\n    excessBlobGas: block.excessBlobGas\n      ? BigInt(block.excessBlobGas)\n      : undefined,\n    gasLimit: block.gasLimit ? BigInt(block.gasLimit) : undefined,\n    gasUsed: block.gasUsed ? BigInt(block.gasUsed) : undefined,\n    hash: block.hash ? block.hash : null,\n    logsBloom: block.logsBloom ? block.logsBloom : null,\n    nonce: block.nonce ? block.nonce : null,\n    number: block.number ? BigInt(block.number) : null,\n    size: block.size ? BigInt(block.size) : undefined,\n    timestamp: block.timestamp ? BigInt(block.timestamp) : undefined,\n    transactions,\n    totalDifficulty: block.totalDifficulty\n      ? BigInt(block.totalDifficulty)\n      : null,\n  } as Block\n}\n\nexport type DefineBlockErrorType = DefineFormatterErrorType | ErrorType\n\nexport const defineBlock = /*#__PURE__*/ defineFormatter('block', formatBlock)\n", "import type { Address } from 'abitype'\n\nimport type { Account } from '../../accounts/types.js'\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { RequestErrorType } from '../../utils/buildRequest.js'\nimport {\n  type HexToNumberErrorType,\n  hexToNumber,\n} from '../../utils/encoding/fromHex.js'\nimport {\n  type NumberToHexErrorType,\n  numberToHex,\n} from '../../utils/encoding/toHex.js'\n\nexport type GetTransactionCountParameters = {\n  /** The account address. */\n  address: Address\n} & (\n  | {\n      /** The block number. */\n      blockNumber?: bigint | undefined\n      blockTag?: undefined\n    }\n  | {\n      blockNumber?: undefined\n      /** The block tag. Defaults to 'latest'. */\n      blockTag?: BlockTag | undefined\n    }\n)\nexport type GetTransactionCountReturnType = number\n\nexport type GetTransactionCountErrorType =\n  | RequestErrorType\n  | NumberToHexErrorType\n  | HexToNumberErrorType\n  | ErrorType\n\n/**\n * Returns the number of [Transactions](https://viem.sh/docs/glossary/terms#transaction) an Account has sent.\n *\n * - Docs: https://viem.sh/docs/actions/public/getTransactionCount\n * - JSON-RPC Methods: [`eth_getTransactionCount`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_gettransactioncount)\n *\n * @param client - Client to use\n * @param parameters - {@link GetTransactionCountParameters}\n * @returns The number of transactions an account has sent. {@link GetTransactionCountReturnType}\n *\n * @example\n * import { createPublicClient, http } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { getTransactionCount } from 'viem/public'\n *\n * const client = createPublicClient({\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const transactionCount = await getTransactionCount(client, {\n *   address: '******************************************',\n * })\n */\nexport async function getTransactionCount<\n  chain extends Chain | undefined,\n  account extends Account | undefined,\n>(\n  client: Client<Transport, chain, account>,\n  { address, blockTag = 'latest', blockNumber }: GetTransactionCountParameters,\n): Promise<GetTransactionCountReturnType> {\n  const count = await client.request(\n    {\n      method: 'eth_getTransactionCount',\n      params: [\n        address,\n        typeof blockNumber === 'bigint' ? numberToHex(blockNumber) : blockTag,\n      ],\n    },\n    {\n      dedupe: Boolean(blockNumber),\n    },\n  )\n  return hexToNumber(count)\n}\n", "// https://github.com/ethereum/EIPs/blob/master/EIPS/eip-4844.md#parameters\n\n/** Blob limit per transaction. */\nconst blobsPerTransaction = 6\n\n/** The number of bytes in a BLS scalar field element. */\nexport const bytesPerFieldElement = 32\n\n/** The number of field elements in a blob. */\nexport const fieldElementsPerBlob = 4096\n\n/** The number of bytes in a blob. */\nexport const bytesPerBlob = bytesPerFieldElement * fieldElementsPerBlob\n\n/** Blob bytes limit per transaction. */\nexport const maxBytesPerTransaction =\n  bytesPerBlob * blobsPerTransaction -\n  // terminator byte (0x80).\n  1 -\n  // zero byte (0x00) appended to each field element.\n  1 * fieldElementsPerBlob * blobsPerTransaction\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Log } from '../../types/log.js'\nimport type { RpcLog } from '../../types/rpc.js'\nimport type { ExactPartial } from '../../types/utils.js'\n\nexport type FormatLogErrorType = ErrorType\n\nexport function formatLog(\n  log: ExactPartial<RpcLog>,\n  {\n    args,\n    eventName,\n  }: { args?: unknown | undefined; eventName?: string | undefined } = {},\n) {\n  return {\n    ...log,\n    blockHash: log.blockHash ? log.blockHash : null,\n    blockNumber: log.blockNumber ? BigInt(log.blockNumber) : null,\n    logIndex: log.logIndex ? Number(log.logIndex) : null,\n    transactionHash: log.transactionHash ? log.transactionHash : null,\n    transactionIndex: log.transactionIndex\n      ? Number(log.transactionIndex)\n      : null,\n    ...(eventName ? { args, eventName } : {}),\n  } as Log\n}\n", "export async function wait(time: number) {\n  return new Promise((res) => setTimeout(res, time))\n}\n", "import type { Address } from 'abitype'\n\nimport type { Account } from '../../accounts/types.js'\nimport {\n  type ParseAccountErrorType,\n  parseAccount,\n} from '../../accounts/utils/parseAccount.js'\nimport type { SignTransactionErrorType } from '../../accounts/utils/signTransaction.js'\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport {\n  AccountNotFoundError,\n  type AccountNotFoundErrorType,\n  AccountTypeNotSupportedError,\n  type AccountTypeNotSupportedErrorType,\n} from '../../errors/account.js'\nimport { BaseError } from '../../errors/base.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { GetAccountParameter } from '../../types/account.js'\nimport type {\n  Chain,\n  Derive<PERSON>hain,\n  GetChainParameter,\n} from '../../types/chain.js'\nimport type { GetTransactionRequestKzgParameter } from '../../types/kzg.js'\nimport type { Hash } from '../../types/misc.js'\nimport type { TransactionRequest } from '../../types/transaction.js'\nimport type { UnionOmit } from '../../types/utils.js'\nimport {\n  type RecoverAuthorizationAddressErrorType,\n  recoverAuthorizationAddress,\n} from '../../utils/authorization/recoverAuthorizationAddress.js'\nimport type { RequestErrorType } from '../../utils/buildRequest.js'\nimport {\n  type AssertCurrentChainErrorType,\n  assertCurrentChain,\n} from '../../utils/chain/assertCurrentChain.js'\nimport {\n  type GetTransactionErrorReturnType,\n  getTransactionError,\n} from '../../utils/errors/getTransactionError.js'\nimport { extract } from '../../utils/formatters/extract.js'\nimport {\n  type FormattedTransactionRequest,\n  formatTransactionRequest,\n} from '../../utils/formatters/transactionRequest.js'\nimport { getAction } from '../../utils/getAction.js'\nimport { LruMap } from '../../utils/lru.js'\nimport {\n  type AssertRequestErrorType,\n  type AssertRequestParameters,\n  assertRequest,\n} from '../../utils/transaction/assertRequest.js'\nimport { type GetChainIdErrorType, getChainId } from '../public/getChainId.js'\nimport {\n  type PrepareTransactionRequestErrorType,\n  defaultParameters,\n  prepareTransactionRequest,\n} from './prepareTransactionRequest.js'\nimport {\n  type SendRawTransactionErrorType,\n  sendRawTransaction,\n} from './sendRawTransaction.js'\n\nconst supportsWalletNamespace = new LruMap<boolean>(128)\n\nexport type SendTransactionRequest<\n  chain extends Chain | undefined = Chain | undefined,\n  chainOverride extends Chain | undefined = Chain | undefined,\n  ///\n  _derivedChain extends Chain | undefined = DeriveChain<chain, chainOverride>,\n> = UnionOmit<FormattedTransactionRequest<_derivedChain>, 'from'> &\n  GetTransactionRequestKzgParameter\n\nexport type SendTransactionParameters<\n  chain extends Chain | undefined = Chain | undefined,\n  account extends Account | undefined = Account | undefined,\n  chainOverride extends Chain | undefined = Chain | undefined,\n  request extends SendTransactionRequest<\n    chain,\n    chainOverride\n  > = SendTransactionRequest<chain, chainOverride>,\n> = request &\n  GetAccountParameter<account, Account | Address, true, true> &\n  GetChainParameter<chain, chainOverride> &\n  GetTransactionRequestKzgParameter<request>\n\nexport type SendTransactionReturnType = Hash\n\nexport type SendTransactionErrorType =\n  | ParseAccountErrorType\n  | GetTransactionErrorReturnType<\n      | AccountNotFoundErrorType\n      | AccountTypeNotSupportedErrorType\n      | AssertCurrentChainErrorType\n      | AssertRequestErrorType\n      | GetChainIdErrorType\n      | PrepareTransactionRequestErrorType\n      | SendRawTransactionErrorType\n      | RecoverAuthorizationAddressErrorType\n      | SignTransactionErrorType\n      | RequestErrorType\n    >\n  | ErrorType\n\n/**\n * Creates, signs, and sends a new transaction to the network.\n *\n * - Docs: https://viem.sh/docs/actions/wallet/sendTransaction\n * - Examples: https://stackblitz.com/github/wevm/viem/tree/main/examples/transactions_sending-transactions\n * - JSON-RPC Methods:\n *   - JSON-RPC Accounts: [`eth_sendTransaction`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_sendtransaction)\n *   - Local Accounts: [`eth_sendRawTransaction`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_sendrawtransaction)\n *\n * @param client - Client to use\n * @param parameters - {@link SendTransactionParameters}\n * @returns The [Transaction](https://viem.sh/docs/glossary/terms#transaction) hash. {@link SendTransactionReturnType}\n *\n * @example\n * import { createWalletClient, custom } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { sendTransaction } from 'viem/wallet'\n *\n * const client = createWalletClient({\n *   chain: mainnet,\n *   transport: custom(window.ethereum),\n * })\n * const hash = await sendTransaction(client, {\n *   account: '******************************************',\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n *\n * @example\n * // Account Hoisting\n * import { createWalletClient, http } from 'viem'\n * import { privateKeyToAccount } from 'viem/accounts'\n * import { mainnet } from 'viem/chains'\n * import { sendTransaction } from 'viem/wallet'\n *\n * const client = createWalletClient({\n *   account: privateKeyToAccount('0x…'),\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const hash = await sendTransaction(client, {\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n */\nexport async function sendTransaction<\n  chain extends Chain | undefined,\n  account extends Account | undefined,\n  const request extends SendTransactionRequest<chain, chainOverride>,\n  chainOverride extends Chain | undefined = undefined,\n>(\n  client: Client<Transport, chain, account>,\n  parameters: SendTransactionParameters<chain, account, chainOverride, request>,\n): Promise<SendTransactionReturnType> {\n  const {\n    account: account_ = client.account,\n    chain = client.chain,\n    accessList,\n    authorizationList,\n    blobs,\n    data,\n    gas,\n    gasPrice,\n    maxFeePerBlobGas,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    nonce,\n    type,\n    value,\n    ...rest\n  } = parameters\n\n  if (typeof account_ === 'undefined')\n    throw new AccountNotFoundError({\n      docsPath: '/docs/actions/wallet/sendTransaction',\n    })\n  const account = account_ ? parseAccount(account_) : null\n\n  try {\n    assertRequest(parameters as AssertRequestParameters)\n\n    const to = await (async () => {\n      // If `to` exists on the parameters, use that.\n      if (parameters.to) return parameters.to\n\n      // If `to` is null, we are sending a deployment transaction.\n      if (parameters.to === null) return undefined\n\n      // If no `to` exists, and we are sending a EIP-7702 transaction, use the\n      // address of the first authorization in the list.\n      if (authorizationList && authorizationList.length > 0)\n        return await recoverAuthorizationAddress({\n          authorization: authorizationList[0],\n        }).catch(() => {\n          throw new BaseError(\n            '`to` is required. Could not infer from `authorizationList`.',\n          )\n        })\n\n      // Otherwise, we are sending a deployment transaction.\n      return undefined\n    })()\n\n    if (account?.type === 'json-rpc' || account === null) {\n      let chainId: number | undefined\n      if (chain !== null) {\n        chainId = await getAction(client, getChainId, 'getChainId')({})\n        assertCurrentChain({\n          currentChainId: chainId,\n          chain,\n        })\n      }\n\n      const chainFormat = client.chain?.formatters?.transactionRequest?.format\n      const format = chainFormat || formatTransactionRequest\n\n      const request = format({\n        // Pick out extra data that might exist on the chain's transaction request type.\n        ...extract(rest, { format: chainFormat }),\n        accessList,\n        authorizationList,\n        blobs,\n        chainId,\n        data,\n        from: account?.address,\n        gas,\n        gasPrice,\n        maxFeePerBlobGas,\n        maxFeePerGas,\n        maxPriorityFeePerGas,\n        nonce,\n        to,\n        type,\n        value,\n      } as TransactionRequest)\n\n      const isWalletNamespaceSupported = supportsWalletNamespace.get(client.uid)\n      const method = isWalletNamespaceSupported\n        ? 'wallet_sendTransaction'\n        : 'eth_sendTransaction'\n\n      try {\n        return await client.request(\n          {\n            method,\n            params: [request],\n          },\n          { retryCount: 0 },\n        )\n      } catch (e) {\n        if (isWalletNamespaceSupported === false) throw e\n\n        const error = e as BaseError\n        // If the transport does not support the method or input, attempt to use the\n        // `wallet_sendTransaction` method.\n        if (\n          error.name === 'InvalidInputRpcError' ||\n          error.name === 'InvalidParamsRpcError' ||\n          error.name === 'MethodNotFoundRpcError' ||\n          error.name === 'MethodNotSupportedRpcError'\n        ) {\n          return await client\n            .request(\n              {\n                method: 'wallet_sendTransaction',\n                params: [request],\n              },\n              { retryCount: 0 },\n            )\n            .then((hash) => {\n              supportsWalletNamespace.set(client.uid, true)\n              return hash\n            })\n            .catch((e) => {\n              const walletNamespaceError = e as BaseError\n              if (\n                walletNamespaceError.name === 'MethodNotFoundRpcError' ||\n                walletNamespaceError.name === 'MethodNotSupportedRpcError'\n              ) {\n                supportsWalletNamespace.set(client.uid, false)\n                throw error\n              }\n\n              throw walletNamespaceError\n            })\n        }\n\n        throw error\n      }\n    }\n\n    if (account?.type === 'local') {\n      // Prepare the request for signing (assign appropriate fees, etc.)\n      const request = await getAction(\n        client,\n        prepareTransactionRequest,\n        'prepareTransactionRequest',\n      )({\n        account,\n        accessList,\n        authorizationList,\n        blobs,\n        chain,\n        data,\n        gas,\n        gasPrice,\n        maxFeePerBlobGas,\n        maxFeePerGas,\n        maxPriorityFeePerGas,\n        nonce,\n        nonceManager: account.nonceManager,\n        parameters: [...defaultParameters, 'sidecars'],\n        type,\n        value,\n        ...rest,\n        to,\n      } as any)\n\n      const serializer = chain?.serializers?.transaction\n      const serializedTransaction = (await account.signTransaction(request, {\n        serializer,\n      })) as Hash\n      return await getAction(\n        client,\n        sendRawTransaction,\n        'sendRawTransaction',\n      )({\n        serializedTransaction,\n      })\n    }\n\n    if (account?.type === 'smart')\n      throw new AccountTypeNotSupportedError({\n        metaMessages: [\n          'Consider using the `sendUserOperation` Action instead.',\n        ],\n        docsPath: '/docs/actions/bundler/sendUserOperation',\n        type: 'smart',\n      })\n\n    throw new AccountTypeNotSupportedError({\n      docsPath: '/docs/actions/wallet/sendTransaction',\n      type: (account as any)?.type,\n    })\n  } catch (err) {\n    if (err instanceof AccountTypeNotSupportedError) throw err\n    throw getTransactionError(err as BaseError, {\n      ...parameters,\n      account,\n      chain: parameters.chain || undefined,\n    })\n  }\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type {\n  Chain,\n  ExtractChainFormatterReturnType,\n} from '../../types/chain.js'\nimport type { RpcTransactionReceipt } from '../../types/rpc.js'\nimport type { TransactionReceipt } from '../../types/transaction.js'\nimport type { ExactPartial } from '../../types/utils.js'\nimport { hexToNumber } from '../encoding/fromHex.js'\n\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\nimport { formatLog } from './log.js'\nimport { transactionType } from './transaction.js'\n\nexport type FormattedTransactionReceipt<\n  chain extends Chain | undefined = undefined,\n> = ExtractChainFormatterReturnType<\n  chain,\n  'transactionReceipt',\n  TransactionReceipt\n>\n\nexport const receiptStatuses = {\n  '0x0': 'reverted',\n  '0x1': 'success',\n} as const\n\nexport type FormatTransactionReceiptErrorType = ErrorType\n\nexport function formatTransactionReceipt(\n  transactionReceipt: ExactPartial<RpcTransactionReceipt>,\n) {\n  const receipt = {\n    ...transactionReceipt,\n    blockNumber: transactionReceipt.blockNumber\n      ? BigInt(transactionReceipt.blockNumber)\n      : null,\n    contractAddress: transactionReceipt.contractAddress\n      ? transactionReceipt.contractAddress\n      : null,\n    cumulativeGasUsed: transactionReceipt.cumulativeGasUsed\n      ? BigInt(transactionReceipt.cumulativeGasUsed)\n      : null,\n    effectiveGasPrice: transactionReceipt.effectiveGasPrice\n      ? BigInt(transactionReceipt.effectiveGasPrice)\n      : null,\n    gasUsed: transactionReceipt.gasUsed\n      ? BigInt(transactionReceipt.gasUsed)\n      : null,\n    logs: transactionReceipt.logs\n      ? transactionReceipt.logs.map((log) => formatLog(log))\n      : null,\n    to: transactionReceipt.to ? transactionReceipt.to : null,\n    transactionIndex: transactionReceipt.transactionIndex\n      ? hexToNumber(transactionReceipt.transactionIndex)\n      : null,\n    status: transactionReceipt.status\n      ? receiptStatuses[transactionReceipt.status]\n      : null,\n    type: transactionReceipt.type\n      ? transactionType[\n          transactionReceipt.type as keyof typeof transactionType\n        ] || transactionReceipt.type\n      : null,\n  } as TransactionReceipt\n\n  if (transactionReceipt.blobGasPrice)\n    receipt.blobGasPrice = BigInt(transactionReceipt.blobGasPrice)\n  if (transactionReceipt.blobGasUsed)\n    receipt.blobGasUsed = BigInt(transactionReceipt.blobGasUsed)\n\n  return receipt\n}\n\nexport type DefineTransactionReceiptErrorType =\n  | DefineFormatterErrorType\n  | ErrorType\n\nexport const defineTransactionReceipt = /*#__PURE__*/ defineFormatter(\n  'transactionReceipt',\n  formatTransactionReceipt,\n)\n", "const size = 256\nlet index = size\nlet buffer: string\n\nexport function uid(length = 11) {\n  if (!buffer || index + length > size * 2) {\n    buffer = ''\n    index = 0\n    for (let i = 0; i < size; i++) {\n      buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1)\n    }\n  }\n  return buffer.substring(index, index++ + length)\n}\n", "import { LruMap } from '../lru.js'\n\n/** @internal */\nexport const promiseCache = /*#__PURE__*/ new LruMap<Promise<any>>(8192)\n\ntype WithDedupeOptions = {\n  enabled?: boolean | undefined\n  id?: string | undefined\n}\n\n/** Deduplicates in-flight promises. */\nexport function withDedupe<data>(\n  fn: () => Promise<data>,\n  { enabled = true, id }: WithDedupeOptions,\n): Promise<data> {\n  if (!enabled || !id) return fn()\n  if (promiseCache.get(id)) return promiseCache.get(id)!\n  const promise = fn().finally(() => promiseCache.delete(id))\n  promiseCache.set(id, promise)\n  return promise\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport { wait } from '../wait.js'\n\nexport type WithRetryParameters = {\n  // The delay (in ms) between retries.\n  delay?:\n    | ((config: { count: number; error: Error }) => number)\n    | number\n    | undefined\n  // The max number of times to retry.\n  retryCount?: number | undefined\n  // Whether or not to retry when an error is thrown.\n  shouldRetry?:\n    | (({\n        count,\n        error,\n      }: {\n        count: number\n        error: Error\n      }) => Promise<boolean> | boolean)\n    | undefined\n}\n\nexport type WithRetryErrorType = ErrorType\n\nexport function withRetry<data>(\n  fn: () => Promise<data>,\n  {\n    delay: delay_ = 100,\n    retryCount = 2,\n    shouldRetry = () => true,\n  }: WithRetryParameters = {},\n) {\n  return new Promise<data>((resolve, reject) => {\n    const attemptRetry = async ({ count = 0 } = {}) => {\n      const retry = async ({ error }: { error: Error }) => {\n        const delay =\n          typeof delay_ === 'function' ? delay_({ count, error }) : delay_\n        if (delay) await wait(delay)\n        attemptRetry({ count: count + 1 })\n      }\n\n      try {\n        const data = await fn()\n        resolve(data)\n      } catch (err) {\n        if (\n          count < retryCount &&\n          (await shouldRetry({ count, error: err as Error }))\n        )\n          return retry({ error: err as Error })\n        reject(err)\n      }\n    }\n    attemptRetry()\n  })\n}\n", "import { BaseError } from '../errors/base.js'\nimport {\n  HttpRequestError,\n  type HttpRequestErrorType,\n  type RpcRequestErrorType,\n  type TimeoutErrorType,\n  type WebSocketRequestErrorType,\n} from '../errors/request.js'\nimport {\n  AtomicReadyWalletRejectedUpgradeError,\n  type AtomicReadyWalletRejectedUpgradeErrorType,\n  AtomicityNotSupportedError,\n  type AtomicityNotSupportedErrorType,\n  BundleTooLargeError,\n  type BundleTooLargeErrorType,\n  ChainDisconnectedError,\n  type ChainDisconnectedErrorType,\n  DuplicateIdError,\n  type DuplicateIdErrorType,\n  InternalRpcError,\n  type InternalRpcErrorType,\n  InvalidInputRpcError,\n  type InvalidInputRpcErrorType,\n  InvalidParamsRpcError,\n  type InvalidParamsRpcErrorType,\n  InvalidRequestRpcError,\n  type InvalidRequestRpcErrorType,\n  JsonRpcVersionUnsupportedError,\n  type JsonRpcVersionUnsupportedErrorType,\n  LimitExceededRpcError,\n  type LimitExceededRpcErrorType,\n  MethodNotFoundRpcError,\n  type MethodNotFoundRpcErrorType,\n  MethodNotSupportedRpcError,\n  type MethodNotSupportedRpcErrorType,\n  ParseRpcError,\n  type ParseRpcErrorType,\n  ProviderDisconnectedError,\n  type ProviderDisconnectedErrorType,\n  type ProviderRpcErrorCode,\n  ResourceNotFoundRpcError,\n  type ResourceNotFoundRpcErrorType,\n  ResourceUnavailableRpcError,\n  type ResourceUnavailableRpcErrorType,\n  type RpcError,\n  type RpcErrorCode,\n  type RpcErrorType,\n  SwitchChainError,\n  type SwitchChainErrorType,\n  TransactionRejectedRpcError,\n  type TransactionRejectedRpcErrorType,\n  UnauthorizedProviderError,\n  type UnauthorizedProviderErrorType,\n  UnknownBundleIdError,\n  type UnknownBundleIdErrorType,\n  UnknownRpcError,\n  type UnknownRpcErrorType,\n  UnsupportedChainIdError,\n  type UnsupportedChainIdErrorType,\n  UnsupportedNonOptionalCapabilityError,\n  type UnsupportedNonOptionalCapabilityErrorType,\n  UnsupportedProviderMethodError,\n  type UnsupportedProviderMethodErrorType,\n  UserRejectedRequestError,\n  type UserRejectedRequestErrorType,\n} from '../errors/rpc.js'\nimport type { ErrorType } from '../errors/utils.js'\nimport type {\n  EIP1193RequestFn,\n  EIP1193RequestOptions,\n} from '../types/eip1193.js'\nimport { stringToHex } from './encoding/toHex.js'\nimport type { CreateBatchSchedulerErrorType } from './promise/createBatchScheduler.js'\nimport { withDedupe } from './promise/withDedupe.js'\nimport { type WithRetryErrorType, withRetry } from './promise/withRetry.js'\nimport type { GetSocketRpcClientErrorType } from './rpc/socket.js'\nimport { stringify } from './stringify.js'\n\nexport type RequestErrorType =\n  | AtomicityNotSupportedErrorType\n  | AtomicReadyWalletRejectedUpgradeErrorType\n  | BundleTooLargeErrorType\n  | ChainDisconnectedErrorType\n  | CreateBatchSchedulerErrorType\n  | DuplicateIdErrorType\n  | HttpRequestErrorType\n  | InternalRpcErrorType\n  | InvalidInputRpcErrorType\n  | InvalidParamsRpcErrorType\n  | InvalidRequestRpcErrorType\n  | GetSocketRpcClientErrorType\n  | JsonRpcVersionUnsupportedErrorType\n  | LimitExceededRpcErrorType\n  | MethodNotFoundRpcErrorType\n  | MethodNotSupportedRpcErrorType\n  | ParseRpcErrorType\n  | ProviderDisconnectedErrorType\n  | ResourceNotFoundRpcErrorType\n  | ResourceUnavailableRpcErrorType\n  | RpcErrorType\n  | RpcRequestErrorType\n  | SwitchChainErrorType\n  | TimeoutErrorType\n  | TransactionRejectedRpcErrorType\n  | UnauthorizedProviderErrorType\n  | UnknownBundleIdErrorType\n  | UnknownRpcErrorType\n  | UnsupportedChainIdErrorType\n  | UnsupportedNonOptionalCapabilityErrorType\n  | UnsupportedProviderMethodErrorType\n  | UserRejectedRequestErrorType\n  | WebSocketRequestErrorType\n  | WithRetryErrorType\n  | ErrorType\n\nexport function buildRequest<request extends (args: any) => Promise<any>>(\n  request: request,\n  options: EIP1193RequestOptions = {},\n): EIP1193RequestFn {\n  return async (args, overrideOptions = {}) => {\n    const {\n      dedupe = false,\n      methods,\n      retryDelay = 150,\n      retryCount = 3,\n      uid,\n    } = {\n      ...options,\n      ...overrideOptions,\n    }\n\n    const { method } = args\n    if (methods?.exclude?.includes(method))\n      throw new MethodNotSupportedRpcError(new Error('method not supported'), {\n        method,\n      })\n    if (methods?.include && !methods.include.includes(method))\n      throw new MethodNotSupportedRpcError(new Error('method not supported'), {\n        method,\n      })\n\n    const requestId = dedupe\n      ? stringToHex(`${uid}.${stringify(args)}`)\n      : undefined\n    return withDedupe(\n      () =>\n        withRetry(\n          async () => {\n            try {\n              return await request(args)\n            } catch (err_) {\n              const err = err_ as unknown as RpcError<\n                RpcErrorCode | ProviderRpcErrorCode\n              >\n              switch (err.code) {\n                // -32700\n                case ParseRpcError.code:\n                  throw new ParseRpcError(err)\n                // -32600\n                case InvalidRequestRpcError.code:\n                  throw new InvalidRequestRpcError(err)\n                // -32601\n                case MethodNotFoundRpcError.code:\n                  throw new MethodNotFoundRpcError(err, { method: args.method })\n                // -32602\n                case InvalidParamsRpcError.code:\n                  throw new InvalidParamsRpcError(err)\n                // -32603\n                case InternalRpcError.code:\n                  throw new InternalRpcError(err)\n                // -32000\n                case InvalidInputRpcError.code:\n                  throw new InvalidInputRpcError(err)\n                // -32001\n                case ResourceNotFoundRpcError.code:\n                  throw new ResourceNotFoundRpcError(err)\n                // -32002\n                case ResourceUnavailableRpcError.code:\n                  throw new ResourceUnavailableRpcError(err)\n                // -32003\n                case TransactionRejectedRpcError.code:\n                  throw new TransactionRejectedRpcError(err)\n                // -32004\n                case MethodNotSupportedRpcError.code:\n                  throw new MethodNotSupportedRpcError(err, {\n                    method: args.method,\n                  })\n                // -32005\n                case LimitExceededRpcError.code:\n                  throw new LimitExceededRpcError(err)\n                // -32006\n                case JsonRpcVersionUnsupportedError.code:\n                  throw new JsonRpcVersionUnsupportedError(err)\n\n                // 4001\n                case UserRejectedRequestError.code:\n                  throw new UserRejectedRequestError(err)\n                // 4100\n                case UnauthorizedProviderError.code:\n                  throw new UnauthorizedProviderError(err)\n                // 4200\n                case UnsupportedProviderMethodError.code:\n                  throw new UnsupportedProviderMethodError(err)\n                // 4900\n                case ProviderDisconnectedError.code:\n                  throw new ProviderDisconnectedError(err)\n                // 4901\n                case ChainDisconnectedError.code:\n                  throw new ChainDisconnectedError(err)\n                // 4902\n                case SwitchChainError.code:\n                  throw new SwitchChainError(err)\n\n                // 5700\n                case UnsupportedNonOptionalCapabilityError.code:\n                  throw new UnsupportedNonOptionalCapabilityError(err)\n                // 5710\n                case UnsupportedChainIdError.code:\n                  throw new UnsupportedChainIdError(err)\n                // 5720\n                case DuplicateIdError.code:\n                  throw new DuplicateIdError(err)\n                // 5730\n                case UnknownBundleIdError.code:\n                  throw new UnknownBundleIdError(err)\n                // 5740\n                case BundleTooLargeError.code:\n                  throw new BundleTooLargeError(err)\n                // 5750\n                case AtomicReadyWalletRejectedUpgradeError.code:\n                  throw new AtomicReadyWalletRejectedUpgradeError(err)\n                // 5760\n                case AtomicityNotSupportedError.code:\n                  throw new AtomicityNotSupportedError(err)\n\n                // CAIP-25: User Rejected Error\n                // https://docs.walletconnect.com/2.0/specs/clients/sign/error-codes#rejected-caip-25\n                case 5000:\n                  throw new UserRejectedRequestError(err)\n\n                default:\n                  if (err_ instanceof BaseError) throw err_\n                  throw new UnknownRpcError(err as Error)\n              }\n            }\n          },\n          {\n            delay: ({ count, error }) => {\n              // If we find a Retry-After header, let's retry after the given time.\n              if (error && error instanceof HttpRequestError) {\n                const retryAfter = error?.headers?.get('Retry-After')\n                if (retryAfter?.match(/\\d/))\n                  return Number.parseInt(retryAfter) * 1000\n              }\n\n              // Otherwise, let's retry with an exponential backoff.\n              return ~~(1 << count) * retryDelay\n            },\n            retryCount,\n            shouldRetry: ({ error }) => shouldRetry(error),\n          },\n        ),\n      { enabled: dedupe, id: requestId },\n    )\n  }\n}\n\n/** @internal */\nexport function shouldRetry(error: Error) {\n  if ('code' in error && typeof error.code === 'number') {\n    if (error.code === -1) return true // Unknown error\n    if (error.code === LimitExceededRpcError.code) return true\n    if (error.code === InternalRpcError.code) return true\n    return false\n  }\n  if (error instanceof HttpRequestError && error.status) {\n    // Forbidden\n    if (error.status === 403) return true\n    // Request Timeout\n    if (error.status === 408) return true\n    // Request Entity Too Large\n    if (error.status === 413) return true\n    // Too Many Requests\n    if (error.status === 429) return true\n    // Internal Server Error\n    if (error.status === 500) return true\n    // Bad Gateway\n    if (error.status === 502) return true\n    // Service Unavailable\n    if (error.status === 503) return true\n    // Gateway Timeout\n    if (error.status === 504) return true\n    return false\n  }\n  return true\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { EIP1193RequestFn } from '../../types/eip1193.js'\nimport type { OneOf } from '../../types/utils.js'\nimport { buildRequest } from '../../utils/buildRequest.js'\nimport { uid as uid_ } from '../../utils/uid.js'\nimport type { ClientConfig } from '../createClient.js'\n\nexport type TransportConfig<\n  type extends string = string,\n  eip1193RequestFn extends EIP1193RequestFn = EIP1193RequestFn,\n> = {\n  /** The name of the transport. */\n  name: string\n  /** The key of the transport. */\n  key: string\n  /** Methods to include or exclude from executing RPC requests. */\n  methods?:\n    | OneOf<\n        | {\n            include?: string[] | undefined\n          }\n        | {\n            exclude?: string[] | undefined\n          }\n      >\n    | undefined\n  /** The JSON-RPC request function that matches the EIP-1193 request spec. */\n  request: eip1193RequestFn\n  /** The base delay (in ms) between retries. */\n  retryDelay?: number | undefined\n  /** The max number of times to retry. */\n  retryCount?: number | undefined\n  /** The timeout (in ms) for requests. */\n  timeout?: number | undefined\n  /** The type of the transport. */\n  type: type\n}\n\nexport type Transport<\n  type extends string = string,\n  rpcAttributes = Record<string, any>,\n  eip1193RequestFn extends EIP1193RequestFn = EIP1193RequestFn,\n> = <chain extends Chain | undefined = Chain>({\n  chain,\n}: {\n  chain?: chain | undefined\n  pollingInterval?: ClientConfig['pollingInterval'] | undefined\n  retryCount?: TransportConfig['retryCount'] | undefined\n  timeout?: TransportConfig['timeout'] | undefined\n}) => {\n  config: TransportConfig<type>\n  request: eip1193RequestFn\n  value?: rpcAttributes | undefined\n}\n\nexport type CreateTransportErrorType = ErrorType\n\n/**\n * @description Creates an transport intended to be used with a client.\n */\nexport function createTransport<\n  type extends string,\n  rpcAttributes extends Record<string, any>,\n>(\n  {\n    key,\n    methods,\n    name,\n    request,\n    retryCount = 3,\n    retryDelay = 150,\n    timeout,\n    type,\n  }: TransportConfig<type>,\n  value?: rpcAttributes | undefined,\n): ReturnType<Transport<type, rpcAttributes>> {\n  const uid = uid_()\n  return {\n    config: {\n      key,\n      methods,\n      name,\n      request,\n      retryCount,\n      retryDelay,\n      timeout,\n      type,\n    },\n    request: buildRequest(request, { methods, retryCount, retryDelay, uid }),\n    value,\n  }\n}\n", "import { ExecutionRevertedError } from '../../errors/node.js'\nimport {\n  TransactionRejectedRpcError,\n  UserRejectedRequestError,\n} from '../../errors/rpc.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Chain } from '../../types/chain.js'\nimport { wait } from '../../utils/wait.js'\n\nimport {\n  type CreateTransportErrorType,\n  type Transport,\n  type TransportConfig,\n  createTransport,\n} from './createTransport.js'\n// TODO: Narrow `method` & `params` types.\nexport type OnResponseFn = (\n  args: {\n    method: string\n    params: unknown[]\n    transport: ReturnType<Transport>\n  } & (\n    | {\n        error?: undefined\n        response: unknown\n        status: 'success'\n      }\n    | {\n        error: Error\n        response?: undefined\n        status: 'error'\n      }\n  ),\n) => void\n\ntype RankOptions = {\n  /**\n   * The polling interval (in ms) at which the ranker should ping the RPC URL.\n   * @default client.pollingInterval\n   */\n  interval?: number | undefined\n  /**\n   * Ping method to determine latency.\n   */\n  ping?: (parameters: { transport: ReturnType<Transport> }) =>\n    | Promise<unknown>\n    | undefined\n  /**\n   * The number of previous samples to perform ranking on.\n   * @default 10\n   */\n  sampleCount?: number | undefined\n  /**\n   * Timeout when sampling transports.\n   * @default 1_000\n   */\n  timeout?: number | undefined\n  /**\n   * Weights to apply to the scores. Weight values are proportional.\n   */\n  weights?:\n    | {\n        /**\n         * The weight to apply to the latency score.\n         * @default 0.3\n         */\n        latency?: number | undefined\n        /**\n         * The weight to apply to the stability score.\n         * @default 0.7\n         */\n        stability?: number | undefined\n      }\n    | undefined\n}\n\nexport type FallbackTransportConfig = {\n  /** The key of the Fallback transport. */\n  key?: TransportConfig['key'] | undefined\n  /** The name of the Fallback transport. */\n  name?: TransportConfig['name'] | undefined\n  /** Toggle to enable ranking, or rank options. */\n  rank?: boolean | RankOptions | undefined\n  /** The max number of times to retry. */\n  retryCount?: TransportConfig['retryCount'] | undefined\n  /** The base delay (in ms) between retries. */\n  retryDelay?: TransportConfig['retryDelay'] | undefined\n  /** Callback on whether an error should throw or try the next transport in the fallback. */\n  shouldThrow?: (error: Error) => boolean | undefined\n}\n\nexport type FallbackTransport<\n  transports extends readonly Transport[] = readonly Transport[],\n> = Transport<\n  'fallback',\n  {\n    onResponse: (fn: OnResponseFn) => void\n    transports: {\n      [key in keyof transports]: ReturnType<transports[key]>\n    }\n  }\n>\n\nexport type FallbackTransportErrorType = CreateTransportErrorType | ErrorType\n\nexport function fallback<const transports extends readonly Transport[]>(\n  transports_: transports,\n  config: FallbackTransportConfig = {},\n): FallbackTransport<transports> {\n  const {\n    key = 'fallback',\n    name = 'Fallback',\n    rank = false,\n    shouldThrow: shouldThrow_ = shouldThrow,\n    retryCount,\n    retryDelay,\n  } = config\n  return (({ chain, pollingInterval = 4_000, timeout, ...rest }) => {\n    let transports = transports_\n\n    let onResponse: OnResponseFn = () => {}\n\n    const transport = createTransport(\n      {\n        key,\n        name,\n        async request({ method, params }) {\n          let includes: boolean | undefined\n\n          const fetch = async (i = 0): Promise<any> => {\n            const transport = transports[i]({\n              ...rest,\n              chain,\n              retryCount: 0,\n              timeout,\n            })\n            try {\n              const response = await transport.request({\n                method,\n                params,\n              } as any)\n\n              onResponse({\n                method,\n                params: params as unknown[],\n                response,\n                transport,\n                status: 'success',\n              })\n\n              return response\n            } catch (err) {\n              onResponse({\n                error: err as Error,\n                method,\n                params: params as unknown[],\n                transport,\n                status: 'error',\n              })\n\n              if (shouldThrow_(err as Error)) throw err\n\n              // If we've reached the end of the fallbacks, throw the error.\n              if (i === transports.length - 1) throw err\n\n              // Check if at least one other transport includes the method\n              includes ??= transports.slice(i + 1).some((transport) => {\n                const { include, exclude } =\n                  transport({ chain }).config.methods || {}\n                if (include) return include.includes(method)\n                if (exclude) return !exclude.includes(method)\n                return true\n              })\n              if (!includes) throw err\n\n              // Otherwise, try the next fallback.\n              return fetch(i + 1)\n            }\n          }\n          return fetch()\n        },\n        retryCount,\n        retryDelay,\n        type: 'fallback',\n      },\n      {\n        onResponse: (fn: OnResponseFn) => (onResponse = fn),\n        transports: transports.map((fn) => fn({ chain, retryCount: 0 })),\n      },\n    )\n\n    if (rank) {\n      const rankOptions = (typeof rank === 'object' ? rank : {}) as RankOptions\n      rankTransports({\n        chain,\n        interval: rankOptions.interval ?? pollingInterval,\n        onTransports: (transports_) => (transports = transports_ as transports),\n        ping: rankOptions.ping,\n        sampleCount: rankOptions.sampleCount,\n        timeout: rankOptions.timeout,\n        transports,\n        weights: rankOptions.weights,\n      })\n    }\n    return transport\n  }) as FallbackTransport<transports>\n}\n\nexport function shouldThrow(error: Error) {\n  if ('code' in error && typeof error.code === 'number') {\n    if (\n      error.code === TransactionRejectedRpcError.code ||\n      error.code === UserRejectedRequestError.code ||\n      ExecutionRevertedError.nodeMessage.test(error.message) ||\n      error.code === 5000 // CAIP UserRejectedRequestError\n    )\n      return true\n  }\n  return false\n}\n\n/** @internal */\nexport function rankTransports({\n  chain,\n  interval = 4_000,\n  onTransports,\n  ping,\n  sampleCount = 10,\n  timeout = 1_000,\n  transports,\n  weights = {},\n}: {\n  chain?: Chain | undefined\n  interval: RankOptions['interval']\n  onTransports: (transports: readonly Transport[]) => void\n  ping?: RankOptions['ping'] | undefined\n  sampleCount?: RankOptions['sampleCount'] | undefined\n  timeout?: RankOptions['timeout'] | undefined\n  transports: readonly Transport[]\n  weights?: RankOptions['weights'] | undefined\n}) {\n  const { stability: stabilityWeight = 0.7, latency: latencyWeight = 0.3 } =\n    weights\n\n  type SampleData = { latency: number; success: number }\n  type Sample = SampleData[]\n  const samples: Sample[] = []\n\n  const rankTransports_ = async () => {\n    // 1. Take a sample from each Transport.\n    const sample: Sample = await Promise.all(\n      transports.map(async (transport) => {\n        const transport_ = transport({ chain, retryCount: 0, timeout })\n\n        const start = Date.now()\n        let end: number\n        let success: number\n        try {\n          await (ping\n            ? ping({ transport: transport_ })\n            : transport_.request({ method: 'net_listening' }))\n          success = 1\n        } catch {\n          success = 0\n        } finally {\n          end = Date.now()\n        }\n        const latency = end - start\n        return { latency, success }\n      }),\n    )\n\n    // 2. Store the sample. If we have more than `sampleCount` samples, remove\n    // the oldest sample.\n    samples.push(sample)\n    if (samples.length > sampleCount) samples.shift()\n\n    // 3. Calculate the max latency from samples.\n    const maxLatency = Math.max(\n      ...samples.map((sample) =>\n        Math.max(...sample.map(({ latency }) => latency)),\n      ),\n    )\n\n    // 4. Calculate the score for each Transport.\n    const scores = transports\n      .map((_, i) => {\n        const latencies = samples.map((sample) => sample[i].latency)\n        const meanLatency =\n          latencies.reduce((acc, latency) => acc + latency, 0) /\n          latencies.length\n        const latencyScore = 1 - meanLatency / maxLatency\n\n        const successes = samples.map((sample) => sample[i].success)\n        const stabilityScore =\n          successes.reduce((acc, success) => acc + success, 0) /\n          successes.length\n\n        if (stabilityScore === 0) return [0, i]\n        return [\n          latencyWeight * latencyScore + stabilityWeight * stabilityScore,\n          i,\n        ]\n      })\n      .sort((a, b) => b[0] - a[0])\n\n    // 5. Sort the Transports by score.\n    onTransports(scores.map(([, i]) => transports[i]))\n\n    // 6. Wait, and then rank again.\n    await wait(interval)\n    rankTransports_()\n  }\n  rankTransports_()\n}\n", "import { BaseError } from './base.js'\n\nexport type UrlRequiredErrorType = UrlRequiredError & {\n  name: 'UrlRequiredError'\n}\nexport class UrlRequiredError extends BaseError {\n  constructor() {\n    super(\n      'No URL was provided to the Transport. Please provide a valid RPC URL to the Transport.',\n      {\n        docsPath: '/docs/clients/intro',\n        name: 'UrlRequiredError',\n      },\n    )\n  }\n}\n", "import type { ErrorType } from '../../errors/utils.js'\n\nexport type WithTimeoutErrorType = ErrorType\n\nexport function withTimeout<data>(\n  fn: ({\n    signal,\n  }: { signal: AbortController['signal'] | null }) => Promise<data>,\n  {\n    errorInstance = new Error('timed out'),\n    timeout,\n    signal,\n  }: {\n    // The error instance to throw when the timeout is reached.\n    errorInstance?: Error | undefined\n    // The timeout (in ms).\n    timeout: number\n    // Whether or not the timeout should use an abort signal.\n    signal?: boolean | undefined\n  },\n): Promise<data> {\n  return new Promise((resolve, reject) => {\n    ;(async () => {\n      let timeoutId!: NodeJS.Timeout\n      try {\n        const controller = new AbortController()\n        if (timeout > 0) {\n          timeoutId = setTimeout(() => {\n            if (signal) {\n              controller.abort()\n            } else {\n              reject(errorInstance)\n            }\n          }, timeout) as NodeJS.Timeout // need to cast because bun globals.d.ts overrides @types/node\n        }\n        resolve(await fn({ signal: controller?.signal || null }))\n      } catch (err) {\n        if ((err as Error)?.name === 'AbortError') reject(errorInstance)\n        reject(err)\n      } finally {\n        clearTimeout(timeoutId)\n      }\n    })()\n  })\n}\n", "function createIdStore() {\n  return {\n    current: 0,\n    take() {\n      return this.current++\n    },\n    reset() {\n      this.current = 0\n    },\n  }\n}\n\nexport const idCache = /*#__PURE__*/ createIdStore()\n", "import {\n  HttpRequestError,\n  type HttpRequestErrorType as HttpRequestErrorType_,\n  TimeoutError,\n  type TimeoutErrorType,\n} from '../../errors/request.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { RpcRequest, RpcResponse } from '../../types/rpc.js'\nimport type { MaybePromise } from '../../types/utils.js'\nimport {\n  type WithTimeoutErrorType,\n  withTimeout,\n} from '../promise/withTimeout.js'\nimport { stringify } from '../stringify.js'\nimport { idCache } from './id.js'\n\nexport type HttpRpcClientOptions = {\n  /** Request configuration to pass to `fetch`. */\n  fetchOptions?: Omit<RequestInit, 'body'> | undefined\n  /** A callback to handle the request. */\n  onRequest?:\n    | ((\n        request: Request,\n        init: RequestInit,\n      ) => MaybePromise<\n        void | undefined | (RequestInit & { url?: string | undefined })\n      >)\n    | undefined\n  /** A callback to handle the response. */\n  onResponse?: ((response: Response) => Promise<void> | void) | undefined\n  /** The timeout (in ms) for the request. */\n  timeout?: number | undefined\n}\n\nexport type HttpRequestParameters<\n  body extends RpcRequest | RpcRequest[] = RpcRequest,\n> = {\n  /** The RPC request body. */\n  body: body\n  /** Request configuration to pass to `fetch`. */\n  fetchOptions?: HttpRpcClientOptions['fetchOptions'] | undefined\n  /** A callback to handle the response. */\n  onRequest?:\n    | ((\n        request: Request,\n        init: RequestInit,\n      ) => MaybePromise<\n        void | undefined | (RequestInit & { url?: string | undefined })\n      >)\n    | undefined\n  /** A callback to handle the response. */\n  onResponse?: ((response: Response) => Promise<void> | void) | undefined\n  /** The timeout (in ms) for the request. */\n  timeout?: HttpRpcClientOptions['timeout'] | undefined\n}\n\nexport type HttpRequestReturnType<\n  body extends RpcRequest | RpcRequest[] = RpcRequest,\n> = body extends RpcRequest[] ? RpcResponse[] : RpcResponse\n\nexport type HttpRequestErrorType =\n  | HttpRequestErrorType_\n  | TimeoutErrorType\n  | WithTimeoutErrorType\n  | ErrorType\n\nexport type HttpRpcClient = {\n  request<body extends RpcRequest | RpcRequest[]>(\n    params: HttpRequestParameters<body>,\n  ): Promise<HttpRequestReturnType<body>>\n}\n\nexport function getHttpRpcClient(\n  url: string,\n  options: HttpRpcClientOptions = {},\n): HttpRpcClient {\n  return {\n    async request(params) {\n      const {\n        body,\n        onRequest = options.onRequest,\n        onResponse = options.onResponse,\n        timeout = options.timeout ?? 10_000,\n      } = params\n\n      const fetchOptions = {\n        ...(options.fetchOptions ?? {}),\n        ...(params.fetchOptions ?? {}),\n      }\n\n      const { headers, method, signal: signal_ } = fetchOptions\n\n      try {\n        const response = await withTimeout(\n          async ({ signal }) => {\n            const init: RequestInit = {\n              ...fetchOptions,\n              body: Array.isArray(body)\n                ? stringify(\n                    body.map((body) => ({\n                      jsonrpc: '2.0',\n                      id: body.id ?? idCache.take(),\n                      ...body,\n                    })),\n                  )\n                : stringify({\n                    jsonrpc: '2.0',\n                    id: body.id ?? idCache.take(),\n                    ...body,\n                  }),\n              headers: {\n                'Content-Type': 'application/json',\n                ...headers,\n              },\n              method: method || 'POST',\n              signal: signal_ || (timeout > 0 ? signal : null),\n            }\n            const request = new Request(url, init)\n            const args = (await onRequest?.(request, init)) ?? { ...init, url }\n            const response = await fetch(args.url ?? url, args)\n            return response\n          },\n          {\n            errorInstance: new TimeoutError({ body, url }),\n            timeout,\n            signal: true,\n          },\n        )\n\n        if (onResponse) await onResponse(response)\n\n        let data: any\n        if (\n          response.headers.get('Content-Type')?.startsWith('application/json')\n        )\n          data = await response.json()\n        else {\n          data = await response.text()\n          try {\n            data = JSON.parse(data || '{}')\n          } catch (err) {\n            if (response.ok) throw err\n            data = { error: data }\n          }\n        }\n\n        if (!response.ok) {\n          throw new HttpRequestError({\n            body,\n            details: stringify(data.error) || response.statusText,\n            headers: response.headers,\n            status: response.status,\n            url,\n          })\n        }\n\n        return data\n      } catch (err) {\n        if (err instanceof HttpRequestError) throw err\n        if (err instanceof TimeoutError) throw err\n        throw new HttpRequestError({\n          body,\n          cause: err as Error,\n          url,\n        })\n      }\n    },\n  }\n}\n", "import { RpcRequestError } from '../../errors/request.js'\nimport {\n  UrlRequiredError,\n  type UrlRequiredErrorType,\n} from '../../errors/transport.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { EIP1193RequestFn, RpcSchema } from '../../types/eip1193.js'\nimport type { RpcRequest } from '../../types/rpc.js'\nimport { createBatchScheduler } from '../../utils/promise/createBatchScheduler.js'\nimport {\n  type HttpRpcClientOptions,\n  getHttpRpcClient,\n} from '../../utils/rpc/http.js'\n\nimport {\n  type CreateTransportErrorType,\n  type Transport,\n  type TransportConfig,\n  createTransport,\n} from './createTransport.js'\n\nexport type HttpTransportConfig<\n  rpcSchema extends RpcSchema | undefined = undefined,\n  raw extends boolean = false,\n> = {\n  /**\n   * Whether to enable Batch JSON-RPC.\n   * @link https://www.jsonrpc.org/specification#batch\n   */\n  batch?:\n    | boolean\n    | {\n        /** The maximum number of JSON-RPC requests to send in a batch. @default 1_000 */\n        batchSize?: number | undefined\n        /** The maximum number of milliseconds to wait before sending a batch. @default 0 */\n        wait?: number | undefined\n      }\n    | undefined\n  /**\n   * Request configuration to pass to `fetch`.\n   * @link https://developer.mozilla.org/en-US/docs/Web/API/fetch\n   */\n  fetchOptions?: HttpRpcClientOptions['fetchOptions'] | undefined\n  /** A callback to handle the response from `fetch`. */\n  onFetchRequest?: HttpRpcClientOptions['onRequest'] | undefined\n  /** A callback to handle the response from `fetch`. */\n  onFetchResponse?: HttpRpcClientOptions['onResponse'] | undefined\n  /** The key of the HTTP transport. */\n  key?: TransportConfig['key'] | undefined\n  /** Methods to include or exclude from executing RPC requests. */\n  methods?: TransportConfig['methods'] | undefined\n  /** The name of the HTTP transport. */\n  name?: TransportConfig['name'] | undefined\n  /** Whether to return JSON RPC errors as responses instead of throwing. */\n  raw?: raw | boolean | undefined\n  /** The max number of times to retry. */\n  retryCount?: TransportConfig['retryCount'] | undefined\n  /** The base delay (in ms) between retries. */\n  retryDelay?: TransportConfig['retryDelay'] | undefined\n  /** Typed JSON-RPC schema for the transport. */\n  rpcSchema?: rpcSchema | RpcSchema | undefined\n  /** The timeout (in ms) for the HTTP request. Default: 10_000 */\n  timeout?: TransportConfig['timeout'] | undefined\n}\n\nexport type HttpTransport<\n  rpcSchema extends RpcSchema | undefined = undefined,\n  raw extends boolean = false,\n> = Transport<\n  'http',\n  {\n    fetchOptions?: HttpTransportConfig['fetchOptions'] | undefined\n    url?: string | undefined\n  },\n  EIP1193RequestFn<rpcSchema, raw>\n>\n\nexport type HttpTransportErrorType =\n  | CreateTransportErrorType\n  | UrlRequiredErrorType\n  | ErrorType\n\n/**\n * @description Creates a HTTP transport that connects to a JSON-RPC API.\n */\nexport function http<\n  rpcSchema extends RpcSchema | undefined = undefined,\n  raw extends boolean = false,\n>(\n  /** URL of the JSON-RPC API. Defaults to the chain's public RPC URL. */\n  url?: string | undefined,\n  config: HttpTransportConfig<rpcSchema, raw> = {},\n): HttpTransport<rpcSchema, raw> {\n  const {\n    batch,\n    fetchOptions,\n    key = 'http',\n    methods,\n    name = 'HTTP JSON-RPC',\n    onFetchRequest,\n    onFetchResponse,\n    retryDelay,\n    raw,\n  } = config\n  return ({ chain, retryCount: retryCount_, timeout: timeout_ }) => {\n    const { batchSize = 1000, wait = 0 } =\n      typeof batch === 'object' ? batch : {}\n    const retryCount = config.retryCount ?? retryCount_\n    const timeout = timeout_ ?? config.timeout ?? 10_000\n    const url_ = url || chain?.rpcUrls.default.http[0]\n    if (!url_) throw new UrlRequiredError()\n\n    const rpcClient = getHttpRpcClient(url_, {\n      fetchOptions,\n      onRequest: onFetchRequest,\n      onResponse: onFetchResponse,\n      timeout,\n    })\n\n    return createTransport(\n      {\n        key,\n        methods,\n        name,\n        async request({ method, params }) {\n          const body = { method, params }\n\n          const { schedule } = createBatchScheduler({\n            id: url_,\n            wait,\n            shouldSplitBatch(requests) {\n              return requests.length > batchSize\n            },\n            fn: (body: RpcRequest[]) =>\n              rpcClient.request({\n                body,\n              }),\n            sort: (a, b) => a.id - b.id,\n          })\n\n          const fn = async (body: RpcRequest) =>\n            batch\n              ? schedule(body)\n              : [\n                  await rpcClient.request({\n                    body,\n                  }),\n                ]\n\n          const [{ error, result }] = await fn(body)\n\n          if (raw) return { error, result }\n          if (error)\n            throw new RpcRequestError({\n              body,\n              error,\n              url: url_,\n            })\n          return result\n        },\n        retryCount,\n        retryDelay,\n        timeout,\n        type: 'http',\n      },\n      {\n        fetchOptions,\n        url: url_,\n      },\n    )\n  }\n}\n", "/**\n * RIPEMD-160 legacy hash function.\n * https://homes.esat.kuleuven.be/~bosselae/ripemd160.html\n * https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf\n * @module\n */\nimport { HashMD } from './_md.ts';\nimport { rotl, wrapConstructor, type CHash } from './utils.ts';\n\nconst Rho = /* @__PURE__ */ new Uint8Array([7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8]);\nconst Id = /* @__PURE__ */ new Uint8Array(new Array(16).fill(0).map((_, i) => i));\nconst Pi = /* @__PURE__ */ Id.map((i) => (9 * i + 5) % 16);\nlet idxL = [Id];\nlet idxR = [Pi];\nfor (let i = 0; i < 4; i++) for (let j of [idxL, idxR]) j.push(j[i].map((k) => Rho[k]));\n\nconst shifts = /* @__PURE__ */ [\n  [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8],\n  [12, 13, 11, 15, 6, 9, 9, 7, 12, 15, 11, 13, 7, 8, 7, 7],\n  [13, 15, 14, 11, 7, 7, 6, 8, 13, 14, 13, 12, 5, 5, 6, 9],\n  [14, 11, 12, 14, 8, 6, 5, 5, 15, 12, 15, 14, 9, 9, 8, 6],\n  [15, 12, 13, 13, 9, 5, 8, 6, 14, 11, 12, 11, 8, 6, 5, 5],\n].map((i) => new Uint8Array(i));\nconst shiftsL = /* @__PURE__ */ idxL.map((idx, i) => idx.map((j) => shifts[i][j]));\nconst shiftsR = /* @__PURE__ */ idxR.map((idx, i) => idx.map((j) => shifts[i][j]));\nconst Kl = /* @__PURE__ */ new Uint32Array([\n  0x00000000, 0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xa953fd4e,\n]);\nconst Kr = /* @__PURE__ */ new Uint32Array([\n  0x50a28be6, 0x5c4dd124, 0x6d703ef3, 0x7a6d76e9, 0x00000000,\n]);\n// It's called f() in spec.\nfunction f(group: number, x: number, y: number, z: number): number {\n  if (group === 0) return x ^ y ^ z;\n  else if (group === 1) return (x & y) | (~x & z);\n  else if (group === 2) return (x | ~y) ^ z;\n  else if (group === 3) return (x & z) | (y & ~z);\n  else return x ^ (y | ~z);\n}\n// Temporary buffer, not used to store anything between runs\nconst R_BUF = /* @__PURE__ */ new Uint32Array(16);\nexport class RIPEMD160 extends HashMD<RIPEMD160> {\n  private h0 = 0x67452301 | 0;\n  private h1 = 0xefcdab89 | 0;\n  private h2 = 0x98badcfe | 0;\n  private h3 = 0x10325476 | 0;\n  private h4 = 0xc3d2e1f0 | 0;\n\n  constructor() {\n    super(64, 20, 8, true);\n  }\n  protected get(): [number, number, number, number, number] {\n    const { h0, h1, h2, h3, h4 } = this;\n    return [h0, h1, h2, h3, h4];\n  }\n  protected set(h0: number, h1: number, h2: number, h3: number, h4: number): void {\n    this.h0 = h0 | 0;\n    this.h1 = h1 | 0;\n    this.h2 = h2 | 0;\n    this.h3 = h3 | 0;\n    this.h4 = h4 | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    for (let i = 0; i < 16; i++, offset += 4) R_BUF[i] = view.getUint32(offset, true);\n    // prettier-ignore\n    let al = this.h0 | 0, ar = al,\n        bl = this.h1 | 0, br = bl,\n        cl = this.h2 | 0, cr = cl,\n        dl = this.h3 | 0, dr = dl,\n        el = this.h4 | 0, er = el;\n\n    // Instead of iterating 0 to 80, we split it into 5 groups\n    // And use the groups in constants, functions, etc. Much simpler\n    for (let group = 0; group < 5; group++) {\n      const rGroup = 4 - group;\n      const hbl = Kl[group], hbr = Kr[group]; // prettier-ignore\n      const rl = idxL[group], rr = idxR[group]; // prettier-ignore\n      const sl = shiftsL[group], sr = shiftsR[group]; // prettier-ignore\n      for (let i = 0; i < 16; i++) {\n        const tl = (rotl(al + f(group, bl, cl, dl) + R_BUF[rl[i]] + hbl, sl[i]) + el) | 0;\n        al = el, el = dl, dl = rotl(cl, 10) | 0, cl = bl, bl = tl; // prettier-ignore\n      }\n      // 2 loops are 10% faster\n      for (let i = 0; i < 16; i++) {\n        const tr = (rotl(ar + f(rGroup, br, cr, dr) + R_BUF[rr[i]] + hbr, sr[i]) + er) | 0;\n        ar = er, er = dr, dr = rotl(cr, 10) | 0, cr = br, br = tr; // prettier-ignore\n      }\n    }\n    // Add the compressed chunk to the current hash value\n    this.set(\n      (this.h1 + cl + dr) | 0,\n      (this.h2 + dl + er) | 0,\n      (this.h3 + el + ar) | 0,\n      (this.h4 + al + br) | 0,\n      (this.h0 + bl + cr) | 0\n    );\n  }\n  protected roundClean(): void {\n    R_BUF.fill(0);\n  }\n  destroy(): void {\n    this.destroyed = true;\n    this.buffer.fill(0);\n    this.set(0, 0, 0, 0, 0);\n  }\n}\n\n/** RIPEMD-160 - a legacy hash function from 1990s. */\nexport const ripemd160: CHash = /* @__PURE__ */ wrapConstructor(() => new RIPEMD160());\n", "import type { Address } from 'abitype'\n\nimport { getTransactionCount } from '../actions/public/getTransactionCount.js'\nimport type { Client } from '../clients/createClient.js'\nimport type { MaybePromise } from '../types/utils.js'\nimport { LruMap } from './lru.js'\n\nexport type CreateNonceManagerParameters = {\n  source: NonceManagerSource\n}\n\ntype FunctionParameters = {\n  address: Address\n  chainId: number\n}\n\nexport type NonceManager = {\n  /** Get and increment a nonce. */\n  consume: (\n    parameters: FunctionParameters & { client: Client },\n  ) => Promise<number>\n  /** Increment a nonce. */\n  increment: (chainId: FunctionParameters) => void\n  /** Get a nonce. */\n  get: (chainId: FunctionParameters & { client: Client }) => Promise<number>\n  /** Reset a nonce. */\n  reset: (chainId: FunctionParameters) => void\n}\n\n/**\n * Creates a nonce manager for auto-incrementing transaction nonces.\n *\n * - Docs: https://viem.sh/docs/accounts/createNonceManager\n *\n * @example\n * ```ts\n * const nonceManager = createNonceManager({\n *   source: jsonRpc(),\n * })\n * ```\n */\nexport function createNonceManager(\n  parameters: CreateNonceManagerParameters,\n): NonceManager {\n  const { source } = parameters\n\n  const deltaMap = new Map()\n  const nonceMap = new LruMap<number>(8192)\n  const promiseMap = new Map<string, Promise<number>>()\n\n  const getKey = ({ address, chainId }: FunctionParameters) =>\n    `${address}.${chainId}`\n\n  return {\n    async consume({ address, chainId, client }) {\n      const key = getKey({ address, chainId })\n      const promise = this.get({ address, chainId, client })\n\n      this.increment({ address, chainId })\n      const nonce = await promise\n\n      await source.set({ address, chainId }, nonce)\n      nonceMap.set(key, nonce)\n\n      return nonce\n    },\n    async increment({ address, chainId }) {\n      const key = getKey({ address, chainId })\n      const delta = deltaMap.get(key) ?? 0\n      deltaMap.set(key, delta + 1)\n    },\n    async get({ address, chainId, client }) {\n      const key = getKey({ address, chainId })\n\n      let promise = promiseMap.get(key)\n      if (!promise) {\n        promise = (async () => {\n          try {\n            const nonce = await source.get({ address, chainId, client })\n            const previousNonce = nonceMap.get(key) ?? 0\n            if (previousNonce > 0 && nonce <= previousNonce)\n              return previousNonce + 1\n            nonceMap.delete(key)\n            return nonce\n          } finally {\n            this.reset({ address, chainId })\n          }\n        })()\n        promiseMap.set(key, promise)\n      }\n\n      const delta = deltaMap.get(key) ?? 0\n      return delta + (await promise)\n    },\n    reset({ address, chainId }) {\n      const key = getKey({ address, chainId })\n      deltaMap.delete(key)\n      promiseMap.delete(key)\n    },\n  }\n}\n\n////////////////////////////////////////////////////////////////////////////////////////////\n// Sources\n\nexport type NonceManagerSource = {\n  /** Get a nonce. */\n  get(parameters: FunctionParameters & { client: Client }): MaybePromise<number>\n  /** Set a nonce. */\n  set(parameters: FunctionParameters, nonce: number): MaybePromise<void>\n}\n\n/** JSON-RPC source for a nonce manager. */\nexport function jsonRpc(): NonceManagerSource {\n  return {\n    async get(parameters) {\n      const { address, client } = parameters\n      return getTransactionCount(client, {\n        address,\n        blockTag: 'pending',\n      })\n    },\n    set() {},\n  }\n}\n\n////////////////////////////////////////////////////////////////////////////////////////////\n// Default\n\n/** Default Nonce Manager with a JSON-RPC source. */\nexport const nonceManager = /*#__PURE__*/ createNonceManager({\n  source: jsonRpc(),\n})\n", "/**\n * Internal assertion helpers.\n * @module\n */\n\n/** Asserts something is positive integer. */\nfunction anumber(n: number): void {\n  if (!Number.isSafeInteger(n) || n < 0) throw new Error('positive integer expected, got ' + n);\n}\n\n/** Is number an Uint8Array? Copied from utils for perf. */\nfunction isBytes(a: unknown): a is Uint8Array {\n  return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n\n/** Asserts something is Uint8Array. */\nfunction abytes(b: Uint8Array | undefined, ...lengths: number[]): void {\n  if (!isBytes(b)) throw new Error('Uint8Array expected');\n  if (lengths.length > 0 && !lengths.includes(b.length))\n    throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n\n/** Hash interface. */\nexport type Hash = {\n  (data: Uint8Array): Uint8Array;\n  blockLen: number;\n  outputLen: number;\n  create: any;\n};\n\n/** Asserts something is hash */\nfunction ahash(h: Hash): void {\n  if (typeof h !== 'function' || typeof h.create !== 'function')\n    throw new Error('Hash should be wrapped by utils.wrapConstructor');\n  anumber(h.outputLen);\n  anumber(h.blockLen);\n}\n\n/** Asserts a hash instance has not been destroyed / finished */\nfunction aexists(instance: any, checkFinished = true): void {\n  if (instance.destroyed) throw new Error('Hash instance has been destroyed');\n  if (checkFinished && instance.finished) throw new Error('Hash#digest() has already been called');\n}\n\n/** Asserts output is properly-sized byte array */\nfunction aoutput(out: any, instance: any): void {\n  abytes(out);\n  const min = instance.outputLen;\n  if (out.length < min) {\n    throw new Error('digestInto() expects output buffer of length at least ' + min);\n  }\n}\n\nexport { anumber, abytes, ahash, aexists, aoutput };\n", "/**\n * Internal webcrypto alias.\n * We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n * See utils.ts for details.\n * @module\n */\ndeclare const globalThis: Record<string, any> | undefined;\nexport const crypto: any =\n  typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;\n", "/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\nimport { abytes } from './_assert.js';\n// export { isBytes } from './_assert.js';\n// We can't reuse isBytes from _assert, because somehow this causes huge perf issues\nexport function isBytes(a: unknown): a is Uint8Array {\n  return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n\n// prettier-ignore\nexport type TypedArray = Int8Array | Uint8ClampedArray | Uint8Array |\n  Uint16Array | Int16Array | Uint32Array | Int32Array;\n\n// Cast array to different type\nexport function u8(arr: TypedArray): Uint8Array {\n  return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\nexport function u32(arr: TypedArray): Uint32Array {\n  return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n\n// Cast array to view\nexport function createView(arr: TypedArray): DataView {\n  return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n\n/** The rotate right (circular right shift) operation for uint32 */\nexport function rotr(word: number, shift: number): number {\n  return (word << (32 - shift)) | (word >>> shift);\n}\n/** The rotate left (circular left shift) operation for uint32 */\nexport function rotl(word: number, shift: number): number {\n  return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nexport const isLE: boolean = /* @__PURE__ */ (() =>\n  new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n// The byte swap operation for uint32\nexport function byteSwap(word: number): number {\n  return (\n    ((word << 24) & 0xff000000) |\n    ((word << 8) & 0xff0000) |\n    ((word >>> 8) & 0xff00) |\n    ((word >>> 24) & 0xff)\n  );\n}\n/** Conditionally byte swap if on a big-endian platform */\nexport const byteSwapIfBE: (n: number) => number = isLE\n  ? (n: number) => n\n  : (n: number) => byteSwap(n);\n\n/** In place byte swap for Uint32Array */\nexport function byteSwap32(arr: Uint32Array): void {\n  for (let i = 0; i < arr.length; i++) {\n    arr[i] = byteSwap(arr[i]);\n  }\n}\n\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) =>\n  i.toString(16).padStart(2, '0')\n);\n/**\n * Convert byte array to hex string.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes: Uint8Array): string {\n  abytes(bytes);\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 } as const;\nfunction asciiToBase16(ch: number): number | undefined {\n  if (ch >= asciis._0 && ch <= asciis._9) return ch - asciis._0; // '2' => 50-48\n  if (ch >= asciis.A && ch <= asciis.F) return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n  if (ch >= asciis.a && ch <= asciis.f) return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n  return;\n}\n\n/**\n * Convert hex string to byte array.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex: string): Uint8Array {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  const hl = hex.length;\n  const al = hl / 2;\n  if (hl % 2) throw new Error('hex string expected, got unpadded hex of length ' + hl);\n  const array = new Uint8Array(al);\n  for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n    const n1 = asciiToBase16(hex.charCodeAt(hi));\n    const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n    if (n1 === undefined || n2 === undefined) {\n      const char = hex[hi] + hex[hi + 1];\n      throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n    }\n    array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n  }\n  return array;\n}\n\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nexport const nextTick = async (): Promise<void> => {};\n\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nexport async function asyncLoop(\n  iters: number,\n  tick: number,\n  cb: (i: number) => void\n): Promise<void> {\n  let ts = Date.now();\n  for (let i = 0; i < iters; i++) {\n    cb(i);\n    // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n    const diff = Date.now() - ts;\n    if (diff >= 0 && diff < tick) continue;\n    await nextTick();\n    ts += diff;\n  }\n}\n\n// Global symbols in both browsers and Node.js since v11\n// See https://github.com/microsoft/TypeScript/issues/31535\ndeclare const TextEncoder: any;\n\n/**\n * Convert JS string to byte array.\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str: string): Uint8Array {\n  if (typeof str !== 'string') throw new Error('utf8ToBytes expected string, got ' + typeof str);\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n\n/** Accepted input of hash functions. Strings are converted to byte arrays. */\nexport type Input = Uint8Array | string;\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data: Input): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays: Uint8Array[]): Uint8Array {\n  let sum = 0;\n  for (let i = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    abytes(a);\n    sum += a.length;\n  }\n  const res = new Uint8Array(sum);\n  for (let i = 0, pad = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    res.set(a, pad);\n    pad += a.length;\n  }\n  return res;\n}\n\n/** For runtime check if class implements interface */\nexport abstract class Hash<T extends Hash<T>> {\n  abstract blockLen: number; // Bytes per block\n  abstract outputLen: number; // Bytes in output\n  abstract update(buf: Input): this;\n  // Writes digest into buf\n  abstract digestInto(buf: Uint8Array): void;\n  abstract digest(): Uint8Array;\n  /**\n   * Resets internal state. Makes Hash instance unusable.\n   * Reset is impossible for keyed hashes if key is consumed into state. If digest is not consumed\n   * by user, they will need to manually call `destroy()` when zeroing is necessary.\n   */\n  abstract destroy(): void;\n  /**\n   * Clones hash instance. Unsafe: doesn't check whether `to` is valid. Can be used as `clone()`\n   * when no options are passed.\n   * Reasons to use `_cloneInto` instead of clone: 1) performance 2) reuse instance => all internal\n   * buffers are overwritten => causes buffer overwrite which is used for digest in some cases.\n   * There are no guarantees for clean-up because it's impossible in JS.\n   */\n  abstract _cloneInto(to?: T): T;\n  // Safe version that clones internal state\n  clone(): T {\n    return this._cloneInto();\n  }\n}\n\n/**\n * XOF: streaming API to read digest in chunks.\n * Same as 'squeeze' in keccak/k12 and 'seek' in blake3, but more generic name.\n * When hash used in XOF mode it is up to user to call '.destroy' afterwards, since we cannot\n * destroy state, next call can require more bytes.\n */\nexport type HashXOF<T extends Hash<T>> = Hash<T> & {\n  xof(bytes: number): Uint8Array; // Read 'bytes' bytes from digest stream\n  xofInto(buf: Uint8Array): Uint8Array; // read buf.length bytes from digest stream into buf\n};\n\ntype EmptyObj = {};\nexport function checkOpts<T1 extends EmptyObj, T2 extends EmptyObj>(\n  defaults: T1,\n  opts?: T2\n): T1 & T2 {\n  if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n    throw new Error('Options should be object or undefined');\n  const merged = Object.assign(defaults, opts);\n  return merged as T1 & T2;\n}\n\n/** Hash function */\nexport type CHash = ReturnType<typeof wrapConstructor>;\n/** Hash function with output */\nexport type CHashO = ReturnType<typeof wrapConstructorWithOpts>;\n/** XOF with output */\nexport type CHashXO = ReturnType<typeof wrapXOFConstructorWithOpts>;\n\n/** Wraps hash function, creating an interface on top of it */\nexport function wrapConstructor<T extends Hash<T>>(\n  hashCons: () => Hash<T>\n): {\n  (msg: Input): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(): Hash<T>;\n} {\n  const hashC = (msg: Input): Uint8Array => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\n\nexport function wrapConstructorWithOpts<H extends Hash<H>, T extends Object>(\n  hashCons: (opts?: T) => Hash<H>\n): {\n  (msg: Input, opts?: T): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(opts: T): Hash<H>;\n} {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts: T) => hashCons(opts);\n  return hashC;\n}\n\nexport function wrapXOFConstructorWithOpts<H extends HashXOF<H>, T extends Object>(\n  hashCons: (opts?: T) => HashXOF<H>\n): {\n  (msg: Input, opts?: T): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(opts: T): HashXOF<H>;\n} {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts: T) => hashCons(opts);\n  return hashC;\n}\n\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nexport function randomBytes(bytesLength = 32): Uint8Array {\n  if (crypto && typeof crypto.getRandomValues === 'function') {\n    return crypto.getRandomValues(new Uint8Array(bytesLength));\n  }\n  // Legacy Node.js compatibility\n  if (crypto && typeof crypto.randomBytes === 'function') {\n    return crypto.randomBytes(bytesLength);\n  }\n  throw new Error('crypto.getRandomValues must be defined');\n}\n", "/**\n * Internal Merkle-<PERSON>gard hash utils.\n * @module\n */\nimport { aexists, aoutput } from './_assert.js';\nimport { type Input, Hash, createView, toBytes } from './utils.js';\n\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nexport function setBigUint64(\n  view: DataView,\n  byteOffset: number,\n  value: bigint,\n  isLE: boolean\n): void {\n  if (typeof view.setBigUint64 === 'function') return view.setBigUint64(byteOffset, value, isLE);\n  const _32n = BigInt(32);\n  const _u32_max = BigInt(0xffffffff);\n  const wh = Number((value >> _32n) & _u32_max);\n  const wl = Number(value & _u32_max);\n  const h = isLE ? 4 : 0;\n  const l = isLE ? 0 : 4;\n  view.setUint32(byteOffset + h, wh, isLE);\n  view.setUint32(byteOffset + l, wl, isLE);\n}\n\n/** Choice: a ? b : c */\nexport function Chi(a: number, b: number, c: number): number {\n  return (a & b) ^ (~a & c);\n}\n\n/** Majority function, true if any two inputs is true. */\nexport function Maj(a: number, b: number, c: number): number {\n  return (a & b) ^ (a & c) ^ (b & c);\n}\n\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nexport abstract class HashMD<T extends HashMD<T>> extends Hash<T> {\n  protected abstract process(buf: DataView, offset: number): void;\n  protected abstract get(): number[];\n  protected abstract set(...args: number[]): void;\n  abstract destroy(): void;\n  protected abstract roundClean(): void;\n  // For partial updates less than block size\n  protected buffer: Uint8Array;\n  protected view: DataView;\n  protected finished = false;\n  protected length = 0;\n  protected pos = 0;\n  protected destroyed = false;\n\n  constructor(\n    readonly blockLen: number,\n    public outputLen: number,\n    readonly padOffset: number,\n    readonly isLE: boolean\n  ) {\n    super();\n    this.buffer = new Uint8Array(blockLen);\n    this.view = createView(this.buffer);\n  }\n  update(data: Input): this {\n    aexists(this);\n    const { view, buffer, blockLen } = this;\n    data = toBytes(data);\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      // Fast path: we have at least one block in input, cast it to view and process\n      if (take === blockLen) {\n        const dataView = createView(data);\n        for (; blockLen <= len - pos; pos += blockLen) this.process(dataView, pos);\n        continue;\n      }\n      buffer.set(data.subarray(pos, pos + take), this.pos);\n      this.pos += take;\n      pos += take;\n      if (this.pos === blockLen) {\n        this.process(view, 0);\n        this.pos = 0;\n      }\n    }\n    this.length += data.length;\n    this.roundClean();\n    return this;\n  }\n  digestInto(out: Uint8Array): void {\n    aexists(this);\n    aoutput(out, this);\n    this.finished = true;\n    // Padding\n    // We can avoid allocation of buffer for padding completely if it\n    // was previously not allocated here. But it won't change performance.\n    const { buffer, view, blockLen, isLE } = this;\n    let { pos } = this;\n    // append the bit '1' to the message\n    buffer[pos++] = 0b10000000;\n    this.buffer.subarray(pos).fill(0);\n    // we have less than padOffset left in buffer, so we cannot put length in\n    // current block, need process it and pad again\n    if (this.padOffset > blockLen - pos) {\n      this.process(view, 0);\n      pos = 0;\n    }\n    // Pad until full block byte with zeros\n    for (let i = pos; i < blockLen; i++) buffer[i] = 0;\n    // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n    // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n    // So we just write lowest 64 bits of that value.\n    setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n    this.process(view, 0);\n    const oview = createView(out);\n    const len = this.outputLen;\n    // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n    if (len % 4) throw new Error('_sha2: outputLen should be aligned to 32bit');\n    const outLen = len / 4;\n    const state = this.get();\n    if (outLen > state.length) throw new Error('_sha2: outputLen bigger than state');\n    for (let i = 0; i < outLen; i++) oview.setUint32(4 * i, state[i], isLE);\n  }\n  digest(): Uint8Array {\n    const { buffer, outputLen } = this;\n    this.digestInto(buffer);\n    const res = buffer.slice(0, outputLen);\n    this.destroy();\n    return res;\n  }\n  _cloneInto(to?: T): T {\n    to ||= new (this.constructor as any)() as T;\n    to.set(...this.get());\n    const { blockLen, buffer, length, finished, destroyed, pos } = this;\n    to.length = length;\n    to.pos = pos;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    if (length % blockLen) to.buffer.set(buffer);\n    return to;\n  }\n}\n", "/**\n * RIPEMD-160 legacy hash function.\n * https://homes.esat.kuleuven.be/~bosselae/ripemd160.html\n * https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf\n * @module\n */\nimport { HashMD } from './_md.js';\nimport { rotl, wrapConstructor, type CHash } from './utils.js';\n\nconst Rho = /* @__PURE__ */ new Uint8Array([7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8]);\nconst Id = /* @__PURE__ */ new Uint8Array(new Array(16).fill(0).map((_, i) => i));\nconst Pi = /* @__PURE__ */ Id.map((i) => (9 * i + 5) % 16);\nlet idxL = [Id];\nlet idxR = [Pi];\nfor (let i = 0; i < 4; i++) for (let j of [idxL, idxR]) j.push(j[i].map((k) => Rho[k]));\n\nconst shifts = /* @__PURE__ */ [\n  [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8],\n  [12, 13, 11, 15, 6, 9, 9, 7, 12, 15, 11, 13, 7, 8, 7, 7],\n  [13, 15, 14, 11, 7, 7, 6, 8, 13, 14, 13, 12, 5, 5, 6, 9],\n  [14, 11, 12, 14, 8, 6, 5, 5, 15, 12, 15, 14, 9, 9, 8, 6],\n  [15, 12, 13, 13, 9, 5, 8, 6, 14, 11, 12, 11, 8, 6, 5, 5],\n].map((i) => new Uint8Array(i));\nconst shiftsL = /* @__PURE__ */ idxL.map((idx, i) => idx.map((j) => shifts[i][j]));\nconst shiftsR = /* @__PURE__ */ idxR.map((idx, i) => idx.map((j) => shifts[i][j]));\nconst Kl = /* @__PURE__ */ new Uint32Array([\n  0x00000000, 0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xa953fd4e,\n]);\nconst Kr = /* @__PURE__ */ new Uint32Array([\n  0x50a28be6, 0x5c4dd124, 0x6d703ef3, 0x7a6d76e9, 0x00000000,\n]);\n// It's called f() in spec.\nfunction f(group: number, x: number, y: number, z: number): number {\n  if (group === 0) return x ^ y ^ z;\n  else if (group === 1) return (x & y) | (~x & z);\n  else if (group === 2) return (x | ~y) ^ z;\n  else if (group === 3) return (x & z) | (y & ~z);\n  else return x ^ (y | ~z);\n}\n// Temporary buffer, not used to store anything between runs\nconst R_BUF = /* @__PURE__ */ new Uint32Array(16);\nexport class RIPEMD160 extends HashMD<RIPEMD160> {\n  private h0 = 0x67452301 | 0;\n  private h1 = 0xefcdab89 | 0;\n  private h2 = 0x98badcfe | 0;\n  private h3 = 0x10325476 | 0;\n  private h4 = 0xc3d2e1f0 | 0;\n\n  constructor() {\n    super(64, 20, 8, true);\n  }\n  protected get(): [number, number, number, number, number] {\n    const { h0, h1, h2, h3, h4 } = this;\n    return [h0, h1, h2, h3, h4];\n  }\n  protected set(h0: number, h1: number, h2: number, h3: number, h4: number): void {\n    this.h0 = h0 | 0;\n    this.h1 = h1 | 0;\n    this.h2 = h2 | 0;\n    this.h3 = h3 | 0;\n    this.h4 = h4 | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    for (let i = 0; i < 16; i++, offset += 4) R_BUF[i] = view.getUint32(offset, true);\n    // prettier-ignore\n    let al = this.h0 | 0, ar = al,\n        bl = this.h1 | 0, br = bl,\n        cl = this.h2 | 0, cr = cl,\n        dl = this.h3 | 0, dr = dl,\n        el = this.h4 | 0, er = el;\n\n    // Instead of iterating 0 to 80, we split it into 5 groups\n    // And use the groups in constants, functions, etc. Much simpler\n    for (let group = 0; group < 5; group++) {\n      const rGroup = 4 - group;\n      const hbl = Kl[group], hbr = Kr[group]; // prettier-ignore\n      const rl = idxL[group], rr = idxR[group]; // prettier-ignore\n      const sl = shiftsL[group], sr = shiftsR[group]; // prettier-ignore\n      for (let i = 0; i < 16; i++) {\n        const tl = (rotl(al + f(group, bl, cl, dl) + R_BUF[rl[i]] + hbl, sl[i]) + el) | 0;\n        al = el, el = dl, dl = rotl(cl, 10) | 0, cl = bl, bl = tl; // prettier-ignore\n      }\n      // 2 loops are 10% faster\n      for (let i = 0; i < 16; i++) {\n        const tr = (rotl(ar + f(rGroup, br, cr, dr) + R_BUF[rr[i]] + hbr, sr[i]) + er) | 0;\n        ar = er, er = dr, dr = rotl(cr, 10) | 0, cr = br, br = tr; // prettier-ignore\n      }\n    }\n    // Add the compressed chunk to the current hash value\n    this.set(\n      (this.h1 + cl + dr) | 0,\n      (this.h2 + dl + er) | 0,\n      (this.h3 + el + ar) | 0,\n      (this.h4 + al + br) | 0,\n      (this.h0 + bl + cr) | 0\n    );\n  }\n  protected roundClean(): void {\n    R_BUF.fill(0);\n  }\n  destroy(): void {\n    this.destroyed = true;\n    this.buffer.fill(0);\n    this.set(0, 0, 0, 0, 0);\n  }\n}\n\n/** RIPEMD-160 - a legacy hash function from 1990s. */\nexport const ripemd160: CHash = /* @__PURE__ */ wrapConstructor(() => new RIPEMD160());\n", "/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n\nfunction fromBig(\n  n: bigint,\n  le = false\n): {\n  h: number;\n  l: number;\n} {\n  if (le) return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n  return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\n\nfunction split(lst: bigint[], le = false): Uint32Array[] {\n  let Ah = new Uint32Array(lst.length);\n  let Al = new Uint32Array(lst.length);\n  for (let i = 0; i < lst.length; i++) {\n    const { h, l } = fromBig(lst[i], le);\n    [Ah[i], Al[i]] = [h, l];\n  }\n  return [Ah, Al];\n}\n\nconst toBig = (h: number, l: number): bigint => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h: number, _l: number, s: number): number => h >>> s;\nconst shrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h: number, l: number, s: number): number => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h: number, l: number, s: number): number => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h: number, l: number, s: number): number => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h: number, l: number): number => l;\nconst rotr32L = (h: number, _l: number): number => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h: number, l: number, s: number): number => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h: number, l: number, s: number): number => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h: number, l: number, s: number): number => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h: number, l: number, s: number): number => (h << (s - 32)) | (l >>> (64 - s));\n\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(\n  Ah: number,\n  Al: number,\n  Bh: number,\n  Bl: number\n): {\n  h: number;\n  l: number;\n} {\n  const l = (Al >>> 0) + (Bl >>> 0);\n  return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al: number, Bl: number, Cl: number): number => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low: number, Ah: number, Bh: number, Ch: number): number =>\n  (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al: number, Bl: number, Cl: number, Dl: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number): number =>\n  (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al: number, Bl: number, Cl: number, Dl: number, El: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number): number =>\n  (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n\n// prettier-ignore\nexport {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\n// prettier-ignore\nconst u64: { fromBig: typeof fromBig; split: typeof split; toBig: (h: number, l: number) => bigint; shrSH: (h: number, _l: number, s: number) => number; shrSL: (h: number, l: number, s: number) => number; rotrSH: (h: number, l: number, s: number) => number; rotrSL: (h: number, l: number, s: number) => number; rotrBH: (h: number, l: number, s: number) => number; rotrBL: (h: number, l: number, s: number) => number; rotr32H: (_h: number, l: number) => number; rotr32L: (h: number, _l: number) => number; rotlSH: (h: number, l: number, s: number) => number; rotlSL: (h: number, l: number, s: number) => number; rotlBH: (h: number, l: number, s: number) => number; rotlBL: (h: number, l: number, s: number) => number; add: typeof add; add3L: (Al: number, Bl: number, Cl: number) => number; add3H: (low: number, Ah: number, Bh: number, Ch: number) => number; add4L: (Al: number, Bl: number, Cl: number, Dl: number) => number; add4H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number) => number; add5H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number) => number; add5L: (Al: number, Bl: number, Cl: number, Dl: number, El: number) => number; } = {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n", "/**\n * SHA3 (keccak) hash function, based on a new \"Sponge function\" design.\n * Different from older hashes, the internal state is bigger than output size.\n *\n * Check out [FIPS-202](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf),\n * [Website](https://keccak.team/keccak.html),\n * [the differences between SHA-3 and Keccak](https://crypto.stackexchange.com/questions/15727/what-are-the-key-differences-between-the-draft-sha-3-standard-and-the-keccak-sub).\n *\n * Check out `sha3-addons` module for cSHA<PERSON>, k12, and others.\n * @module\n */\nimport { abytes, aexists, anumber, aoutput } from './_assert.js';\nimport { rotlBH, rotlBL, rotlSH, rotlSL, split } from './_u64.js';\nimport {\n  byteSwap32,\n  Hash,\n  isLE,\n  toBytes,\n  u32,\n  wrapConstructor,\n  wrapXOFConstructorWithOpts,\n  type CHash,\n  type CHashXO,\n  type HashXOF,\n  type Input,\n} from './utils.js';\n\n// Various per round constants calculations\nconst SHA3_PI: number[] = [];\nconst SHA3_ROTL: number[] = [];\nconst _SHA3_IOTA: bigint[] = [];\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nconst _7n = /* @__PURE__ */ BigInt(7);\nconst _256n = /* @__PURE__ */ BigInt(256);\nconst _0x71n = /* @__PURE__ */ BigInt(0x71);\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n  // Pi\n  [x, y] = [y, (2 * x + 3 * y) % 5];\n  SHA3_PI.push(2 * (5 * y + x));\n  // Rotational\n  SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n  // Iota\n  let t = _0n;\n  for (let j = 0; j < 7; j++) {\n    R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n    if (R & _2n) t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n  }\n  _SHA3_IOTA.push(t);\n}\nconst [SHA3_IOTA_H, SHA3_IOTA_L] = /* @__PURE__ */ split(_SHA3_IOTA, true);\n\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h: number, l: number, s: number) => (s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s));\nconst rotlL = (h: number, l: number, s: number) => (s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s));\n\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nexport function keccakP(s: Uint32Array, rounds: number = 24): void {\n  const B = new Uint32Array(5 * 2);\n  // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n  for (let round = 24 - rounds; round < 24; round++) {\n    // Theta θ\n    for (let x = 0; x < 10; x++) B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n    for (let x = 0; x < 10; x += 2) {\n      const idx1 = (x + 8) % 10;\n      const idx0 = (x + 2) % 10;\n      const B0 = B[idx0];\n      const B1 = B[idx0 + 1];\n      const Th = rotlH(B0, B1, 1) ^ B[idx1];\n      const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n      for (let y = 0; y < 50; y += 10) {\n        s[x + y] ^= Th;\n        s[x + y + 1] ^= Tl;\n      }\n    }\n    // Rho (ρ) and Pi (π)\n    let curH = s[2];\n    let curL = s[3];\n    for (let t = 0; t < 24; t++) {\n      const shift = SHA3_ROTL[t];\n      const Th = rotlH(curH, curL, shift);\n      const Tl = rotlL(curH, curL, shift);\n      const PI = SHA3_PI[t];\n      curH = s[PI];\n      curL = s[PI + 1];\n      s[PI] = Th;\n      s[PI + 1] = Tl;\n    }\n    // Chi (χ)\n    for (let y = 0; y < 50; y += 10) {\n      for (let x = 0; x < 10; x++) B[x] = s[y + x];\n      for (let x = 0; x < 10; x++) s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n    }\n    // Iota (ι)\n    s[0] ^= SHA3_IOTA_H[round];\n    s[1] ^= SHA3_IOTA_L[round];\n  }\n  B.fill(0);\n}\n\n/** Keccak sponge function. */\nexport class Keccak extends Hash<Keccak> implements HashXOF<Keccak> {\n  protected state: Uint8Array;\n  protected pos = 0;\n  protected posOut = 0;\n  protected finished = false;\n  protected state32: Uint32Array;\n  protected destroyed = false;\n  // NOTE: we accept arguments in bytes instead of bits here.\n  constructor(\n    public blockLen: number,\n    public suffix: number,\n    public outputLen: number,\n    protected enableXOF = false,\n    protected rounds: number = 24\n  ) {\n    super();\n    // Can be passed from user as dkLen\n    anumber(outputLen);\n    // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n    // 0 < blockLen < 200\n    if (0 >= this.blockLen || this.blockLen >= 200)\n      throw new Error('Sha3 supports only keccak-f1600 function');\n    this.state = new Uint8Array(200);\n    this.state32 = u32(this.state);\n  }\n  protected keccak(): void {\n    if (!isLE) byteSwap32(this.state32);\n    keccakP(this.state32, this.rounds);\n    if (!isLE) byteSwap32(this.state32);\n    this.posOut = 0;\n    this.pos = 0;\n  }\n  update(data: Input): this {\n    aexists(this);\n    const { blockLen, state } = this;\n    data = toBytes(data);\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      for (let i = 0; i < take; i++) state[this.pos++] ^= data[pos++];\n      if (this.pos === blockLen) this.keccak();\n    }\n    return this;\n  }\n  protected finish(): void {\n    if (this.finished) return;\n    this.finished = true;\n    const { state, suffix, pos, blockLen } = this;\n    // Do the padding\n    state[pos] ^= suffix;\n    if ((suffix & 0x80) !== 0 && pos === blockLen - 1) this.keccak();\n    state[blockLen - 1] ^= 0x80;\n    this.keccak();\n  }\n  protected writeInto(out: Uint8Array): Uint8Array {\n    aexists(this, false);\n    abytes(out);\n    this.finish();\n    const bufferOut = this.state;\n    const { blockLen } = this;\n    for (let pos = 0, len = out.length; pos < len; ) {\n      if (this.posOut >= blockLen) this.keccak();\n      const take = Math.min(blockLen - this.posOut, len - pos);\n      out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n      this.posOut += take;\n      pos += take;\n    }\n    return out;\n  }\n  xofInto(out: Uint8Array): Uint8Array {\n    // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n    if (!this.enableXOF) throw new Error('XOF is not possible for this instance');\n    return this.writeInto(out);\n  }\n  xof(bytes: number): Uint8Array {\n    anumber(bytes);\n    return this.xofInto(new Uint8Array(bytes));\n  }\n  digestInto(out: Uint8Array): Uint8Array {\n    aoutput(out, this);\n    if (this.finished) throw new Error('digest() was already called');\n    this.writeInto(out);\n    this.destroy();\n    return out;\n  }\n  digest(): Uint8Array {\n    return this.digestInto(new Uint8Array(this.outputLen));\n  }\n  destroy(): void {\n    this.destroyed = true;\n    this.state.fill(0);\n  }\n  _cloneInto(to?: Keccak): Keccak {\n    const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n    to ||= new Keccak(blockLen, suffix, outputLen, enableXOF, rounds);\n    to.state32.set(this.state32);\n    to.pos = this.pos;\n    to.posOut = this.posOut;\n    to.finished = this.finished;\n    to.rounds = rounds;\n    // Suffix can change in cSHAKE\n    to.suffix = suffix;\n    to.outputLen = outputLen;\n    to.enableXOF = enableXOF;\n    to.destroyed = this.destroyed;\n    return to;\n  }\n}\n\nconst gen = (suffix: number, blockLen: number, outputLen: number) =>\n  wrapConstructor(() => new Keccak(blockLen, suffix, outputLen));\n\n/** SHA3-224 hash function. */\nexport const sha3_224: CHash = /* @__PURE__ */ gen(0x06, 144, 224 / 8);\n/** SHA3-256 hash function. Different from keccak-256. */\nexport const sha3_256: CHash = /* @__PURE__ */ gen(0x06, 136, 256 / 8);\n/** SHA3-384 hash function. */\nexport const sha3_384: CHash = /* @__PURE__ */ gen(0x06, 104, 384 / 8);\n/** SHA3-512 hash function. */\nexport const sha3_512: CHash = /* @__PURE__ */ gen(0x06, 72, 512 / 8);\n\n/** keccak-224 hash function. */\nexport const keccak_224: CHash = /* @__PURE__ */ gen(0x01, 144, 224 / 8);\n/** keccak-256 hash function. Different from SHA3-256. */\nexport const keccak_256: CHash = /* @__PURE__ */ gen(0x01, 136, 256 / 8);\n/** keccak-384 hash function. */\nexport const keccak_384: CHash = /* @__PURE__ */ gen(0x01, 104, 384 / 8);\n/** keccak-512 hash function. */\nexport const keccak_512: CHash = /* @__PURE__ */ gen(0x01, 72, 512 / 8);\n\nexport type ShakeOpts = { dkLen?: number };\n\nconst genShake = (suffix: number, blockLen: number, outputLen: number) =>\n  wrapXOFConstructorWithOpts<HashXOF<Keccak>, ShakeOpts>(\n    (opts: ShakeOpts = {}) =>\n      new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true)\n  );\n\n/** SHAKE128 XOF with 128-bit security. */\nexport const shake128: CHashXO = /* @__PURE__ */ genShake(0x1f, 168, 128 / 8);\n/** SHAKE256 XOF with 256-bit security. */\nexport const shake256: CHashXO = /* @__PURE__ */ genShake(0x1f, 136, 256 / 8);\n", "/**\n * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake<PERSON>.\n *\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n *\n * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\nimport { Chi, HashMD, Maj } from './_md.js';\nimport { type CHash, rotr, wrapConstructor } from './utils.js';\n\n/** Round constants: first 32 bits of fractional parts of the cube roots of the first 64 primes 2..311). */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n  0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n  0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n  0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n  0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n  0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n  0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n  0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n  0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n\n/** Initial state: first 32 bits of fractional parts of the square roots of the first 8 primes 2..19. */\n// prettier-ignore\nconst SHA256_IV = /* @__PURE__ */ new Uint32Array([\n  0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n\n/**\n * Temporary buffer, not used to store anything between runs.\n * Named this way because it matches specification.\n */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nexport class SHA256 extends HashMD<SHA256> {\n  // We cannot use array here since array allows indexing by variable\n  // which means optimizer/compiler cannot use registers.\n  protected A: number = SHA256_IV[0] | 0;\n  protected B: number = SHA256_IV[1] | 0;\n  protected C: number = SHA256_IV[2] | 0;\n  protected D: number = SHA256_IV[3] | 0;\n  protected E: number = SHA256_IV[4] | 0;\n  protected F: number = SHA256_IV[5] | 0;\n  protected G: number = SHA256_IV[6] | 0;\n  protected H: number = SHA256_IV[7] | 0;\n\n  constructor() {\n    super(64, 32, 8, false);\n  }\n  protected get(): [number, number, number, number, number, number, number, number] {\n    const { A, B, C, D, E, F, G, H } = this;\n    return [A, B, C, D, E, F, G, H];\n  }\n  // prettier-ignore\n  protected set(\n    A: number, B: number, C: number, D: number, E: number, F: number, G: number, H: number\n  ): void {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n    this.E = E | 0;\n    this.F = F | 0;\n    this.G = G | 0;\n    this.H = H | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) SHA256_W[i] = view.getUint32(offset, false);\n    for (let i = 16; i < 64; i++) {\n      const W15 = SHA256_W[i - 15];\n      const W2 = SHA256_W[i - 2];\n      const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n      const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n      SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n    }\n    // Compression function main loop, 64 rounds\n    let { A, B, C, D, E, F, G, H } = this;\n    for (let i = 0; i < 64; i++) {\n      const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n      const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n      const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n      const T2 = (sigma0 + Maj(A, B, C)) | 0;\n      H = G;\n      G = F;\n      F = E;\n      E = (D + T1) | 0;\n      D = C;\n      C = B;\n      B = A;\n      A = (T1 + T2) | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    A = (A + this.A) | 0;\n    B = (B + this.B) | 0;\n    C = (C + this.C) | 0;\n    D = (D + this.D) | 0;\n    E = (E + this.E) | 0;\n    F = (F + this.F) | 0;\n    G = (G + this.G) | 0;\n    H = (H + this.H) | 0;\n    this.set(A, B, C, D, E, F, G, H);\n  }\n  protected roundClean(): void {\n    SHA256_W.fill(0);\n  }\n  destroy(): void {\n    this.set(0, 0, 0, 0, 0, 0, 0, 0);\n    this.buffer.fill(0);\n  }\n}\n\n/**\n * Constants taken from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf.\n */\nclass SHA224 extends SHA256 {\n  protected A = 0xc1059ed8 | 0;\n  protected B = 0x367cd507 | 0;\n  protected C = 0x3070dd17 | 0;\n  protected D = 0xf70e5939 | 0;\n  protected E = 0xffc00b31 | 0;\n  protected F = 0x68581511 | 0;\n  protected G = 0x64f98fa7 | 0;\n  protected H = 0xbefa4fa4 | 0;\n  constructor() {\n    super();\n    this.outputLen = 28;\n  }\n}\n\n/** SHA2-256 hash function */\nexport const sha256: CHash = /* @__PURE__ */ wrapConstructor(() => new SHA256());\n/** SHA2-224 hash function */\nexport const sha224: CHash = /* @__PURE__ */ wrapConstructor(() => new SHA224());\n", "import { ripemd160 as noble_ripemd160 } from '@noble/hashes/ripemd160'\nimport { keccak_256 as noble_keccak256 } from '@noble/hashes/sha3'\nimport { sha256 as noble_sha256 } from '@noble/hashes/sha256'\nimport * as Bytes from './Bytes.js'\nimport type * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\n\n/**\n * Calculates the [Keccak256](https://en.wikipedia.org/wiki/SHA-3) hash of a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n *\n * This function is a re-export of `keccak_256` from [`@noble/hashes`](https://github.com/paulmillr/noble-hashes), an audited & minimal JS hashing library.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.keccak256('0xdeadbeef')\n * // @log: '0xd4fd4e189132273036449fc9e11198c739161b4c0116a9a2dccdfa1c492006f1'\n * ```\n *\n * @example\n * ### Calculate Hash of a String\n *\n * ```ts twoslash\n * import { Hash, Hex } from 'ox'\n *\n * Hash.keccak256(Hex.fromString('hello world'))\n * // @log: '0x3ea2f1d0abf3fc66cf29eebb70cbd4e7fe762ef8a09bcc06c8edf641230afec0'\n * ```\n *\n * @example\n * ### Configure Return Type\n *\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.keccak256('0xdeadbeef', { as: 'Bytes' })\n * // @log: Uint8Array [...]\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n * @param options - Options.\n * @returns Keccak256 hash.\n */\nexport function keccak256<\n  value extends Hex.Hex | Bytes.Bytes,\n  as extends 'Hex' | 'Bytes' =\n    | (value extends Hex.Hex ? 'Hex' : never)\n    | (value extends Bytes.Bytes ? 'Bytes' : never),\n>(\n  value: value | Hex.Hex | Bytes.Bytes,\n  options: keccak256.Options<as> = {},\n): keccak256.ReturnType<as> {\n  const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options\n  const bytes = noble_keccak256(Bytes.from(value))\n  if (as === 'Bytes') return bytes as never\n  return Hex.fromBytes(bytes) as never\n}\n\nexport declare namespace keccak256 {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> = {\n    /** The return type. @default 'Hex' */\n    as?: as | 'Hex' | 'Bytes' | undefined\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Bytes.from.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Calculates the [Ripemd160](https://en.wikipedia.org/wiki/RIPEMD) hash of a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n *\n * This function is a re-export of `ripemd160` from [`@noble/hashes`](https://github.com/paulmillr/noble-hashes), an audited & minimal JS hashing library.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.ripemd160('0xdeadbeef')\n * // '0x226821c2f5423e11fe9af68bd285c249db2e4b5a'\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n * @param options - Options.\n * @returns Ripemd160 hash.\n */\nexport function ripemd160<\n  value extends Hex.Hex | Bytes.Bytes,\n  as extends 'Hex' | 'Bytes' =\n    | (value extends Hex.Hex ? 'Hex' : never)\n    | (value extends Bytes.Bytes ? 'Bytes' : never),\n>(\n  value: value | Hex.Hex | Bytes.Bytes,\n  options: ripemd160.Options<as> = {},\n): ripemd160.ReturnType<as> {\n  const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options\n  const bytes = noble_ripemd160(Bytes.from(value))\n  if (as === 'Bytes') return bytes as never\n  return Hex.fromBytes(bytes) as never\n}\n\nexport declare namespace ripemd160 {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> = {\n    /** The return type. @default 'Hex' */\n    as?: as | 'Hex' | 'Bytes' | undefined\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Bytes.from.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Calculates the [Sha256](https://en.wikipedia.org/wiki/SHA-256) hash of a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n *\n * This function is a re-export of `sha256` from [`@noble/hashes`](https://github.com/paulmillr/noble-hashes), an audited & minimal JS hashing library.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.sha256('0xdeadbeef')\n * // '0x5f78c33274e43fa9de5659265c1d917e25c03722dcb0b8d27db8d5feaa813953'\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n * @param options - Options.\n * @returns Sha256 hash.\n */\nexport function sha256<\n  value extends Hex.Hex | Bytes.Bytes,\n  as extends 'Hex' | 'Bytes' =\n    | (value extends Hex.Hex ? 'Hex' : never)\n    | (value extends Bytes.Bytes ? 'Bytes' : never),\n>(\n  value: value | Hex.Hex | Bytes.Bytes,\n  options: sha256.Options<as> = {},\n): sha256.ReturnType<as> {\n  const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options\n  const bytes = noble_sha256(Bytes.from(value))\n  if (as === 'Bytes') return bytes as never\n  return Hex.fromBytes(bytes) as never\n}\n\nexport declare namespace sha256 {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex'> = {\n    /** The return type. @default 'Hex' */\n    as?: as | 'Hex' | 'Bytes' | undefined\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Bytes.from.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if a string is a valid hash value.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.validate('0x')\n * // @log: false\n *\n * Hash.validate('0x3ea2f1d0abf3fc66cf29eebb70cbd4e7fe762ef8a09bcc06c8edf641230afec0')\n * // @log: true\n * ```\n *\n * @param value - Value to check.\n * @returns Whether the value is a valid hash.\n */\nexport function validate(value: string): value is Hex.Hex {\n  return Hex.validate(value) && Hex.size(value) === 32\n}\n\nexport declare namespace validate {\n  type ErrorType =\n    | Hex.validate.ErrorType\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n", "/**\n * @internal\n *\n * Map with a LRU (Least recently used) policy.\n * @see https://en.wikipedia.org/wiki/Cache_replacement_policies#LRU\n */\nexport class LruMap<value = unknown> extends Map<string, value> {\n  maxSize: number\n\n  constructor(size: number) {\n    super()\n    this.maxSize = size\n  }\n\n  override get(key: string) {\n    const value = super.get(key)\n\n    if (super.has(key) && value !== undefined) {\n      this.delete(key)\n      super.set(key, value)\n    }\n\n    return value\n  }\n\n  override set(key: string, value: value) {\n    super.set(key, value)\n    if (this.maxSize && this.size > this.maxSize) {\n      const firstKey = this.keys().next().value\n      if (firstKey) this.delete(firstKey)\n    }\n    return this\n  }\n}\n", "import type * as Address from './Address.js'\nimport { LruMap } from './internal/lru.js'\n\nconst caches = {\n  checksum: /*#__PURE__*/ new LruMap<Address.Address>(8192),\n}\n\nexport const checksum = caches.checksum\n\n/**\n * Clears all global caches.\n *\n * @example\n * ```ts\n * import { Caches } from 'ox'\n * Caches.clear()\n * ```\n */\nexport function clear() {\n  for (const cache of Object.values(caches)) cache.clear()\n}\n", "import type { Address as abitype_Address } from 'abitype'\nimport * as Bytes from './Bytes.js'\nimport * as Caches from './Caches.js'\nimport * as Errors from './Errors.js'\nimport * as Hash from './Hash.js'\nimport * as <PERSON>Key from './PublicKey.js'\n\nconst addressRegex = /*#__PURE__*/ /^0x[a-fA-F0-9]{40}$/\n\n/** Root type for Address. */\nexport type Address = abitype_Address\n\n/**\n * Asserts that the given value is a valid {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.assert('******************************************')\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.assert('0xdeadbeef')\n * // @error: InvalidAddressError: Address \"0xdeadbeef\" is invalid.\n * ```\n *\n * @param value - Value to assert if it is a valid address.\n * @param options - Assertion options.\n */\nexport function assert(\n  value: string,\n  options: assert.Options = {},\n): asserts value is Address {\n  const { strict = true } = options\n\n  if (!addressRegex.test(value))\n    throw new InvalidAddressError({\n      address: value,\n      cause: new InvalidInputError(),\n    })\n\n  if (strict) {\n    if (value.toLowerCase() === value) return\n    if (checksum(value as Address) !== value)\n      throw new InvalidAddressError({\n        address: value,\n        cause: new InvalidChecksumError(),\n      })\n  }\n}\n\nexport declare namespace assert {\n  type Options = {\n    /**\n     * Enables strict mode. Whether or not to compare the address against its checksum.\n     *\n     * @default true\n     */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType = InvalidAddressError | Errors.GlobalErrorType\n}\n\n/**\n * Computes the checksum address for the given {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.checksum('******************************************')\n * // @log: '******************************************'\n * ```\n *\n * @param address - The address to compute the checksum for.\n * @returns The checksummed address.\n */\nexport function checksum(address: string): Address {\n  if (Caches.checksum.has(address)) return Caches.checksum.get(address)!\n\n  assert(address, { strict: false })\n\n  const hexAddress = address.substring(2).toLowerCase()\n  const hash = Hash.keccak256(Bytes.fromString(hexAddress), { as: 'Bytes' })\n\n  const characters = hexAddress.split('')\n  for (let i = 0; i < 40; i += 2) {\n    if (hash[i >> 1]! >> 4 >= 8 && characters[i]) {\n      characters[i] = characters[i]!.toUpperCase()\n    }\n    if ((hash[i >> 1]! & 0x0f) >= 8 && characters[i + 1]) {\n      characters[i + 1] = characters[i + 1]!.toUpperCase()\n    }\n  }\n\n  const result = `0x${characters.join('')}` as const\n  Caches.checksum.set(address, result)\n  return result\n}\n\nexport declare namespace checksum {\n  type ErrorType =\n    | assert.ErrorType\n    | Hash.keccak256.ErrorType\n    | Bytes.fromString.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts a stringified address to a typed (checksummed) {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('******************************************')\n * // @log: '******************************************'\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('******************************************', {\n *   checksum: false\n * })\n * // @log: '******************************************'\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('hello')\n * // @error: InvalidAddressError: Address \"0xa\" is invalid.\n * ```\n *\n * @param address - An address string to convert to a typed Address.\n * @param options - Conversion options.\n * @returns The typed Address.\n */\nexport function from(address: string, options: from.Options = {}): Address {\n  const { checksum: checksumVal = false } = options\n  assert(address)\n  if (checksumVal) return checksum(address)\n  return address as Address\n}\n\nexport declare namespace from {\n  type Options = {\n    /**\n     * Whether to checksum the address.\n     *\n     * @default false\n     */\n    checksum?: boolean | undefined\n  }\n\n  type ErrorType =\n    | assert.ErrorType\n    | checksum.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts an ECDSA public key to an {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address, PublicKey } from 'ox'\n *\n * const publicKey = PublicKey.from(\n *   '0x048318535b54105d4a7aae60c08fc45f9687181b4fdfc625bd1a753fa7397fed753547f11ca8696646f2f3acb08e31016afac23e630c5d11f59f61fef57b0d2aa5',\n * )\n * const address = Address.fromPublicKey(publicKey)\n * // @log: '******************************************'\n * ```\n *\n * @param publicKey - The ECDSA public key to convert to an {@link ox#Address.Address}.\n * @param options - Conversion options.\n * @returns The {@link ox#Address.Address} corresponding to the public key.\n */\nexport function fromPublicKey(\n  publicKey: PublicKey.PublicKey,\n  options: fromPublicKey.Options = {},\n): Address {\n  const address = Hash.keccak256(\n    `0x${PublicKey.toHex(publicKey).slice(4)}`,\n  ).substring(26)\n  return from(`0x${address}`, options)\n}\n\nexport declare namespace fromPublicKey {\n  type Options = {\n    /**\n     * Whether to checksum the address.\n     *\n     * @default false\n     */\n    checksum?: boolean | undefined\n  }\n\n  type ErrorType =\n    | Hash.keccak256.ErrorType\n    | PublicKey.toHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if two {@link ox#Address.Address} are equal.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.isEqual(\n *   '******************************************',\n *   '******************************************'\n * )\n * // @log: true\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.isEqual(\n *   '******************************************',\n *   '******************************************'\n * )\n * // @log: false\n * ```\n *\n * @param addressA - The first address to compare.\n * @param addressB - The second address to compare.\n * @returns Whether the addresses are equal.\n */\nexport function isEqual(addressA: Address, addressB: Address): boolean {\n  assert(addressA, { strict: false })\n  assert(addressB, { strict: false })\n  return addressA.toLowerCase() === addressB.toLowerCase()\n}\n\nexport declare namespace isEqual {\n  type ErrorType = assert.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Checks if the given address is a valid {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.validate('******************************************')\n * // @log: true\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.validate('0xdeadbeef')\n * // @log: false\n * ```\n *\n * @param address - Value to check if it is a valid address.\n * @param options - Check options.\n * @returns Whether the address is a valid address.\n */\nexport function validate(\n  address: string,\n  options: validate.Options = {},\n): address is Address {\n  const { strict = true } = options ?? {}\n  try {\n    assert(address, { strict })\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type Options = {\n    /**\n     * Enables strict mode. Whether or not to compare the address against its checksum.\n     *\n     * @default true\n     */\n    strict?: boolean | undefined\n  }\n}\n\n/**\n * Thrown when an address is invalid.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('0x123')\n * // @error: Address.InvalidAddressError: Address `0x123` is invalid.\n * ```\n */\nexport class InvalidAddressError<\n  cause extends InvalidInputError | InvalidChecksumError =\n    | InvalidInputError\n    | InvalidChecksumError,\n> extends Errors.BaseError<cause> {\n  override readonly name = 'Address.InvalidAddressError'\n\n  constructor({ address, cause }: { address: string; cause: cause }) {\n    super(`Address \"${address}\" is invalid.`, {\n      cause,\n    })\n  }\n}\n\n/** Thrown when an address is not a 20 byte (40 hexadecimal character) value. */\nexport class InvalidInputError extends Errors.BaseError {\n  override readonly name = 'Address.InvalidInputError'\n\n  constructor() {\n    super('Address is not a 20 byte (40 hexadecimal character) value.')\n  }\n}\n\n/** Thrown when an address does not match its checksum counterpart. */\nexport class InvalidChecksumError extends Errors.BaseError {\n  override readonly name = 'Address.InvalidChecksumError'\n\n  constructor() {\n    super('Address does not match its checksum counterpart.')\n  }\n}\n", "export const arrayRegex = /^(.*)\\[([0-9]*)\\]$/\n\n// `bytes<M>`: binary type of `M` bytes, `0 < M <= 32`\n// https://regexr.com/6va55\nexport const bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/\n\n// `(u)int<M>`: (un)signed integer type of `M` bits, `0 < M <= 256`, `M % 8 == 0`\n// https://regexr.com/6v8hp\nexport const integerRegex =\n  /^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/\n\nexport const maxInt8 = 2n ** (8n - 1n) - 1n\nexport const maxInt16 = 2n ** (16n - 1n) - 1n\nexport const maxInt24 = 2n ** (24n - 1n) - 1n\nexport const maxInt32 = 2n ** (32n - 1n) - 1n\nexport const maxInt40 = 2n ** (40n - 1n) - 1n\nexport const maxInt48 = 2n ** (48n - 1n) - 1n\nexport const maxInt56 = 2n ** (56n - 1n) - 1n\nexport const maxInt64 = 2n ** (64n - 1n) - 1n\nexport const maxInt72 = 2n ** (72n - 1n) - 1n\nexport const maxInt80 = 2n ** (80n - 1n) - 1n\nexport const maxInt88 = 2n ** (88n - 1n) - 1n\nexport const maxInt96 = 2n ** (96n - 1n) - 1n\nexport const maxInt104 = 2n ** (104n - 1n) - 1n\nexport const maxInt112 = 2n ** (112n - 1n) - 1n\nexport const maxInt120 = 2n ** (120n - 1n) - 1n\nexport const maxInt128 = 2n ** (128n - 1n) - 1n\nexport const maxInt136 = 2n ** (136n - 1n) - 1n\nexport const maxInt144 = 2n ** (144n - 1n) - 1n\nexport const maxInt152 = 2n ** (152n - 1n) - 1n\nexport const maxInt160 = 2n ** (160n - 1n) - 1n\nexport const maxInt168 = 2n ** (168n - 1n) - 1n\nexport const maxInt176 = 2n ** (176n - 1n) - 1n\nexport const maxInt184 = 2n ** (184n - 1n) - 1n\nexport const maxInt192 = 2n ** (192n - 1n) - 1n\nexport const maxInt200 = 2n ** (200n - 1n) - 1n\nexport const maxInt208 = 2n ** (208n - 1n) - 1n\nexport const maxInt216 = 2n ** (216n - 1n) - 1n\nexport const maxInt224 = 2n ** (224n - 1n) - 1n\nexport const maxInt232 = 2n ** (232n - 1n) - 1n\nexport const maxInt240 = 2n ** (240n - 1n) - 1n\nexport const maxInt248 = 2n ** (248n - 1n) - 1n\nexport const maxInt256 = 2n ** (256n - 1n) - 1n\n\nexport const minInt8 = -(2n ** (8n - 1n))\nexport const minInt16 = -(2n ** (16n - 1n))\nexport const minInt24 = -(2n ** (24n - 1n))\nexport const minInt32 = -(2n ** (32n - 1n))\nexport const minInt40 = -(2n ** (40n - 1n))\nexport const minInt48 = -(2n ** (48n - 1n))\nexport const minInt56 = -(2n ** (56n - 1n))\nexport const minInt64 = -(2n ** (64n - 1n))\nexport const minInt72 = -(2n ** (72n - 1n))\nexport const minInt80 = -(2n ** (80n - 1n))\nexport const minInt88 = -(2n ** (88n - 1n))\nexport const minInt96 = -(2n ** (96n - 1n))\nexport const minInt104 = -(2n ** (104n - 1n))\nexport const minInt112 = -(2n ** (112n - 1n))\nexport const minInt120 = -(2n ** (120n - 1n))\nexport const minInt128 = -(2n ** (128n - 1n))\nexport const minInt136 = -(2n ** (136n - 1n))\nexport const minInt144 = -(2n ** (144n - 1n))\nexport const minInt152 = -(2n ** (152n - 1n))\nexport const minInt160 = -(2n ** (160n - 1n))\nexport const minInt168 = -(2n ** (168n - 1n))\nexport const minInt176 = -(2n ** (176n - 1n))\nexport const minInt184 = -(2n ** (184n - 1n))\nexport const minInt192 = -(2n ** (192n - 1n))\nexport const minInt200 = -(2n ** (200n - 1n))\nexport const minInt208 = -(2n ** (208n - 1n))\nexport const minInt216 = -(2n ** (216n - 1n))\nexport const minInt224 = -(2n ** (224n - 1n))\nexport const minInt232 = -(2n ** (232n - 1n))\nexport const minInt240 = -(2n ** (240n - 1n))\nexport const minInt248 = -(2n ** (248n - 1n))\nexport const minInt256 = -(2n ** (256n - 1n))\n\nexport const maxUint8 = 2n ** 8n - 1n\nexport const maxUint16 = 2n ** 16n - 1n\nexport const maxUint24 = 2n ** 24n - 1n\nexport const maxUint32 = 2n ** 32n - 1n\nexport const maxUint40 = 2n ** 40n - 1n\nexport const maxUint48 = 2n ** 48n - 1n\nexport const maxUint56 = 2n ** 56n - 1n\nexport const maxUint64 = 2n ** 64n - 1n\nexport const maxUint72 = 2n ** 72n - 1n\nexport const maxUint80 = 2n ** 80n - 1n\nexport const maxUint88 = 2n ** 88n - 1n\nexport const maxUint96 = 2n ** 96n - 1n\nexport const maxUint104 = 2n ** 104n - 1n\nexport const maxUint112 = 2n ** 112n - 1n\nexport const maxUint120 = 2n ** 120n - 1n\nexport const maxUint128 = 2n ** 128n - 1n\nexport const maxUint136 = 2n ** 136n - 1n\nexport const maxUint144 = 2n ** 144n - 1n\nexport const maxUint152 = 2n ** 152n - 1n\nexport const maxUint160 = 2n ** 160n - 1n\nexport const maxUint168 = 2n ** 168n - 1n\nexport const maxUint176 = 2n ** 176n - 1n\nexport const maxUint184 = 2n ** 184n - 1n\nexport const maxUint192 = 2n ** 192n - 1n\nexport const maxUint200 = 2n ** 200n - 1n\nexport const maxUint208 = 2n ** 208n - 1n\nexport const maxUint216 = 2n ** 216n - 1n\nexport const maxUint224 = 2n ** 224n - 1n\nexport const maxUint232 = 2n ** 232n - 1n\nexport const maxUint240 = 2n ** 240n - 1n\nexport const maxUint248 = 2n ** 248n - 1n\nexport const maxUint256 = 2n ** 256n - 1n\n", "import type { Bytes } from '../Bytes.js'\nimport * as Errors from '../Errors.js'\n\n/** @internal */\nexport type Cursor = {\n  bytes: Bytes\n  dataView: DataView\n  position: number\n  positionReadCount: Map<number, number>\n  recursiveReadCount: number\n  recursiveReadLimit: number\n  remaining: number\n  assertReadLimit(position?: number): void\n  assertPosition(position: number): void\n  decrementPosition(offset: number): void\n  getReadCount(position?: number): number\n  incrementPosition(offset: number): void\n  inspectByte(position?: number): Bytes[number]\n  inspectBytes(length: number, position?: number): Bytes\n  inspectUint8(position?: number): number\n  inspectUint16(position?: number): number\n  inspectUint24(position?: number): number\n  inspectUint32(position?: number): number\n  pushByte(byte: Bytes[number]): void\n  pushBytes(bytes: Bytes): void\n  pushUint8(value: number): void\n  pushUint16(value: number): void\n  pushUint24(value: number): void\n  pushUint32(value: number): void\n  readByte(): Bytes[number]\n  readBytes(length: number, size?: number): Bytes\n  readUint8(): number\n  readUint16(): number\n  readUint24(): number\n  readUint32(): number\n  setPosition(position: number): () => void\n  _touch(): void\n}\n\nconst staticCursor: Cursor = /*#__PURE__*/ {\n  bytes: new Uint8Array(),\n  dataView: new DataView(new ArrayBuffer(0)),\n  position: 0,\n  positionReadCount: new Map(),\n  recursiveReadCount: 0,\n  recursiveReadLimit: Number.POSITIVE_INFINITY,\n  assertReadLimit() {\n    if (this.recursiveReadCount >= this.recursiveReadLimit)\n      throw new RecursiveReadLimitExceededError({\n        count: this.recursiveReadCount + 1,\n        limit: this.recursiveReadLimit,\n      })\n  },\n  assertPosition(position) {\n    if (position < 0 || position > this.bytes.length - 1)\n      throw new PositionOutOfBoundsError({\n        length: this.bytes.length,\n        position,\n      })\n  },\n  decrementPosition(offset) {\n    if (offset < 0) throw new NegativeOffsetError({ offset })\n    const position = this.position - offset\n    this.assertPosition(position)\n    this.position = position\n  },\n  getReadCount(position) {\n    return this.positionReadCount.get(position || this.position) || 0\n  },\n  incrementPosition(offset) {\n    if (offset < 0) throw new NegativeOffsetError({ offset })\n    const position = this.position + offset\n    this.assertPosition(position)\n    this.position = position\n  },\n  inspectByte(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position)\n    return this.bytes[position]!\n  },\n  inspectBytes(length, position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + length - 1)\n    return this.bytes.subarray(position, position + length)\n  },\n  inspectUint8(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position)\n    return this.bytes[position]!\n  },\n  inspectUint16(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 1)\n    return this.dataView.getUint16(position)\n  },\n  inspectUint24(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 2)\n    return (\n      (this.dataView.getUint16(position) << 8) +\n      this.dataView.getUint8(position + 2)\n    )\n  },\n  inspectUint32(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 3)\n    return this.dataView.getUint32(position)\n  },\n  pushByte(byte: Bytes[number]) {\n    this.assertPosition(this.position)\n    this.bytes[this.position] = byte\n    this.position++\n  },\n  pushBytes(bytes: Bytes) {\n    this.assertPosition(this.position + bytes.length - 1)\n    this.bytes.set(bytes, this.position)\n    this.position += bytes.length\n  },\n  pushUint8(value: number) {\n    this.assertPosition(this.position)\n    this.bytes[this.position] = value\n    this.position++\n  },\n  pushUint16(value: number) {\n    this.assertPosition(this.position + 1)\n    this.dataView.setUint16(this.position, value)\n    this.position += 2\n  },\n  pushUint24(value: number) {\n    this.assertPosition(this.position + 2)\n    this.dataView.setUint16(this.position, value >> 8)\n    this.dataView.setUint8(this.position + 2, value & ~4294967040)\n    this.position += 3\n  },\n  pushUint32(value: number) {\n    this.assertPosition(this.position + 3)\n    this.dataView.setUint32(this.position, value)\n    this.position += 4\n  },\n  readByte() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectByte()\n    this.position++\n    return value\n  },\n  readBytes(length, size) {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectBytes(length)\n    this.position += size ?? length\n    return value\n  },\n  readUint8() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint8()\n    this.position += 1\n    return value\n  },\n  readUint16() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint16()\n    this.position += 2\n    return value\n  },\n  readUint24() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint24()\n    this.position += 3\n    return value\n  },\n  readUint32() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint32()\n    this.position += 4\n    return value\n  },\n  get remaining() {\n    return this.bytes.length - this.position\n  },\n  setPosition(position) {\n    const oldPosition = this.position\n    this.assertPosition(position)\n    this.position = position\n    return () => (this.position = oldPosition)\n  },\n  _touch() {\n    if (this.recursiveReadLimit === Number.POSITIVE_INFINITY) return\n    const count = this.getReadCount()\n    this.positionReadCount.set(this.position, count + 1)\n    if (count > 0) this.recursiveReadCount++\n  },\n}\n\n/** @internal */\nexport function create(\n  bytes: Bytes,\n  { recursiveReadLimit = 8_192 }: create.Config = {},\n): Cursor {\n  const cursor: Cursor = Object.create(staticCursor)\n  cursor.bytes = bytes\n  cursor.dataView = new DataView(\n    bytes.buffer,\n    bytes.byteOffset,\n    bytes.byteLength,\n  )\n  cursor.positionReadCount = new Map()\n  cursor.recursiveReadLimit = recursiveReadLimit\n  return cursor\n}\n\n/** @internal */\nexport declare namespace create {\n  type Config = { recursiveReadLimit?: number | undefined }\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/** @internal */\nexport class NegativeOffsetError extends Errors.BaseError {\n  override readonly name = 'Cursor.NegativeOffsetError'\n\n  constructor({ offset }: { offset: number }) {\n    super(`Offset \\`${offset}\\` cannot be negative.`)\n  }\n}\n\n/** @internal */\nexport class PositionOutOfBoundsError extends Errors.BaseError {\n  override readonly name = 'Cursor.PositionOutOfBoundsError'\n\n  constructor({ length, position }: { length: number; position: number }) {\n    super(\n      `Position \\`${position}\\` is out of bounds (\\`0 < position < ${length}\\`).`,\n    )\n  }\n}\n\n/** @internal */\nexport class RecursiveReadLimitExceededError extends Errors.BaseError {\n  override readonly name = 'Cursor.RecursiveReadLimitExceededError'\n\n  constructor({ count, limit }: { count: number; limit: number }) {\n    super(\n      `Recursive read limit of \\`${limit}\\` exceeded (recursive read count: \\`${count}\\`).`,\n    )\n  }\n}\n", "import * as abitype from 'abitype'\nimport * as Address from './Address.js'\nimport * as Bytes from './Bytes.js'\nimport * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\nimport * as Solidity from './Solidity.js'\nimport * as internal from './internal/abiParameters.js'\nimport * as Cursor from './internal/cursor.js'\n\n/** Root type for ABI parameters. */\nexport type AbiParameters = readonly abitype.AbiParameter[]\n\n/** A parameter on an {@link ox#AbiParameters.AbiParameters}. */\nexport type Parameter = abitype.AbiParameter\n\n/** A packed ABI type. */\nexport type PackedAbiType =\n  | abitype.SolidityAddress\n  | abitype.SolidityBool\n  | abitype.SolidityBytes\n  | abitype.SolidityInt\n  | abitype.SolidityString\n  | abitype.SolidityArrayWithoutTuple\n\n/**\n * Decodes ABI-encoded data into its respective primitive values based on ABI Parameters.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.decode(\n *   AbiParameters.from(['string', 'uint', 'bool']),\n *   '0x000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000001a4000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000057761676d69000000000000000000000000000000000000000000000000000000',\n * )\n * // @log: ['wagmi', 420n, true]\n * ```\n *\n * @example\n * ### JSON Parameters\n *\n * You can pass **JSON ABI** Parameters:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.decode(\n *   [\n *     { name: 'x', type: 'string' },\n *     { name: 'y', type: 'uint' },\n *     { name: 'z', type: 'bool' },\n *   ],\n *   '0x000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000001a4000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000057761676d69000000000000000000000000000000000000000000000000000000',\n * )\n * // @log: ['wagmi', 420n, true]\n * ```\n *\n * @param parameters - The set of ABI parameters to decode, in the shape of the `inputs` or `outputs` attribute of an ABI Item. These parameters must include valid [ABI types](https://docs.soliditylang.org/en/latest/types.html).\n * @param data - ABI encoded data.\n * @param options - Decoding options.\n * @returns Array of decoded values.\n */\nexport function decode<\n  const parameters extends AbiParameters,\n  as extends 'Object' | 'Array' = 'Array',\n>(\n  parameters: parameters,\n  data: Bytes.Bytes | Hex.Hex,\n  options?: decode.Options<as>,\n): decode.ReturnType<parameters, as>\n\n// eslint-disable-next-line jsdoc/require-jsdoc\nexport function decode(\n  parameters: AbiParameters,\n  data: Bytes.Bytes | Hex.Hex,\n  options: {\n    as?: 'Array' | 'Object' | undefined\n    checksumAddress?: boolean | undefined\n  } = {},\n): readonly unknown[] | Record<string, unknown> {\n  const { as = 'Array', checksumAddress = false } = options\n\n  const bytes = typeof data === 'string' ? Bytes.fromHex(data) : data\n  const cursor = Cursor.create(bytes)\n\n  if (Bytes.size(bytes) === 0 && parameters.length > 0)\n    throw new ZeroDataError()\n  if (Bytes.size(bytes) && Bytes.size(bytes) < 32)\n    throw new DataSizeTooSmallError({\n      data: typeof data === 'string' ? data : Hex.fromBytes(data),\n      parameters: parameters as readonly Parameter[],\n      size: Bytes.size(bytes),\n    })\n\n  let consumed = 0\n  const values: any = as === 'Array' ? [] : {}\n  for (let i = 0; i < parameters.length; ++i) {\n    const param = parameters[i] as Parameter\n    cursor.setPosition(consumed)\n    const [data, consumed_] = internal.decodeParameter(cursor, param, {\n      checksumAddress,\n      staticPosition: 0,\n    })\n    consumed += consumed_\n    if (as === 'Array') values.push(data)\n    else values[param.name ?? i] = data\n  }\n  return values\n}\n\nexport declare namespace decode {\n  type Options<as extends 'Object' | 'Array'> = {\n    /**\n     * Whether the decoded values should be returned as an `Object` or `Array`.\n     *\n     * @default \"Array\"\n     */\n    as?: as | 'Object' | 'Array' | undefined\n    /**\n     * Whether decoded addresses should be checksummed.\n     *\n     * @default false\n     */\n    checksumAddress?: boolean | undefined\n  }\n\n  type ReturnType<\n    parameters extends AbiParameters = AbiParameters,\n    as extends 'Object' | 'Array' = 'Array',\n  > = parameters extends readonly []\n    ? as extends 'Object'\n      ? {}\n      : []\n    : as extends 'Object'\n      ? internal.ToObject<parameters>\n      : internal.ToPrimitiveTypes<parameters>\n\n  type ErrorType =\n    | Bytes.fromHex.ErrorType\n    | internal.decodeParameter.ErrorType\n    | ZeroDataError\n    | DataSizeTooSmallError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes primitive values into ABI encoded data as per the [Application Binary Interface (ABI) Specification](https://docs.soliditylang.org/en/latest/abi-spec).\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.encode(\n *   AbiParameters.from(['string', 'uint', 'bool']),\n *   ['wagmi', 420n, true],\n * )\n * ```\n *\n * @example\n * ### JSON Parameters\n *\n * Specify **JSON ABI** Parameters as schema:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.encode(\n *   [\n *     { type: 'string', name: 'name' },\n *     { type: 'uint', name: 'age' },\n *     { type: 'bool', name: 'isOwner' },\n *   ],\n *   ['wagmi', 420n, true],\n * )\n * ```\n *\n * @param parameters - The set of ABI parameters to encode, in the shape of the `inputs` or `outputs` attribute of an ABI Item. These parameters must include valid [ABI types](https://docs.soliditylang.org/en/latest/types.html).\n * @param values - The set of primitive values that correspond to the ABI types defined in `parameters`.\n * @returns ABI encoded data.\n */\nexport function encode<\n  const parameters extends AbiParameters | readonly unknown[],\n>(\n  parameters: parameters,\n  values: parameters extends AbiParameters\n    ? internal.ToPrimitiveTypes<parameters>\n    : never,\n  options?: encode.Options,\n): Hex.Hex {\n  const { checksumAddress = false } = options ?? {}\n\n  if (parameters.length !== values.length)\n    throw new LengthMismatchError({\n      expectedLength: parameters.length as number,\n      givenLength: values.length as any,\n    })\n  // Prepare the parameters to determine dynamic types to encode.\n  const preparedParameters = internal.prepareParameters({\n    checksumAddress,\n    parameters: parameters as readonly Parameter[],\n    values: values as any,\n  })\n  const data = internal.encode(preparedParameters)\n  if (data.length === 0) return '0x'\n  return data\n}\n\nexport declare namespace encode {\n  type ErrorType =\n    | LengthMismatchError\n    | internal.encode.ErrorType\n    | internal.prepareParameters.ErrorType\n    | Errors.GlobalErrorType\n\n  type Options = {\n    /**\n     * Whether addresses should be checked against their checksum.\n     *\n     * @default false\n     */\n    checksumAddress?: boolean | undefined\n  }\n}\n\n/**\n * Encodes an array of primitive values to a [packed ABI encoding](https://docs.soliditylang.org/en/latest/abi-spec.html#non-standard-packed-mode).\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const encoded = AbiParameters.encodePacked(\n *   ['address', 'string'],\n *   ['******************************************', 'hello world'],\n * )\n * // @log: '******************************************68656c6c6f20776f726c64'\n * ```\n *\n * @param types - Set of ABI types to pack encode.\n * @param values - The set of primitive values that correspond to the ABI types defined in `types`.\n * @returns The encoded packed data.\n */\nexport function encodePacked<\n  const packedAbiTypes extends readonly PackedAbiType[] | readonly unknown[],\n>(types: packedAbiTypes, values: encodePacked.Values<packedAbiTypes>): Hex.Hex {\n  if (types.length !== values.length)\n    throw new LengthMismatchError({\n      expectedLength: types.length as number,\n      givenLength: values.length as number,\n    })\n\n  const data: Hex.Hex[] = []\n  for (let i = 0; i < (types as unknown[]).length; i++) {\n    const type = types[i]\n    const value = values[i]\n    data.push(encodePacked.encode(type, value))\n  }\n  return Hex.concat(...data)\n}\n\nexport namespace encodePacked {\n  export type ErrorType =\n    | Hex.concat.ErrorType\n    | LengthMismatchError\n    | Errors.GlobalErrorType\n\n  export type Values<\n    packedAbiTypes extends readonly PackedAbiType[] | readonly unknown[],\n  > = {\n    [key in keyof packedAbiTypes]: packedAbiTypes[key] extends abitype.AbiType\n      ? abitype.AbiParameterToPrimitiveType<{ type: packedAbiTypes[key] }>\n      : unknown\n  }\n\n  // eslint-disable-next-line jsdoc/require-jsdoc\n  export function encode<const packedAbiType extends PackedAbiType | unknown>(\n    type: packedAbiType,\n    value: Values<[packedAbiType]>[0],\n    isArray = false,\n  ): Hex.Hex {\n    if (type === 'address') {\n      const address = value as Address.Address\n      Address.assert(address)\n      return Hex.padLeft(\n        address.toLowerCase() as Hex.Hex,\n        isArray ? 32 : 0,\n      ) as Address.Address\n    }\n    if (type === 'string') return Hex.fromString(value as string)\n    if (type === 'bytes') return value as Hex.Hex\n    if (type === 'bool')\n      return Hex.padLeft(Hex.fromBoolean(value as boolean), isArray ? 32 : 1)\n\n    const intMatch = (type as string).match(Solidity.integerRegex)\n    if (intMatch) {\n      const [_type, baseType, bits = '256'] = intMatch\n      const size = Number.parseInt(bits) / 8\n      return Hex.fromNumber(value as number, {\n        size: isArray ? 32 : size,\n        signed: baseType === 'int',\n      })\n    }\n\n    const bytesMatch = (type as string).match(Solidity.bytesRegex)\n    if (bytesMatch) {\n      const [_type, size] = bytesMatch\n      if (Number.parseInt(size!) !== ((value as Hex.Hex).length - 2) / 2)\n        throw new BytesSizeMismatchError({\n          expectedSize: Number.parseInt(size!),\n          value: value as Hex.Hex,\n        })\n      return Hex.padRight(value as Hex.Hex, isArray ? 32 : 0) as Hex.Hex\n    }\n\n    const arrayMatch = (type as string).match(Solidity.arrayRegex)\n    if (arrayMatch && Array.isArray(value)) {\n      const [_type, childType] = arrayMatch\n      const data: Hex.Hex[] = []\n      for (let i = 0; i < value.length; i++) {\n        data.push(encode(childType, value[i], true))\n      }\n      if (data.length === 0) return '0x'\n      return Hex.concat(...data)\n    }\n\n    throw new InvalidTypeError(type as string)\n  }\n}\n\n/**\n * Formats {@link ox#AbiParameters.AbiParameters} into **Human Readable ABI Parameters**.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const formatted = AbiParameters.format([\n *   {\n *     name: 'spender',\n *     type: 'address',\n *   },\n *   {\n *     name: 'amount',\n *     type: 'uint256',\n *   },\n * ])\n *\n * formatted\n * //    ^?\n *\n *\n * ```\n *\n * @param parameters - The ABI Parameters to format.\n * @returns The formatted ABI Parameters  .\n */\nexport function format<\n  const parameters extends readonly [\n    Parameter | abitype.AbiEventParameter,\n    ...(readonly (Parameter | abitype.AbiEventParameter)[]),\n  ],\n>(\n  parameters:\n    | parameters\n    | readonly [\n        Parameter | abitype.AbiEventParameter,\n        ...(readonly (Parameter | abitype.AbiEventParameter)[]),\n      ],\n): abitype.FormatAbiParameters<parameters> {\n  return abitype.formatAbiParameters(parameters)\n}\n\nexport declare namespace format {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Parses arbitrary **JSON ABI Parameters** or **Human Readable ABI Parameters** into typed {@link ox#AbiParameters.AbiParameters}.\n *\n * @example\n * ### JSON Parameters\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const parameters = AbiParameters.from([\n *   {\n *     name: 'spender',\n *     type: 'address',\n *   },\n *   {\n *     name: 'amount',\n *     type: 'uint256',\n *   },\n * ])\n *\n * parameters\n * //^?\n *\n *\n *\n *\n *\n *\n *\n * ```\n *\n * @example\n * ### Human Readable Parameters\n *\n * Human Readable ABI Parameters can be parsed into a typed {@link ox#AbiParameters.AbiParameters}:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const parameters = AbiParameters.from('address spender, uint256 amount')\n *\n * parameters\n * //^?\n *\n *\n *\n *\n *\n *\n *\n * ```\n *\n * @example\n * It is possible to specify `struct`s along with your definitions:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const parameters = AbiParameters.from([\n *   'struct Foo { address spender; uint256 amount; }', // [!code hl]\n *   'Foo foo, address bar',\n * ])\n *\n * parameters\n * //^?\n *\n *\n *\n *\n *\n *\n *\n *\n *\n *\n *\n *\n * ```\n *\n *\n *\n * @param parameters - The ABI Parameters to parse.\n * @returns The typed ABI Parameters.\n */\nexport function from<\n  const parameters extends AbiParameters | string | readonly string[],\n>(\n  parameters: parameters | AbiParameters | string | readonly string[],\n): from.ReturnType<parameters> {\n  if (Array.isArray(parameters) && typeof parameters[0] === 'string')\n    return abitype.parseAbiParameters(parameters) as never\n  if (typeof parameters === 'string')\n    return abitype.parseAbiParameters(parameters) as never\n  return parameters as never\n}\n\nexport declare namespace from {\n  type ReturnType<\n    parameters extends AbiParameters | string | readonly string[],\n  > = parameters extends string\n    ? abitype.ParseAbiParameters<parameters>\n    : parameters extends readonly string[]\n      ? abitype.ParseAbiParameters<parameters>\n      : parameters\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Throws when the data size is too small for the given parameters.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x010f')\n * //                                             ↑ ❌ 2 bytes\n * // @error: AbiParameters.DataSizeTooSmallError: Data size of 2 bytes is too small for given parameters.\n * // @error: Params: (uint256)\n * // @error: Data:   0x010f (2 bytes)\n * ```\n *\n * ### Solution\n *\n * Pass a valid data size.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x00000000000000000000000000000000000000000000000000000000000010f')\n * //                                             ↑ ✅ 32 bytes\n * ```\n */\nexport class DataSizeTooSmallError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.DataSizeTooSmallError'\n  constructor({\n    data,\n    parameters,\n    size,\n  }: { data: Hex.Hex; parameters: readonly Parameter[]; size: number }) {\n    super(`Data size of ${size} bytes is too small for given parameters.`, {\n      metaMessages: [\n        `Params: (${abitype.formatAbiParameters(parameters as readonly [Parameter])})`,\n        `Data:   ${data} (${size} bytes)`,\n      ],\n    })\n  }\n}\n\n/**\n * Throws when zero data is provided, but data is expected.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x')\n * //                                           ↑ ❌ zero data\n * // @error: AbiParameters.DataSizeTooSmallError: Data size of 2 bytes is too small for given parameters.\n * // @error: Params: (uint256)\n * // @error: Data:   0x010f (2 bytes)\n * ```\n *\n * ### Solution\n *\n * Pass valid data.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x00000000000000000000000000000000000000000000000000000000000010f')\n * //                                             ↑ ✅ 32 bytes\n * ```\n */\nexport class ZeroDataError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.ZeroDataError'\n  constructor() {\n    super('Cannot decode zero data (\"0x\") with ABI parameters.')\n  }\n}\n\n/**\n * The length of the array value does not match the length specified in the corresponding ABI parameter.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from('uint256[3]'), [[69n, 420n]])\n * //                                               ↑ expected: 3  ↑ ❌ length: 2\n * // @error: AbiParameters.ArrayLengthMismatchError: ABI encoding array length mismatch\n * // @error: for type `uint256[3]`. Expected: `3`. Given: `2`.\n * ```\n *\n * ### Solution\n *\n * Pass an array of the correct length.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['uint256[3]']), [[69n, 420n, 69n]])\n * //                                                         ↑ ✅ length: 3\n * ```\n */\nexport class ArrayLengthMismatchError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.ArrayLengthMismatchError'\n  constructor({\n    expectedLength,\n    givenLength,\n    type,\n  }: { expectedLength: number; givenLength: number; type: string }) {\n    super(\n      `Array length mismatch for type \\`${type}\\`. Expected: \\`${expectedLength}\\`. Given: \\`${givenLength}\\`.`,\n    )\n  }\n}\n\n/**\n * The size of the bytes value does not match the size specified in the corresponding ABI parameter.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from('bytes8'), [['0xdeadbeefdeadbeefdeadbeef']])\n * //                                            ↑ expected: 8 bytes  ↑ ❌ size: 12 bytes\n * // @error: BytesSizeMismatchError: Size of bytes \"0xdeadbeefdeadbeefdeadbeef\"\n * // @error: (bytes12) does not match expected size (bytes8).\n * ```\n *\n * ### Solution\n *\n * Pass a bytes value of the correct size.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['bytes8']), ['0xdeadbeefdeadbeef'])\n * //                                                       ↑ ✅ size: 8 bytes\n * ```\n */\nexport class BytesSizeMismatchError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.BytesSizeMismatchError'\n  constructor({\n    expectedSize,\n    value,\n  }: { expectedSize: number; value: Hex.Hex }) {\n    super(\n      `Size of bytes \"${value}\" (bytes${Hex.size(\n        value,\n      )}) does not match expected size (bytes${expectedSize}).`,\n    )\n  }\n}\n\n/**\n * The length of the values to encode does not match the length of the ABI parameters.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['string', 'uint256']), ['hello'])\n * // @error: LengthMismatchError: ABI encoding params/values length mismatch.\n * // @error: Expected length (params): 2\n * // @error: Given length (values): 1\n * ```\n *\n * ### Solution\n *\n * Pass the correct number of values to encode.\n *\n * ### Solution\n *\n * Pass a [valid ABI type](https://docs.soliditylang.org/en/develop/abi-spec.html#types).\n */\nexport class LengthMismatchError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.LengthMismatchError'\n  constructor({\n    expectedLength,\n    givenLength,\n  }: { expectedLength: number; givenLength: number }) {\n    super(\n      [\n        'ABI encoding parameters/values length mismatch.',\n        `Expected length (parameters): ${expectedLength}`,\n        `Given length (values): ${givenLength}`,\n      ].join('\\n'),\n    )\n  }\n}\n\n/**\n * The value provided is not a valid array as specified in the corresponding ABI parameter.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['uint256[3]']), [69])\n * ```\n *\n * ### Solution\n *\n * Pass an array value.\n */\nexport class InvalidArrayError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.InvalidArrayError'\n  constructor(value: unknown) {\n    super(`Value \\`${value}\\` is not a valid array.`)\n  }\n}\n\n/**\n * Throws when the ABI parameter type is invalid.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'lol' }], '0x00000000000000000000000000000000000000000000000000000000000010f')\n * //                             ↑ ❌ invalid type\n * // @error: AbiParameters.InvalidTypeError: Type `lol` is not a valid ABI Type.\n * ```\n */\nexport class InvalidTypeError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.InvalidTypeError'\n  constructor(type: string) {\n    super(`Type \\`${type}\\` is not a valid ABI Type.`)\n  }\n}\n", null, null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,IAAMA,iBAAgB;EAE3B,wBAAwB;EACxB,sBAAsB;EACtB,yBAAyB;EACzB,wBAAwB;EACxB,uBAAuB;EACvB,2BAA2B;EAC3B,yBAAyB;EACzB,uBAAuB;EACvB,yBAAyB;EACzB,uBAAuB;EACvB,wBAAwB;EAExB,QAAQ;EACR,kBAAkB;EAClB,wBAAwB;EACxB,uBAAuB;EACvB,oBAAoB;IAClB,gBAAgB;IAChB,mBAAmB;;EAErB,yBAAyB;EACzB,+BAA+B;EAC/B,yBAAyB;EACzB,0BAA0B;EAC1B,qBAAqB;EACrB,4BAA4B;EAC5B,yBAAyB;;;;ACzBpB,IAAM,cAAc;EACzB,sBAAsB;IACpB,CAAC,cAAoB,aAAa,QAAQ,GACxC;IACF,CAAC,cAAoB,aAAa,YAAY,GAC5C;IACF,CAAC,cAAoB,aAAa,IAAI,GACpC;IACF,CAAC,cAAoB,aAAa,MAAM,GACtC;IACF,CAAC,cAAoB,aAAa,GAAG,GACnC;IAGF,CAACC,eAAc,sBAAsB,GACnC;IACF,CAACA,eAAc,oBAAoB,GACjC;IACF,CAACA,eAAc,uBAAuB,GACpC;IACF,CAACA,eAAc,sBAAsB,GACnC;IACF,CAACA,eAAc,qBAAqB,GAClC;IACF,CAACA,eAAc,yBAAyB,GACtC;IACF,CAACA,eAAc,uBAAuB,GACpC;IACF,CAACA,eAAc,qBAAqB,GAClC;IACF,CAACA,eAAc,uBAAuB,GACpC;IACF,CAACA,eAAc,qBAAqB,GAClC;IACF,CAACA,eAAc,sBAAsB,GACnC;;EAEJ,iBAAiB;IAEf,GAAG;IAEH,OAAO;IAEP,OAAO;IAEP,IAAI;IAEJ,KAAK;IAEL,IAAI;IAEJ,KAAK;IAEL,KAAM;IAEN,KAAK;IAEL,UAAY;IAEZ,OAAO;IAEP,MAAM;IAEN,KAAK;IAEL,OAAQ;IAER,KAAK;IAEL,MAAM;IAEN,KAAK;IAEL,KAAK;IAEL,MAAM;IAEN,MAAM;IAEN,MAAM;IAEN,MAAM;IAEN,SAAS;IAET,OAAO;IAEP,MAAM;IAEN,YAAY;IAEZ,MAAM;IAEN,MAAM;IAEN,OAAO;IAEP,oCAAoC;IACpC,oCAAoC;IACpC,kCAAkC;IAElC,oCAAoC;IAEpC,oCAAoC;;EAGtC,mBAAmB;IACjB,CAAC,cAAoB,aAAa,QAAQ,GAAG;IAC7C,CAAC,cAAoB,aAAa,YAAY,GAAG;IACjD,CAAC,cAAoB,aAAa,IAAI,GAAG;IACzC,CAAC,cAAoB,aAAa,MAAM,GAAG;IAC3C,CAAC,cAAoB,aAAa,cAAc,GAAG;IACnD,CAAC,cAAoB,aAAa,QAAQ,GAAG;;EAG/C,mBAAmB;IACjB,CAAC,cAAoB,aAAa,QAAQ,GAAG;IAC7C,CAAC,cAAoB,aAAa,cAAc,GAAG;IACnD,CAAC,cAAoB,aAAa,QAAQ,GAAG;IAC7C,CAAC,cAAoB,aAAa,YAAY,GAAG;IACjD,CAAC,cAAoB,aAAa,MAAM,GAAG;IAC3C,CAAC,cAAoB,aAAa,IAAI,GAAG;;EAG3C,mBAAmB;IACjB,CAAC,cAAoB,aAAa,QAAQ,GAAG;IAC7C,CAAC,cAAoB,aAAa,cAAc,GAAG;IACnD,CAAC,cAAoB,aAAa,OAAO,GAAG;IAC5C,CAAC,cAAoB,aAAa,IAAI,GAAG;;EAG3C,0BAA0B;IAExB;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;;;;;ACjLG,IAAM,cAAc;EACzB,cAAc,QAAe;AAC3B,QAAI,CAAC,QAAQ;AACX,aAAO;IACT;AAEA,UAAM,aAAqB,CAAA;AAC3B,WAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,IAAI,KAAK,MAAK;AAC7C,iBAAW,GAAGC,eAAc,MAAM,IAAI,EAAE,EAAmB,IAAI;IACjE,CAAC;AAED,WAAO;EACT;EAEA,iBAAiB,MAAe,MAAa;AAC3C,YAAO,6BAAM,oBAAkB,6BAAM;EACvC;;;;ACrBK,IAAM,YAAY;EACvB,yBAAyB;IACvB,iCAAiC;MAC/B,SAAS;MACT,eAAe;;IAEjB,sBAAsB;MACpB,SAAS;MACT,eAAe;;IAEjB,aAAa;MACX,SAAS;MACT,eAAe;;;EAGnB,cAAc;IACZ,0BAA0B;MACxB,cAAc;MACd,aACE;;IAEJ,2BAA2B;MACzB,cAAc;MACd,aAAa,MACX,UACE,OAAM,IAAK,OAAO,SAAS,SAC7B;;IAEJ,iBAAiB;MACf,cAAc;MACd,aAAa,MACX;;IAEJ,qBAAqB;MACnB,cAAc;MACd,aACE;;IAEJ,oBAAoB;MAClB,cAAc;MACd,aAAa;;IAEf,2BAA2B;MACzB,cAAc;MACd,aAAa;;;;AAKnB,SAAS,SAAM;AACb,SAAO,OAAO,WAAW;AAC3B;;;ACjDO,IAAM,aAAa;EACxB,aAAa,SAAsD,QAAQ,SAAO;AAChF,UAAM,gBAAgB,EAAwB;MAC5C;KACD;AAED,UAAM,EAAE,OAAM,IAAK,EAAuB;MACxC,MAAM;KACP;AAED,WAAO,QAAQ,IAAI,SAAmB;AACpC,iBAAW,OAAO,MAAM;AACtB,YAAI,eAAe,OAAO;AACxB,kBAAQ,KAAK,GAAG,IAAI;AAEpB;QACF;MACF;AAEA,cAAQ,QAAW,GAAG,IAAI;IAC5B;AAEA,WAAO;EACT;;;;ACYK,IAAM,kBAAkB;EAC7B,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;;AAKH,SAAU,kBAAkB,aAAyC;AACzE,QAAM,eAAe;IACnB,GAAG;IACH,WAAW,YAAY,YAAY,YAAY,YAAY;IAC3D,aAAa,YAAY,cACrB,OAAO,YAAY,WAAW,IAC9B;IACJ,SAAS,YAAY,UAAU,YAAY,YAAY,OAAO,IAAI;IAClE,KAAK,YAAY,MAAM,OAAO,YAAY,GAAG,IAAI;IACjD,UAAU,YAAY,WAAW,OAAO,YAAY,QAAQ,IAAI;IAChE,kBAAkB,YAAY,mBAC1B,OAAO,YAAY,gBAAgB,IACnC;IACJ,cAAc,YAAY,eACtB,OAAO,YAAY,YAAY,IAC/B;IACJ,sBAAsB,YAAY,uBAC9B,OAAO,YAAY,oBAAoB,IACvC;IACJ,OAAO,YAAY,QAAQ,YAAY,YAAY,KAAK,IAAI;IAC5D,IAAI,YAAY,KAAK,YAAY,KAAK;IACtC,kBAAkB,YAAY,mBAC1B,OAAO,YAAY,gBAAgB,IACnC;IACJ,MAAM,YAAY,OACb,gBAAwB,YAAY,IAAI,IACzC;IACJ,SAAS,YAAY,OAAO,YAAY,OAAO;IAC/C,OAAO,YAAY,QAAQ,OAAO,YAAY,KAAK,IAAI;IACvD,GAAG,YAAY,IAAI,OAAO,YAAY,CAAC,IAAI;;AAG7C,MAAI,YAAY;AACd,iBAAa,oBAAoB,wBAC/B,YAAY,iBAAiB;AAGjC,eAAa,WAAW,MAAK;AAE3B,QAAI,YAAY;AAAS,aAAO,OAAO,YAAY,OAAO;AAG1D,QAAI,OAAO,aAAa,MAAM,UAAU;AACtC,UAAI,aAAa,MAAM,MAAM,aAAa,MAAM;AAAK,eAAO;AAC5D,UAAI,aAAa,MAAM,MAAM,aAAa,MAAM;AAAK,eAAO;AAC5D,UAAI,aAAa,KAAK;AAAK,eAAO,aAAa,IAAI,OAAO,KAAK,IAAI;IACrE;AAEA,WAAO;EACT,GAAE;AAEF,MAAI,aAAa,SAAS,UAAU;AAClC,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;EACtB;AACA,MAAI,aAAa,SAAS,WAAW;AACnC,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;EACtB;AACA,MAAI,aAAa,SAAS,WAAW;AACnC,WAAO,aAAa;EACtB;AACA,SAAO;AACT;AAIO,IAAM,oBAAkC,gBAC7C,eACA,iBAAiB;AAKnB,SAAS,wBACP,mBAAuC;AAEvC,SAAO,kBAAkB,IAAI,CAAC,mBAAmB;IAC/C,SAAU,cAAsB;IAChC,SAAS,OAAO,cAAc,OAAO;IACrC,OAAO,OAAO,cAAc,KAAK;IACjC,GAAG,cAAc;IACjB,GAAG,cAAc;IACjB,SAAS,OAAO,cAAc,OAAO;IACrC;AACJ;;;AC9FM,SAAU,YAAY,OAA6B;AACvD,QAAM,gBAAgB,MAAM,gBAAgB,CAAA,GAAI,IAAI,CAAC,gBAAe;AAClE,QAAI,OAAO,gBAAgB;AAAU,aAAO;AAC5C,WAAO,kBAAkB,WAAW;EACtC,CAAC;AACD,SAAO;IACL,GAAG;IACH,eAAe,MAAM,gBAAgB,OAAO,MAAM,aAAa,IAAI;IACnE,aAAa,MAAM,cAAc,OAAO,MAAM,WAAW,IAAI;IAC7D,YAAY,MAAM,aAAa,OAAO,MAAM,UAAU,IAAI;IAC1D,eAAe,MAAM,gBACjB,OAAO,MAAM,aAAa,IAC1B;IACJ,UAAU,MAAM,WAAW,OAAO,MAAM,QAAQ,IAAI;IACpD,SAAS,MAAM,UAAU,OAAO,MAAM,OAAO,IAAI;IACjD,MAAM,MAAM,OAAO,MAAM,OAAO;IAChC,WAAW,MAAM,YAAY,MAAM,YAAY;IAC/C,OAAO,MAAM,QAAQ,MAAM,QAAQ;IACnC,QAAQ,MAAM,SAAS,OAAO,MAAM,MAAM,IAAI;IAC9C,MAAM,MAAM,OAAO,OAAO,MAAM,IAAI,IAAI;IACxC,WAAW,MAAM,YAAY,OAAO,MAAM,SAAS,IAAI;IACvD;IACA,iBAAiB,MAAM,kBACnB,OAAO,MAAM,eAAe,IAC5B;;AAER;AAIO,IAAM,cAA4B,gBAAgB,SAAS,WAAW;;;ACR7E,eAAsB,oBAIpB,QACA,EAAE,SAAS,WAAW,UAAU,YAAW,GAAiC;AAE5E,QAAM,QAAQ,MAAM,OAAO,QACzB;IACE,QAAQ;IACR,QAAQ;MACN;MACA,OAAO,gBAAgB,WAAW,YAAY,WAAW,IAAI;;KAGjE;IACE,QAAQ,QAAQ,WAAW;GAC5B;AAEH,SAAO,YAAY,KAAK;AAC1B;;;ACjFA,IAAM,sBAAsB;AAGrB,IAAM,uBAAuB;AAG7B,IAAM,uBAAuB;AAG7B,IAAM,eAAe,uBAAuB;AAG5C,IAAM,yBACX,eAAe;AAEf;AAEA,IAAI,uBAAuB;;;ACbvB,SAAU,UACd,KACA,EACE,MACA,UAAS,IACyD,CAAA,GAAE;AAEtE,SAAO;IACL,GAAG;IACH,WAAW,IAAI,YAAY,IAAI,YAAY;IAC3C,aAAa,IAAI,cAAc,OAAO,IAAI,WAAW,IAAI;IACzD,UAAU,IAAI,WAAW,OAAO,IAAI,QAAQ,IAAI;IAChD,iBAAiB,IAAI,kBAAkB,IAAI,kBAAkB;IAC7D,kBAAkB,IAAI,mBAClB,OAAO,IAAI,gBAAgB,IAC3B;IACJ,GAAI,YAAY,EAAE,MAAM,UAAS,IAAK,CAAA;;AAE1C;;;ACzBA,eAAsB,KAAK,MAAY;AACrC,SAAO,IAAI,QAAQ,CAAC,QAAQ,WAAW,KAAK,IAAI,CAAC;AACnD;;;AC8DA,IAAM,0BAA0B,IAAI,OAAgB,GAAG;;;AC1ChD,IAAM,kBAAkB;EAC7B,OAAO;EACP,OAAO;;AAKH,SAAU,yBACd,oBAAuD;AAEvD,QAAM,UAAU;IACd,GAAG;IACH,aAAa,mBAAmB,cAC5B,OAAO,mBAAmB,WAAW,IACrC;IACJ,iBAAiB,mBAAmB,kBAChC,mBAAmB,kBACnB;IACJ,mBAAmB,mBAAmB,oBAClC,OAAO,mBAAmB,iBAAiB,IAC3C;IACJ,mBAAmB,mBAAmB,oBAClC,OAAO,mBAAmB,iBAAiB,IAC3C;IACJ,SAAS,mBAAmB,UACxB,OAAO,mBAAmB,OAAO,IACjC;IACJ,MAAM,mBAAmB,OACrB,mBAAmB,KAAK,IAAI,CAAC,QAAQ,UAAU,GAAG,CAAC,IACnD;IACJ,IAAI,mBAAmB,KAAK,mBAAmB,KAAK;IACpD,kBAAkB,mBAAmB,mBACjC,YAAY,mBAAmB,gBAAgB,IAC/C;IACJ,QAAQ,mBAAmB,SACvB,gBAAgB,mBAAmB,MAAM,IACzC;IACJ,MAAM,mBAAmB,OACrB,gBACE,mBAAmB,IAAoC,KACpD,mBAAmB,OACxB;;AAGN,MAAI,mBAAmB;AACrB,YAAQ,eAAe,OAAO,mBAAmB,YAAY;AAC/D,MAAI,mBAAmB;AACrB,YAAQ,cAAc,OAAO,mBAAmB,WAAW;AAE7D,SAAO;AACT;AAMO,IAAM,2BAAyC,gBACpD,sBACA,wBAAwB;;;AChF1B,IAAMC,QAAO;AACb,IAAI,QAAQA;AACZ,IAAI;AAEE,SAAU,IAAI,SAAS,IAAE;AAC7B,MAAI,CAAC,UAAU,QAAQ,SAASA,QAAO,GAAG;AACxC,aAAS;AACT,YAAQ;AACR,aAAS,IAAI,GAAG,IAAIA,OAAM,KAAK;AAC7B,iBAAY,MAAM,KAAK,OAAM,IAAK,MAAO,GAAG,SAAS,EAAE,EAAE,UAAU,CAAC;IACtE;EACF;AACA,SAAO,OAAO,UAAU,OAAO,UAAU,MAAM;AACjD;;;ACVO,IAAM,eAA6B,IAAI,OAAqB,IAAI;AAQjE,SAAU,WACd,IACA,EAAE,UAAU,MAAM,GAAE,GAAqB;AAEzC,MAAI,CAAC,WAAW,CAAC;AAAI,WAAO,GAAE;AAC9B,MAAI,aAAa,IAAI,EAAE;AAAG,WAAO,aAAa,IAAI,EAAE;AACpD,QAAM,UAAU,GAAE,EAAG,QAAQ,MAAM,aAAa,OAAO,EAAE,CAAC;AAC1D,eAAa,IAAI,IAAI,OAAO;AAC5B,SAAO;AACT;;;ACKM,SAAU,UACd,IACA,EACE,OAAO,SAAS,KAChB,aAAa,GACb,aAAAC,eAAc,MAAM,KAAI,IACD,CAAA,GAAE;AAE3B,SAAO,IAAI,QAAc,CAAC,SAAS,WAAU;AAC3C,UAAM,eAAe,OAAO,EAAE,QAAQ,EAAC,IAAK,CAAA,MAAM;AAChD,YAAM,QAAQ,OAAO,EAAE,MAAK,MAAwB;AAClD,cAAM,QACJ,OAAO,WAAW,aAAa,OAAO,EAAE,OAAO,MAAK,CAAE,IAAI;AAC5D,YAAI;AAAO,gBAAM,KAAK,KAAK;AAC3B,qBAAa,EAAE,OAAO,QAAQ,EAAC,CAAE;MACnC;AAEA,UAAI;AACF,cAAM,OAAO,MAAM,GAAE;AACrB,gBAAQ,IAAI;MACd,SAAS,KAAK;AACZ,YACE,QAAQ,cACP,MAAMA,aAAY,EAAE,OAAO,OAAO,IAAY,CAAE;AAEjD,iBAAO,MAAM,EAAE,OAAO,IAAY,CAAE;AACtC,eAAO,GAAG;MACZ;IACF;AACA,iBAAY;EACd,CAAC;AACH;;;AC2DM,SAAU,aACd,SACA,UAAiC,CAAA,GAAE;AAEnC,SAAO,OAAO,MAAM,kBAAkB,CAAA,MAAM;AAvH9C;AAwHI,UAAM,EACJ,SAAS,OACT,SACA,aAAa,KACb,aAAa,GACb,KAAAC,KAAG,IACD;MACF,GAAG;MACH,GAAG;;AAGL,UAAM,EAAE,OAAM,IAAK;AACnB,SAAI,wCAAS,YAAT,mBAAkB,SAAS;AAC7B,YAAM,IAAI,2BAA2B,IAAI,MAAM,sBAAsB,GAAG;QACtE;OACD;AACH,SAAI,mCAAS,YAAW,CAAC,QAAQ,QAAQ,SAAS,MAAM;AACtD,YAAM,IAAI,2BAA2B,IAAI,MAAM,sBAAsB,GAAG;QACtE;OACD;AAEH,UAAM,YAAY,SACd,YAAY,GAAGA,IAAG,IAAI,UAAU,IAAI,CAAC,EAAE,IACvC;AACJ,WAAO,WACL,MACE,UACE,YAAW;AACT,UAAI;AACF,eAAO,MAAM,QAAQ,IAAI;MAC3B,SAAS,MAAM;AACb,cAAM,MAAM;AAGZ,gBAAQ,IAAI,MAAM;;UAEhB,KAAK,cAAc;AACjB,kBAAM,IAAI,cAAc,GAAG;;UAE7B,KAAK,uBAAuB;AAC1B,kBAAM,IAAI,uBAAuB,GAAG;;UAEtC,KAAK,uBAAuB;AAC1B,kBAAM,IAAI,uBAAuB,KAAK,EAAE,QAAQ,KAAK,OAAM,CAAE;;UAE/D,KAAK,sBAAsB;AACzB,kBAAM,IAAI,sBAAsB,GAAG;;UAErC,KAAK,iBAAiB;AACpB,kBAAM,IAAI,iBAAiB,GAAG;;UAEhC,KAAK,qBAAqB;AACxB,kBAAM,IAAI,qBAAqB,GAAG;;UAEpC,KAAK,yBAAyB;AAC5B,kBAAM,IAAI,yBAAyB,GAAG;;UAExC,KAAK,4BAA4B;AAC/B,kBAAM,IAAI,4BAA4B,GAAG;;UAE3C,KAAK,4BAA4B;AAC/B,kBAAM,IAAI,4BAA4B,GAAG;;UAE3C,KAAK,2BAA2B;AAC9B,kBAAM,IAAI,2BAA2B,KAAK;cACxC,QAAQ,KAAK;aACd;;UAEH,KAAK,sBAAsB;AACzB,kBAAM,IAAI,sBAAsB,GAAG;;UAErC,KAAK,+BAA+B;AAClC,kBAAM,IAAI,+BAA+B,GAAG;;UAG9C,KAAK,yBAAyB;AAC5B,kBAAM,IAAI,yBAAyB,GAAG;;UAExC,KAAK,0BAA0B;AAC7B,kBAAM,IAAI,0BAA0B,GAAG;;UAEzC,KAAK,+BAA+B;AAClC,kBAAM,IAAI,+BAA+B,GAAG;;UAE9C,KAAK,0BAA0B;AAC7B,kBAAM,IAAI,0BAA0B,GAAG;;UAEzC,KAAK,uBAAuB;AAC1B,kBAAM,IAAI,uBAAuB,GAAG;;UAEtC,KAAK,iBAAiB;AACpB,kBAAM,IAAI,iBAAiB,GAAG;;UAGhC,KAAK,sCAAsC;AACzC,kBAAM,IAAI,sCAAsC,GAAG;;UAErD,KAAK,wBAAwB;AAC3B,kBAAM,IAAI,wBAAwB,GAAG;;UAEvC,KAAK,iBAAiB;AACpB,kBAAM,IAAI,iBAAiB,GAAG;;UAEhC,KAAK,qBAAqB;AACxB,kBAAM,IAAI,qBAAqB,GAAG;;UAEpC,KAAK,oBAAoB;AACvB,kBAAM,IAAI,oBAAoB,GAAG;;UAEnC,KAAK,sCAAsC;AACzC,kBAAM,IAAI,sCAAsC,GAAG;;UAErD,KAAK,2BAA2B;AAC9B,kBAAM,IAAI,2BAA2B,GAAG;;;UAI1C,KAAK;AACH,kBAAM,IAAI,yBAAyB,GAAG;UAExC;AACE,gBAAI,gBAAgB;AAAW,oBAAM;AACrC,kBAAM,IAAI,gBAAgB,GAAY;QAC1C;MACF;IACF,GACA;MACE,OAAO,CAAC,EAAE,OAAO,MAAK,MAAM;AAvPxC,YAAAC;AAyPc,YAAI,SAAS,iBAAiB,kBAAkB;AAC9C,gBAAM,cAAaA,MAAA,+BAAO,YAAP,gBAAAA,IAAgB,IAAI;AACvC,cAAI,yCAAY,MAAM;AACpB,mBAAO,OAAO,SAAS,UAAU,IAAI;QACzC;AAGA,eAAO,CAAC,EAAE,KAAK,SAAS;MAC1B;MACA;MACA,aAAa,CAAC,EAAE,MAAK,MAAO,YAAY,KAAK;KAC9C,GAEL,EAAE,SAAS,QAAQ,IAAI,UAAS,CAAE;EAEtC;AACF;AAGM,SAAU,YAAY,OAAY;AACtC,MAAI,UAAU,SAAS,OAAO,MAAM,SAAS,UAAU;AACrD,QAAI,MAAM,SAAS;AAAI,aAAO;AAC9B,QAAI,MAAM,SAAS,sBAAsB;AAAM,aAAO;AACtD,QAAI,MAAM,SAAS,iBAAiB;AAAM,aAAO;AACjD,WAAO;EACT;AACA,MAAI,iBAAiB,oBAAoB,MAAM,QAAQ;AAErD,QAAI,MAAM,WAAW;AAAK,aAAO;AAEjC,QAAI,MAAM,WAAW;AAAK,aAAO;AAEjC,QAAI,MAAM,WAAW;AAAK,aAAO;AAEjC,QAAI,MAAM,WAAW;AAAK,aAAO;AAEjC,QAAI,MAAM,WAAW;AAAK,aAAO;AAEjC,QAAI,MAAM,WAAW;AAAK,aAAO;AAEjC,QAAI,MAAM,WAAW;AAAK,aAAO;AAEjC,QAAI,MAAM,WAAW;AAAK,aAAO;AACjC,WAAO;EACT;AACA,SAAO;AACT;;;AC1OM,SAAU,gBAId,EACE,KACA,SACA,MACA,SACA,aAAa,GACb,aAAa,KACb,SACA,KAAI,GAEN,OAAiC;AAEjC,QAAMC,OAAM,IAAI;AAChB,SAAO;IACL,QAAQ;MACN;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;IAEF,SAAS,aAAa,SAAS,EAAE,SAAS,YAAY,YAAY,KAAAA,KAAG,CAAE;IACvE;;AAEJ;;;ACaM,SAAU,SACd,aACA,SAAkC,CAAA,GAAE;AAEpC,QAAM,EACJ,MAAM,YACN,OAAO,YACP,OAAO,OACP,aAAa,eAAe,aAC5B,YACA,WAAU,IACR;AACJ,SAAQ,CAAC,EAAE,OAAO,kBAAkB,KAAO,SAAS,GAAG,KAAI,MAAM;AAC/D,QAAI,aAAa;AAEjB,QAAI,aAA2B,MAAK;IAAE;AAEtC,UAAM,YAAY,gBAChB;MACE;MACA;MACA,MAAM,QAAQ,EAAE,QAAQ,OAAM,GAAE;AAC9B,YAAI;AAEJ,cAAMC,SAAQ,OAAO,IAAI,MAAmB;AAC1C,gBAAMC,aAAY,WAAW,CAAC,EAAE;YAC9B,GAAG;YACH;YACA,YAAY;YACZ;WACD;AACD,cAAI;AACF,kBAAM,WAAW,MAAMA,WAAU,QAAQ;cACvC;cACA;aACM;AAER,uBAAW;cACT;cACA;cACA;cACA,WAAAA;cACA,QAAQ;aACT;AAED,mBAAO;UACT,SAAS,KAAK;AACZ,uBAAW;cACT,OAAO;cACP;cACA;cACA,WAAAA;cACA,QAAQ;aACT;AAED,gBAAI,aAAa,GAAY;AAAG,oBAAM;AAGtC,gBAAI,MAAM,WAAW,SAAS;AAAG,oBAAM;AAGvC,oCAAa,WAAW,MAAM,IAAI,CAAC,EAAE,KAAK,CAACA,eAAa;AACtD,oBAAM,EAAE,SAAS,QAAO,IACtBA,WAAU,EAAE,MAAK,CAAE,EAAE,OAAO,WAAW,CAAA;AACzC,kBAAI;AAAS,uBAAO,QAAQ,SAAS,MAAM;AAC3C,kBAAI;AAAS,uBAAO,CAAC,QAAQ,SAAS,MAAM;AAC5C,qBAAO;YACT,CAAC;AACD,gBAAI,CAAC;AAAU,oBAAM;AAGrB,mBAAOD,OAAM,IAAI,CAAC;UACpB;QACF;AACA,eAAOA,OAAK;MACd;MACA;MACA;MACA,MAAM;OAER;MACE,YAAY,CAAC,OAAsB,aAAa;MAChD,YAAY,WAAW,IAAI,CAAC,OAAO,GAAG,EAAE,OAAO,YAAY,EAAC,CAAE,CAAC;KAChE;AAGH,QAAI,MAAM;AACR,YAAM,cAAe,OAAO,SAAS,WAAW,OAAO,CAAA;AACvD,qBAAe;QACb;QACA,UAAU,YAAY,YAAY;QAClC,cAAc,CAACE,iBAAiB,aAAaA;QAC7C,MAAM,YAAY;QAClB,aAAa,YAAY;QACzB,SAAS,YAAY;QACrB;QACA,SAAS,YAAY;OACtB;IACH;AACA,WAAO;EACT;AACF;AAEM,SAAU,YAAY,OAAY;AACtC,MAAI,UAAU,SAAS,OAAO,MAAM,SAAS,UAAU;AACrD,QACE,MAAM,SAAS,4BAA4B,QAC3C,MAAM,SAAS,yBAAyB,QACxC,uBAAuB,YAAY,KAAK,MAAM,OAAO,KACrD,MAAM,SAAS;AAEf,aAAO;EACX;AACA,SAAO;AACT;AAGM,SAAU,eAAe,EAC7B,OACA,WAAW,KACX,cACA,MACA,cAAc,IACd,UAAU,KACV,YACA,UAAU,CAAA,EAAE,GAUb;AACC,QAAM,EAAE,WAAW,kBAAkB,KAAK,SAAS,gBAAgB,IAAG,IACpE;AAIF,QAAM,UAAoB,CAAA;AAE1B,QAAM,kBAAkB,YAAW;AAEjC,UAAM,SAAiB,MAAM,QAAQ,IACnC,WAAW,IAAI,OAAO,cAAa;AACjC,YAAM,aAAa,UAAU,EAAE,OAAO,YAAY,GAAG,QAAO,CAAE;AAE9D,YAAM,QAAQ,KAAK,IAAG;AACtB,UAAI;AACJ,UAAI;AACJ,UAAI;AACF,eAAO,OACH,KAAK,EAAE,WAAW,WAAU,CAAE,IAC9B,WAAW,QAAQ,EAAE,QAAQ,gBAAe,CAAE;AAClD,kBAAU;MACZ,QAAQ;AACN,kBAAU;MACZ;AACE,cAAM,KAAK,IAAG;MAChB;AACA,YAAM,UAAU,MAAM;AACtB,aAAO,EAAE,SAAS,QAAO;IAC3B,CAAC,CAAC;AAKJ,YAAQ,KAAK,MAAM;AACnB,QAAI,QAAQ,SAAS;AAAa,cAAQ,MAAK;AAG/C,UAAM,aAAa,KAAK,IACtB,GAAG,QAAQ,IAAI,CAACC,YACd,KAAK,IAAI,GAAGA,QAAO,IAAI,CAAC,EAAE,QAAO,MAAO,OAAO,CAAC,CAAC,CAClD;AAIH,UAAM,SAAS,WACZ,IAAI,CAAC,GAAG,MAAK;AACZ,YAAM,YAAY,QAAQ,IAAI,CAACA,YAAWA,QAAO,CAAC,EAAE,OAAO;AAC3D,YAAM,cACJ,UAAU,OAAO,CAAC,KAAK,YAAY,MAAM,SAAS,CAAC,IACnD,UAAU;AACZ,YAAM,eAAe,IAAI,cAAc;AAEvC,YAAM,YAAY,QAAQ,IAAI,CAACA,YAAWA,QAAO,CAAC,EAAE,OAAO;AAC3D,YAAM,iBACJ,UAAU,OAAO,CAAC,KAAK,YAAY,MAAM,SAAS,CAAC,IACnD,UAAU;AAEZ,UAAI,mBAAmB;AAAG,eAAO,CAAC,GAAG,CAAC;AACtC,aAAO;QACL,gBAAgB,eAAe,kBAAkB;QACjD;;IAEJ,CAAC,EACA,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAG7B,iBAAa,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,WAAW,CAAC,CAAC,CAAC;AAGjD,UAAM,KAAK,QAAQ;AACnB,oBAAe;EACjB;AACA,kBAAe;AACjB;;;ACrTM,IAAO,mBAAP,cAAgC,UAAS;EAC7C,cAAA;AACE,UACE,0FACA;MACE,UAAU;MACV,MAAM;KACP;EAEL;;;;ACVI,SAAU,YACd,IAGA,EACE,gBAAgB,IAAI,MAAM,WAAW,GACrC,SACA,OAAM,GAQP;AAED,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC;AAAC,KAAC,YAAW;AACX,UAAI;AACJ,UAAI;AACF,cAAM,aAAa,IAAI,gBAAe;AACtC,YAAI,UAAU,GAAG;AACf,sBAAY,WAAW,MAAK;AAC1B,gBAAI,QAAQ;AACV,yBAAW,MAAK;YAClB,OAAO;AACL,qBAAO,aAAa;YACtB;UACF,GAAG,OAAO;QACZ;AACA,gBAAQ,MAAM,GAAG,EAAE,SAAQ,yCAAY,WAAU,KAAI,CAAE,CAAC;MAC1D,SAAS,KAAK;AACZ,aAAK,2BAAe,UAAS;AAAc,iBAAO,aAAa;AAC/D,eAAO,GAAG;MACZ;AACE,qBAAa,SAAS;MACxB;IACF,GAAE;EACJ,CAAC;AACH;;;AC5CA,SAAS,gBAAa;AACpB,SAAO;IACL,SAAS;IACT,OAAI;AACF,aAAO,KAAK;IACd;IACA,QAAK;AACH,WAAK,UAAU;IACjB;;AAEJ;AAEO,IAAM,UAAwB,cAAa;;;AC4D5C,SAAU,iBACd,KACA,UAAgC,CAAA,GAAE;AAElC,SAAO;IACL,MAAM,QAAQ,QAAM;AA7ExB;AA8EM,YAAM,EACJ,MACA,YAAY,QAAQ,WACpB,aAAa,QAAQ,YACrB,UAAU,QAAQ,WAAW,IAAM,IACjC;AAEJ,YAAM,eAAe;QACnB,GAAI,QAAQ,gBAAgB,CAAA;QAC5B,GAAI,OAAO,gBAAgB,CAAA;;AAG7B,YAAM,EAAE,SAAS,QAAQ,QAAQ,QAAO,IAAK;AAE7C,UAAI;AACF,cAAM,WAAW,MAAM,YACrB,OAAO,EAAE,OAAM,MAAM;AACnB,gBAAM,OAAoB;YACxB,GAAG;YACH,MAAM,MAAM,QAAQ,IAAI,IACpB,UACE,KAAK,IAAI,CAACC,WAAU;cAClB,SAAS;cACT,IAAIA,MAAK,MAAM,QAAQ,KAAI;cAC3B,GAAGA;cACH,CAAC,IAEL,UAAU;cACR,SAAS;cACT,IAAI,KAAK,MAAM,QAAQ,KAAI;cAC3B,GAAG;aACJ;YACL,SAAS;cACP,gBAAgB;cAChB,GAAG;;YAEL,QAAQ,UAAU;YAClB,QAAQ,YAAY,UAAU,IAAI,SAAS;;AAE7C,gBAAM,UAAU,IAAI,QAAQ,KAAK,IAAI;AACrC,gBAAM,OAAQ,OAAM,uCAAY,SAAS,UAAU,EAAE,GAAG,MAAM,IAAG;AACjE,gBAAMC,YAAW,MAAM,MAAM,KAAK,OAAO,KAAK,IAAI;AAClD,iBAAOA;QACT,GACA;UACE,eAAe,IAAI,aAAa,EAAE,MAAM,IAAG,CAAE;UAC7C;UACA,QAAQ;SACT;AAGH,YAAI;AAAY,gBAAM,WAAW,QAAQ;AAEzC,YAAI;AACJ,aACE,cAAS,QAAQ,IAAI,cAAc,MAAnC,mBAAsC,WAAW;AAEjD,iBAAO,MAAM,SAAS,KAAI;aACvB;AACH,iBAAO,MAAM,SAAS,KAAI;AAC1B,cAAI;AACF,mBAAO,KAAK,MAAM,QAAQ,IAAI;UAChC,SAAS,KAAK;AACZ,gBAAI,SAAS;AAAI,oBAAM;AACvB,mBAAO,EAAE,OAAO,KAAI;UACtB;QACF;AAEA,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM,IAAI,iBAAiB;YACzB;YACA,SAAS,UAAU,KAAK,KAAK,KAAK,SAAS;YAC3C,SAAS,SAAS;YAClB,QAAQ,SAAS;YACjB;WACD;QACH;AAEA,eAAO;MACT,SAAS,KAAK;AACZ,YAAI,eAAe;AAAkB,gBAAM;AAC3C,YAAI,eAAe;AAAc,gBAAM;AACvC,cAAM,IAAI,iBAAiB;UACzB;UACA,OAAO;UACP;SACD;MACH;IACF;;AAEJ;;;ACnFM,SAAU,KAKd,KACA,SAA8C,CAAA,GAAE;AAEhD,QAAM,EACJ,OACA,cACA,MAAM,QACN,SACA,OAAO,iBACP,gBACA,iBACA,YACA,IAAG,IACD;AACJ,SAAO,CAAC,EAAE,OAAO,YAAY,aAAa,SAAS,SAAQ,MAAM;AAC/D,UAAM,EAAE,YAAY,KAAM,MAAAC,QAAO,EAAC,IAChC,OAAO,UAAU,WAAW,QAAQ,CAAA;AACtC,UAAM,aAAa,OAAO,cAAc;AACxC,UAAM,UAAU,YAAY,OAAO,WAAW;AAC9C,UAAM,OAAO,QAAO,+BAAO,QAAQ,QAAQ,KAAK;AAChD,QAAI,CAAC;AAAM,YAAM,IAAI,iBAAgB;AAErC,UAAM,YAAY,iBAAiB,MAAM;MACvC;MACA,WAAW;MACX,YAAY;MACZ;KACD;AAED,WAAO,gBACL;MACE;MACA;MACA;MACA,MAAM,QAAQ,EAAE,QAAQ,OAAM,GAAE;AAC9B,cAAM,OAAO,EAAE,QAAQ,OAAM;AAE7B,cAAM,EAAE,SAAQ,IAAK,qBAAqB;UACxC,IAAI;UACJ,MAAAA;UACA,iBAAiB,UAAQ;AACvB,mBAAO,SAAS,SAAS;UAC3B;UACA,IAAI,CAACC,UACH,UAAU,QAAQ;YAChB,MAAAA;WACD;UACH,MAAM,CAAC,GAAG,MAAM,EAAE,KAAK,EAAE;SAC1B;AAED,cAAM,KAAK,OAAOA,UAChB,QACI,SAASA,KAAI,IACb;UACE,MAAM,UAAU,QAAQ;YACtB,MAAAA;WACD;;AAGT,cAAM,CAAC,EAAE,OAAO,OAAM,CAAE,IAAI,MAAM,GAAG,IAAI;AAEzC,YAAI;AAAK,iBAAO,EAAE,OAAO,OAAM;AAC/B,YAAI;AACF,gBAAM,IAAI,gBAAgB;YACxB;YACA;YACA,KAAK;WACN;AACH,eAAO;MACT;MACA;MACA;MACA;MACA,MAAM;OAER;MACE;MACA,KAAK;KACN;EAEL;AACF;;;AClKA,IAAM,MAAsB,IAAI,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC;AACjG,IAAM,KAAqB,IAAI,WAAW,IAAI,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAChF,IAAM,KAAqB,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,EAAE;AACzD,IAAI,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,CAAC,EAAE;AACd,SAAS,IAAI,GAAG,IAAI,GAAG;AAAK,WAAS,KAAK,CAAC,MAAM,IAAI;AAAG,MAAE,KAAK,EAAE,CAAC,EAAE,IAAI,CAACC,OAAM,IAAIA,EAAC,CAAC,CAAC;AAEtF,IAAM,SAAyB;EAC7B,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,IAAI,CAAC,MAAM,IAAI,WAAW,CAAC,CAAC;AAC9B,IAAM,UAA0B,KAAK,IAAI,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AACjF,IAAM,UAA0B,KAAK,IAAI,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AACjF,IAAM,KAAqB,IAAI,YAAY;EACzC;EAAY;EAAY;EAAY;EAAY;CACjD;AACD,IAAM,KAAqB,IAAI,YAAY;EACzC;EAAY;EAAY;EAAY;EAAY;CACjD;AAED,SAAS,EAAE,OAAe,GAAW,GAAW,GAAS;AACvD,MAAI,UAAU;AAAG,WAAO,IAAI,IAAI;WACvB,UAAU;AAAG,WAAQ,IAAI,IAAM,CAAC,IAAI;WACpC,UAAU;AAAG,YAAQ,IAAI,CAAC,KAAK;WAC/B,UAAU;AAAG,WAAQ,IAAI,IAAM,IAAI,CAAC;;AACxC,WAAO,KAAK,IAAI,CAAC;AACxB;AAEA,IAAM,QAAwB,IAAI,YAAY,EAAE;AAC1C,IAAO,YAAP,cAAyB,OAAiB;EAO9C,cAAA;AACE,UAAM,IAAI,IAAI,GAAG,IAAI;AAPf,SAAA,KAAK,aAAa;AAClB,SAAA,KAAK,aAAa;AAClB,SAAA,KAAK,aAAa;AAClB,SAAA,KAAK,YAAa;AAClB,SAAA,KAAK,aAAa;EAI1B;EACU,MAAG;AACX,UAAM,EAAE,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AAC/B,WAAO,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE;EAC5B;EACU,IAAI,IAAY,IAAY,IAAY,IAAY,IAAU;AACtE,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;EACjB;EACU,QAAQ,MAAgB,QAAc;AAC9C,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,UAAU;AAAG,YAAM,CAAC,IAAI,KAAK,UAAU,QAAQ,IAAI;AAEhF,QAAI,KAAK,KAAK,KAAK,GAAG,KAAK,IACvB,KAAK,KAAK,KAAK,GAAG,KAAK,IACvB,KAAK,KAAK,KAAK,GAAG,KAAK,IACvB,KAAK,KAAK,KAAK,GAAG,KAAK,IACvB,KAAK,KAAK,KAAK,GAAG,KAAK;AAI3B,aAAS,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACtC,YAAM,SAAS,IAAI;AACnB,YAAM,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK;AACrC,YAAM,KAAK,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK;AACvC,YAAM,KAAK,QAAQ,KAAK,GAAG,KAAK,QAAQ,KAAK;AAC7C,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,KAAM,KAAK,KAAK,EAAE,OAAO,IAAI,IAAI,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,KAAM;AAChF,aAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE,IAAI,GAAG,KAAK,IAAI,KAAK;MACzD;AAEA,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,KAAM,KAAK,KAAK,EAAE,QAAQ,IAAI,IAAI,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,KAAM;AACjF,aAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE,IAAI,GAAG,KAAK,IAAI,KAAK;MACzD;IACF;AAEA,SAAK,IACF,KAAK,KAAK,KAAK,KAAM,GACrB,KAAK,KAAK,KAAK,KAAM,GACrB,KAAK,KAAK,KAAK,KAAM,GACrB,KAAK,KAAK,KAAK,KAAM,GACrB,KAAK,KAAK,KAAK,KAAM,CAAC;EAE3B;EACU,aAAU;AAClB,UAAM,KAAK,CAAC;EACd;EACA,UAAO;AACL,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK,CAAC;AAClB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;EACxB;;AAIK,IAAM,YAAmC,gBAAgB,MAAM,IAAI,UAAS,CAAE;;;ACnE/E,SAAU,mBACd,YAAwC;AAExC,QAAM,EAAE,OAAM,IAAK;AAEnB,QAAM,WAAW,oBAAI,IAAG;AACxB,QAAM,WAAW,IAAI,OAAe,IAAI;AACxC,QAAM,aAAa,oBAAI,IAAG;AAE1B,QAAM,SAAS,CAAC,EAAE,SAAS,QAAO,MAChC,GAAG,OAAO,IAAI,OAAO;AAEvB,SAAO;IACL,MAAM,QAAQ,EAAE,SAAS,SAAS,OAAM,GAAE;AACxC,YAAM,MAAM,OAAO,EAAE,SAAS,QAAO,CAAE;AACvC,YAAM,UAAU,KAAK,IAAI,EAAE,SAAS,SAAS,OAAM,CAAE;AAErD,WAAK,UAAU,EAAE,SAAS,QAAO,CAAE;AACnC,YAAM,QAAQ,MAAM;AAEpB,YAAM,OAAO,IAAI,EAAE,SAAS,QAAO,GAAI,KAAK;AAC5C,eAAS,IAAI,KAAK,KAAK;AAEvB,aAAO;IACT;IACA,MAAM,UAAU,EAAE,SAAS,QAAO,GAAE;AAClC,YAAM,MAAM,OAAO,EAAE,SAAS,QAAO,CAAE;AACvC,YAAM,QAAQ,SAAS,IAAI,GAAG,KAAK;AACnC,eAAS,IAAI,KAAK,QAAQ,CAAC;IAC7B;IACA,MAAM,IAAI,EAAE,SAAS,SAAS,OAAM,GAAE;AACpC,YAAM,MAAM,OAAO,EAAE,SAAS,QAAO,CAAE;AAEvC,UAAI,UAAU,WAAW,IAAI,GAAG;AAChC,UAAI,CAAC,SAAS;AACZ,mBAAW,YAAW;AACpB,cAAI;AACF,kBAAM,QAAQ,MAAM,OAAO,IAAI,EAAE,SAAS,SAAS,OAAM,CAAE;AAC3D,kBAAM,gBAAgB,SAAS,IAAI,GAAG,KAAK;AAC3C,gBAAI,gBAAgB,KAAK,SAAS;AAChC,qBAAO,gBAAgB;AACzB,qBAAS,OAAO,GAAG;AACnB,mBAAO;UACT;AACE,iBAAK,MAAM,EAAE,SAAS,QAAO,CAAE;UACjC;QACF,GAAE;AACF,mBAAW,IAAI,KAAK,OAAO;MAC7B;AAEA,YAAM,QAAQ,SAAS,IAAI,GAAG,KAAK;AACnC,aAAO,QAAS,MAAM;IACxB;IACA,MAAM,EAAE,SAAS,QAAO,GAAE;AACxB,YAAM,MAAM,OAAO,EAAE,SAAS,QAAO,CAAE;AACvC,eAAS,OAAO,GAAG;AACnB,iBAAW,OAAO,GAAG;IACvB;;AAEJ;AAaM,SAAU,UAAO;AACrB,SAAO;IACL,MAAM,IAAI,YAAU;AAClB,YAAM,EAAE,SAAS,OAAM,IAAK;AAC5B,aAAO,oBAAoB,QAAQ;QACjC;QACA,UAAU;OACX;IACH;IACA,MAAG;IAAI;;AAEX;AAMO,IAAM,eAA6B,mBAAmB;EAC3D,QAAQ,QAAO;CAChB;;;AC9HD,SAAS,QAAQ,GAAS;AACxB,MAAI,CAAC,OAAO,cAAc,CAAC,KAAK,IAAI;AAAG,UAAM,IAAI,MAAM,oCAAoC,CAAC;AAC9F;AAGA,SAASC,SAAQ,GAAU;AACzB,SAAO,aAAa,cAAe,YAAY,OAAO,CAAC,KAAK,EAAE,YAAY,SAAS;AACrF;AAGA,SAAS,OAAO,MAA8B,SAAiB;AAC7D,MAAI,CAACA,SAAQ,CAAC;AAAG,UAAM,IAAI,MAAM,qBAAqB;AACtD,MAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,MAAM;AAClD,UAAM,IAAI,MAAM,mCAAmC,UAAU,kBAAkB,EAAE,MAAM;AAC3F;AAmBA,SAAS,QAAQ,UAAe,gBAAgB,MAAI;AAClD,MAAI,SAAS;AAAW,UAAM,IAAI,MAAM,kCAAkC;AAC1E,MAAI,iBAAiB,SAAS;AAAU,UAAM,IAAI,MAAM,uCAAuC;AACjG;AAGA,SAAS,QAAQ,KAAU,UAAa;AACtC,SAAO,GAAG;AACV,QAAM,MAAM,SAAS;AACrB,MAAI,IAAI,SAAS,KAAK;AACpB,UAAM,IAAI,MAAM,2DAA2D,GAAG;EAChF;AACF;;;AC5CO,IAAM,SACX,OAAO,eAAe,YAAY,YAAY,aAAa,WAAW,SAAS;;;ACoB3E,SAAU,IAAI,KAAe;AACjC,SAAO,IAAI,YAAY,IAAI,QAAQ,IAAI,YAAY,KAAK,MAAM,IAAI,aAAa,CAAC,CAAC;AACnF;AAGM,SAAU,WAAW,KAAe;AACxC,SAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AAChE;AAGM,SAAU,KAAK,MAAc,OAAa;AAC9C,SAAQ,QAAS,KAAK,QAAW,SAAS;AAC5C;AAEM,SAAUC,MAAK,MAAc,OAAa;AAC9C,SAAQ,QAAQ,QAAW,SAAU,KAAK,UAAY;AACxD;AAGO,IAAM,QAAiC,MAC5C,IAAI,WAAW,IAAI,YAAY,CAAC,SAAU,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,IAAK;AAE7D,SAAU,SAAS,MAAY;AACnC,SACI,QAAQ,KAAM,aACd,QAAQ,IAAK,WACb,SAAS,IAAK,QACd,SAAS,KAAM;AAErB;AAOM,SAAU,WAAW,KAAgB;AACzC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC;EAC1B;AACF;AAGA,IAAM,QAAwB,MAAM,KAAK,EAAE,QAAQ,IAAG,GAAI,CAAC,GAAG,MAC5D,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AA+E3B,SAAU,YAAY,KAAW;AACrC,MAAI,OAAO,QAAQ;AAAU,UAAM,IAAI,MAAM,sCAAsC,OAAO,GAAG;AAC7F,SAAO,IAAI,WAAW,IAAI,YAAW,EAAG,OAAO,GAAG,CAAC;AACrD;AASM,SAAUC,SAAQ,MAAW;AACjC,MAAI,OAAO,SAAS;AAAU,WAAO,YAAY,IAAI;AACrD,SAAO,IAAI;AACX,SAAO;AACT;AAsBM,IAAgB,OAAhB,MAAoB;;EAsBxB,QAAK;AACH,WAAO,KAAK,WAAU;EACxB;;AAiCI,SAAUC,iBACd,UAAuB;AAOvB,QAAM,QAAQ,CAAC,QAA2B,SAAQ,EAAG,OAAOC,SAAQ,GAAG,CAAC,EAAE,OAAM;AAChF,QAAM,MAAM,SAAQ;AACpB,QAAM,YAAY,IAAI;AACtB,QAAM,WAAW,IAAI;AACrB,QAAM,SAAS,MAAM,SAAQ;AAC7B,SAAO;AACT;AAkBM,SAAU,2BACd,UAAkC;AAOlC,QAAM,QAAQ,CAAC,KAAY,SAAyB,SAAS,IAAI,EAAE,OAAOC,SAAQ,GAAG,CAAC,EAAE,OAAM;AAC9F,QAAM,MAAM,SAAS,CAAA,CAAO;AAC5B,QAAM,YAAY,IAAI;AACtB,QAAM,WAAW,IAAI;AACrB,QAAM,SAAS,CAAC,SAAY,SAAS,IAAI;AACzC,SAAO;AACT;;;AC5RM,SAAU,aACd,MACA,YACA,OACAC,OAAa;AAEb,MAAI,OAAO,KAAK,iBAAiB;AAAY,WAAO,KAAK,aAAa,YAAY,OAAOA,KAAI;AAC7F,QAAMC,QAAO,OAAO,EAAE;AACtB,QAAM,WAAW,OAAO,UAAU;AAClC,QAAM,KAAK,OAAQ,SAASA,QAAQ,QAAQ;AAC5C,QAAM,KAAK,OAAO,QAAQ,QAAQ;AAClC,QAAM,IAAID,QAAO,IAAI;AACrB,QAAM,IAAIA,QAAO,IAAI;AACrB,OAAK,UAAU,aAAa,GAAG,IAAIA,KAAI;AACvC,OAAK,UAAU,aAAa,GAAG,IAAIA,KAAI;AACzC;AAGM,SAAU,IAAI,GAAW,GAAW,GAAS;AACjD,SAAQ,IAAI,IAAM,CAAC,IAAI;AACzB;AAGM,SAAU,IAAI,GAAW,GAAW,GAAS;AACjD,SAAQ,IAAI,IAAM,IAAI,IAAM,IAAI;AAClC;AAMM,IAAgBE,UAAhB,cAAoD,KAAO;EAc/D,YACW,UACF,WACE,WACAF,OAAa;AAEtB,UAAK;AALI,SAAA,WAAA;AACF,SAAA,YAAA;AACE,SAAA,YAAA;AACA,SAAA,OAAAA;AATD,SAAA,WAAW;AACX,SAAA,SAAS;AACT,SAAA,MAAM;AACN,SAAA,YAAY;AASpB,SAAK,SAAS,IAAI,WAAW,QAAQ;AACrC,SAAK,OAAO,WAAW,KAAK,MAAM;EACpC;EACA,OAAO,MAAW;AAChB,YAAQ,IAAI;AACZ,UAAM,EAAE,MAAM,QAAAG,SAAQ,SAAQ,IAAK;AACnC,WAAOC,SAAQ,IAAI;AACnB,UAAM,MAAM,KAAK;AACjB,aAAS,MAAM,GAAG,MAAM,OAAO;AAC7B,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AAEpD,UAAI,SAAS,UAAU;AACrB,cAAM,WAAW,WAAW,IAAI;AAChC,eAAO,YAAY,MAAM,KAAK,OAAO;AAAU,eAAK,QAAQ,UAAU,GAAG;AACzE;MACF;AACA,MAAAD,QAAO,IAAI,KAAK,SAAS,KAAK,MAAM,IAAI,GAAG,KAAK,GAAG;AACnD,WAAK,OAAO;AACZ,aAAO;AACP,UAAI,KAAK,QAAQ,UAAU;AACzB,aAAK,QAAQ,MAAM,CAAC;AACpB,aAAK,MAAM;MACb;IACF;AACA,SAAK,UAAU,KAAK;AACpB,SAAK,WAAU;AACf,WAAO;EACT;EACA,WAAW,KAAe;AACxB,YAAQ,IAAI;AACZ,YAAQ,KAAK,IAAI;AACjB,SAAK,WAAW;AAIhB,UAAM,EAAE,QAAAA,SAAQ,MAAM,UAAU,MAAAH,MAAI,IAAK;AACzC,QAAI,EAAE,IAAG,IAAK;AAEd,IAAAG,QAAO,KAAK,IAAI;AAChB,SAAK,OAAO,SAAS,GAAG,EAAE,KAAK,CAAC;AAGhC,QAAI,KAAK,YAAY,WAAW,KAAK;AACnC,WAAK,QAAQ,MAAM,CAAC;AACpB,YAAM;IACR;AAEA,aAAS,IAAI,KAAK,IAAI,UAAU;AAAK,MAAAA,QAAO,CAAC,IAAI;AAIjD,iBAAa,MAAM,WAAW,GAAG,OAAO,KAAK,SAAS,CAAC,GAAGH,KAAI;AAC9D,SAAK,QAAQ,MAAM,CAAC;AACpB,UAAM,QAAQ,WAAW,GAAG;AAC5B,UAAM,MAAM,KAAK;AAEjB,QAAI,MAAM;AAAG,YAAM,IAAI,MAAM,6CAA6C;AAC1E,UAAM,SAAS,MAAM;AACrB,UAAMK,SAAQ,KAAK,IAAG;AACtB,QAAI,SAASA,OAAM;AAAQ,YAAM,IAAI,MAAM,oCAAoC;AAC/E,aAAS,IAAI,GAAG,IAAI,QAAQ;AAAK,YAAM,UAAU,IAAI,GAAGA,OAAM,CAAC,GAAGL,KAAI;EACxE;EACA,SAAM;AACJ,UAAM,EAAE,QAAAG,SAAQ,UAAS,IAAK;AAC9B,SAAK,WAAWA,OAAM;AACtB,UAAM,MAAMA,QAAO,MAAM,GAAG,SAAS;AACrC,SAAK,QAAO;AACZ,WAAO;EACT;EACA,WAAW,IAAM;AACf,WAAA,KAAO,IAAK,KAAK,YAAmB;AACpC,OAAG,IAAI,GAAG,KAAK,IAAG,CAAE;AACpB,UAAM,EAAE,UAAU,QAAAA,SAAQ,QAAQ,UAAU,WAAW,IAAG,IAAK;AAC/D,OAAG,SAAS;AACZ,OAAG,MAAM;AACT,OAAG,WAAW;AACd,OAAG,YAAY;AACf,QAAI,SAAS;AAAU,SAAG,OAAO,IAAIA,OAAM;AAC3C,WAAO;EACT;;;;AClIF,IAAMG,OAAsB,IAAI,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC;AACjG,IAAMC,MAAqB,IAAI,WAAW,IAAI,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAChF,IAAMC,MAAqBD,IAAG,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,EAAE;AACzD,IAAIE,QAAO,CAACF,GAAE;AACd,IAAIG,QAAO,CAACF,GAAE;AACd,SAAS,IAAI,GAAG,IAAI,GAAG;AAAK,WAAS,KAAK,CAACC,OAAMC,KAAI;AAAG,MAAE,KAAK,EAAE,CAAC,EAAE,IAAI,CAACC,OAAML,KAAIK,EAAC,CAAC,CAAC;AAEtF,IAAMC,UAAyB;EAC7B,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EACvD,IAAI,CAAC,MAAM,IAAI,WAAW,CAAC,CAAC;AAC9B,IAAMC,WAA0BJ,MAAK,IAAI,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,MAAMG,QAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AACjF,IAAME,WAA0BJ,MAAK,IAAI,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,MAAME,QAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AACjF,IAAMG,MAAqB,IAAI,YAAY;EACzC;EAAY;EAAY;EAAY;EAAY;CACjD;AACD,IAAMC,MAAqB,IAAI,YAAY;EACzC;EAAY;EAAY;EAAY;EAAY;CACjD;AAED,SAASC,GAAE,OAAe,GAAW,GAAW,GAAS;AACvD,MAAI,UAAU;AAAG,WAAO,IAAI,IAAI;WACvB,UAAU;AAAG,WAAQ,IAAI,IAAM,CAAC,IAAI;WACpC,UAAU;AAAG,YAAQ,IAAI,CAAC,KAAK;WAC/B,UAAU;AAAG,WAAQ,IAAI,IAAM,IAAI,CAAC;;AACxC,WAAO,KAAK,IAAI,CAAC;AACxB;AAEA,IAAMC,SAAwB,IAAI,YAAY,EAAE;AAC1C,IAAOC,aAAP,cAAyBC,QAAiB;EAO9C,cAAA;AACE,UAAM,IAAI,IAAI,GAAG,IAAI;AAPf,SAAA,KAAK,aAAa;AAClB,SAAA,KAAK,aAAa;AAClB,SAAA,KAAK,aAAa;AAClB,SAAA,KAAK,YAAa;AAClB,SAAA,KAAK,aAAa;EAI1B;EACU,MAAG;AACX,UAAM,EAAE,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AAC/B,WAAO,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE;EAC5B;EACU,IAAI,IAAY,IAAY,IAAY,IAAY,IAAU;AACtE,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;EACjB;EACU,QAAQ,MAAgB,QAAc;AAC9C,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,UAAU;AAAG,MAAAF,OAAM,CAAC,IAAI,KAAK,UAAU,QAAQ,IAAI;AAEhF,QAAI,KAAK,KAAK,KAAK,GAAG,KAAK,IACvB,KAAK,KAAK,KAAK,GAAG,KAAK,IACvB,KAAK,KAAK,KAAK,GAAG,KAAK,IACvB,KAAK,KAAK,KAAK,GAAG,KAAK,IACvB,KAAK,KAAK,KAAK,GAAG,KAAK;AAI3B,aAAS,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACtC,YAAM,SAAS,IAAI;AACnB,YAAM,MAAMH,IAAG,KAAK,GAAG,MAAMC,IAAG,KAAK;AACrC,YAAM,KAAKP,MAAK,KAAK,GAAG,KAAKC,MAAK,KAAK;AACvC,YAAM,KAAKG,SAAQ,KAAK,GAAG,KAAKC,SAAQ,KAAK;AAC7C,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,KAAMO,MAAK,KAAKJ,GAAE,OAAO,IAAI,IAAI,EAAE,IAAIC,OAAM,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,KAAM;AAChF,aAAK,IAAI,KAAK,IAAI,KAAKG,MAAK,IAAI,EAAE,IAAI,GAAG,KAAK,IAAI,KAAK;MACzD;AAEA,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,KAAMA,MAAK,KAAKJ,GAAE,QAAQ,IAAI,IAAI,EAAE,IAAIC,OAAM,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,KAAM;AACjF,aAAK,IAAI,KAAK,IAAI,KAAKG,MAAK,IAAI,EAAE,IAAI,GAAG,KAAK,IAAI,KAAK;MACzD;IACF;AAEA,SAAK,IACF,KAAK,KAAK,KAAK,KAAM,GACrB,KAAK,KAAK,KAAK,KAAM,GACrB,KAAK,KAAK,KAAK,KAAM,GACrB,KAAK,KAAK,KAAK,KAAM,GACrB,KAAK,KAAK,KAAK,KAAM,CAAC;EAE3B;EACU,aAAU;AAClB,IAAAH,OAAM,KAAK,CAAC;EACd;EACA,UAAO;AACL,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK,CAAC;AAClB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;EACxB;;AAIK,IAAMI,aAAmCC,iBAAgB,MAAM,IAAIJ,WAAS,CAAE;;;ACvGrF,IAAM,aAA6B,OAAO,KAAK,KAAK,CAAC;AACrD,IAAM,OAAuB,OAAO,EAAE;AAEtC,SAAS,QACP,GACA,KAAK,OAAK;AAKV,MAAI;AAAI,WAAO,EAAE,GAAG,OAAO,IAAI,UAAU,GAAG,GAAG,OAAQ,KAAK,OAAQ,UAAU,EAAC;AAC/E,SAAO,EAAE,GAAG,OAAQ,KAAK,OAAQ,UAAU,IAAI,GAAG,GAAG,OAAO,IAAI,UAAU,IAAI,EAAC;AACjF;AAEA,SAAS,MAAM,KAAe,KAAK,OAAK;AACtC,MAAI,KAAK,IAAI,YAAY,IAAI,MAAM;AACnC,MAAI,KAAK,IAAI,YAAY,IAAI,MAAM;AACnC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAM,EAAE,GAAG,EAAC,IAAK,QAAQ,IAAI,CAAC,GAAG,EAAE;AACnC,KAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EACxB;AACA,SAAO,CAAC,IAAI,EAAE;AAChB;AAgBA,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAK,IAAM,MAAO,KAAK;AACpF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAK,IAAM,MAAO,KAAK;AAEpF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,IAAI,KAAQ,MAAO,KAAK;AAC3F,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,IAAI,KAAQ,MAAO,KAAK;;;ACnB3F,IAAM,UAAoB,CAAA;AAC1B,IAAM,YAAsB,CAAA;AAC5B,IAAM,aAAuB,CAAA;AAC7B,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,QAAwB,OAAO,GAAG;AACxC,IAAM,SAAyB,OAAO,GAAI;AAC1C,SAAS,QAAQ,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,SAAS;AAE9D,GAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC;AAChC,UAAQ,KAAK,KAAK,IAAI,IAAI,EAAE;AAE5B,YAAU,MAAQ,QAAQ,MAAM,QAAQ,KAAM,IAAK,EAAE;AAErD,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,SAAM,KAAK,OAAS,KAAK,OAAO,UAAW;AAC3C,QAAI,IAAI;AAAK,WAAK,QAAS,OAAuB,OAAO,CAAC,KAAK;EACjE;AACA,aAAW,KAAK,CAAC;AACnB;AACA,IAAM,CAAC,aAAa,WAAW,IAAoB,MAAM,YAAY,IAAI;AAGzE,IAAM,QAAQ,CAAC,GAAW,GAAW,MAAe,IAAI,KAAK,OAAO,GAAG,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,CAAC;AAC7F,IAAM,QAAQ,CAAC,GAAW,GAAW,MAAe,IAAI,KAAK,OAAO,GAAG,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,CAAC;AAGvF,SAAU,QAAQ,GAAgB,SAAiB,IAAE;AACzD,QAAM,IAAI,IAAI,YAAY,IAAI,CAAC;AAE/B,WAAS,QAAQ,KAAK,QAAQ,QAAQ,IAAI,SAAS;AAEjD,aAAS,IAAI,GAAG,IAAI,IAAI;AAAK,QAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AACvF,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,YAAM,QAAQ,IAAI,KAAK;AACvB,YAAM,QAAQ,IAAI,KAAK;AACvB,YAAM,KAAK,EAAE,IAAI;AACjB,YAAM,KAAK,EAAE,OAAO,CAAC;AACrB,YAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI;AACpC,YAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AACxC,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI;AAC/B,UAAE,IAAI,CAAC,KAAK;AACZ,UAAE,IAAI,IAAI,CAAC,KAAK;MAClB;IACF;AAEA,QAAI,OAAO,EAAE,CAAC;AACd,QAAI,OAAO,EAAE,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAM,QAAQ,UAAU,CAAC;AACzB,YAAM,KAAK,MAAM,MAAM,MAAM,KAAK;AAClC,YAAM,KAAK,MAAM,MAAM,MAAM,KAAK;AAClC,YAAM,KAAK,QAAQ,CAAC;AACpB,aAAO,EAAE,EAAE;AACX,aAAO,EAAE,KAAK,CAAC;AACf,QAAE,EAAE,IAAI;AACR,QAAE,KAAK,CAAC,IAAI;IACd;AAEA,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI;AAC/B,eAAS,IAAI,GAAG,IAAI,IAAI;AAAK,UAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAC3C,eAAS,IAAI,GAAG,IAAI,IAAI;AAAK,UAAE,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,KAAK,EAAE;IAC5E;AAEA,MAAE,CAAC,KAAK,YAAY,KAAK;AACzB,MAAE,CAAC,KAAK,YAAY,KAAK;EAC3B;AACA,IAAE,KAAK,CAAC;AACV;AAGM,IAAO,SAAP,MAAO,gBAAe,KAAY;;EAQtC,YACS,UACA,QACA,WACG,YAAY,OACZ,SAAiB,IAAE;AAE7B,UAAK;AANE,SAAA,WAAA;AACA,SAAA,SAAA;AACA,SAAA,YAAA;AACG,SAAA,YAAA;AACA,SAAA,SAAA;AAXF,SAAA,MAAM;AACN,SAAA,SAAS;AACT,SAAA,WAAW;AAEX,SAAA,YAAY;AAWpB,YAAQ,SAAS;AAGjB,QAAI,KAAK,KAAK,YAAY,KAAK,YAAY;AACzC,YAAM,IAAI,MAAM,0CAA0C;AAC5D,SAAK,QAAQ,IAAI,WAAW,GAAG;AAC/B,SAAK,UAAU,IAAI,KAAK,KAAK;EAC/B;EACU,SAAM;AACd,QAAI,CAAC;AAAM,iBAAW,KAAK,OAAO;AAClC,YAAQ,KAAK,SAAS,KAAK,MAAM;AACjC,QAAI,CAAC;AAAM,iBAAW,KAAK,OAAO;AAClC,SAAK,SAAS;AACd,SAAK,MAAM;EACb;EACA,OAAO,MAAW;AAChB,YAAQ,IAAI;AACZ,UAAM,EAAE,UAAU,OAAAK,OAAK,IAAK;AAC5B,WAAOC,SAAQ,IAAI;AACnB,UAAM,MAAM,KAAK;AACjB,aAAS,MAAM,GAAG,MAAM,OAAO;AAC7B,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AACpD,eAAS,IAAI,GAAG,IAAI,MAAM;AAAK,QAAAD,OAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAC9D,UAAI,KAAK,QAAQ;AAAU,aAAK,OAAM;IACxC;AACA,WAAO;EACT;EACU,SAAM;AACd,QAAI,KAAK;AAAU;AACnB,SAAK,WAAW;AAChB,UAAM,EAAE,OAAAA,QAAO,QAAQ,KAAK,SAAQ,IAAK;AAEzC,IAAAA,OAAM,GAAG,KAAK;AACd,SAAK,SAAS,SAAU,KAAK,QAAQ,WAAW;AAAG,WAAK,OAAM;AAC9D,IAAAA,OAAM,WAAW,CAAC,KAAK;AACvB,SAAK,OAAM;EACb;EACU,UAAU,KAAe;AACjC,YAAQ,MAAM,KAAK;AACnB,WAAO,GAAG;AACV,SAAK,OAAM;AACX,UAAM,YAAY,KAAK;AACvB,UAAM,EAAE,SAAQ,IAAK;AACrB,aAAS,MAAM,GAAG,MAAM,IAAI,QAAQ,MAAM,OAAO;AAC/C,UAAI,KAAK,UAAU;AAAU,aAAK,OAAM;AACxC,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,QAAQ,MAAM,GAAG;AACvD,UAAI,IAAI,UAAU,SAAS,KAAK,QAAQ,KAAK,SAAS,IAAI,GAAG,GAAG;AAChE,WAAK,UAAU;AACf,aAAO;IACT;AACA,WAAO;EACT;EACA,QAAQ,KAAe;AAErB,QAAI,CAAC,KAAK;AAAW,YAAM,IAAI,MAAM,uCAAuC;AAC5E,WAAO,KAAK,UAAU,GAAG;EAC3B;EACA,IAAI,OAAa;AACf,YAAQ,KAAK;AACb,WAAO,KAAK,QAAQ,IAAI,WAAW,KAAK,CAAC;EAC3C;EACA,WAAW,KAAe;AACxB,YAAQ,KAAK,IAAI;AACjB,QAAI,KAAK;AAAU,YAAM,IAAI,MAAM,6BAA6B;AAChE,SAAK,UAAU,GAAG;AAClB,SAAK,QAAO;AACZ,WAAO;EACT;EACA,SAAM;AACJ,WAAO,KAAK,WAAW,IAAI,WAAW,KAAK,SAAS,CAAC;EACvD;EACA,UAAO;AACL,SAAK,YAAY;AACjB,SAAK,MAAM,KAAK,CAAC;EACnB;EACA,WAAW,IAAW;AACpB,UAAM,EAAE,UAAU,QAAQ,WAAW,QAAQ,UAAS,IAAK;AAC3D,WAAA,KAAO,IAAI,QAAO,UAAU,QAAQ,WAAW,WAAW,MAAM;AAChE,OAAG,QAAQ,IAAI,KAAK,OAAO;AAC3B,OAAG,MAAM,KAAK;AACd,OAAG,SAAS,KAAK;AACjB,OAAG,WAAW,KAAK;AACnB,OAAG,SAAS;AAEZ,OAAG,SAAS;AACZ,OAAG,YAAY;AACf,OAAG,YAAY;AACf,OAAG,YAAY,KAAK;AACpB,WAAO;EACT;;AAGF,IAAM,MAAM,CAAC,QAAgB,UAAkB,cAC7CE,iBAAgB,MAAM,IAAI,OAAO,UAAU,QAAQ,SAAS,CAAC;AAGxD,IAAM,WAAkC,IAAI,GAAM,KAAK,MAAM,CAAC;AAE9D,IAAM,WAAkC,IAAI,GAAM,KAAK,MAAM,CAAC;AAE9D,IAAM,WAAkC,IAAI,GAAM,KAAK,MAAM,CAAC;AAE9D,IAAM,WAAkC,IAAI,GAAM,IAAI,MAAM,CAAC;AAG7D,IAAM,aAAoC,IAAI,GAAM,KAAK,MAAM,CAAC;AAEhE,IAAM,aAAoC,IAAI,GAAM,KAAK,MAAM,CAAC;AAEhE,IAAM,aAAoC,IAAI,GAAM,KAAK,MAAM,CAAC;AAEhE,IAAM,aAAoC,IAAI,GAAM,IAAI,MAAM,CAAC;AAItE,IAAM,WAAW,CAAC,QAAgB,UAAkB,cAClD,2BACE,CAAC,OAAkB,CAAA,MACjB,IAAI,OAAO,UAAU,QAAQ,KAAK,UAAU,SAAY,YAAY,KAAK,OAAO,IAAI,CAAC;AAIpF,IAAM,WAAoC,SAAS,IAAM,KAAK,MAAM,CAAC;AAErE,IAAM,WAAoC,SAAS,IAAM,KAAK,MAAM,CAAC;;;ACrO5E,IAAM,WAA2B,IAAI,YAAY;EAC/C;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAID,IAAM,YAA4B,IAAI,YAAY;EAChD;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAMD,IAAM,WAA2B,IAAI,YAAY,EAAE;AAC7C,IAAO,SAAP,cAAsBC,QAAc;EAYxC,cAAA;AACE,UAAM,IAAI,IAAI,GAAG,KAAK;AAVd,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;EAIrC;EACU,MAAG;AACX,UAAM,EAAE,GAAAC,IAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAC,IAAK;AACnC,WAAO,CAACA,IAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAChC;;EAEU,IACRA,IAAW,GAAW,GAAW,GAAW,GAAW,GAAW,GAAW,GAAS;AAEtF,SAAK,IAAIA,KAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;EACf;EACU,QAAQ,MAAgB,QAAc;AAE9C,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,UAAU;AAAG,eAAS,CAAC,IAAI,KAAK,UAAU,QAAQ,KAAK;AACpF,aAAS,IAAI,IAAI,IAAI,IAAI,KAAK;AAC5B,YAAM,MAAM,SAAS,IAAI,EAAE;AAC3B,YAAM,KAAK,SAAS,IAAI,CAAC;AACzB,YAAM,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,IAAK,QAAQ;AACnD,YAAM,KAAK,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAK,OAAO;AACjD,eAAS,CAAC,IAAK,KAAK,SAAS,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,EAAE,IAAK;IACjE;AAEA,QAAI,EAAE,GAAAA,IAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAC,IAAK;AACjC,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAM,SAAS,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE;AACpD,YAAM,KAAM,IAAI,SAAS,IAAI,GAAG,GAAG,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,CAAC,IAAK;AACrE,YAAM,SAAS,KAAKA,IAAG,CAAC,IAAI,KAAKA,IAAG,EAAE,IAAI,KAAKA,IAAG,EAAE;AACpD,YAAM,KAAM,SAAS,IAAIA,IAAG,GAAG,CAAC,IAAK;AACrC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAK,IAAI,KAAM;AACf,UAAI;AACJ,UAAI;AACJ,UAAIA;AACJ,MAAAA,KAAK,KAAK,KAAM;IAClB;AAEA,IAAAA,KAAKA,KAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,SAAK,IAAIA,IAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACjC;EACU,aAAU;AAClB,aAAS,KAAK,CAAC;EACjB;EACA,UAAO;AACL,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B,SAAK,OAAO,KAAK,CAAC;EACpB;;AAMF,IAAM,SAAN,cAAqB,OAAM;EASzB,cAAA;AACE,UAAK;AATG,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,YAAa;AACjB,SAAA,IAAI,YAAa;AACjB,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,aAAa;AAGzB,SAAK,YAAY;EACnB;;AAIK,IAAMC,UAAgCC,iBAAgB,MAAM,IAAI,OAAM,CAAE;AAExE,IAAM,SAAgCA,iBAAgB,MAAM,IAAI,OAAM,CAAE;;;AC3FzE,SAAUC,WAMd,OACA,UAAiC,CAAA,GAAE;AAEnC,QAAM,EAAE,KAAK,OAAO,UAAU,WAAW,QAAQ,QAAO,IAAK;AAC7D,QAAM,QAAQ,WAAsB,KAAK,KAAK,CAAC;AAC/C,MAAI,OAAO;AAAS,WAAO;AAC3B,SAAW,UAAU,KAAK;AAC5B;;;ACnDM,IAAOC,UAAP,cAAuC,IAAkB;EAG7D,YAAYC,OAAY;AACtB,UAAK;AAHP,WAAA,eAAA,MAAA,WAAA;;;;;;AAIE,SAAK,UAAUA;EACjB;EAES,IAAI,KAAW;AACtB,UAAM,QAAQ,MAAM,IAAI,GAAG;AAE3B,QAAI,MAAM,IAAI,GAAG,KAAK,UAAU,QAAW;AACzC,WAAK,OAAO,GAAG;AACf,YAAM,IAAI,KAAK,KAAK;IACtB;AAEA,WAAO;EACT;EAES,IAAI,KAAa,OAAY;AACpC,UAAM,IAAI,KAAK,KAAK;AACpB,QAAI,KAAK,WAAW,KAAK,OAAO,KAAK,SAAS;AAC5C,YAAM,WAAW,KAAK,KAAI,EAAG,KAAI,EAAG;AACpC,UAAI;AAAU,aAAK,OAAO,QAAQ;IACpC;AACA,WAAO;EACT;;;;AC7BF,IAAM,SAAS;EACb,UAAwB,IAAIC,QAAwB,IAAI;;AAGnD,IAAM,WAAW,OAAO;;;ACA/B,IAAM,eAA6B;AA0B7B,SAAU,OACd,OACA,UAA0B,CAAA,GAAE;AAE5B,QAAM,EAAE,SAAS,KAAI,IAAK;AAE1B,MAAI,CAAC,aAAa,KAAK,KAAK;AAC1B,UAAM,IAAIC,qBAAoB;MAC5B,SAAS;MACT,OAAO,IAAI,kBAAiB;KAC7B;AAEH,MAAI,QAAQ;AACV,QAAI,MAAM,YAAW,MAAO;AAAO;AACnC,QAAIC,UAAS,KAAgB,MAAM;AACjC,YAAM,IAAID,qBAAoB;QAC5B,SAAS;QACT,OAAO,IAAI,qBAAoB;OAChC;EACL;AACF;AA6BM,SAAUC,UAAS,SAAe;AACtC,MAAW,SAAS,IAAI,OAAO;AAAG,WAAc,SAAS,IAAI,OAAO;AAEpE,SAAO,SAAS,EAAE,QAAQ,MAAK,CAAE;AAEjC,QAAM,aAAa,QAAQ,UAAU,CAAC,EAAE,YAAW;AACnD,QAAM,OAAYC,WAAgB,WAAW,UAAU,GAAG,EAAE,IAAI,QAAO,CAAE;AAEzE,QAAM,aAAa,WAAW,MAAM,EAAE;AACtC,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,QAAI,KAAK,KAAK,CAAC,KAAM,KAAK,KAAK,WAAW,CAAC,GAAG;AAC5C,iBAAW,CAAC,IAAI,WAAW,CAAC,EAAG,YAAW;IAC5C;AACA,SAAK,KAAK,KAAK,CAAC,IAAK,OAAS,KAAK,WAAW,IAAI,CAAC,GAAG;AACpD,iBAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAG,YAAW;IACpD;EACF;AAEA,QAAM,SAAS,KAAK,WAAW,KAAK,EAAE,CAAC;AACvC,EAAO,SAAS,IAAI,SAAS,MAAM;AACnC,SAAO;AACT;AA+MM,IAAOC,uBAAP,cAIWC,WAAgB;EAG/B,YAAY,EAAE,SAAS,MAAK,GAAqC;AAC/D,UAAM,YAAY,OAAO,iBAAiB;MACxC;KACD;AALe,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAII,IAAO,oBAAP,cAAwCA,WAAS;EAGrD,cAAA;AACE,UAAM,4DAA4D;AAHlD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;AAII,IAAO,uBAAP,cAA2CA,WAAS;EAGxD,cAAA;AACE,UAAM,kDAAkD;AAHxC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;;;ACnVK,IAAMC,cAAa;AAInB,IAAMC,cAAa;AAInB,IAAMC,gBACX;AAEK,IAAM,UAAU,OAAO,KAAK,MAAM;AAClC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AAEtC,IAAM,UAAU,EAAE,OAAO,KAAK;AAC9B,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAElC,IAAM,WAAW,MAAM,KAAK;AAC5B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAMC,cAAa,MAAM,OAAO;;;ACrEvC,IAAM,eAAqC;EACzC,OAAO,IAAI,WAAU;EACrB,UAAU,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC;EACzC,UAAU;EACV,mBAAmB,oBAAI,IAAG;EAC1B,oBAAoB;EACpB,oBAAoB,OAAO;EAC3B,kBAAe;AACb,QAAI,KAAK,sBAAsB,KAAK;AAClC,YAAM,IAAI,gCAAgC;QACxC,OAAO,KAAK,qBAAqB;QACjC,OAAO,KAAK;OACb;EACL;EACA,eAAe,UAAQ;AACrB,QAAI,WAAW,KAAK,WAAW,KAAK,MAAM,SAAS;AACjD,YAAM,IAAIC,0BAAyB;QACjC,QAAQ,KAAK,MAAM;QACnB;OACD;EACL;EACA,kBAAkB,QAAM;AACtB,QAAI,SAAS;AAAG,YAAM,IAAI,oBAAoB,EAAE,OAAM,CAAE;AACxD,UAAM,WAAW,KAAK,WAAW;AACjC,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;EAClB;EACA,aAAa,UAAQ;AACnB,WAAO,KAAK,kBAAkB,IAAI,YAAY,KAAK,QAAQ,KAAK;EAClE;EACA,kBAAkB,QAAM;AACtB,QAAI,SAAS;AAAG,YAAM,IAAI,oBAAoB,EAAE,OAAM,CAAE;AACxD,UAAM,WAAW,KAAK,WAAW;AACjC,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;EAClB;EACA,YAAY,WAAS;AACnB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,QAAQ;AAC5B,WAAO,KAAK,MAAM,QAAQ;EAC5B;EACA,aAAa,QAAQ,WAAS;AAC5B,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,SAAS,CAAC;AACzC,WAAO,KAAK,MAAM,SAAS,UAAU,WAAW,MAAM;EACxD;EACA,aAAa,WAAS;AACpB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,QAAQ;AAC5B,WAAO,KAAK,MAAM,QAAQ;EAC5B;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,WAAO,KAAK,SAAS,UAAU,QAAQ;EACzC;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,YACG,KAAK,SAAS,UAAU,QAAQ,KAAK,KACtC,KAAK,SAAS,SAAS,WAAW,CAAC;EAEvC;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,WAAO,KAAK,SAAS,UAAU,QAAQ;EACzC;EACA,SAAS,MAAmB;AAC1B,SAAK,eAAe,KAAK,QAAQ;AACjC,SAAK,MAAM,KAAK,QAAQ,IAAI;AAC5B,SAAK;EACP;EACA,UAAU,OAAY;AACpB,SAAK,eAAe,KAAK,WAAW,MAAM,SAAS,CAAC;AACpD,SAAK,MAAM,IAAI,OAAO,KAAK,QAAQ;AACnC,SAAK,YAAY,MAAM;EACzB;EACA,UAAU,OAAa;AACrB,SAAK,eAAe,KAAK,QAAQ;AACjC,SAAK,MAAM,KAAK,QAAQ,IAAI;AAC5B,SAAK;EACP;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,KAAK;AAC5C,SAAK,YAAY;EACnB;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,SAAS,CAAC;AACjD,SAAK,SAAS,SAAS,KAAK,WAAW,GAAG,QAAQ,CAAC,UAAU;AAC7D,SAAK,YAAY;EACnB;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,KAAK;AAC5C,SAAK,YAAY;EACnB;EACA,WAAQ;AACN,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,YAAW;AAC9B,SAAK;AACL,WAAO;EACT;EACA,UAAU,QAAQC,OAAI;AACpB,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,aAAa,MAAM;AACtC,SAAK,YAAYA,SAAQ;AACzB,WAAO;EACT;EACA,YAAS;AACP,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,aAAY;AAC/B,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,IAAI,YAAS;AACX,WAAO,KAAK,MAAM,SAAS,KAAK;EAClC;EACA,YAAY,UAAQ;AAClB,UAAM,cAAc,KAAK;AACzB,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;AAChB,WAAO,MAAO,KAAK,WAAW;EAChC;EACA,SAAM;AACJ,QAAI,KAAK,uBAAuB,OAAO;AAAmB;AAC1D,UAAM,QAAQ,KAAK,aAAY;AAC/B,SAAK,kBAAkB,IAAI,KAAK,UAAU,QAAQ,CAAC;AACnD,QAAI,QAAQ;AAAG,WAAK;EACtB;;AA4BI,IAAO,sBAAP,cAA0CC,WAAS;EAGvD,YAAY,EAAE,OAAM,GAAsB;AACxC,UAAM,YAAY,MAAM,wBAAwB;AAHhC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;AAII,IAAOC,4BAAP,cAA+CD,WAAS;EAG5D,YAAY,EAAE,QAAQ,SAAQ,GAAwC;AACpE,UACE,cAAc,QAAQ,yCAAyC,MAAM,MAAM;AAJ7D,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAII,IAAO,kCAAP,cAAsDA,WAAS;EAGnE,YAAY,EAAE,OAAO,MAAK,GAAoC;AAC5D,UACE,6BAA6B,KAAK,wCAAwC,KAAK,MAAM;AAJvE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;;;ACRI,SAAUE,cAEd,OAAuB,QAA2C;AAClE,MAAI,MAAM,WAAW,OAAO;AAC1B,UAAM,IAAI,oBAAoB;MAC5B,gBAAgB,MAAM;MACtB,aAAa,OAAO;KACrB;AAEH,QAAM,OAAkB,CAAA;AACxB,WAAS,IAAI,GAAG,IAAK,MAAoB,QAAQ,KAAK;AACpD,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,QAAQ,OAAO,CAAC;AACtB,SAAK,KAAKA,cAAa,OAAO,MAAM,KAAK,CAAC;EAC5C;AACA,SAAW,OAAO,GAAG,IAAI;AAC3B;CAEA,SAAiBA,eAAY;AAe3B,WAAgBC,QACd,MACA,OACA,UAAU,OAAK;AAEf,QAAI,SAAS,WAAW;AACtB,YAAM,UAAU;AAChB,MAAQ,OAAO,OAAO;AACtB,aAAW,QACT,QAAQ,YAAW,GACnB,UAAU,KAAK,CAAC;IAEpB;AACA,QAAI,SAAS;AAAU,aAAWC,YAAW,KAAe;AAC5D,QAAI,SAAS;AAAS,aAAO;AAC7B,QAAI,SAAS;AACX,aAAW,QAAY,YAAY,KAAgB,GAAG,UAAU,KAAK,CAAC;AAExE,UAAM,WAAY,KAAgB,MAAeC,aAAY;AAC7D,QAAI,UAAU;AACZ,YAAM,CAAC,OAAO,UAAU,OAAO,KAAK,IAAI;AACxC,YAAMC,QAAO,OAAO,SAAS,IAAI,IAAI;AACrC,aAAW,WAAW,OAAiB;QACrC,MAAM,UAAU,KAAKA;QACrB,QAAQ,aAAa;OACtB;IACH;AAEA,UAAM,aAAc,KAAgB,MAAeC,WAAU;AAC7D,QAAI,YAAY;AACd,YAAM,CAAC,OAAOD,KAAI,IAAI;AACtB,UAAI,OAAO,SAASA,KAAK,OAAQ,MAAkB,SAAS,KAAK;AAC/D,cAAM,IAAIE,wBAAuB;UAC/B,cAAc,OAAO,SAASF,KAAK;UACnC;SACD;AACH,aAAW,SAAS,OAAkB,UAAU,KAAK,CAAC;IACxD;AAEA,UAAM,aAAc,KAAgB,MAAeG,WAAU;AAC7D,QAAI,cAAc,MAAM,QAAQ,KAAK,GAAG;AACtC,YAAM,CAAC,OAAO,SAAS,IAAI;AAC3B,YAAM,OAAkB,CAAA;AACxB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,aAAK,KAAKN,QAAO,WAAW,MAAM,CAAC,GAAG,IAAI,CAAC;MAC7C;AACA,UAAI,KAAK,WAAW;AAAG,eAAO;AAC9B,aAAW,OAAO,GAAG,IAAI;IAC3B;AAEA,UAAM,IAAI,iBAAiB,IAAc;EAC3C;AAnDgB,EAAAD,cAAA,SAAMC;AAoDxB,GAnEiBD,kBAAAA,gBAAY,CAAA,EAAA;AA0WvB,IAAOQ,0BAAP,cAA6CC,WAAS;EAE1D,YAAY,EACV,cACA,MAAK,GACoC;AACzC,UACE,kBAAkB,KAAK,WAAe,KACpC,KAAK,CACN,wCAAwC,YAAY,IAAI;AAR3C,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAUzB;;AA0BI,IAAO,sBAAP,cAA0CA,WAAS;EAEvD,YAAY,EACV,gBACA,YAAW,GACqC;AAChD,UACE;MACE;MACA,iCAAiC,cAAc;MAC/C,0BAA0B,WAAW;MACrC,KAAK,IAAI,CAAC;AAVE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYzB;;AAsCI,IAAO,mBAAP,cAAuCC,WAAS;EAEpD,YAAY,MAAY;AACtB,UAAM,UAAU,IAAI,6BAA6B;AAFjC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGzB;;;;AC3rBF,IAAM,eAAe;AAEf,SAAU,uBAAuB,eAA8B,WAAiB;AACpF,QAAM,MAAM,IAAI,IAAI,mCAAmC;AACvD,MAAI,aAAa,IAAI,WAAW,aAAa;AAC7C,MAAI,aAAa,IAAI,aAAa,SAAS;AAE3C,SAAO,IAAI,SAAQ;AACrB;AAEA,IAAM,+BAA+B;EACnC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAUK,IAAM,mBAAmB;EAO9B,0BAA0B,QAAgB,WAAiB;AACzD,QAAI,aAAa;AACjB,QAAI;AACF,YAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,mBAAa,IAAI,SAAS;IAC5B,SAAS,GAAG;AACV,mBAAa;IACf;AAEA,QAAI,YAAY;AACd,YAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,UAAI,CAAC,IAAI,aAAa,IAAI,WAAW,GAAG;AACtC,YAAI,aAAa,IAAI,aAAa,SAAS;MAC7C;AAEA,aAAO,IAAI,SAAQ;IACrB;AAEA,WAAO;EACT;EAEA,cAAc,SAAsB;AAClC,WAAO,oBAAoB,WAAW,mBAAmB;EAC3D;EAEA,kBAAkB,SAAsB;AACtC,QAAI,KAAK,cAAc,OAAO,GAAG;AAC/B,aAAO,QAAQ;IACjB;AAEA,WAAO,cAAc,MAAM;EAC7B;EAEA,iBAAiB,SAAsB;AACrC,QAAI,KAAK,cAAc,OAAO,GAAG;AAC/B,aAAO,QAAQ;IACjB;AAEA,WAAO,GAAG,cAAc,MAAM,GAAG,IAAI,QAAQ,EAAE;EACjD;EAGA,iBAAiB,aAA4B,eAA8B,WAAiB;AA1H9F;AA2HI,UAAM,iBAAgB,6BAAY,YAAZ,mBAAqB,YAArB,mBAA8B,SAA9B,mBAAqC;AAE3D,QAAI,6BAA6B,SAAS,aAAa,GAAG;AACxD,aAAO,uBAAuB,eAAe,SAAS;IACxD;AAEA,WAAO,iBAAiB;EAC1B;EAaA,kBACE,aACA,EAAE,wBAAwB,WAAW,cAAa,GAA2B;AAjJjF;AAmJI,UAAM,iBAAiB,KAAK,kBAAkB,WAAW;AACzD,UAAM,gBAAgB,KAAK,iBAAiB,WAAW;AAEvD,UAAM,wBAAuB,iBAAY,QAAQ,QAAQ,SAA5B,mBAAmC;AAChE,UAAM,cAAc,KAAK,iBAAiB,aAAa,eAAe,SAAS;AAE/E,UAAM,uBACJ,4DAAa,YAAb,mBAAuB,oBAAvB,mBAAwC,SAAxC,mBAA+C,OAAM;AACvD,UAAM,2BAAyB,oDAAgB,mBAAhB,mBAAgC,IAAI,OAAK,EAAE,SAAQ,CAAA;AAElF,UAAM,UAAU,CAAC,GAAG,wBAAwB,WAAW;AACvD,UAAM,sBAAsB,CAAC,GAAG,sBAAsB;AAEtD,QAAI,sBAAsB,CAAC,oBAAoB,SAAS,kBAAkB,GAAG;AAC3E,0BAAoB,KAAK,kBAAkB;IAC7C;AAEA,WAAO;MACL,GAAG;MACH;MACA;MACA,QAAQ;QACN,SAAS,YAAY,gBAAgB,YAAY,EAAE;QACnD,UAAU,iEAAyB,YAAY;;MAEjD,SAAS;QACP,GAAG,YAAY;QACf,SAAS;UACP,MAAM;;QAGR,cAAc;UACZ,MAAM;;;;EAId;EAYA,mBACE,cACA,EAAE,wBAAwB,WAAW,cAAa,GAA2B;AAE7E,WAAO,aAAa,IAAI,iBACtB,iBAAiB,kBAAkB,aAAa;MAC9C;MACA;MACA;KACD,CAAC;EAEN;EAEA,iBAAiB,aAA0B,WAAmB,eAA8B;AAC1F,UAAM,aAA8B,CAAA;AAEpC,mDAAe,QAAQ,YAAS;AAC9B,iBAAW,KAAK,KAAK,OAAO,KAAK,OAAO,MAAM,CAAC;IACjD;AAEA,QAAI,6BAA6B,SAAS,YAAY,aAAa,GAAG;AACpE,YAAM,cAAc,KAAK,iBAAiB,aAAa,YAAY,eAAe,SAAS;AAC3F,iBAAW,KACT,KAAK,aAAa;QAKhB,cAAc;UACZ,SAAS;YACP,gBAAgB;;;OAGrB,CAAC;IAEN;AAEA,WAAO,SAAS,UAAU;EAC5B;EAEA,sBAAsB,aAA0B,WAAmB,WAAoB;AACrF,QAAI,6BAA6B,SAAS,YAAY,aAAa,GAAG;AACpE,YAAM,cAAc,KAAK,iBAAiB,aAAa,YAAY,eAAe,SAAS;AAE3F,aAAO,SAAS,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC;IAChD;AAEA,WAAO;EACT;EAOA,sBAAsB,eAA4B;AAChD,WAAO;MACL,IAAI,cAAc,MAAM,GAAG,EAAE,CAAC;MAC9B;MACA,MAAM,cAAc;MACpB,gBAAgB,cAAc,MAAM,GAAG,EAAE,CAAC;MAC1C,gBAAgB;QACd,MAAM;QACN,UAAU;QACV,QAAQ;;MAEV,SAAS;QACP,SAAS;UACP,MAAM,CAAA;;;;EAId;EAMA,0BAA0B,oBAAgC;AAjR5D;AAkRI,UAAM,2BAA2B,YAAY,uBAAsB;AACnE,UAAM,eAAe,gBAAgB,4BAA2B;AAChE,UAAM,sBAAsB,MAAM,OAAK,qBAAgB,MAAM,WAAtB,mBAA8B,WAAU,CAAA,CAAE;AACjF,UAAM,YAAY,qEAA0B,MAAM,KAAK;AACvD,UAAM,uBAAuB,YAAY,oBAAoB,SAAS,SAAS,IAAI;AACnF,UAAM,cAAc,6CAAc,KAAK,QAAM,GAAG,kBAAkB;AAClE,UAAM,uBAAuB,wBAAwB,CAAC,eAAe;AAErE,QAAI,sBAAsB;AACxB,aAAO,KAAK,sBAAsB,wBAAwB;IAC5D;AAEA,QAAI,aAAa;AACf,aAAO;IACT;AAEA,QAAI,oBAAoB;AACtB,aAAO;IACT;AAEA,WAAO,6CAAe;EACxB;;;;AChRF,IAAM,wBAAwB;EAC5B,QAAQ;EACR,QAAQ;EACR,UAAU;EACV,QAAQ;;AAGV,IAAM,QAAQ,MAA8B;EAC1C,WAAW,EAAE,GAAG,sBAAqB;EACrC,aAAa,EAAE,GAAG,sBAAqB;CACxC;AAEM,IAAM,eAAe;EAC1B;EAEA,aAAiC,KAAQ,UAAoD;AAC3F,WAAO,aAAO,OAAO,KAAK,QAAQ;EACpC;EAEA,UAAU,UAAiD;AACzD,WAAO,UAAU,OAAO,MAAK;AAC3B,eAAS,KAAK;IAChB,CAAC;EACH;EAEA,mBAAmB,UAAkE;AACnF,WAAO,UAAU,MAAM,WAAW,MAAM,SAAS,MAAM,SAAS,CAAC;EACnE;EAEA,YAAmC,gBAAgC,UAAW;AAC5E,QAAI,UAAU;AACZ,YAAM,UAAU,cAAc,IAAI,IAAI,QAAQ;IAChD;EACF;EAEA,YAAmC,gBAA8B;AAC/D,WAAO,MAAM,UAAU,cAAc;EACvC;EAEA,cAAc,gBAAgC,YAAyB;AACrE,QAAI,YAAY;AACd,YAAM,YAAY,cAAc,IAAI;IACtC;EACF;EAEA,cAAc,gBAA0C;AACtD,QAAI,CAAC,gBAAgB;AACnB,aAAO;IACT;AAEA,WAAO,MAAM,YAAY,cAAc;EACzC;EAEA,QAAK;AACH,UAAM,YAAY,EAAE,GAAG,sBAAqB;AAC5C,UAAM,cAAc,EAAE,GAAG,sBAAqB;EAChD;EAEA,WAAW,gBAA8B;AACvC,UAAM,UAAU,cAAc,IAAI;AAClC,UAAM,YAAY,cAAc,IAAI;EACtC;;;;AC1EF,IAAY;CAAZ,SAAYC,qBAAkB;AAC5B,EAAAA,oBAAA,QAAA,IAAA;AACA,EAAAA,oBAAA,QAAA,IAAA;AACA,EAAAA,oBAAA,OAAA,IAAA;AACA,EAAAA,oBAAA,UAAA,IAAA;AACA,EAAAA,oBAAA,GAAA,IAAA;AACA,EAAAA,oBAAA,SAAA,IAAA;AACA,EAAAA,oBAAA,WAAA,IAAA;AACF,GARY,uBAAA,qBAAkB,CAAA,EAAA;;;ACoBvB,IAAM,gBAAgB;EAC3B,oBACE,YACA,aACA,UAAoB;AAEpB,UAAM,EAAE,cAAa,IAAK,kBAAkB;AAC5C,UAAM,SAAS,YAAY,iBAAgB;AAE3C,UAAM,sBAAsB,WAAW,0BAA0B,WAAW;AAC5E,UAAM,mBAAmB,WAAW,0BAA0B,QAAQ;AAEtE,UAAM,aAAa,WAAW,OAAO,eAAa,UAAU,SAAS,aAAa;AAClF,UAAM,YAAY,WAAW,OAAO,eAAa,UAAU,SAAS,WAAW;AAC/E,UAAM,WAAW,WAAW,OAAO,eAAa,UAAU,SAAS,UAAU;AAC7E,UAAM,WAAW,WAAW,OAAO,eAAa,UAAU,SAAS,UAAU;AAE7E,WAAO;MACL,QAAQ;MACR;MACA;MACA;MACA;MACA;MACA,aAAa;MACb,UAAU;;EAEd;EAEA,cAAc,WAAiC;AA3DjD;AA4DI,UAAM,QAAO,eAAU,SAAV,mBAAgB;AAE7B,UAAM,iBACJ,QAAQ,IAAI,KAAK,cAAc,MAAM,gBAAgB,KAAK,YAAU,OAAO,SAAS,IAAI;AAE1F,UAAM,iBACJ,QAAQ,UAAU,IAAI,KACtB,cAAc,MAAM,gBAAgB,KAAK,YACvC,YAAY,iBAAiB,OAAO,MAAM,UAAU,IAAI,CAAC;AAG7D,QAAI,UAAU,SAAS,YAAY;AACjC,UAAI,CAAC,eAAe,SAAQ,KAAM,UAAU,SAAS,kBAAkB;AACrE,eAAO;MACT;AAEA,UAAI,CAAC,QAAQ,CAAC,qBAAqB,eAAc,GAAI;AACnD,eAAO;MACT;AAEA,UAAI,kBAAkB,gBAAgB;AACpC,eAAO;MACT;IACF;AAEA,QAAI,UAAU,SAAS,gBAAgB,kBAAkB,iBAAiB;AACxE,aAAO;IACT;AAEA,WAAO;EACT;EAMA,uBAAoB;AAClB,UAAM,SAAS,MAAM,KAAK,gBAAgB,MAAM,OAAO,OAAM,CAAE;AAC/D,UAAM,oBAAoB,OAAO,KAAK,WAAQ;AAC5C,YAAM,cAAc,oBAAoB,eAAe,MAAM,SAAS;AAEtE,aAAO,gBAAgB,cAAc,aAAa;IACpD,CAAC;AAED,WAAO;EACT;EAMA,sBAAsB,EACpB,aACA,UACA,QAAAC,SACA,QACA,WACA,UACA,YACA,UACA,wBAAuB,gCAAkB,MAAM,aAAxB,mBAAkC,yBAAsB,CAAA,EAAE,GACjD;AAChC,UAAM,oBAAoB,cAAc,qBAAoB;AAC5D,UAAM,cAAc,kBAAkB,MAAM;AAE5C,UAAM,gBAAgB;MACpB,EAAE,MAAM,iBAAiB,WAAW,eAAe,CAAC,kBAAiB;MACrE,EAAE,MAAM,UAAU,WAAW,OAAO,SAAS,EAAC;MAC9C,EAAE,MAAM,YAAY,WAAW,CAAC,GAAG,UAAU,GAAG,WAAW,GAAG,UAAU,EAAE,SAAS,EAAC;MACpF,EAAE,MAAM,YAAY,WAAW,SAAS,SAAS,EAAC;MAClD,EAAE,MAAM,UAAU,WAAWA,WAAUA,QAAO,SAAS,EAAC;MACxD,EAAE,MAAM,YAAY,WAAW,SAAS,SAAS,EAAC;MAClD,EAAE,MAAM,eAAe,WAAW,YAAY,SAAS,EAAC;;AAG1D,UAAM,oBAAoB,cAAc,OAAO,YAAU,OAAO,SAAS;AAEzE,UAAM,wBAAwB,IAAI,IAAI,kBAAkB,IAAI,YAAU,OAAO,IAAI,CAAC;AAElF,UAAM,wBAAwB,qBAC3B,OAAO,UAAQ,sBAAsB,IAAI,IAAI,CAAC,EAC9C,IAAI,WAAS,EAAE,MAAM,WAAW,KAAI,EAAG;AAE1C,UAAM,sBAAsB,kBAAkB,OAAO,CAAC,EAAE,MAAM,qBAAoB,MAAM;AACtF,YAAM,0BAA0B,sBAAsB,KACpD,CAAC,EAAE,MAAM,yBAAwB,MAAO,6BAA6B,oBAAoB;AAG3F,aAAO,CAAC;IACV,CAAC;AAED,WAAO,MAAM,KACX,IAAI,IAAI,CAAC,GAAG,uBAAuB,GAAG,mBAAmB,EAAE,IAAI,CAAC,EAAE,KAAI,MAAO,IAAI,CAAC,CAAC;EAEvF;;;;AC1IK,IAAM,aAAa;EACxB,0BAA0B,SAAmB;AAC3C,UAAM,aAAa,kBAAkB,MAAM,gBACvC,oBAAoB,MAAM,aAC1B,CAAA;AACJ,UAAM,SAAS,YAAY,iBAAgB;AAE3C,UAAM,iBAAiB,WACpB,IAAI,eAAU;AAxBrB;AAwBwB,6BAAU,SAAV,mBAAgB;KAAI,EACrC,OAAO,OAAO;AAEjB,UAAM,cAAc,OAAO,IAAI,YAAU,OAAO,IAAI,EAAE,OAAO,OAAO;AACpE,UAAM,WAAW,eAAe,OAAO,WAAW;AAClD,QAAI,SAAS,SAAS,oBAAoB,KAAK,eAAe,SAAQ,GAAI;AACxE,YAAMC,SAAQ,SAAS,QAAQ,oBAAoB;AACnD,eAASA,MAAK,IAAI;IACpB;AACA,UAAM,WAAW,QAAQ,OAAO,YAAU,CAAC,SAAS,SAAS,OAAO,iCAAQ,IAAI,CAAC,CAAC;AAElF,WAAO;EACT;EAEA,yBAAyB,SAAmB;AAC1C,UAAM,aAAa,oBAAoB,MAAM,WAAW,OACtD,eAAa,UAAU,SAAS,eAAe,UAAU,SAAS,UAAU;AAE9E,UAAM,SAAS,YAAY,iBAAgB;AAE3C,UAAM,eAAe,WAAW,IAAI,eAAa,UAAU,UAAU;AAErE,UAAM,YAAY,OAAO,IAAI,YAAU,OAAO,EAAE;AAEhD,UAAM,SAAS,aAAa,OAAO,SAAS;AAE5C,UAAM,WAAW,QAAQ,OAAO,YAAU,CAAC,OAAO,SAAS,iCAAQ,EAAE,CAAC;AAEtE,WAAO;EACT;EAEA,0BAA0B,SAAmB;AAC3C,UAAM,eAAe,KAAK,0BAA0B,OAAO;AAC3D,UAAM,gBAAgB,KAAK,yBAAyB,YAAY;AAEhE,WAAO;EACT;EAEA,uBAAuB,SAAmB;AACxC,UAAM,EAAE,WAAU,IAAK,oBAAoB;AAC3C,UAAM,sBAAsB,WACzB,OAAO,OAAK,EAAE,SAAS,WAAW,EAClC,OAAgC,CAAC,MAAM,QAAO;AAlErD;AAmEQ,UAAI,GAAC,SAAI,SAAJ,mBAAU,OAAM;AACnB,eAAO;MACT;AACA,WAAK,IAAI,KAAK,IAAI,IAAI;AAEtB,aAAO;IACT,GAAG,CAAA,CAAE;AAEP,UAAM,uBAAuC,QAAQ,IAAI,aAAW;MAClE,GAAG;MACH,WAAW,QAAQ,OAAO,IAAI,KAAK,QAAQ,oBAAoB,OAAO,QAAQ,EAAE,CAAC;MACjF;AAEF,UAAM,gBAAgB,qBAAqB,KACzC,CAAC,GAAG,MAAM,OAAO,EAAE,SAAS,IAAI,OAAO,EAAE,SAAS,CAAC;AAGrD,WAAO;EACT;EAEA,sBAAsB,WAAiC,aAAwB;AAvFjF;AAwFI,UAAM,sBACJ,uCAAW,0BAAuB,uBAAkB,MAAM,aAAxB,mBAAkC;AACtE,UAAM,aAAa,eAAe,oBAAoB,MAAM;AAE5D,QAAI,oBAAoB;AACtB,aAAO;IACT;AAEA,UAAM,EAAE,UAAU,UAAS,IAAK,cAAc,oBAC5C,YACA,cAAc,MAAM,aACpB,cAAc,MAAM,QAAQ;AAG9B,UAAM,gBAAgB,SAAS,OAAO,cAAc,aAAa;AACjE,UAAM,iBAAiB,UAAU,OAAO,cAAc,aAAa;AAEnE,QAAI,cAAc,UAAU,eAAe,QAAQ;AACjD,aAAO,CAAC,UAAU,SAAS,QAAQ;IACrC;AAEA,WAAOC,eAAc;EACvB;;", "names": ["Constants<PERSON><PERSON>", "Constants<PERSON><PERSON>", "Constants<PERSON><PERSON>", "size", "shouldRetry", "uid", "_a", "uid", "fetch", "transport", "transports_", "sample", "body", "response", "wait", "body", "k", "isBytes", "rotl", "toBytes", "wrapConstructor", "toBytes", "toBytes", "isLE", "_32n", "HashMD", "buffer", "toBytes", "state", "Rho", "Id", "Pi", "idxL", "idxR", "k", "shifts", "shiftsL", "shiftsR", "Kl", "Kr", "f", "R_BUF", "RIPEMD160", "HashMD", "rotl", "ripemd160", "wrapConstructor", "state", "toBytes", "wrapConstructor", "HashMD", "A", "sha256", "wrapConstructor", "keccak256", "LruMap", "size", "LruMap", "InvalidAddressError", "checksum", "keccak256", "InvalidAddressError", "BaseError", "arrayRegex", "bytesRegex", "integerRegex", "maxUint256", "PositionOutOfBoundsError", "size", "BaseError", "PositionOutOfBoundsError", "encodePacked", "encode", "fromString", "integerRegex", "size", "bytesRegex", "BytesSizeMismatchError", "arrayRegex", "BytesSizeMismatchError", "BaseError", "BaseError", "SocialProviderEnum", "custom", "index", "Constants<PERSON><PERSON>"]}