/**
 * <PERSON><PERSON>t to migrate existing orders to use user IDs instead of wallet addresses
 *
 * This script will update all orders in the database to use user IDs instead of wallet addresses.
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const User = require('../models/User');
const { sequelize } = require('../config/postgresql');
const Order = require('../models/postgresql/order');
const logger = require('../utils/logger');

// Create a log file
const logFile = path.join(__dirname, 'migrate_orders_log.txt');
fs.writeFileSync(logFile, `Migrate Orders Log - ${new Date().toISOString()}\n\n`);

// Function to append to log file
function logToFile(message) {
  fs.appendFileSync(logFile, message + '\n');
  console.log(message);
}

async function migrateOrdersToUserIds() {
  try {
    logToFile('Starting migration of orders to use user IDs');
    logger.info('Starting migration of orders to use user IDs');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    logToFile('Connected to MongoDB');
    logger.info('Connected to MongoDB');

    // Get all users from MongoDB
    const users = await User.find({});
    logToFile(`Found ${users.length} users in MongoDB`);
    logger.info(`Found ${users.length} users in MongoDB`);

    // Create a map of wallet addresses to user IDs
    const walletToUserIdMap = {};
    for (const user of users) {
      if (user.walletAddress) {
        walletToUserIdMap[user.walletAddress] = user._id.toString();
        logToFile(`Mapped wallet ${user.walletAddress} to user ID ${user._id}`);
      }
    }

    // Get all orders from PostgreSQL
    const orders = await Order.findAll();
    logToFile(`Found ${orders.length} orders in PostgreSQL`);
    logger.info(`Found ${orders.length} orders in PostgreSQL`);

    // Update orders to use user IDs
    let updatedCount = 0;
    let skippedCount = 0;

    for (const order of orders) {
      logToFile(`Processing order ${order.id}`);
      if (order.walletAddress && walletToUserIdMap[order.walletAddress]) {
        // Update the order to use the user ID
        order.userId = walletToUserIdMap[order.walletAddress];
        await order.save();
        logToFile(`Updated order ${order.id} to use user ID ${order.userId}`);
        updatedCount++;
      } else {
        logToFile(`Skipped order ${order.id} - no wallet address or no matching user`);
        // Skip orders without a wallet address or without a matching user
        skippedCount++;
      }
    }

    logToFile(`Migration complete. Updated ${updatedCount} orders, skipped ${skippedCount} orders`);
    logger.info(`Migration complete. Updated ${updatedCount} orders, skipped ${skippedCount} orders`);

    // Close connections
    await mongoose.disconnect();
    await sequelize.close();

    logToFile('Database connections closed');
    logger.info('Database connections closed');

    logToFile(`Log file written to: ${logFile}`);
  } catch (error) {
    logger.error(`Error in migrateOrdersToUserIds: ${error.message}`);

    // Close connections
    try {
      await mongoose.disconnect();
      await sequelize.close();
    } catch (closeError) {
      logger.error(`Error closing database connections: ${closeError.message}`);
    }

    process.exit(1);
  }
}

// Run the script
logToFile('Starting script...');

try {
  migrateOrdersToUserIds()
    .then(() => {
      logToFile('Script completed successfully');
      logger.info('Script completed successfully');
      process.exit(0);
    })
    .catch(error => {
      logToFile(`Script failed: ${error.message}`);
      logger.error(`Script failed: ${error.message}`);
      process.exit(1);
    });
} catch (error) {
  logToFile(`Uncaught error: ${error.message}`);
  logToFile(error.stack);
  process.exit(1);
}
