/**
 * Health Check Routes
 * 
 * These routes provide health check endpoints for monitoring the application.
 */

const express = require('express');
const router = express.Router();
const database = require('../config/database');
const os = require('os');
const { version } = require('../../package.json');

// Basic health check endpoint
router.get('/', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version
  });
});

// Detailed health check endpoint
router.get('/detailed', async (req, res) => {
  try {
    // Check database health
    const dbHealth = await database.checkDatabaseHealth();
    
    // System information
    const systemInfo = {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem()
      },
      cpu: os.cpus(),
      uptime: process.uptime(),
      loadAvg: os.loadavg()
    };
    
    // Application information
    const appInfo = {
      version,
      environment: process.env.NODE_ENV || 'development',
      startTime: new Date(Date.now() - process.uptime() * 1000).toISOString()
    };
    
    res.json({
      status: dbHealth.status,
      timestamp: new Date().toISOString(),
      databases: dbHealth.databases,
      system: systemInfo,
      application: appInfo
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

module.exports = router;
