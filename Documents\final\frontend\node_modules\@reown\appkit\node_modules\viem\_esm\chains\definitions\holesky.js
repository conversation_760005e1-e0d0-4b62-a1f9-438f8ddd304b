import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const holesky = /*#__PURE__*/ defineChain({
    id: 17000,
    name: '<PERSON><PERSON>',
    nativeCurrency: { name: '<PERSON><PERSON> Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://ethereum-holesky-rpc.publicnode.com'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Etherscan',
            url: 'https://holesky.etherscan.io',
            apiUrl: 'https://api-holesky.etherscan.io/api',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 77,
        },
        ensRegistry: {
            address: '******************************************',
            blockCreated: 801613,
        },
        ensUniversalResolver: {
            address: '******************************************',
            blockCreated: 973484,
        },
    },
    testnet: true,
});
//# sourceMappingURL=holesky.js.map