/**
 * Database Service
 * Centralizes database connections and provides fallback mechanisms for development
 */

const mongoose = require('mongoose');
// const { Pool } = require('pg'); // REMOVED - PostgreSQL no longer used
const { IS_PROD, IS_DEV, config, isFeatureEnabled } = require('../config/environment');

class DatabaseService {
  constructor() {
    this.mongoConnected = false;
    // this.pgConnected = false; // REMOVED - PostgreSQL no longer used
    this.mongoClient = null;
    // this.pgPool = null; // REMOVED - PostgreSQL no longer used
    this.mockMode = false;
  }

  /**
   * Initialize database connections
   */
  async initialize() {
    try {
      // Connect to MongoDB
      await this.connectMongo();

      // Connect to PostgreSQL
      await this.connectPostgres();

      return true;
    } catch (error) {
      console.error('Failed to initialize database service:', error);

      // In development, we can continue with mock functionality
      if (!IS_PROD && config.database.allowMockFallback) {
        console.warn('Continuing with mock database for development');
        this._initializeMockMode();
        return true;
      }

      return false;
    }
  }

  /**
   * Connect to MongoDB with retry logic
   */
  async connectMongo() {
    try {
      // Get MongoDB connection string from environment
      const mongoUri = process.env.MONGODB_URI;

      if (!mongoUri) {
        throw new Error('MONGODB_URI environment variable not set');
      }

      console.log('Initializing robust MongoDB connection...');
      // Mask sensitive information in the connection string
      const maskedUri = mongoUri.replace(/\/\/([^:]+):([^@]+)@/, '\/\/$1:<credentials>@');
      console.log(`Using connection URI: ${maskedUri}`);

      // Set mongoose options with increased timeouts
      const mongooseOptions = {
        // Removed deprecated options: useNewUrlParser, useUnifiedTopology
        serverSelectionTimeoutMS: 30000,  // Increase server selection timeout
        socketTimeoutMS: 45000,           // Increase socket timeout
        connectTimeoutMS: 30000,          // Increase connection timeout
        maxPoolSize: 10                   // Increase connection pool size
      };

      // Implement retry logic
      const maxRetries = 5;
      let retryCount = 0;
      let connected = false;

      while (!connected && retryCount < maxRetries) {
        try {
          retryCount++;
          console.log(`MongoDB connection attempt ${retryCount}/${maxRetries}...`);

          // Connect
          await mongoose.connect(mongoUri, mongooseOptions);

          // Test the connection with a ping
          await mongoose.connection.db.admin().ping();
          console.log('MongoDB connection verified with ping');

          connected = true;
        } catch (connectionError) {
          console.error(`MongoDB connection attempt ${retryCount} failed:`, connectionError.message);

          if (retryCount < maxRetries) {
            // Wait before retrying (exponential backoff)
            const waitTime = Math.min(1000 * Math.pow(2, retryCount), 10000);
            console.log(`Waiting ${waitTime}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
          }
        }
      }

      if (!connected) {
        throw new Error(`Failed to connect to MongoDB after ${maxRetries} attempts`);
      }

      // Set connection status
      this.mongoConnected = true;
      this.mongoClient = mongoose.connection;

      console.log(`MongoDB connected successfully after ${retryCount} attempt(s)`);

      // Set up connection event handlers
      mongoose.connection.on('error', (error) => {
        console.error('MongoDB connection error:', error);
        this.mongoConnected = false;

        // In development, switch to mock mode if connection fails
        if (!IS_PROD && config.database.allowMockFallback && !this.mockMode) {
          console.warn('Switching to mock database mode due to MongoDB connection error');
          this._initializeMockMode();
        }
      });

      mongoose.connection.on('disconnected', () => {
        console.log('MongoDB disconnected');
        this.mongoConnected = false;

        // In development, switch to mock mode if connection fails
        if (!IS_PROD && config.database.allowMockFallback && !this.mockMode) {
          console.warn('Switching to mock database mode due to MongoDB disconnection');
          this._initializeMockMode();
        }
      });

      return true;
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);

      // In development, we can continue with mock functionality
      if (!IS_PROD && config.database.allowMockFallback) {
        console.warn('Failed to connect to MongoDB, will use mock data for development');
        return false;
      }

      throw error;
    }
  }

  /**
   * Connect to PostgreSQL - DISABLED (PostgreSQL removed)
   */
  async connectPostgres() {
    // PostgreSQL removed - using mock implementation
    console.log('PostgreSQL connection skipped (PostgreSQL removed from project)');
    this.mockMode = true;
    return true;
  }

  /**
   * Initialize mock mode for development
   */
  _initializeMockMode() {
    if (this.mockMode) {
      return; // Already in mock mode
    }

    this.mockMode = true;
    console.log('Database service initialized in MOCK MODE');

    // Create in-memory data stores
    this._createMockDataStores();
  }

  /**
   * Create mock data stores for development
   */
  _createMockDataStores() {
    // Create in-memory collections
    this.mockCollections = {
      users: [],
      tokens: [],
      orders: [],
      trades: [],
      wallets: []
    };

    // Add some mock data
    this._addMockData();
  }

  /**
   * Add mock data for development
   */
  _addMockData() {
    // Add mock users
    this.mockCollections.users.push({
      _id: 'user1',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'user',
      createdAt: new Date()
    });

    // Add mock tokens
    this.mockCollections.tokens.push({
      _id: 'token1',
      name: 'Test Token',
      symbol: 'TEST',
      totalSupply: 1000000,
      decimals: 9,
      mintAddress: 'mock-mint-address-1',
      creator: 'user1',
      createdAt: new Date()
    });

    // Add mock wallets
    this.mockCollections.wallets.push({
      _id: 'wallet1',
      userId: 'user1',
      address: 'mock-wallet-address-1',
      balance: 1000,
      createdAt: new Date()
    });

    console.log('Added mock data for development');
  }

  /**
   * Get MongoDB connection
   */
  getMongo() {
    if (this.mockMode) {
      console.warn('Using mock MongoDB connection');
      return {
        isMock: true,
        connection: this.mockCollections
      };
    }

    if (!this.mongoConnected) {
      throw new Error('Not connected to MongoDB');
    }

    return {
      isMock: false,
      connection: this.mongoClient
    };
  }

  /**
   * Get PostgreSQL connection - DISABLED (PostgreSQL removed)
   */
  getPostgres() {
    // PostgreSQL removed - always return mock
    console.warn('Using mock PostgreSQL connection (PostgreSQL removed)');
    return {
      isMock: true,
      connection: this.mockCollections || {}
    };
  }

  /**
   * Check if we're in mock mode
   */
  isMockMode() {
    return this.mockMode;
  }

  /**
   * Close all database connections
   */
  async close() {
    try {
      // Close MongoDB connection
      if (this.mongoConnected) {
        await mongoose.disconnect();
        this.mongoConnected = false;
        console.log('MongoDB connection closed');
      }

      // PostgreSQL connection removed
      // if (this.pgConnected) {
      //   await this.pgPool.end();
      //   this.pgConnected = false;
      //   console.log('PostgreSQL connection closed');
      // }

      return true;
    } catch (error) {
      console.error('Error closing database connections:', error);
      return false;
    }
  }
}

// Create and export singleton instance
const databaseService = new DatabaseService();
module.exports = databaseService;
