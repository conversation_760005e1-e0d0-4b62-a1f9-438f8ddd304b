/**
 * Database Service
 * Centralizes database connections and provides fallback mechanisms for development
 */

const mongoose = require('mongoose');
const { Pool } = require('pg');
const { IS_PROD, IS_DEV, config, isFeatureEnabled } = require('../config/environment');

class DatabaseService {
  constructor() {
    this.mongoConnected = false;
    this.pgConnected = false;
    this.mongoClient = null;
    this.pgPool = null;
    this.mockMode = false;
  }

  /**
   * Initialize database connections
   */
  async initialize() {
    try {
      // Connect to MongoDB
      await this.connectMongo();

      // Connect to PostgreSQL
      await this.connectPostgres();

      return true;
    } catch (error) {
      console.error('Failed to initialize database service:', error);

      // In development, we can continue with mock functionality
      if (!IS_PROD && config.database.allowMockFallback) {
        console.warn('Continuing with mock database for development');
        this._initializeMockMode();
        return true;
      }

      return false;
    }
  }

  /**
   * Connect to MongoDB with retry logic
   */
  async connectMongo() {
    try {
      // Get MongoDB connection string from environment
      const mongoUri = process.env.MONGODB_URI;

      if (!mongoUri) {
        throw new Error('MONGODB_URI environment variable not set');
      }

      console.log('Initializing robust MongoDB connection...');
      // Mask sensitive information in the connection string
      const maskedUri = mongoUri.replace(/\/\/([^:]+):([^@]+)@/, '\/\/$1:<credentials>@');
      console.log(`Using connection URI: ${maskedUri}`);

      // Set mongoose options with increased timeouts
      const mongooseOptions = {
        // Removed deprecated options: useNewUrlParser, useUnifiedTopology
        serverSelectionTimeoutMS: 30000,  // Increase server selection timeout
        socketTimeoutMS: 45000,           // Increase socket timeout
        connectTimeoutMS: 30000,          // Increase connection timeout
        maxPoolSize: 10                   // Increase connection pool size
      };

      // Implement retry logic
      const maxRetries = 5;
      let retryCount = 0;
      let connected = false;

      while (!connected && retryCount < maxRetries) {
        try {
          retryCount++;
          console.log(`MongoDB connection attempt ${retryCount}/${maxRetries}...`);

          // Connect
          await mongoose.connect(mongoUri, mongooseOptions);

          // Test the connection with a ping
          await mongoose.connection.db.admin().ping();
          console.log('MongoDB connection verified with ping');

          connected = true;
        } catch (connectionError) {
          console.error(`MongoDB connection attempt ${retryCount} failed:`, connectionError.message);

          if (retryCount < maxRetries) {
            // Wait before retrying (exponential backoff)
            const waitTime = Math.min(1000 * Math.pow(2, retryCount), 10000);
            console.log(`Waiting ${waitTime}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
          }
        }
      }

      if (!connected) {
        throw new Error(`Failed to connect to MongoDB after ${maxRetries} attempts`);
      }

      // Set connection status
      this.mongoConnected = true;
      this.mongoClient = mongoose.connection;

      console.log(`MongoDB connected successfully after ${retryCount} attempt(s)`);

      // Set up connection event handlers
      mongoose.connection.on('error', (error) => {
        console.error('MongoDB connection error:', error);
        this.mongoConnected = false;

        // In development, switch to mock mode if connection fails
        if (!IS_PROD && config.database.allowMockFallback && !this.mockMode) {
          console.warn('Switching to mock database mode due to MongoDB connection error');
          this._initializeMockMode();
        }
      });

      mongoose.connection.on('disconnected', () => {
        console.log('MongoDB disconnected');
        this.mongoConnected = false;

        // In development, switch to mock mode if connection fails
        if (!IS_PROD && config.database.allowMockFallback && !this.mockMode) {
          console.warn('Switching to mock database mode due to MongoDB disconnection');
          this._initializeMockMode();
        }
      });

      return true;
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);

      // In development, we can continue with mock functionality
      if (!IS_PROD && config.database.allowMockFallback) {
        console.warn('Failed to connect to MongoDB, will use mock data for development');
        return false;
      }

      throw error;
    }
  }

  /**
   * Connect to PostgreSQL
   */
  async connectPostgres() {
    try {
      // First try to use the POSTGRESQL_URI environment variable
      const postgresqlUri = process.env.POSTGRESQL_URI;
      let pgConfig;

      // Add more detailed logging
      console.log('Connecting to PostgreSQL...');
      console.log(`Connection string: ${postgresqlUri ? postgresqlUri.replace(/:[^:]*@/, ':****@') : 'Not provided'}`);
      console.log(`Host: ${process.env.POSTGRES_HOST}, Database: ${process.env.POSTGRES_DB}, User: ${process.env.POSTGRES_USER}`);
      console.log(`SSL enabled: ${process.env.POSTGRES_SSL === 'true' ? 'Yes' : 'No'}`);

      if (postgresqlUri) {
        console.log('Using POSTGRESQL_URI for connection');
        // Use the connection string directly
        pgConfig = {
          connectionString: postgresqlUri,
          ssl: {
            rejectUnauthorized: false // Required for Render PostgreSQL
          }
        };
      } else {
        // Fall back to legacy configuration
        console.log('Using legacy PostgreSQL configuration');
        pgConfig = {
          user: process.env.POSTGRES_USER,
          password: process.env.POSTGRES_PASSWORD,
          host: process.env.POSTGRES_HOST,
          port: process.env.POSTGRES_PORT || 5432,
          database: process.env.POSTGRES_DB,
          ssl: process.env.POSTGRES_SSL === 'true' ? { rejectUnauthorized: false } : false
        };
      }

      // Check if PostgreSQL is configured
      const isPgConfigured = postgresqlUri || (pgConfig.user && pgConfig.password && pgConfig.host && pgConfig.database);

      if (!isPgConfigured) {
        console.warn('PostgreSQL not configured, skipping connection');
        return false;
      }

      // Connect to PostgreSQL
      console.log('Connecting to PostgreSQL...');
      console.log(`Connection details: ${postgresqlUri ? 'Using connection string' : `Host: ${pgConfig.host}, Database: ${pgConfig.database}`}`);
      console.log(`SSL enabled: ${pgConfig.ssl ? 'Yes' : 'No'}`);

      // Create pool
      this.pgPool = new Pool(pgConfig);

      // Test connection
      const client = await this.pgPool.connect();

      // Get server version to confirm connection
      const versionResult = await client.query('SELECT version()');
      console.log(`PostgreSQL server version: ${versionResult.rows[0].version}`);

      client.release();

      // Set connection status
      this.pgConnected = true;

      console.log('Connected to PostgreSQL successfully');

      // Set up error handler
      this.pgPool.on('error', (error) => {
        console.error('PostgreSQL connection error:', error);
        this.pgConnected = false;

        // In development, switch to mock mode if connection fails
        if (!IS_PROD && config.database.allowMockFallback && !this.mockMode) {
          console.warn('Switching to mock database mode due to PostgreSQL connection error');
          this._initializeMockMode();
        }
      });

      return true;
    } catch (error) {
      console.error('Failed to connect to PostgreSQL:', error);
      console.error(`Error details: ${JSON.stringify(error, null, 2)}`);

      // Log connection details for troubleshooting (masking password)
      if (postgresqlUri) {
        const maskedUri = postgresqlUri.replace(/:[^:]*@/, ':****@');
        console.error(`Connection string used: ${maskedUri}`);
      } else {
        console.error(`Host: ${pgConfig.host}, Database: ${pgConfig.database}, User: ${pgConfig.user}`);
        console.error(`SSL config: ${JSON.stringify(pgConfig.ssl)}`);
      }

      // In development, we can continue with mock functionality
      if (!IS_PROD && config.database.allowMockFallback) {
        console.warn('Failed to connect to PostgreSQL, will use mock data for development');
        return false;
      }

      // In production, we might still want to continue if MongoDB is connected
      if (IS_PROD && this.mongoConnected) {
        console.warn('PostgreSQL connection failed, but MongoDB is connected. Continuing with limited functionality.');
        return false;
      }

      throw error;
    }
  }

  /**
   * Initialize mock mode for development
   */
  _initializeMockMode() {
    if (this.mockMode) {
      return; // Already in mock mode
    }

    this.mockMode = true;
    console.log('Database service initialized in MOCK MODE');

    // Create in-memory data stores
    this._createMockDataStores();
  }

  /**
   * Create mock data stores for development
   */
  _createMockDataStores() {
    // Create in-memory collections
    this.mockCollections = {
      users: [],
      tokens: [],
      orders: [],
      trades: [],
      wallets: []
    };

    // Add some mock data
    this._addMockData();
  }

  /**
   * Add mock data for development
   */
  _addMockData() {
    // Add mock users
    this.mockCollections.users.push({
      _id: 'user1',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'user',
      createdAt: new Date()
    });

    // Add mock tokens
    this.mockCollections.tokens.push({
      _id: 'token1',
      name: 'Test Token',
      symbol: 'TEST',
      totalSupply: 1000000,
      decimals: 9,
      mintAddress: 'mock-mint-address-1',
      creator: 'user1',
      createdAt: new Date()
    });

    // Add mock wallets
    this.mockCollections.wallets.push({
      _id: 'wallet1',
      userId: 'user1',
      address: 'mock-wallet-address-1',
      balance: 1000,
      createdAt: new Date()
    });

    console.log('Added mock data for development');
  }

  /**
   * Get MongoDB connection
   */
  getMongo() {
    if (this.mockMode) {
      console.warn('Using mock MongoDB connection');
      return {
        isMock: true,
        connection: this.mockCollections
      };
    }

    if (!this.mongoConnected) {
      throw new Error('Not connected to MongoDB');
    }

    return {
      isMock: false,
      connection: this.mongoClient
    };
  }

  /**
   * Get PostgreSQL connection
   */
  getPostgres() {
    if (this.mockMode) {
      console.warn('Using mock PostgreSQL connection');
      return {
        isMock: true,
        connection: this.mockCollections
      };
    }

    if (!this.pgConnected) {
      throw new Error('Not connected to PostgreSQL');
    }

    return {
      isMock: false,
      connection: this.pgPool
    };
  }

  /**
   * Check if we're in mock mode
   */
  isMockMode() {
    return this.mockMode;
  }

  /**
   * Close all database connections
   */
  async close() {
    try {
      // Close MongoDB connection
      if (this.mongoConnected) {
        await mongoose.disconnect();
        this.mongoConnected = false;
        console.log('MongoDB connection closed');
      }

      // Close PostgreSQL connection
      if (this.pgConnected) {
        await this.pgPool.end();
        this.pgConnected = false;
        console.log('PostgreSQL connection closed');
      }

      return true;
    } catch (error) {
      console.error('Error closing database connections:', error);
      return false;
    }
  }
}

// Create and export singleton instance
const databaseService = new DatabaseService();
module.exports = databaseService;
