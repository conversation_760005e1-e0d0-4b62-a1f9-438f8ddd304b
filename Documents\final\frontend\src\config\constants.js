// API Configuration
export const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// WebSocket Configuration  
export const SOCKETIO_URL = process.env.REACT_APP_SOCKETIO_URL || 'http://localhost:3001';

// Solana Configuration
export const SOLANA_RPC_URL = process.env.REACT_APP_SOLANA_RPC_URL || 'https://api.devnet.solana.com';

// App Configuration
export const APP_NAME = 'WB-Swap';
export const APP_VERSION = '1.0.0';

// Trading Configuration
export const DEFAULT_SLIPPAGE = 0.5; // 0.5%
export const MAX_SLIPPAGE = 50; // 50%

// Token Configuration
export const DEFAULT_TOKEN_DECIMALS = 9;
export const MIN_TOKEN_SUPPLY = 1000;
export const MAX_TOKEN_SUPPLY = 1000000000;

// Bonding Curve Configuration
export const BONDING_CURVE_TYPES = {
  LINEAR: 'linear',
  EXPONENTIAL: 'exponential',
  LOGARITHMIC: 'logarithmic'
};

export const DEFAULT_RESERVE_RATIO = 50; // 50%
export const MIN_RESERVE_RATIO = 10; // 10%
export const MAX_RESERVE_RATIO = 90; // 90%

// Fee Configuration
export const TRADING_FEE_PERCENTAGE = 0.25; // 0.25%
export const TOKEN_CREATION_FEE = 0.1; // 0.1 SOL

// UI Configuration
export const ITEMS_PER_PAGE = 20;
export const CHART_UPDATE_INTERVAL = 5000; // 5 seconds

// Validation
export const USERNAME_MIN_LENGTH = 3;
export const USERNAME_MAX_LENGTH = 20;
export const PASSWORD_MIN_LENGTH = 8;
export const TOKEN_NAME_MAX_LENGTH = 50;
export const TOKEN_SYMBOL_MAX_LENGTH = 10;
export const TOKEN_DESCRIPTION_MAX_LENGTH = 500;
