const { Redis } = require('@upstash/redis');

// Initialize Upstash Redis client
let upstashClient = null;

// In production, Redis credentials are required
if (!process.env.UPSTASH_REDIS_REST_URL || !process.env.UPSTASH_REDIS_REST_TOKEN) {
  if (process.env.NODE_ENV === 'production') {
    console.error('CRITICAL: Redis credentials not set in production environment!');
    console.error('UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN must be set in production.');
    throw new Error('Redis credentials not configured for production environment');
  } else {
    console.warn('Redis credentials not set. Using in-memory fallback in development mode.');
  }
} else {
  // Initialize the Redis client with environment variables
  try {
    upstashClient = new Redis({
      url: process.env.UPSTASH_REDIS_REST_URL,
      token: process.env.UPSTASH_REDIS_REST_TOKEN,
    });
    console.log('Upstash Redis client initialized for comments');
  } catch (error) {
    console.error('Failed to initialize Upstash Redis client:', error.message);
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Redis client initialization failed in production');
    }
  }
}

// Test Redis connection immediately
(async function testRedisConnection() {
  // Skip test if Redis client is not initialized
  if (!upstashClient) {
    console.warn('Skipping Redis connection test - client not initialized');
    return;
  }

  try {
    await upstashClient.set('comments:test', 'test-connection');
    await upstashClient.get('comments:test');  // Verify we can read too
    console.log('Upstash Redis for comments caching initialized successfully');
  } catch (err) {
    console.error('Upstash Redis for comments caching initialization failed:', err);

    // In production, Redis is required
    if (process.env.NODE_ENV === 'production') {
      console.error('Failed to connect to Upstash Redis for comments in production mode.');
      // Don't throw an error here, let the application continue with fallback
      // This is a temporary fix to allow the application to start
    } else {
      console.warn('Redis connection failed in development mode. Using in-memory fallback.');
    }
  }
})();

// Cache TTL in seconds (1 hour)
const CACHE_TTL = 3600;

const commentRedisService = {

  /**
   * Cache comments for a token in Redis
   * @param {string} tokenSymbol - The token symbol
   * @param {Array} comments - Array of comment objects
   * @returns {Promise<boolean>} - Success status
   */
  async cacheComments(tokenSymbol, comments) {

    try {
      if (!comments || comments.length === 0) {
        return false;
      }

      // Cache the entire comments array
      const cacheKey = `comments:cache:${tokenSymbol}`;
      await upstashClient.set(cacheKey, JSON.stringify(comments), { ex: CACHE_TTL });
      console.log(`Cached ${comments.length} comments for ${tokenSymbol} in Redis`);

      return true;
    } catch (error) {
      console.error('Error caching comments in Redis:', error);
      throw new Error('Redis operation failed: ' + error.message);
    }
  },

  /**
   * Get cached comments for a token from Redis
   * @param {string} tokenSymbol - The token symbol
   * @returns {Promise<Array|null>} - Array of comments or null if not cached
   */
  async getCachedComments(tokenSymbol) {

    try {
      const cacheKey = `comments:cache:${tokenSymbol}`;
      const cachedData = await upstashClient.get(cacheKey);

      if (!cachedData) {
        console.log(`No cached comments found for ${tokenSymbol}`);
        return null;
      }

      const comments = JSON.parse(cachedData);
      console.log(`Retrieved ${comments.length} cached comments for ${tokenSymbol} from Redis`);
      return comments;
    } catch (error) {
      console.error('Error getting cached comments from Redis:', error);
      throw new Error('Redis operation failed: ' + error.message);
    }
  },

  /**
   * Invalidate the comments cache for a token
   * @param {string} tokenSymbol - The token symbol
   * @returns {Promise<boolean>} - Success status
   */
  async invalidateCommentsCache(tokenSymbol) {
    try {
      // Delete the main comments cache key
      const cacheKey = `comments:cache:${tokenSymbol}`;
      await upstashClient.del(cacheKey);

      // Also delete any keys that might be related to this token's comments
      // This is a more aggressive cache invalidation approach
      const pattern = `*${tokenSymbol}*`;
      try {
        // Try to scan for keys matching the pattern (if supported)
        const keys = await upstashClient.keys(pattern);
        if (keys && keys.length > 0) {
          console.log(`Found ${keys.length} additional keys to invalidate for ${tokenSymbol}`);
          for (const key of keys) {
            if (key !== cacheKey) { // Avoid double deletion
              await upstashClient.del(key);
            }
          }
        }
      } catch (scanError) {
        // If scan/keys is not supported, just log and continue
        console.log('Advanced cache invalidation not supported, using basic invalidation');
      }

      console.log(`Invalidated comments cache for ${tokenSymbol}`);
      return true;
    } catch (error) {
      console.error('Error invalidating comments cache:', error);
      throw new Error('Redis operation failed: ' + error.message);
    }
  },

  /**
   * Cache a single comment in Redis
   * @param {Object} comment - The comment object
   * @param {boolean} skipInvalidation - Whether to skip invalidating the comments list cache
   * @returns {Promise<boolean>} - Success status
   */
  async cacheComment(comment, skipInvalidation = false) {
    try {
      if (!comment || !comment._id || !comment.tokenSymbol) {
        return false;
      }

      const cacheKey = `comment:${comment._id}`;
      await upstashClient.set(cacheKey, JSON.stringify(comment), { ex: CACHE_TTL });
      console.log(`Cached comment ${comment._id} in Redis`);

      // Only invalidate if not explicitly skipped
      // This helps prevent redundant invalidations when the caller already handles it
      if (!skipInvalidation) {
        await this.invalidateCommentsCache(comment.tokenSymbol);
      }

      return true;
    } catch (error) {
      console.error('Error caching comment in Redis:', error);
      throw new Error('Redis operation failed: ' + error.message);
    }
  },

  /**
   * Get a cached comment from Redis
   * @param {string} commentId - The comment ID
   * @returns {Promise<Object|null>} - Comment object or null if not cached
   */
  async getCachedComment(commentId) {

    try {
      const cacheKey = `comment:${commentId}`;
      const cachedData = await upstashClient.get(cacheKey);

      if (!cachedData) {
        return null;
      }

      return JSON.parse(cachedData);
    } catch (error) {
      console.error('Error getting cached comment from Redis:', error);
      throw new Error('Redis operation failed: ' + error.message);
    }
  },

  /**
   * Cache like status for a comment
   * @param {string} commentId - The comment ID
   * @param {string} userId - The user ID
   * @param {boolean} liked - Whether the user liked the comment
   * @returns {Promise<boolean>} - Success status
   */
  async cacheLikeStatus(commentId, userId, liked) {

    try {
      const cacheKey = `like:${commentId}:${userId}`;
      await upstashClient.set(cacheKey, liked ? '1' : '0', { ex: CACHE_TTL });
      return true;
    } catch (error) {
      console.error('Error caching like status in Redis:', error);
      throw new Error('Redis operation failed: ' + error.message);
    }
  },

  /**
   * Get cached like status for a comment
   * @param {string} commentId - The comment ID
   * @param {string} userId - The user ID
   * @returns {Promise<boolean|null>} - Like status or null if not cached
   */
  async getCachedLikeStatus(commentId, userId) {

    try {
      const cacheKey = `like:${commentId}:${userId}`;
      const status = await upstashClient.get(cacheKey);

      if (status === null) {
        return null;
      }

      return status === '1';
    } catch (error) {
      console.error('Error getting cached like status from Redis:', error);
      throw new Error('Redis operation failed: ' + error.message);
    }
  }
};

module.exports = commentRedisService;
