import React, { useState, useEffect } from 'react';
import Navbar from '../components/Navbar';
import '../styles/dashboard.css';
import { Link } from 'react-router-dom';
import { API_URL } from '../config/constants';

const DashboardPage = () => {
  const [activeTab, setActiveTab] = useState('holdings');
  const [trendingTokens, setTrendingTokens] = useState([]);
  const [exploreTokens, setExploreTokens] = useState([]);

  // Fetch trending tokens
  useEffect(() => {
    fetch(`${API_URL}/tokens/trending`)
      .then(res => res.json())
      .then(data => setTrendingTokens(data))
      .catch(() => setTrendingTokens([
        { id: 101, name: 'MemeRocket', symbol: 'MRKT', price: 0.12, change: '+25%', volume: 120000, holders: 5000, marketCap: 1000000, description: 'A trending meme coin!', volume_24h: 120000 },
        { id: 102, name: 'SolanaPepe', symbol: 'SPEPE', price: 0.05, change: '+18%', volume: 95000, holders: 3200, marketCap: 800000, description: 'Pepe on Solana!', volume_24h: 95000 },
      ]));
  }, []);

  // Fetch explore tokens
  useEffect(() => {
    fetch(`${API_URL}/tokens/explore`)
      .then(res => res.json())
      .then(data => setExploreTokens(data))
      .catch(() => setExploreTokens([
        { id: 201, name: 'FreshMeme', symbol: 'FMEME', price: 0.01, change: '+2%', volume: 10000, holders: 100, marketCap: 20000, description: 'A new meme coin!', created_at: new Date().toISOString() },
        { id: 202, name: 'SolanaDog', symbol: 'SDOG', price: 0.03, change: '+5%', volume: 20000, holders: 250, marketCap: 60000, description: 'Dog coin on Solana!', created_at: new Date().toISOString() },
      ]));
  }, []);

  // Sample data for holdings
  const holdingsData = [
    {
      id: 1,
      name: 'token name',
      price: '$25.64',
      change: '+1.75%',
      image: null,
      changeColor: 'green'
    },
    {
      id: 2,
      name: 'token name',
      price: '$25.64',
      change: '-2.52%',
      image: null,
      changeColor: 'red'
    },
    {
      id: 3,
      name: 'token name',
      price: '$25.64',
      change: '+1.75%',
      image: null,
      changeColor: 'green'
    }
  ];

  // Sample data for positions
  const positionsData = [
    {
      id: 1,
      name: 'token name',
      price: '$25.64',
      profit: '+320 SOL',
      image: null,
      profitColor: 'green'
    },
    {
      id: 2,
      name: 'token name',
      price: '$25.64',
      profit: '+110 SOL',
      image: null,
      profitColor: 'green'
    },
    {
      id: 3,
      name: 'token name',
      price: '$25.64',
      profit: '-5 SOL',
      image: null,
      profitColor: 'red'
    }
  ];

  // Sample data for orders
  const ordersData = [
    {
      id: 1,
      name: 'token name',
      price: '$25.64',
      buyPrice: '1.2550',
      sellPrice: '1.2530',
      image: null
    },
    {
      id: 2,
      name: 'token name',
      price: '$25.64',
      buyPrice: '1.2550',
      sellPrice: '1.2530',
      image: null
    },
    {
      id: 3,
      name: 'token name',
      price: '$25.64',
      buyPrice: '1.2550',
      sellPrice: '1.2530',
      image: null
    }
  ];

  // Sample data for created tokens
  const createdTokensData = [
    {
      id: 1,
      name: 'token name',
      symbol: 'token symbol',
      price: '$25.74',
      volume: '$500000',
      holders: '25000',
      image: null
    },
    {
      id: 2,
      name: 'token name',
      symbol: 'token symbol',
      price: '$25.74',
      volume: '$500000',
      holders: '25000',
      image: null
    },
    {
      id: 3,
      name: 'token name',
      symbol: 'token symbol',
      price: '$25.74',
      volume: '$500000',
      holders: '25000',
      image: null
    }
  ];

  // Render holdings tab content
  const renderHoldings = () => {
    return (
      <div className="dashboard-content">
        {holdingsData.map(token => (
          <Link key={token.id} to={`/trade/${token.id}`} className="token-row-link">
            <div className="token-row">
              <div className="dashboard-token-info">
                <div className="token-image-placeholder"></div>
                <div className="token-details">
                  <div className="token-name">{token.name}</div>
                  <div className="token-price">{token.price}</div>
                </div>
              </div>
              <div className={`token-change ${token.changeColor}`}>
                {token.change}
              </div>
            </div>
          </Link>
        ))}
      </div>
    );
  };

  // Render positions tab content
  const renderPositions = () => {
    return (
      <div className="dashboard-content">
        <div className="total-profit-loss">
          <span>Total Profit/loss</span>
          <span className="green">+405 SOL</span>
        </div>

        {positionsData.map(token => (
          <Link key={token.id} to={`/trade/${token.id}`} className="token-row-link">
            <div className="token-row">
              <div className="dashboard-token-info">
                <div className="token-image-placeholder"></div>
                <div className="token-details">
                  <div className="token-name">{token.name}</div>
                  <div className="token-price">{token.price}</div>
                </div>
              </div>
              <div className={`token-profit ${token.profitColor}`}>
                {token.profit}
              </div>
            </div>
          </Link>
        ))}
      </div>
    );
  };

  // Render orders tab content
  const renderOrders = () => {
    return (
      <div className="dashboard-content">
        {ordersData.map(token => (
          <Link key={token.id} to={`/trade/${token.id}`} className="token-row-link">
            <div className="token-row">
              <div className="dashboard-token-info">
                <div className="token-image-placeholder"></div>
                <div className="token-details">
                  <div className="token-name">{token.name}</div>
                  <div className="token-price">{token.price}</div>
                </div>
              </div>
              <div className="token-orders">
                <div className="order-row">
                  <span className="order-label">Buy at</span>
                  <span className="order-price green">{token.buyPrice}</span>
                </div>
                <div className="order-row">
                  <span className="order-label">Sell at</span>
                  <span className="order-price red">{token.sellPrice}</span>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    );
  };

  // Render created tokens tab content
  const renderCreatedTokens = () => {
    return (
      <div className="dashboard-content">
        {createdTokensData.map(token => (
          <Link key={token.id} to={`/trade/${token.id}`} className="token-row-link">
            <div className="token-row">
              <div className="dashboard-token-info">
                <div className="token-image-placeholder"></div>
                <div className="token-details">
                  <div className="token-name gradient-text">{token.name}</div>
                  <div className="token-symbol">{token.symbol}</div>
                  <div className="token-price green">{token.price}</div>
                </div>
              </div>
              <div className="token-stats">
                <div className="stat-row">
                  <span className="stat-label">Total volume:</span>
                  <span className="stat-value">{token.volume}</span>
                </div>
                <div className="stat-row">
                  <span className="stat-label">Total holders:</span>
                  <span className="stat-value">{token.holders}</span>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    );
  };

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'holdings':
        return renderHoldings();
      case 'positions':
        return renderPositions();
      case 'orders':
        return renderOrders();
      case 'created':
        return renderCreatedTokens();
      default:
        return renderHoldings();
    }
  };

  // Add trending section
  const renderTrending = () => (
    <div className="dashboard-trending-section">
      <h2>Trending Tokens</h2>
      <div className="dashboard-trending-list">
        {trendingTokens.map(token => (
          <Link key={token.id} to={`/trade/${token.id}`} className="trending-token-card">
            <div className="trending-token-name">{token.name} <span className="trending-token-symbol">({token.symbol})</span></div>
            <div className="trending-token-price">${token.price}</div>
            <div className="trending-token-volume">Vol: ${token.volume_24h || token.volume}</div>
            <div className="trending-token-change">{token.change}</div>
          </Link>
        ))}
      </div>
    </div>
  );

  // Add explore section
  const renderExplore = () => (
    <div className="dashboard-explore-section">
      <h2>Explore New Tokens</h2>
      <div className="dashboard-explore-list">
        {exploreTokens.map(token => (
          <Link key={token.id} to={`/trade/${token.id}`} className="explore-token-card">
            <div className="explore-token-name">{token.name} <span className="explore-token-symbol">({token.symbol})</span></div>
            <div className="explore-token-price">${token.price}</div>
            <div className="explore-token-date">Created: {token.created_at ? token.created_at.split('T')[0] : 'N/A'}</div>
            <div className="explore-token-description">{token.description}</div>
          </Link>
        ))}
      </div>
    </div>
  );

  return (
    <div className="dashboard-page">
      <Navbar />

      <div className="dashboard-container">
        {renderTrending()}
        {renderExplore()}
        <div className="search-bar">
          <input type="text" placeholder="Search your holdings" />
        </div>

        <div className="dashboard-tabs">
          <button
            className={`tab-button ${activeTab === 'holdings' ? 'active' : ''}`}
            onClick={() => setActiveTab('holdings')}
          >
            holdings
          </button>
          <button
            className={`tab-button ${activeTab === 'positions' ? 'active' : ''}`}
            onClick={() => setActiveTab('positions')}
          >
            positions
          </button>
          <button
            className={`tab-button ${activeTab === 'orders' ? 'active' : ''}`}
            onClick={() => setActiveTab('orders')}
          >
            orders
          </button>
          <button
            className={`tab-button ${activeTab === 'created' ? 'active' : ''}`}
            onClick={() => setActiveTab('created')}
          >
            created by you
          </button>
        </div>

        {renderContent()}
      </div>
    </div>
  );
};

export default DashboardPage;
