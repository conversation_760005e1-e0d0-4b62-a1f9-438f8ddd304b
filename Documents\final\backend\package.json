{"name": "swap-backend", "version": "1.0.0", "description": "Backend for Solana-based meme token platform", "main": "src/index.js", "scripts": {"start": "node --tls-min-v1.0 --tls-max-v1.2 src/index.js", "start:secure": "node --tls-min-v1.0 --tls-max-v1.2 src/index.js", "start:node18": "node --tls-min-v1.2 src/index.js", "start:monitor": "node server-monitor.js", "start:dashboard": "node server.js", "dev": "cross-env NODE_ENV=development nodemon --tls-min-v1.0 --tls-max-v1.2 src/index.js", "dev:dashboard": "nodemon server.js", "dev:all": "concurrently \"npm run dev\" \"npm run dev:dashboard\"", "test": "jest", "lint": "eslint .", "db:seed": "node src/scripts/seed.js", "db:migrate": "node src/scripts/migrations.js", "env:dev": "node switch-env.js development", "env:prod": "node switch-env.js production", "start:dev": "cross-env NODE_ENV=development npm run start", "start:prod": "cross-env NODE_ENV=production npm run start", "start:production": "node scripts/start-production.js", "generate:secrets": "node scripts/generate-secrets.js", "security:check": "npm audit", "prepare:production": "npm run lint && npm run test && npm run security:check && npm run generate:secrets"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.787.0", "@aws-sdk/client-redshift": "^3.787.0", "@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/client-ses": "^3.777.0", "@aws-sdk/s3-request-presigner": "^3.777.0", "@elastic/elasticsearch": "^8.12.1", "@opentelemetry/api": "^1.7.0", "@opentelemetry/exporter-trace-otlp-http": "^0.48.0", "@opentelemetry/instrumentation": "^0.48.0", "@opentelemetry/resources": "^1.21.0", "@opentelemetry/sdk-trace-base": "^1.21.0", "@opentelemetry/semantic-conventions": "^1.21.0", "@project-serum/serum": "^0.13.65", "@raydium-io/raydium-sdk": "^1.3.1-beta.58", "@sendgrid/mail": "^8.1.5", "@solana/buffer-layout": "^4.0.1", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.0", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "bn.js": "^5.2.1", "borsh": "^2.0.0", "bs58": "^6.0.0", "cassandra-driver": "^4.8.0", "compression": "^1.8.0", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "express-session": "^1.18.1", "express-validator": "^7.0.1", "geoip-lite": "^1.4.10", "gridfs-stream": "^1.1.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "jwk-to-pem": "^2.0.5", "method-override": "^3.0.0", "mongodb-memory-server": "^10.1.4", "mongoose": "^7.6.8", "morgan": "^1.10.0", "multer": "^1.4.4", "multer-gridfs-storage": "^5.0.2", "node-fetch": "^2.7.0", "node-gyp-build": "^4.8.4", "nodemailer": "^6.10.0", "openssl": "^2.0.0", "pino": "^8.19.0", "pino-http": "^9.0.0", "punycode": "^2.3.1", "qrcode": "^1.5.4", "sharp": "^0.34.0", "snowflake-sdk": "^1.9.0", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "ssl": "^1.0.0", "tls": "^0.0.1", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0", "winston": "^3.8.2", "ws": "^8.18.1"}, "devDependencies": {"concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^8.57.0", "jest": "^29.5.0", "nodemon": "^2.0.22", "pino-pretty": "^10.3.1"}, "engines": {"node": ">=18.0.0"}, "author": "", "license": "ISC"}