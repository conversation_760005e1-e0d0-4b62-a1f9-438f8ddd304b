import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const polynomialSepolia = /*#__PURE__*/ define<PERSON>hain({
    id: 80008,
    name: 'Polynomia Sepolia',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: {
            http: ['https://rpc.sepolia.polynomial.fi'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Polynomial Scan',
            url: 'https://sepolia.polynomialscan.io',
        },
    },
    testnet: true,
    contracts: {
        multicall3: {
            address: '******************************************',
        },
    },
});
//# sourceMappingURL=polynomialSepolia.js.map