const { redisClient } = require('../config/redis');
const { ses, sesConfig, sns, snsConfig } = require('../config/aws');
const crypto = require('crypto');

class OTPService {
  /**
   * Generate a random OTP
   * @param {number} length - Length of the OTP (default: 6)
   * @returns {string} - Generated OTP
   */
  generateOTP(length = 6) {
    // Generate a cryptographically secure random OTP
    return crypto.randomInt(Math.pow(10, length - 1), Math.pow(10, length)).toString();
  }

  /**
   * Store OTP in Redis with TTL
   * @param {string} userId - User ID or email/phone for whom the OTP is generated
   * @param {string} channel - The channel for which OTP is being used (e.g., 'email', 'sms', 'registration')
   * @param {string} otp - The generated OTP
   * @param {number} expirySeconds - Expiry time in seconds (default: 300 seconds or 5 minutes)
   */
  async storeOTP(userId, channel, otp, expirySeconds = 300) {
    const key = `otp:${userId}:${channel}`;
    await redisClient.set(key, otp, 'EX', expirySeconds);
    
    // Store a count for rate limiting
    const countKey = `otp:count:${userId}:${channel}`;
    const count = await redisClient.get(countKey) || 0;
    await redisClient.set(countKey, parseInt(count) + 1, 'EX', 86400); // 24 hours expiry for count
  }

  /**
   * Get stored OTP from Redis
   * @param {string} userId - User ID or email/phone
   * @param {string} channel - The channel (e.g., 'email', 'sms', 'registration')
   * @returns {Promise<string|null>} - The stored OTP or null if not found
   */
  async getStoredOTP(userId, channel) {
    const key = `otp:${userId}:${channel}`;
    return await redisClient.get(key);
  }

  /**
   * Verify OTP
   * @param {string} userId - User ID or email/phone
   * @param {string} channel - The channel
   * @param {string} otp - The OTP to verify
   * @returns {Promise<boolean>} - Whether the OTP is valid
   */
  async verifyOTP(userId, channel, otp) {
    const storedOTP = await this.getStoredOTP(userId, channel);
    
    if (!storedOTP) {
      return false; // No OTP found or expired
    }
    
    const isValid = storedOTP === otp;
    
    // If OTP is valid, delete it to prevent reuse
    if (isValid) {
      await this.deleteOTP(userId, channel);
    }
    
    return isValid;
  }

  /**
   * Delete OTP from Redis
   * @param {string} userId - User ID or email/phone
   * @param {string} channel - The channel
   */
  async deleteOTP(userId, channel) {
    const key = `otp:${userId}:${channel}`;
    await redisClient.del(key);
  }

  /**
   * Check if user has exceeded OTP request limit
   * @param {string} userId - User ID or email/phone
   * @param {string} channel - The channel
   * @param {number} maxAttempts - Maximum allowed attempts in 24 hours
   * @returns {Promise<boolean>} - Whether limit is exceeded
   */
  async checkOTPRequestLimit(userId, channel, maxAttempts = 5) {
    const countKey = `otp:count:${userId}:${channel}`;
    const count = await redisClient.get(countKey) || 0;
    return parseInt(count) >= maxAttempts;
  }

  /**
   * Send OTP via Email using AWS SES
   * @param {string} email - Recipient email
   * @param {string} otp - OTP to send
   * @param {string} template - Template name (default: 'default')
   * @returns {Promise<object>} - SES send response
   */
  async sendOTPViaEmail(email, otp, template = 'default') {
    // Email templates based on usage
    const templates = {
      default: {
        subject: 'Your SWAP Verification Code',
        bodyHtml: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f7f9fc; border-radius: 10px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <img src="https://swaptrade.in/logo.png" alt="SWAP" style="width: 120px;">
            </div>
            <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
              <h2 style="color: #333; text-align: center; margin-bottom: 20px;">Verification Code</h2>
              <p style="color: #555; font-size: 16px; line-height: 1.5;">Hello,</p>
              <p style="color: #555; font-size: 16px; line-height: 1.5;">Your verification code is:</p>
              <div style="background-color: #f0f0f0; padding: 15px; text-align: center; border-radius: 5px; margin: 20px 0; letter-spacing: 5px; font-size: 24px; font-weight: bold;">
                ${otp}
              </div>
              <p style="color: #555; font-size: 16px; line-height: 1.5;">This code will expire in 5 minutes for security reasons.</p>
              <p style="color: #555; font-size: 16px; line-height: 1.5;">If you didn't request this code, please ignore this email.</p>
              <div style="margin-top: 30px; border-top: 1px solid #eee; padding-top: 20px; text-align: center; color: #888; font-size: 14px;">
                <p>This is an automated message, please do not reply.</p>
                <p>&copy; ${new Date().getFullYear()} SWAP. All rights reserved.</p>
              </div>
            </div>
          </div>
        `,
        bodyText: `Your SWAP verification code is: ${otp}. This code will expire in 5 minutes.`
      },
      registration: {
        subject: 'Complete Your SWAP Registration',
        bodyHtml: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f7f9fc; border-radius: 10px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <img src="https://swaptrade.in/logo.png" alt="SWAP" style="width: 120px;">
            </div>
            <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
              <h2 style="color: #333; text-align: center; margin-bottom: 20px;">Complete Your Registration</h2>
              <p style="color: #555; font-size: 16px; line-height: 1.5;">Welcome to SWAP!</p>
              <p style="color: #555; font-size: 16px; line-height: 1.5;">To complete your registration, please use this verification code:</p>
              <div style="background-color: #f0f0f0; padding: 15px; text-align: center; border-radius: 5px; margin: 20px 0; letter-spacing: 5px; font-size: 24px; font-weight: bold;">
                ${otp}
              </div>
              <p style="color: #555; font-size: 16px; line-height: 1.5;">This code will expire in 5 minutes for security reasons.</p>
              <p style="color: #555; font-size: 16px; line-height: 1.5;">If you didn't attempt to register for SWAP, please ignore this email.</p>
              <div style="margin-top: 30px; border-top: 1px solid #eee; padding-top: 20px; text-align: center; color: #888; font-size: 14px;">
                <p>This is an automated message, please do not reply.</p>
                <p>&copy; ${new Date().getFullYear()} SWAP. All rights reserved.</p>
              </div>
            </div>
          </div>
        `,
        bodyText: `Welcome to SWAP! Your registration verification code is: ${otp}. This code will expire in 5 minutes.`
      },
      passwordReset: {
        subject: 'Reset Your SWAP Password',
        bodyHtml: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f7f9fc; border-radius: 10px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <img src="https://swaptrade.in/logo.png" alt="SWAP" style="width: 120px;">
            </div>
            <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
              <h2 style="color: #333; text-align: center; margin-bottom: 20px;">Password Reset Request</h2>
              <p style="color: #555; font-size: 16px; line-height: 1.5;">Hello,</p>
              <p style="color: #555; font-size: 16px; line-height: 1.5;">We received a request to reset your SWAP password. Use this verification code to complete the process:</p>
              <div style="background-color: #f0f0f0; padding: 15px; text-align: center; border-radius: 5px; margin: 20px 0; letter-spacing: 5px; font-size: 24px; font-weight: bold;">
                ${otp}
              </div>
              <p style="color: #555; font-size: 16px; line-height: 1.5;">This code will expire in 5 minutes for security reasons.</p>
              <p style="color: #555; font-size: 16px; line-height: 1.5;">If you didn't request a password reset, please ignore this email and consider changing your password for security.</p>
              <div style="margin-top: 30px; border-top: 1px solid #eee; padding-top: 20px; text-align: center; color: #888; font-size: 14px;">
                <p>This is an automated message, please do not reply.</p>
                <p>&copy; ${new Date().getFullYear()} SWAP. All rights reserved.</p>
              </div>
            </div>
          </div>
        `,
        bodyText: `We received a request to reset your SWAP password. Your verification code is: ${otp}. This code will expire in 5 minutes.`
      }
    };

    // Use the specified template or fallback to default
    const emailTemplate = templates[template] || templates.default;

    const params = {
      Source: sesConfig.sourceEmail,
      Destination: {
        ToAddresses: [email]
      },
      Message: {
        Subject: {
          Data: emailTemplate.subject,
          Charset: 'UTF-8'
        },
        Body: {
          Html: {
            Data: emailTemplate.bodyHtml,
            Charset: 'UTF-8'
          },
          Text: {
            Data: emailTemplate.bodyText,
            Charset: 'UTF-8'
          }
        }
      }
    };

    try {
      return await ses.sendEmail(params).promise();
    } catch (error) {
      console.error('Error sending OTP email:', error);
      throw error;
    }
  }

  /**
   * Formats a phone number for AWS SNS
   * @param {string} phoneNumber - The phone number to format
   * @returns {string} - Formatted phone number in E.164 format
   */
  formatPhoneNumber(phoneNumber) {
    // Remove all non-digit characters
    let digits = phoneNumber.replace(/\D/g, '');
    
    // Check if the number starts with a country code
    if (!digits.startsWith('1') && !digits.startsWith('91')) {
      // Default to India (+91) for SWAP platform
      digits = '91' + digits;
    }
    
    // Add + prefix for E.164 format
    return '+' + digits;
  }
  
  /**
   * Send OTP via SMS using AWS SNS
   * @param {string} phoneNumber - Recipient phone number
   * @param {string} otp - OTP to send
   * @param {string} template - Template name (default: 'default')
   * @returns {Promise<object>} - SNS publish response
   */
  async sendOTPViaSMS(phoneNumber, otp, template = 'default') {
    // Format phone number to E.164 format (+[country code][number])
    const formattedPhone = this.formatPhoneNumber(phoneNumber);

    // SMS templates based on usage
    const templates = {
      default: `Your SWAP verification code is: ${otp}. This code will expire in 5 minutes.`,
      registration: `Welcome to SWAP! Your registration verification code is: ${otp}. This code will expire in 5 minutes.`,
      passwordReset: `SWAP password reset code: ${otp}. This code will expire in 5 minutes.`,
      login: `Your SWAP login verification code is: ${otp}. This code will expire in 5 minutes.`,
      twoFactor: `Your SWAP two-factor authentication code is: ${otp}. This code will expire in 5 minutes.`
    };

    // Use the specified template or fallback to default
    const message = templates[template] || templates.default;

    // Set message attributes for Transactional SMS
    const params = {
      Message: message,
      PhoneNumber: formattedPhone,
      MessageAttributes: {
        'AWS.SNS.SMS.SenderID': {
          DataType: 'String',
          StringValue: snsConfig.senderId
        },
        'AWS.SNS.SMS.SMSType': {
          DataType: 'String',
          StringValue: snsConfig.smsType
        }
      }
    };

    try {
      return await sns.publish(params).promise();
    } catch (error) {
      console.error('Error sending OTP SMS:', error);
      throw error;
    }
  }
}

module.exports = new OTPService();
