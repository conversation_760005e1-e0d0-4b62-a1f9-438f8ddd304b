# Main Environment Configuration Template
# Copy this file to .env and fill in the values
# These values are used in both development and production

# MongoDB Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority
MONGODB_DB_NAME=database_name

# PostgreSQL Configuration
POSTGRESQL_URI=postgresql://username:password@hostname:port/database?sslmode=require
POSTGRES_HOST=hostname
POSTGRES_PORT=5432
POSTGRES_DB=database_name
POSTGRES_USER=username
POSTGRES_PASSWORD=password
POSTGRES_SSL=true

# Redis Configuration
UPSTASH_REDIS_REST_URL=https://your-redis-instance.upstash.io
UPSTASH_REDIS_REST_TOKEN=your_redis_token
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Solana Configuration
SOLANA_RPC_URL=https://api.devnet.solana.com
SOLANA_PROGRAM_ID=your_program_id

# Platform Wallet Configuration
PLATFORM_WALLET_ADDRESS=your_wallet_address
WALLET_SOURCE=file
WALLET_PATH=./scripts/platform-wallet.json

# Order Matching Engine Configuration
ORDER_MATCHING_ENGINE_PATH=path/to/order/matching/engine
