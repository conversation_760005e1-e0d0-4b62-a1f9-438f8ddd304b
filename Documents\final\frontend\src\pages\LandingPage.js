import React from 'react';
import { Link } from 'react-router-dom';
import Navbar from '../components/Navbar';

// Import background image
import backgroundImage from '../assets/background.jpg';

const LandingPage = () => {
  return (
    <div className="landing-page">
      <div
        className="landing-background"
        style={{
          backgroundImage: `url(${backgroundImage})`,
          backgroundPosition: 'right', /* Move background much more to the right */
          backgroundSize: 'auto', /* Make the background image fill the width */
          backgroundRepeat: 'no-repeat',
          backgroundBlendMode: 'normal',
          opacity: 1 /* Full opacity */
        }}
      ></div>
      <Navbar />

      <div className="landing-content">
        <div className="landing-text">
          <h1 className="landing-title">
            <div className="create-text">CREATE</div>
            <div className="trade-text">TRADE</div>
            <div className="swap-text">Swap</div>
          </h1>

          <Link to="/home" className="get-started-btn">
            Get Started
          </Link>
        </div>
      </div>

      {/* <div className="stats-box">
        <h3 className="stats-title">Check this out</h3>

        <div className="stats-item">
          <div className="stats-value">80</div>
          <div className="stats-label">Tokens created</div>
        </div>

        <div className="stats-item">
          <div className="stats-value">80</div>
          <div className="stats-label">Active users</div>
        </div>

        <div className="stats-item">
          <div className="stats-value">80</div>
          <div className="stats-label">Volume</div>
        </div>
      </div> */}
    </div>
  );
};

export default LandingPage;
