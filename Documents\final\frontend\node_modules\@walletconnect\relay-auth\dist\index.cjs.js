"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var pn=require("@walletconnect/time"),De=require("@walletconnect/safe-json");function En(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function fe(t,...e){if(!En(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function de(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function wn(t,e){fe(t);const n=e.outputLen;if(t.length<n)throw new Error("digestInto() expects output buffer of length at least "+n)}const it=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */const _t=t=>new DataView(t.buffer,t.byteOffset,t.byteLength);function gn(t){if(typeof t!="string")throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array(new TextEncoder().encode(t))}function le(t){return typeof t=="string"&&(t=gn(t)),fe(t),t}class yn{clone(){return this._cloneInto()}}function xn(t){const e=r=>t().update(le(r)).digest(),n=t();return e.outputLen=n.outputLen,e.blockLen=n.blockLen,e.create=()=>t(),e}function he(t=32){if(it&&typeof it.getRandomValues=="function")return it.getRandomValues(new Uint8Array(t));if(it&&typeof it.randomBytes=="function")return it.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")}function Cn(t,e,n,r){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,n,r);const o=BigInt(32),s=BigInt(**********),a=Number(n>>o&s),u=Number(n&s),i=r?4:0,f=r?0:4;t.setUint32(e+i,a,r),t.setUint32(e+f,u,r)}class Bn extends yn{constructor(e,n,r,o){super(),this.blockLen=e,this.outputLen=n,this.padOffset=r,this.isLE=o,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=_t(this.buffer)}update(e){de(this);const{view:n,buffer:r,blockLen:o}=this;e=le(e);const s=e.length;for(let a=0;a<s;){const u=Math.min(o-this.pos,s-a);if(u===o){const i=_t(e);for(;o<=s-a;a+=o)this.process(i,a);continue}r.set(e.subarray(a,a+u),this.pos),this.pos+=u,a+=u,this.pos===o&&(this.process(n,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){de(this),wn(e,this),this.finished=!0;const{buffer:n,view:r,blockLen:o,isLE:s}=this;let{pos:a}=this;n[a++]=128,this.buffer.subarray(a).fill(0),this.padOffset>o-a&&(this.process(r,0),a=0);for(let h=a;h<o;h++)n[h]=0;Cn(r,o-8,BigInt(this.length*8),s),this.process(r,0);const u=_t(e),i=this.outputLen;if(i%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const f=i/4,c=this.get();if(f>c.length)throw new Error("_sha2: outputLen bigger than state");for(let h=0;h<f;h++)u.setUint32(4*h,c[h],s)}digest(){const{buffer:e,outputLen:n}=this;this.digestInto(e);const r=e.slice(0,n);return this.destroy(),r}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:n,buffer:r,length:o,finished:s,destroyed:a,pos:u}=this;return e.length=o,e.pos=u,e.finished=s,e.destroyed=a,o%n&&e.buffer.set(r),e}}const Et=BigInt(2**32-1),It=BigInt(32);function be(t,e=!1){return e?{h:Number(t&Et),l:Number(t>>It&Et)}:{h:Number(t>>It&Et)|0,l:Number(t&Et)|0}}function An(t,e=!1){let n=new Uint32Array(t.length),r=new Uint32Array(t.length);for(let o=0;o<t.length;o++){const{h:s,l:a}=be(t[o],e);[n[o],r[o]]=[s,a]}return[n,r]}const mn=(t,e)=>BigInt(t>>>0)<<It|BigInt(e>>>0),_n=(t,e,n)=>t>>>n,In=(t,e,n)=>t<<32-n|e>>>n,Sn=(t,e,n)=>t>>>n|e<<32-n,vn=(t,e,n)=>t<<32-n|e>>>n,Tn=(t,e,n)=>t<<64-n|e>>>n-32,Un=(t,e,n)=>t>>>n-32|e<<64-n,Nn=(t,e)=>e,Fn=(t,e)=>t,Ln=(t,e,n)=>t<<n|e>>>32-n,On=(t,e,n)=>e<<n|t>>>32-n,Mn=(t,e,n)=>e<<n-32|t>>>64-n,Hn=(t,e,n)=>t<<n-32|e>>>64-n;function zn(t,e,n,r){const o=(e>>>0)+(r>>>0);return{h:t+n+(o/2**32|0)|0,l:o|0}}const Rn=(t,e,n)=>(t>>>0)+(e>>>0)+(n>>>0),qn=(t,e,n,r)=>e+n+r+(t/2**32|0)|0,$n=(t,e,n,r)=>(t>>>0)+(e>>>0)+(n>>>0)+(r>>>0),kn=(t,e,n,r,o)=>e+n+r+o+(t/2**32|0)|0,jn=(t,e,n,r,o)=>(t>>>0)+(e>>>0)+(n>>>0)+(r>>>0)+(o>>>0),Gn=(t,e,n,r,o,s)=>e+n+r+o+s+(t/2**32|0)|0,x={fromBig:be,split:An,toBig:mn,shrSH:_n,shrSL:In,rotrSH:Sn,rotrSL:vn,rotrBH:Tn,rotrBL:Un,rotr32H:Nn,rotr32L:Fn,rotlSH:Ln,rotlSL:On,rotlBH:Mn,rotlBL:Hn,add:zn,add3L:Rn,add3H:qn,add4L:$n,add4H:kn,add5H:Gn,add5L:jn},[Zn,Jn]=(()=>x.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(t=>BigInt(t))))(),X=new Uint32Array(80),Q=new Uint32Array(80);class Wn extends Bn{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){const{Ah:e,Al:n,Bh:r,Bl:o,Ch:s,Cl:a,Dh:u,Dl:i,Eh:f,El:c,Fh:h,Fl:p,Gh:E,Gl:l,Hh:g,Hl:I}=this;return[e,n,r,o,s,a,u,i,f,c,h,p,E,l,g,I]}set(e,n,r,o,s,a,u,i,f,c,h,p,E,l,g,I){this.Ah=e|0,this.Al=n|0,this.Bh=r|0,this.Bl=o|0,this.Ch=s|0,this.Cl=a|0,this.Dh=u|0,this.Dl=i|0,this.Eh=f|0,this.El=c|0,this.Fh=h|0,this.Fl=p|0,this.Gh=E|0,this.Gl=l|0,this.Hh=g|0,this.Hl=I|0}process(e,n){for(let d=0;d<16;d++,n+=4)X[d]=e.getUint32(n),Q[d]=e.getUint32(n+=4);for(let d=16;d<80;d++){const m=X[d-15]|0,N=Q[d-15]|0,R=x.rotrSH(m,N,1)^x.rotrSH(m,N,8)^x.shrSH(m,N,7),H=x.rotrSL(m,N,1)^x.rotrSL(m,N,8)^x.shrSL(m,N,7),v=X[d-2]|0,O=Q[d-2]|0,ot=x.rotrSH(v,O,19)^x.rotrBH(v,O,61)^x.shrSH(v,O,6),tt=x.rotrSL(v,O,19)^x.rotrBL(v,O,61)^x.shrSL(v,O,6),st=x.add4L(H,tt,Q[d-7],Q[d-16]),at=x.add4H(st,R,ot,X[d-7],X[d-16]);X[d]=at|0,Q[d]=st|0}let{Ah:r,Al:o,Bh:s,Bl:a,Ch:u,Cl:i,Dh:f,Dl:c,Eh:h,El:p,Fh:E,Fl:l,Gh:g,Gl:I,Hh:S,Hl:L}=this;for(let d=0;d<80;d++){const m=x.rotrSH(h,p,14)^x.rotrSH(h,p,18)^x.rotrBH(h,p,41),N=x.rotrSL(h,p,14)^x.rotrSL(h,p,18)^x.rotrBL(h,p,41),R=h&E^~h&g,H=p&l^~p&I,v=x.add5L(L,N,H,Jn[d],Q[d]),O=x.add5H(v,S,m,R,Zn[d],X[d]),ot=v|0,tt=x.rotrSH(r,o,28)^x.rotrBH(r,o,34)^x.rotrBH(r,o,39),st=x.rotrSL(r,o,28)^x.rotrBL(r,o,34)^x.rotrBL(r,o,39),at=r&s^r&u^s&u,Bt=o&a^o&i^a&i;S=g|0,L=I|0,g=E|0,I=l|0,E=h|0,l=p|0,{h,l:p}=x.add(f|0,c|0,O|0,ot|0),f=u|0,c=i|0,u=s|0,i=a|0,s=r|0,a=o|0;const At=x.add3L(ot,st,Bt);r=x.add3H(At,O,tt,at),o=At|0}({h:r,l:o}=x.add(this.Ah|0,this.Al|0,r|0,o|0)),{h:s,l:a}=x.add(this.Bh|0,this.Bl|0,s|0,a|0),{h:u,l:i}=x.add(this.Ch|0,this.Cl|0,u|0,i|0),{h:f,l:c}=x.add(this.Dh|0,this.Dl|0,f|0,c|0),{h,l:p}=x.add(this.Eh|0,this.El|0,h|0,p|0),{h:E,l}=x.add(this.Fh|0,this.Fl|0,E|0,l|0),{h:g,l:I}=x.add(this.Gh|0,this.Gl|0,g|0,I|0),{h:S,l:L}=x.add(this.Hh|0,this.Hl|0,S|0,L|0),this.set(r,o,s,a,u,i,f,c,h,p,E,l,g,I,S,L)}roundClean(){X.fill(0),Q.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}const Vn=xn(()=>new Wn);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const St=BigInt(0),pe=BigInt(1),Yn=BigInt(2);function vt(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function Tt(t){if(!vt(t))throw new Error("Uint8Array expected")}function Ut(t,e){if(typeof e!="boolean")throw new Error(t+" boolean expected, got "+e)}const Kn=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function Nt(t){Tt(t);let e="";for(let n=0;n<t.length;n++)e+=Kn[t[n]];return e}function Ee(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);return t===""?St:BigInt("0x"+t)}const Y={_0:48,_9:57,A:65,F:70,a:97,f:102};function we(t){if(t>=Y._0&&t<=Y._9)return t-Y._0;if(t>=Y.A&&t<=Y.F)return t-(Y.A-10);if(t>=Y.a&&t<=Y.f)return t-(Y.a-10)}function ge(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);const e=t.length,n=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);const r=new Uint8Array(n);for(let o=0,s=0;o<n;o++,s+=2){const a=we(t.charCodeAt(s)),u=we(t.charCodeAt(s+1));if(a===void 0||u===void 0){const i=t[s]+t[s+1];throw new Error('hex string expected, got non-hex character "'+i+'" at index '+s)}r[o]=a*16+u}return r}function Pn(t){return Ee(Nt(t))}function wt(t){return Tt(t),Ee(Nt(Uint8Array.from(t).reverse()))}function ye(t,e){return ge(t.toString(16).padStart(e*2,"0"))}function Ft(t,e){return ye(t,e).reverse()}function K(t,e,n){let r;if(typeof e=="string")try{r=ge(e)}catch(s){throw new Error(t+" must be hex string or Uint8Array, cause: "+s)}else if(vt(e))r=Uint8Array.from(e);else throw new Error(t+" must be hex string or Uint8Array");const o=r.length;if(typeof n=="number"&&o!==n)throw new Error(t+" of length "+n+" expected, got "+o);return r}function xe(...t){let e=0;for(let r=0;r<t.length;r++){const o=t[r];Tt(o),e+=o.length}const n=new Uint8Array(e);for(let r=0,o=0;r<t.length;r++){const s=t[r];n.set(s,o),o+=s.length}return n}const Lt=t=>typeof t=="bigint"&&St<=t;function Xn(t,e,n){return Lt(t)&&Lt(e)&&Lt(n)&&e<=t&&t<n}function Dt(t,e,n,r){if(!Xn(e,n,r))throw new Error("expected valid "+t+": "+n+" <= n < "+r+", got "+e)}function Qn(t){let e;for(e=0;t>St;t>>=pe,e+=1);return e}const tr=t=>(Yn<<BigInt(t-1))-pe,er={bigint:t=>typeof t=="bigint",function:t=>typeof t=="function",boolean:t=>typeof t=="boolean",string:t=>typeof t=="string",stringOrUint8Array:t=>typeof t=="string"||vt(t),isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>typeof t=="function"&&Number.isSafeInteger(t.outputLen)};function Ot(t,e,n={}){const r=(o,s,a)=>{const u=er[s];if(typeof u!="function")throw new Error("invalid validator function");const i=t[o];if(!(a&&i===void 0)&&!u(i,t))throw new Error("param "+String(o)+" is invalid. Expected "+s+", got "+i)};for(const[o,s]of Object.entries(e))r(o,s,!1);for(const[o,s]of Object.entries(n))r(o,s,!0);return t}function Ce(t){const e=new WeakMap;return(n,...r)=>{const o=e.get(n);if(o!==void 0)return o;const s=t(n,...r);return e.set(n,s),s}}const z=BigInt(0),F=BigInt(1),nt=BigInt(2),nr=BigInt(3),Mt=BigInt(4),Be=BigInt(5),Ae=BigInt(8);function M(t,e){const n=t%e;return n>=z?n:e+n}function rr(t,e,n){if(e<z)throw new Error("invalid exponent, negatives unsupported");if(n<=z)throw new Error("invalid modulus");if(n===F)return z;let r=F;for(;e>z;)e&F&&(r=r*t%n),t=t*t%n,e>>=F;return r}function V(t,e,n){let r=t;for(;e-- >z;)r*=r,r%=n;return r}function me(t,e){if(t===z)throw new Error("invert: expected non-zero number");if(e<=z)throw new Error("invert: expected positive modulus, got "+e);let n=M(t,e),r=e,o=z,s=F;for(;n!==z;){const u=r/n,i=r%n,f=o-s*u;r=n,n=i,o=s,s=f}if(r!==F)throw new Error("invert: does not exist");return M(o,e)}function or(t){const e=(t-F)/nt;let n,r,o;for(n=t-F,r=0;n%nt===z;n/=nt,r++);for(o=nt;o<t&&rr(o,e,t)!==t-F;o++)if(o>1e3)throw new Error("Cannot find square root: likely non-prime P");if(r===1){const a=(t+F)/Mt;return function(i,f){const c=i.pow(f,a);if(!i.eql(i.sqr(c),f))throw new Error("Cannot find square root");return c}}const s=(n+F)/nt;return function(u,i){if(u.pow(i,e)===u.neg(u.ONE))throw new Error("Cannot find square root");let f=r,c=u.pow(u.mul(u.ONE,o),n),h=u.pow(i,s),p=u.pow(i,n);for(;!u.eql(p,u.ONE);){if(u.eql(p,u.ZERO))return u.ZERO;let E=1;for(let g=u.sqr(p);E<f&&!u.eql(g,u.ONE);E++)g=u.sqr(g);const l=u.pow(c,F<<BigInt(f-E-1));c=u.sqr(l),h=u.mul(h,l),p=u.mul(p,c),f=E}return h}}function sr(t){if(t%Mt===nr){const e=(t+F)/Mt;return function(r,o){const s=r.pow(o,e);if(!r.eql(r.sqr(s),o))throw new Error("Cannot find square root");return s}}if(t%Ae===Be){const e=(t-Be)/Ae;return function(r,o){const s=r.mul(o,nt),a=r.pow(s,e),u=r.mul(o,a),i=r.mul(r.mul(u,nt),a),f=r.mul(u,r.sub(i,r.ONE));if(!r.eql(r.sqr(f),o))throw new Error("Cannot find square root");return f}}return or(t)}const ir=(t,e)=>(M(t,e)&F)===F,ur=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function cr(t){const e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},n=ur.reduce((r,o)=>(r[o]="function",r),e);return Ot(t,n)}function ar(t,e,n){if(n<z)throw new Error("invalid exponent, negatives unsupported");if(n===z)return t.ONE;if(n===F)return e;let r=t.ONE,o=e;for(;n>z;)n&F&&(r=t.mul(r,o)),o=t.sqr(o),n>>=F;return r}function Dr(t,e){const n=new Array(e.length),r=e.reduce((s,a,u)=>t.is0(a)?s:(n[u]=s,t.mul(s,a)),t.ONE),o=t.inv(r);return e.reduceRight((s,a,u)=>t.is0(a)?s:(n[u]=t.mul(s,n[u]),t.mul(s,a)),o),n}function _e(t,e){const n=e!==void 0?e:t.toString(2).length,r=Math.ceil(n/8);return{nBitLength:n,nByteLength:r}}function Ie(t,e,n=!1,r={}){if(t<=z)throw new Error("invalid field: expected ORDER > 0, got "+t);const{nBitLength:o,nByteLength:s}=_e(t,e);if(s>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let a;const u=Object.freeze({ORDER:t,isLE:n,BITS:o,BYTES:s,MASK:tr(o),ZERO:z,ONE:F,create:i=>M(i,t),isValid:i=>{if(typeof i!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof i);return z<=i&&i<t},is0:i=>i===z,isOdd:i=>(i&F)===F,neg:i=>M(-i,t),eql:(i,f)=>i===f,sqr:i=>M(i*i,t),add:(i,f)=>M(i+f,t),sub:(i,f)=>M(i-f,t),mul:(i,f)=>M(i*f,t),pow:(i,f)=>ar(u,i,f),div:(i,f)=>M(i*me(f,t),t),sqrN:i=>i*i,addN:(i,f)=>i+f,subN:(i,f)=>i-f,mulN:(i,f)=>i*f,inv:i=>me(i,t),sqrt:r.sqrt||(i=>(a||(a=sr(t)),a(u,i))),invertBatch:i=>Dr(u,i),cmov:(i,f,c)=>c?f:i,toBytes:i=>n?Ft(i,s):ye(i,s),fromBytes:i=>{if(i.length!==s)throw new Error("Field.fromBytes: expected "+s+" bytes, got "+i.length);return n?wt(i):Pn(i)}});return Object.freeze(u)}const Se=BigInt(0),gt=BigInt(1);function Ht(t,e){const n=e.negate();return t?n:e}function ve(t,e){if(!Number.isSafeInteger(t)||t<=0||t>e)throw new Error("invalid window size, expected [1.."+e+"], got W="+t)}function zt(t,e){ve(t,e);const n=Math.ceil(e/t)+1,r=2**(t-1);return{windows:n,windowSize:r}}function fr(t,e){if(!Array.isArray(t))throw new Error("array expected");t.forEach((n,r)=>{if(!(n instanceof e))throw new Error("invalid point at index "+r)})}function dr(t,e){if(!Array.isArray(t))throw new Error("array of scalars expected");t.forEach((n,r)=>{if(!e.isValid(n))throw new Error("invalid scalar at index "+r)})}const Rt=new WeakMap,Te=new WeakMap;function qt(t){return Te.get(t)||1}function lr(t,e){return{constTimeNegate:Ht,hasPrecomputes(n){return qt(n)!==1},unsafeLadder(n,r,o=t.ZERO){let s=n;for(;r>Se;)r&gt&&(o=o.add(s)),s=s.double(),r>>=gt;return o},precomputeWindow(n,r){const{windows:o,windowSize:s}=zt(r,e),a=[];let u=n,i=u;for(let f=0;f<o;f++){i=u,a.push(i);for(let c=1;c<s;c++)i=i.add(u),a.push(i);u=i.double()}return a},wNAF(n,r,o){const{windows:s,windowSize:a}=zt(n,e);let u=t.ZERO,i=t.BASE;const f=BigInt(2**n-1),c=2**n,h=BigInt(n);for(let p=0;p<s;p++){const E=p*a;let l=Number(o&f);o>>=h,l>a&&(l-=c,o+=gt);const g=E,I=E+Math.abs(l)-1,S=p%2!==0,L=l<0;l===0?i=i.add(Ht(S,r[g])):u=u.add(Ht(L,r[I]))}return{p:u,f:i}},wNAFUnsafe(n,r,o,s=t.ZERO){const{windows:a,windowSize:u}=zt(n,e),i=BigInt(2**n-1),f=2**n,c=BigInt(n);for(let h=0;h<a;h++){const p=h*u;if(o===Se)break;let E=Number(o&i);if(o>>=c,E>u&&(E-=f,o+=gt),E===0)continue;let l=r[p+Math.abs(E)-1];E<0&&(l=l.negate()),s=s.add(l)}return s},getPrecomputes(n,r,o){let s=Rt.get(r);return s||(s=this.precomputeWindow(r,n),n!==1&&Rt.set(r,o(s))),s},wNAFCached(n,r,o){const s=qt(n);return this.wNAF(s,this.getPrecomputes(s,n,o),r)},wNAFCachedUnsafe(n,r,o,s){const a=qt(n);return a===1?this.unsafeLadder(n,r,s):this.wNAFUnsafe(a,this.getPrecomputes(a,n,o),r,s)},setWindowSize(n,r){ve(r,e),Te.set(n,r),Rt.delete(n)}}}function hr(t,e,n,r){if(fr(n,t),dr(r,e),n.length!==r.length)throw new Error("arrays of points and scalars must have equal length");const o=t.ZERO,s=Qn(BigInt(n.length)),a=s>12?s-3:s>4?s-2:s?2:1,u=(1<<a)-1,i=new Array(u+1).fill(o),f=Math.floor((e.BITS-1)/a)*a;let c=o;for(let h=f;h>=0;h-=a){i.fill(o);for(let E=0;E<r.length;E++){const l=r[E],g=Number(l>>BigInt(h)&BigInt(u));i[g]=i[g].add(n[E])}let p=o;for(let E=i.length-1,l=o;E>0;E--)l=l.add(i[E]),p=p.add(l);if(c=c.add(p),h!==0)for(let E=0;E<a;E++)c=c.double()}return c}function br(t){return cr(t.Fp),Ot(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({..._e(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}const Z=BigInt(0),j=BigInt(1),yt=BigInt(2),pr=BigInt(8),Er={zip215:!0};function wr(t){const e=br(t);return Ot(t,{hash:"function",a:"bigint",d:"bigint",randomBytes:"function"},{adjustScalarBytes:"function",domain:"function",uvRatio:"function",mapToCurve:"function"}),Object.freeze({...e})}function gr(t){const e=wr(t),{Fp:n,n:r,prehash:o,hash:s,randomBytes:a,nByteLength:u,h:i}=e,f=yt<<BigInt(u*8)-j,c=n.create,h=Ie(e.n,e.nBitLength),p=e.uvRatio||((y,D)=>{try{return{isValid:!0,value:n.sqrt(y*n.inv(D))}}catch{return{isValid:!1,value:Z}}}),E=e.adjustScalarBytes||(y=>y),l=e.domain||((y,D,b)=>{if(Ut("phflag",b),D.length||b)throw new Error("Contexts/pre-hash are not supported");return y});function g(y,D){Dt("coordinate "+y,D,Z,f)}function I(y){if(!(y instanceof d))throw new Error("ExtendedPoint expected")}const S=Ce((y,D)=>{const{ex:b,ey:w,ez:C}=y,B=y.is0();D==null&&(D=B?pr:n.inv(C));const A=c(b*D),T=c(w*D),_=c(C*D);if(B)return{x:Z,y:j};if(_!==j)throw new Error("invZ was invalid");return{x:A,y:T}}),L=Ce(y=>{const{a:D,d:b}=e;if(y.is0())throw new Error("bad point: ZERO");const{ex:w,ey:C,ez:B,et:A}=y,T=c(w*w),_=c(C*C),U=c(B*B),q=c(U*U),k=c(T*D),J=c(U*c(k+_)),W=c(q+c(b*c(T*_)));if(J!==W)throw new Error("bad point: equation left != right (1)");const G=c(w*C),P=c(B*A);if(G!==P)throw new Error("bad point: equation left != right (2)");return!0});class d{constructor(D,b,w,C){this.ex=D,this.ey=b,this.ez=w,this.et=C,g("x",D),g("y",b),g("z",w),g("t",C),Object.freeze(this)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static fromAffine(D){if(D instanceof d)throw new Error("extended point not allowed");const{x:b,y:w}=D||{};return g("x",b),g("y",w),new d(b,w,j,c(b*w))}static normalizeZ(D){const b=n.invertBatch(D.map(w=>w.ez));return D.map((w,C)=>w.toAffine(b[C])).map(d.fromAffine)}static msm(D,b){return hr(d,h,D,b)}_setWindowSize(D){R.setWindowSize(this,D)}assertValidity(){L(this)}equals(D){I(D);const{ex:b,ey:w,ez:C}=this,{ex:B,ey:A,ez:T}=D,_=c(b*T),U=c(B*C),q=c(w*T),k=c(A*C);return _===U&&q===k}is0(){return this.equals(d.ZERO)}negate(){return new d(c(-this.ex),this.ey,this.ez,c(-this.et))}double(){const{a:D}=e,{ex:b,ey:w,ez:C}=this,B=c(b*b),A=c(w*w),T=c(yt*c(C*C)),_=c(D*B),U=b+w,q=c(c(U*U)-B-A),k=_+A,J=k-T,W=_-A,G=c(q*J),P=c(k*W),et=c(q*W),pt=c(J*k);return new d(G,P,pt,et)}add(D){I(D);const{a:b,d:w}=e,{ex:C,ey:B,ez:A,et:T}=this,{ex:_,ey:U,ez:q,et:k}=D;if(b===BigInt(-1)){const re=c((B-C)*(U+_)),oe=c((B+C)*(U-_)),mt=c(oe-re);if(mt===Z)return this.double();const se=c(A*yt*k),ie=c(T*yt*q),ue=ie+se,ce=oe+re,ae=ie-se,dn=c(ue*mt),ln=c(ce*ae),hn=c(ue*ae),bn=c(mt*ce);return new d(dn,ln,bn,hn)}const J=c(C*_),W=c(B*U),G=c(T*w*k),P=c(A*q),et=c((C+B)*(_+U)-J-W),pt=P-G,ee=P+G,ne=c(W-b*J),cn=c(et*pt),an=c(ee*ne),Dn=c(et*ne),fn=c(pt*ee);return new d(cn,an,fn,Dn)}subtract(D){return this.add(D.negate())}wNAF(D){return R.wNAFCached(this,D,d.normalizeZ)}multiply(D){const b=D;Dt("scalar",b,j,r);const{p:w,f:C}=this.wNAF(b);return d.normalizeZ([w,C])[0]}multiplyUnsafe(D,b=d.ZERO){const w=D;return Dt("scalar",w,Z,r),w===Z?N:this.is0()||w===j?this:R.wNAFCachedUnsafe(this,w,d.normalizeZ,b)}isSmallOrder(){return this.multiplyUnsafe(i).is0()}isTorsionFree(){return R.unsafeLadder(this,r).is0()}toAffine(D){return S(this,D)}clearCofactor(){const{h:D}=e;return D===j?this:this.multiplyUnsafe(D)}static fromHex(D,b=!1){const{d:w,a:C}=e,B=n.BYTES;D=K("pointHex",D,B),Ut("zip215",b);const A=D.slice(),T=D[B-1];A[B-1]=T&-129;const _=wt(A),U=b?f:n.ORDER;Dt("pointHex.y",_,Z,U);const q=c(_*_),k=c(q-j),J=c(w*q-C);let{isValid:W,value:G}=p(k,J);if(!W)throw new Error("Point.fromHex: invalid y coordinate");const P=(G&j)===j,et=(T&128)!==0;if(!b&&G===Z&&et)throw new Error("Point.fromHex: x=0 and x_0=1");return et!==P&&(G=c(-G)),d.fromAffine({x:G,y:_})}static fromPrivateKey(D){return O(D).point}toRawBytes(){const{x:D,y:b}=this.toAffine(),w=Ft(b,n.BYTES);return w[w.length-1]|=D&j?128:0,w}toHex(){return Nt(this.toRawBytes())}}d.BASE=new d(e.Gx,e.Gy,j,c(e.Gx*e.Gy)),d.ZERO=new d(Z,j,j,Z);const{BASE:m,ZERO:N}=d,R=lr(d,u*8);function H(y){return M(y,r)}function v(y){return H(wt(y))}function O(y){const D=n.BYTES;y=K("private key",y,D);const b=K("hashed private key",s(y),2*D),w=E(b.slice(0,D)),C=b.slice(D,2*D),B=v(w),A=m.multiply(B),T=A.toRawBytes();return{head:w,prefix:C,scalar:B,point:A,pointBytes:T}}function ot(y){return O(y).pointBytes}function tt(y=new Uint8Array,...D){const b=xe(...D);return v(s(l(b,K("context",y),!!o)))}function st(y,D,b={}){y=K("message",y),o&&(y=o(y));const{prefix:w,scalar:C,pointBytes:B}=O(D),A=tt(b.context,w,y),T=m.multiply(A).toRawBytes(),_=tt(b.context,T,B,y),U=H(A+_*C);Dt("signature.s",U,Z,r);const q=xe(T,Ft(U,n.BYTES));return K("result",q,n.BYTES*2)}const at=Er;function Bt(y,D,b,w=at){const{context:C,zip215:B}=w,A=n.BYTES;y=K("signature",y,2*A),D=K("message",D),b=K("publicKey",b,A),B!==void 0&&Ut("zip215",B),o&&(D=o(D));const T=wt(y.slice(A,2*A));let _,U,q;try{_=d.fromHex(b,B),U=d.fromHex(y.slice(0,A),B),q=m.multiplyUnsafe(T)}catch{return!1}if(!B&&_.isSmallOrder())return!1;const k=tt(C,U.toRawBytes(),_.toRawBytes(),D);return U.add(_.multiplyUnsafe(k)).subtract(q).clearCofactor().equals(d.ZERO)}return m._setWindowSize(8),{CURVE:e,getPublicKey:ot,sign:st,verify:Bt,ExtendedPoint:d,utils:{getExtendedPublicKey:O,randomPrivateKey:()=>a(n.BYTES),precompute(y=8,D=d.BASE){return D._setWindowSize(y),D.multiply(BigInt(3)),D}}}}BigInt(0),BigInt(1);const $t=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949"),Ue=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752");BigInt(0);const yr=BigInt(1),Ne=BigInt(2);BigInt(3);const xr=BigInt(5),Cr=BigInt(8);function Br(t){const e=BigInt(10),n=BigInt(20),r=BigInt(40),o=BigInt(80),s=$t,u=t*t%s*t%s,i=V(u,Ne,s)*u%s,f=V(i,yr,s)*t%s,c=V(f,xr,s)*f%s,h=V(c,e,s)*c%s,p=V(h,n,s)*h%s,E=V(p,r,s)*p%s,l=V(E,o,s)*E%s,g=V(l,o,s)*E%s,I=V(g,e,s)*c%s;return{pow_p_5_8:V(I,Ne,s)*t%s,b2:u}}function Ar(t){return t[0]&=248,t[31]&=127,t[31]|=64,t}function mr(t,e){const n=$t,r=M(e*e*e,n),o=M(r*r*e,n),s=Br(t*o).pow_p_5_8;let a=M(t*r*s,n);const u=M(e*a*a,n),i=a,f=M(a*Ue,n),c=u===t,h=u===M(-t,n),p=u===M(-t*Ue,n);return c&&(a=i),(h||p)&&(a=f),ir(a,n)&&(a=M(-a,n)),{isValid:c||h,value:a}}const _r=(()=>Ie($t,void 0,!0))(),Ir=(()=>({a:BigInt(-1),d:BigInt("37095705934669439343138083508754565189542113879843219016388785533085940283555"),Fp:_r,n:BigInt("7237005577332262213973186563042994240857116359379907606001950938285454250989"),h:Cr,Gx:BigInt("15112221349535400772501151409588531511454012693041857206046113283949847762202"),Gy:BigInt("46316835694926478169428394003475163141307993866256225615783033603165251855960"),hash:Vn,randomBytes:he,adjustScalarBytes:Ar,uvRatio:mr}))(),kt=(()=>gr(Ir))(),jt="EdDSA",Gt="JWT",ut=".",ft="base64url",Zt="utf8",xt="utf8",Jt=":",Wt="did",Vt="key",dt="base58btc",Yt="z",Kt="K36",Fe=32,Le=32;function Pt(t){return globalThis.Buffer!=null?new Uint8Array(t.buffer,t.byteOffset,t.byteLength):t}function Oe(t=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?Pt(globalThis.Buffer.allocUnsafe(t)):new Uint8Array(t)}function Me(t,e){e||(e=t.reduce((o,s)=>o+s.length,0));const n=Oe(e);let r=0;for(const o of t)n.set(o,r),r+=o.length;return Pt(n)}function Sr(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var n=new Uint8Array(256),r=0;r<n.length;r++)n[r]=255;for(var o=0;o<t.length;o++){var s=t.charAt(o),a=s.charCodeAt(0);if(n[a]!==255)throw new TypeError(s+" is ambiguous");n[a]=o}var u=t.length,i=t.charAt(0),f=Math.log(u)/Math.log(256),c=Math.log(256)/Math.log(u);function h(l){if(l instanceof Uint8Array||(ArrayBuffer.isView(l)?l=new Uint8Array(l.buffer,l.byteOffset,l.byteLength):Array.isArray(l)&&(l=Uint8Array.from(l))),!(l instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(l.length===0)return"";for(var g=0,I=0,S=0,L=l.length;S!==L&&l[S]===0;)S++,g++;for(var d=(L-S)*c+1>>>0,m=new Uint8Array(d);S!==L;){for(var N=l[S],R=0,H=d-1;(N!==0||R<I)&&H!==-1;H--,R++)N+=256*m[H]>>>0,m[H]=N%u>>>0,N=N/u>>>0;if(N!==0)throw new Error("Non-zero carry");I=R,S++}for(var v=d-I;v!==d&&m[v]===0;)v++;for(var O=i.repeat(g);v<d;++v)O+=t.charAt(m[v]);return O}function p(l){if(typeof l!="string")throw new TypeError("Expected String");if(l.length===0)return new Uint8Array;var g=0;if(l[g]!==" "){for(var I=0,S=0;l[g]===i;)I++,g++;for(var L=(l.length-g)*f+1>>>0,d=new Uint8Array(L);l[g];){var m=n[l.charCodeAt(g)];if(m===255)return;for(var N=0,R=L-1;(m!==0||N<S)&&R!==-1;R--,N++)m+=u*d[R]>>>0,d[R]=m%256>>>0,m=m/256>>>0;if(m!==0)throw new Error("Non-zero carry");S=N,g++}if(l[g]!==" "){for(var H=L-S;H!==L&&d[H]===0;)H++;for(var v=new Uint8Array(I+(L-H)),O=I;H!==L;)v[O++]=d[H++];return v}}}function E(l){var g=p(l);if(g)return g;throw new Error(`Non-${e} character`)}return{encode:h,decodeUnsafe:p,decode:E}}var vr=Sr,Tr=vr;const He=t=>{if(t instanceof Uint8Array&&t.constructor.name==="Uint8Array")return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")},Ur=t=>new TextEncoder().encode(t),Nr=t=>new TextDecoder().decode(t);class Fr{constructor(e,n,r){this.name=e,this.prefix=n,this.baseEncode=r}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class Lr{constructor(e,n,r){if(this.name=e,this.prefix=n,n.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=n.codePointAt(0),this.baseDecode=r}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return ze(this,e)}}class Or{constructor(e){this.decoders=e}or(e){return ze(this,e)}decode(e){const n=e[0],r=this.decoders[n];if(r)return r.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const ze=(t,e)=>new Or({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class Mr{constructor(e,n,r,o){this.name=e,this.prefix=n,this.baseEncode=r,this.baseDecode=o,this.encoder=new Fr(e,n,r),this.decoder=new Lr(e,n,o)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const Ct=({name:t,prefix:e,encode:n,decode:r})=>new Mr(t,e,n,r),lt=({prefix:t,name:e,alphabet:n})=>{const{encode:r,decode:o}=Tr(n,e);return Ct({prefix:t,name:e,encode:r,decode:s=>He(o(s))})},Hr=(t,e,n,r)=>{const o={};for(let c=0;c<e.length;++c)o[e[c]]=c;let s=t.length;for(;t[s-1]==="=";)--s;const a=new Uint8Array(s*n/8|0);let u=0,i=0,f=0;for(let c=0;c<s;++c){const h=o[t[c]];if(h===void 0)throw new SyntaxError(`Non-${r} character`);i=i<<n|h,u+=n,u>=8&&(u-=8,a[f++]=255&i>>u)}if(u>=n||255&i<<8-u)throw new SyntaxError("Unexpected end of data");return a},zr=(t,e,n)=>{const r=e[e.length-1]==="=",o=(1<<n)-1;let s="",a=0,u=0;for(let i=0;i<t.length;++i)for(u=u<<8|t[i],a+=8;a>n;)a-=n,s+=e[o&u>>a];if(a&&(s+=e[o&u<<n-a]),r)for(;s.length*n&7;)s+="=";return s},$=({name:t,prefix:e,bitsPerChar:n,alphabet:r})=>Ct({prefix:e,name:t,encode(o){return zr(o,r,n)},decode(o){return Hr(o,r,n,t)}}),Rr=Ct({prefix:"\0",name:"identity",encode:t=>Nr(t),decode:t=>Ur(t)});var qr=Object.freeze({__proto__:null,identity:Rr});const $r=$({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var kr=Object.freeze({__proto__:null,base2:$r});const jr=$({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var Gr=Object.freeze({__proto__:null,base8:jr});const Zr=lt({prefix:"9",name:"base10",alphabet:"0123456789"});var Jr=Object.freeze({__proto__:null,base10:Zr});const Wr=$({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),Vr=$({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var Yr=Object.freeze({__proto__:null,base16:Wr,base16upper:Vr});const Kr=$({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),Pr=$({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),Xr=$({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),Qr=$({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),to=$({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),eo=$({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),no=$({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),ro=$({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),oo=$({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var so=Object.freeze({__proto__:null,base32:Kr,base32upper:Pr,base32pad:Xr,base32padupper:Qr,base32hex:to,base32hexupper:eo,base32hexpad:no,base32hexpadupper:ro,base32z:oo});const io=lt({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),uo=lt({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var co=Object.freeze({__proto__:null,base36:io,base36upper:uo});const ao=lt({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),Do=lt({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var fo=Object.freeze({__proto__:null,base58btc:ao,base58flickr:Do});const lo=$({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),ho=$({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),bo=$({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),po=$({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var Eo=Object.freeze({__proto__:null,base64:lo,base64pad:ho,base64url:bo,base64urlpad:po});const Re=Array.from("\u{1F680}\u{1FA90}\u2604\u{1F6F0}\u{1F30C}\u{1F311}\u{1F312}\u{1F313}\u{1F314}\u{1F315}\u{1F316}\u{1F317}\u{1F318}\u{1F30D}\u{1F30F}\u{1F30E}\u{1F409}\u2600\u{1F4BB}\u{1F5A5}\u{1F4BE}\u{1F4BF}\u{1F602}\u2764\u{1F60D}\u{1F923}\u{1F60A}\u{1F64F}\u{1F495}\u{1F62D}\u{1F618}\u{1F44D}\u{1F605}\u{1F44F}\u{1F601}\u{1F525}\u{1F970}\u{1F494}\u{1F496}\u{1F499}\u{1F622}\u{1F914}\u{1F606}\u{1F644}\u{1F4AA}\u{1F609}\u263A\u{1F44C}\u{1F917}\u{1F49C}\u{1F614}\u{1F60E}\u{1F607}\u{1F339}\u{1F926}\u{1F389}\u{1F49E}\u270C\u2728\u{1F937}\u{1F631}\u{1F60C}\u{1F338}\u{1F64C}\u{1F60B}\u{1F497}\u{1F49A}\u{1F60F}\u{1F49B}\u{1F642}\u{1F493}\u{1F929}\u{1F604}\u{1F600}\u{1F5A4}\u{1F603}\u{1F4AF}\u{1F648}\u{1F447}\u{1F3B6}\u{1F612}\u{1F92D}\u2763\u{1F61C}\u{1F48B}\u{1F440}\u{1F62A}\u{1F611}\u{1F4A5}\u{1F64B}\u{1F61E}\u{1F629}\u{1F621}\u{1F92A}\u{1F44A}\u{1F973}\u{1F625}\u{1F924}\u{1F449}\u{1F483}\u{1F633}\u270B\u{1F61A}\u{1F61D}\u{1F634}\u{1F31F}\u{1F62C}\u{1F643}\u{1F340}\u{1F337}\u{1F63B}\u{1F613}\u2B50\u2705\u{1F97A}\u{1F308}\u{1F608}\u{1F918}\u{1F4A6}\u2714\u{1F623}\u{1F3C3}\u{1F490}\u2639\u{1F38A}\u{1F498}\u{1F620}\u261D\u{1F615}\u{1F33A}\u{1F382}\u{1F33B}\u{1F610}\u{1F595}\u{1F49D}\u{1F64A}\u{1F639}\u{1F5E3}\u{1F4AB}\u{1F480}\u{1F451}\u{1F3B5}\u{1F91E}\u{1F61B}\u{1F534}\u{1F624}\u{1F33C}\u{1F62B}\u26BD\u{1F919}\u2615\u{1F3C6}\u{1F92B}\u{1F448}\u{1F62E}\u{1F646}\u{1F37B}\u{1F343}\u{1F436}\u{1F481}\u{1F632}\u{1F33F}\u{1F9E1}\u{1F381}\u26A1\u{1F31E}\u{1F388}\u274C\u270A\u{1F44B}\u{1F630}\u{1F928}\u{1F636}\u{1F91D}\u{1F6B6}\u{1F4B0}\u{1F353}\u{1F4A2}\u{1F91F}\u{1F641}\u{1F6A8}\u{1F4A8}\u{1F92C}\u2708\u{1F380}\u{1F37A}\u{1F913}\u{1F619}\u{1F49F}\u{1F331}\u{1F616}\u{1F476}\u{1F974}\u25B6\u27A1\u2753\u{1F48E}\u{1F4B8}\u2B07\u{1F628}\u{1F31A}\u{1F98B}\u{1F637}\u{1F57A}\u26A0\u{1F645}\u{1F61F}\u{1F635}\u{1F44E}\u{1F932}\u{1F920}\u{1F927}\u{1F4CC}\u{1F535}\u{1F485}\u{1F9D0}\u{1F43E}\u{1F352}\u{1F617}\u{1F911}\u{1F30A}\u{1F92F}\u{1F437}\u260E\u{1F4A7}\u{1F62F}\u{1F486}\u{1F446}\u{1F3A4}\u{1F647}\u{1F351}\u2744\u{1F334}\u{1F4A3}\u{1F438}\u{1F48C}\u{1F4CD}\u{1F940}\u{1F922}\u{1F445}\u{1F4A1}\u{1F4A9}\u{1F450}\u{1F4F8}\u{1F47B}\u{1F910}\u{1F92E}\u{1F3BC}\u{1F975}\u{1F6A9}\u{1F34E}\u{1F34A}\u{1F47C}\u{1F48D}\u{1F4E3}\u{1F942}"),wo=Re.reduce((t,e,n)=>(t[n]=e,t),[]),go=Re.reduce((t,e,n)=>(t[e.codePointAt(0)]=n,t),[]);function yo(t){return t.reduce((e,n)=>(e+=wo[n],e),"")}function xo(t){const e=[];for(const n of t){const r=go[n.codePointAt(0)];if(r===void 0)throw new Error(`Non-base256emoji character: ${n}`);e.push(r)}return new Uint8Array(e)}const Co=Ct({prefix:"\u{1F680}",name:"base256emoji",encode:yo,decode:xo});var Bo=Object.freeze({__proto__:null,base256emoji:Co}),Ao=$e,qe=128,mo=127,_o=~mo,Io=Math.pow(2,31);function $e(t,e,n){e=e||[],n=n||0;for(var r=n;t>=Io;)e[n++]=t&255|qe,t/=128;for(;t&_o;)e[n++]=t&255|qe,t>>>=7;return e[n]=t|0,$e.bytes=n-r+1,e}var So=Xt,vo=128,ke=127;function Xt(t,r){var n=0,r=r||0,o=0,s=r,a,u=t.length;do{if(s>=u)throw Xt.bytes=0,new RangeError("Could not decode varint");a=t[s++],n+=o<28?(a&ke)<<o:(a&ke)*Math.pow(2,o),o+=7}while(a>=vo);return Xt.bytes=s-r,n}var To=Math.pow(2,7),Uo=Math.pow(2,14),No=Math.pow(2,21),Fo=Math.pow(2,28),Lo=Math.pow(2,35),Oo=Math.pow(2,42),Mo=Math.pow(2,49),Ho=Math.pow(2,56),zo=Math.pow(2,63),Ro=function(t){return t<To?1:t<Uo?2:t<No?3:t<Fo?4:t<Lo?5:t<Oo?6:t<Mo?7:t<Ho?8:t<zo?9:10},qo={encode:Ao,decode:So,encodingLength:Ro},je=qo;const Ge=(t,e,n=0)=>(je.encode(t,e,n),e),Ze=t=>je.encodingLength(t),Qt=(t,e)=>{const n=e.byteLength,r=Ze(t),o=r+Ze(n),s=new Uint8Array(o+n);return Ge(t,s,0),Ge(n,s,r),s.set(e,o),new $o(t,n,e,s)};class $o{constructor(e,n,r,o){this.code=e,this.size=n,this.digest=r,this.bytes=o}}const Je=({name:t,code:e,encode:n})=>new ko(t,e,n);class ko{constructor(e,n,r){this.name=e,this.code=n,this.encode=r}digest(e){if(e instanceof Uint8Array){const n=this.encode(e);return n instanceof Uint8Array?Qt(this.code,n):n.then(r=>Qt(this.code,r))}else throw Error("Unknown type, must be binary type")}}const We=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),jo=Je({name:"sha2-256",code:18,encode:We("SHA-256")}),Go=Je({name:"sha2-512",code:19,encode:We("SHA-512")});var Zo=Object.freeze({__proto__:null,sha256:jo,sha512:Go});const Ve=0,Jo="identity",Ye=He,Wo=t=>Qt(Ve,Ye(t)),Vo={code:Ve,name:Jo,encode:Ye,digest:Wo};var Yo=Object.freeze({__proto__:null,identity:Vo});new TextEncoder,new TextDecoder;const Ke={...qr,...kr,...Gr,...Jr,...Yr,...so,...co,...fo,...Eo,...Bo};({...Zo,...Yo});function Pe(t,e,n,r){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:n},decoder:{decode:r}}}const Xe=Pe("utf8","u",t=>"u"+new TextDecoder("utf8").decode(t),t=>new TextEncoder().encode(t.substring(1))),te=Pe("ascii","a",t=>{let e="a";for(let n=0;n<t.length;n++)e+=String.fromCharCode(t[n]);return e},t=>{t=t.substring(1);const e=Oe(t.length);for(let n=0;n<t.length;n++)e[n]=t.charCodeAt(n);return e}),Qe={utf8:Xe,"utf-8":Xe,hex:Ke.base16,latin1:te,ascii:te,binary:te,...Ke};function ct(t,e="utf8"){const n=Qe[e];if(!n)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t.buffer,t.byteOffset,t.byteLength).toString("utf8"):n.encoder.encode(t).substring(1)}function rt(t,e="utf8"){const n=Qe[e];if(!n)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?Pt(globalThis.Buffer.from(t,"utf-8")):n.decoder.decode(`${n.prefix}${t}`)}function ht(t){return De.safeJsonParse(ct(rt(t,ft),Zt))}function bt(t){return ct(rt(De.safeJsonStringify(t),Zt),ft)}function tn(t){const e=rt(Kt,dt),n=Yt+ct(Me([e,t]),dt);return[Wt,Vt,n].join(Jt)}function en(t){const[e,n,r]=t.split(Jt);if(e!==Wt||n!==Vt)throw new Error('Issuer must be a DID with method "key"');if(r.slice(0,1)!==Yt)throw new Error("Issuer must be a key in mulicodec format");const o=rt(r.slice(1),dt);if(ct(o.slice(0,2),dt)!==Kt)throw new Error('Issuer must be a public key with type "Ed25519"');const s=o.slice(2);if(s.length!==Fe)throw new Error("Issuer must be a public key with length 32 bytes");return s}function nn(t){return ct(t,ft)}function rn(t){return rt(t,ft)}function on(t){return rt([bt(t.header),bt(t.payload)].join(ut),xt)}function Ko(t){const e=ct(t,xt).split(ut),n=ht(e[0]),r=ht(e[1]);return{header:n,payload:r}}function sn(t){return[bt(t.header),bt(t.payload),nn(t.signature)].join(ut)}function un(t){const e=t.split(ut),n=ht(e[0]),r=ht(e[1]),o=rn(e[2]),s=rt(e.slice(0,2).join(ut),xt);return{header:n,payload:r,signature:o,data:s}}function Po(t=he(Le)){const e=kt.getPublicKey(t);return{secretKey:Me([t,e]),publicKey:e}}async function Xo(t,e,n,r,o=pn.fromMiliseconds(Date.now())){const s={alg:jt,typ:Gt},a=tn(r.publicKey),u=o+n,i={iss:a,sub:t,aud:e,iat:o,exp:u},f=on({header:s,payload:i}),c=kt.sign(f,r.secretKey.slice(0,32));return sn({header:s,payload:i,signature:c})}async function Qo(t){const{header:e,payload:n,data:r,signature:o}=un(t);if(e.alg!==jt||e.typ!==Gt)throw new Error("JWT must use EdDSA algorithm");const s=en(n.iss);return kt.verify(o,r,s)}exports.DATA_ENCODING=xt,exports.DID_DELIMITER=Jt,exports.DID_METHOD=Vt,exports.DID_PREFIX=Wt,exports.JSON_ENCODING=Zt,exports.JWT_DELIMITER=ut,exports.JWT_ENCODING=ft,exports.JWT_IRIDIUM_ALG=jt,exports.JWT_IRIDIUM_TYP=Gt,exports.KEY_PAIR_SEED_LENGTH=Le,exports.MULTICODEC_ED25519_BASE=Yt,exports.MULTICODEC_ED25519_ENCODING=dt,exports.MULTICODEC_ED25519_HEADER=Kt,exports.MULTICODEC_ED25519_LENGTH=Fe,exports.decodeData=Ko,exports.decodeIss=en,exports.decodeJSON=ht,exports.decodeJWT=un,exports.decodeSig=rn,exports.encodeData=on,exports.encodeIss=tn,exports.encodeJSON=bt,exports.encodeJWT=sn,exports.encodeSig=nn,exports.generateKeyPair=Po,exports.signJWT=Xo,exports.verifyJWT=Qo;
//# sourceMappingURL=index.cjs.js.map
