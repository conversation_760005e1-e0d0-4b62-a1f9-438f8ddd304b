const WebSocket = require('ws');
const net = require('net');
const { v4: uuidv4 } = require('uuid');
const Redis = require('ioredis');
const redisConfig = require('../config/redis');

// Import environment configuration
const { ENV, IS_PROD, IS_DEV, FEATURES, config, isFeatureEnabled } = require('../config/environment');

class OrderMatchingService {
  constructor() {
    this.ws = null;
    this.callbackMap = new Map();
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.pendingRequests = [];
    this.recentTrades = new Map(); // Map of symbol -> array of trades
    this.pingInterval = null;
    this.reconnectTimeout = null;
    this.lastPongTime = Date.now(); // Track last pong time for connection health

    // Mock data for when the Order Matching Engine is not available
    this.mockOrderBook = {
      'SOL/USDC': {
        bids: [
          { price: 99.5, size: 10 },
          { price: 99.0, size: 20 },
          { price: 98.5, size: 30 }
        ],
        asks: [
          { price: 100.5, size: 15 },
          { price: 101.0, size: 25 },
          { price: 101.5, size: 35 }
        ]
      }
    };

    // Fallback mode - will be used if WebSocket connection fails
    this.useFallbackMode = false;
    this.mockOrderBook = new Map(); // Map of symbol -> { bids: [], asks: [] }
    this.mockOrders = new Map(); // Map of orderId -> order
    this.nextOrderId = 1000;

    // Start in fallback mode by default to prevent server crashes
    this.useFallbackMode = true;
    this.isConnected = true;

    // Try to connect to the Order Matching Engine in the background
    setTimeout(() => this.connect(), 1000);

    // After 3 failed connection attempts, switch to fallback mode
    this.maxConnectionAttempts = 3;

    // Try to initialize Redis
    try {
      if (process.env.REDIS_USE_LOCAL === 'true') {
        // Use local Redis
        this.redis = new Redis(config.redis);
        // Subscribe to trade channel for real-time updates
        this.redis.subscribe('trades');
        this.redis.on('message', (channel, message) => {
          if (channel === 'trades') {
            try {
              const trade = JSON.parse(message);
              this.addRecentTrade(trade);
            } catch (error) {
              console.error('Error processing trade from Redis:', error);
            }
          }
        });
      } else {
        // Use Upstash Redis or dummy implementation
        this.redis = {
          publish: async () => true,
          subscribe: async () => true,
          on: () => {}
        };
        console.log('Using dummy Redis implementation for order matching service');
      }
    } catch (error) {
      console.error('Failed to initialize Redis for order matching service:', error.message);
      // Create dummy Redis implementation
      this.redis = {
        publish: async () => true,
        subscribe: async () => true,
        on: () => {}
      };
    }

    // Try to connect immediately
    this.connect();

    // Set up periodic reconnect
    setInterval(() => {
      if (!this.isConnected) {
        console.log('Attempting to reconnect to Order Matching Engine...');
        this.connect();
      }
    }, 5000);
  }

  connect() {
    try {
      // If already in fallback mode, just log and return
      if (this.useFallbackMode) {
        console.log('Using fallback mode for Order Matching Engine');
        this.isConnected = true;
        return;
      }

      // If we already have a connection, close it first
      if (this.ws) {
        try {
          this.ws.terminate();
        } catch (err) {
          // Ignore errors when terminating
        }
        this.ws = null;
      }

      // If we've tried to connect more than maxConnectionAttempts times, switch to fallback mode
      if (this.connectionAttempts >= this.maxConnectionAttempts) {
        console.log('Switching to fallback mode after multiple failed connection attempts');
        this.useFallbackMode = true;
        this.isConnected = true;
        this.processPendingRequests();
        return;
      }

      this.connectionAttempts++;
      console.log(`Attempt ${this.connectionAttempts} to connect to Order Matching Engine...`);

      // Create a new TCP socket connection with a timeout
      // Check if the order matching engine is running on the expected port
      try {
        console.log('Creating TCP socket connection to order matching engine...');

        // Connect directly to the C++ Order Matching Engine TCP server
        const host = 'localhost';
        const port = 9002;
        console.log(`Connecting directly to Order Matching Engine at ${host}:${port}`);

        // Create a TCP socket connection
        this.socket = new net.Socket();

        // Connect to the C++ Order Matching Engine
        this.socket.connect(port, host);

        // Set up event handlers for the TCP socket

        // Set a connection timeout
        const connectionTimeout = setTimeout(() => {
          if (!this.isConnected) {
            console.log('TCP socket connection timeout');
            try {
              this.socket.destroy();
            } catch (err) {
              // Ignore errors when destroying
            }
            this.socket = null;
            this.isConnected = false;
          }
        }, 10000); // 10 seconds timeout

        // Handle connection to the C++ Order Matching Engine
        this.socket.on('connect', () => {
        console.log('Successfully connected to Order Matching Engine');
        this.isConnected = true;
        this.connectionAttempts = 0;
        clearTimeout(connectionTimeout);

          // Process any pending requests
          while (this.pendingRequests.length > 0) {
            const { request, callback } = this.pendingRequests.shift();
            this.sendRequest(request, callback);
          }

          // Call the sendPing method to set up the ping interval
          this.sendPing();

          // Send a properly formatted JSON message to verify the connection
          try {
            // Wait a short time before sending the first ping
            setTimeout(() => {
              if (this.socket && !this.socket.destroyed) {
                const pingMessage = JSON.stringify({
                  type: 'ping',
                  requestId: uuidv4(),
                  timestamp: Date.now()
                });
                this.socket.write(pingMessage);
                console.log('Sent initial JSON ping to verify connection');
              }
            }, 1000);
          } catch (error) {
            console.error('Error sending initial ping:', error.message);
          }
        });

        // Handle data from the C++ Order Matching Engine
        this.socket.on('data', (data) => {
          try {
            console.log('Received data from Order Matching Engine');

            // Convert data to string
            const messageStr = data.toString('utf8');

            console.log('Raw message:', messageStr);

            // Update last pong time for any message
            this.lastPongTime = Date.now();

            // Try to parse as JSON
            let response;
            try {
              response = JSON.parse(messageStr);

              // Handle ping/pong messages specially
              if (response.type === 'ping') {
                console.log('Received ping from Order Matching Engine, sending pong');
                if (this.socket && !this.socket.destroyed) {
                  const pongMessage = JSON.stringify({
                    type: 'pong',
                    requestId: response.requestId || uuidv4(),
                    timestamp: Date.now()
                  });
                  this.socket.write(pongMessage);
                }
                return;
              } else if (response.type === 'pong') {
                console.log('Received pong from Order Matching Engine');
                return;
              }

              // Handle other responses
              if (response.requestId) {
                const callback = this.callbackMap.get(response.requestId);
                if (callback) {
                  callback(response);
                  this.callbackMap.delete(response.requestId);
                  console.log(`Processed response for request ${response.requestId}`);
                } else {
                  console.log(`No callback found for request ${response.requestId}`);
                }
              } else {
                console.log('Received message without requestId:', response.type || response.message || 'unknown type');
              }
            } catch (parseError) {
              console.error('Error parsing JSON message:', parseError.message);
              console.log('Raw message was:', messageStr);
            }
          } catch (error) {
            console.error('Error handling message from Order Matching Engine:', error.message);
            console.error('Error stack:', error.stack);
          }
        });

        // Handle C++ Order Matching Engine disconnection
        this.socket.on('close', () => {
          console.log('Disconnected from Order Matching Engine');
          this.isConnected = false;
          clearTimeout(connectionTimeout);

          // Clear any ping interval
          if (this.pingInterval) {
            clearInterval(this.pingInterval);
            this.pingInterval = null;
          }

          // Clean up the socket object
          this.socket = null;

          console.log('Attempting to reconnect after disconnect...');

          // Wait a short time before reconnecting
          setTimeout(() => {
            this.connect();
          }, 2000); // 2 seconds delay
        });

        // Handle C++ Order Matching Engine errors
        this.socket.on('error', (error) => {
          console.error('Order Matching Engine connection error:', error.code);
          console.error('Error message:', error.message);
          console.error('Error stack:', error.stack);
          console.error('Error number:', error.errno);
          console.error('System call:', error.syscall);
          console.error('Address:', error.address);
          console.error('Port:', error.port);

          // Don't set isConnected to false here, let the close event handle it

          // Switch to fallback mode after a few attempts
          if (this.connectionAttempts >= this.maxConnectionAttempts) {
            console.log('Switching to fallback mode after connection errors');
            this.useFallbackMode = true;
            this.isConnected = true;
          }
        });
      } catch (error) {
        console.error('Failed to create TCP socket connection:', error.message);
        this.isConnected = false;

        // If we can't even create the socket, try again later
        setTimeout(() => this.connect(), 5000);
        return;
      }

      this.ws.on('message', (data) => {
        try {
          console.log('Received message from Order Matching Engine');

          // Convert data to string with proper handling for different formats
          let messageStr;
          if (data instanceof Buffer) {
            messageStr = data.toString('utf8');
          } else if (typeof data === 'string') {
            messageStr = data;
          } else {
            messageStr = String(data);
          }

          console.log('Raw message:', messageStr);

          // Update last pong time for any message
          this.lastPongTime = Date.now();

          // Try to parse as JSON
          let response;
          try {
            response = JSON.parse(messageStr);

            // Handle ping/pong messages specially
            if (response.type === 'ping') {
              console.log('Received ping from Order Matching Engine, sending pong');
              if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                const pongMessage = JSON.stringify({
                  type: 'pong',
                  requestId: response.requestId || uuidv4(),
                  timestamp: Date.now()
                });
                this.ws.send(pongMessage);
              }
              return;
            } else if (response.type === 'pong') {
              console.log('Received pong from Order Matching Engine');
              return;
            }
          } catch (parseError) {
            // Not valid JSON, check for simple string messages
            if (messageStr === 'ping') {
              console.log('Received simple ping from Order Matching Engine, sending JSON pong');
              if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                const pongMessage = JSON.stringify({
                  type: 'pong',
                  requestId: uuidv4(),
                  timestamp: Date.now()
                });
                this.ws.send(pongMessage);
              }
              return;
            } else if (messageStr === 'pong') {
              console.log('Received simple pong from Order Matching Engine');
              return;
            }

            // Not a recognized message format
            console.log('Received non-JSON message:', messageStr);
            return;
          }

          // Handle other responses
          if (response.requestId) {
            const callback = this.callbackMap.get(response.requestId);
            if (callback) {
              callback(response);
              this.callbackMap.delete(response.requestId);
              console.log(`Processed response for request ${response.requestId}`);
            } else {
              console.log(`No callback found for request ${response.requestId}`);
            }
          } else {
            console.log('Received message without requestId:', response.type || response.message || 'unknown type');
          }
        } catch (error) {
          console.error('Error handling message from Order Matching Engine:', error.message);
          console.error('Error stack:', error.stack);
        }
      });

      this.ws.on('close', (code, reason) => {
        console.log(`Disconnected from Order Matching Engine: Code ${code}, Reason: ${reason || 'No reason provided'}`);
        this.isConnected = false;
        clearTimeout(connectionTimeout);

        // Clear any ping interval
        if (this.pingInterval) {
          clearInterval(this.pingInterval);
          this.pingInterval = null;
        }

        // Clean up the WebSocket object
        this.ws = null;

        console.log('Attempting to reconnect after disconnect...');

        // Handle different close codes
        if (code === 1006) { // Abnormal closure
          console.log('Abnormal closure detected, reconnecting after a short delay...');
          setTimeout(() => {
            this.connect();
          }, 2000); // 2 seconds delay
        } else {
          // Try to reconnect after a short delay for other close codes
          setTimeout(() => {
            if (!this.isConnected) {
              console.log('Attempting to reconnect after disconnect...');
              this.connect();
            }
          }, 3000); // 3 seconds delay
        }
      });

      this.ws.on('error', (error) => {
        // Only log the error once, not the full stack trace
        console.error(`Order Matching Engine connection error: ${error.code || 'UNKNOWN'}`);
        console.error(`Error message: ${error.message || 'No error message'}`);
        this.isConnected = false;
        clearTimeout(connectionTimeout);

        // Clean up the WebSocket object on error
        if (this.ws) {
          try {
            this.ws.terminate();
          } catch (e) {
            // Ignore errors when terminating
          }
          this.ws = null;
        }

        // Try to reconnect after a short delay
        setTimeout(() => {
          if (!this.isConnected) {
            console.log('Attempting to reconnect after error...');
            this.connect();
          }
        }, 3000);
      });
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error.message);
      this.isConnected = false;
    }
  }

  // Send a ping to verify the connection
  sendPing() {
    if (!this.pingInterval) {
      this.lastPongTime = Date.now();

      // Set up a ping interval to keep the connection alive
      this.pingInterval = setInterval(() => {
        // Check if we're using the socket connection
        if (this.isConnected && this.socket && !this.socket.destroyed) {
          try {
            // Send a properly formatted JSON ping message
            const pingMessage = JSON.stringify({
              type: 'ping',
              requestId: uuidv4(),
              timestamp: Date.now()
            });
            this.socket.write(pingMessage);
            console.log('Sent JSON ping to Order Matching Engine');

            // Check if we've received a response within a reasonable time
            const now = Date.now();
            const timeSinceLastPong = now - this.lastPongTime;

            if (timeSinceLastPong > 120000) { // 2 minutes timeout
              console.log(`No response received in ${Math.round(timeSinceLastPong/1000)} seconds, reconnecting...`);
              this.isConnected = false;

              // Close the existing connection
              try {
                if (this.socket) {
                  this.socket.destroy();
                  this.socket = null;
                }
              } catch (e) {
                // Ignore errors when destroying
              }

              // Reconnect after a short delay
              setTimeout(() => this.connect(), 2000);
              return;
            }
          } catch (error) {
            console.error('Error sending ping:', error.message);
            this.isConnected = false;

            // Close the existing connection
            try {
              if (this.socket) {
                this.socket.destroy();
                this.socket = null;
              }
            } catch (e) {
              // Ignore errors when destroying
            }

            // Reconnect after a short delay
            setTimeout(() => this.connect(), 2000);
            return;
          }
        }
        // Check if we're using the WebSocket connection
        else if (this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN) {
          try {
            // Send a properly formatted JSON ping message
            const pingMessage = JSON.stringify({
              type: 'ping',
              requestId: uuidv4(),
              timestamp: Date.now()
            });
            this.ws.send(pingMessage);
            console.log('Sent JSON ping to Order Matching Engine via WebSocket');

            // Check if we've received a response within a reasonable time
            const now = Date.now();
            const timeSinceLastPong = now - this.lastPongTime;

            if (timeSinceLastPong > 120000) { // 2 minutes timeout
              console.log(`No response received in ${Math.round(timeSinceLastPong/1000)} seconds, reconnecting...`);
              this.isConnected = false;

              // Close the existing connection
              try {
                if (this.ws) {
                  this.ws.terminate();
                  this.ws = null;
                }
              } catch (e) {
                // Ignore errors when terminating
              }

              // Reconnect after a short delay
              setTimeout(() => this.connect(), 2000);
              return;
            }
          } catch (error) {
            console.error('Error sending ping:', error.message);
            this.isConnected = false;

            // Close the existing connection
            try {
              if (this.ws) {
                this.ws.terminate();
                this.ws = null;
              }
            } catch (e) {
              // Ignore errors when terminating
            }

            // Reconnect after a short delay
            setTimeout(() => this.connect(), 2000);
            return;
          }
        } else if (!this.isConnected) {
          // If we're not connected, try to reconnect
          console.log('Not connected, attempting to reconnect...');
          this.connect();

          // Clear the interval if we're not connected
          clearInterval(this.pingInterval);
          this.pingInterval = null;
        }
      }, 30000); // Send ping every 30 seconds
    }
  }

  // Helper method to send requests or queue them if not connected
  sendRequest(request, callback) {
    try {
      // If in fallback mode, handle the request locally
      if (this.useFallbackMode) {
        console.log(`Handling request ${request.type} in fallback mode`);
        this.handleFallbackRequest(request, callback);
        return;
      }

      if (this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.callbackMap.set(request.requestId, callback);
        this.ws.send(JSON.stringify(request));
      } else {
        console.log('Engine not connected, queuing request:', request.type);
        this.pendingRequests.push({ request, callback });

        // If we're not connected, try to reconnect
        if (!this.isConnected) {
          this.connect();
        }
      }
    } catch (error) {
      console.error('Error sending request:', error.message);
      // Queue the request for retry
      this.pendingRequests.push({ request, callback });
      // Mark as disconnected and try to reconnect
      this.isConnected = false;
      this.connect();
    }
  }

  // Process any pending requests
  processPendingRequests() {
    if (this.pendingRequests.length > 0) {
      console.log(`Processing ${this.pendingRequests.length} pending requests`);

      // Process each pending request
      this.pendingRequests.forEach(({ request, callback }) => {
        if (this.useFallbackMode) {
          this.handleFallbackRequest(request, callback);
        } else if (this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN) {
          this.callbackMap.set(request.requestId, callback);
          this.ws.send(JSON.stringify(request));
        } else {
          callback({ success: false, error: 'Failed to send request to Order Matching Engine' });
        }
      });

      // Clear the pending requests
      this.pendingRequests = [];
    }
  }

  // Handle requests in fallback mode
  handleFallbackRequest(request, callback) {
    console.log(`Handling request ${request.type} in fallback mode`);

    // Simulate a delay to make it feel more realistic
    setTimeout(() => {
      switch (request.type) {
        case 'placeOrder':
        case 'submitOrder':
          this.handleFallbackPlaceOrder(request, callback);
          break;
        case 'cancelOrder':
          this.handleFallbackCancelOrder(request, callback);
          break;
        case 'getOrderBook':
          this.handleFallbackGetOrderBook(request, callback);
          break;
        case 'getOrderBookDepth':
          this.handleFallbackGetOrderBookDepth(request, callback);
          break;
        case 'getMarketData':
          this.handleFallbackGetMarketData(request, callback);
          break;
        case 'getUserOrders':
          this.handleFallbackGetUserOrders(request, callback);
          break;
        default:
          callback({ success: false, error: `Unsupported request type: ${request.type}` });
      }
    }, 100); // Small delay to simulate network latency
  }

  // Fallback implementation for placeOrder
  handleFallbackPlaceOrder(request, callback) {
    const { order } = request;
    const orderId = this.nextOrderId++;

    // Store the order
    const newOrder = {
      ...order,
      orderId,
      status: 'open',
      timestamp: Date.now()
    };

    this.mockOrders.set(orderId, newOrder);

    // Update the order book
    const symbol = order.symbol;
    if (!this.mockOrderBook.has(symbol)) {
      this.mockOrderBook.set(symbol, { bids: [], asks: [] });
    }

    const orderBook = this.mockOrderBook.get(symbol);
    if (order.side === 'buy') {
      orderBook.bids.push({ price: order.price, size: order.size, orderId });
      orderBook.bids.sort((a, b) => b.price - a.price); // Sort bids in descending order
    } else {
      orderBook.asks.push({ price: order.price, size: order.size, orderId });
      orderBook.asks.sort((a, b) => a.price - b.price); // Sort asks in ascending order
    }

    callback({ success: true, orderId });
  }

  // Fallback implementation for cancelOrder
  handleFallbackCancelOrder(request, callback) {
    const { orderId } = request;

    if (this.mockOrders.has(orderId)) {
      const order = this.mockOrders.get(orderId);
      order.status = 'cancelled';

      // Remove from order book
      const symbol = order.symbol;
      if (this.mockOrderBook.has(symbol)) {
        const orderBook = this.mockOrderBook.get(symbol);
        if (order.side === 'buy') {
          orderBook.bids = orderBook.bids.filter(bid => bid.orderId !== orderId);
        } else {
          orderBook.asks = orderBook.asks.filter(ask => ask.orderId !== orderId);
        }
      }

      callback({ success: true });
    } else {
      callback({ success: false, error: 'Order not found' });
    }
  }

  // Fallback implementation for getOrderBook
  handleFallbackGetOrderBook(request, callback) {
    const { symbol } = request;

    if (!this.mockOrderBook.has(symbol)) {
      this.mockOrderBook.set(symbol, { bids: [], asks: [] });
    }

    const orderBook = this.mockOrderBook.get(symbol);
    callback({ success: true, orderBook });
  }

  // Fallback implementation for getMarketData
  handleFallbackGetMarketData(request, callback) {
    const { symbol } = request;

    // Generate some fake market data
    const marketData = {
      symbol,
      lastPrice: 100 + Math.random() * 10,
      volume24h: 1000 + Math.random() * 500,
      high24h: 110 + Math.random() * 5,
      low24h: 95 + Math.random() * 5,
      timestamp: Date.now()
    };

    callback({ success: true, marketData });
  }

  // Fallback implementation for getUserOrders
  handleFallbackGetUserOrders(request, callback) {
    const { walletAddress } = request;

    // Filter orders by wallet address
    const userOrders = Array.from(this.mockOrders.values())
      .filter(order => order.walletAddress === walletAddress);

    callback({ success: true, orders: userOrders });
  }

  async submitOrder(order) {
    return new Promise((resolve, reject) => {
      const requestId = uuidv4();
      const request = {
        requestId,
        type: 'submitOrder',
        order: {
          symbol: order.symbol,
          side: order.side,
          type: order.type,
          price: order.price,
          quantity: order.quantity,
          walletAddress: order.walletAddress
        }
      };

      const callback = (response) => {
        if (response.success) {
          // Add trades to recent trades
          if (response.trades && response.trades.length > 0) {
            response.trades.forEach(trade => this.addRecentTrade(trade));
          }
          resolve(response.trades || []);
        } else {
          reject(new Error(response.error || 'Failed to submit order'));
        }
      };

      this.sendRequest(request, callback);

      // Add timeout after 10 seconds
      setTimeout(() => {
        if (this.callbackMap.has(requestId)) {
          this.callbackMap.delete(requestId);
          reject(new Error('Request timed out. Order Matching Engine might be down.'));
        }
      }, 10000);
    });
  }

  async getOrderBook(symbol) {
    return new Promise((resolve, reject) => {
      const requestId = uuidv4();
      const request = {
        requestId,
        type: 'getOrderBook',
        symbol
      };

      const callback = (response) => {
        if (response.success) {
          resolve(response.orderBook);
        } else {
          reject(new Error(response.error || 'Failed to get order book'));
        }
      };

      this.sendRequest(request, callback);

      // Add timeout after 10 seconds
      setTimeout(() => {
        if (this.callbackMap.has(requestId)) {
          this.callbackMap.delete(requestId);
          reject(new Error('Request timed out. Order Matching Engine might be down.'));
        }
      }, 10000);
    });
  }

  async cancelOrder(orderId) {
    return new Promise((resolve, reject) => {
      const requestId = uuidv4();
      const request = {
        requestId,
        type: 'cancelOrder',
        orderId
      };

      const callback = (response) => {
        if (response.success) {
          resolve(response);
        } else {
          reject(new Error(response.error || 'Failed to cancel order'));
        }
      };

      this.sendRequest(request, callback);

      // Add timeout after 10 seconds
      setTimeout(() => {
        if (this.callbackMap.has(requestId)) {
          this.callbackMap.delete(requestId);
          reject(new Error('Request timed out. Order Matching Engine might be down.'));
        }
      }, 10000);
    });
  }

  async getOrderStatus(orderId) {
    return new Promise((resolve, reject) => {
      const requestId = uuidv4();
      const request = {
        requestId,
        type: 'getOrderStatus',
        orderId
      };

      const callback = (response) => {
        if (response.success) {
          resolve(response.status);
        } else {
          reject(new Error(response.error || 'Failed to get order status'));
        }
      };

      this.sendRequest(request, callback);

      // Add timeout after 10 seconds
      setTimeout(() => {
        if (this.callbackMap.has(requestId)) {
          this.callbackMap.delete(requestId);
          reject(new Error('Request timed out. Order Matching Engine might be down.'));
        }
      }, 10000);
    });
  }

  // New method to add a trade to the recent trades list
  addRecentTrade(trade) {
    if (!trade || !trade.symbol) return;

    if (!this.recentTrades.has(trade.symbol)) {
      this.recentTrades.set(trade.symbol, []);
    }

    const trades = this.recentTrades.get(trade.symbol);
    trades.unshift(trade); // Add to the beginning (most recent first)

    // Limit to 100 trades per symbol
    if (trades.length > 100) {
      trades.pop(); // Remove oldest
    }

    // Publish to Redis for other services if available
    try {
      if (this.redis && this.redis.publish) {
        this.redis.publish('trade_updates', JSON.stringify(trade));
      }
    } catch (error) {
      console.error('Error publishing trade to Redis:', error.message);
    }
  }

  // Get recent trades for a symbol
  getRecentTrades(symbol, limit = 50) {
    if (!this.recentTrades.has(symbol)) {
      return [];
    }

    const trades = this.recentTrades.get(symbol);
    return trades.slice(0, limit);
  }

  // Get market data (price, volume, etc.)
  async getMarketData(symbol) {
    return new Promise((resolve, reject) => {
      const requestId = uuidv4();
      const request = {
        requestId,
        type: 'getMarketData',
        symbol
      };

      const callback = (response) => {
        if (response.success) {
          resolve(response.marketData);
        } else {
          reject(new Error(response.error || 'Failed to get market data'));
        }
      };

      this.sendRequest(request, callback);

      // Add timeout after 10 seconds
      setTimeout(() => {
        if (this.callbackMap.has(requestId)) {
          this.callbackMap.delete(requestId);
          reject(new Error('Request timed out. Order Matching Engine might be down.'));
        }
      }, 10000);
    });
  }

  // Get user's open orders
  async getUserOrders(walletAddress) {
    return new Promise((resolve, reject) => {
      const requestId = uuidv4();
      const request = {
        requestId,
        type: 'getUserOrders',
        walletAddress
      };

      const callback = (response) => {
        if (response.success) {
          resolve(response.orders || []);
        } else {
          reject(new Error(response.error || 'Failed to get user orders'));
        }
      };

      this.sendRequest(request, callback);

      // Add timeout after 10 seconds
      setTimeout(() => {
        if (this.callbackMap.has(requestId)) {
          this.callbackMap.delete(requestId);
          reject(new Error('Request timed out. Order Matching Engine might be down.'));
        }
      }, 10000);
    });
  }

  // Get available tokens for trading
  async getAvailableTokens() {
    return new Promise((resolve, reject) => {
      const requestId = uuidv4();
      const request = {
        requestId,
        type: 'getAvailableTokens'
      };

      const callback = (response) => {
        if (response.success) {
          resolve(response.tokens || []);
        } else {
          reject(new Error(response.error || 'Failed to get available tokens'));
        }
      };

      this.sendRequest(request, callback);

      // Add timeout after 10 seconds
      setTimeout(() => {
        if (this.callbackMap.has(requestId)) {
          this.callbackMap.delete(requestId);
          reject(new Error('Request timed out. Order Matching Engine might be down.'));
        }
      }, 10000);
    });
  }

  // Get order book depth with multiple price levels
  async getOrderBookDepth(symbol, levels = 10) {
    return new Promise((resolve, reject) => {
      const requestId = uuidv4();
      const request = {
        requestId,
        type: 'getOrderBookDepth',
        symbol,
        levels
      };

      const callback = (response) => {
        if (response.success) {
          resolve(response.depth);
        } else {
          reject(new Error(response.error || 'Failed to get order book depth'));
        }
      };

      this.sendRequest(request, callback);

      // Add timeout after 10 seconds
      setTimeout(() => {
        if (this.callbackMap.has(requestId)) {
          this.callbackMap.delete(requestId);
          reject(new Error('Request timed out. Order Matching Engine might be down.'));
        }
      }, 10000);
    });
  }

  // Fallback implementation for getOrderBookDepth
  handleFallbackGetOrderBookDepth(request, callback) {
    const { symbol, levels = 10 } = request;

    if (!this.mockOrderBook.has(symbol)) {
      this.mockOrderBook.set(symbol, { bids: [], asks: [] });
    }

    const orderBook = this.mockOrderBook.get(symbol);

    // Create a depth structure with the available mock data
    const depth = {
      symbol,
      bids: orderBook.bids.slice(0, levels).map(bid => ({
        price: bid.price,
        quantity: bid.size
      })),
      asks: orderBook.asks.slice(0, levels).map(ask => ({
        price: ask.price,
        quantity: ask.size
      }))
    };

    callback({ success: true, depth });
  }
}

module.exports = {
  orderMatchingService: new OrderMatchingService()
};