{"version": 3, "file": "index.cjs.js", "sources": ["../../../node_modules/@noble/hashes/esm/_assert.js", "../../../node_modules/@noble/hashes/esm/crypto.js", "../../../node_modules/@noble/hashes/esm/utils.js", "../../../node_modules/@noble/hashes/esm/_md.js", "../../../node_modules/@noble/hashes/esm/_u64.js", "../../../node_modules/@noble/hashes/esm/sha512.js", "../../../node_modules/@noble/curves/esm/abstract/utils.js", "../../../node_modules/@noble/curves/esm/abstract/modular.js", "../../../node_modules/@noble/curves/esm/abstract/curve.js", "../../../node_modules/@noble/curves/esm/abstract/edwards.js", "../../../node_modules/@noble/curves/esm/abstract/montgomery.js", "../../../node_modules/@noble/curves/esm/ed25519.js", "../src/constants.ts", "../../../node_modules/uint8arrays/esm/src/util/as-uint8array.js", "../../../node_modules/uint8arrays/esm/src/alloc.js", "../../../node_modules/uint8arrays/esm/src/concat.js", "../../../node_modules/multiformats/esm/vendor/base-x.js", "../../../node_modules/multiformats/esm/src/bytes.js", "../../../node_modules/multiformats/esm/src/bases/base.js", "../../../node_modules/multiformats/esm/src/bases/identity.js", "../../../node_modules/multiformats/esm/src/bases/base2.js", "../../../node_modules/multiformats/esm/src/bases/base8.js", "../../../node_modules/multiformats/esm/src/bases/base10.js", "../../../node_modules/multiformats/esm/src/bases/base16.js", "../../../node_modules/multiformats/esm/src/bases/base32.js", "../../../node_modules/multiformats/esm/src/bases/base36.js", "../../../node_modules/multiformats/esm/src/bases/base58.js", "../../../node_modules/multiformats/esm/src/bases/base64.js", "../../../node_modules/multiformats/esm/src/bases/base256emoji.js", "../../../node_modules/multiformats/esm/vendor/varint.js", "../../../node_modules/multiformats/esm/src/varint.js", "../../../node_modules/multiformats/esm/src/hashes/digest.js", "../../../node_modules/multiformats/esm/src/hashes/hasher.js", "../../../node_modules/multiformats/esm/src/hashes/sha2-browser.js", "../../../node_modules/multiformats/esm/src/hashes/identity.js", "../../../node_modules/multiformats/esm/src/codecs/json.js", "../../../node_modules/multiformats/esm/src/basics.js", "../../../node_modules/uint8arrays/esm/src/util/bases.js", "../../../node_modules/uint8arrays/esm/src/to-string.js", "../../../node_modules/uint8arrays/esm/src/from-string.js", "../src/utils.ts", "../src/api.ts"], "sourcesContent": ["/**\n * Assertion helpers\n * @module\n */\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error('positive integer expected, got ' + n);\n}\n// copied from utils\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\nfunction ahash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.wrapConstructor');\n    anumber(h.outputLen);\n    anumber(h.blockLen);\n}\nfunction aexists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nfunction aoutput(out, instance) {\n    abytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error('digestInto() expects output buffer of length at least ' + min);\n    }\n}\nexport { anumber, abytes, ahash, aexists, aoutput };\n//# sourceMappingURL=_assert.js.map", "export const crypto = typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;\n//# sourceMappingURL=crypto.js.map", "/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\nimport { abytes } from './_assert.js';\n// export { isBytes } from './_assert.js';\n// We can't reuse isBytes from _assert, because somehow this causes huge perf issues\nexport function isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n// Cast array to different type\nexport const u8 = (arr) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = (arr) => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n// Cast array to view\nexport const createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n/** The rotate right (circular right shift) operation for uint32 */\nexport const rotr = (word, shift) => (word << (32 - shift)) | (word >>> shift);\n/** The rotate left (circular left shift) operation for uint32 */\nexport const rotl = (word, shift) => (word << shift) | ((word >>> (32 - shift)) >>> 0);\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nexport const isLE = /* @__PURE__ */ (() => new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n// The byte swap operation for uint32\nexport const byteSwap = (word) => ((word << 24) & 0xff000000) |\n    ((word << 8) & 0xff0000) |\n    ((word >>> 8) & 0xff00) |\n    ((word >>> 24) & 0xff);\n/** Conditionally byte swap if on a big-endian platform */\nexport const byteSwapIfBE = isLE\n    ? (n) => n\n    : (n) => byteSwap(n);\n/** In place byte swap for Uint32Array */\nexport function byteSwap32(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        arr[i] = byteSwap(arr[i]);\n    }\n}\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    abytes(bytes);\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * Convert hex string to byte array.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => { };\n// Returns control to thread each 'tick' ms to avoid blocking\nexport async function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * Convert JS string to byte array.\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('utf8ToBytes expected string, got ' + typeof str);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\n// For runtime check if class implements interface\nexport class Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nexport function checkOpts(defaults, opts) {\n    if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n        throw new Error('Options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\nexport function wrapConstructor(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nexport function wrapConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nexport function wrapXOFConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nexport function randomBytes(bytesLength = 32) {\n    if (crypto && typeof crypto.getRandomValues === 'function') {\n        return crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    // Legacy Node.js compatibility\n    if (crypto && typeof crypto.randomBytes === 'function') {\n        return crypto.randomBytes(bytesLength);\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map", "import { aexists, aoutput } from './_assert.js';\nimport { Hash, createView, toBytes } from './utils.js';\n/**\n * Merkle-Damgard hash utils.\n * @module\n */\n/**\n * Polyfill for Safari 14\n */\nexport function setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n/**\n * Choice: a ? b : c\n */\nexport const Chi = (a, b, c) => (a & b) ^ (~a & c);\n/**\n * Majority function, true if any two inputs is true\n */\nexport const Maj = (a, b, c) => (a & b) ^ (a & c) ^ (b & c);\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nexport class HashMD extends Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = createView(this.buffer);\n    }\n    update(data) {\n        aexists(this);\n        const { view, buffer, blockLen } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = createView(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        aexists(this);\n        aoutput(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in\n        // current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = createView(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n//# sourceMappingURL=_md.js.map", "const U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n/**\n * BigUint64Array is too slow as per 2024, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    let Ah = new Uint32Array(lst.length);\n    let Al = new Uint32Array(lst.length);\n    for (let i = 0; i < lst.length; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n// prettier-ignore\nexport { fromBig, split, toBig, shrSH, shrSL, rotrSH, rotrSL, rotrBH, rotrBL, rotr32H, rotr32L, rotlSH, rotlSL, rotlBH, rotlBL, add, add3L, add3H, add4L, add4H, add5H, add5L, };\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n//# sourceMappingURL=_u64.js.map", "import { HashMD } from './_md.js';\nimport u64 from './_u64.js';\nimport { wrapConstructor } from './utils.js';\n/**\n * SHA2-512 a.k.a. sha512 and sha384. It is slower than sha256 in js because u64 operations are slow.\n *\n * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and\n * [the paper on truncated SHA512/256](https://eprint.iacr.org/2010/548.pdf).\n * @module\n */\n// Round contants (first 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409):\n// prettier-ignore\nconst [SHA512_Kh, SHA512_Kl] = /* @__PURE__ */ (() => u64.split([\n    '0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc',\n    '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118',\n    '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2',\n    '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694',\n    '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65',\n    '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5',\n    '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4',\n    '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70',\n    '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df',\n    '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b',\n    '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30',\n    '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8',\n    '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8',\n    '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3',\n    '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec',\n    '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b',\n    '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178',\n    '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b',\n    '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c',\n    '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'\n].map(n => BigInt(n))))();\n// Temporary buffer, not used to store anything between runs\nconst SHA512_W_H = /* @__PURE__ */ new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */ new Uint32Array(80);\nexport class SHA512 extends HashMD {\n    constructor() {\n        super(128, 64, 16, false);\n        // We cannot use array here since array allows indexing by variable which means optimizer/compiler cannot use registers.\n        // Also looks cleaner and easier to verify with spec.\n        // Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = 0x6a09e667 | 0;\n        this.Al = 0xf3bcc908 | 0;\n        this.Bh = 0xbb67ae85 | 0;\n        this.Bl = 0x84caa73b | 0;\n        this.Ch = 0x3c6ef372 | 0;\n        this.Cl = 0xfe94f82b | 0;\n        this.Dh = 0xa54ff53a | 0;\n        this.Dl = 0x5f1d36f1 | 0;\n        this.Eh = 0x510e527f | 0;\n        this.El = 0xade682d1 | 0;\n        this.Fh = 0x9b05688c | 0;\n        this.Fl = 0x2b3e6c1f | 0;\n        this.Gh = 0x1f83d9ab | 0;\n        this.Gl = 0xfb41bd6b | 0;\n        this.Hh = 0x5be0cd19 | 0;\n        this.Hl = 0x137e2179 | 0;\n    }\n    // prettier-ignore\n    get() {\n        const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n    }\n    // prettier-ignore\n    set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {\n        this.Ah = Ah | 0;\n        this.Al = Al | 0;\n        this.Bh = Bh | 0;\n        this.Bl = Bl | 0;\n        this.Ch = Ch | 0;\n        this.Cl = Cl | 0;\n        this.Dh = Dh | 0;\n        this.Dl = Dl | 0;\n        this.Eh = Eh | 0;\n        this.El = El | 0;\n        this.Fh = Fh | 0;\n        this.Fl = Fl | 0;\n        this.Gh = Gh | 0;\n        this.Gl = Gl | 0;\n        this.Hh = Hh | 0;\n        this.Hl = Hl | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4) {\n            SHA512_W_H[i] = view.getUint32(offset);\n            SHA512_W_L[i] = view.getUint32((offset += 4));\n        }\n        for (let i = 16; i < 80; i++) {\n            // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n            const W15h = SHA512_W_H[i - 15] | 0;\n            const W15l = SHA512_W_L[i - 15] | 0;\n            const s0h = u64.rotrSH(W15h, W15l, 1) ^ u64.rotrSH(W15h, W15l, 8) ^ u64.shrSH(W15h, W15l, 7);\n            const s0l = u64.rotrSL(W15h, W15l, 1) ^ u64.rotrSL(W15h, W15l, 8) ^ u64.shrSL(W15h, W15l, 7);\n            // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n            const W2h = SHA512_W_H[i - 2] | 0;\n            const W2l = SHA512_W_L[i - 2] | 0;\n            const s1h = u64.rotrSH(W2h, W2l, 19) ^ u64.rotrBH(W2h, W2l, 61) ^ u64.shrSH(W2h, W2l, 6);\n            const s1l = u64.rotrSL(W2h, W2l, 19) ^ u64.rotrBL(W2h, W2l, 61) ^ u64.shrSL(W2h, W2l, 6);\n            // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n            const SUMl = u64.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n            const SUMh = u64.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n            SHA512_W_H[i] = SUMh | 0;\n            SHA512_W_L[i] = SUMl | 0;\n        }\n        let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        // Compression function main loop, 80 rounds\n        for (let i = 0; i < 80; i++) {\n            // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n            const sigma1h = u64.rotrSH(Eh, El, 14) ^ u64.rotrSH(Eh, El, 18) ^ u64.rotrBH(Eh, El, 41);\n            const sigma1l = u64.rotrSL(Eh, El, 14) ^ u64.rotrSL(Eh, El, 18) ^ u64.rotrBL(Eh, El, 41);\n            //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const CHIh = (Eh & Fh) ^ (~Eh & Gh);\n            const CHIl = (El & Fl) ^ (~El & Gl);\n            // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n            // prettier-ignore\n            const T1ll = u64.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n            const T1h = u64.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n            const T1l = T1ll | 0;\n            // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n            const sigma0h = u64.rotrSH(Ah, Al, 28) ^ u64.rotrBH(Ah, Al, 34) ^ u64.rotrBH(Ah, Al, 39);\n            const sigma0l = u64.rotrSL(Ah, Al, 28) ^ u64.rotrBL(Ah, Al, 34) ^ u64.rotrBL(Ah, Al, 39);\n            const MAJh = (Ah & Bh) ^ (Ah & Ch) ^ (Bh & Ch);\n            const MAJl = (Al & Bl) ^ (Al & Cl) ^ (Bl & Cl);\n            Hh = Gh | 0;\n            Hl = Gl | 0;\n            Gh = Fh | 0;\n            Gl = Fl | 0;\n            Fh = Eh | 0;\n            Fl = El | 0;\n            ({ h: Eh, l: El } = u64.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n            Dh = Ch | 0;\n            Dl = Cl | 0;\n            Ch = Bh | 0;\n            Cl = Bl | 0;\n            Bh = Ah | 0;\n            Bl = Al | 0;\n            const All = u64.add3L(T1l, sigma0l, MAJl);\n            Ah = u64.add3H(All, T1h, sigma0h, MAJh);\n            Al = All | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        ({ h: Ah, l: Al } = u64.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n        ({ h: Bh, l: Bl } = u64.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n        ({ h: Ch, l: Cl } = u64.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n        ({ h: Dh, l: Dl } = u64.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n        ({ h: Eh, l: El } = u64.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n        ({ h: Fh, l: Fl } = u64.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n        ({ h: Gh, l: Gl } = u64.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n        ({ h: Hh, l: Hl } = u64.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n        this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n    }\n    roundClean() {\n        SHA512_W_H.fill(0);\n        SHA512_W_L.fill(0);\n    }\n    destroy() {\n        this.buffer.fill(0);\n        this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n    }\n}\nexport class SHA512_224 extends SHA512 {\n    constructor() {\n        super();\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = 0x8c3d37c8 | 0;\n        this.Al = 0x19544da2 | 0;\n        this.Bh = 0x73e19966 | 0;\n        this.Bl = 0x89dcd4d6 | 0;\n        this.Ch = 0x1dfab7ae | 0;\n        this.Cl = 0x32ff9c82 | 0;\n        this.Dh = 0x679dd514 | 0;\n        this.Dl = 0x582f9fcf | 0;\n        this.Eh = 0x0f6d2b69 | 0;\n        this.El = 0x7bd44da8 | 0;\n        this.Fh = 0x77e36f73 | 0;\n        this.Fl = 0x04c48942 | 0;\n        this.Gh = 0x3f9d85a8 | 0;\n        this.Gl = 0x6a1d36c8 | 0;\n        this.Hh = 0x1112e6ad | 0;\n        this.Hl = 0x91d692a1 | 0;\n        this.outputLen = 28;\n    }\n}\nexport class SHA512_256 extends SHA512 {\n    constructor() {\n        super();\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = 0x22312194 | 0;\n        this.Al = 0xfc2bf72c | 0;\n        this.Bh = 0x9f555fa3 | 0;\n        this.Bl = 0xc84c64c2 | 0;\n        this.Ch = 0x2393b86b | 0;\n        this.Cl = 0x6f53b151 | 0;\n        this.Dh = 0x96387719 | 0;\n        this.Dl = 0x5940eabd | 0;\n        this.Eh = 0x96283ee2 | 0;\n        this.El = 0xa88effe3 | 0;\n        this.Fh = 0xbe5e1e25 | 0;\n        this.Fl = 0x53863992 | 0;\n        this.Gh = 0x2b0199fc | 0;\n        this.Gl = 0x2c85b8aa | 0;\n        this.Hh = 0x0eb72ddc | 0;\n        this.Hl = 0x81c52ca2 | 0;\n        this.outputLen = 32;\n    }\n}\nexport class SHA384 extends SHA512 {\n    constructor() {\n        super();\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = 0xcbbb9d5d | 0;\n        this.Al = 0xc1059ed8 | 0;\n        this.Bh = 0x629a292a | 0;\n        this.Bl = 0x367cd507 | 0;\n        this.Ch = 0x9159015a | 0;\n        this.Cl = 0x3070dd17 | 0;\n        this.Dh = 0x152fecd8 | 0;\n        this.Dl = 0xf70e5939 | 0;\n        this.Eh = 0x67332667 | 0;\n        this.El = 0xffc00b31 | 0;\n        this.Fh = 0x8eb44a87 | 0;\n        this.Fl = 0x68581511 | 0;\n        this.Gh = 0xdb0c2e0d | 0;\n        this.Gl = 0x64f98fa7 | 0;\n        this.Hh = 0x47b5481d | 0;\n        this.Hl = 0xbefa4fa4 | 0;\n        this.outputLen = 48;\n    }\n}\n/** SHA2-512 hash function. */\nexport const sha512 = /* @__PURE__ */ wrapConstructor(() => new SHA512());\n/** SHA2-512/224 \"truncated\" hash function, with improved resistance to length extension attacks. */\nexport const sha512_224 = /* @__PURE__ */ wrapConstructor(() => new SHA512_224());\n/** SHA2-512/256 \"truncated\" hash function, with improved resistance to length extension attacks. */\nexport const sha512_256 = /* @__PURE__ */ wrapConstructor(() => new SHA512_256());\n/** SHA2-384 hash function. */\nexport const sha384 = /* @__PURE__ */ wrapConstructor(() => new SHA384());\n//# sourceMappingURL=sha512.js.map", "/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nexport function isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\nexport function abytes(item) {\n    if (!isBytes(item))\n        throw new Error('Uint8Array expected');\n}\nexport function abool(title, value) {\n    if (typeof value !== 'boolean')\n        throw new Error(title + ' boolean expected, got ' + value);\n}\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    abytes(bytes);\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\nexport function numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? '0' + hex : hex;\n}\nexport function hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n// BE: Big Endian, LE: Little Endian\nexport function bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nexport function bytesToNumberLE(bytes) {\n    abytes(bytes);\n    return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nexport function numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nexport function numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nexport function numberToVarBytesBE(n) {\n    return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nexport function ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === 'string') {\n        try {\n            res = hexToBytes(hex);\n        }\n        catch (e) {\n            throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n        }\n    }\n    else if (isBytes(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    }\n    else {\n        throw new Error(title + ' must be hex string or Uint8Array');\n    }\n    const len = res.length;\n    if (typeof expectedLength === 'number' && len !== expectedLength)\n        throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n    return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\n// Compares 2 u8a-s in kinda constant time\nexport function equalBytes(a, b) {\n    if (a.length !== b.length)\n        return false;\n    let diff = 0;\n    for (let i = 0; i < a.length; i++)\n        diff |= a[i] ^ b[i];\n    return diff === 0;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Is positive bigint\nconst isPosBig = (n) => typeof n === 'bigint' && _0n <= n;\nexport function inRange(n, min, max) {\n    return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nexport function aInRange(title, n, min, max) {\n    // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n    // consider P=256n, min=0n, max=P\n    // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n    // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n    // - our way is the cleanest:               `inRange('x', x, 0n, P)\n    if (!inRange(n, min, max))\n        throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */\nexport function bitLen(n) {\n    let len;\n    for (len = 0; n > _0n; n >>= _1n, len += 1)\n        ;\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nexport function bitGet(n, pos) {\n    return (n >> BigInt(pos)) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nexport function bitSet(n, pos, value) {\n    return n | ((value ? _1n : _0n) << BigInt(pos));\n}\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nexport const bitMask = (n) => (_2n << BigInt(n - 1)) - _1n;\n// DRBG\nconst u8n = (data) => new Uint8Array(data); // creates Uint8Array\nconst u8fr = (arr) => Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nexport function createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== 'number' || hashLen < 2)\n        throw new Error('hashLen must be a number');\n    if (typeof qByteLen !== 'number' || qByteLen < 2)\n        throw new Error('qByteLen must be a number');\n    if (typeof hmacFn !== 'function')\n        throw new Error('hmacFn must be a function');\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = () => {\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n()) => {\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0)\n            return;\n        k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = () => {\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000)\n            throw new Error('drbg: tried 1000 values');\n        let len = 0;\n        const out = [];\n        while (len < qByteLen) {\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return concatBytes(...out);\n    };\n    const genUntil = (seed, pred) => {\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while (!(res = pred(gen())))\n            reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val) => typeof val === 'bigint',\n    function: (val) => typeof val === 'function',\n    boolean: (val) => typeof val === 'boolean',\n    string: (val) => typeof val === 'string',\n    stringOrUint8Array: (val) => typeof val === 'string' || isBytes(val),\n    isSafeInteger: (val) => Number.isSafeInteger(val),\n    array: (val) => Array.isArray(val),\n    field: (val, object) => object.Fp.isValid(val),\n    hash: (val) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nexport function validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional) => {\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== 'function')\n            throw new Error('invalid validator function');\n        const val = object[fieldName];\n        if (isOptional && val === undefined)\n            return;\n        if (!checkVal(val, object)) {\n            throw new Error('param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))\n        checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))\n        checkField(fieldName, type, true);\n    return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n/**\n * throws not implemented error\n */\nexport const notImplemented = () => {\n    throw new Error('not implemented');\n};\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nexport function memoized(fn) {\n    const map = new WeakMap();\n    return (arg, ...args) => {\n        const val = map.get(arg);\n        if (val !== undefined)\n            return val;\n        const computed = fn(arg, ...args);\n        map.set(arg, computed);\n        return computed;\n    };\n}\n//# sourceMappingURL=utils.js.map", "/**\n * Utils for modular division and finite fields.\n * A finite field over 11 is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { bitMask, bytesToNumberBE, bytesToNumberLE, ensureBytes, numberToBytesBE, numberToBytesLE, validateObject, } from './utils.js';\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = /* @__PURE__ */ BigInt(2), _3n = /* @__PURE__ */ BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */ BigInt(4), _5n = /* @__PURE__ */ BigInt(5), _8n = /* @__PURE__ */ BigInt(8);\n// prettier-ignore\nconst _9n = /* @__PURE__ */ BigInt(9), _16n = /* @__PURE__ */ BigInt(16);\n// Calculates a modulo b\nexport function mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @todo use field version && remove\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nexport function pow(num, power, modulo) {\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (modulo <= _0n)\n        throw new Error('invalid modulus');\n    if (modulo === _1n)\n        return _0n;\n    let res = _1n;\n    while (power > _0n) {\n        if (power & _1n)\n            res = (res * num) % modulo;\n        num = (num * num) % modulo;\n        power >>= _1n;\n    }\n    return res;\n}\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nexport function pow2(x, power, modulo) {\n    let res = x;\n    while (power-- > _0n) {\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nexport function invert(number, modulo) {\n    if (number === _0n)\n        throw new Error('invert: expected non-zero number');\n    if (modulo <= _0n)\n        throw new Error('invert: expected positive modulus, got ' + modulo);\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while (a !== _0n) {\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n)\n        throw new Error('invert: does not exist');\n    return mod(x, modulo);\n}\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nexport function tonelliShanks(P) {\n    // Legendre constant: used to calculate Legendre symbol (a | p),\n    // which denotes the value of a^((p-1)/2) (mod p).\n    // (a | p) ≡ 1    if a is a square (mod p)\n    // (a | p) ≡ -1   if a is not a square (mod p)\n    // (a | p) ≡ 0    if a ≡ 0 (mod p)\n    const legendreC = (P - _1n) / _2n;\n    let Q, S, Z;\n    // Step 1: By factoring out powers of 2 from p - 1,\n    // find q and s such that p - 1 = q*(2^s) with q odd\n    for (Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++)\n        ;\n    // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n    for (Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++) {\n        // Crash instead of infinity loop, we cannot reasonable count until P.\n        if (Z > 1000)\n            throw new Error('Cannot find square root: likely non-prime P');\n    }\n    // Fast-path\n    if (S === 1) {\n        const p1div4 = (P + _1n) / _4n;\n        return function tonelliFast(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Slow-path\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n        if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE))\n            throw new Error('Cannot find square root');\n        let r = S;\n        // TODO: will fail at Fp2/etc\n        let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n        let x = Fp.pow(n, Q1div2); // first guess at the square root\n        let b = Fp.pow(n, Q); // first guess at the fudge factor\n        while (!Fp.eql(b, Fp.ONE)) {\n            if (Fp.eql(b, Fp.ZERO))\n                return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n            // Find m such b^(2^m)==1\n            let m = 1;\n            for (let t2 = Fp.sqr(b); m < r; m++) {\n                if (Fp.eql(t2, Fp.ONE))\n                    break;\n                t2 = Fp.sqr(t2); // t2 *= t2\n            }\n            // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n            const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n            g = Fp.sqr(ge); // g = ge * ge\n            x = Fp.mul(x, ge); // x *= ge\n            b = Fp.mul(b, g); // b *= g\n            r = m;\n        }\n        return x;\n    };\n}\n/**\n * Square root for a finite field. It will try to check if optimizations are applicable and fall back to 4:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. P ≡ 9 (mod 16)\n * 4. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nexport function FpSqrt(P) {\n    // P ≡ 3 (mod 4)\n    // √n = n^((P+1)/4)\n    if (P % _4n === _3n) {\n        // Not all roots possible!\n        // const ORDER =\n        //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n        // const NUM = 72057594037927816n;\n        const p1div4 = (P + _1n) / _4n;\n        return function sqrt3mod4(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            // Throw if root**2 != n\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n    if (P % _8n === _5n) {\n        const c1 = (P - _5n) / _8n;\n        return function sqrt5mod8(Fp, n) {\n            const n2 = Fp.mul(n, _2n);\n            const v = Fp.pow(n2, c1);\n            const nv = Fp.mul(n, v);\n            const i = Fp.mul(Fp.mul(nv, _2n), v);\n            const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // P ≡ 9 (mod 16)\n    if (P % _16n === _9n) {\n        // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n        // Means we cannot use sqrt for constants at all!\n        //\n        // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n        // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n        // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n        // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n        // sqrt = (x) => {\n        //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n        //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n        //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n        //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n        //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n        //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n        //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n        //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n        //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n        //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n        // }\n    }\n    // Other cases: Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nexport const isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n    'eql', 'add', 'sub', 'mul', 'pow', 'div',\n    'addN', 'subN', 'mulN', 'sqrN'\n];\nexport function validateField(field) {\n    const initial = {\n        ORDER: 'bigint',\n        MASK: 'bigint',\n        BYTES: 'isSafeInteger',\n        BITS: 'isSafeInteger',\n    };\n    const opts = FIELD_FIELDS.reduce((map, val) => {\n        map[val] = 'function';\n        return map;\n    }, initial);\n    return validateObject(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nexport function FpPow(f, num, power) {\n    // Should have same speed as pow for bigints\n    // TODO: benchmark!\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (power === _0n)\n        return f.ONE;\n    if (power === _1n)\n        return num;\n    let p = f.ONE;\n    let d = num;\n    while (power > _0n) {\n        if (power & _1n)\n            p = f.mul(p, d);\n        d = f.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */\nexport function FpInvertBatch(f, nums) {\n    const tmp = new Array(nums.length);\n    // Walk from first to last, multiply them by each other MOD p\n    const lastMultiplied = nums.reduce((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = acc;\n        return f.mul(acc, num);\n    }, f.ONE);\n    // Invert last element\n    const inverted = f.inv(lastMultiplied);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = f.mul(acc, tmp[i]);\n        return f.mul(acc, num);\n    }, inverted);\n    return tmp;\n}\nexport function FpDiv(f, lhs, rhs) {\n    return f.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n/**\n * Legendre symbol.\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nexport function FpLegendre(order) {\n    const legendreConst = (order - _1n) / _2n; // Integer arithmetic\n    return (f, x) => f.pow(x, legendreConst);\n}\n// This function returns True whenever the value x is a square in the field F.\nexport function FpIsSquare(f) {\n    const legendre = FpLegendre(f.ORDER);\n    return (x) => {\n        const p = legendre(f, x);\n        return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n    };\n}\n// CURVE.n lengths\nexport function nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return { nBitLength: _nBitLength, nByteLength };\n}\n/**\n * Initializes a finite field over prime.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nexport function Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n)\n        throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048)\n        throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n    let sqrtP; // cached sqrtP\n    const f = Object.freeze({\n        ORDER,\n        isLE,\n        BITS,\n        BYTES,\n        MASK: bitMask(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num) => mod(num, ORDER),\n        isValid: (num) => {\n            if (typeof num !== 'bigint')\n                throw new Error('invalid field element: expected bigint, got ' + typeof num);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num) => num === _0n,\n        isOdd: (num) => (num & _1n) === _1n,\n        neg: (num) => mod(-num, ORDER),\n        eql: (lhs, rhs) => lhs === rhs,\n        sqr: (num) => mod(num * num, ORDER),\n        add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n        pow: (num, power) => FpPow(f, num, power),\n        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num) => num * num,\n        addN: (lhs, rhs) => lhs + rhs,\n        subN: (lhs, rhs) => lhs - rhs,\n        mulN: (lhs, rhs) => lhs * rhs,\n        inv: (num) => invert(num, ORDER),\n        sqrt: redef.sqrt ||\n            ((n) => {\n                if (!sqrtP)\n                    sqrtP = FpSqrt(ORDER);\n                return sqrtP(f, n);\n            }),\n        invertBatch: (lst) => FpInvertBatch(f, lst),\n        // TODO: do we really need constant cmov?\n        // We don't have const-time bigints anyway, so probably will be not very useful\n        cmov: (a, b, c) => (c ? b : a),\n        toBytes: (num) => (isLE ? numberToBytesLE(num, BYTES) : numberToBytesBE(num, BYTES)),\n        fromBytes: (bytes) => {\n            if (bytes.length !== BYTES)\n                throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n            return isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n        },\n    });\n    return Object.freeze(f);\n}\nexport function FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nexport function FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nexport function hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = ensureBytes('privateHash', hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n        throw new Error('hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen);\n    const num = isLE ? bytesToNumberLE(hash) : bytesToNumberBE(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nexport function getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== 'bigint')\n        throw new Error('field order must be bigint');\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nexport function getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nexport function mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024)\n        throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n    const num = isLE ? bytesToNumberLE(key) : bytesToNumberBE(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? numberToBytesLE(reduced, fieldLen) : numberToBytesBE(reduced, fieldLen);\n}\n//# sourceMappingURL=modular.js.map", "/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNAF, pippenger\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { validateField, nLength } from './modular.js';\nimport { validateObject, bitLen } from './utils.js';\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nfunction constTimeNegate(condition, item) {\n    const neg = item.negate();\n    return condition ? neg : item;\n}\nfunction validateW(W, bits) {\n    if (!Number.isSafeInteger(W) || W <= 0 || W > bits)\n        throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\nfunction calcWOpts(W, bits) {\n    validateW(W, bits);\n    const windows = Math.ceil(bits / W) + 1; // +1, because\n    const windowSize = 2 ** (W - 1); // -1 because we skip zero\n    return { windows, windowSize };\n}\nfunction validateMSMPoints(points, c) {\n    if (!Array.isArray(points))\n        throw new Error('array expected');\n    points.forEach((p, i) => {\n        if (!(p instanceof c))\n            throw new Error('invalid point at index ' + i);\n    });\n}\nfunction validateMSMScalars(scalars, field) {\n    if (!Array.isArray(scalars))\n        throw new Error('array of scalars expected');\n    scalars.forEach((s, i) => {\n        if (!field.isValid(s))\n            throw new Error('invalid scalar at index ' + i);\n    });\n}\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes\nconst pointPrecomputes = new WeakMap();\nconst pointWindowSizes = new WeakMap(); // This allows use make points immutable (nothing changes inside)\nfunction getW(P) {\n    return pointWindowSizes.get(P) || 1;\n}\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nexport function wNAF(c, bits) {\n    return {\n        constTimeNegate,\n        hasPrecomputes(elm) {\n            return getW(elm) !== 1;\n        },\n        // non-const time multiplication ladder\n        unsafeLadder(elm, n, p = c.ZERO) {\n            let d = elm;\n            while (n > _0n) {\n                if (n & _1n)\n                    p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @param elm Point instance\n         * @param W window size\n         * @returns precomputed point tables flattened to a single array\n         */\n        precomputeWindow(elm, W) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for (let window = 0; window < windows; window++) {\n                base = p;\n                points.push(base);\n                // =1, because we skip zero\n                for (let i = 1; i < windowSize; i++) {\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */\n        wNAF(W, precomputes, n) {\n            // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n            // But need to carefully remove other checks before wNAF. ORDER == bits here\n            const { windows, windowSize } = calcWOpts(W, bits);\n            let p = c.ZERO;\n            let f = c.BASE;\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n                // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n                // there is negate now: it is possible that negated element from low value\n                // would be the same as high element, which will create carry into next window.\n                // It's not obvious how this can fail, but still worth investigating later.\n                // Check if we're onto Zero point.\n                // Add random point inside current window to f.\n                const offset1 = offset;\n                const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n                const cond1 = window % 2 !== 0;\n                const cond2 = wbits < 0;\n                if (wbits === 0) {\n                    // The most important part for const-time getPublicKey\n                    f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n                }\n                else {\n                    p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n                }\n            }\n            // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n            // Even if the variable is still unused, there are some checks which will\n            // throw an exception, so compiler needs to prove they won't happen, which is hard.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return { p, f };\n        },\n        /**\n         * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @param acc accumulator point to add result of multiplication\n         * @returns point\n         */\n        wNAFUnsafe(W, precomputes, n, acc = c.ZERO) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                if (n === _0n)\n                    break; // No need to go over empty scalar\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                if (wbits === 0)\n                    continue;\n                let curr = precomputes[offset + Math.abs(wbits) - 1]; // -1 because we skip zero\n                if (wbits < 0)\n                    curr = curr.negate();\n                // NOTE: by re-using acc, we can save a lot of additions in case of MSM\n                acc = acc.add(curr);\n            }\n            return acc;\n        },\n        getPrecomputes(W, P, transform) {\n            // Calculate precomputes on a first run, reuse them after\n            let comp = pointPrecomputes.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1)\n                    pointPrecomputes.set(P, transform(comp));\n            }\n            return comp;\n        },\n        wNAFCached(P, n, transform) {\n            const W = getW(P);\n            return this.wNAF(W, this.getPrecomputes(W, P, transform), n);\n        },\n        wNAFCachedUnsafe(P, n, transform, prev) {\n            const W = getW(P);\n            if (W === 1)\n                return this.unsafeLadder(P, n, prev); // For W=1 ladder is ~x2 faster\n            return this.wNAFUnsafe(W, this.getPrecomputes(W, P, transform), n, prev);\n        },\n        // We calculate precomputes for elliptic curve point multiplication\n        // using windowed method. This specifies window size and\n        // stores precomputed values. Usually only base point would be precomputed.\n        setWindowSize(P, W) {\n            validateW(W, bits);\n            pointWindowSizes.set(P, W);\n            pointPrecomputes.delete(P);\n        },\n    };\n}\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster with precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka private keys / bigints)\n */\nexport function pippenger(c, fieldN, points, scalars) {\n    // If we split scalars by some window (let's say 8 bits), every chunk will only\n    // take 256 buckets even if there are 4096 scalars, also re-uses double.\n    // TODO:\n    // - https://eprint.iacr.org/2024/750.pdf\n    // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n    // 0 is accepted in scalars\n    validateMSMPoints(points, c);\n    validateMSMScalars(scalars, fieldN);\n    if (points.length !== scalars.length)\n        throw new Error('arrays of points and scalars must have equal length');\n    const zero = c.ZERO;\n    const wbits = bitLen(BigInt(points.length));\n    const windowSize = wbits > 12 ? wbits - 3 : wbits > 4 ? wbits - 2 : wbits ? 2 : 1; // in bits\n    const MASK = (1 << windowSize) - 1;\n    const buckets = new Array(MASK + 1).fill(zero); // +1 for zero array\n    const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n    let sum = zero;\n    for (let i = lastBits; i >= 0; i -= windowSize) {\n        buckets.fill(zero);\n        for (let j = 0; j < scalars.length; j++) {\n            const scalar = scalars[j];\n            const wbits = Number((scalar >> BigInt(i)) & BigInt(MASK));\n            buckets[wbits] = buckets[wbits].add(points[j]);\n        }\n        let resI = zero; // not using this will do small speed-up, but will lose ct\n        // Skip first bucket, because it is zero\n        for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n            sumI = sumI.add(buckets[j]);\n            resI = resI.add(sumI);\n        }\n        sum = sum.add(resI);\n        if (i !== 0)\n            for (let j = 0; j < windowSize; j++)\n                sum = sum.double();\n    }\n    return sum;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nexport function precomputeMSMUnsafe(c, fieldN, points, windowSize) {\n    /**\n     * Performance Analysis of Window-based Precomputation\n     *\n     * Base Case (256-bit scalar, 8-bit window):\n     * - Standard precomputation requires:\n     *   - 31 additions per scalar × 256 scalars = 7,936 ops\n     *   - Plus 255 summary additions = 8,191 total ops\n     *   Note: Summary additions can be optimized via accumulator\n     *\n     * Chunked Precomputation Analysis:\n     * - Using 32 chunks requires:\n     *   - 255 additions per chunk\n     *   - 256 doublings\n     *   - Total: (255 × 32) + 256 = 8,416 ops\n     *\n     * Memory Usage Comparison:\n     * Window Size | Standard Points | Chunked Points\n     * ------------|-----------------|---------------\n     *     4-bit   |     520         |      15\n     *     8-bit   |    4,224        |     255\n     *    10-bit   |   13,824        |   1,023\n     *    16-bit   |  557,056        |  65,535\n     *\n     * Key Advantages:\n     * 1. Enables larger window sizes due to reduced memory overhead\n     * 2. More efficient for smaller scalar counts:\n     *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n     *    - ~2x faster than standard 8,191 ops\n     *\n     * Limitations:\n     * - Not suitable for plain precomputes (requires 256 constant doublings)\n     * - Performance degrades with larger scalar counts:\n     *   - Optimal for ~256 scalars\n     *   - Less efficient for 4096+ scalars (Pippenger preferred)\n     */\n    validateW(windowSize, fieldN.BITS);\n    validateMSMPoints(points, c);\n    const zero = c.ZERO;\n    const tableSize = 2 ** windowSize - 1; // table size (without zero)\n    const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n    const MASK = BigInt((1 << windowSize) - 1);\n    const tables = points.map((p) => {\n        const res = [];\n        for (let i = 0, acc = p; i < tableSize; i++) {\n            res.push(acc);\n            acc = acc.add(p);\n        }\n        return res;\n    });\n    return (scalars) => {\n        validateMSMScalars(scalars, fieldN);\n        if (scalars.length > points.length)\n            throw new Error('array of scalars must be smaller than array of points');\n        let res = zero;\n        for (let i = 0; i < chunks; i++) {\n            // No need to double if accumulator is still zero.\n            if (res !== zero)\n                for (let j = 0; j < windowSize; j++)\n                    res = res.double();\n            const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n            for (let j = 0; j < scalars.length; j++) {\n                const n = scalars[j];\n                const curr = Number((n >> shiftBy) & MASK);\n                if (!curr)\n                    continue; // skip zero scalars chunks\n                res = res.add(tables[j][curr - 1]);\n            }\n        }\n        return res;\n    };\n}\nexport function validateBasic(curve) {\n    validateField(curve.Fp);\n    validateObject(curve, {\n        n: 'bigint',\n        h: 'bigint',\n        Gx: 'field',\n        Gy: 'field',\n    }, {\n        nBitLength: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n    });\n    // Set defaults\n    return Object.freeze({\n        ...nLength(curve.n, curve.nBitLength),\n        ...curve,\n        ...{ p: curve.Fp.ORDER },\n    });\n}\n//# sourceMappingURL=curve.js.map", "/**\n * Twisted <PERSON> curve. The formula is: ax² + y² = 1 + dx²y².\n * For design rationale of types / exports, see weierstrass module documentation.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { validateBasic, wNA<PERSON>, pippenger, } from './curve.js';\nimport { mod, Field } from './modular.js';\nimport * as ut from './utils.js';\nimport { ensureBytes, memoized, abool } from './utils.js';\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _8n = BigInt(8);\n// verification rule is either zip215 or rfc8032 / nist186-5. Consult fromHex:\nconst VERIFY_DEFAULT = { zip215: true };\nfunction validateOpts(curve) {\n    const opts = validateBasic(curve);\n    ut.validateObject(curve, {\n        hash: 'function',\n        a: 'bigint',\n        d: 'bigint',\n        randomBytes: 'function',\n    }, {\n        adjustScalarBytes: 'function',\n        domain: 'function',\n        uvRatio: 'function',\n        mapToCurve: 'function',\n    });\n    // Set defaults\n    return Object.freeze({ ...opts });\n}\n/**\n * Creates Twisted Edwards curve with EdDSA signatures.\n * @example\n * import { Field } from '@noble/curves/abstract/modular';\n * // Before that, define BigInt-s: a, d, p, n, Gx, Gy, h\n * const curve = twistedEdwards({ a, d, Fp: Field(p), n, Gx, Gy, h })\n */\nexport function twistedEdwards(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER, prehash: prehash, hash: cHash, randomBytes, nByteLength, h: cofactor, } = CURVE;\n    // Important:\n    // There are some places where Fp.BYTES is used instead of nByteLength.\n    // So far, everything has been tested with curves of Fp.BYTES == nByteLength.\n    // TODO: test and find curves which behave otherwise.\n    const MASK = _2n << (BigInt(nByteLength * 8) - _1n);\n    const modP = Fp.create; // Function overrides\n    const Fn = Field(CURVE.n, CURVE.nBitLength);\n    // sqrt(u/v)\n    const uvRatio = CURVE.uvRatio ||\n        ((u, v) => {\n            try {\n                return { isValid: true, value: Fp.sqrt(u * Fp.inv(v)) };\n            }\n            catch (e) {\n                return { isValid: false, value: _0n };\n            }\n        });\n    const adjustScalarBytes = CURVE.adjustScalarBytes || ((bytes) => bytes); // NOOP\n    const domain = CURVE.domain ||\n        ((data, ctx, phflag) => {\n            abool('phflag', phflag);\n            if (ctx.length || phflag)\n                throw new Error('Contexts/pre-hash are not supported');\n            return data;\n        }); // NOOP\n    // 0 <= n < MASK\n    // Coordinates larger than Fp.ORDER are allowed for zip215\n    function aCoordinate(title, n) {\n        ut.aInRange('coordinate ' + title, n, _0n, MASK);\n    }\n    function assertPoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ExtendedPoint expected');\n    }\n    // Converts Extended point to default (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    const toAffineMemo = memoized((p, iz) => {\n        const { ex: x, ey: y, ez: z } = p;\n        const is0 = p.is0();\n        if (iz == null)\n            iz = is0 ? _8n : Fp.inv(z); // 8 was chosen arbitrarily\n        const ax = modP(x * iz);\n        const ay = modP(y * iz);\n        const zz = modP(z * iz);\n        if (is0)\n            return { x: _0n, y: _1n };\n        if (zz !== _1n)\n            throw new Error('invZ was invalid');\n        return { x: ax, y: ay };\n    });\n    const assertValidMemo = memoized((p) => {\n        const { a, d } = CURVE;\n        if (p.is0())\n            throw new Error('bad point: ZERO'); // TODO: optimize, with vars below?\n        // Equation in affine coordinates: ax² + y² = 1 + dx²y²\n        // Equation in projective coordinates (X/Z, Y/Z, Z):  (aX² + Y²)Z² = Z⁴ + dX²Y²\n        const { ex: X, ey: Y, ez: Z, et: T } = p;\n        const X2 = modP(X * X); // X²\n        const Y2 = modP(Y * Y); // Y²\n        const Z2 = modP(Z * Z); // Z²\n        const Z4 = modP(Z2 * Z2); // Z⁴\n        const aX2 = modP(X2 * a); // aX²\n        const left = modP(Z2 * modP(aX2 + Y2)); // (aX² + Y²)Z²\n        const right = modP(Z4 + modP(d * modP(X2 * Y2))); // Z⁴ + dX²Y²\n        if (left !== right)\n            throw new Error('bad point: equation left != right (1)');\n        // In Extended coordinates we also have T, which is x*y=T/Z: check X*Y == Z*T\n        const XY = modP(X * Y);\n        const ZT = modP(Z * T);\n        if (XY !== ZT)\n            throw new Error('bad point: equation left != right (2)');\n        return true;\n    });\n    // Extended Point works in extended coordinates: (x, y, z, t) ∋ (x=x/z, y=y/z, t=xy).\n    // https://en.wikipedia.org/wiki/Twisted_Edwards_curve#Extended_coordinates\n    class Point {\n        constructor(ex, ey, ez, et) {\n            this.ex = ex;\n            this.ey = ey;\n            this.ez = ez;\n            this.et = et;\n            aCoordinate('x', ex);\n            aCoordinate('y', ey);\n            aCoordinate('z', ez);\n            aCoordinate('t', et);\n            Object.freeze(this);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        static fromAffine(p) {\n            if (p instanceof Point)\n                throw new Error('extended point not allowed');\n            const { x, y } = p || {};\n            aCoordinate('x', x);\n            aCoordinate('y', y);\n            return new Point(x, y, _1n, modP(x * y));\n        }\n        static normalizeZ(points) {\n            const toInv = Fp.invertBatch(points.map((p) => p.ez));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        // Multiscalar Multiplication\n        static msm(points, scalars) {\n            return pippenger(Point, Fn, points, scalars);\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            wnaf.setWindowSize(this, windowSize);\n        }\n        // Not required for fromHex(), which always creates valid points.\n        // Could be useful for fromAffine().\n        assertValidity() {\n            assertValidMemo(this);\n        }\n        // Compare one point to another.\n        equals(other) {\n            assertPoint(other);\n            const { ex: X1, ey: Y1, ez: Z1 } = this;\n            const { ex: X2, ey: Y2, ez: Z2 } = other;\n            const X1Z2 = modP(X1 * Z2);\n            const X2Z1 = modP(X2 * Z1);\n            const Y1Z2 = modP(Y1 * Z2);\n            const Y2Z1 = modP(Y2 * Z1);\n            return X1Z2 === X2Z1 && Y1Z2 === Y2Z1;\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        negate() {\n            // Flips point sign to a negative one (-x, y in affine coords)\n            return new Point(modP(-this.ex), this.ey, this.ez, modP(-this.et));\n        }\n        // Fast algo for doubling Extended Point.\n        // https://hyperelliptic.org/EFD/g1p/auto-twisted-extended.html#doubling-dbl-2008-hwcd\n        // Cost: 4M + 4S + 1*a + 6add + 1*2.\n        double() {\n            const { a } = CURVE;\n            const { ex: X1, ey: Y1, ez: Z1 } = this;\n            const A = modP(X1 * X1); // A = X12\n            const B = modP(Y1 * Y1); // B = Y12\n            const C = modP(_2n * modP(Z1 * Z1)); // C = 2*Z12\n            const D = modP(a * A); // D = a*A\n            const x1y1 = X1 + Y1;\n            const E = modP(modP(x1y1 * x1y1) - A - B); // E = (X1+Y1)2-A-B\n            const G = D + B; // G = D+B\n            const F = G - C; // F = G-C\n            const H = D - B; // H = D-B\n            const X3 = modP(E * F); // X3 = E*F\n            const Y3 = modP(G * H); // Y3 = G*H\n            const T3 = modP(E * H); // T3 = E*H\n            const Z3 = modP(F * G); // Z3 = F*G\n            return new Point(X3, Y3, Z3, T3);\n        }\n        // Fast algo for adding 2 Extended Points.\n        // https://hyperelliptic.org/EFD/g1p/auto-twisted-extended.html#addition-add-2008-hwcd\n        // Cost: 9M + 1*a + 1*d + 7add.\n        add(other) {\n            assertPoint(other);\n            const { a, d } = CURVE;\n            const { ex: X1, ey: Y1, ez: Z1, et: T1 } = this;\n            const { ex: X2, ey: Y2, ez: Z2, et: T2 } = other;\n            // Faster algo for adding 2 Extended Points when curve's a=-1.\n            // http://hyperelliptic.org/EFD/g1p/auto-twisted-extended-1.html#addition-add-2008-hwcd-4\n            // Cost: 8M + 8add + 2*2.\n            // Note: It does not check whether the `other` point is valid.\n            if (a === BigInt(-1)) {\n                const A = modP((Y1 - X1) * (Y2 + X2));\n                const B = modP((Y1 + X1) * (Y2 - X2));\n                const F = modP(B - A);\n                if (F === _0n)\n                    return this.double(); // Same point. Tests say it doesn't affect timing\n                const C = modP(Z1 * _2n * T2);\n                const D = modP(T1 * _2n * Z2);\n                const E = D + C;\n                const G = B + A;\n                const H = D - C;\n                const X3 = modP(E * F);\n                const Y3 = modP(G * H);\n                const T3 = modP(E * H);\n                const Z3 = modP(F * G);\n                return new Point(X3, Y3, Z3, T3);\n            }\n            const A = modP(X1 * X2); // A = X1*X2\n            const B = modP(Y1 * Y2); // B = Y1*Y2\n            const C = modP(T1 * d * T2); // C = T1*d*T2\n            const D = modP(Z1 * Z2); // D = Z1*Z2\n            const E = modP((X1 + Y1) * (X2 + Y2) - A - B); // E = (X1+Y1)*(X2+Y2)-A-B\n            const F = D - C; // F = D-C\n            const G = D + C; // G = D+C\n            const H = modP(B - a * A); // H = B-a*A\n            const X3 = modP(E * F); // X3 = E*F\n            const Y3 = modP(G * H); // Y3 = G*H\n            const T3 = modP(E * H); // T3 = E*H\n            const Z3 = modP(F * G); // Z3 = F*G\n            return new Point(X3, Y3, Z3, T3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, n, Point.normalizeZ);\n        }\n        // Constant-time multiplication.\n        multiply(scalar) {\n            const n = scalar;\n            ut.aInRange('scalar', n, _1n, CURVE_ORDER); // 1 <= scalar < L\n            const { p, f } = this.wNAF(n);\n            return Point.normalizeZ([p, f])[0];\n        }\n        // Non-constant-time multiplication. Uses double-and-add algorithm.\n        // It's faster, but should only be used when you don't care about\n        // an exposed private key e.g. sig verification.\n        // Does NOT allow scalars higher than CURVE.n.\n        // Accepts optional accumulator to merge with multiply (important for sparse scalars)\n        multiplyUnsafe(scalar, acc = Point.ZERO) {\n            const n = scalar;\n            ut.aInRange('scalar', n, _0n, CURVE_ORDER); // 0 <= scalar < L\n            if (n === _0n)\n                return I;\n            if (this.is0() || n === _1n)\n                return this;\n            return wnaf.wNAFCachedUnsafe(this, n, Point.normalizeZ, acc);\n        }\n        // Checks if point is of small order.\n        // If you add something to small order point, you will have \"dirty\"\n        // point with torsion component.\n        // Multiplies point by cofactor and checks if the result is 0.\n        isSmallOrder() {\n            return this.multiplyUnsafe(cofactor).is0();\n        }\n        // Multiplies point by curve order and checks if the result is 0.\n        // Returns `false` is the point is dirty.\n        isTorsionFree() {\n            return wnaf.unsafeLadder(this, CURVE_ORDER).is0();\n        }\n        // Converts Extended point to default (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        toAffine(iz) {\n            return toAffineMemo(this, iz);\n        }\n        clearCofactor() {\n            const { h: cofactor } = CURVE;\n            if (cofactor === _1n)\n                return this;\n            return this.multiplyUnsafe(cofactor);\n        }\n        // Converts hash string or Uint8Array to Point.\n        // Uses algo from RFC8032 5.1.3.\n        static fromHex(hex, zip215 = false) {\n            const { d, a } = CURVE;\n            const len = Fp.BYTES;\n            hex = ensureBytes('pointHex', hex, len); // copy hex to a new array\n            abool('zip215', zip215);\n            const normed = hex.slice(); // copy again, we'll manipulate it\n            const lastByte = hex[len - 1]; // select last byte\n            normed[len - 1] = lastByte & ~0x80; // clear last bit\n            const y = ut.bytesToNumberLE(normed);\n            // zip215=true is good for consensus-critical apps. =false follows RFC8032 / NIST186-5.\n            // RFC8032 prohibits >= p, but ZIP215 doesn't\n            // zip215=true:  0 <= y < MASK (2^256 for ed25519)\n            // zip215=false: 0 <= y < P (2^255-19 for ed25519)\n            const max = zip215 ? MASK : Fp.ORDER;\n            ut.aInRange('pointHex.y', y, _0n, max);\n            // Ed25519: x² = (y²-1)/(dy²+1) mod p. Ed448: x² = (y²-1)/(dy²-1) mod p. Generic case:\n            // ax²+y²=1+dx²y² => y²-1=dx²y²-ax² => y²-1=x²(dy²-a) => x²=(y²-1)/(dy²-a)\n            const y2 = modP(y * y); // denominator is always non-0 mod p.\n            const u = modP(y2 - _1n); // u = y² - 1\n            const v = modP(d * y2 - a); // v = d y² + 1.\n            let { isValid, value: x } = uvRatio(u, v); // √(u/v)\n            if (!isValid)\n                throw new Error('Point.fromHex: invalid y coordinate');\n            const isXOdd = (x & _1n) === _1n; // There are 2 square roots. Use x_0 bit to select proper\n            const isLastByteOdd = (lastByte & 0x80) !== 0; // x_0, last bit\n            if (!zip215 && x === _0n && isLastByteOdd)\n                // if x=0 and x_0 = 1, fail\n                throw new Error('Point.fromHex: x=0 and x_0=1');\n            if (isLastByteOdd !== isXOdd)\n                x = modP(-x); // if x_0 != x mod 2, set x = p-x\n            return Point.fromAffine({ x, y });\n        }\n        static fromPrivateKey(privKey) {\n            return getExtendedPublicKey(privKey).point;\n        }\n        toRawBytes() {\n            const { x, y } = this.toAffine();\n            const bytes = ut.numberToBytesLE(y, Fp.BYTES); // each y has 2 x values (x, -y)\n            bytes[bytes.length - 1] |= x & _1n ? 0x80 : 0; // when compressing, it's enough to store y\n            return bytes; // and use the last byte to encode sign of x\n        }\n        toHex() {\n            return ut.bytesToHex(this.toRawBytes()); // Same as toRawBytes, but returns string.\n        }\n    }\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, _1n, modP(CURVE.Gx * CURVE.Gy));\n    Point.ZERO = new Point(_0n, _1n, _1n, _0n); // 0, 1, 1, 0\n    const { BASE: G, ZERO: I } = Point;\n    const wnaf = wNAF(Point, nByteLength * 8);\n    function modN(a) {\n        return mod(a, CURVE_ORDER);\n    }\n    // Little-endian SHA512 with modulo n\n    function modN_LE(hash) {\n        return modN(ut.bytesToNumberLE(hash));\n    }\n    /** Convenience method that creates public key and other stuff. RFC8032 5.1.5 */\n    function getExtendedPublicKey(key) {\n        const len = Fp.BYTES;\n        key = ensureBytes('private key', key, len);\n        // Hash private key with curve's hash function to produce uniformingly random input\n        // Check byte lengths: ensure(64, h(ensure(32, key)))\n        const hashed = ensureBytes('hashed private key', cHash(key), 2 * len);\n        const head = adjustScalarBytes(hashed.slice(0, len)); // clear first half bits, produce FE\n        const prefix = hashed.slice(len, 2 * len); // second half is called key prefix (5.1.6)\n        const scalar = modN_LE(head); // The actual private scalar\n        const point = G.multiply(scalar); // Point on Edwards curve aka public key\n        const pointBytes = point.toRawBytes(); // Uint8Array representation\n        return { head, prefix, scalar, point, pointBytes };\n    }\n    // Calculates EdDSA pub key. RFC8032 5.1.5. Privkey is hashed. Use first half with 3 bits cleared\n    function getPublicKey(privKey) {\n        return getExtendedPublicKey(privKey).pointBytes;\n    }\n    // int('LE', SHA512(dom2(F, C) || msgs)) mod N\n    function hashDomainToScalar(context = new Uint8Array(), ...msgs) {\n        const msg = ut.concatBytes(...msgs);\n        return modN_LE(cHash(domain(msg, ensureBytes('context', context), !!prehash)));\n    }\n    /** Signs message with privateKey. RFC8032 5.1.6 */\n    function sign(msg, privKey, options = {}) {\n        msg = ensureBytes('message', msg);\n        if (prehash)\n            msg = prehash(msg); // for ed25519ph etc.\n        const { prefix, scalar, pointBytes } = getExtendedPublicKey(privKey);\n        const r = hashDomainToScalar(options.context, prefix, msg); // r = dom2(F, C) || prefix || PH(M)\n        const R = G.multiply(r).toRawBytes(); // R = rG\n        const k = hashDomainToScalar(options.context, R, pointBytes, msg); // R || A || PH(M)\n        const s = modN(r + k * scalar); // S = (r + k * s) mod L\n        ut.aInRange('signature.s', s, _0n, CURVE_ORDER); // 0 <= s < l\n        const res = ut.concatBytes(R, ut.numberToBytesLE(s, Fp.BYTES));\n        return ensureBytes('result', res, Fp.BYTES * 2); // 64-byte signature\n    }\n    const verifyOpts = VERIFY_DEFAULT;\n    /**\n     * Verifies EdDSA signature against message and public key. RFC8032 5.1.7.\n     * An extended group equation is checked.\n     */\n    function verify(sig, msg, publicKey, options = verifyOpts) {\n        const { context, zip215 } = options;\n        const len = Fp.BYTES; // Verifies EdDSA signature against message and public key. RFC8032 5.1.7.\n        sig = ensureBytes('signature', sig, 2 * len); // An extended group equation is checked.\n        msg = ensureBytes('message', msg);\n        publicKey = ensureBytes('publicKey', publicKey, len);\n        if (zip215 !== undefined)\n            abool('zip215', zip215);\n        if (prehash)\n            msg = prehash(msg); // for ed25519ph, etc\n        const s = ut.bytesToNumberLE(sig.slice(len, 2 * len));\n        let A, R, SB;\n        try {\n            // zip215=true is good for consensus-critical apps. =false follows RFC8032 / NIST186-5.\n            // zip215=true:  0 <= y < MASK (2^256 for ed25519)\n            // zip215=false: 0 <= y < P (2^255-19 for ed25519)\n            A = Point.fromHex(publicKey, zip215);\n            R = Point.fromHex(sig.slice(0, len), zip215);\n            SB = G.multiplyUnsafe(s); // 0 <= s < l is done inside\n        }\n        catch (error) {\n            return false;\n        }\n        if (!zip215 && A.isSmallOrder())\n            return false;\n        const k = hashDomainToScalar(context, R.toRawBytes(), A.toRawBytes(), msg);\n        const RkA = R.add(A.multiplyUnsafe(k));\n        // Extended group equation\n        // [8][S]B = [8]R + [8][k]A'\n        return RkA.subtract(SB).clearCofactor().equals(Point.ZERO);\n    }\n    G._setWindowSize(8); // Enable precomputes. Slows down first publicKey computation by 20ms.\n    const utils = {\n        getExtendedPublicKey,\n        // ed25519 private keys are uniform 32b. No need to check for modulo bias, like in secp256k1.\n        randomPrivateKey: () => randomBytes(Fp.BYTES),\n        /**\n         * We're doing scalar multiplication (used in getPublicKey etc) with precomputed BASE_POINT\n         * values. This slows down first getPublicKey() by milliseconds (see Speed section),\n         * but allows to speed-up subsequent getPublicKey() calls up to 20x.\n         * @param windowSize 2, 4, 8, 16\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3));\n            return point;\n        },\n    };\n    return {\n        CURVE,\n        getPublicKey,\n        sign,\n        verify,\n        ExtendedPoint: Point,\n        utils,\n    };\n}\n//# sourceMappingURL=edwards.js.map", "/**\n * Montgomery curve methods. It's not really whole montgomery curve,\n * just bunch of very specific methods for X25519 / X448 from\n * [RFC 7748](https://www.rfc-editor.org/rfc/rfc7748)\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { mod, pow } from './modular.js';\nimport { aInRange, bytesToNumberLE, ensureBytes, numberToBytesLE, validateObject, } from './utils.js';\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nfunction validateOpts(curve) {\n    validateObject(curve, {\n        a: 'bigint',\n    }, {\n        montgomeryBits: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n        adjustScalarBytes: 'function',\n        domain: 'function',\n        powPminus2: 'function',\n        Gu: 'bigint',\n    });\n    // Set defaults\n    return Object.freeze({ ...curve });\n}\n// Uses only one coordinate instead of two\nexport function montgomery(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { P } = CURVE;\n    const modP = (n) => mod(n, P);\n    const montgomeryBits = CURVE.montgomeryBits;\n    const montgomeryBytes = Math.ceil(montgomeryBits / 8);\n    const fieldLen = CURVE.nByteLength;\n    const adjustScalarBytes = CURVE.adjustScalarBytes || ((bytes) => bytes);\n    const powPminus2 = CURVE.powPminus2 || ((x) => pow(x, P - BigInt(2), P));\n    // cswap from RFC7748. But it is not from RFC7748!\n    /*\n      cswap(swap, x_2, x_3):\n           dummy = mask(swap) AND (x_2 XOR x_3)\n           x_2 = x_2 XOR dummy\n           x_3 = x_3 XOR dummy\n           Return (x_2, x_3)\n    Where mask(swap) is the all-1 or all-0 word of the same length as x_2\n     and x_3, computed, e.g., as mask(swap) = 0 - swap.\n    */\n    function cswap(swap, x_2, x_3) {\n        const dummy = modP(swap * (x_2 - x_3));\n        x_2 = modP(x_2 - dummy);\n        x_3 = modP(x_3 + dummy);\n        return [x_2, x_3];\n    }\n    // x25519 from 4\n    // The constant a24 is (486662 - 2) / 4 = 121665 for curve25519/X25519\n    const a24 = (CURVE.a - BigInt(2)) / BigInt(4);\n    /**\n     *\n     * @param pointU u coordinate (x) on Montgomery Curve 25519\n     * @param scalar by which the point would be multiplied\n     * @returns new Point on Montgomery curve\n     */\n    function montgomeryLadder(u, scalar) {\n        aInRange('u', u, _0n, P);\n        aInRange('scalar', scalar, _0n, P);\n        // Section 5: Implementations MUST accept non-canonical values and process them as\n        // if they had been reduced modulo the field prime.\n        const k = scalar;\n        const x_1 = u;\n        let x_2 = _1n;\n        let z_2 = _0n;\n        let x_3 = u;\n        let z_3 = _1n;\n        let swap = _0n;\n        let sw;\n        for (let t = BigInt(montgomeryBits - 1); t >= _0n; t--) {\n            const k_t = (k >> t) & _1n;\n            swap ^= k_t;\n            sw = cswap(swap, x_2, x_3);\n            x_2 = sw[0];\n            x_3 = sw[1];\n            sw = cswap(swap, z_2, z_3);\n            z_2 = sw[0];\n            z_3 = sw[1];\n            swap = k_t;\n            const A = x_2 + z_2;\n            const AA = modP(A * A);\n            const B = x_2 - z_2;\n            const BB = modP(B * B);\n            const E = AA - BB;\n            const C = x_3 + z_3;\n            const D = x_3 - z_3;\n            const DA = modP(D * A);\n            const CB = modP(C * B);\n            const dacb = DA + CB;\n            const da_cb = DA - CB;\n            x_3 = modP(dacb * dacb);\n            z_3 = modP(x_1 * modP(da_cb * da_cb));\n            x_2 = modP(AA * BB);\n            z_2 = modP(E * (AA + modP(a24 * E)));\n        }\n        // (x_2, x_3) = cswap(swap, x_2, x_3)\n        sw = cswap(swap, x_2, x_3);\n        x_2 = sw[0];\n        x_3 = sw[1];\n        // (z_2, z_3) = cswap(swap, z_2, z_3)\n        sw = cswap(swap, z_2, z_3);\n        z_2 = sw[0];\n        z_3 = sw[1];\n        // z_2^(p - 2)\n        const z2 = powPminus2(z_2);\n        // Return x_2 * (z_2^(p - 2))\n        return modP(x_2 * z2);\n    }\n    function encodeUCoordinate(u) {\n        return numberToBytesLE(modP(u), montgomeryBytes);\n    }\n    function decodeUCoordinate(uEnc) {\n        // Section 5: When receiving such an array, implementations of X25519\n        // MUST mask the most significant bit in the final byte.\n        const u = ensureBytes('u coordinate', uEnc, montgomeryBytes);\n        if (fieldLen === 32)\n            u[31] &= 127; // 0b0111_1111\n        return bytesToNumberLE(u);\n    }\n    function decodeScalar(n) {\n        const bytes = ensureBytes('scalar', n);\n        const len = bytes.length;\n        if (len !== montgomeryBytes && len !== fieldLen) {\n            let valid = '' + montgomeryBytes + ' or ' + fieldLen;\n            throw new Error('invalid scalar, expected ' + valid + ' bytes, got ' + len);\n        }\n        return bytesToNumberLE(adjustScalarBytes(bytes));\n    }\n    function scalarMult(scalar, u) {\n        const pointU = decodeUCoordinate(u);\n        const _scalar = decodeScalar(scalar);\n        const pu = montgomeryLadder(pointU, _scalar);\n        // The result was not contributory\n        // https://cr.yp.to/ecdh.html#validate\n        if (pu === _0n)\n            throw new Error('invalid private or public key received');\n        return encodeUCoordinate(pu);\n    }\n    // Computes public key from private. By doing scalar multiplication of base point.\n    const GuBytes = encodeUCoordinate(CURVE.Gu);\n    function scalarMultBase(scalar) {\n        return scalarMult(scalar, GuBytes);\n    }\n    return {\n        scalarMult,\n        scalarMultBase,\n        getSharedSecret: (privateKey, publicKey) => scalarMult(privateKey, publicKey),\n        getPublicKey: (privateKey) => scalarMultBase(privateKey),\n        utils: { randomPrivateKey: () => CURVE.randomBytes(CURVE.nByteLength) },\n        GuBytes: GuBytes,\n    };\n}\n//# sourceMappingURL=montgomery.js.map", "/**\n * ed25519 Twisted <PERSON> curve with following addons:\n * - X25519 ECDH\n * - Ristretto cofactor elimination\n * - Elligator hash-to-group / point indistinguishability\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha512 } from '@noble/hashes/sha512';\nimport { concatBytes, randomBytes, utf8ToBytes } from '@noble/hashes/utils';\nimport { twistedEdwards } from './abstract/edwards.js';\nimport { createHasher, expand_message_xmd, } from './abstract/hash-to-curve.js';\nimport { Field, FpSqrtEven, isNegativeLE, mod, pow2 } from './abstract/modular.js';\nimport { montgomery } from './abstract/montgomery.js';\nimport { pippenger } from './abstract/curve.js';\nimport { bytesToHex, bytesToNumberLE, ensureBytes, equalBytes, numberToBytesLE, } from './abstract/utils.js';\nconst ED25519_P = BigInt('57896044618658097711785492504343953926634992332820282019728792003956564819949');\n// √(-1) aka √(a) aka 2^((p-1)/4)\nconst ED25519_SQRT_M1 = /* @__PURE__ */ BigInt('19681161376707505956807079304988542015446066515923890162744021073123829784752');\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3);\n// prettier-ignore\nconst _5n = BigInt(5), _8n = BigInt(8);\nfunction ed25519_pow_2_252_3(x) {\n    // prettier-ignore\n    const _10n = BigInt(10), _20n = BigInt(20), _40n = BigInt(40), _80n = BigInt(80);\n    const P = ED25519_P;\n    const x2 = (x * x) % P;\n    const b2 = (x2 * x) % P; // x^3, 11\n    const b4 = (pow2(b2, _2n, P) * b2) % P; // x^15, 1111\n    const b5 = (pow2(b4, _1n, P) * x) % P; // x^31\n    const b10 = (pow2(b5, _5n, P) * b5) % P;\n    const b20 = (pow2(b10, _10n, P) * b10) % P;\n    const b40 = (pow2(b20, _20n, P) * b20) % P;\n    const b80 = (pow2(b40, _40n, P) * b40) % P;\n    const b160 = (pow2(b80, _80n, P) * b80) % P;\n    const b240 = (pow2(b160, _80n, P) * b80) % P;\n    const b250 = (pow2(b240, _10n, P) * b10) % P;\n    const pow_p_5_8 = (pow2(b250, _2n, P) * x) % P;\n    // ^ To pow to (p+3)/8, multiply it by x.\n    return { pow_p_5_8, b2 };\n}\nfunction adjustScalarBytes(bytes) {\n    // Section 5: For X25519, in order to decode 32 random bytes as an integer scalar,\n    // set the three least significant bits of the first byte\n    bytes[0] &= 248; // 0b1111_1000\n    // and the most significant bit of the last to zero,\n    bytes[31] &= 127; // 0b0111_1111\n    // set the second most significant bit of the last byte to 1\n    bytes[31] |= 64; // 0b0100_0000\n    return bytes;\n}\n// sqrt(u/v)\nfunction uvRatio(u, v) {\n    const P = ED25519_P;\n    const v3 = mod(v * v * v, P); // v³\n    const v7 = mod(v3 * v3 * v, P); // v⁷\n    // (p+3)/8 and (p-5)/8\n    const pow = ed25519_pow_2_252_3(u * v7).pow_p_5_8;\n    let x = mod(u * v3 * pow, P); // (uv³)(uv⁷)^(p-5)/8\n    const vx2 = mod(v * x * x, P); // vx²\n    const root1 = x; // First root candidate\n    const root2 = mod(x * ED25519_SQRT_M1, P); // Second root candidate\n    const useRoot1 = vx2 === u; // If vx² = u (mod p), x is a square root\n    const useRoot2 = vx2 === mod(-u, P); // If vx² = -u, set x <-- x * 2^((p-1)/4)\n    const noRoot = vx2 === mod(-u * ED25519_SQRT_M1, P); // There is no valid root, vx² = -u√(-1)\n    if (useRoot1)\n        x = root1;\n    if (useRoot2 || noRoot)\n        x = root2; // We return root2 anyway, for const-time\n    if (isNegativeLE(x, P))\n        x = mod(-x, P);\n    return { isValid: useRoot1 || useRoot2, value: x };\n}\n// Just in case\nexport const ED25519_TORSION_SUBGROUP = [\n    '0100000000000000000000000000000000000000000000000000000000000000',\n    'c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac037a',\n    '0000000000000000000000000000000000000000000000000000000000000080',\n    '26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05',\n    'ecffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f',\n    '26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc85',\n    '0000000000000000000000000000000000000000000000000000000000000000',\n    'c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac03fa',\n];\nconst Fp = /* @__PURE__ */ (() => Field(ED25519_P, undefined, true))();\nconst ed25519Defaults = /* @__PURE__ */ (() => ({\n    // Param: a\n    a: BigInt(-1), // Fp.create(-1) is proper; our way still works and is faster\n    // d is equal to -121665/121666 over finite field.\n    // Negative number is P - number, and division is invert(number, P)\n    d: BigInt('37095705934669439343138083508754565189542113879843219016388785533085940283555'),\n    // Finite field 𝔽p over which we'll do calculations; 2n**255n - 19n\n    Fp,\n    // Subgroup order: how many points curve has\n    // 2n**252n + 27742317777372353535851937790883648493n;\n    n: BigInt('7237005577332262213973186563042994240857116359379907606001950938285454250989'),\n    // Cofactor\n    h: _8n,\n    // Base point (x, y) aka generator point\n    Gx: BigInt('15112221349535400772501151409588531511454012693041857206046113283949847762202'),\n    Gy: BigInt('46316835694926478169428394003475163141307993866256225615783033603165251855960'),\n    hash: sha512,\n    randomBytes,\n    adjustScalarBytes,\n    // dom2\n    // Ratio of u to v. Allows us to combine inversion and square root. Uses algo from RFC8032 5.1.3.\n    // Constant-time, u/√v\n    uvRatio,\n}))();\n/**\n * ed25519 curve with EdDSA signatures.\n * @example\n * import { ed25519 } from '@noble/curves/ed25519';\n * const priv = ed25519.utils.randomPrivateKey();\n * const pub = ed25519.getPublicKey(priv);\n * const msg = new TextEncoder().encode('hello');\n * const sig = ed25519.sign(msg, priv);\n * ed25519.verify(sig, msg, pub); // Default mode: follows ZIP215\n * ed25519.verify(sig, msg, pub, { zip215: false }); // RFC8032 / FIPS 186-5\n */\nexport const ed25519 = /* @__PURE__ */ (() => twistedEdwards(ed25519Defaults))();\nfunction ed25519_domain(data, ctx, phflag) {\n    if (ctx.length > 255)\n        throw new Error('Context is too big');\n    return concatBytes(utf8ToBytes('SigEd25519 no Ed25519 collisions'), new Uint8Array([phflag ? 1 : 0, ctx.length]), ctx, data);\n}\nexport const ed25519ctx = /* @__PURE__ */ (() => twistedEdwards({\n    ...ed25519Defaults,\n    domain: ed25519_domain,\n}))();\nexport const ed25519ph = /* @__PURE__ */ (() => twistedEdwards(Object.assign({}, ed25519Defaults, {\n    domain: ed25519_domain,\n    prehash: sha512,\n})))();\n/**\n * ECDH using curve25519 aka x25519.\n * @example\n * import { x25519 } from '@noble/curves/ed25519';\n * const priv = 'a546e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449ac4';\n * const pub = 'e6db6867583030db3594c1a424b15f7c726624ec26b3353b10a903a6d0ab1c4c';\n * x25519.getSharedSecret(priv, pub) === x25519.scalarMult(priv, pub); // aliases\n * x25519.getPublicKey(priv) === x25519.scalarMultBase(priv);\n * x25519.getPublicKey(x25519.utils.randomPrivateKey());\n */\nexport const x25519 = /* @__PURE__ */ (() => montgomery({\n    P: ED25519_P,\n    a: BigInt(486662),\n    montgomeryBits: 255, // n is 253 bits\n    nByteLength: 32,\n    Gu: BigInt(9),\n    powPminus2: (x) => {\n        const P = ED25519_P;\n        // x^(p-2) aka x^(2^255-21)\n        const { pow_p_5_8, b2 } = ed25519_pow_2_252_3(x);\n        return mod(pow2(pow_p_5_8, _3n, P) * b2, P);\n    },\n    adjustScalarBytes,\n    randomBytes,\n}))();\n/**\n * Converts ed25519 public key to x25519 public key. Uses formula:\n * * `(u, v) = ((1+y)/(1-y), sqrt(-486664)*u/x)`\n * * `(x, y) = (sqrt(-486664)*u/v, (u-1)/(u+1))`\n * @example\n *   const someonesPub = ed25519.getPublicKey(ed25519.utils.randomPrivateKey());\n *   const aPriv = x25519.utils.randomPrivateKey();\n *   x25519.getSharedSecret(aPriv, edwardsToMontgomeryPub(someonesPub))\n */\nexport function edwardsToMontgomeryPub(edwardsPub) {\n    const { y } = ed25519.ExtendedPoint.fromHex(edwardsPub);\n    const _1n = BigInt(1);\n    return Fp.toBytes(Fp.create((_1n + y) * Fp.inv(_1n - y)));\n}\nexport const edwardsToMontgomery = edwardsToMontgomeryPub; // deprecated\n/**\n * Converts ed25519 secret key to x25519 secret key.\n * @example\n *   const someonesPub = x25519.getPublicKey(x25519.utils.randomPrivateKey());\n *   const aPriv = ed25519.utils.randomPrivateKey();\n *   x25519.getSharedSecret(edwardsToMontgomeryPriv(aPriv), someonesPub)\n */\nexport function edwardsToMontgomeryPriv(edwardsPriv) {\n    const hashed = ed25519Defaults.hash(edwardsPriv.subarray(0, 32));\n    return ed25519Defaults.adjustScalarBytes(hashed).subarray(0, 32);\n}\n// Hash To Curve Elligator2 Map (NOTE: different from ristretto255 elligator)\n// NOTE: very important part is usage of FpSqrtEven for ELL2_C1_EDWARDS, since\n// SageMath returns different root first and everything falls apart\nconst ELL2_C1 = /* @__PURE__ */ (() => (Fp.ORDER + _3n) / _8n)(); // 1. c1 = (q + 3) / 8       # Integer arithmetic\nconst ELL2_C2 = /* @__PURE__ */ (() => Fp.pow(_2n, ELL2_C1))(); // 2. c2 = 2^c1\nconst ELL2_C3 = /* @__PURE__ */ (() => Fp.sqrt(Fp.neg(Fp.ONE)))(); // 3. c3 = sqrt(-1)\n// prettier-ignore\nfunction map_to_curve_elligator2_curve25519(u) {\n    const ELL2_C4 = (Fp.ORDER - _5n) / _8n; // 4. c4 = (q - 5) / 8       # Integer arithmetic\n    const ELL2_J = BigInt(486662);\n    let tv1 = Fp.sqr(u); //  1.  tv1 = u^2\n    tv1 = Fp.mul(tv1, _2n); //  2.  tv1 = 2 * tv1\n    let xd = Fp.add(tv1, Fp.ONE); //  3.   xd = tv1 + 1         # Nonzero: -1 is square (mod p), tv1 is not\n    let x1n = Fp.neg(ELL2_J); //  4.  x1n = -J              # x1 = x1n / xd = -J / (1 + 2 * u^2)\n    let tv2 = Fp.sqr(xd); //  5.  tv2 = xd^2\n    let gxd = Fp.mul(tv2, xd); //  6.  gxd = tv2 * xd        # gxd = xd^3\n    let gx1 = Fp.mul(tv1, ELL2_J); //  7.  gx1 = J * tv1         # x1n + J * xd\n    gx1 = Fp.mul(gx1, x1n); //  8.  gx1 = gx1 * x1n       # x1n^2 + J * x1n * xd\n    gx1 = Fp.add(gx1, tv2); //  9.  gx1 = gx1 + tv2       # x1n^2 + J * x1n * xd + xd^2\n    gx1 = Fp.mul(gx1, x1n); //  10. gx1 = gx1 * x1n       # x1n^3 + J * x1n^2 * xd + x1n * xd^2\n    let tv3 = Fp.sqr(gxd); //  11. tv3 = gxd^2\n    tv2 = Fp.sqr(tv3); //  12. tv2 = tv3^2           # gxd^4\n    tv3 = Fp.mul(tv3, gxd); //  13. tv3 = tv3 * gxd       # gxd^3\n    tv3 = Fp.mul(tv3, gx1); //  14. tv3 = tv3 * gx1       # gx1 * gxd^3\n    tv2 = Fp.mul(tv2, tv3); //  15. tv2 = tv2 * tv3       # gx1 * gxd^7\n    let y11 = Fp.pow(tv2, ELL2_C4); //  16. y11 = tv2^c4        # (gx1 * gxd^7)^((p - 5) / 8)\n    y11 = Fp.mul(y11, tv3); //  17. y11 = y11 * tv3       # gx1*gxd^3*(gx1*gxd^7)^((p-5)/8)\n    let y12 = Fp.mul(y11, ELL2_C3); //  18. y12 = y11 * c3\n    tv2 = Fp.sqr(y11); //  19. tv2 = y11^2\n    tv2 = Fp.mul(tv2, gxd); //  20. tv2 = tv2 * gxd\n    let e1 = Fp.eql(tv2, gx1); //  21.  e1 = tv2 == gx1\n    let y1 = Fp.cmov(y12, y11, e1); //  22.  y1 = CMOV(y12, y11, e1)  # If g(x1) is square, this is its sqrt\n    let x2n = Fp.mul(x1n, tv1); //  23. x2n = x1n * tv1       # x2 = x2n / xd = 2 * u^2 * x1n / xd\n    let y21 = Fp.mul(y11, u); //  24. y21 = y11 * u\n    y21 = Fp.mul(y21, ELL2_C2); //  25. y21 = y21 * c2\n    let y22 = Fp.mul(y21, ELL2_C3); //  26. y22 = y21 * c3\n    let gx2 = Fp.mul(gx1, tv1); //  27. gx2 = gx1 * tv1       # g(x2) = gx2 / gxd = 2 * u^2 * g(x1)\n    tv2 = Fp.sqr(y21); //  28. tv2 = y21^2\n    tv2 = Fp.mul(tv2, gxd); //  29. tv2 = tv2 * gxd\n    let e2 = Fp.eql(tv2, gx2); //  30.  e2 = tv2 == gx2\n    let y2 = Fp.cmov(y22, y21, e2); //  31.  y2 = CMOV(y22, y21, e2)  # If g(x2) is square, this is its sqrt\n    tv2 = Fp.sqr(y1); //  32. tv2 = y1^2\n    tv2 = Fp.mul(tv2, gxd); //  33. tv2 = tv2 * gxd\n    let e3 = Fp.eql(tv2, gx1); //  34.  e3 = tv2 == gx1\n    let xn = Fp.cmov(x2n, x1n, e3); //  35.  xn = CMOV(x2n, x1n, e3)  # If e3, x = x1, else x = x2\n    let y = Fp.cmov(y2, y1, e3); //  36.   y = CMOV(y2, y1, e3)    # If e3, y = y1, else y = y2\n    let e4 = Fp.isOdd(y); //  37.  e4 = sgn0(y) == 1        # Fix sign of y\n    y = Fp.cmov(y, Fp.neg(y), e3 !== e4); //  38.   y = CMOV(y, -y, e3 XOR e4)\n    return { xMn: xn, xMd: xd, yMn: y, yMd: _1n }; //  39. return (xn, xd, y, 1)\n}\nconst ELL2_C1_EDWARDS = /* @__PURE__ */ (() => FpSqrtEven(Fp, Fp.neg(BigInt(486664))))(); // sgn0(c1) MUST equal 0\nfunction map_to_curve_elligator2_edwards25519(u) {\n    const { xMn, xMd, yMn, yMd } = map_to_curve_elligator2_curve25519(u); //  1.  (xMn, xMd, yMn, yMd) =\n    // map_to_curve_elligator2_curve25519(u)\n    let xn = Fp.mul(xMn, yMd); //  2.  xn = xMn * yMd\n    xn = Fp.mul(xn, ELL2_C1_EDWARDS); //  3.  xn = xn * c1\n    let xd = Fp.mul(xMd, yMn); //  4.  xd = xMd * yMn    # xn / xd = c1 * xM / yM\n    let yn = Fp.sub(xMn, xMd); //  5.  yn = xMn - xMd\n    let yd = Fp.add(xMn, xMd); //  6.  yd = xMn + xMd    # (n / d - 1) / (n / d + 1) = (n - d) / (n + d)\n    let tv1 = Fp.mul(xd, yd); //  7. tv1 = xd * yd\n    let e = Fp.eql(tv1, Fp.ZERO); //  8.   e = tv1 == 0\n    xn = Fp.cmov(xn, Fp.ZERO, e); //  9.  xn = CMOV(xn, 0, e)\n    xd = Fp.cmov(xd, Fp.ONE, e); //  10. xd = CMOV(xd, 1, e)\n    yn = Fp.cmov(yn, Fp.ONE, e); //  11. yn = CMOV(yn, 1, e)\n    yd = Fp.cmov(yd, Fp.ONE, e); //  12. yd = CMOV(yd, 1, e)\n    const inv = Fp.invertBatch([xd, yd]); // batch division\n    return { x: Fp.mul(xn, inv[0]), y: Fp.mul(yn, inv[1]) }; //  13. return (xn, xd, yn, yd)\n}\nconst htf = /* @__PURE__ */ (() => createHasher(ed25519.ExtendedPoint, (scalars) => map_to_curve_elligator2_edwards25519(scalars[0]), {\n    DST: 'edwards25519_XMD:SHA-512_ELL2_RO_',\n    encodeDST: 'edwards25519_XMD:SHA-512_ELL2_NU_',\n    p: Fp.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: sha512,\n}))();\nexport const hashToCurve = /* @__PURE__ */ (() => htf.hashToCurve)();\nexport const encodeToCurve = /* @__PURE__ */ (() => htf.encodeToCurve)();\nfunction assertRstPoint(other) {\n    if (!(other instanceof RistPoint))\n        throw new Error('RistrettoPoint expected');\n}\n// √(-1) aka √(a) aka 2^((p-1)/4)\nconst SQRT_M1 = ED25519_SQRT_M1;\n// √(ad - 1)\nconst SQRT_AD_MINUS_ONE = /* @__PURE__ */ BigInt('25063068953384623474111414158702152701244531502492656460079210482610430750235');\n// 1 / √(a-d)\nconst INVSQRT_A_MINUS_D = /* @__PURE__ */ BigInt('54469307008909316920995813868745141605393597292927456921205312896311721017578');\n// 1-d²\nconst ONE_MINUS_D_SQ = /* @__PURE__ */ BigInt('1159843021668779879193775521855586647937357759715417654439879720876111806838');\n// (d-1)²\nconst D_MINUS_ONE_SQ = /* @__PURE__ */ BigInt('40440834346308536858101042469323190826248399146238708352240133220865137265952');\n// Calculates 1/√(number)\nconst invertSqrt = (number) => uvRatio(_1n, number);\nconst MAX_255B = /* @__PURE__ */ BigInt('0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff');\nconst bytes255ToNumberLE = (bytes) => ed25519.CURVE.Fp.create(bytesToNumberLE(bytes) & MAX_255B);\n// Computes Elligator map for Ristretto\n// https://ristretto.group/formulas/elligator.html\nfunction calcElligatorRistrettoMap(r0) {\n    const { d } = ed25519.CURVE;\n    const P = ed25519.CURVE.Fp.ORDER;\n    const mod = ed25519.CURVE.Fp.create;\n    const r = mod(SQRT_M1 * r0 * r0); // 1\n    const Ns = mod((r + _1n) * ONE_MINUS_D_SQ); // 2\n    let c = BigInt(-1); // 3\n    const D = mod((c - d * r) * mod(r + d)); // 4\n    let { isValid: Ns_D_is_sq, value: s } = uvRatio(Ns, D); // 5\n    let s_ = mod(s * r0); // 6\n    if (!isNegativeLE(s_, P))\n        s_ = mod(-s_);\n    if (!Ns_D_is_sq)\n        s = s_; // 7\n    if (!Ns_D_is_sq)\n        c = r; // 8\n    const Nt = mod(c * (r - _1n) * D_MINUS_ONE_SQ - D); // 9\n    const s2 = s * s;\n    const W0 = mod((s + s) * D); // 10\n    const W1 = mod(Nt * SQRT_AD_MINUS_ONE); // 11\n    const W2 = mod(_1n - s2); // 12\n    const W3 = mod(_1n + s2); // 13\n    return new ed25519.ExtendedPoint(mod(W0 * W3), mod(W2 * W1), mod(W1 * W3), mod(W0 * W2));\n}\n/**\n * Each ed25519/ExtendedPoint has 8 different equivalent points. This can be\n * a source of bugs for protocols like ring signatures. Ristretto was created to solve this.\n * Ristretto point operates in X:Y:Z:T extended coordinates like ExtendedPoint,\n * but it should work in its own namespace: do not combine those two.\n * https://datatracker.ietf.org/doc/html/draft-irtf-cfrg-ristretto255-decaf448\n */\nclass RistPoint {\n    // Private property to discourage combining ExtendedPoint + RistrettoPoint\n    // Always use Ristretto encoding/decoding instead.\n    constructor(ep) {\n        this.ep = ep;\n    }\n    static fromAffine(ap) {\n        return new RistPoint(ed25519.ExtendedPoint.fromAffine(ap));\n    }\n    /**\n     * Takes uniform output of 64-byte hash function like sha512 and converts it to `RistrettoPoint`.\n     * The hash-to-group operation applies Elligator twice and adds the results.\n     * **Note:** this is one-way map, there is no conversion from point to hash.\n     * https://ristretto.group/formulas/elligator.html\n     * @param hex 64-byte output of a hash function\n     */\n    static hashToCurve(hex) {\n        hex = ensureBytes('ristrettoHash', hex, 64);\n        const r1 = bytes255ToNumberLE(hex.slice(0, 32));\n        const R1 = calcElligatorRistrettoMap(r1);\n        const r2 = bytes255ToNumberLE(hex.slice(32, 64));\n        const R2 = calcElligatorRistrettoMap(r2);\n        return new RistPoint(R1.add(R2));\n    }\n    /**\n     * Converts ristretto-encoded string to ristretto point.\n     * https://ristretto.group/formulas/decoding.html\n     * @param hex Ristretto-encoded 32 bytes. Not every 32-byte string is valid ristretto encoding\n     */\n    static fromHex(hex) {\n        hex = ensureBytes('ristrettoHex', hex, 32);\n        const { a, d } = ed25519.CURVE;\n        const P = ed25519.CURVE.Fp.ORDER;\n        const mod = ed25519.CURVE.Fp.create;\n        const emsg = 'RistrettoPoint.fromHex: the hex is not valid encoding of RistrettoPoint';\n        const s = bytes255ToNumberLE(hex);\n        // 1. Check that s_bytes is the canonical encoding of a field element, or else abort.\n        // 3. Check that s is non-negative, or else abort\n        if (!equalBytes(numberToBytesLE(s, 32), hex) || isNegativeLE(s, P))\n            throw new Error(emsg);\n        const s2 = mod(s * s);\n        const u1 = mod(_1n + a * s2); // 4 (a is -1)\n        const u2 = mod(_1n - a * s2); // 5\n        const u1_2 = mod(u1 * u1);\n        const u2_2 = mod(u2 * u2);\n        const v = mod(a * d * u1_2 - u2_2); // 6\n        const { isValid, value: I } = invertSqrt(mod(v * u2_2)); // 7\n        const Dx = mod(I * u2); // 8\n        const Dy = mod(I * Dx * v); // 9\n        let x = mod((s + s) * Dx); // 10\n        if (isNegativeLE(x, P))\n            x = mod(-x); // 10\n        const y = mod(u1 * Dy); // 11\n        const t = mod(x * y); // 12\n        if (!isValid || isNegativeLE(t, P) || y === _0n)\n            throw new Error(emsg);\n        return new RistPoint(new ed25519.ExtendedPoint(x, y, _1n, t));\n    }\n    static msm(points, scalars) {\n        const Fn = Field(ed25519.CURVE.n, ed25519.CURVE.nBitLength);\n        return pippenger(RistPoint, Fn, points, scalars);\n    }\n    /**\n     * Encodes ristretto point to Uint8Array.\n     * https://ristretto.group/formulas/encoding.html\n     */\n    toRawBytes() {\n        let { ex: x, ey: y, ez: z, et: t } = this.ep;\n        const P = ed25519.CURVE.Fp.ORDER;\n        const mod = ed25519.CURVE.Fp.create;\n        const u1 = mod(mod(z + y) * mod(z - y)); // 1\n        const u2 = mod(x * y); // 2\n        // Square root always exists\n        const u2sq = mod(u2 * u2);\n        const { value: invsqrt } = invertSqrt(mod(u1 * u2sq)); // 3\n        const D1 = mod(invsqrt * u1); // 4\n        const D2 = mod(invsqrt * u2); // 5\n        const zInv = mod(D1 * D2 * t); // 6\n        let D; // 7\n        if (isNegativeLE(t * zInv, P)) {\n            let _x = mod(y * SQRT_M1);\n            let _y = mod(x * SQRT_M1);\n            x = _x;\n            y = _y;\n            D = mod(D1 * INVSQRT_A_MINUS_D);\n        }\n        else {\n            D = D2; // 8\n        }\n        if (isNegativeLE(x * zInv, P))\n            y = mod(-y); // 9\n        let s = mod((z - y) * D); // 10 (check footer's note, no sqrt(-a))\n        if (isNegativeLE(s, P))\n            s = mod(-s);\n        return numberToBytesLE(s, 32); // 11\n    }\n    toHex() {\n        return bytesToHex(this.toRawBytes());\n    }\n    toString() {\n        return this.toHex();\n    }\n    // Compare one point to another.\n    equals(other) {\n        assertRstPoint(other);\n        const { ex: X1, ey: Y1 } = this.ep;\n        const { ex: X2, ey: Y2 } = other.ep;\n        const mod = ed25519.CURVE.Fp.create;\n        // (x1 * y2 == y1 * x2) | (y1 * y2 == x1 * x2)\n        const one = mod(X1 * Y2) === mod(Y1 * X2);\n        const two = mod(Y1 * Y2) === mod(X1 * X2);\n        return one || two;\n    }\n    add(other) {\n        assertRstPoint(other);\n        return new RistPoint(this.ep.add(other.ep));\n    }\n    subtract(other) {\n        assertRstPoint(other);\n        return new RistPoint(this.ep.subtract(other.ep));\n    }\n    multiply(scalar) {\n        return new RistPoint(this.ep.multiply(scalar));\n    }\n    multiplyUnsafe(scalar) {\n        return new RistPoint(this.ep.multiplyUnsafe(scalar));\n    }\n    double() {\n        return new RistPoint(this.ep.double());\n    }\n    negate() {\n        return new RistPoint(this.ep.negate());\n    }\n}\nexport const RistrettoPoint = /* @__PURE__ */ (() => {\n    if (!RistPoint.BASE)\n        RistPoint.BASE = new RistPoint(ed25519.ExtendedPoint.BASE);\n    if (!RistPoint.ZERO)\n        RistPoint.ZERO = new RistPoint(ed25519.ExtendedPoint.ZERO);\n    return RistPoint;\n})();\n// Hashing to ristretto255. https://www.rfc-editor.org/rfc/rfc9380#appendix-B\nexport const hashToRistretto255 = (msg, options) => {\n    const d = options.DST;\n    const DST = typeof d === 'string' ? utf8ToBytes(d) : d;\n    const uniform_bytes = expand_message_xmd(msg, DST, 64, sha512);\n    const P = RistPoint.hashToCurve(uniform_bytes);\n    return P;\n};\nexport const hash_to_ristretto255 = hashToRistretto255; // legacy\n//# sourceMappingURL=ed25519.js.map", "import { <PERSON><PERSON><PERSON><PERSON><PERSON>THeader } from \"./types\";\n\n// ---------- JWT ----------------------------------------------- //\n\nexport const JWT_IRIDIUM_ALG: IridiumJWTHeader[\"alg\"] = \"EdDSA\";\n\nexport const JWT_IRIDIUM_TYP: IridiumJWTHeader[\"typ\"] = \"JWT\";\n\nexport const JWT_DELIMITER = \".\";\n\nexport const JWT_ENCODING = \"base64url\";\n\nexport const JSON_ENCODING = \"utf8\";\n\nexport const DATA_ENCODING = \"utf8\";\n\n// ---------- DID ----------------------------------------------- //\n\nexport const DID_DELIMITER = \":\";\n\nexport const DID_PREFIX = \"did\";\n\nexport const DID_METHOD = \"key\";\n\n// ---------- Multicodec ----------------------------------------------- //\n\nexport const MULTICODEC_ED25519_ENCODING = \"base58btc\";\n\nexport const MULTICODEC_ED25519_BASE = \"z\";\n\nexport const MULTICODEC_ED25519_HEADER = \"K36\";\n\nexport const MULTICODEC_ED25519_LENGTH = 32;\n\n// ---------- KeyPair ----------------------------------------------- //\n\nexport const KEY_PAIR_SEED_LENGTH = 32;\n", "export function asUint8Array(buf) {\n  if (globalThis.Buffer != null) {\n    return new Uint8Array(buf.buffer, buf.byteOffset, buf.byteLength);\n  }\n  return buf;\n}", "import { asUint8Array } from './util/as-uint8array.js';\nexport function alloc(size = 0) {\n  if (globalThis.Buffer != null && globalThis.Buffer.alloc != null) {\n    return asUint8Array(globalThis.Buffer.alloc(size));\n  }\n  return new Uint8Array(size);\n}\nexport function allocUnsafe(size = 0) {\n  if (globalThis.Buffer != null && globalThis.Buffer.allocUnsafe != null) {\n    return asUint8Array(globalThis.Buffer.allocUnsafe(size));\n  }\n  return new Uint8Array(size);\n}", "import { allocUnsafe } from './alloc.js';\nimport { asUint8Array } from './util/as-uint8array.js';\nexport function concat(arrays, length) {\n  if (!length) {\n    length = arrays.reduce((acc, curr) => acc + curr.length, 0);\n  }\n  const output = allocUnsafe(length);\n  let offset = 0;\n  for (const arr of arrays) {\n    output.set(arr, offset);\n    offset += arr.length;\n  }\n  return asUint8Array(output);\n}", "function base(ALPHABET, name) {\n  if (ALPHABET.length >= 255) {\n    throw new TypeError('Alphabet too long');\n  }\n  var BASE_MAP = new Uint8Array(256);\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255;\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i);\n    var xc = x.charCodeAt(0);\n    if (BASE_MAP[xc] !== 255) {\n      throw new TypeError(x + ' is ambiguous');\n    }\n    BASE_MAP[xc] = i;\n  }\n  var BASE = ALPHABET.length;\n  var LEADER = ALPHABET.charAt(0);\n  var FACTOR = Math.log(BASE) / Math.log(256);\n  var iFACTOR = Math.log(256) / Math.log(BASE);\n  function encode(source) {\n    if (source instanceof Uint8Array);\n    else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength);\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source);\n    }\n    if (!(source instanceof Uint8Array)) {\n      throw new TypeError('Expected Uint8Array');\n    }\n    if (source.length === 0) {\n      return '';\n    }\n    var zeroes = 0;\n    var length = 0;\n    var pbegin = 0;\n    var pend = source.length;\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++;\n      zeroes++;\n    }\n    var size = (pend - pbegin) * iFACTOR + 1 >>> 0;\n    var b58 = new Uint8Array(size);\n    while (pbegin !== pend) {\n      var carry = source[pbegin];\n      var i = 0;\n      for (var it1 = size - 1; (carry !== 0 || i < length) && it1 !== -1; it1--, i++) {\n        carry += 256 * b58[it1] >>> 0;\n        b58[it1] = carry % BASE >>> 0;\n        carry = carry / BASE >>> 0;\n      }\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n      length = i;\n      pbegin++;\n    }\n    var it2 = size - length;\n    while (it2 !== size && b58[it2] === 0) {\n      it2++;\n    }\n    var str = LEADER.repeat(zeroes);\n    for (; it2 < size; ++it2) {\n      str += ALPHABET.charAt(b58[it2]);\n    }\n    return str;\n  }\n  function decodeUnsafe(source) {\n    if (typeof source !== 'string') {\n      throw new TypeError('Expected String');\n    }\n    if (source.length === 0) {\n      return new Uint8Array();\n    }\n    var psz = 0;\n    if (source[psz] === ' ') {\n      return;\n    }\n    var zeroes = 0;\n    var length = 0;\n    while (source[psz] === LEADER) {\n      zeroes++;\n      psz++;\n    }\n    var size = (source.length - psz) * FACTOR + 1 >>> 0;\n    var b256 = new Uint8Array(size);\n    while (source[psz]) {\n      var carry = BASE_MAP[source.charCodeAt(psz)];\n      if (carry === 255) {\n        return;\n      }\n      var i = 0;\n      for (var it3 = size - 1; (carry !== 0 || i < length) && it3 !== -1; it3--, i++) {\n        carry += BASE * b256[it3] >>> 0;\n        b256[it3] = carry % 256 >>> 0;\n        carry = carry / 256 >>> 0;\n      }\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n      length = i;\n      psz++;\n    }\n    if (source[psz] === ' ') {\n      return;\n    }\n    var it4 = size - length;\n    while (it4 !== size && b256[it4] === 0) {\n      it4++;\n    }\n    var vch = new Uint8Array(zeroes + (size - it4));\n    var j = zeroes;\n    while (it4 !== size) {\n      vch[j++] = b256[it4++];\n    }\n    return vch;\n  }\n  function decode(string) {\n    var buffer = decodeUnsafe(string);\n    if (buffer) {\n      return buffer;\n    }\n    throw new Error(`Non-${ name } character`);\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  };\n}\nvar src = base;\nvar _brrp__multiformats_scope_baseX = src;\nexport default _brrp__multiformats_scope_baseX;", "const empty = new Uint8Array(0);\nconst toHex = d => d.reduce((hex, byte) => hex + byte.toString(16).padStart(2, '0'), '');\nconst fromHex = hex => {\n  const hexes = hex.match(/../g);\n  return hexes ? new Uint8Array(hexes.map(b => parseInt(b, 16))) : empty;\n};\nconst equals = (aa, bb) => {\n  if (aa === bb)\n    return true;\n  if (aa.byteLength !== bb.byteLength) {\n    return false;\n  }\n  for (let ii = 0; ii < aa.byteLength; ii++) {\n    if (aa[ii] !== bb[ii]) {\n      return false;\n    }\n  }\n  return true;\n};\nconst coerce = o => {\n  if (o instanceof Uint8Array && o.constructor.name === 'Uint8Array')\n    return o;\n  if (o instanceof ArrayBuffer)\n    return new Uint8Array(o);\n  if (ArrayBuffer.isView(o)) {\n    return new Uint8Array(o.buffer, o.byteOffset, o.byteLength);\n  }\n  throw new Error('Unknown type, must be binary type');\n};\nconst isBinary = o => o instanceof ArrayBuffer || ArrayBuffer.isView(o);\nconst fromString = str => new TextEncoder().encode(str);\nconst toString = b => new TextDecoder().decode(b);\nexport {\n  equals,\n  coerce,\n  isBinary,\n  fromHex,\n  toHex,\n  fromString,\n  toString,\n  empty\n};", "import basex from '../../vendor/base-x.js';\nimport { coerce } from '../bytes.js';\nclass Encoder {\n  constructor(name, prefix, baseEncode) {\n    this.name = name;\n    this.prefix = prefix;\n    this.baseEncode = baseEncode;\n  }\n  encode(bytes) {\n    if (bytes instanceof Uint8Array) {\n      return `${ this.prefix }${ this.baseEncode(bytes) }`;\n    } else {\n      throw Error('Unknown type, must be binary type');\n    }\n  }\n}\nclass Decoder {\n  constructor(name, prefix, baseDecode) {\n    this.name = name;\n    this.prefix = prefix;\n    if (prefix.codePointAt(0) === undefined) {\n      throw new Error('Invalid prefix character');\n    }\n    this.prefixCodePoint = prefix.codePointAt(0);\n    this.baseDecode = baseDecode;\n  }\n  decode(text) {\n    if (typeof text === 'string') {\n      if (text.codePointAt(0) !== this.prefixCodePoint) {\n        throw Error(`Unable to decode multibase string ${ JSON.stringify(text) }, ${ this.name } decoder only supports inputs prefixed with ${ this.prefix }`);\n      }\n      return this.baseDecode(text.slice(this.prefix.length));\n    } else {\n      throw Error('Can only multibase decode strings');\n    }\n  }\n  or(decoder) {\n    return or(this, decoder);\n  }\n}\nclass ComposedDecoder {\n  constructor(decoders) {\n    this.decoders = decoders;\n  }\n  or(decoder) {\n    return or(this, decoder);\n  }\n  decode(input) {\n    const prefix = input[0];\n    const decoder = this.decoders[prefix];\n    if (decoder) {\n      return decoder.decode(input);\n    } else {\n      throw RangeError(`Unable to decode multibase string ${ JSON.stringify(input) }, only inputs prefixed with ${ Object.keys(this.decoders) } are supported`);\n    }\n  }\n}\nexport const or = (left, right) => new ComposedDecoder({\n  ...left.decoders || { [left.prefix]: left },\n  ...right.decoders || { [right.prefix]: right }\n});\nexport class Codec {\n  constructor(name, prefix, baseEncode, baseDecode) {\n    this.name = name;\n    this.prefix = prefix;\n    this.baseEncode = baseEncode;\n    this.baseDecode = baseDecode;\n    this.encoder = new Encoder(name, prefix, baseEncode);\n    this.decoder = new Decoder(name, prefix, baseDecode);\n  }\n  encode(input) {\n    return this.encoder.encode(input);\n  }\n  decode(input) {\n    return this.decoder.decode(input);\n  }\n}\nexport const from = ({name, prefix, encode, decode}) => new Codec(name, prefix, encode, decode);\nexport const baseX = ({prefix, name, alphabet}) => {\n  const {encode, decode} = basex(alphabet, name);\n  return from({\n    prefix,\n    name,\n    encode,\n    decode: text => coerce(decode(text))\n  });\n};\nconst decode = (string, alphabet, bitsPerChar, name) => {\n  const codes = {};\n  for (let i = 0; i < alphabet.length; ++i) {\n    codes[alphabet[i]] = i;\n  }\n  let end = string.length;\n  while (string[end - 1] === '=') {\n    --end;\n  }\n  const out = new Uint8Array(end * bitsPerChar / 8 | 0);\n  let bits = 0;\n  let buffer = 0;\n  let written = 0;\n  for (let i = 0; i < end; ++i) {\n    const value = codes[string[i]];\n    if (value === undefined) {\n      throw new SyntaxError(`Non-${ name } character`);\n    }\n    buffer = buffer << bitsPerChar | value;\n    bits += bitsPerChar;\n    if (bits >= 8) {\n      bits -= 8;\n      out[written++] = 255 & buffer >> bits;\n    }\n  }\n  if (bits >= bitsPerChar || 255 & buffer << 8 - bits) {\n    throw new SyntaxError('Unexpected end of data');\n  }\n  return out;\n};\nconst encode = (data, alphabet, bitsPerChar) => {\n  const pad = alphabet[alphabet.length - 1] === '=';\n  const mask = (1 << bitsPerChar) - 1;\n  let out = '';\n  let bits = 0;\n  let buffer = 0;\n  for (let i = 0; i < data.length; ++i) {\n    buffer = buffer << 8 | data[i];\n    bits += 8;\n    while (bits > bitsPerChar) {\n      bits -= bitsPerChar;\n      out += alphabet[mask & buffer >> bits];\n    }\n  }\n  if (bits) {\n    out += alphabet[mask & buffer << bitsPerChar - bits];\n  }\n  if (pad) {\n    while (out.length * bitsPerChar & 7) {\n      out += '=';\n    }\n  }\n  return out;\n};\nexport const rfc4648 = ({name, prefix, bitsPerChar, alphabet}) => {\n  return from({\n    prefix,\n    name,\n    encode(input) {\n      return encode(input, alphabet, bitsPerChar);\n    },\n    decode(input) {\n      return decode(input, alphabet, bitsPerChar, name);\n    }\n  });\n};", "import { from } from './base.js';\nimport {\n  fromString,\n  toString\n} from '../bytes.js';\nexport const identity = from({\n  prefix: '\\0',\n  name: 'identity',\n  encode: buf => toString(buf),\n  decode: str => fromString(str)\n});", "import { rfc4648 } from './base.js';\nexport const base2 = rfc4648({\n  prefix: '0',\n  name: 'base2',\n  alphabet: '01',\n  bitsPerChar: 1\n});", "import { rfc4648 } from './base.js';\nexport const base8 = rfc4648({\n  prefix: '7',\n  name: 'base8',\n  alphabet: '01234567',\n  bitsPerChar: 3\n});", "import { baseX } from './base.js';\nexport const base10 = baseX({\n  prefix: '9',\n  name: 'base10',\n  alphabet: '0123456789'\n});", "import { rfc4648 } from './base.js';\nexport const base16 = rfc4648({\n  prefix: 'f',\n  name: 'base16',\n  alphabet: '0123456789abcdef',\n  bitsPerChar: 4\n});\nexport const base16upper = rfc4648({\n  prefix: 'F',\n  name: 'base16upper',\n  alphabet: '0123456789ABCDEF',\n  bitsPerChar: 4\n});", "import { rfc4648 } from './base.js';\nexport const base32 = rfc4648({\n  prefix: 'b',\n  name: 'base32',\n  alphabet: 'abcdefghijklmnopqrstuvwxyz234567',\n  bitsPerChar: 5\n});\nexport const base32upper = rfc4648({\n  prefix: 'B',\n  name: 'base32upper',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567',\n  bitsPerChar: 5\n});\nexport const base32pad = rfc4648({\n  prefix: 'c',\n  name: 'base32pad',\n  alphabet: 'abcdefghijklmnopqrstuvwxyz234567=',\n  bitsPerChar: 5\n});\nexport const base32padupper = rfc4648({\n  prefix: 'C',\n  name: 'base32padupper',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=',\n  bitsPerChar: 5\n});\nexport const base32hex = rfc4648({\n  prefix: 'v',\n  name: 'base32hex',\n  alphabet: '0123456789abcdefghijklmnopqrstuv',\n  bitsPerChar: 5\n});\nexport const base32hexupper = rfc4648({\n  prefix: 'V',\n  name: 'base32hexupper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUV',\n  bitsPerChar: 5\n});\nexport const base32hexpad = rfc4648({\n  prefix: 't',\n  name: 'base32hexpad',\n  alphabet: '0123456789abcdefghijklmnopqrstuv=',\n  bitsPerChar: 5\n});\nexport const base32hexpadupper = rfc4648({\n  prefix: 'T',\n  name: 'base32hexpadupper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUV=',\n  bitsPerChar: 5\n});\nexport const base32z = rfc4648({\n  prefix: 'h',\n  name: 'base32z',\n  alphabet: 'ybndrfg8ejkmcpqxot1uwisza345h769',\n  bitsPerChar: 5\n});", "import { baseX } from './base.js';\nexport const base36 = baseX({\n  prefix: 'k',\n  name: 'base36',\n  alphabet: '0123456789abcdefghijklmnopqrstuvwxyz'\n});\nexport const base36upper = baseX({\n  prefix: 'K',\n  name: 'base36upper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'\n});", "import { baseX } from './base.js';\nexport const base58btc = baseX({\n  name: 'base58btc',\n  prefix: 'z',\n  alphabet: '**********************************************************'\n});\nexport const base58flickr = baseX({\n  name: 'base58flickr',\n  prefix: 'Z',\n  alphabet: '**********************************************************'\n});", "import { rfc4648 } from './base.js';\nexport const base64 = rfc4648({\n  prefix: 'm',\n  name: 'base64',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',\n  bitsPerChar: 6\n});\nexport const base64pad = rfc4648({\n  prefix: 'M',\n  name: 'base64pad',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',\n  bitsPerChar: 6\n});\nexport const base64url = rfc4648({\n  prefix: 'u',\n  name: 'base64url',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_',\n  bitsPerChar: 6\n});\nexport const base64urlpad = rfc4648({\n  prefix: 'U',\n  name: 'base64urlpad',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=',\n  bitsPerChar: 6\n});", "import { from } from './base.js';\nconst alphabet = Array.from('\\uD83D\\uDE80\\uD83E\\uDE90\\u2604\\uD83D\\uDEF0\\uD83C\\uDF0C\\uD83C\\uDF11\\uD83C\\uDF12\\uD83C\\uDF13\\uD83C\\uDF14\\uD83C\\uDF15\\uD83C\\uDF16\\uD83C\\uDF17\\uD83C\\uDF18\\uD83C\\uDF0D\\uD83C\\uDF0F\\uD83C\\uDF0E\\uD83D\\uDC09\\u2600\\uD83D\\uDCBB\\uD83D\\uDDA5\\uD83D\\uDCBE\\uD83D\\uDCBF\\uD83D\\uDE02\\u2764\\uD83D\\uDE0D\\uD83E\\uDD23\\uD83D\\uDE0A\\uD83D\\uDE4F\\uD83D\\uDC95\\uD83D\\uDE2D\\uD83D\\uDE18\\uD83D\\uDC4D\\uD83D\\uDE05\\uD83D\\uDC4F\\uD83D\\uDE01\\uD83D\\uDD25\\uD83E\\uDD70\\uD83D\\uDC94\\uD83D\\uDC96\\uD83D\\uDC99\\uD83D\\uDE22\\uD83E\\uDD14\\uD83D\\uDE06\\uD83D\\uDE44\\uD83D\\uDCAA\\uD83D\\uDE09\\u263A\\uD83D\\uDC4C\\uD83E\\uDD17\\uD83D\\uDC9C\\uD83D\\uDE14\\uD83D\\uDE0E\\uD83D\\uDE07\\uD83C\\uDF39\\uD83E\\uDD26\\uD83C\\uDF89\\uD83D\\uDC9E\\u270C\\u2728\\uD83E\\uDD37\\uD83D\\uDE31\\uD83D\\uDE0C\\uD83C\\uDF38\\uD83D\\uDE4C\\uD83D\\uDE0B\\uD83D\\uDC97\\uD83D\\uDC9A\\uD83D\\uDE0F\\uD83D\\uDC9B\\uD83D\\uDE42\\uD83D\\uDC93\\uD83E\\uDD29\\uD83D\\uDE04\\uD83D\\uDE00\\uD83D\\uDDA4\\uD83D\\uDE03\\uD83D\\uDCAF\\uD83D\\uDE48\\uD83D\\uDC47\\uD83C\\uDFB6\\uD83D\\uDE12\\uD83E\\uDD2D\\u2763\\uD83D\\uDE1C\\uD83D\\uDC8B\\uD83D\\uDC40\\uD83D\\uDE2A\\uD83D\\uDE11\\uD83D\\uDCA5\\uD83D\\uDE4B\\uD83D\\uDE1E\\uD83D\\uDE29\\uD83D\\uDE21\\uD83E\\uDD2A\\uD83D\\uDC4A\\uD83E\\uDD73\\uD83D\\uDE25\\uD83E\\uDD24\\uD83D\\uDC49\\uD83D\\uDC83\\uD83D\\uDE33\\u270B\\uD83D\\uDE1A\\uD83D\\uDE1D\\uD83D\\uDE34\\uD83C\\uDF1F\\uD83D\\uDE2C\\uD83D\\uDE43\\uD83C\\uDF40\\uD83C\\uDF37\\uD83D\\uDE3B\\uD83D\\uDE13\\u2B50\\u2705\\uD83E\\uDD7A\\uD83C\\uDF08\\uD83D\\uDE08\\uD83E\\uDD18\\uD83D\\uDCA6\\u2714\\uD83D\\uDE23\\uD83C\\uDFC3\\uD83D\\uDC90\\u2639\\uD83C\\uDF8A\\uD83D\\uDC98\\uD83D\\uDE20\\u261D\\uD83D\\uDE15\\uD83C\\uDF3A\\uD83C\\uDF82\\uD83C\\uDF3B\\uD83D\\uDE10\\uD83D\\uDD95\\uD83D\\uDC9D\\uD83D\\uDE4A\\uD83D\\uDE39\\uD83D\\uDDE3\\uD83D\\uDCAB\\uD83D\\uDC80\\uD83D\\uDC51\\uD83C\\uDFB5\\uD83E\\uDD1E\\uD83D\\uDE1B\\uD83D\\uDD34\\uD83D\\uDE24\\uD83C\\uDF3C\\uD83D\\uDE2B\\u26BD\\uD83E\\uDD19\\u2615\\uD83C\\uDFC6\\uD83E\\uDD2B\\uD83D\\uDC48\\uD83D\\uDE2E\\uD83D\\uDE46\\uD83C\\uDF7B\\uD83C\\uDF43\\uD83D\\uDC36\\uD83D\\uDC81\\uD83D\\uDE32\\uD83C\\uDF3F\\uD83E\\uDDE1\\uD83C\\uDF81\\u26A1\\uD83C\\uDF1E\\uD83C\\uDF88\\u274C\\u270A\\uD83D\\uDC4B\\uD83D\\uDE30\\uD83E\\uDD28\\uD83D\\uDE36\\uD83E\\uDD1D\\uD83D\\uDEB6\\uD83D\\uDCB0\\uD83C\\uDF53\\uD83D\\uDCA2\\uD83E\\uDD1F\\uD83D\\uDE41\\uD83D\\uDEA8\\uD83D\\uDCA8\\uD83E\\uDD2C\\u2708\\uD83C\\uDF80\\uD83C\\uDF7A\\uD83E\\uDD13\\uD83D\\uDE19\\uD83D\\uDC9F\\uD83C\\uDF31\\uD83D\\uDE16\\uD83D\\uDC76\\uD83E\\uDD74\\u25B6\\u27A1\\u2753\\uD83D\\uDC8E\\uD83D\\uDCB8\\u2B07\\uD83D\\uDE28\\uD83C\\uDF1A\\uD83E\\uDD8B\\uD83D\\uDE37\\uD83D\\uDD7A\\u26A0\\uD83D\\uDE45\\uD83D\\uDE1F\\uD83D\\uDE35\\uD83D\\uDC4E\\uD83E\\uDD32\\uD83E\\uDD20\\uD83E\\uDD27\\uD83D\\uDCCC\\uD83D\\uDD35\\uD83D\\uDC85\\uD83E\\uDDD0\\uD83D\\uDC3E\\uD83C\\uDF52\\uD83D\\uDE17\\uD83E\\uDD11\\uD83C\\uDF0A\\uD83E\\uDD2F\\uD83D\\uDC37\\u260E\\uD83D\\uDCA7\\uD83D\\uDE2F\\uD83D\\uDC86\\uD83D\\uDC46\\uD83C\\uDFA4\\uD83D\\uDE47\\uD83C\\uDF51\\u2744\\uD83C\\uDF34\\uD83D\\uDCA3\\uD83D\\uDC38\\uD83D\\uDC8C\\uD83D\\uDCCD\\uD83E\\uDD40\\uD83E\\uDD22\\uD83D\\uDC45\\uD83D\\uDCA1\\uD83D\\uDCA9\\uD83D\\uDC50\\uD83D\\uDCF8\\uD83D\\uDC7B\\uD83E\\uDD10\\uD83E\\uDD2E\\uD83C\\uDFBC\\uD83E\\uDD75\\uD83D\\uDEA9\\uD83C\\uDF4E\\uD83C\\uDF4A\\uD83D\\uDC7C\\uD83D\\uDC8D\\uD83D\\uDCE3\\uD83E\\uDD42');\nconst alphabetBytesToChars = alphabet.reduce((p, c, i) => {\n  p[i] = c;\n  return p;\n}, []);\nconst alphabetCharsToBytes = alphabet.reduce((p, c, i) => {\n  p[c.codePointAt(0)] = i;\n  return p;\n}, []);\nfunction encode(data) {\n  return data.reduce((p, c) => {\n    p += alphabetBytesToChars[c];\n    return p;\n  }, '');\n}\nfunction decode(str) {\n  const byts = [];\n  for (const char of str) {\n    const byt = alphabetCharsToBytes[char.codePointAt(0)];\n    if (byt === undefined) {\n      throw new Error(`Non-base256emoji character: ${ char }`);\n    }\n    byts.push(byt);\n  }\n  return new Uint8Array(byts);\n}\nexport const base256emoji = from({\n  prefix: '\\uD83D\\uDE80',\n  name: 'base256emoji',\n  encode,\n  decode\n});", "var encode_1 = encode;\nvar MSB = 128, REST = 127, MSBALL = ~REST, INT = Math.pow(2, 31);\nfunction encode(num, out, offset) {\n  out = out || [];\n  offset = offset || 0;\n  var oldOffset = offset;\n  while (num >= INT) {\n    out[offset++] = num & 255 | MSB;\n    num /= 128;\n  }\n  while (num & MSBALL) {\n    out[offset++] = num & 255 | MSB;\n    num >>>= 7;\n  }\n  out[offset] = num | 0;\n  encode.bytes = offset - oldOffset + 1;\n  return out;\n}\nvar decode = read;\nvar MSB$1 = 128, REST$1 = 127;\nfunction read(buf, offset) {\n  var res = 0, offset = offset || 0, shift = 0, counter = offset, b, l = buf.length;\n  do {\n    if (counter >= l) {\n      read.bytes = 0;\n      throw new RangeError('Could not decode varint');\n    }\n    b = buf[counter++];\n    res += shift < 28 ? (b & REST$1) << shift : (b & REST$1) * Math.pow(2, shift);\n    shift += 7;\n  } while (b >= MSB$1);\n  read.bytes = counter - offset;\n  return res;\n}\nvar N1 = Math.pow(2, 7);\nvar N2 = Math.pow(2, 14);\nvar N3 = Math.pow(2, 21);\nvar N4 = Math.pow(2, 28);\nvar N5 = Math.pow(2, 35);\nvar N6 = Math.pow(2, 42);\nvar N7 = Math.pow(2, 49);\nvar N8 = Math.pow(2, 56);\nvar N9 = Math.pow(2, 63);\nvar length = function (value) {\n  return value < N1 ? 1 : value < N2 ? 2 : value < N3 ? 3 : value < N4 ? 4 : value < N5 ? 5 : value < N6 ? 6 : value < N7 ? 7 : value < N8 ? 8 : value < N9 ? 9 : 10;\n};\nvar varint = {\n  encode: encode_1,\n  decode: decode,\n  encodingLength: length\n};\nvar _brrp_varint = varint;\nexport default _brrp_varint;", "import varint from '../vendor/varint.js';\nexport const decode = (data, offset = 0) => {\n  const code = varint.decode(data, offset);\n  return [\n    code,\n    varint.decode.bytes\n  ];\n};\nexport const encodeTo = (int, target, offset = 0) => {\n  varint.encode(int, target, offset);\n  return target;\n};\nexport const encodingLength = int => {\n  return varint.encodingLength(int);\n};", "import {\n  coerce,\n  equals as equalBytes\n} from '../bytes.js';\nimport * as varint from '../varint.js';\nexport const create = (code, digest) => {\n  const size = digest.byteLength;\n  const sizeOffset = varint.encodingLength(code);\n  const digestOffset = sizeOffset + varint.encodingLength(size);\n  const bytes = new Uint8Array(digestOffset + size);\n  varint.encodeTo(code, bytes, 0);\n  varint.encodeTo(size, bytes, sizeOffset);\n  bytes.set(digest, digestOffset);\n  return new Digest(code, size, digest, bytes);\n};\nexport const decode = multihash => {\n  const bytes = coerce(multihash);\n  const [code, sizeOffset] = varint.decode(bytes);\n  const [size, digestOffset] = varint.decode(bytes.subarray(sizeOffset));\n  const digest = bytes.subarray(sizeOffset + digestOffset);\n  if (digest.byteLength !== size) {\n    throw new Error('Incorrect length');\n  }\n  return new Digest(code, size, digest, bytes);\n};\nexport const equals = (a, b) => {\n  if (a === b) {\n    return true;\n  } else {\n    return a.code === b.code && a.size === b.size && equalBytes(a.bytes, b.bytes);\n  }\n};\nexport class Digest {\n  constructor(code, size, digest, bytes) {\n    this.code = code;\n    this.size = size;\n    this.digest = digest;\n    this.bytes = bytes;\n  }\n}", "import * as Digest from './digest.js';\nexport const from = ({name, code, encode}) => new Hasher(name, code, encode);\nexport class Hasher {\n  constructor(name, code, encode) {\n    this.name = name;\n    this.code = code;\n    this.encode = encode;\n  }\n  digest(input) {\n    if (input instanceof Uint8Array) {\n      const result = this.encode(input);\n      return result instanceof Uint8Array ? Digest.create(this.code, result) : result.then(digest => Digest.create(this.code, digest));\n    } else {\n      throw Error('Unknown type, must be binary type');\n    }\n  }\n}", "import { from } from './hasher.js';\nconst sha = name => async data => new Uint8Array(await crypto.subtle.digest(name, data));\nexport const sha256 = from({\n  name: 'sha2-256',\n  code: 18,\n  encode: sha('SHA-256')\n});\nexport const sha512 = from({\n  name: 'sha2-512',\n  code: 19,\n  encode: sha('SHA-512')\n});", "import { coerce } from '../bytes.js';\nimport * as Digest from './digest.js';\nconst code = 0;\nconst name = 'identity';\nconst encode = coerce;\nconst digest = input => Digest.create(code, encode(input));\nexport const identity = {\n  code,\n  name,\n  encode,\n  digest\n};", "const textEncoder = new TextEncoder();\nconst textDecoder = new TextDecoder();\nexport const name = 'json';\nexport const code = 512;\nexport const encode = node => textEncoder.encode(JSON.stringify(node));\nexport const decode = data => JSON.parse(textDecoder.decode(data));", "import * as identityBase from './bases/identity.js';\nimport * as base2 from './bases/base2.js';\nimport * as base8 from './bases/base8.js';\nimport * as base10 from './bases/base10.js';\nimport * as base16 from './bases/base16.js';\nimport * as base32 from './bases/base32.js';\nimport * as base36 from './bases/base36.js';\nimport * as base58 from './bases/base58.js';\nimport * as base64 from './bases/base64.js';\nimport * as base256emoji from './bases/base256emoji.js';\nimport * as sha2 from './hashes/sha2.js';\nimport * as identity from './hashes/identity.js';\nimport * as raw from './codecs/raw.js';\nimport * as json from './codecs/json.js';\nimport {\n  CID,\n  hasher,\n  digest,\n  varint,\n  bytes\n} from './index.js';\nconst bases = {\n  ...identityBase,\n  ...base2,\n  ...base8,\n  ...base10,\n  ...base16,\n  ...base32,\n  ...base36,\n  ...base58,\n  ...base64,\n  ...base256emoji\n};\nconst hashes = {\n  ...sha2,\n  ...identity\n};\nconst codecs = {\n  raw,\n  json\n};\nexport {\n  CID,\n  hasher,\n  digest,\n  varint,\n  bytes,\n  hashes,\n  bases,\n  codecs\n};", "import { bases } from 'multiformats/basics';\nimport { allocUnsafe } from '../alloc.js';\nfunction createCodec(name, prefix, encode, decode) {\n  return {\n    name,\n    prefix,\n    encoder: {\n      name,\n      prefix,\n      encode\n    },\n    decoder: { decode }\n  };\n}\nconst string = createCodec('utf8', 'u', buf => {\n  const decoder = new TextDecoder('utf8');\n  return 'u' + decoder.decode(buf);\n}, str => {\n  const encoder = new TextEncoder();\n  return encoder.encode(str.substring(1));\n});\nconst ascii = createCodec('ascii', 'a', buf => {\n  let string = 'a';\n  for (let i = 0; i < buf.length; i++) {\n    string += String.fromCharCode(buf[i]);\n  }\n  return string;\n}, str => {\n  str = str.substring(1);\n  const buf = allocUnsafe(str.length);\n  for (let i = 0; i < str.length; i++) {\n    buf[i] = str.charCodeAt(i);\n  }\n  return buf;\n});\nconst BASES = {\n  utf8: string,\n  'utf-8': string,\n  hex: bases.base16,\n  latin1: ascii,\n  ascii: ascii,\n  binary: ascii,\n  ...bases\n};\nexport default BASES;", "import bases from './util/bases.js';\nexport function toString(array, encoding = 'utf8') {\n  const base = bases[encoding];\n  if (!base) {\n    throw new Error(`Unsupported encoding \"${ encoding }\"`);\n  }\n  if ((encoding === 'utf8' || encoding === 'utf-8') && globalThis.Buffer != null && globalThis.Buffer.from != null) {\n    return globalThis.Buffer.from(array.buffer, array.byteOffset, array.byteLength).toString('utf8');\n  }\n  return base.encoder.encode(array).substring(1);\n}", "import bases from './util/bases.js';\nimport { asUint8Array } from './util/as-uint8array.js';\nexport function fromString(string, encoding = 'utf8') {\n  const base = bases[encoding];\n  if (!base) {\n    throw new Error(`Unsupported encoding \"${ encoding }\"`);\n  }\n  if ((encoding === 'utf8' || encoding === 'utf-8') && globalThis.Buffer != null && globalThis.Buffer.from != null) {\n    return asUint8Array(globalThis.Buffer.from(string, 'utf-8'));\n  }\n  return base.decoder.decode(`${ base.prefix }${ string }`);\n}", "import { concat } from \"uint8arrays/concat\";\nimport { toString } from \"uint8arrays/to-string\";\nimport { fromString } from \"uint8arrays/from-string\";\nimport { safeJsonParse, safeJsonStringify } from \"@walletconnect/safe-json\";\n\nimport {\n  DATA_ENCODING,\n  DID_DELIMITER,\n  DID_METHOD,\n  DID_PREFIX,\n  JSON_ENCODING,\n  JWT_DELIMITER,\n  JWT_ENCODING,\n  MULTICODEC_ED25519_BASE,\n  MULTICODEC_ED25519_ENCODING,\n  MULTICODEC_ED25519_HEADER,\n  MULTICODEC_ED25519_LENGTH,\n} from \"./constants\";\nimport { IridiumJWTData, IridiumJWTDecoded, IridiumJWTSigned } from \"./types\";\n\n// ---------- JSON ----------------------------------------------- //\n\nexport function decodeJSON(str: string): any {\n  return safeJsonParse(toString(fromString(str, JWT_ENCODING), JSON_ENCODING));\n}\n\nexport function encodeJSON(val: any): string {\n  return toString(fromString(safeJsonStringify(val), JSON_ENCODING), JWT_ENCODING);\n}\n\n// ---------- Issuer ----------------------------------------------- //\n\nexport function encodeIss(publicKey: Uint8Array): string {\n  const header = fromString(MULTICODEC_ED25519_HEADER, MULTICODEC_ED25519_ENCODING);\n  const multicodec =\n    MULTICODEC_ED25519_BASE + toString(concat([header, publicKey]), MULTICODEC_ED25519_ENCODING);\n  return [DID_PREFIX, DID_METHOD, multicodec].join(DID_DELIMITER);\n}\n\nexport function decodeIss(issuer: string): Uint8Array {\n  const [prefix, method, multicodec] = issuer.split(DID_DELIMITER);\n  if (prefix !== DID_PREFIX || method !== DID_METHOD) {\n    throw new Error(`Issuer must be a DID with method \"key\"`);\n  }\n  const base = multicodec.slice(0, 1);\n  if (base !== MULTICODEC_ED25519_BASE) {\n    throw new Error(`Issuer must be a key in mulicodec format`);\n  }\n  const bytes = fromString(multicodec.slice(1), MULTICODEC_ED25519_ENCODING);\n  const type = toString(bytes.slice(0, 2), MULTICODEC_ED25519_ENCODING);\n  if (type !== MULTICODEC_ED25519_HEADER) {\n    throw new Error(`Issuer must be a public key with type \"Ed25519\"`);\n  }\n  const publicKey = bytes.slice(2);\n  if (publicKey.length !== MULTICODEC_ED25519_LENGTH) {\n    throw new Error(`Issuer must be a public key with length 32 bytes`);\n  }\n  return publicKey;\n}\n\n// ---------- Signature ----------------------------------------------- //\n\nexport function encodeSig(bytes: Uint8Array): string {\n  return toString(bytes, JWT_ENCODING);\n}\n\nexport function decodeSig(encoded: string): Uint8Array {\n  return fromString(encoded, JWT_ENCODING);\n}\n\n// ---------- Data ----------------------------------------------- //\n\nexport function encodeData(params: IridiumJWTData): Uint8Array {\n  return fromString(\n    [encodeJSON(params.header), encodeJSON(params.payload)].join(JWT_DELIMITER),\n    DATA_ENCODING,\n  );\n}\n\nexport function decodeData(data: Uint8Array): IridiumJWTData {\n  const params = toString(data, DATA_ENCODING).split(JWT_DELIMITER);\n  const header = decodeJSON(params[0]);\n  const payload = decodeJSON(params[1]);\n  return { header, payload };\n}\n\n// ---------- JWT ----------------------------------------------- //\n\nexport function encodeJWT(params: IridiumJWTSigned): string {\n  return [encodeJSON(params.header), encodeJSON(params.payload), encodeSig(params.signature)].join(\n    JWT_DELIMITER,\n  );\n}\n\nexport function decodeJWT(jwt: string): IridiumJWTDecoded {\n  const params = jwt.split(JWT_DELIMITER);\n  const header = decodeJSON(params[0]);\n  const payload = decodeJSON(params[1]);\n  const signature = decodeSig(params[2]);\n  const data = fromString(params.slice(0, 2).join(JWT_DELIMITER), DATA_ENCODING);\n  return { header, payload, signature, data };\n}\n", "import { ed25519 } from \"@noble/curves/ed25519\";\nimport { randomBytes } from \"@noble/hashes/utils\";\nimport { fromMiliseconds } from \"@walletconnect/time\";\nimport { JWT_IRIDIUM_ALG, JWT_IRIDIUM_TYP, KEY_PAIR_SEED_LENGTH } from \"./constants\";\n\nimport { decodeIss, decodeJWT, encodeData, encodeIss, encodeJWT } from \"./utils\";\nimport { concat } from \"uint8arrays/concat\";\n\nexport interface Keypair {\n  secretKey: Uint8Array;\n  publicKey: Uint8Array;\n}\n\nexport function generateKeyPair(seed: Uint8Array = randomBytes(KEY_PAIR_SEED_LENGTH)): Keypair {\n  const publicKey = ed25519.getPublicKey(seed);\n  const secretKey = concat([seed, publicKey]);\n  return {\n    secretKey,\n    publicKey,\n  };\n}\n\nexport async function signJWT(\n  sub: string,\n  aud: string,\n  ttl: number,\n  keyPair: Keypair,\n  iat: number = fromMiliseconds(Date.now()),\n) {\n  const header = { alg: JWT_IRIDIUM_ALG, typ: JWT_IRIDIUM_TYP };\n  const iss = encodeIss(keyPair.publicKey);\n  const exp = iat + ttl;\n  const payload = { iss, sub, aud, iat, exp };\n  const data = encodeData({ header, payload });\n  const signature = ed25519.sign(data, keyPair.secretKey.slice(0, 32));\n  return encodeJWT({ header, payload, signature });\n}\n\nexport async function verifyJWT(jwt: string) {\n  const { header, payload, data, signature } = decodeJWT(jwt);\n  if (header.alg !== JWT_IRIDIUM_ALG || header.typ !== JWT_IRIDIUM_TYP) {\n    throw new Error(\"JWT must use EdDSA algorithm\");\n  }\n  const publicKey = decodeIss(payload.iss);\n  return ed25519.verify(signature, data, publicKey);\n}\n"], "names": ["isBytes", "abytes", "crypto", "sha512", "_0n", "_1n", "_2n", "_5n", "_8n", "ut.validateObject", "ut.aInRange", "ut.bytesToNumberLE", "ut.numberToBytesLE", "ut.bytesToHex", "ut.concatBytes", "fromString", "toString", "from", "basex", "decode", "encode", "identity", "varint", "varint.encoding<PERSON>ength", "varint.encodeTo", "Digest.create", "base2", "base8", "base10", "base16", "base32", "base36", "base64", "base256emoji", "bases", "str", "safeJsonParse", "JWT_ENCODING", "JSON_ENCODING", "val", "safeJsonStringify", "public<PERSON>ey", "header", "MULTICODEC_ED25519_HEADER", "MULTICODEC_ED25519_ENCODING", "multicodec", "MULTICODEC_ED25519_BASE", "concat", "DID_PREFIX", "DID_METHOD", "DID_DELIMITER", "issuer", "prefix", "method", "bytes", "MULTICODEC_ED25519_LENGTH", "encoded", "params", "JWT_DELIMITER", "DATA_ENCODING", "data", "payload", "jwt", "signature", "seed", "randomBytes", "KEY_PAIR_SEED_LENGTH", "ed25519", "sub", "aud", "ttl", "keyPair", "iat", "fromMiliseconds", "JWT_IRIDIUM_ALG", "JWT_IRIDIUM_TYP", "iss", "encodeIss", "exp", "encodeData", "encodeJWT", "decodeJWT", "decodeIss"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AAKA;AACA,SAASA,SAAO,CAAC,CAAC,EAAE;AACpB,IAAI,OAAO,CAAC,YAAY,UAAU,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC;AACrG,CAAC;AACD,SAASC,QAAM,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE;AAC/B,IAAI,IAAI,CAACD,SAAO,CAAC,CAAC,CAAC;AACnB,QAAQ,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAC/C,IAAI,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;AACzD,QAAQ,MAAM,IAAI,KAAK,CAAC,gCAAgC,GAAG,OAAO,GAAG,eAAe,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;AACjG,CAAC;AAOD,SAAS,OAAO,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAI,EAAE;AACjD,IAAI,IAAI,QAAQ,CAAC,SAAS;AAC1B,QAAQ,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AAC5D,IAAI,IAAI,aAAa,IAAI,QAAQ,CAAC,QAAQ;AAC1C,QAAQ,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AACjE,CAAC;AACD,SAAS,OAAO,CAAC,GAAG,EAAE,QAAQ,EAAE;AAChC,IAAIC,QAAM,CAAC,GAAG,CAAC,CAAC;AAChB,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC;AACnC,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;AAC1B,QAAQ,MAAM,IAAI,KAAK,CAAC,wDAAwD,GAAG,GAAG,CAAC,CAAC;AACxF,KAAK;AACL;;ACpCO,MAAMC,QAAM,GAAG,OAAO,UAAU,KAAK,QAAQ,IAAI,QAAQ,IAAI,UAAU,GAAG,UAAU,CAAC,MAAM,GAAG,SAAS;;ACA9G;AAqBA;AACO,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AAwF5F;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,GAAG,EAAE;AACjC,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,mCAAmC,GAAG,OAAO,GAAG,CAAC,CAAC;AAC1E,IAAI,OAAO,IAAI,UAAU,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,IAAI,EAAE;AAC9B,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ;AAChC,QAAQ,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AACjC,IAAID,QAAM,CAAC,IAAI,CAAC,CAAC;AACjB,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AAmBD;AACO,MAAM,IAAI,CAAC;AAClB;AACA,IAAI,KAAK,GAAG;AACZ,QAAQ,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;AACjC,KAAK;AACL,CAAC;AAOM,SAAS,eAAe,CAAC,QAAQ,EAAE;AAC1C,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AACpE,IAAI,MAAM,GAAG,GAAG,QAAQ,EAAE,CAAC;AAC3B,IAAI,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;AACpC,IAAI,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AAClC,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,QAAQ,EAAE,CAAC;AACpC,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AAiBD;AACA;AACA;AACO,SAAS,WAAW,CAAC,WAAW,GAAG,EAAE,EAAE;AAC9C,IAAI,IAAIC,QAAM,IAAI,OAAOA,QAAM,CAAC,eAAe,KAAK,UAAU,EAAE;AAChE,QAAQ,OAAOA,QAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;AACnE,KAAK;AACL;AACA,IAAI,IAAIA,QAAM,IAAI,OAAOA,QAAM,CAAC,WAAW,KAAK,UAAU,EAAE;AAC5D,QAAQ,OAAOA,QAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC9D;;ACnMA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;AAC5D,IAAI,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,UAAU;AAC/C,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC1D,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AAC5B,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;AACxC,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,IAAI,IAAI,QAAQ,CAAC,CAAC;AAClD,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC;AACxC,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC7C,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC7C,CAAC;AASD;AACA;AACA;AACA;AACO,MAAM,MAAM,SAAS,IAAI,CAAC;AACjC,IAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;AACtD,QAAQ,KAAK,EAAE,CAAC;AAChB,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC9B,QAAQ,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACxB,QAAQ,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACrB,QAAQ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC/B,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC/C,QAAQ,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,MAAM,CAAC,IAAI,EAAE;AACjB,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC;AACtB,QAAQ,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;AAChD,QAAQ,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;AAChC,QAAQ,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AACtC,YAAY,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;AAClE;AACA,YAAY,IAAI,IAAI,KAAK,QAAQ,EAAE;AACnC,gBAAgB,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AAClD,gBAAgB,OAAO,QAAQ,IAAI,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI,QAAQ;AAC7D,oBAAoB,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AAChD,gBAAgB,SAAS;AACzB,aAAa;AACb,YAAY,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACjE,YAAY,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;AAC7B,YAAY,GAAG,IAAI,IAAI,CAAC;AACxB,YAAY,IAAI,IAAI,CAAC,GAAG,KAAK,QAAQ,EAAE;AACvC,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACtC,gBAAgB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AAC7B,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1B,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,UAAU,CAAC,GAAG,EAAE;AACpB,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC;AACtB,QAAQ,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B;AACA;AACA;AACA,QAAQ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AACtD,QAAQ,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC3B;AACA,QAAQ,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC;AACnC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1C;AACA;AACA,QAAQ,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,GAAG,EAAE;AAC7C,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAClC,YAAY,GAAG,GAAG,CAAC,CAAC;AACpB,SAAS;AACT;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE;AAC3C,YAAY,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1B;AACA;AACA;AACA,QAAQ,YAAY,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACxE,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC9B,QAAQ,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AACtC,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC;AACA,QAAQ,IAAI,GAAG,GAAG,CAAC;AACnB,YAAY,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAC3E,QAAQ,MAAM,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC;AAC/B,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACjC,QAAQ,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM;AACjC,YAAY,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAClE,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE;AACvC,YAAY,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,MAAM,GAAG;AACb,QAAQ,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;AAC3C,QAAQ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAChC,QAAQ,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAC/C,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;AACvB,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL,IAAI,UAAU,CAAC,EAAE,EAAE;AACnB,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAC5C,QAAQ,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9B,QAAQ,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC5E,QAAQ,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;AAC3B,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC;AACrB,QAAQ,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC/B,QAAQ,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;AACjC,QAAQ,IAAI,MAAM,GAAG,QAAQ;AAC7B,YAAY,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClC,QAAQ,OAAO,EAAE,CAAC;AAClB,KAAK;AACL;;AClIA,MAAM,UAAU,mBAAmB,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AACvD,MAAM,IAAI,mBAAmB,MAAM,CAAC,EAAE,CAAC,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,EAAE;AAChC,IAAI,IAAI,EAAE;AACV,QAAQ,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,UAAU,CAAC,EAAE,CAAC;AAClF,IAAI,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;AACtF,CAAC;AACD,SAAS,KAAK,CAAC,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE;AAChC,IAAI,IAAI,EAAE,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACzC,IAAI,IAAI,EAAE,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACzC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,QAAQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC7C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpB,CAAC;AACD,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACpE;AACA,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpC,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACvD;AACA,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACxD;AACA,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC/D,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/D;AACA,MAAM,OAAO,GAAG,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;AAC7B,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AAC7B;AACA,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD;AACA,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/D,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/D;AACA;AACA,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAC7B,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AACtC,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AAChE,CAAC;AACD;AACA,MAAM,KAAK,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AACnE,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC9E,MAAM,KAAK,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AACpF,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvF,MAAM,KAAK,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AACrG,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAGhG;AACA,MAAM,GAAG,GAAG;AACZ,IAAI,OAAO,EAAE,KAAK,EAAE,KAAK;AACzB,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;AAClC,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;AAClC,IAAI,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AACjD,CAAC;;AC5DD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC;AAChE,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAC1B;AACA,MAAM,UAAU,mBAAmB,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;AACvD,MAAM,UAAU,mBAAmB,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;AAChD,MAAM,MAAM,SAAS,MAAM,CAAC;AACnC,IAAI,WAAW,GAAG;AAClB,QAAQ,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;AAClC;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,KAAK;AACL;AACA,IAAI,GAAG,GAAG;AACV,QAAQ,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACxF,QAAQ,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAChF,KAAK;AACL;AACA,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACxE,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE;AAC1B;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,EAAE;AAClD,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACnD,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC;AAC1D,SAAS;AACT,QAAQ,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACtC;AACA,YAAY,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;AAChD,YAAY,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;AAChD,YAAY,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACzG,YAAY,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACzG;AACA,YAAY,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9C,YAAY,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9C,YAAY,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACrG,YAAY,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACrG;AACA,YAAY,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACpF,YAAY,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC1F,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;AACrC,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;AACrC,SAAS;AACT,QAAQ,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACtF;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACrC;AACA,YAAY,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACrG,YAAY,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACrG;AACA,YAAY,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAChD,YAAY,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAChD;AACA;AACA,YAAY,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,YAAY,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACxF,YAAY,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;AACjC;AACA,YAAY,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACrG,YAAY,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACrG,YAAY,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;AAC3D,YAAY,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;AAC3D,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE;AAC3E,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACtD,YAAY,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACpD,YAAY,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,SAAS;AACT;AACA,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACjF,KAAK;AACL,IAAI,UAAU,GAAG;AACjB,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3B,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3B,KAAK;AACL,IAAI,OAAO,GAAG;AACd,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACjE,KAAK;AACL,CAAC;AAsED;AACO,MAAMC,QAAM,mBAAmB,eAAe,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC;;AC1OzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAG,mBAAmB,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,MAAMC,KAAG,mBAAmB,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,MAAMC,KAAG,mBAAmB,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B,SAAS,OAAO,CAAC,CAAC,EAAE;AAC3B,IAAI,OAAO,CAAC,YAAY,UAAU,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC;AACrG,CAAC;AACM,SAAS,MAAM,CAAC,IAAI,EAAE;AAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AACtB,QAAQ,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAC/C,CAAC;AACM,SAAS,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE;AACpC,IAAI,IAAI,OAAO,KAAK,KAAK,SAAS;AAClC,QAAQ,MAAM,IAAI,KAAK,CAAC,KAAK,GAAG,yBAAyB,GAAG,KAAK,CAAC,CAAC;AACnE,CAAC;AACD;AACA,MAAM,KAAK,mBAAmB,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACrG;AACA;AACA;AACO,SAAS,UAAU,CAAC,KAAK,EAAE;AAClC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;AAClB;AACA,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;AACjB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AAKM,SAAS,WAAW,CAAC,GAAG,EAAE;AACjC,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,2BAA2B,GAAG,OAAO,GAAG,CAAC,CAAC;AAClE,IAAI,OAAO,GAAG,KAAK,EAAE,GAAGF,KAAG,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AACjD,CAAC;AACD;AACA,MAAM,MAAM,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AAC/D,SAAS,aAAa,CAAC,EAAE,EAAE;AAC3B,IAAI,IAAI,EAAE,IAAI,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,MAAM,CAAC,EAAE;AAC1C,QAAQ,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAC9B,IAAI,IAAI,EAAE,IAAI,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,MAAM,CAAC,CAAC;AACxC,QAAQ,OAAO,EAAE,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACpC,IAAI,IAAI,EAAE,IAAI,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,MAAM,CAAC,CAAC;AACxC,QAAQ,OAAO,EAAE,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACpC,IAAI,OAAO;AACX,CAAC;AACD;AACA;AACA;AACO,SAAS,UAAU,CAAC,GAAG,EAAE;AAChC,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,2BAA2B,GAAG,OAAO,GAAG,CAAC,CAAC;AAClE,IAAI,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;AAC1B,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACtB,IAAI,IAAI,EAAE,GAAG,CAAC;AACd,QAAQ,MAAM,IAAI,KAAK,CAAC,kDAAkD,GAAG,EAAE,CAAC,CAAC;AACjF,IAAI,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;AACrC,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE;AACrD,QAAQ,MAAM,EAAE,GAAG,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;AACrD,QAAQ,MAAM,EAAE,GAAG,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,QAAQ,IAAI,EAAE,KAAK,SAAS,IAAI,EAAE,KAAK,SAAS,EAAE;AAClD,YAAY,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC/C,YAAY,MAAM,IAAI,KAAK,CAAC,8CAA8C,GAAG,IAAI,GAAG,aAAa,GAAG,EAAE,CAAC,CAAC;AACxG,SAAS;AACT,QAAQ,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD;AACO,SAAS,eAAe,CAAC,KAAK,EAAE;AACvC,IAAI,OAAO,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1C,CAAC;AACM,SAAS,eAAe,CAAC,KAAK,EAAE;AACvC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;AAClB,IAAI,OAAO,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AACrE,CAAC;AACM,SAAS,eAAe,CAAC,CAAC,EAAE,GAAG,EAAE;AACxC,IAAI,OAAO,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC7D,CAAC;AACM,SAAS,eAAe,CAAC,CAAC,EAAE,GAAG,EAAE;AACxC,IAAI,OAAO,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;AAC7C,CAAC;AAKD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,cAAc,EAAE;AACxD,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACjC,QAAQ,IAAI;AACZ,YAAY,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AAClC,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE;AAClB,YAAY,MAAM,IAAI,KAAK,CAAC,KAAK,GAAG,4CAA4C,GAAG,CAAC,CAAC,CAAC;AACtF,SAAS;AACT,KAAK;AACL,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE;AAC3B;AACA;AACA,QAAQ,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC,KAAK;AACL,SAAS;AACT,QAAQ,MAAM,IAAI,KAAK,CAAC,KAAK,GAAG,mCAAmC,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;AAC3B,IAAI,IAAI,OAAO,cAAc,KAAK,QAAQ,IAAI,GAAG,KAAK,cAAc;AACpE,QAAQ,MAAM,IAAI,KAAK,CAAC,KAAK,GAAG,aAAa,GAAG,cAAc,GAAG,iBAAiB,GAAG,GAAG,CAAC,CAAC;AAC1F,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACA;AACA;AACO,SAAS,WAAW,CAAC,GAAG,MAAM,EAAE;AACvC,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAQ,MAAM,CAAC,CAAC,CAAC,CAAC;AAClB,QAAQ,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC;AACxB,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AACpC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrD,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACxB,QAAQ,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC;AACxB,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AAkBD;AACA,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,QAAQ,IAAIA,KAAG,IAAI,CAAC,CAAC;AACnD,SAAS,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE;AACrC,IAAI,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AAChF,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE;AAC7C;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AAC7B,QAAQ,MAAM,IAAI,KAAK,CAAC,iBAAiB,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG,CAAC,CAAC,CAAC;AAClG,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,SAAS,MAAM,CAAC,CAAC,EAAE;AAC1B,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,KAAK,GAAG,GAAG,CAAC,EAAE,CAAC,GAAGA,KAAG,EAAE,CAAC,KAAKC,KAAG,EAAE,GAAG,IAAI,CAAC;AAC9C,QAAQ,CAAC;AACT,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AAeD;AACA;AACA;AACA;AACO,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAACC,KAAG,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAID,KAAG,CAAC;AA8D3D;AACA,MAAM,YAAY,GAAG;AACrB,IAAI,MAAM,EAAE,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ;AAC5C,IAAI,QAAQ,EAAE,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,UAAU;AAChD,IAAI,OAAO,EAAE,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,SAAS;AAC9C,IAAI,MAAM,EAAE,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ;AAC5C,IAAI,kBAAkB,EAAE,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC;AACxE,IAAI,aAAa,EAAE,CAAC,GAAG,KAAK,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC;AACrD,IAAI,KAAK,EAAE,CAAC,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;AACtC,IAAI,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC;AAClD,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,UAAU,IAAI,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC;AACnF,CAAC,CAAC;AACF;AACO,SAAS,cAAc,CAAC,MAAM,EAAE,UAAU,EAAE,aAAa,GAAG,EAAE,EAAE;AACvE,IAAI,MAAM,UAAU,GAAG,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,KAAK;AACxD,QAAQ,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;AAC5C,QAAQ,IAAI,OAAO,QAAQ,KAAK,UAAU;AAC1C,YAAY,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;AAC1D,QAAQ,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AACtC,QAAQ,IAAI,UAAU,IAAI,GAAG,KAAK,SAAS;AAC3C,YAAY,OAAO;AACnB,QAAQ,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE;AACpC,YAAY,MAAM,IAAI,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,wBAAwB,GAAG,IAAI,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC;AAC7G,SAAS;AACT,KAAK,CAAC;AACN,IAAI,KAAK,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAC9D,QAAQ,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC3C,IAAI,KAAK,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC;AACjE,QAAQ,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC1C,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;AAeD;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,EAAE,EAAE;AAC7B,IAAI,MAAM,GAAG,GAAG,IAAI,OAAO,EAAE,CAAC;AAC9B,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,IAAI,KAAK;AAC7B,QAAQ,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,GAAG,KAAK,SAAS;AAC7B,YAAY,OAAO,GAAG,CAAC;AACvB,QAAQ,MAAM,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAC1C,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AAC/B,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK,CAAC;AACN;;AC7UA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA,MAAMD,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAEC,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAEC,KAAG,mBAAmB,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,mBAAmB,MAAM,CAAC,CAAC,CAAC,CAAC;AACzG;AACA,MAAM,GAAG,mBAAmB,MAAM,CAAC,CAAC,CAAC,EAAEC,KAAG,mBAAmB,MAAM,CAAC,CAAC,CAAC,EAAEC,KAAG,mBAAmB,MAAM,CAAC,CAAC,CAAC,CAAC;AAGxG;AACO,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE;AAC1B,IAAI,MAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AACzB,IAAI,OAAO,MAAM,IAAIJ,KAAG,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC;AAC/C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;AACxC,IAAI,IAAI,KAAK,GAAGA,KAAG;AACnB,QAAQ,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AACnE,IAAI,IAAI,MAAM,IAAIA,KAAG;AACrB,QAAQ,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAC3C,IAAI,IAAI,MAAM,KAAKC,KAAG;AACtB,QAAQ,OAAOD,KAAG,CAAC;AACnB,IAAI,IAAI,GAAG,GAAGC,KAAG,CAAC;AAClB,IAAI,OAAO,KAAK,GAAGD,KAAG,EAAE;AACxB,QAAQ,IAAI,KAAK,GAAGC,KAAG;AACvB,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,MAAM,CAAC;AACvC,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,MAAM,CAAC;AACnC,QAAQ,KAAK,KAAKA,KAAG,CAAC;AACtB,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACO,SAAS,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE;AACvC,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,OAAO,KAAK,EAAE,GAAGD,KAAG,EAAE;AAC1B,QAAQ,GAAG,IAAI,GAAG,CAAC;AACnB,QAAQ,GAAG,IAAI,MAAM,CAAC;AACtB,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA;AACO,SAAS,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE;AACvC,IAAI,IAAI,MAAM,KAAKA,KAAG;AACtB,QAAQ,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AAC5D,IAAI,IAAI,MAAM,IAAIA,KAAG;AACrB,QAAQ,MAAM,IAAI,KAAK,CAAC,yCAAyC,GAAG,MAAM,CAAC,CAAC;AAC5E;AACA,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC;AACnB;AACA,IAAO,IAAC,CAAC,GAAGA,KAAG,CAAC,CAAU,CAAC,GAAGC,KAAG,CAAU;AAC3C,IAAI,OAAO,CAAC,KAAKD,KAAG,EAAE;AACtB;AACA,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAE5B;AACA,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAS,CAAC,GAAG,CAAQ,CAAC;AACjD,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,GAAG,KAAKC,KAAG;AACnB,QAAQ,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAClD,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,CAAC,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA,IAAI,MAAM,SAAS,GAAG,CAAC,CAAC,GAAGA,KAAG,IAAIC,KAAG,CAAC;AACtC,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAChB;AACA;AACA,IAAI,KAAK,CAAC,GAAG,CAAC,GAAGD,KAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGC,KAAG,KAAKF,KAAG,EAAE,CAAC,IAAIE,KAAG,EAAE,CAAC,EAAE;AAC3D,QAAQ,CAAC;AACT;AACA,IAAI,KAAK,CAAC,GAAGA,KAAG,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,GAAGD,KAAG,EAAE,CAAC,EAAE,EAAE;AAClE;AACA,QAAQ,IAAI,CAAC,GAAG,IAAI;AACpB,YAAY,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAC3E,KAAK;AACL;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;AACjB,QAAQ,MAAM,MAAM,GAAG,CAAC,CAAC,GAAGA,KAAG,IAAI,GAAG,CAAC;AACvC,QAAQ,OAAO,SAAS,WAAW,CAAC,EAAE,EAAE,CAAC,EAAE;AAC3C,YAAY,MAAM,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3C,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxC,gBAAgB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AAC3D,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,KAAK;AACL;AACA,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,GAAGA,KAAG,IAAIC,KAAG,CAAC;AACnC,IAAI,OAAO,SAAS,WAAW,CAAC,EAAE,EAAE,CAAC,EAAE;AACvC;AACA,QAAQ,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC;AACnD,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AACvD,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC;AAClB;AACA,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7C,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAClC,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7B,QAAQ,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE;AACnC,YAAY,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;AAClC,gBAAgB,OAAO,EAAE,CAAC,IAAI,CAAC;AAC/B;AACA,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC;AACtB,YAAY,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACjD,gBAAgB,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC;AACtC,oBAAoB,MAAM;AAC1B,gBAAgB,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAChC,aAAa;AACb;AACA,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAED,KAAG,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC3B,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9B,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7B,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,SAAS;AACT,QAAQ,OAAO,CAAC,CAAC;AACjB,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,MAAM,CAAC,CAAC,EAAE;AAC1B;AACA;AACA,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,EAAE;AACzB;AACA;AACA;AACA;AACA,QAAQ,MAAM,MAAM,GAAG,CAAC,CAAC,GAAGA,KAAG,IAAI,GAAG,CAAC;AACvC,QAAQ,OAAO,SAAS,SAAS,CAAC,EAAE,EAAE,CAAC,EAAE;AACzC,YAAY,MAAM,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3C;AACA,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxC,gBAAgB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AAC3D,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,KAAK;AACL;AACA,IAAI,IAAI,CAAC,GAAGG,KAAG,KAAKD,KAAG,EAAE;AACzB,QAAQ,MAAM,EAAE,GAAG,CAAC,CAAC,GAAGA,KAAG,IAAIC,KAAG,CAAC;AACnC,QAAQ,OAAO,SAAS,SAAS,CAAC,EAAE,EAAE,CAAC,EAAE;AACzC,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAEF,KAAG,CAAC,CAAC;AACtC,YAAY,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACrC,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpC,YAAY,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAEA,KAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AACjD,YAAY,MAAM,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACvD,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxC,gBAAgB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AAC3D,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,KAAK;AAuBL;AACA,IAAI,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC;AACD;AACO,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,GAAGD,KAAG,MAAMA,KAAG,CAAC;AAC9E;AACA,MAAM,YAAY,GAAG;AACrB,IAAI,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;AAC3D,IAAI,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AAC5C,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;AAClC,CAAC,CAAC;AACK,SAAS,aAAa,CAAC,KAAK,EAAE;AACrC,IAAI,MAAM,OAAO,GAAG;AACpB,QAAQ,KAAK,EAAE,QAAQ;AACvB,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,KAAK,EAAE,eAAe;AAC9B,QAAQ,IAAI,EAAE,eAAe;AAC7B,KAAK,CAAC;AACN,IAAI,MAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK;AACnD,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;AAC9B,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,EAAE,OAAO,CAAC,CAAC;AAChB,IAAI,OAAO,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACvC,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,SAAS,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE;AACrC;AACA;AACA,IAAI,IAAI,KAAK,GAAGD,KAAG;AACnB,QAAQ,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AACnE,IAAI,IAAI,KAAK,KAAKA,KAAG;AACrB,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,IAAI,KAAK,KAAKC,KAAG;AACrB,QAAQ,OAAO,GAAG,CAAC;AACnB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AAClB,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AAChB,IAAI,OAAO,KAAK,GAAGD,KAAG,EAAE;AACxB,QAAQ,IAAI,KAAK,GAAGC,KAAG;AACvB,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrB,QAAQ,KAAK,KAAKA,KAAG,CAAC;AACtB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC;AACb,CAAC;AACD;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE;AACvC,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC;AACA,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,KAAK;AACxD,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AACtB,YAAY,OAAO,GAAG,CAAC;AACvB,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACrB,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/B,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACd;AACA,IAAI,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAC3C;AACA,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,KAAK;AACtC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AACtB,YAAY,OAAO,GAAG,CAAC;AACvB,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/B,KAAK,EAAE,QAAQ,CAAC,CAAC;AACjB,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AAsBD;AACO,SAAS,OAAO,CAAC,CAAC,EAAE,UAAU,EAAE;AACvC;AACA,IAAI,MAAM,WAAW,GAAG,UAAU,KAAK,SAAS,GAAG,UAAU,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACrF,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AACnD,IAAI,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACpD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,GAAG,KAAK,EAAE,KAAK,GAAG,EAAE,EAAE;AAC/D,IAAI,IAAI,KAAK,IAAID,KAAG;AACpB,QAAQ,MAAM,IAAI,KAAK,CAAC,yCAAyC,GAAG,KAAK,CAAC,CAAC;AAC3E,IAAI,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC5E,IAAI,IAAI,KAAK,GAAG,IAAI;AACpB,QAAQ,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;AAC1E,IAAI,IAAI,KAAK,CAAC;AACd,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;AAC5B,QAAQ,KAAK;AACb,QAAQ,IAAI;AACZ,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;AAC3B,QAAQ,IAAI,EAAEA,KAAG;AACjB,QAAQ,GAAG,EAAEC,KAAG;AAChB,QAAQ,MAAM,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC;AACxC,QAAQ,OAAO,EAAE,CAAC,GAAG,KAAK;AAC1B,YAAY,IAAI,OAAO,GAAG,KAAK,QAAQ;AACvC,gBAAgB,MAAM,IAAI,KAAK,CAAC,8CAA8C,GAAG,OAAO,GAAG,CAAC,CAAC;AAC7F,YAAY,OAAOD,KAAG,IAAI,GAAG,IAAI,GAAG,GAAG,KAAK,CAAC;AAC7C,SAAS;AACT,QAAQ,GAAG,EAAE,CAAC,GAAG,KAAK,GAAG,KAAKA,KAAG;AACjC,QAAQ,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,GAAGC,KAAG,MAAMA,KAAG;AAC3C,QAAQ,GAAG,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC;AACtC,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG;AACtC,QAAQ,GAAG,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,KAAK,CAAC;AAC3C,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,KAAK,CAAC;AAChD,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,KAAK,CAAC;AAChD,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,KAAK,CAAC;AAChD,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;AACjD,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC;AAC/D;AACA,QAAQ,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG;AAChC,QAAQ,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,GAAG,GAAG;AACrC,QAAQ,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,GAAG,GAAG;AACrC,QAAQ,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,GAAG,GAAG;AACrC,QAAQ,GAAG,EAAE,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC;AACxC,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,aAAa,CAAC,CAAC,KAAK;AACpB,gBAAgB,IAAI,CAAC,KAAK;AAC1B,oBAAoB,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC1C,gBAAgB,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC,aAAa,CAAC;AACd,QAAQ,WAAW,EAAE,CAAC,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,GAAG,CAAC;AACnD;AACA;AACA,QAAQ,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACtC,QAAQ,OAAO,EAAE,CAAC,GAAG,MAAM,IAAI,GAAG,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC5F,QAAQ,SAAS,EAAE,CAAC,KAAK,KAAK;AAC9B,YAAY,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK;AACtC,gBAAgB,MAAM,IAAI,KAAK,CAAC,4BAA4B,GAAG,KAAK,GAAG,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AACtG,YAAY,OAAO,IAAI,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC1E,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B;;AC5XA;AACA;AACA;AACA;AACA;AAIA,MAAMD,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAMC,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,SAAS,eAAe,CAAC,SAAS,EAAE,IAAI,EAAE;AAC1C,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC9B,IAAI,OAAO,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC;AAClC,CAAC;AACD,SAAS,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;AACtD,QAAQ,MAAM,IAAI,KAAK,CAAC,oCAAoC,GAAG,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC;AACvF,CAAC;AACD,SAAS,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE;AAC5B,IAAI,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACvB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC5C,IAAI,MAAM,UAAU,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACpC,IAAI,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;AACnC,CAAC;AACD,SAAS,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE;AACtC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;AAC9B,QAAQ,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAC1C,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAC7B,QAAQ,IAAI,EAAE,CAAC,YAAY,CAAC,CAAC;AAC7B,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,CAAC,CAAC,CAAC;AAC3D,KAAK,CAAC,CAAC;AACP,CAAC;AACD,SAAS,kBAAkB,CAAC,OAAO,EAAE,KAAK,EAAE;AAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;AACrD,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAC9B,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAC7B,YAAY,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC;AAC5D,KAAK,CAAC,CAAC;AACP,CAAC;AACD;AACA;AACA,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAE,CAAC;AACvC,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAE,CAAC;AACvC,SAAS,IAAI,CAAC,CAAC,EAAE;AACjB,IAAI,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACxC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE;AAC9B,IAAI,OAAO;AACX,QAAQ,eAAe;AACvB,QAAQ,cAAc,CAAC,GAAG,EAAE;AAC5B,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACnC,SAAS;AACT;AACA,QAAQ,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;AACzC,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC;AACxB,YAAY,OAAO,CAAC,GAAGD,KAAG,EAAE;AAC5B,gBAAgB,IAAI,CAAC,GAAGC,KAAG;AAC3B,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AAC/B,gBAAgB,CAAC,KAAKA,KAAG,CAAC;AAC1B,aAAa;AACb,YAAY,OAAO,CAAC,CAAC;AACrB,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,gBAAgB,CAAC,GAAG,EAAE,CAAC,EAAE;AACjC,YAAY,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/D,YAAY,MAAM,MAAM,GAAG,EAAE,CAAC;AAC9B,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC;AACxB,YAAY,IAAI,IAAI,GAAG,CAAC,CAAC;AACzB,YAAY,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,EAAE;AAC7D,gBAAgB,IAAI,GAAG,CAAC,CAAC;AACzB,gBAAgB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClC;AACA,gBAAgB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;AACrD,oBAAoB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC,oBAAoB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACtC,iBAAiB;AACjB,gBAAgB,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAClC,aAAa;AACb,YAAY,OAAO,MAAM,CAAC;AAC1B,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;AAChC;AACA;AACA,YAAY,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/D,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AAC3B,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AAC3B,YAAY,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C,YAAY,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC;AACrC,YAAY,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,YAAY,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,EAAE;AAC7D,gBAAgB,MAAM,MAAM,GAAG,MAAM,GAAG,UAAU,CAAC;AACnD;AACA,gBAAgB,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC7C;AACA,gBAAgB,CAAC,KAAK,OAAO,CAAC;AAC9B;AACA;AACA,gBAAgB,IAAI,KAAK,GAAG,UAAU,EAAE;AACxC,oBAAoB,KAAK,IAAI,SAAS,CAAC;AACvC,oBAAoB,CAAC,IAAIA,KAAG,CAAC;AAC7B,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,MAAM,OAAO,GAAG,MAAM,CAAC;AACvC,gBAAgB,MAAM,OAAO,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7D,gBAAgB,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/C,gBAAgB,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;AACxC,gBAAgB,IAAI,KAAK,KAAK,CAAC,EAAE;AACjC;AACA,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5E,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5E,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,YAAY,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC5B,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,UAAU,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE;AACpD,YAAY,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/D,YAAY,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C,YAAY,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC;AACrC,YAAY,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,YAAY,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,EAAE;AAC7D,gBAAgB,MAAM,MAAM,GAAG,MAAM,GAAG,UAAU,CAAC;AACnD,gBAAgB,IAAI,CAAC,KAAKD,KAAG;AAC7B,oBAAoB,MAAM;AAC1B;AACA,gBAAgB,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC7C;AACA,gBAAgB,CAAC,KAAK,OAAO,CAAC;AAC9B;AACA;AACA,gBAAgB,IAAI,KAAK,GAAG,UAAU,EAAE;AACxC,oBAAoB,KAAK,IAAI,SAAS,CAAC;AACvC,oBAAoB,CAAC,IAAIC,KAAG,CAAC;AAC7B,iBAAiB;AACjB,gBAAgB,IAAI,KAAK,KAAK,CAAC;AAC/B,oBAAoB,SAAS;AAC7B,gBAAgB,IAAI,IAAI,GAAG,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACrE,gBAAgB,IAAI,KAAK,GAAG,CAAC;AAC7B,oBAAoB,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACzC;AACA,gBAAgB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACpC,aAAa;AACb,YAAY,OAAO,GAAG,CAAC;AACvB,SAAS;AACT,QAAQ,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE;AACxC;AACA,YAAY,IAAI,IAAI,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,YAAY,IAAI,CAAC,IAAI,EAAE;AACvB,gBAAgB,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnD,gBAAgB,IAAI,CAAC,KAAK,CAAC;AAC3B,oBAAoB,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7D,aAAa;AACb,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE;AACpC,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9B,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AACzE,SAAS;AACT,QAAQ,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;AAChD,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9B,YAAY,IAAI,CAAC,KAAK,CAAC;AACvB,gBAAgB,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACrD,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACrF,SAAS;AACT;AACA;AACA;AACA,QAAQ,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE;AAC5B,YAAY,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/B,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvC,YAAY,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACvC,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACjC,IAAI,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACxC,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;AACxC,QAAQ,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;AAC/E,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AACxB,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AAChD,IAAI,MAAM,UAAU,GAAG,KAAK,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;AACtF,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,UAAU,IAAI,CAAC,CAAC;AACvC,IAAI,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnD,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,UAAU,CAAC,GAAG,UAAU,CAAC;AAC7E,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC;AACnB,IAAI,KAAK,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,UAAU,EAAE;AACpD,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,YAAY,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACtC,YAAY,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACvE,YAAY,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,SAAS;AACT,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC;AACxB;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAClE,YAAY,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,YAAY,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAClC,SAAS;AACT,QAAQ,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5B,QAAQ,IAAI,CAAC,KAAK,CAAC;AACnB,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE;AAC/C,gBAAgB,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;AACnC,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AAgFM,SAAS,aAAa,CAAC,KAAK,EAAE;AACrC,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC5B,IAAI,cAAc,CAAC,KAAK,EAAE;AAC1B,QAAQ,CAAC,EAAE,QAAQ;AACnB,QAAQ,CAAC,EAAE,QAAQ;AACnB,QAAQ,EAAE,EAAE,OAAO;AACnB,QAAQ,EAAE,EAAE,OAAO;AACnB,KAAK,EAAE;AACP,QAAQ,UAAU,EAAE,eAAe;AACnC,QAAQ,WAAW,EAAE,eAAe;AACpC,KAAK,CAAC,CAAC;AACP;AACA,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC;AACzB,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC7C,QAAQ,GAAG,KAAK;AAChB,QAAQ,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;AAChC,KAAK,CAAC,CAAC;AACP;;ACpXA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAEA,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAEC,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAEE,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACzE;AACA,MAAM,cAAc,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACxC,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,IAAI,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;AACtC,IAAIC,cAAiB,CAAC,KAAK,EAAE;AAC7B,QAAQ,IAAI,EAAE,UAAU;AACxB,QAAQ,CAAC,EAAE,QAAQ;AACnB,QAAQ,CAAC,EAAE,QAAQ;AACnB,QAAQ,WAAW,EAAE,UAAU;AAC/B,KAAK,EAAE;AACP,QAAQ,iBAAiB,EAAE,UAAU;AACrC,QAAQ,MAAM,EAAE,UAAU;AAC1B,QAAQ,OAAO,EAAE,UAAU;AAC3B,QAAQ,UAAU,EAAE,UAAU;AAC9B,KAAK,CAAC,CAAC;AACP;AACA,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;AACtC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,QAAQ,EAAE;AACzC,IAAI,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;AACzC,IAAI,MAAM,EAAE,EAAE,EAAE,CAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC,EAAE,QAAQ,GAAG,GAAG,KAAK,CAAC;AAChH;AACA;AACA;AACA;AACA,IAAI,MAAM,IAAI,GAAGH,KAAG,KAAK,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,GAAGD,KAAG,CAAC,CAAC;AACxD,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC,MAAM,CAAC;AAC3B,IAAI,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;AAChD;AACA,IAAI,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;AACjC,SAAS,CAAC,CAAC,EAAE,CAAC,KAAK;AACnB,YAAY,IAAI;AAChB,gBAAgB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACxE,aAAa;AACb,YAAY,OAAO,CAAC,EAAE;AACtB,gBAAgB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AACtD,aAAa;AACb,SAAS,CAAC,CAAC;AACX,IAAI,MAAM,iBAAiB,GAAG,KAAK,CAAC,iBAAiB,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC;AAC5E,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM;AAC/B,SAAS,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK;AAChC,YAAY,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACpC,YAAY,IAAI,GAAG,CAAC,MAAM,IAAI,MAAM;AACpC,gBAAgB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AACvE,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC,CAAC;AACX;AACA;AACA,IAAI,SAAS,WAAW,CAAC,KAAK,EAAE,CAAC,EAAE;AACnC,QAAQK,QAAW,CAAC,aAAa,GAAG,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AACzD,KAAK;AACL,IAAI,SAAS,WAAW,CAAC,KAAK,EAAE;AAChC,QAAQ,IAAI,EAAE,KAAK,YAAY,KAAK,CAAC;AACrC,YAAY,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AACtD,KAAK;AACL;AACA;AACA,IAAI,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK;AAC7C,QAAQ,MAAM,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAC1C,QAAQ,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AAC5B,QAAQ,IAAI,EAAE,IAAI,IAAI;AACtB,YAAY,EAAE,GAAG,GAAG,GAAGF,KAAG,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAChC,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAChC,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAChC,QAAQ,IAAI,GAAG;AACf,YAAY,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAEH,KAAG,EAAE,CAAC;AACtC,QAAQ,IAAI,EAAE,KAAKA,KAAG;AACtB,YAAY,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;AAChD,QAAQ,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;AAChC,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,eAAe,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK;AAC5C,QAAQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC/B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE;AACnB,YAAY,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAC/C;AACA;AACA,QAAQ,MAAM,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AACjD,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/B,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/B,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/B,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACjC,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACjC,QAAQ,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;AAC/C,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACzD,QAAQ,IAAI,IAAI,KAAK,KAAK;AAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AACrE;AACA,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/B,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/B,QAAQ,IAAI,EAAE,KAAK,EAAE;AACrB,YAAY,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AACrE,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC,CAAC;AACP;AACA;AACA,IAAI,MAAM,KAAK,CAAC;AAChB,QAAQ,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACpC,YAAY,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,YAAY,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,YAAY,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,YAAY,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,YAAY,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AACjC,YAAY,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AACjC,YAAY,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AACjC,YAAY,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AACjC,YAAY,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChC,SAAS;AACT,QAAQ,IAAI,CAAC,GAAG;AAChB,YAAY,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACrC,SAAS;AACT,QAAQ,IAAI,CAAC,GAAG;AAChB,YAAY,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,UAAU,CAAC,CAAC,EAAE;AAC7B,YAAY,IAAI,CAAC,YAAY,KAAK;AAClC,gBAAgB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;AAC9D,YAAY,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AACrC,YAAY,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAChC,YAAY,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAChC,YAAY,OAAO,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEA,KAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD,SAAS;AACT,QAAQ,OAAO,UAAU,CAAC,MAAM,EAAE;AAClC,YAAY,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClE,YAAY,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACpF,SAAS;AACT;AACA,QAAQ,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE;AACpC,YAAY,OAAO,SAAS,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACzD,SAAS;AACT;AACA,QAAQ,cAAc,CAAC,UAAU,EAAE;AACnC,YAAY,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AACjD,SAAS;AACT;AACA;AACA,QAAQ,cAAc,GAAG;AACzB,YAAY,eAAe,CAAC,IAAI,CAAC,CAAC;AAClC,SAAS;AACT;AACA,QAAQ,MAAM,CAAC,KAAK,EAAE;AACtB,YAAY,WAAW,CAAC,KAAK,CAAC,CAAC;AAC/B,YAAY,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACpD,YAAY,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC;AACrD,YAAY,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACvC,YAAY,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACvC,YAAY,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACvC,YAAY,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACvC,YAAY,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC;AAClD,SAAS;AACT,QAAQ,GAAG,GAAG;AACd,YAAY,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC3C,SAAS;AACT,QAAQ,MAAM,GAAG;AACjB;AACA,YAAY,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/E,SAAS;AACT;AACA;AACA;AACA,QAAQ,MAAM,GAAG;AACjB,YAAY,MAAM,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAChC,YAAY,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACpD,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACpC,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACpC,YAAY,MAAM,CAAC,GAAG,IAAI,CAACC,KAAG,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAChD,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAClC,YAAY,MAAM,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;AACjC,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACtD,YAAY,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B,YAAY,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B,YAAY,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,YAAY,OAAO,IAAI,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC7C,SAAS;AACT;AACA;AACA;AACA,QAAQ,GAAG,CAAC,KAAK,EAAE;AACnB,YAAY,WAAW,CAAC,KAAK,CAAC,CAAC;AAC/B,YAAY,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AACnC,YAAY,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AAC5D,YAAY,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC;AAC7D;AACA;AACA;AACA;AACA,YAAY,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;AAClC,gBAAgB,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACtD,gBAAgB,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACtD,gBAAgB,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC,gBAAgB,IAAI,CAAC,KAAK,GAAG;AAC7B,oBAAoB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AACzC,gBAAgB,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAGA,KAAG,GAAG,EAAE,CAAC,CAAC;AAC9C,gBAAgB,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAGA,KAAG,GAAG,EAAE,CAAC,CAAC;AAC9C,gBAAgB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAChC,gBAAgB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAChC,gBAAgB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAChC,gBAAgB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACvC,gBAAgB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACvC,gBAAgB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACvC,gBAAgB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACvC,gBAAgB,OAAO,IAAI,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACjD,aAAa;AACb,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACpC,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACpC,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;AACxC,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACpC,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1D,YAAY,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B,YAAY,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,YAAY,OAAO,IAAI,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC7C,SAAS;AACT,QAAQ,QAAQ,CAAC,KAAK,EAAE;AACxB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;AAC5C,SAAS;AACT,QAAQ,IAAI,CAAC,CAAC,EAAE;AAChB,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;AAC9D,SAAS;AACT;AACA,QAAQ,QAAQ,CAAC,MAAM,EAAE;AACzB,YAAY,MAAM,CAAC,GAAG,MAAM,CAAC;AAC7B,YAAYI,QAAW,CAAC,QAAQ,EAAE,CAAC,EAAEL,KAAG,EAAE,WAAW,CAAC,CAAC;AACvD,YAAY,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1C,YAAY,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,QAAQ,cAAc,CAAC,MAAM,EAAE,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE;AACjD,YAAY,MAAM,CAAC,GAAG,MAAM,CAAC;AAC7B,YAAYK,QAAW,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;AACvD,YAAY,IAAI,CAAC,KAAK,GAAG;AACzB,gBAAgB,OAAO,CAAC,CAAC;AACzB,YAAY,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAKL,KAAG;AACvC,gBAAgB,OAAO,IAAI,CAAC;AAC5B,YAAY,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACzE,SAAS;AACT;AACA;AACA;AACA;AACA,QAAQ,YAAY,GAAG;AACvB,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC;AACvD,SAAS;AACT;AACA;AACA,QAAQ,aAAa,GAAG;AACxB,YAAY,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC;AAC9D,SAAS;AACT;AACA;AACA,QAAQ,QAAQ,CAAC,EAAE,EAAE;AACrB,YAAY,OAAO,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAC1C,SAAS;AACT,QAAQ,aAAa,GAAG;AACxB,YAAY,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;AAC1C,YAAY,IAAI,QAAQ,KAAKA,KAAG;AAChC,gBAAgB,OAAO,IAAI,CAAC;AAC5B,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AACjD,SAAS;AACT;AACA;AACA,QAAQ,OAAO,OAAO,CAAC,GAAG,EAAE,MAAM,GAAG,KAAK,EAAE;AAC5C,YAAY,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AACnC,YAAY,MAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC;AACjC,YAAY,GAAG,GAAG,WAAW,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpD,YAAY,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACpC,YAAY,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;AACvC,YAAY,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1C,YAAY,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,CAAC;AAC/C,YAAY,MAAM,CAAC,GAAGM,eAAkB,CAAC,MAAM,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA,YAAY,MAAM,GAAG,GAAG,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC;AACjD,YAAYD,QAAW,CAAC,YAAY,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACnD;AACA;AACA,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAGL,KAAG,CAAC,CAAC;AACrC,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AACvC,YAAY,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtD,YAAY,IAAI,CAAC,OAAO;AACxB,gBAAgB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AACvE,YAAY,MAAM,MAAM,GAAG,CAAC,CAAC,GAAGA,KAAG,MAAMA,KAAG,CAAC;AAC7C,YAAY,MAAM,aAAa,GAAG,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,CAAC;AAC1D,YAAY,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,aAAa;AACrD;AACA,gBAAgB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AAChE,YAAY,IAAI,aAAa,KAAK,MAAM;AACxC,gBAAgB,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,YAAY,OAAO,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9C,SAAS;AACT,QAAQ,OAAO,cAAc,CAAC,OAAO,EAAE;AACvC,YAAY,OAAO,oBAAoB,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;AACvD,SAAS;AACT,QAAQ,UAAU,GAAG;AACrB,YAAY,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC7C,YAAY,MAAM,KAAK,GAAGO,eAAkB,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;AAC1D,YAAY,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGP,KAAG,GAAG,IAAI,GAAG,CAAC,CAAC;AAC1D,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,KAAK,GAAG;AAChB,YAAY,OAAOQ,UAAa,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AACpD,SAAS;AACT,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,EAAER,KAAG,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/E,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,EAAEA,KAAG,EAAEA,KAAG,EAAE,GAAG,CAAC,CAAC;AAC/C,IAAI,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AACvC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC;AAC9C,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE;AACrB,QAAQ,OAAO,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AACnC,KAAK;AACL;AACA,IAAI,SAAS,OAAO,CAAC,IAAI,EAAE;AAC3B,QAAQ,OAAO,IAAI,CAACM,eAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9C,KAAK;AACL;AACA,IAAI,SAAS,oBAAoB,CAAC,GAAG,EAAE;AACvC,QAAQ,MAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC;AAC7B,QAAQ,GAAG,GAAG,WAAW,CAAC,aAAa,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACnD;AACA;AACA,QAAQ,MAAM,MAAM,GAAG,WAAW,CAAC,oBAAoB,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9E,QAAQ,MAAM,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC7D,QAAQ,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AAClD,QAAQ,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AACrC,QAAQ,MAAM,KAAK,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACzC,QAAQ,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;AAC9C,QAAQ,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;AAC3D,KAAK;AACL;AACA,IAAI,SAAS,YAAY,CAAC,OAAO,EAAE;AACnC,QAAQ,OAAO,oBAAoB,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC;AACxD,KAAK;AACL;AACA,IAAI,SAAS,kBAAkB,CAAC,OAAO,GAAG,IAAI,UAAU,EAAE,EAAE,GAAG,IAAI,EAAE;AACrE,QAAQ,MAAM,GAAG,GAAGG,WAAc,CAAC,GAAG,IAAI,CAAC,CAAC;AAC5C,QAAQ,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACvF,KAAK;AACL;AACA,IAAI,SAAS,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,EAAE;AAC9C,QAAQ,GAAG,GAAG,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AAC1C,QAAQ,IAAI,OAAO;AACnB,YAAY,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B,QAAQ,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;AAC7E,QAAQ,MAAM,CAAC,GAAG,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;AACnE,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;AAC7C,QAAQ,MAAM,CAAC,GAAG,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;AAC1E,QAAQ,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AACvC,QAAQJ,QAAW,CAAC,aAAa,EAAE,CAAC,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;AACxD,QAAQ,MAAM,GAAG,GAAGI,WAAc,CAAC,CAAC,EAAEF,eAAkB,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACvE,QAAQ,OAAO,WAAW,CAAC,QAAQ,EAAE,GAAG,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,cAAc,CAAC;AACtC;AACA;AACA;AACA;AACA,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,GAAG,UAAU,EAAE;AAC/D,QAAQ,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC5C,QAAQ,MAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC;AAC7B,QAAQ,GAAG,GAAG,WAAW,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AACrD,QAAQ,GAAG,GAAG,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AAC1C,QAAQ,SAAS,GAAG,WAAW,CAAC,WAAW,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;AAC7D,QAAQ,IAAI,MAAM,KAAK,SAAS;AAChC,YAAY,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACpC,QAAQ,IAAI,OAAO;AACnB,YAAY,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B,QAAQ,MAAM,CAAC,GAAGD,eAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC9D,QAAQ,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;AACrB,QAAQ,IAAI;AACZ;AACA;AACA;AACA,YAAY,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AACjD,YAAY,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;AACzD,YAAY,EAAE,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,KAAK,EAAE;AACtB,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,YAAY,EAAE;AACvC,YAAY,OAAO,KAAK,CAAC;AACzB,QAAQ,MAAM,CAAC,GAAG,kBAAkB,CAAC,OAAO,EAAE,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG,CAAC,CAAC;AACnF,QAAQ,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA;AACA,QAAQ,OAAO,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACnE,KAAK;AACL,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACxB,IAAI,MAAM,KAAK,GAAG;AAClB,QAAQ,oBAAoB;AAC5B;AACA,QAAQ,gBAAgB,EAAE,MAAM,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,UAAU,CAAC,UAAU,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE;AACvD,YAAY,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAC7C,YAAY,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,KAAK,CAAC;AACN,IAAI,OAAO;AACX,QAAQ,KAAK;AACb,QAAQ,YAAY;AACpB,QAAQ,IAAI;AACZ,QAAQ,MAAM;AACd,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,KAAK;AACb,KAAK,CAAC;AACN;;AC/bA;AACA;AACA;AACA;AACA;AACA;AAIY,MAAM,CAAC,CAAC,EAAE;AACV,MAAM,CAAC,CAAC;;ACVpB;AACA;AACA;AACA;AACA;AACA;AACA;AAUA,MAAM,SAAS,GAAG,MAAM,CAAC,+EAA+E,CAAC,CAAC;AAC1G;AACA,MAAM,eAAe,mBAAmB,MAAM,CAAC,+EAA+E,CAAC,CAAC;AAChI;AACY,MAAM,CAAC,CAAC,CAAC,CAAC,OAAC,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAO,MAAM,CAAC,CAAC,EAAE;AACzE;AACA,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACvC,SAAS,mBAAmB,CAAC,CAAC,EAAE;AAChC;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AACrF,IAAI,MAAM,CAAC,GAAG,SAAS,CAAC;AACxB,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3B,IAAI,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5B,IAAI,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3C,IAAI,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1C,IAAI,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC5C,IAAI,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAC/C,IAAI,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAC/C,IAAI,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAC/C,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAChD,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AACjD,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AACjD,IAAI,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACnD;AACA,IAAI,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;AAC7B,CAAC;AACD,SAAS,iBAAiB,CAAC,KAAK,EAAE;AAClC;AACA;AACA,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;AACpB;AACA,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC;AACrB;AACA,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;AACpB,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD;AACA,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;AACvB,IAAI,MAAM,CAAC,GAAG,SAAS,CAAC;AACxB,IAAI,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AACjC,IAAI,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC;AACA,IAAI,MAAM,GAAG,GAAG,mBAAmB,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS,CAAC;AACtD,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;AACjC,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAClC,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC;AACpB,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,eAAe,EAAE,CAAC,CAAC,CAAC;AAC9C,IAAI,MAAM,QAAQ,GAAG,GAAG,KAAK,CAAC,CAAC;AAC/B,IAAI,MAAM,QAAQ,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,eAAe,EAAE,CAAC,CAAC,CAAC;AACxD,IAAI,IAAI,QAAQ;AAChB,QAAQ,CAAC,GAAG,KAAK,CAAC;AAClB,IAAI,IAAI,QAAQ,IAAI,MAAM;AAC1B,QAAQ,CAAC,GAAG,KAAK,CAAC;AAClB,IAAI,IAAI,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1B,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvB,IAAI,OAAO,EAAE,OAAO,EAAE,QAAQ,IAAI,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AACvD,CAAC;AAYD,MAAM,EAAE,mBAAmB,CAAC,MAAM,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC;AACvE,MAAM,eAAe,mBAAmB,CAAC,OAAO;AAChD;AACA,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AACjB;AACA;AACA,IAAI,CAAC,EAAE,MAAM,CAAC,+EAA+E,CAAC;AAC9F;AACA,IAAI,EAAE;AACN;AACA;AACA,IAAI,CAAC,EAAE,MAAM,CAAC,8EAA8E,CAAC;AAC7F;AACA,IAAI,CAAC,EAAE,GAAG;AACV;AACA,IAAI,EAAE,EAAE,MAAM,CAAC,+EAA+E,CAAC;AAC/F,IAAI,EAAE,EAAE,MAAM,CAAC,+EAA+E,CAAC;AAC/F,IAAI,IAAI,EAAER,QAAM;AAChB,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB;AACA;AACA;AACA,IAAI,OAAO;AACX,CAAC,CAAC,GAAG,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,OAAO,mBAAmB,CAAC,MAAM,cAAc,CAAC,eAAe,CAAC,GAAG;;ACrHnE,MAAA,eAAA,CAA2C,OAE3C,CAAA,eAAA,CAA2C,KAE3C,CAAA,aAAA,CAAgB,GAEhB,CAAA,YAAA,CAAe,WAEf,CAAA,aAAA,CAAgB,MAEhB,CAAA,aAAA,CAAgB,MAIhB,CAAA,aAAA,CAAgB,IAEhB,UAAa,CAAA,KAAA,CAEb,UAAa,CAAA,KAAA,CAIb,2BAA8B,CAAA,WAAA,CAE9B,uBAA0B,CAAA,GAAA,CAE1B,yBAA4B,CAAA,KAAA,CAE5B,yBAA4B,CAAA,EAAA,CAI5B,oBAAuB,CAAA;;ACpC7B,SAAS,YAAY,CAAC,GAAG,EAAE;AAClC,EAAE,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,EAAE;AACjC,IAAI,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AACtE,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb;;ACEO,SAAS,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE;AACtC,EAAE,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,IAAI,UAAU,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,EAAE;AAC1E,IAAI,OAAO,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7D,GAAG;AACH,EAAE,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAC9B;;ACVO,SAAS,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE;AACvC,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAChE,GAAG;AACH,EAAE,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;AACrC,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;AAC5B,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAC5B,IAAI,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC;AACzB,GAAG;AACH,EAAE,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;AAC9B;;ACbA,SAAS,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE;AAC9B,EAAE,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE;AAC9B,IAAI,MAAM,IAAI,SAAS,CAAC,mBAAmB,CAAC,CAAC;AAC7C,GAAG;AACH,EAAE,IAAI,QAAQ,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AACrC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,GAAG;AACH,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,IAAI,QAAQ,CAAC,EAAE,CAAC,KAAK,GAAG,EAAE;AAC9B,MAAM,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AACrB,GAAG;AACH,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC7B,EAAE,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClC,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/C,EAAE,SAAS,MAAM,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,MAAM,YAAY,UAAU,CAAC,CAAC;AACtC,SAAS,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;AACzC,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;AACnF,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACtC,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,KAAK;AACL,IAAI,IAAI,EAAE,MAAM,YAAY,UAAU,CAAC,EAAE;AACzC,MAAM,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;AACjD,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;AAC7B,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,IAAI,OAAO,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;AACpD,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,MAAM,EAAE,CAAC;AACf,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,GAAG,MAAM,IAAI,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC;AACnD,IAAI,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AACnC,IAAI,OAAO,MAAM,KAAK,IAAI,EAAE;AAC5B,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACjC,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,MAAM,KAAK,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACtF,QAAQ,KAAK,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACtC,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC;AACtC,QAAQ,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC;AACnC,OAAO;AACP,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE;AACvB,QAAQ,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAC1C,OAAO;AACP,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,MAAM,MAAM,EAAE,CAAC;AACf,KAAK;AACL,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,MAAM,CAAC;AAC5B,IAAI,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC3C,MAAM,GAAG,EAAE,CAAC;AACZ,KAAK;AACL,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACpC,IAAI,OAAO,GAAG,GAAG,IAAI,EAAE,EAAE,GAAG,EAAE;AAC9B,MAAM,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACvC,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH,EAAE,SAAS,YAAY,CAAC,MAAM,EAAE;AAChC,IAAI,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AACpC,MAAM,MAAM,IAAI,SAAS,CAAC,iBAAiB,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;AAC7B,MAAM,OAAO,IAAI,UAAU,EAAE,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;AAC7B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE;AACnC,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,GAAG,EAAE,CAAC;AACZ,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC;AACxD,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AACpC,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,EAAE;AACxB,MAAM,IAAI,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AACnD,MAAM,IAAI,KAAK,KAAK,GAAG,EAAE;AACzB,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,MAAM,KAAK,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACtF,QAAQ,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACxC,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,CAAC;AACtC,QAAQ,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,CAAC;AAClC,OAAO;AACP,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE;AACvB,QAAQ,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAC1C,OAAO;AACP,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,MAAM,GAAG,EAAE,CAAC;AACZ,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;AAC7B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,MAAM,CAAC;AAC5B,IAAI,OAAO,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC5C,MAAM,GAAG,EAAE,CAAC;AACZ,KAAK;AACL,IAAI,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC;AACnB,IAAI,OAAO,GAAG,KAAK,IAAI,EAAE;AACzB,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH,EAAE,SAAS,MAAM,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;AACtC,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,IAAI,GAAG,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;AAC/C,GAAG;AACH,EAAE,OAAO;AACT,IAAI,MAAM,EAAE,MAAM;AAClB,IAAI,YAAY,EAAE,YAAY;AAC9B,IAAI,MAAM,EAAE,MAAM;AAClB,GAAG,CAAC;AACJ,CAAC;AACD,IAAI,GAAG,GAAG,IAAI,CAAC;AACf,IAAI,+BAA+B,GAAG,GAAG;;AChHzC,MAAM,MAAM,GAAG,CAAC,IAAI;AACpB,EAAE,IAAI,CAAC,YAAY,UAAU,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,KAAK,YAAY;AACpE,IAAI,OAAO,CAAC,CAAC;AACb,EAAE,IAAI,CAAC,YAAY,WAAW;AAC9B,IAAI,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,EAAE,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;AAC7B,IAAI,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC;AAChE,GAAG;AACH,EAAE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACvD,CAAC,CAAC;AAEF,MAAMY,YAAU,GAAG,GAAG,IAAI,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACxD,MAAMC,UAAQ,GAAG,CAAC,IAAI,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;;AC7BjD,MAAM,OAAO,CAAC;AACd,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE;AACxC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,GAAG;AACH,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,IAAI,KAAK,YAAY,UAAU,EAAE;AACrC,MAAM,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC3D,KAAK,MAAM;AACX,MAAM,MAAM,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACvD,KAAK;AACL,GAAG;AACH,CAAC;AACD,MAAM,OAAO,CAAC;AACd,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE;AACxC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;AAC7C,MAAM,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,GAAG;AACH,EAAE,MAAM,CAAC,IAAI,EAAE;AACf,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,eAAe,EAAE;AACxD,QAAQ,MAAM,KAAK,CAAC,CAAC,kCAAkC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,4CAA4C,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC/J,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7D,KAAK,MAAM;AACX,MAAM,MAAM,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACvD,KAAK;AACL,GAAG;AACH,EAAE,EAAE,CAAC,OAAO,EAAE;AACd,IAAI,OAAO,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7B,GAAG;AACH,CAAC;AACD,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH,EAAE,EAAE,CAAC,OAAO,EAAE;AACd,IAAI,OAAO,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7B,GAAG;AACH,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5B,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC1C,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACnC,KAAK,MAAM;AACX,MAAM,MAAM,UAAU,CAAC,CAAC,kCAAkC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,4BAA4B,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;AAChK,KAAK;AACL,GAAG;AACH,CAAC;AACM,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK,IAAI,eAAe,CAAC;AACvD,EAAE,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE;AAC7C,EAAE,GAAG,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,EAAE;AAChD,CAAC,CAAC,CAAC;AACI,MAAM,KAAK,CAAC;AACnB,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE;AACpD,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACtC,GAAG;AACH,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACtC,GAAG;AACH,CAAC;AACM,MAAMC,MAAI,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACzF,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAK;AACnD,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAGC,+BAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACjD,EAAE,OAAOD,MAAI,CAAC;AACd,IAAI,MAAM;AACV,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,MAAM,EAAE,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACxC,GAAG,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAME,QAAM,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,KAAK;AACxD,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;AACnB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AAC5C,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,GAAG;AACH,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;AAC1B,EAAE,OAAO,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;AAClC,IAAI,EAAE,GAAG,CAAC;AACV,GAAG;AACH,EAAE,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,GAAG,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACxD,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC;AACf,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC;AAClB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;AAChC,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AAC7B,MAAM,MAAM,IAAI,WAAW,CAAC,CAAC,IAAI,GAAG,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;AACvD,KAAK;AACL,IAAI,MAAM,GAAG,MAAM,IAAI,WAAW,GAAG,KAAK,CAAC;AAC3C,IAAI,IAAI,IAAI,WAAW,CAAC;AACxB,IAAI,IAAI,IAAI,IAAI,CAAC,EAAE;AACnB,MAAM,IAAI,IAAI,CAAC,CAAC;AAChB,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,MAAM,IAAI,IAAI,CAAC;AAC5C,KAAK;AACL,GAAG;AACH,EAAE,IAAI,IAAI,IAAI,WAAW,IAAI,GAAG,GAAG,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE;AACvD,IAAI,MAAM,IAAI,WAAW,CAAC,wBAAwB,CAAC,CAAC;AACpD,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACF,MAAMC,QAAM,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,KAAK;AAChD,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC;AACpD,EAAE,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,WAAW,IAAI,CAAC,CAAC;AACtC,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC;AACf,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC;AACf,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACxC,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACnC,IAAI,IAAI,IAAI,CAAC,CAAC;AACd,IAAI,OAAO,IAAI,GAAG,WAAW,EAAE;AAC/B,MAAM,IAAI,IAAI,WAAW,CAAC;AAC1B,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC;AAC7C,KAAK;AACL,GAAG;AACH,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,GAAG,MAAM,IAAI,WAAW,GAAG,IAAI,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,IAAI,GAAG,EAAE;AACX,IAAI,OAAO,GAAG,CAAC,MAAM,GAAG,WAAW,GAAG,CAAC,EAAE;AACzC,MAAM,GAAG,IAAI,GAAG,CAAC;AACjB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACK,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,CAAC,KAAK;AAClE,EAAE,OAAOH,MAAI,CAAC;AACd,IAAI,MAAM;AACV,IAAI,IAAI;AACR,IAAI,MAAM,CAAC,KAAK,EAAE;AAClB,MAAM,OAAOG,QAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,MAAM,CAAC,KAAK,EAAE;AAClB,MAAM,OAAOD,QAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACxD,KAAK;AACL,GAAG,CAAC,CAAC;AACL,CAAC;;ACnJM,MAAME,UAAQ,GAAGJ,MAAI,CAAC;AAC7B,EAAE,MAAM,EAAE,IAAI;AACd,EAAE,IAAI,EAAE,UAAU;AAClB,EAAE,MAAM,EAAE,GAAG,IAAID,UAAQ,CAAC,GAAG,CAAC;AAC9B,EAAE,MAAM,EAAE,GAAG,IAAID,YAAU,CAAC,GAAG,CAAC;AAChC,CAAC,CAAC;;;;;;;ACTK,MAAM,KAAK,GAAG,OAAO,CAAC;AAC7B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,QAAQ,EAAE,IAAI;AAChB,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;;;;;;;ACLK,MAAM,KAAK,GAAG,OAAO,CAAC;AAC7B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,QAAQ,EAAE,UAAU;AACtB,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;;;;;;;ACLK,MAAM,MAAM,GAAG,KAAK,CAAC;AAC5B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,YAAY;AACxB,CAAC,CAAC;;;;;;;ACJK,MAAM,MAAM,GAAG,OAAO,CAAC;AAC9B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,kBAAkB;AAC9B,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,WAAW,GAAG,OAAO,CAAC;AACnC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,QAAQ,EAAE,kBAAkB;AAC9B,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;;;;;;;;ACXK,MAAM,MAAM,GAAG,OAAO,CAAC;AAC9B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,kCAAkC;AAC9C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,WAAW,GAAG,OAAO,CAAC;AACnC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,QAAQ,EAAE,kCAAkC;AAC9C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,SAAS,GAAG,OAAO,CAAC;AACjC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,mCAAmC;AAC/C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,cAAc,GAAG,OAAO,CAAC;AACtC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,gBAAgB;AACxB,EAAE,QAAQ,EAAE,mCAAmC;AAC/C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,SAAS,GAAG,OAAO,CAAC;AACjC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,kCAAkC;AAC9C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,cAAc,GAAG,OAAO,CAAC;AACtC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,gBAAgB;AACxB,EAAE,QAAQ,EAAE,kCAAkC;AAC9C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,YAAY,GAAG,OAAO,CAAC;AACpC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,cAAc;AACtB,EAAE,QAAQ,EAAE,mCAAmC;AAC/C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,iBAAiB,GAAG,OAAO,CAAC;AACzC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,mBAAmB;AAC3B,EAAE,QAAQ,EAAE,mCAAmC;AAC/C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,OAAO,GAAG,OAAO,CAAC;AAC/B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,QAAQ,EAAE,kCAAkC;AAC9C,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;;;;;;;;;;;;;;;ACrDK,MAAM,MAAM,GAAG,KAAK,CAAC;AAC5B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,sCAAsC;AAClD,CAAC,CAAC,CAAC;AACI,MAAM,WAAW,GAAG,KAAK,CAAC;AACjC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,QAAQ,EAAE,sCAAsC;AAClD,CAAC,CAAC;;;;;;;;ACTK,MAAM,SAAS,GAAG,KAAK,CAAC;AAC/B,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,QAAQ,EAAE,4DAA4D;AACxE,CAAC,CAAC,CAAC;AACI,MAAM,YAAY,GAAG,KAAK,CAAC;AAClC,EAAE,IAAI,EAAE,cAAc;AACtB,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,QAAQ,EAAE,4DAA4D;AACxE,CAAC,CAAC;;;;;;;;ACTK,MAAM,MAAM,GAAG,OAAO,CAAC;AAC9B,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,kEAAkE;AAC9E,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,SAAS,GAAG,OAAO,CAAC;AACjC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,mEAAmE;AAC/E,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,SAAS,GAAG,OAAO,CAAC;AACjC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,kEAAkE;AAC9E,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC,CAAC;AACI,MAAM,YAAY,GAAG,OAAO,CAAC;AACpC,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,IAAI,EAAE,cAAc;AACtB,EAAE,QAAQ,EAAE,mEAAmE;AAC/E,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;;;;;;;;;;ACvBF,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,s2FAAs2F,CAAC,CAAC;AACp4F,MAAM,oBAAoB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;AAC1D,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,EAAE,OAAO,CAAC,CAAC;AACX,CAAC,EAAE,EAAE,CAAC,CAAC;AACP,MAAM,oBAAoB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;AAC1D,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1B,EAAE,OAAO,CAAC,CAAC;AACX,CAAC,EAAE,EAAE,CAAC,CAAC;AACP,SAASK,QAAM,CAAC,IAAI,EAAE;AACtB,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAC/B,IAAI,CAAC,IAAI,oBAAoB,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,OAAO,CAAC,CAAC;AACb,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AACD,SAASD,QAAM,CAAC,GAAG,EAAE;AACrB,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC;AAClB,EAAE,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;AAC1B,IAAI,MAAM,GAAG,GAAG,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;AAC3B,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,4BAA4B,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;AAC/D,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,GAAG;AACH,EAAE,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC;AACM,MAAM,YAAY,GAAGF,MAAI,CAAC;AACjC,EAAE,MAAM,EAAE,cAAc;AACxB,EAAE,IAAI,EAAE,cAAc;AACtB,UAAEG,QAAM;AACR,UAAED,QAAM;AACR,CAAC,CAAC;;;;;;;AChCF,IAAI,QAAQ,GAAGC,QAAM,CAAC;AACtB,IAAI,GAAG,GAAG,GAAG,EAAE,IAAI,GAAG,GAAG,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACjE,SAASA,QAAM,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE;AAClC,EAAE,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;AAClB,EAAE,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;AACvB,EAAE,IAAI,SAAS,GAAG,MAAM,CAAC;AACzB,EAAE,OAAO,GAAG,IAAI,GAAG,EAAE;AACrB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACpC,IAAI,GAAG,IAAI,GAAG,CAAC;AACf,GAAG;AACH,EAAE,OAAO,GAAG,GAAG,MAAM,EAAE;AACvB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACpC,IAAI,GAAG,MAAM,CAAC,CAAC;AACf,GAAG;AACH,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACxB,EAAEA,QAAM,CAAC,KAAK,GAAG,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC;AACxC,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,IAAI,MAAM,GAAG,IAAI,CAAC;AAClB,IAAI,KAAK,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;AAC9B,SAAS,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE;AAC3B,EAAE,IAAI,GAAG,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;AACpF,EAAE,GAAG;AACL,IAAI,IAAI,OAAO,IAAI,CAAC,EAAE;AACtB,MAAM,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACrB,MAAM,MAAM,IAAI,UAAU,CAAC,yBAAyB,CAAC,CAAC;AACtD,KAAK;AACL,IAAI,CAAC,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;AACvB,IAAI,GAAG,IAAI,KAAK,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAClF,IAAI,KAAK,IAAI,CAAC,CAAC;AACf,GAAG,QAAQ,CAAC,IAAI,KAAK,EAAE;AACvB,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC;AAChC,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzB,IAAI,MAAM,GAAG,UAAU,KAAK,EAAE;AAC9B,EAAE,OAAO,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AACrK,CAAC,CAAC;AACF,IAAI,MAAM,GAAG;AACb,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,cAAc,EAAE,MAAM;AACxB,CAAC,CAAC;AACF,IAAI,YAAY,GAAG,MAAM;;AC3ClB,MAAM,QAAQ,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,KAAK;AACrD,EAAEE,YAAM,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACrC,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AACK,MAAM,cAAc,GAAG,GAAG,IAAI;AACrC,EAAE,OAAOA,YAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACpC,CAAC;;ACTM,MAAM,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK;AACxC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC;AACjC,EAAE,MAAM,UAAU,GAAGC,cAAqB,CAAC,IAAI,CAAC,CAAC;AACjD,EAAE,MAAM,YAAY,GAAG,UAAU,GAAGA,cAAqB,CAAC,IAAI,CAAC,CAAC;AAChE,EAAE,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC;AACpD,EAAEC,QAAe,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AAClC,EAAEA,QAAe,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAC3C,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AAClC,EAAE,OAAO,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAC/C,CAAC,CAAC;AAkBK,MAAM,MAAM,CAAC;AACpB,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE;AACzC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,GAAG;AACH;;ACtCO,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACtE,MAAM,MAAM,CAAC;AACpB,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAClC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,GAAG;AACH,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,IAAI,KAAK,YAAY,UAAU,EAAE;AACrC,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACxC,MAAM,OAAO,MAAM,YAAY,UAAU,GAAGC,MAAa,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,IAAIA,MAAa,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;AACvI,KAAK,MAAM;AACX,MAAM,MAAM,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACvD,KAAK;AACL,GAAG;AACH;;ACfA,MAAM,GAAG,GAAG,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,UAAU,CAAC,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAClF,MAAM,MAAM,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,EAAE,UAAU;AAClB,EAAE,IAAI,EAAE,EAAE;AACV,EAAE,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC;AACxB,CAAC,CAAC,CAAC;AACI,MAAM,MAAM,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,EAAE,UAAU;AAClB,EAAE,IAAI,EAAE,EAAE;AACV,EAAE,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC;AACxB,CAAC,CAAC;;;;;;;;ACTF,MAAM,IAAI,GAAG,CAAC,CAAC;AACf,MAAM,IAAI,GAAG,UAAU,CAAC;AACxB,MAAM,MAAM,GAAG,MAAM,CAAC;AACtB,MAAM,MAAM,GAAG,KAAK,IAAIA,MAAa,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACpD,MAAM,QAAQ,GAAG;AACxB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,MAAM;AACR,EAAE,MAAM;AACR,CAAC;;;;;;;ACXmB,IAAI,WAAW,GAAG;AAClB,IAAI,WAAW;;ACoBnC,MAAM,KAAK,GAAG;AACd,EAAE,GAAG,YAAY;AACjB,EAAE,GAAGC,OAAK;AACV,EAAE,GAAGC,OAAK;AACV,EAAE,GAAGC,QAAM;AACX,EAAE,GAAGC,QAAM;AACX,EAAE,GAAGC,QAAM;AACX,EAAE,GAAGC,QAAM;AACX,EAAE,GAAG,MAAM;AACX,EAAE,GAAGC,QAAM;AACX,EAAE,GAAGC,cAAY;AACjB,CAAC,CAAC;CACa;AACf,EAAE,GAAG,IAAI;AACT,EAAE,GAAGZ,UAAQ;AACb;;AClCA,SAAS,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AACnD,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,OAAO,EAAE;AACb,MAAM,IAAI;AACV,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,MAAM,EAAE;AACvB,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;AAC/C,EAAE,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;AAC1C,EAAE,OAAO,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACnC,CAAC,EAAE,GAAG,IAAI;AACV,EAAE,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;AACpC,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI;AAC/C,EAAE,IAAI,MAAM,GAAG,GAAG,CAAC;AACnB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,IAAI,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC,EAAE,GAAG,IAAI;AACV,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACtC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/B,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,OAAO,EAAE,MAAM;AACjB,EAAE,GAAG,EAAE,KAAK,CAAC,MAAM;AACnB,EAAE,MAAM,EAAE,KAAK;AACf,EAAE,KAAK,EAAE,KAAK;AACd,EAAE,MAAM,EAAE,KAAK;AACf,EAAE,GAAG,KAAK;AACV,CAAC;;AC1CM,SAAS,QAAQ,CAAC,KAAK,EAAE,QAAQ,GAAG,MAAM,EAAE;AACnD,EAAE,MAAM,IAAI,GAAGa,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC/B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,sBAAsB,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5D,GAAG;AACH,EAAE,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO,KAAK,UAAU,CAAC,MAAM,IAAI,IAAI,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE;AACpH,IAAI,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACrG,GAAG;AACH,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACjD;;ACRO,SAAS,UAAU,CAAC,MAAM,EAAE,QAAQ,GAAG,MAAM,EAAE;AACtD,EAAE,MAAM,IAAI,GAAGA,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC/B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,sBAAsB,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5D,GAAG;AACH,EAAE,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO,KAAK,UAAU,CAAC,MAAM,IAAI,IAAI,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE;AACpH,IAAI,OAAO,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;AACjE,GAAG;AACH,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;AAC5D;;SCWgB,UAAWC,CAAAA,CAAAA,CAAkB,CAC3C,OAAOC,uBAAcpB,QAASD,CAAAA,UAAAA,CAAWoB,CAAKE,CAAAA,YAAY,EAAGC,aAAa,CAAC,CAC7E,CAEO,SAAS,WAAWC,CAAkB,CAAA,CAC3C,OAAOvB,QAAAA,CAASD,WAAWyB,0BAAkBD,CAAAA,CAAG,CAAGD,CAAAA,aAAa,EAAGD,YAAY,CACjF,CAIO,SAAS,UAAUI,CAA+B,CAAA,CACvD,MAAMC,CAAS3B,CAAAA,UAAAA,CAAW4B,0BAA2BC,2BAA2B,CAAA,CAC1EC,CACJC,CAAAA,uBAAAA,CAA0B9B,SAAS+B,MAAO,CAAA,CAACL,CAAQD,CAAAA,CAAS,CAAC,CAAGG,CAAAA,2BAA2B,CAC7F,CAAA,OAAO,CAACI,UAAYC,CAAAA,UAAAA,CAAYJ,CAAU,CAAA,CAAE,KAAKK,aAAa,CAChE,CAEO,SAAS,UAAUC,CAA4B,CAAA,CACpD,KAAM,CAACC,EAAQC,CAAQR,CAAAA,CAAU,CAAIM,CAAAA,CAAAA,CAAO,MAAMD,aAAa,CAAA,CAC/D,GAAIE,CAAWJ,GAAAA,UAAAA,EAAcK,IAAWJ,UACtC,CAAA,MAAM,IAAI,KAAA,CAAM,wCAAwC,CAG1D,CAAA,GADaJ,CAAW,CAAA,KAAA,CAAM,EAAG,CAAC,CAAA,GACrBC,uBACX,CAAA,MAAM,IAAI,KAAM,CAAA,0CAA0C,EAE5D,MAAMQ,CAAAA,CAAQvC,WAAW8B,CAAW,CAAA,KAAA,CAAM,CAAC,CAAA,CAAGD,2BAA2B,CAEzE,CAAA,GADa5B,QAASsC,CAAAA,CAAAA,CAAM,MAAM,CAAG,CAAA,CAAC,CAAGV,CAAAA,2BAA2B,IACvDD,yBACX,CAAA,MAAM,IAAI,KAAM,CAAA,iDAAiD,EAEnE,MAAMF,CAAAA,CAAYa,CAAM,CAAA,KAAA,CAAM,CAAC,CAC/B,CAAA,GAAIb,CAAU,CAAA,MAAA,GAAWc,0BACvB,MAAM,IAAI,KAAM,CAAA,kDAAkD,EAEpE,OAAOd,CACT,CAIO,SAAS,UAAUa,CAA2B,CAAA,CACnD,OAAOtC,QAAAA,CAASsC,EAAOjB,YAAY,CACrC,CAEO,SAAS,UAAUmB,CAA6B,CAAA,CACrD,OAAOzC,UAAAA,CAAWyC,EAASnB,YAAY,CACzC,CAIgB,SAAA,UAAA,CAAWoB,EAAoC,CAC7D,OAAO1C,UACL,CAAA,CAAC,WAAW0C,CAAO,CAAA,MAAM,CAAG,CAAA,UAAA,CAAWA,EAAO,OAAO,CAAC,CAAE,CAAA,IAAA,CAAKC,aAAa,CAC1EC,CAAAA,aACF,CACF,CAEO,SAAS,WAAWC,CAAkC,CAAA,CAC3D,MAAMH,CAAAA,CAASzC,SAAS4C,CAAMD,CAAAA,aAAa,CAAE,CAAA,KAAA,CAAMD,aAAa,CAC1DhB,CAAAA,CAAAA,CAAS,UAAWe,CAAAA,CAAAA,CAAO,CAAC,CAAC,CAAA,CAC7BI,EAAU,UAAWJ,CAAAA,CAAAA,CAAO,CAAC,CAAC,CAAA,CACpC,OAAO,CAAE,OAAAf,CAAQ,CAAA,OAAA,CAAAmB,CAAQ,CAC3B,CAIgB,SAAA,SAAA,CAAUJ,CAAkC,CAAA,CAC1D,OAAO,CAAC,UAAA,CAAWA,CAAO,CAAA,MAAM,EAAG,UAAWA,CAAAA,CAAAA,CAAO,OAAO,CAAA,CAAG,UAAUA,CAAO,CAAA,SAAS,CAAC,CAAA,CAAE,KAC1FC,aACF,CACF,CAEO,SAAS,UAAUI,CAAgC,CAAA,CACxD,MAAML,CAASK,CAAAA,CAAAA,CAAI,MAAMJ,aAAa,CAAA,CAChChB,CAAS,CAAA,UAAA,CAAWe,EAAO,CAAC,CAAC,CAC7BI,CAAAA,CAAAA,CAAU,WAAWJ,CAAO,CAAA,CAAC,CAAC,CAAA,CAC9BM,EAAY,SAAUN,CAAAA,CAAAA,CAAO,CAAC,CAAC,CAAA,CAC/BG,EAAO7C,UAAW0C,CAAAA,CAAAA,CAAO,KAAM,CAAA,CAAA,CAAG,CAAC,CAAE,CAAA,IAAA,CAAKC,aAAa,CAAA,CAAGC,aAAa,CAC7E,CAAA,OAAO,CAAE,MAAA,CAAAjB,EAAQ,OAAAmB,CAAAA,CAAAA,CAAS,UAAAE,CAAW,CAAA,IAAA,CAAAH,CAAK,CAC5C;;ACxFgB,SAAA,eAAA,CAAgBI,EAAmBC,WAAYC,CAAAA,oBAAoB,EAAY,CAC7F,MAAMzB,CAAY0B,CAAAA,OAAAA,CAAQ,aAAaH,CAAI,CAAA,CAE3C,OAAO,CACL,SAAA,CAFgBjB,OAAO,CAACiB,CAAAA,CAAMvB,CAAS,CAAC,EAGxC,SAAAA,CAAAA,CACF,CACF,CAEA,eAAsB,QACpB2B,CACAC,CAAAA,CAAAA,CACAC,CACAC,CAAAA,CAAAA,CACAC,EAAcC,oBAAgB,CAAA,IAAA,CAAK,KAAK,CAAA,CACxC,CACA,MAAM/B,CAAAA,CAAS,CAAE,GAAKgC,CAAAA,eAAAA,CAAiB,IAAKC,eAAgB,CAAA,CACtDC,EAAMC,SAAUN,CAAAA,CAAAA,CAAQ,SAAS,CACjCO,CAAAA,CAAAA,CAAMN,CAAMF,CAAAA,CAAAA,CACZT,EAAU,CAAE,GAAA,CAAAe,EAAK,GAAAR,CAAAA,CAAAA,CAAK,IAAAC,CAAK,CAAA,GAAA,CAAAG,CAAK,CAAA,GAAA,CAAAM,CAAI,CACpClB,CAAAA,CAAAA,CAAOmB,WAAW,CAAE,MAAA,CAAArC,EAAQ,OAAAmB,CAAAA,CAAQ,CAAC,CAAA,CACrCE,EAAYI,OAAQ,CAAA,IAAA,CAAKP,EAAMW,CAAQ,CAAA,SAAA,CAAU,MAAM,CAAG,CAAA,EAAE,CAAC,CACnE,CAAA,OAAOS,UAAU,CAAE,MAAA,CAAAtC,EAAQ,OAAAmB,CAAAA,CAAAA,CAAS,UAAAE,CAAU,CAAC,CACjD,gBAEsB,SAAUD,CAAAA,CAAAA,CAAa,CAC3C,KAAM,CAAE,OAAApB,CAAQ,CAAA,OAAA,CAAAmB,EAAS,IAAAD,CAAAA,CAAAA,CAAM,UAAAG,CAAU,CAAA,CAAIkB,UAAUnB,CAAG,CAAA,CAC1D,GAAIpB,CAAO,CAAA,GAAA,GAAQgC,eAAmBhC,EAAAA,CAAAA,CAAO,MAAQiC,eACnD,CAAA,MAAM,IAAI,KAAM,CAAA,8BAA8B,EAEhD,MAAMlC,CAAAA,CAAYyC,UAAUrB,CAAQ,CAAA,GAAG,EACvC,OAAOM,OAAAA,CAAQ,OAAOJ,CAAWH,CAAAA,CAAAA,CAAMnB,CAAS,CAClD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}