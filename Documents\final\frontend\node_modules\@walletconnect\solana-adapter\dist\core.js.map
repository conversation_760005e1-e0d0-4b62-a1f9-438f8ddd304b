{"version": 3, "file": "core.js", "sourceRoot": "", "sources": ["../src/core.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAA;AAC3F,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,oBAAoB,EAAE,MAAM,iBAAiB,CAAA;AAE9E,OAAO,EAAE,iBAAiB,EAAE,MAAM,mCAAmC,CAAA;AACrE,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAA;AACrD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAA;AACjD,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAA;AAE5E,OAAO,MAAM,MAAM,MAAM,CAAA;AAKzB,OAAO,EAAE,uBAAuB,EAAE,MAAM,gBAAgB,CAAA;AAExD,OAAO,EAAE,yBAAyB,EAAE,MAAM,uCAAuC,CAAA;AACjF,OAAO,EAAE,qCAAqC,EAAE,MAAM,4CAA4C,CAAA;AAMlG,OAAO,EAAE,gBAAgB,EAAE,0BAA0B,EAAE,MAAM,YAAY,CAAA;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAA;AAE/D,MAAM,OAAO,mBAAmB;IACtB,kBAAkB,CAAmC;IACrD,QAAQ,CAAiC;IACzC,MAAM,CAAoB;IAC1B,UAAU,CAAQ;IAClB,QAAQ,CAAsB;IAC9B,qBAAqB,CAAwC;IAErE,YAAY,MAAwC;QAClD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QAC/B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAA;QAE9B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC9B,MAAM,KAAK,CAAC,gDAAgD,CAAC,CAAA;QAC/D,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAA;IAC5C,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE;gBACtB,IAAI,CAAC,qBAAqB,GAAG,GAAG,CAAA;YAClC,CAAC,CAAC,CAAA;QACJ,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CACb,oFAAoF,CACrF,CAAA;QACH,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAA;YAC/C,MAAM,cAAc,GAAG,0BAA0B,CAC/C,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,QAAQ,CACU,CAAA;YACzB,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAA;YAC9B,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,cAAc,CAAC,CAAA;YAEvD,OAAO;gBACL,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B,CAAA;QACH,CAAC;QACD,MAAM,IAAI,CAAC,SAAS,EAAE,CAAA;QACtB,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC9C,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAA;QAEnB,mDAAmD;QACnD,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAA;QACxC,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAA;QAEhC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;gBAChB,UAAU,CAAC,KAAK,CAAC,IAAI,gBAAgB,EAAE,CAAC,CAAA;YAC1C,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;gBACjC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,MAAM,CAAC;gBACxC,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;oBAC/B,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;wBACpC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;oBACvB,CAAC,CAAC,CAAA;gBACJ,CAAC,CAAC;aACH,CAAC,CAAA;YACF,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,CAAA;YACpB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAA;YACvB,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,qBAAqB,EAAE,CAAA;YACnC,CAAC;YACD,MAAM,cAAc,GAAG,0BAA0B,CAC/C,OAAO,EACP,IAAI,CAAC,QAAQ,CACU,CAAA;YACzB,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAA;YAC9B,IAAI,CAAC,kBAAkB,EAAE,eAAe,CAAC,cAAc,CAAC,CAAA;YAExD,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAA;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,CAAA;YACpB,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC;YACrC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAA;YACtB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,KAAK,CAAC,iEAAiE,CAAC,CAAA;YAChF,CAAC;YACD,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAA;YAC9B,iCAAiC;YACjC,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAA;QAC3B,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,yBAAyB,EAAE,CAAA;QACvC,CAAC;IAEH,CAAC;IAED,IAAI,MAAM;QACR,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,kBAAkB,CAAA;QAChC,CAAC;QACD,MAAM,IAAI,yBAAyB,EAAE,CAAA;IACvC,CAAC;IAED,IAAI,OAAO;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,yBAAyB,EAAE,CAAA;QACvC,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAA;IACtB,CAAC;IAED,IAAI,SAAS;QACX,IAAI,IAAI,CAAC,kBAAkB,EAAE,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtD,MAAM,EAAE,OAAO,EAAE,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;YAE1F,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC,CAAA;QAC/B,CAAC;QACD,MAAM,IAAI,yBAAyB,EAAE,CAAA;IACvC,CAAC;IAED,KAAK,CAAC,eAAe,CAA+C,WAAc;QAChF,IAAI,CAAC,2BAA2B,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAA;QAEzE,MAAM,WAAW,GAAG,sBAAsB,CAAC,WAAW,CAAC,CAAA;QAEvD,MAAM,iBAAiB,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAA;QAExD,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,2BAA2B,EAAE,GAC3D,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAG7B;YACD,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;YACzB,OAAO,EAAE;gBACP,MAAM,EAAE,uBAAuB,CAAC,eAAe;gBAC/C,MAAM,EAAE;oBACN;;;;uBAIG;oBACH,GAAG,iBAAiB;oBACpB,8DAA8D;oBAC9D,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;iBACzC;aACF;SACF,CAAC,CAAA;QAEJ,IAAI,2BAA2B,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,WAAW,CAAC,2BAA2B,EAAE,WAAW,CAAM,CAAA;QACxE,CAAC;QAED,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QAE/E,OAAO,WAAW,CAAA;IACpB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAmB;QACnC,IAAI,CAAC,2BAA2B,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAA;QAErE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAEnD;YACD,6GAA6G;YAC7G,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;YACzB,OAAO,EAAE;gBACP,MAAM,EAAE,uBAAuB,CAAC,WAAW;gBAC3C,MAAM,EAAE;oBACN,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;oBACjC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;iBAChC;aACF;SACF,CAAC,CAAA;QAEF,OAAO,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;IACjC,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,WAAc;QAEd,IAAI,CAAC,2BAA2B,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,CAAA;QAEhF,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAEnD;YACD,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;YACzB,OAAO,EAAE;gBACP,MAAM,EAAE,uBAAuB,CAAC,sBAAsB;gBACtD,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE;aACrD;SACF,CAAC,CAAA;QAEF,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,YAAiB;QAEjB,IAAI,CAAC;YACH,IAAI,CAAC,2BAA2B,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,CAAA;YAE7E,MAAM,sBAAsB,GAAG,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAA;YAE3F,MAAM,EAAE,YAAY,EAAE,4BAA4B,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAEpF;gBACD,OAAO,EAAE,IAAI,CAAC,QAAQ;gBACtB,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;gBACzB,OAAO,EAAE;oBACP,MAAM,EAAE,uBAAuB,CAAC,mBAAmB;oBACnD,MAAM,EAAE,EAAE,YAAY,EAAE,sBAAsB,EAAE;iBACjD;aACF,CAAC,CAAA;YAEF,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE;gBAC7C,IAAI,sBAAsB,CAAC,WAAW,CAAC,EAAE,CAAC;oBACxC,OAAO,IAAI,CAAC,WAAW,CAAC,4BAA4B,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CAAA;gBAC1E,CAAC;gBAED,OAAO,IAAI,CAAC,WAAW,CAAC,4BAA4B,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAA;YACpE,CAAC,CAAQ,CAAA;QACX,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,qCAAqC,EAAE,CAAC;gBAC3D,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAA;gBACnF,MAAM,kBAAkB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;gBAEtD,OAAO,kBAAkB,CAAA;YAC3B,CAAC;YAED,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,OAAgC;QAC/C,MAAM,QAAQ,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACtD,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAA;QAClC,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA;QAClC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS;QACb,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,OAAM;QACR,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CACb,gFAAgF,CACjF,CAAA;QACH,CAAC;QAGD,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC;YACzB,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,QAAQ,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,aAAa,CAAC;YAC/C,eAAe,EAAE,IAAI;SACtB,CAAC,CAAA;IACJ,CAAC;IAEO,SAAS,CAAC,WAA+C;QAC/D,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;IAC3F,CAAC;IAEO,WAAW,CACjB,qBAA6B,EAC7B,SAAS,GAAG,KAAK;QAEjB,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,oBAAoB,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC,CAAA;QACvF,CAAC;QAED,OAAO,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC,CAAA;IACvE,CAAC;IAEO,2BAA2B,CAAC,MAA+B;QACjE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,qCAAqC,CAAC,MAAM,CAAC,CAAA;QACzD,CAAC;IACH,CAAC;CACF"}