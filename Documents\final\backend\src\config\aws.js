/**
 * AWS Configuration
 * 
 * This file centralizes all AWS-related configuration.
 */

// Load environment variables
require('dotenv').config();

// AWS SDK
const AWS = require('aws-sdk');

// AWS region
const region = process.env.AWS_REGION || 'us-east-1';

// Configure AWS
AWS.config.update({
  region,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

// SES configuration
const sesConfig = {
  sourceEmail: process.env.EMAIL_FROM || '<EMAIL>',
  region
};

// SNS configuration
const snsConfig = {
  senderId: process.env.SMS_SENDER_ID || 'SWAP',
  smsType: 'Transactional',
  region
};

// Create AWS service objects
const ses = new AWS.SES({ apiVersion: '2010-12-01' });
const sns = new AWS.SNS({ apiVersion: '2010-03-31' });

// Export AWS services and configurations
module.exports = {
  ses,
  sesConfig,
  sns,
  snsConfig,
  s3: new AWS.S3({ apiVersion: '2006-03-01' }),
  cloudWatch: new AWS.CloudWatch({ apiVersion: '2010-08-01' })
};
