const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  tokenMintAddress: {
    type: String,
    required: false
  },
  tokenSymbol: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['buy', 'sell', 'swap', 'deposit', 'withdraw'],
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  total: {
    type: Number,
    required: true
  },
  fee: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  signature: {
    type: String,
    required: false
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
});

// Indexes for faster queries
transactionSchema.index({ userId: 1, timestamp: -1 });
transactionSchema.index({ tokenMintAddress: 1, timestamp: -1 });

// Static methods
transactionSchema.statics.getUserTransactions = async function(userId, limit = 50) {
  return this.find({ userId })
    .sort({ timestamp: -1 })
    .limit(limit);
};

transactionSchema.statics.getTokenTransactions = async function(tokenMintAddress, limit = 50) {
  return this.find({ tokenMintAddress })
    .sort({ timestamp: -1 })
    .limit(limit);
};

transactionSchema.statics.getUserPortfolio = async function(userId) {
  const transactions = await this.find({ userId });
  const portfolio = {};

  transactions.forEach(tx => {
    if (!portfolio[tx.tokenMintAddress]) {
      portfolio[tx.tokenMintAddress] = {
        totalBought: 0,
        totalSold: 0,
        averageBuyPrice: 0,
        averageSellPrice: 0
      };
    }

    const token = portfolio[tx.tokenMintAddress];
    if (tx.type === 'buy') {
      token.totalBought += tx.amount;
      token.averageBuyPrice = (token.averageBuyPrice * (token.totalBought - tx.amount) + tx.price * tx.amount) / token.totalBought;
    } else {
      token.totalSold += tx.amount;
      token.averageSellPrice = (token.averageSellPrice * (token.totalSold - tx.amount) + tx.price * tx.amount) / token.totalSold;
    }
  });

  return portfolio;
};

const Transaction = mongoose.model('Transaction', transactionSchema);

module.exports = Transaction;