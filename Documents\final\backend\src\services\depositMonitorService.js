/**
 * Deposit Monitor Service
 * Periodically checks for new deposits to the platform wallet
 */

const solanaService = require('./solanaService');
const logger = require('../utils/logger');

class DepositMonitorService {
  constructor() {
    this.isRunning = false;
    this.monitorInterval = null;
    this.intervalMs = 60000; // Check every minute by default
  }

  /**
   * Start monitoring for deposits
   * @param {number} intervalMs - Interval in milliseconds (default: 60000)
   */
  start(intervalMs = 60000) {
    if (this.isRunning) {
      logger.warn('Deposit monitor is already running');
      return;
    }

    this.intervalMs = intervalMs;
    this.isRunning = true;

    logger.info(`Starting deposit monitor service (interval: ${intervalMs}ms)`);

    // Run immediately on start
    this.checkDeposits();

    // Then set up interval
    this.monitorInterval = setInterval(() => {
      this.checkDeposits();
    }, this.intervalMs);
  }

  /**
   * Stop monitoring for deposits
   */
  stop() {
    if (!this.isRunning) {
      logger.warn('Deposit monitor is not running');
      return;
    }

    logger.info('Stopping deposit monitor service');

    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }

    this.isRunning = false;
  }

  /**
   * Check for new deposits
   */
  async checkDeposits() {
    try {
      logger.info('Checking for new deposits...');

      const deposits = await solanaService.monitorDeposits();

      if (deposits.length > 0) {
        logger.info(`Processed ${deposits.length} new deposits`);
        
        // Log each deposit
        deposits.forEach(deposit => {
          logger.info(`Deposit: ${deposit.amount} ${deposit.token} for user ${deposit.userId}`);
        });
      } else {
        logger.info('No new deposits found');
      }
    } catch (error) {
      logger.error(`Error checking for deposits: ${error.message}`);
    }
  }
}

// Create singleton instance
const depositMonitorService = new DepositMonitorService();

module.exports = depositMonitorService;
