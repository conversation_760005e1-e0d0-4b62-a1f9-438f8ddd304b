/* Trading Page Styles - All classes prefixed with tp- to avoid conflicts */
.tp-trading-page {
  padding-top: 70px;
  min-height: 100vh;
  background-color: #000000;
  color: #ffffff;
}

.tp-trading-container {
  max-width: 100vw;
  margin: 0 auto;
  padding: 20px;
}

/* Trading Header */
.tp-trading-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  border-bottom: 1px solid #333;
}

.tp-header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.tp-back-button {
  display: flex;
  align-items: center;
  gap: 5px;
  background: transparent;
  border: none;
  color: #aaa;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 5px;
  transition: all 0.3s;
}

.tp-back-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.tp-token-name {
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0;
  color: #ffffff;
  font-family: Arial, sans-serif;
}

.tp-token-price-info {
  text-align: right;
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 5px;
}

.tp-current-price {
  font-size: 1.8rem;
  font-weight: 600;
  color: #ffffff;
  font-family: Arial, sans-serif;
}

.tp-price-change {
  font-size: 0.9rem;
  font-weight: 400;
}

.tp-token-volume {
  font-size: 0.9rem;
  color: #aaaaaa;
  font-family: Arial, sans-serif;
}

.tp-green {
  color: #00C853;
}

.tp-red {
  color: #FF3D00;
}

/* Trading Content */
.tp-trading-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.tp-trading-main {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

/* Chart Styles */
.tp-trading-chart {
  background-color: #111;
  border-radius: 10px;
  padding: 20px;
  border: 1px solid #333;
}

.tp-chart-container {
  margin-bottom: 20px;
}

.tp-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.tp-chart-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
}

.tp-chart-timeframes {
  display: flex;
  gap: 5px;
}

.tp-timeframe-btn {
  background-color: #1e1e1e;
  border: none;
  color: #aaa;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s;
}

.tp-timeframe-btn:hover,
.tp-timeframe-btn.tp-active {
  background-color: #333;
  color: white;
}

.tp-tradingview-chart {
  height: 400px;
  width: 100%;
  position: relative;
  background-color: #131722;
  border-radius: 5px;
  overflow: hidden;
}

.tp-tradingview-widget {
  height: 100%;
  width: 100%;
}

.tp-chart-placeholder {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.tp-chart-placeholder-header {
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
  background-color: #1e222d;
  color: #d1d4dc;
  font-size: 0.9rem;
}

.tp-chart-placeholder-content {
  flex: 1;
  position: relative;
  padding: 10px 0;
}

.tp-chart-candles {
  height: 80%;
  width: 100%;
  position: relative;
}

.tp-chart-candle {
  position: absolute;
  bottom: 0;
  width: 8px;
  margin-left: -4px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tp-candle-up {
  background-color: #26a69a;
}

.tp-candle-down {
  background-color: #ef5350;
}

.tp-candle-wick {
  width: 1px;
  background-color: inherit;
  position: absolute;
  bottom: 100%;
}

.tp-chart-volume {
  height: 20%;
  width: 100%;
  position: relative;
  border-top: 1px solid #2a2e39;
  margin-top: 10px;
  padding-top: 10px;
}

.tp-volume-bar {
  position: absolute;
  bottom: 0;
  width: 8px;
  margin-left: -4px;
}

.tp-volume-up {
  background-color: rgba(38, 166, 154, 0.5);
}

.tp-volume-down {
  background-color: rgba(239, 83, 80, 0.5);
}

/* Token Stats */
.tp-token-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #333;
}

.tp-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tp-stat-label {
  font-size: 0.8rem;
  color: #aaa;
  margin-bottom: 5px;
}

.tp-stat-value {
  font-size: 1rem;
  font-weight: 500;
}

/* Trading Panel */
.tp-trading-panel {
  background-color: #111;
  border-radius: 10px;
  padding: 20px;
  border: 1px solid #333;
}

.tp-panel-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #333;
}

.tp-panel-tab {
  flex: 1;
  background: transparent;
  border: none;
  color: #aaa;
  padding: 10px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.tp-panel-tab.tp-active {
  color: white;
}

.tp-panel-tab.tp-active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
}

.tp-trading-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.tp-form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.tp-form-group label {
  font-size: 0.9rem;
  color: #aaa;
}

.tp-form-group input {
  padding: 10px;
  background-color: #1e1e1e;
  border: 1px solid #333;
  border-radius: 5px;
  color: white;
  font-size: 1rem;
}

.tp-form-group input:focus {
  outline: none;
  border-color: #FF6B00;
}

.tp-total-amount {
  padding: 10px;
  background-color: #1e1e1e;
  border: 1px solid #333;
  border-radius: 5px;
  color: white;
  font-size: 1rem;
  font-weight: 500;
}

.tp-submit-btn {
  padding: 12px;
  border: none;
  border-radius: 5px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 10px;
  transition: all 0.3s;
}

.tp-buy-btn {
  background: linear-gradient(90deg, #00C853, #00796B);
}

.tp-sell-btn {
  background: linear-gradient(90deg, #FF3D00, #C62828);
}

.tp-submit-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.tp-wallet-balance {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #333;
}

.tp-balance-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.tp-balance-label {
  color: #aaa;
  font-size: 0.9rem;
}

.tp-balance-value {
  font-weight: 500;
}

/* Token Description */
.tp-token-description {
  background-color: #111;
  border-radius: 10px;
  padding: 20px;
  border: 1px solid #333;
}

.tp-token-description h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2rem;
  font-weight: 500;
}

.tp-token-description p {
  color: #ccc;
  line-height: 1.6;
  margin: 0;
}

/* Comments Section */
.tp-comments-section {
  background-color: #111;
  border-radius: 10px;
  padding: 20px;
  border: 1px solid #333;
}

.tp-comments-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.2rem;
  font-weight: 500;
}

.tp-comment-form {
  margin-bottom: 30px;
}

.tp-comment-form textarea {
  width: 100%;
  height: 100px;
  padding: 15px;
  background-color: #1e1e1e;
  border: 1px solid #333;
  border-radius: 5px;
  color: white;
  font-size: 1rem;
  resize: none;
  margin-bottom: 10px;
}

.tp-comment-form textarea:focus {
  outline: none;
  border-color: #FF6B00;
}

.tp-comment-btn {
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.tp-comment-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.tp-comments-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.tp-comment {
  background-color: #1e1e1e;
  border-radius: 8px;
  padding: 15px;
}

.tp-comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.tp-comment-user {
  font-weight: 500;
}

.tp-comment-time {
  font-size: 0.8rem;
  color: #777;
}

.tp-comment-text {
  color: #ccc;
  line-height: 1.5;
  margin-bottom: 10px;
}

.tp-comment-actions {
  display: flex;
  justify-content: flex-end;
}

.tp-like-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  background: transparent;
  border: none;
  color: #aaa;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.3s;
}

.tp-like-btn:hover {
  color: #FF6B00;
}

/* Gradient text utility class */
.tp-gradient-text {
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Responsive styles */
@media (max-width: 900px) {
  .tp-trading-main {
    grid-template-columns: 1fr;
  }

  .tp-trading-header {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
  }

  .tp-token-price-info {
    text-align: right;
  }
}

@media (max-width: 600px) {
  .tp-trading-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .tp-token-price-info {
    text-align: left;
    margin-top: 10px;
  }
}
