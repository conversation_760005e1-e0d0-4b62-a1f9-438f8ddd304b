/**
 * Token Holdings Controller
 * Handles API requests for token holdings data
 */
const TokenHoldingsDAO = require('../db/tokenHoldingsDAO');
const { getErrorResponse, getSuccessResponse } = require('../utils/responseUtils');

/**
 * Get top holders for a specific token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getTopHolders(req, res) {
  try {
    const { symbol } = req.params;
    const limit = parseInt(req.query.limit, 10) || 20;
    const offset = parseInt(req.query.offset, 10) || 0;
    
    if (!symbol) {
      return getErrorResponse(res, 'Token symbol is required', new Error('Missing symbol parameter'), 400);
    }
    
    const holders = await TokenHoldingsDAO.getTopHoldersBySymbol(symbol.toUpperCase(), limit, offset);
    const holderCount = await TokenHoldingsDAO.getHolderCount(symbol.toUpperCase());
    const tokenSupply = await TokenHoldingsDAO.getTokenSupply(symbol.toUpperCase());
    
    return getSuccessResponse(res, {
      symbol: symbol.toUpperCase(),
      holders,
      holderCount,
      tokenSupply
    }, 'Top holders retrieved successfully');
  } catch (error) {
    return getErrorResponse(res, 'Failed to get top holders', error);
  }
}

/**
 * Get token holdings for a specific user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getUserHoldings(req, res) {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      return getErrorResponse(res, 'User ID is required', new Error('Missing userId parameter'), 400);
    }
    
    const holdings = await TokenHoldingsDAO.getHoldingsByUserId(userId);
    
    return getSuccessResponse(res, {
      userId,
      holdings
    }, 'User holdings retrieved successfully');
  } catch (error) {
    return getErrorResponse(res, 'Failed to get user holdings', error);
  }
}

/**
 * Update token holdings for a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function updateHolding(req, res) {
  try {
    const { userId, symbol } = req.params;
    const { balance, value, percentage, username } = req.body;
    
    if (!userId || !symbol) {
      return getErrorResponse(res, 'User ID and symbol are required', new Error('Missing required parameters'), 400);
    }
    
    if (balance === undefined || value === undefined) {
      return getErrorResponse(res, 'Balance and value are required', new Error('Missing required fields'), 400);
    }
    
    const holding = await TokenHoldingsDAO.upsertHolding({
      user_id: userId,
      username,
      symbol: symbol.toUpperCase(),
      balance: parseFloat(balance),
      value: parseFloat(value),
      percentage: percentage ? parseFloat(percentage) : 0
    });
    
    return getSuccessResponse(res, {
      holding
    }, 'Holding updated successfully');
  } catch (error) {
    return getErrorResponse(res, 'Failed to update holding', error);
  }
}

/**
 * Update token supply information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function updateTokenSupply(req, res) {
  try {
    const { symbol } = req.params;
    const { totalSupply, circulatingSupply } = req.body;
    
    if (!symbol) {
      return getErrorResponse(res, 'Token symbol is required', new Error('Missing symbol parameter'), 400);
    }
    
    if (totalSupply === undefined || circulatingSupply === undefined) {
      return getErrorResponse(res, 'Total supply and circulating supply are required', new Error('Missing required fields'), 400);
    }
    
    const supply = await TokenHoldingsDAO.upsertTokenSupply({
      symbol: symbol.toUpperCase(),
      total_supply: parseFloat(totalSupply),
      circulating_supply: parseFloat(circulatingSupply)
    });
    
    return getSuccessResponse(res, {
      supply
    }, 'Token supply updated successfully');
  } catch (error) {
    return getErrorResponse(res, 'Failed to update token supply', error);
  }
}

/**
 * Delete a token holding
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function deleteHolding(req, res) {
  try {
    const { userId, symbol } = req.params;
    
    if (!userId || !symbol) {
      return getErrorResponse(res, 'User ID and symbol are required', new Error('Missing required parameters'), 400);
    }
    
    const deleted = await TokenHoldingsDAO.deleteHolding(userId, symbol.toUpperCase());
    
    if (!deleted) {
      return getErrorResponse(res, 'Holding not found', new Error('Holding not found'), 404);
    }
    
    return getSuccessResponse(res, {
      deleted: true
    }, 'Holding deleted successfully');
  } catch (error) {
    return getErrorResponse(res, 'Failed to delete holding', error);
  }
}

/**
 * Seed sample data for testing
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function seedSampleData(req, res) {
  try {
    if (process.env.NODE_ENV === 'production') {
      return getErrorResponse(res, 'Seeding is not allowed in production', new Error('Operation not allowed'), 403);
    }
    
    await TokenHoldingsDAO.seedSampleData();
    
    return getSuccessResponse(res, {
      seeded: true
    }, 'Sample data seeded successfully');
  } catch (error) {
    return getErrorResponse(res, 'Failed to seed sample data', error);
  }
}

module.exports = {
  getTopHolders,
  getUserHoldings,
  updateHolding,
  updateTokenSupply,
  deleteHolding,
  seedSampleData
};
