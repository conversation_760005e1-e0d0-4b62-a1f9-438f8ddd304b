const User = require('../models/User');
const authService = require('../services/authService');
const otpService = require('../services/otpService');
const { validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const path = require('path');
const emailService = require('../services/emailService');
const verificationService = require('../services/verificationService');
const fallbackDataService = require('../services/fallbackDataService');

// Check if MongoDB is available
const isMongoDBAvailable = mongoose.connection.readyState === 1;

// Register a new user
exports.register = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Validation errors:', errors.array());
      return res.status(400).json({ message: 'Validation errors', errors: errors.array() });
    }

    console.log('Register request:', req.body);

    const { username, email, password } = req.body;

    // Check if MongoDB is available
    if (mongoose.connection.readyState === 1) {
      // MongoDB is available, use it
      try {
        // Check if user exists
        let user = await User.findOne({ email });
        if (user) {
          console.log('User already exists:', email);
          return res.status(400).json({ message: 'User already exists' });
        }

        // SIMPLIFIED VERSION - NO EMAIL VERIFICATION
        console.log('Creating user directly without verification');

        // Create user
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);

        user = new User({
          username,
          email,
          password: hashedPassword,
          isVerified: true
        });

        await user.save();
        console.log('User created successfully:', user._id);

        // Generate JWT token
        const token = jwt.sign(
          { id: user._id, email: user.email, role: user.role || 'user' },
          process.env.JWT_SECRET || 'your-secret-key',
          { expiresIn: '24h' }
        );

        return res.status(201).json({
          message: 'User registered successfully',
          token,
          user: {
            id: user._id,
            username: user.username,
            email: user.email
          }
        });
      } catch (saveError) {
        console.error('Error saving user:', saveError);

        // Check for MongoDB validation errors
        if (saveError.name === 'ValidationError') {
          const messages = Object.values(saveError.errors).map(err => err.message);
          return res.status(400).json({
            message: 'Validation error',
            errors: messages
          });
        }

        // Check for MongoDB duplicate key error
        if (saveError.code === 11000) {
          const field = Object.keys(saveError.keyPattern)[0];
          return res.status(400).json({
            message: `${field} already exists`
          });
        }

        // Fall back to in-memory storage
        console.log('Falling back to in-memory storage');
        const existingUser = fallbackDataService.getUserByEmail(email);
        if (existingUser) {
          console.log('User already exists in fallback storage:', email);
          return res.status(400).json({ message: 'User already exists' });
        }

        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);

        const user = fallbackDataService.createUser({
          username,
          email,
          password: hashedPassword,
          isVerified: true
        });
        console.log('User created successfully in fallback storage:', user._id);

        // Generate JWT token
        const token = jwt.sign(
          { id: user._id, email: user.email, role: user.role || 'user' },
          process.env.JWT_SECRET || 'your-secret-key',
          { expiresIn: '24h' }
        );

        return res.status(201).json({
          message: 'User registered successfully',
          token,
          user: {
            id: user._id,
            username: user.username,
            email: user.email
          }
        });
      }
    } else {
      // MongoDB is not available, use fallback
      console.log('MongoDB not available, using fallback storage');
      const existingUser = fallbackDataService.getUserByEmail(email);
      if (existingUser) {
        console.log('User already exists in fallback storage:', email);
        return res.status(400).json({ message: 'User already exists' });
      }

      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      const user = fallbackDataService.createUser({
        username,
        email,
        password: hashedPassword,
        isVerified: true
      });
      console.log('User created successfully in fallback storage:', user._id);

      // Generate JWT token
      const token = jwt.sign(
        { id: user._id, email: user.email, role: user.role || 'user' },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '24h' }
      );

      return res.status(201).json({
        message: 'User registered successfully',
        token,
        user: {
          id: user._id,
          username: user.username,
          email: user.email
        }
      });
    }
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Login a user
exports.login = async (req, res) => {
  try {
    console.log('Login attempt with:', req.body.email);

    // Special bypass for specific credentials
    if (req.body.email === '<EMAIL>' &&
        req.body.password === '7973425726Rishab') {

      console.log('Special login bypass activated');

      let user;
      
      // Check if MongoDB is available
      if (mongoose.connection.readyState === 1) {
        try {
          // Find the user without validation
          user = await User.findOne({ email: req.body.email });

          if (!user) {
            // Create user if doesn't exist (for development purposes)
            console.log('Creating development user in MongoDB');

            // Hash the password
            const salt = await bcrypt.genSalt(10);
            const hashedPassword = await bcrypt.hash('7973425726Rishab', salt);

            // Create a new user with the required username field
            const newUser = new User({
              username: 'Rishab',  // Set a username to pass validation
              email: '<EMAIL>',
              password: hashedPassword,
              isVerified: true,
              role: 'user'
            });

            try {
              user = await newUser.save();
              console.log('Development user created successfully in MongoDB:', user._id);
            } catch (createErr) {
              console.error('Error creating dev user in MongoDB:', createErr);
              // Fall back to in-memory storage
              user = fallbackDataService.createUser({
                username: 'Rishab',
                email: '<EMAIL>',
                password: 'hashed-password', // Not actually used for verification
                isVerified: true,
                role: 'user'
              });
              console.log('Development user created in fallback storage:', user._id);
            }
          }
        } catch (mongoError) {
          console.error('MongoDB error during login:', mongoError);
          // Fall back to in-memory storage
          user = fallbackDataService.getUserByEmail('<EMAIL>');
          if (!user) {
            user = fallbackDataService.createUser({
              username: 'Rishab',
              email: '<EMAIL>',
              password: 'hashed-password', // Not actually used for verification
              isVerified: true,
              role: 'user'
            });
            console.log('Development user created in fallback storage:', user._id);
          }
        }
      } else {
        // MongoDB is not available, use fallback
        console.log('MongoDB not available, using fallback storage for login');
        user = fallbackDataService.getUserByEmail('<EMAIL>');
        if (!user) {
          user = fallbackDataService.createUser({
            username: 'Rishab',
            email: '<EMAIL>',
            password: 'hashed-password', // Not actually used for verification
            isVerified: true,
            role: 'user'
          });
          console.log('Development user created in fallback storage:', user._id);
        }
      }

      // Generate JWT token
      const token = jwt.sign(
        { id: user._id, email: user.email, role: user.role || 'user' },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: process.env.JWT_EXPIRY || '24h' }
      );

      // Update last login time
      if (mongoose.connection.readyState === 1) {
        try {
          // Update last login time directly with MongoDB driver
          await mongoose.connection.collection('users').updateOne(
            { _id: user._id },
            {
              $set: {
                lastLogin: new Date(),
                username: user.username || 'Rishab'  // Ensure username is set
              }
            }
          );
          console.log('User data updated in MongoDB database');
        } catch (updateErr) {
          console.error('Database update error:', updateErr);
          // Continue anyway as this is not critical
        }
      } else if (typeof user === 'object') {
        // Update in fallback storage
        user.lastLogin = new Date();
        user.username = user.username || 'Rishab';
        console.log('User data updated in fallback storage');
      }

      return res.json({
        success: true,
        token,
        user: {
          id: user._id,
          username: user.username || 'Rishab',
          email: user.email,
          role: user.role || 'user'
        }
      });
    }

    // Regular login flow
    const { email, password } = req.body;

    // Validate required fields
    if (!email || !password) {
      return res.status(400).json({ message: 'Please provide email and password' });
    }

    // Check if MongoDB is available
    if (mongoose.connection.readyState === 1) {
      try {
        // Find user by email
        const user = await User.findOne({ email });
        if (!user) {
          return res.status(401).json({ message: 'Invalid credentials' });
        }

        // Check password
        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
          return res.status(401).json({ message: 'Invalid credentials' });
        }

        // Check if user is verified
        if (!user.isVerified) {
          return res.status(401).json({ message: 'Please verify your email before logging in' });
        }

        // Generate JWT token
        const token = jwt.sign(
          { id: user._id, email: user.email, role: user.role || 'user' },
          process.env.JWT_SECRET || 'your-secret-key',
          { expiresIn: process.env.JWT_EXPIRY || '24h' }
        );

        // Update last login time
        try {
          user.lastLogin = new Date();
          await user.save();
        } catch (updateErr) {
          console.error('Error updating last login time:', updateErr);
          // Continue anyway as this is not critical
        }

        return res.json({
          success: true,
          token,
          user: {
            id: user._id,
            username: user.username,
            email: user.email,
            role: user.role || 'user'
          }
        });
      } catch (mongoError) {
        console.error('MongoDB error during regular login:', mongoError);
        // Fall back to in-memory storage
        const user = fallbackDataService.getUserByEmail(email);
        if (!user) {
          return res.status(401).json({ message: 'Invalid credentials' });
        }

        // Generate JWT token
        const token = jwt.sign(
          { id: user._id, email: user.email, role: user.role || 'user' },
          process.env.JWT_SECRET || 'your-secret-key',
          { expiresIn: process.env.JWT_EXPIRY || '24h' }
        );

        // Update last login time in fallback storage
        user.lastLogin = new Date();

        return res.json({
          success: true,
          token,
          user: {
            id: user._id,
            username: user.username,
            email: user.email,
            role: user.role || 'user'
          }
        });
      }
    } else {
      // MongoDB is not available, use fallback
      console.log('MongoDB not available, using fallback storage for regular login');
      const user = fallbackDataService.getUserByEmail(email);
      if (!user) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Generate JWT token
      const token = jwt.sign(
        { id: user._id, email: user.email, role: user.role || 'user' },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: process.env.JWT_EXPIRY || '24h' }
      );

      // Update last login time in fallback storage
      user.lastLogin = new Date();

      return res.json({
        success: true,
        token,
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          role: user.role || 'user'
        }
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Verify a user's email
exports.verifyEmail = async (req, res) => {
  try {
    const { token } = req.params;
    
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    // Check if MongoDB is available
    if (mongoose.connection.readyState === 1) {
      try {
        // Find user and update verification status
        const user = await User.findById(decoded.id);
        
        if (!user) {
          return res.status(404).json({ message: 'User not found' });
        }
        
        user.isVerified = true;
        await user.save();
        
        return res.json({ message: 'Email verified successfully' });
      } catch (mongoError) {
        console.error('MongoDB error during email verification:', mongoError);
        return res.status(500).json({ message: 'Server error', error: mongoError.message });
      }
    } else {
      // MongoDB is not available, use fallback
      console.log('MongoDB not available, using fallback storage for email verification');
      const user = fallbackDataService.getUserById(decoded.id);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      
      user.isVerified = true;
      
      return res.json({ message: 'Email verified successfully' });
    }
  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get current user
exports.getCurrentUser = async (req, res) => {
  try {
    // Check if MongoDB is available
    if (mongoose.connection.readyState === 1) {
      try {
        const user = await User.findById(req.user.id).select('-password');
        if (!user) {
          return res.status(404).json({ message: 'User not found' });
        }
        
        return res.json(user);
      } catch (mongoError) {
        console.error('MongoDB error getting current user:', mongoError);
        // Fall back to in-memory storage
        const user = fallbackDataService.getUserById(req.user.id);
        if (!user) {
          return res.status(404).json({ message: 'User not found' });
        }
        
        // Remove password from response
        const { password, ...userWithoutPassword } = user;
        
        return res.json(userWithoutPassword);
      }
    } else {
      // MongoDB is not available, use fallback
      console.log('MongoDB not available, using fallback storage for getting current user');
      const user = fallbackDataService.getUserById(req.user.id);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      
      // Remove password from response
      const { password, ...userWithoutPassword } = user;
      
      return res.json(userWithoutPassword);
    }
  } catch (error) {
    console.error('Get current user error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Update user profile
exports.updateProfile = async (req, res) => {
  try {
    const { username, email } = req.body;
    
    // Check if MongoDB is available
    if (mongoose.connection.readyState === 1) {
      try {
        // Find user and update profile
        const user = await User.findById(req.user.id);
        
        if (!user) {
          return res.status(404).json({ message: 'User not found' });
        }
        
        if (username) user.username = username;
        if (email) user.email = email;
        
        await user.save();
        
        return res.json({ message: 'Profile updated successfully', user });
      } catch (mongoError) {
        console.error('MongoDB error updating profile:', mongoError);
        // Fall back to in-memory storage
        const user = fallbackDataService.getUserById(req.user.id);
        if (!user) {
          return res.status(404).json({ message: 'User not found' });
        }
        
        if (username) user.username = username;
        if (email) user.email = email;
        
        return res.json({ message: 'Profile updated successfully', user });
      }
    } else {
      // MongoDB is not available, use fallback
      console.log('MongoDB not available, using fallback storage for updating profile');
      const user = fallbackDataService.getUserById(req.user.id);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      
      if (username) user.username = username;
      if (email) user.email = email;
      
      return res.json({ message: 'Profile updated successfully', user });
    }
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
