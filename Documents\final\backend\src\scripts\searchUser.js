/**
 * <PERSON><PERSON>t to search for a specific user in PostgreSQL
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

// Create a log file
const logFile = path.join(__dirname, 'search_user_log.txt');
fs.writeFileSync(logFile, `Search User Log - ${new Date().toISOString()}\n\n`);

// Function to append to log file
function log(message) {
  const logMessage = typeof message === 'object' ? JSON.stringify(message, null, 2) : message;
  fs.appendFileSync(logFile, logMessage + '\n');
  console.log(message);
}

// Main function
async function searchUser(searchTerm) {
  log(`Searching for user with term: ${searchTerm}`);

  try {
    // Connect to PostgreSQL
    log('Connecting to PostgreSQL...');
    
    const pool = new Pool({
      host: process.env.POSTGRESQL_HOST,
      port: process.env.POSTGRESQL_PORT,
      database: process.env.POSTGRESQL_DATABASE,
      user: process.env.POSTGRESQL_USER,
      password: process.env.POSTGRESQL_PASSWORD
    });
    
    log('Connected to PostgreSQL');
    
    // Search for user in user_balances table
    const query = {
      text: 'SELECT * FROM user_balances WHERE user_id LIKE $1',
      values: [`%${searchTerm}%`]
    };
    
    log(`Executing query: ${query.text} with values: ${query.values}`);
    
    const result = await pool.query(query);
    
    log(`Query completed. Found ${result.rows.length} results:`);
    log(result.rows);
    
    // Close the connection
    await pool.end();
    log('PostgreSQL connection closed');
    
    log(`Log file written to: ${logFile}`);
    return result.rows;
  } catch (error) {
    log(`Error searching for user: ${error.message}`);
    log(error.stack);
    throw error;
  }
}

// Get search term from command line arguments
const searchTerm = process.argv[2] || 'ri_shab';

// Run the search
searchUser(searchTerm)
  .then(() => {
    console.log('Search completed');
    process.exit(0);
  })
  .catch(error => {
    console.error(`Search failed: ${error.message}`);
    process.exit(1);
  });
