import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const meter = /*#__PURE__*/ defineChain({
    id: 82,
    name: 'Meter',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: '<PERSON><PERSON>',
    },
    rpcUrls: {
        default: { http: ['https://rpc.meter.io'] },
    },
    blockExplorers: {
        default: {
            name: 'MeterScan',
            url: 'https://scan.meter.io',
        },
    },
});
//# sourceMappingURL=meter.js.map