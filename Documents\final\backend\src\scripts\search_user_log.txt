Search User Log - 2025-04-19T16:57:10.652Z

Searching for user with term: ri_shab
Connecting to PostgreSQL...
Connected to PostgreSQL
Executing query: SELECT * FROM user_balances WHERE user_id LIKE $1 with values: %ri_shab%
Error searching for user: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string
    at C:\Users\<USER>\Documents\project V\wb-swap\backend\node_modules\pg-pool\index.js:45:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async searchUser (C:\Users\<USER>\Documents\project V\wb-swap\backend\src\scripts\searchUser.js:47:20)
