PORT=3001
MONGODB_URI=mongodb+srv://swaptradepvt:<EMAIL>/?retryWrites=true&w=majority&appName=swap
NODE_ENV=development

# Redis Cloud Configuration (Upstash)
UPSTASH_REDIS_REST_URL=https://helpful-mite-37766.upstash.io
UPSTASH_REDIS_REST_TOKEN=AZOGAAIjcDFkZTVmYzdjN2EyM2I0ZmI5ODQ1YTc2YjUwZjEyZDMwMHAxMA

# Solana Configuration
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_NETWORK=mainnet-beta

# AWS Configuration
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=UvWsCmfCK2rqF2sXuHv3Kv4aG7f/DLVg6/7/EwbQ
AWS_REGION=ap-south-1
AWS_S3_BUCKET=swap-trade-files-mumbai

# JWT Configuration
JWT_SECRET=ae47f381281e5ac7d04fa367be5af88ecdffd7dcecc1a2eabe38908aefeebe18
JWT_EXPIRES_IN=24h

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100