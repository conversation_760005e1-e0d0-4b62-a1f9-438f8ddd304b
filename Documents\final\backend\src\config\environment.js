/**
 * Environment Configuration
 *
 * This file centralizes all environment-related configuration.
 */

// Load environment variables
require('dotenv').config();

// Environment detection
const IS_PROD = process.env.NODE_ENV === 'production';
const IS_DEV = process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;

// Feature flags
const FEATURES = {
  USE_REAL_SOLANA: process.env.USE_REAL_SOLANA === 'true',
  USE_REAL_ORDER_MATCHING: process.env.USE_REAL_ORDER_MATCHING === 'true',
  STORE_TRADES_ON_CHAIN: process.env.STORE_TRADES_ON_CHAIN === 'true',
  USE_HARDWARE_WALLET: process.env.USE_HARDWARE_WALLET === 'true',
  REQUIRE_EMAIL_VERIFICATION: process.env.REQUIRE_EMAIL_VERIFICATION === 'true',
  ENFORCE_RATE_LIMITS: process.env.ENFORCE_RATE_LIMITS === 'true'
};

// Helper function to check if a feature is enabled
function isFeatureEnabled(feature) {
  return FEATURES[feature] === true;
}

// Configuration object
const config = {
  // Server configuration
  server: {
    port: parseInt(process.env.PORT || '3001', 10),
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000'
  },

  // Database configuration
  database: {
    mongodb: {
      uri: process.env.MONGODB_URI,
      dbName: process.env.MONGODB_DB_NAME || 'swap'
    },
    postgresql: {
      host: process.env.POSTGRES_HOST || 'localhost',
      port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
      user: process.env.POSTGRES_USER || 'postgres',
      password: process.env.POSTGRES_PASSWORD || 'postgres',
      database: process.env.POSTGRES_DB || 'swap-dev'
    },
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
      password: process.env.REDIS_PASSWORD || ''
    }
  },

  // Solana configuration
  solana: {
    network: process.env.SOLANA_NETWORK || 'devnet',
    rpcUrl: process.env.SOLANA_RPC_URL || 'https://api.devnet.solana.com',
    programId: process.env.SOLANA_PROGRAM_ID || 'CVHXGtveLFr54yPsc4R6PgUhLLYCY5YQbXiqcLqEdkcu',
    walletPath: process.env.SOLANA_WALLET_PATH || './scripts/solana-keypair.json'
  },

  // Platform wallet configuration
  platformWallet: {
    address: process.env.PLATFORM_WALLET_ADDRESS,
    source: process.env.WALLET_SOURCE || 'file',
    path: process.env.WALLET_PATH || './scripts/platform-wallet.json'
  },

  // Order matching engine configuration
  orderMatchingEngine: {
    host: process.env.ORDER_MATCHING_ENGINE_HOST || 'localhost',
    port: parseInt(process.env.ORDER_MATCHING_ENGINE_PORT || '9002', 10)
  },

  // Fee configuration
  fees: {
    tokenCreationFee: parseFloat(process.env.TOKEN_CREATION_FEE || '0'),
    tradingFeePercentage: parseFloat(process.env.TRADING_FEE_PERCENTAGE || '1.0'),
    graduationFee: parseFloat(process.env.GRADUATION_FEE || '2.3')
  }
};

// Export configuration
module.exports = {
  IS_PROD,
  IS_DEV,
  FEATURES,
  isFeatureEnabled,
  config
};
