-- Fix transactions table schema to match Sequelize model

-- Check if userId column exists, if not add it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'transactions' 
        AND column_name = 'userId'
    ) THEN
        -- Add userId column
        ALTER TABLE transactions ADD COLUMN "userId" VARCHAR(255);
        
        -- Copy data from user_id to userId if user_id exists
        IF EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'transactions' 
            AND column_name = 'user_id'
        ) THEN
            UPDATE transactions SET "userId" = user_id;
        END IF;
        
        -- Create index on userId
        CREATE INDEX IF NOT EXISTS idx_transactions_userId ON transactions("userId");
    END IF;
END
$$;

-- Make sure all required columns from the Sequelize model exist
ALTER TABLE transactions 
    ADD COLUMN IF NOT EXISTS "type" VARCHAR(50),
    ADD COLUMN IF NOT EXISTS "txId" VARCHAR(255),
    ADD COLUMN IF NOT EXISTS "signature" VARCHAR(255),
    ADD COLUMN IF NOT EXISTS "walletAddress" VARCHAR(255),
    ADD COLUMN IF NOT EXISTS "tokenMint" VARCHAR(255),
    ADD COLUMN IF NOT EXISTS "token" VARCHAR(50),
    ADD COLUMN IF NOT EXISTS "amount" DECIMAL(24, 9),
    ADD COLUMN IF NOT EXISTS "tokenAmount" VARCHAR(255),
    ADD COLUMN IF NOT EXISTS "solAmount" VARCHAR(255),
    ADD COLUMN IF NOT EXISTS "feeAmount" VARCHAR(255),
    ADD COLUMN IF NOT EXISTS "status" VARCHAR(50),
    ADD COLUMN IF NOT EXISTS "blockTime" TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS "metadata" JSONB DEFAULT '{}'::jsonb,
    ADD COLUMN IF NOT EXISTS "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ADD COLUMN IF NOT EXISTS "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;

-- Create indexes for all fields used in the Sequelize model
CREATE INDEX IF NOT EXISTS idx_transactions_walletAddress ON transactions("walletAddress");
CREATE INDEX IF NOT EXISTS idx_transactions_txId ON transactions("txId");
CREATE INDEX IF NOT EXISTS idx_transactions_tokenMint ON transactions("tokenMint");
CREATE INDEX IF NOT EXISTS idx_transactions_type_str ON transactions("type");
CREATE INDEX IF NOT EXISTS idx_transactions_status_str ON transactions("status");
CREATE INDEX IF NOT EXISTS idx_transactions_createdAt ON transactions("createdAt");
