const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const mongoose = require('mongoose');
const User = require('../models/User');
// const { Redis } = require('@upstash/redis'); // REMOVED - Redis no longer used

// Configuration
// In production, JWT_SECRET must be set in environment variables
const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET && process.env.NODE_ENV === 'production') {
  console.error('ERROR: JWT_SECRET environment variable is not set in production mode!');
  process.exit(1);
}

// Use shorter token expiration for production, longer for development
const ACCESS_TOKEN_EXPIRY = process.env.NODE_ENV === 'production' ? '30m' : '24h'; // 30 minutes in production, 24 hours in development
const REFRESH_TOKEN_EXPIRY = process.env.NODE_ENV === 'production' ? '24h' : '7d'; // 24 hours in production, 7 days in development

// Redis removed - using in-memory storage and MongoDB for token management
// const REDIS_URL = process.env.UPSTASH_REDIS_REST_URL;
// const REDIS_TOKEN = process.env.UPSTASH_REDIS_REST_TOKEN;

// if ((!REDIS_URL || !REDIS_TOKEN) && process.env.NODE_ENV === 'production') {
//   console.warn('WARNING: Redis credentials not set in production mode. Using in-memory storage which is not recommended!');
// }

// Connect to Redis for session storage - REMOVED
// let redisClient;
// let useRedis = false;

// In-memory storage (now primary storage)
const inMemoryTokenStore = new Map();

// Redis removed - using in-memory storage for token management
console.log('Using in-memory storage for secure session management');

// Create in-memory token management (replaces Redis)
const tokenClient = {
  get: async (key) => inMemoryTokenStore.get(key) || null,
  set: async (key, value, options) => {
    inMemoryTokenStore.set(key, value);
    // Handle expiration if specified
    if (options && options.ex) {
      setTimeout(() => inMemoryTokenStore.delete(key), options.ex * 1000);
    }
    return true;
  },
  del: async (key) => {
    inMemoryTokenStore.delete(key);
    return true;
  },
  ping: async () => 'PONG'
};

// try {
//   // Use Upstash Redis client
//   redisClient = new Redis({
//     url: REDIS_URL,
//     token: REDIS_TOKEN
//   });
//   useRedis = true;
//   console.log('Connected to Upstash Redis for secure session management');

//   // Test Redis connection
//   redisClient.set('auth:test', 'test-connection')
//     .then(() => {
//       console.log('Redis auth connection test successful');
//       useRedis = true;
//     })
//     .catch(err => {
//       console.error('Redis auth connection test failed:', err);
//       console.warn('Falling back to in-memory session storage (not recommended for production)');
//       useRedis = false;

//       // Create in-memory fallback for Redis
//       redisClient = {
//         get: async (key) => inMemoryTokenStore.get(key) || null,
//         set: async (key, value, options) => {
//           inMemoryTokenStore.set(key, value);
//           // Handle expiration if specified
//           if (options && options.ex) {
//             setTimeout(() => inMemoryTokenStore.delete(key), options.ex * 1000);
//           }
//           return true;
//         },
//         del: async (key) => {
//           inMemoryTokenStore.delete(key);
//           return true;
//         },
//         ping: async () => 'PONG'
//       };
//     });
// } catch (error) {
//   console.error('Redis connection error:', error);
//   console.warn('Falling back to in-memory session storage (not recommended for production)');
//   useRedis = false;

//   // Create in-memory fallback for Redis
//   redisClient = {
//     get: async (key) => inMemoryTokenStore.get(key) || null,
//     set: async (key, value, options) => {
//       inMemoryTokenStore.set(key, value);
//       // Handle expiration if specified
//       if (options && options.ex) {
//         setTimeout(() => inMemoryTokenStore.delete(key), options.ex * 1000);
//       }
//       return true;
//     },
//     del: async (key) => {
//       inMemoryTokenStore.delete(key);
//       return true;
//     },
//     ping: async () => 'PONG'
//   };
// }

// Redis removed - token client is always available
// if (redisClient) {
//   // Test Redis connection periodically
//   const testRedisConnection = async () => {
//     try {
//       await redisClient.ping();
//       useRedis = true;
//       console.log('Redis connection is healthy');
//     } catch (err) {
//       console.error('Redis connection error:', err);
//       useRedis = false;
//     }
//   };

//   // Initial test
//   testRedisConnection();
// }

/**
 * Generate a secure random token
 * @param {number} bytes - Number of bytes for the token
 * @returns {string} - Hex string token
 */
const generateSecureToken = (bytes = 32) => {
  return crypto.randomBytes(bytes).toString('hex');
};

/**
 * Generate JWT access token
 * @param {Object} user - User object
 * @returns {string} - JWT token
 */
const generateAccessToken = (user) => {
  // Ensure we're using the most up-to-date profile picture
  // This is important when refreshing tokens after profile picture updates

  // Use the user's role or default to 'user'
  let role = user.role || 'user';
  let username = user.username;

  return jwt.sign(
    {
      id: user._id || user.id,
      email: user.email,
      username: username,
      role: role,
      profilePicture: user.profilePicture || null,
      // Add timestamp to prevent caching issues with profile picture
      updatedAt: Date.now()
    },
    JWT_SECRET,
    { expiresIn: ACCESS_TOKEN_EXPIRY }
  );
};

/**
 * Generate refresh token and store it
 * @param {Object} user - User object
 * @returns {string} - Refresh token
 */
const generateRefreshToken = async (user) => {
  const refreshToken = generateSecureToken();
  const userId = user._id || user.id;

  // Store refresh token with user ID in token client (in-memory)
  try {
    // Store in token client with expiration
    await tokenClient.set(
      `refresh_token:${refreshToken}`,
      userId.toString(),
      { ex: 60 * 60 * 24 * 7 } // 7 days in seconds
    );
  } catch (error) {
    console.error('Error storing refresh token in token client:', error);
    // Fallback to global storage
    global.refreshTokens = global.refreshTokens || {};
    global.refreshTokens[refreshToken] = {
      userId: userId.toString(),
      expires: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days in milliseconds
    };
  }

  // // Store refresh token with user ID
  // if (useRedis && redisClient) {
  //   try {
  //     // Store in Redis with expiration - Upstash Redis client has a different API
  //     await redisClient.set(
  //       `refresh_token:${refreshToken}`,
  //       userId.toString(),
  //       { ex: 60 * 60 * 24 * 7 } // 7 days in seconds
  //     );
  //   } catch (error) {
  //     console.error('Error storing refresh token in Redis:', error);
  //     // Fallback to in-memory storage
  //     useRedis = false;
  //   }
  // }

  // // Fallback: store in memory if Redis is not available
  // if (!useRedis) {
  //   // In a real production environment, you'd want to ensure Redis is available
  //   global.refreshTokens = global.refreshTokens || {};
  //   global.refreshTokens[refreshToken] = {
  //     userId: userId.toString(),
  //     expires: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days in milliseconds
  //   };
  // }

  return refreshToken;
};

/**
 * Verify refresh token and return user ID if valid
 * @param {string} refreshToken - Refresh token to verify
 * @returns {string|null} - User ID if valid, null otherwise
 */
const verifyRefreshToken = async (refreshToken) => {
  if (!refreshToken) return null;

  try {
    let userId;

    // Get from token client (in-memory)
    try {
      userId = await tokenClient.get(`refresh_token:${refreshToken}`);
    } catch (error) {
      console.error('Error getting refresh token from token client:', error);
    }

    // Fallback: get from global storage if token client failed
    if (!userId) {
      const tokenData = global.refreshTokens?.[refreshToken];
      if (tokenData && tokenData.expires > Date.now()) {
        userId = tokenData.userId;
      }
    }

    // if (useRedis && redisClient) {
    //   try {
    //     // Get from Redis
    //     userId = await redisClient.get(`refresh_token:${refreshToken}`);
    //   } catch (error) {
    //     console.error('Error getting refresh token from Redis:', error);
    //     // Fallback to in-memory storage
    //     useRedis = false;
    //   }
    // }

    // // Fallback: get from memory if Redis failed or is not available
    // if (!userId && !useRedis) {
    //   const tokenData = global.refreshTokens?.[refreshToken];
    //   if (tokenData && tokenData.expires > Date.now()) {
    //     userId = tokenData.userId;
    //   }
    // }

    if (!userId) return null;

    // Find user in database
    if (mongoose.connection.readyState === 1) {
      const user = await User.findById(userId);
      if (!user) return null;
      return user;
    } else {
      // Fallback for development
      return {
        _id: userId,
        id: userId,
        email: '<EMAIL>',
        username: 'User',
        role: 'user',
        profilePicture: null
      };
    }
  } catch (error) {
    console.error('Error verifying refresh token:', error);
    return null;
  }
};

/**
 * Invalidate a refresh token
 * @param {string} refreshToken - Refresh token to invalidate
 */
const invalidateRefreshToken = async (refreshToken) => {
  if (!refreshToken) return;

  try {
    // Delete from token client (in-memory)
    try {
      await tokenClient.del(`refresh_token:${refreshToken}`);
    } catch (error) {
      console.error('Error deleting refresh token from token client:', error);
    }

    // Always try to delete from global storage as well (fallback)
    if (global.refreshTokens && global.refreshTokens[refreshToken]) {
      delete global.refreshTokens[refreshToken];
    }

    // if (useRedis && redisClient) {
    //   try {
    //     // Delete from Redis
    //     await redisClient.del(`refresh_token:${refreshToken}`);
    //   } catch (error) {
    //     console.error('Error deleting refresh token from Redis:', error);
    //     // Fallback to in-memory storage
    //     useRedis = false;
    //   }
    // }

    // // Always try to delete from memory as well (in case we've fallen back)
    // if (global.refreshTokens && global.refreshTokens[refreshToken]) {
    //   delete global.refreshTokens[refreshToken];
    // }
  } catch (error) {
    console.error('Error invalidating refresh token:', error);
  }
};

/**
 * Invalidate all refresh tokens for a user
 * @param {string} userId - User ID
 */
const invalidateAllUserTokens = async (userId) => {
  if (!userId) return;

  try {
    // Find all tokens for this user and delete them from token client
    // Note: token client doesn't have keys() method, so we'll rely on global storage for this operation

    // Delete from global storage
    if (global.refreshTokens) {
      Object.keys(global.refreshTokens).forEach(token => {
        if (global.refreshTokens[token].userId === userId.toString()) {
          // Also delete from token client
          tokenClient.del(`refresh_token:${token}`).catch(err =>
            console.error('Error deleting token from token client:', err)
          );
          delete global.refreshTokens[token];
        }
      });
    }

    // if (useRedis && redisClient) {
    //   try {
    //     // Find all tokens for this user and delete them
    //     // This is a simplified approach - in production you might want a more efficient method
    //     const keys = await redisClient.keys('refresh_token:*');
    //     for (const key of keys) {
    //       const tokenUserId = await redisClient.get(key);
    //       if (tokenUserId === userId.toString()) {
    //         await redisClient.del(key);
    //       }
    //     }
    //   } catch (error) {
    //     console.error('Error invalidating all user tokens in Redis:', error);
    //     // Fallback to in-memory storage
    //     useRedis = false;
    //   }
    // }

    // // Always try to delete from memory as well (in case we've fallen back)
    // if (global.refreshTokens) {
    //   Object.keys(global.refreshTokens).forEach(token => {
    //     if (global.refreshTokens[token].userId === userId.toString()) {
    //       delete global.refreshTokens[token];
    //     }
    //   });
    // }
  } catch (error) {
    console.error('Error invalidating user tokens:', error);
  }
};

/**
 * Set authentication cookies
 * @param {Object} res - Express response object
 * @param {string} accessToken - JWT access token
 * @param {string} refreshToken - Refresh token
 * @param {Object} [req=null] - Express request object (optional)
 */
const setAuthCookies = (res, accessToken, refreshToken, req = null) => {
  // Determine environment
  const isProduction = process.env.NODE_ENV === 'production';

  // Cookie options that work in all environments
  const accessTokenOptions = {
    httpOnly: true,
    secure: isProduction, // Only use secure in production
    sameSite: 'lax', // 'lax' works better for most use cases
    path: '/',
    maxAge: process.env.NODE_ENV === 'production' ? 30 * 60 * 1000 : 24 * 60 * 60 * 1000, // 30 minutes in production, 24 hours in development
    // Don't set domain in development to allow localhost to work properly
    ...(isProduction && { domain: process.env.COOKIE_DOMAIN })
  };

  const refreshTokenOptions = {
    httpOnly: true,
    secure: isProduction, // Only use secure in production
    sameSite: 'lax', // 'lax' works better for most use cases
    path: '/',
    maxAge: process.env.NODE_ENV === 'production' ? 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000, // 24 hours in production, 7 days in development
    // Don't set domain in development to allow localhost to work properly
    ...(isProduction && { domain: process.env.COOKIE_DOMAIN })
  };

  // Set access token cookie
  res.cookie('access_token', accessToken, accessTokenOptions);

  // Set refresh token cookie
  res.cookie('refresh_token', refreshToken, refreshTokenOptions);
};

/**
 * Clear authentication cookies
 * @param {Object} res - Express response object
 */
const clearAuthCookies = (res) => {
  console.log('Clearing auth cookies');

  // Determine environment
  const isProduction = process.env.NODE_ENV === 'production';

  // Try multiple approaches to ensure cookies are cleared

  // 1. Standard approach with full options
  const cookieOptions = {
    path: '/',
    httpOnly: true,
    secure: isProduction, // Only use secure in production
    sameSite: 'lax', // 'lax' works better for most use cases
    // Don't set domain in development to allow localhost to work properly
    ...(isProduction && { domain: process.env.COOKIE_DOMAIN })
  };

  // Clear both cookies with the same options
  res.clearCookie('access_token', cookieOptions);
  res.clearCookie('refresh_token', cookieOptions);

  // 2. Try with path only
  res.clearCookie('access_token', { path: '/' });
  res.clearCookie('refresh_token', { path: '/' });

  // 3. Try with minimal options
  res.clearCookie('access_token');
  res.clearCookie('refresh_token');

  // 4. Try with expires approach
  const expiredOptions = {
    ...cookieOptions,
    expires: new Date(0)
  };

  res.clearCookie('access_token', expiredOptions);
  res.clearCookie('refresh_token', expiredOptions);

  // 5. Try setting empty cookies with past expiration
  res.cookie('access_token', '', { expires: new Date(0), path: '/' });
  res.cookie('refresh_token', '', { expires: new Date(0), path: '/' });

  // Log the cookie clearing
  console.log('Auth cookies cleared using multiple approaches');
};

module.exports = {
  generateAccessToken,
  generateRefreshToken,
  verifyRefreshToken,
  invalidateRefreshToken,
  invalidateAllUserTokens,
  setAuthCookies,
  clearAuthCookies
};
