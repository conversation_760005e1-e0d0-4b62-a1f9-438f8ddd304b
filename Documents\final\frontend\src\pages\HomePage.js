import React, { useRef, useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Navbar from '../components/Navbar';
import TokenCard from '../components/TokenCard';
import TrendingTokenCard from '../components/TrendingTokenCard';
import bitcoinLogo from '../assets/bitcoin-logo.png';
import { API_URL } from '../config/constants';

const HomePage = () => {
  const trendingTokensRef = useRef(null);
  const [trendingTokens, setTrendingTokens] = useState([]);
  const [exploreTokens, setExploreTokens] = useState([]);

  useEffect(() => {
    fetch(`${API_URL}/tokens/trending`)
      .then(res => res.json())
      .then(data => setTrendingTokens(data))
      .catch(() => setTrendingTokens([
        { id: 101, name: 'MemeRocket', symbol: 'MRKT', price: 0.12, change: '+25%', volume: 120000, holders: 5000, marketCap: 1000000, description: 'A trending meme coin!', volume_24h: 120000, image: bitcoinLogo },
        { id: 102, name: 'SolanaPepe', symbol: 'SPEPE', price: 0.05, change: '+18%', volume: 95000, holders: 3200, marketCap: 800000, description: 'Pepe on Solana!', volume_24h: 95000, image: bitcoinLogo },
      ]));
  }, []);

  useEffect(() => {
    fetch(`${API_URL}/tokens/explore`)
      .then(res => res.json())
      .then(data => setExploreTokens(data))
      .catch(() => setExploreTokens([
        { id: 201, name: 'FreshMeme', symbol: 'FMEME', price: 0.01, change: '+2%', volume: 10000, holders: 100, marketCap: 20000, description: 'A new meme coin!', created_at: new Date().toISOString(), image: bitcoinLogo, creator: 'User' },
        { id: 202, name: 'SolanaDog', symbol: 'SDOG', price: 0.03, change: '+5%', volume: 20000, holders: 250, marketCap: 60000, description: 'Dog coin on Solana!', created_at: new Date().toISOString(), image: bitcoinLogo, creator: 'User' },
      ]));
  }, []);

  const scrollLeft = () => {
    if (trendingTokensRef.current) {
      const containerWidth = trendingTokensRef.current.clientWidth;
      trendingTokensRef.current.scrollBy({
        left: -containerWidth,
        behavior: 'smooth'
      });
    }
  };

  const scrollRight = () => {
    if (trendingTokensRef.current) {
      const containerWidth = trendingTokensRef.current.clientWidth;
      trendingTokensRef.current.scrollBy({
        left: containerWidth,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="home-page">
      <Navbar />

      <div className="home-content">
        <div className="trending-section">
          <h2 className="trending-header">Now Trending</h2>
          <div className="navigation-buttons">
            <button className="nav-button prev" onClick={scrollLeft}></button>
            <button className="nav-button next" onClick={scrollRight}></button>
          </div>

          <div className="trending-tokens" ref={trendingTokensRef}>
            {trendingTokens.map(token => (
              <TrendingTokenCard key={token.id} token={token} />
            ))}
          </div>
        </div>

        <div className="action-buttons">
          <Link to="/create-token" className="create-token-btn">Create Token</Link>
          <button className="manage-tokens-btn">Manage Tokens</button>
        </div>

        <div className="search-section">
          <div className="search-container">
            <input type="text" placeholder="Search for tokens here" className="search-input" />
          </div>
          <div className="filter-options">
            <button className="filter-option-btn new">New</button>
            <button className="filter-option-btn trending">Trending</button>
          </div>
        </div>

        <div className="explore-section">
          <h2>Explore</h2>
          <div className="explore-tokens">
            {exploreTokens.map(token => (
              <TokenCard key={token.id} token={token} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
