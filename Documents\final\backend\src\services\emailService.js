/**
 * Email Service using SendGrid
 *
 * This service handles all email-related functionality including:
 * - Sending verification emails
 * - Sending password reset emails
 * - Sending notification emails
 */

const sgMail = require('@sendgrid/mail');
const crypto = require('crypto');
// const redis = require('../config/redis'); // REMOVED - Redis no longer used

// Initialize SendGrid with API key
const SENDGRID_API_KEY = process.env.SENDGRID_API_KEY || 'SG.YOUR_SENDGRID_API_KEY';
const SENDER_EMAIL = process.env.SENDER_EMAIL || '<EMAIL>';
sgMail.setApiKey(SENDGRID_API_KEY);

// OTP expiration time in seconds (10 minutes)
const OTP_EXPIRATION = 10 * 60;

/**
 * Generate a secure OTP code
 * @param {number} length - Length of the OTP code
 * @returns {string} - Generated OTP code
 */
const generateOTP = (length = 6) => {
  // Generate a random numeric OTP
  const otp = crypto.randomInt(100000, 999999).toString().padStart(6, '0');
  return otp;
};

/**
 * Store OTP in memory with expiration (Redis removed)
 * @param {string} email - User's email
 * @param {string} otp - Generated OTP
 * @returns {Promise<boolean>} - Success status
 */
const storeOTP = async (email, otp) => {
  try {
    // Redis removed - using in-memory storage only
    const key = `otp:${email.toLowerCase()}`;

    // Store in global memory
    global.otpStore = global.otpStore || {};
    global.otpStore[key] = otp;

    // Set timeout to delete OTP after expiration
    setTimeout(() => {
      if (global.otpStore && global.otpStore[key]) {
        delete global.otpStore[key];
      }
    }, OTP_EXPIRATION * 1000);

    return true;

    // // Use Redis OTP manager if available
    // if (redis.otpManager) {
    //   return await redis.otpManager.storeOTP(email, otp, OTP_EXPIRATION);
    // }

    // // Legacy fallback if Redis OTP manager is not available
    // const key = `otp:${email.toLowerCase()}`;

    // if (redis.client) {
    //   await redis.client.set(key, otp);
    //   await redis.client.expire(key, OTP_EXPIRATION);
    //   return true;
    // }

    // // Fallback if Redis is not available
    // global.otpStore = global.otpStore || {};
    // global.otpStore[key] = otp;

    // // Set timeout to delete OTP after expiration
    // setTimeout(() => {
    //   if (global.otpStore && global.otpStore[key]) {
    //     delete global.otpStore[key];
    //   }
    // }, OTP_EXPIRATION * 1000);

    // return true;
  } catch (error) {
    console.error('Error storing OTP:', error);
    return false;
  }
};

/**
 * Verify OTP for a given email
 * @param {string} email - User's email
 * @param {string} otp - OTP to verify
 * @returns {Promise<boolean>} - Verification result
 */
const verifyOTP = async (email, otp) => {
  try {
    // For development mode, always return true
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[DEVELOPMENT] Bypassing OTP verification for ${email}`);
      return true;
    }

    // Redis removed - using in-memory storage only
    const key = `otp:${email.toLowerCase()}`;
    let storedOTP;

    // Get from global memory storage
    storedOTP = global.otpStore && global.otpStore[key];

    // Delete OTP after verification attempt
    if (storedOTP) {
      delete global.otpStore[key];
    }

    return storedOTP === otp;

    // // Use Redis OTP manager if available
    // if (redis.otpManager) {
    //   return await redis.otpManager.verifyOTP(email, otp);
    // }

    // // Legacy fallback if Redis OTP manager is not available
    // const key = `otp:${email.toLowerCase()}`;
    // let storedOTP;

    // if (redis.client) {
    //   storedOTP = await redis.client.get(key);

    //   // Delete OTP after verification attempt
    //   if (storedOTP) {
    //     await redis.client.del(key);
    //   }
    // } else {
    //   // Fallback if Redis is not available
    //   storedOTP = global.otpStore && global.otpStore[key];

    //   // Delete OTP after verification attempt
    //   if (storedOTP) {
    //     delete global.otpStore[key];
    //   }
    // }

    // return storedOTP === otp;
  } catch (error) {
    console.error('Error verifying OTP:', error);
    return false;
  }
};

/**
 * Send verification email with OTP
 * @param {string} to - Recipient email
 * @param {string} verificationCode - OTP code
 * @returns {Promise<boolean>} - Success status
 */
const sendVerificationEmail = async (to, verificationCode) => {
  try {
    // For development, log the verification code
    console.log(`[VERIFICATION CODE for ${to}]: ${verificationCode}`);

    // Store OTP in memory storage
    await storeOTP(to, verificationCode);

    // In development, just return true without actually sending an email
    if (process.env.NODE_ENV !== 'production') {
      return true;
    }

    // Prepare email for SendGrid
    const msg = {
      to: to,
      from: SENDER_EMAIL,
      subject: 'Verify Your Email Address',
      text: `Your verification code is: ${verificationCode}\n\nThis code will expire in 10 minutes.`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #333; text-align: center;">Email Verification</h2>
          <p>Thank you for registering with our platform. To complete your registration, please use the verification code below:</p>
          <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 24px; letter-spacing: 5px; margin: 20px 0; border-radius: 5px;">
            <strong>${verificationCode}</strong>
          </div>
          <p>This code will expire in 10 minutes.</p>
          <p>If you didn't request this verification, please ignore this email.</p>
          <p style="margin-top: 30px; font-size: 12px; color: #777; text-align: center;">
            &copy; ${new Date().getFullYear()} Swap. All rights reserved.
          </p>
        </div>
      `
    };

    // Send email via SendGrid
    await sgMail.send(msg);
    console.log('Email sent successfully to:', to);
    return true;
  } catch (error) {
    console.error('Error sending verification email:', error);
    return false;
  }
};

/**
 * Send password reset email with OTP
 * @param {string} to - Recipient email
 * @param {string} resetCode - Reset code
 * @returns {Promise<boolean>} - Success status
 */
const sendPasswordResetEmail = async (to, resetCode) => {
  try {
    // For development, log the reset code
    console.log(`[PASSWORD RESET CODE for ${to}]: ${resetCode}`);

    // Store OTP in memory storage
    await storeOTP(to, resetCode);

    // In development, just return true without actually sending an email
    if (process.env.NODE_ENV !== 'production') {
      return true;
    }

    // Prepare email for SendGrid
    const msg = {
      to: to,
      from: SENDER_EMAIL,
      subject: 'Reset Your Password',
      text: `Your password reset code is: ${resetCode}\n\nThis code will expire in 10 minutes.`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #333; text-align: center;">Password Reset</h2>
          <p>We received a request to reset your password. Please use the code below to reset your password:</p>
          <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 24px; letter-spacing: 5px; margin: 20px 0; border-radius: 5px;">
            <strong>${resetCode}</strong>
          </div>
          <p>This code will expire in 10 minutes.</p>
          <p>If you didn't request a password reset, please ignore this email or contact support if you have concerns.</p>
          <p style="margin-top: 30px; font-size: 12px; color: #777; text-align: center;">
            &copy; ${new Date().getFullYear()} Swap. All rights reserved.
          </p>
        </div>
      `
    };

    // Send email via SendGrid
    await sgMail.send(msg);
    console.log('Password reset email sent successfully to:', to);
    return true;
  } catch (error) {
    console.error('Error sending password reset email:', error);
    return false;
  }
};

module.exports = {
  sendVerificationEmail,
  sendPasswordResetEmail,
  verifyOTP,
  generateOTP
};