/**
 * User Routes
 */

const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const { upstashClient } = require('../config/redis');

/**
 * Get user notification preferences
 * @route GET /api/user/notification-preferences
 * @access Private
 */
router.get('/notification-preferences', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const User = require('../models/User');
    const mongoose = require('mongoose');

    // Default preferences if none are found
    let preferences = {
      emailNotifications: true,
      smsNotifications: true,
      pushNotifications: false
    };

    // Try to get preferences from MongoDB if available
    if (mongoose.connection.readyState === 1) {
      try {
        const user = await User.findById(userId);
        if (user && user.notificationPreferences) {
          preferences = user.notificationPreferences;
          console.log(`Retrieved notification preferences from MongoDB for user ${userId}`);
        }
      } catch (mongoError) {
        console.error('Error retrieving from MongoDB:', mongoError);
        // Continue with Redis fallback
      }
    }

    // If not found in MongoDB, try Redis as fallback
    if (!preferences && upstashClient) {
      try {
        const prefsJson = await upstashClient.get(`user:${userId}:notification-preferences`);
        if (prefsJson) {
          preferences = JSON.parse(prefsJson);
          console.log(`Retrieved notification preferences from Redis for user ${userId}`);
        }
      } catch (redisError) {
        console.error('Error retrieving from Redis:', redisError);
        // Continue with default preferences
      }
    }

    // Return preferences
    res.json({
      success: true,
      preferences
    });
  } catch (error) {
    console.error('Error fetching notification preferences:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notification preferences',
      error: error.message
    });
  }
});

/**
 * Save user notification preferences
 * @route POST /api/user/notification-preferences
 * @access Private
 */
router.post('/notification-preferences', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const User = require('../models/User');
    const mongoose = require('mongoose');

    // Get preferences from request body
    const preferences = {
      emailNotifications: req.body.emailNotifications !== undefined ? req.body.emailNotifications : true,
      smsNotifications: req.body.smsNotifications !== undefined ? req.body.smsNotifications : true,
      pushNotifications: req.body.pushNotifications !== undefined ? req.body.pushNotifications : false
    };

    // Validate preferences
    if (typeof preferences.emailNotifications !== 'boolean' ||
        typeof preferences.smsNotifications !== 'boolean' ||
        typeof preferences.pushNotifications !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'Preferences must be boolean values'
      });
    }

    let savedToMongo = false;

    // Try to save to MongoDB if available
    if (mongoose.connection.readyState === 1) {
      try {
        const user = await User.findById(userId);
        if (user) {
          user.notificationPreferences = preferences;
          await user.save();
          savedToMongo = true;
          console.log(`Saved notification preferences to MongoDB for user ${userId}`);
        }
      } catch (mongoError) {
        console.error('Error saving to MongoDB:', mongoError);
        // Continue with Redis fallback
      }
    }

    // If not saved to MongoDB or as a fallback, save to Redis
    if (!savedToMongo && upstashClient) {
      try {
        await upstashClient.set(
          `user:${userId}:notification-preferences`,
          JSON.stringify(preferences)
        );
        console.log(`Saved notification preferences to Redis for user ${userId}`);
      } catch (redisError) {
        console.error('Error saving to Redis:', redisError);

        // If we couldn't save to MongoDB or Redis, return error
        if (!savedToMongo) {
          return res.status(500).json({
            success: false,
            message: 'Storage services unavailable',
            error: redisError.message
          });
        }
        // Otherwise continue since we saved to MongoDB
      }
    }

    res.json({
      success: true,
      message: 'Notification preferences saved successfully'
    });
  } catch (error) {
    console.error('Error saving notification preferences:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save notification preferences',
      error: error.message
    });
  }
});

/**
 * Reset user notification preferences to defaults
 * @route DELETE /api/user/notification-preferences
 * @access Private
 */
router.delete('/notification-preferences', auth, async (req, res) => {
  try {
    const userId = req.user.id;

    // Delete preferences from Redis
    try {
      await upstashClient.del(`user:${userId}:notification-preferences`);
      console.log(`Reset notification preferences for user ${userId}`);
    } catch (redisError) {
      console.error('Error deleting from Redis:', redisError);
      // Continue anyway to return success to the user
    }

    res.json({
      success: true,
      message: 'Notification preferences reset to defaults'
    });
  } catch (error) {
    console.error('Error resetting notification preferences:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset notification preferences',
      error: error.message
    });
  }
});

module.exports = router;
