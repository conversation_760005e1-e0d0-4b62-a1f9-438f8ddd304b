[package]
name = "meme_coin_platform"
version = "0.1.0"
description = "Solana program for meme coin creation and trading with bonding curves"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "meme_coin_platform"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = []

[dependencies]
anchor-lang = { version = "0.28.0", features = ["init-if-needed"] }
anchor-spl = { version = "0.28.0", features = ["metadata"] }
solana-program = "1.16.0"
