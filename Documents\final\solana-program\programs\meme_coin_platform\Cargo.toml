[package]
name = "meme_coin_platform"
version = "0.1.0"
description = "Solana program for meme coin creation and trading with bonding curves"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "meme_coin_platform"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = []
idl-build = ["anchor-lang/idl-build", "anchor-spl/idl-build"]

[dependencies]
anchor-lang = { version = "0.30.1", features = ["init-if-needed"] }
anchor-spl = { version = "0.30.1", features = ["metadata"] }
solana-program = "1.18.4"
spl-token = { version = "4.0.0", features = ["no-entrypoint"] }
spl-memo = "4.0.0"

[patch.crates-io]
proc-macro2 = { version = "1.0.60" }
