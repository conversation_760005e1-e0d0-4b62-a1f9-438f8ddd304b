const { Connection, PublicKey } = require('@solana/web3.js');

// Solana configuration
const solanaConfig = {
  rpcUrl: process.env.SOLANA_RPC_URL || 'https://api.devnet.solana.com',
  network: process.env.SOLANA_NETWORK || 'devnet'
};

// Initialize Solana connection
const connection = new Connection(solanaConfig.rpcUrl, {
  commitment: 'confirmed',
  confirmTransactionInitialTimeout: 60000
});

// Test Solana connection
const testSolanaConnection = async () => {
  try {
    const version = await connection.getVersion();
    console.log('Successfully connected to Solana network:', solanaConfig.network);
    console.log('Solana version:', version);
    return true;
  } catch (error) {
    console.error('Failed to connect to Solana network:', error.message);
    return false;
  }
};

module.exports = {
  connection,
  solanaConfig,
  testSolanaConnection
};