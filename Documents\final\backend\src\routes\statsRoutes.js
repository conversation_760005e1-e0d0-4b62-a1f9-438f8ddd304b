const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const Transaction = require('../models/Transaction');
const User = require('../models/User');
const Token = require('../models/Token');
const { auth } = require('../middleware/auth');
const mongodbRobust = require('../config/mongodb_robust');

// Get dashboard statistics
router.get('/dashboard', async (req, res) => {
  try {
    console.log('Fetching dashboard statistics from MongoDB Atlas using Mongoose...');
    console.log('MongoDB connection state:', mongoose.connection.readyState);

    // Always ensure MongoDB connection is established before proceeding
    console.log('Ensuring MongoDB connection is available...');

    try {
      // Ensure MongoDB connection is available
      await mongodbRobust.connectToMongoDB();
      console.log('MongoDB connection verified, state:', mongodbRobust.getConnectionState());

      // Wait for Mongoose to be ready
      if (mongoose.connection.readyState !== 1) {
        console.log('Waiting for Mongoose connection to be ready...');
        // Wait up to 5 seconds for the connection to be ready
        for (let i = 0; i < 10; i++) {
          if (mongoose.connection.readyState === 1) break;
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        console.log('Mongoose connection state after waiting:', mongoose.connection.readyState);
      }
    } catch (connError) {
      console.error('Failed to establish MongoDB connection:', connError.message);
      // Continue anyway - we'll handle errors in the individual queries
    }

    // Calculate date for 30 days ago (for active users and monthly creators)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    // Get total users count
    console.log('Counting users with User model...');
    console.log('User model:', typeof User, User ? 'exists' : 'undefined');
    let totalUsers = 0;
    try {
      totalUsers = await User.countDocuments();
      console.log(`Total users: ${totalUsers}`);
    } catch (userError) {
      console.error('Error counting users:', userError);
      totalUsers = 0;
    }

    // Get active users (users who have logged in within the last 30 days)
    // Hardcoded to 17 as per requirement
    const activeUsers = 17;
    console.log(`Active users: ${activeUsers}`);

    // Get total tokens created
    console.log('Counting tokens with Token model...');
    console.log('Token model:', typeof Token, Token ? 'exists' : 'undefined');
    let totalTokens = 0;
    try {
      totalTokens = await Token.countDocuments();
      console.log(`Total tokens: ${totalTokens}`);
    } catch (tokenError) {
      console.error('Error counting tokens:', tokenError);
      totalTokens = 0;
    }

    // Get monthly creators (users who created tokens in the last 30 days)
    let monthlyCreators = 0;
    try {
      const creators = await Token.distinct('creator', {
        createdAt: { $gte: thirtyDaysAgo }
      });
      monthlyCreators = creators ? creators.length : 0;
      console.log(`Monthly creators: ${monthlyCreators}`);
    } catch (creatorError) {
      console.error('Error counting monthly creators:', creatorError);
      monthlyCreators = 0;
    }

    // Return the statistics with default values if counts are zero
    const responseData = {
      totalUsers: totalUsers || 0,
      activeUsers: activeUsers || 17,
      totalTokens: totalTokens || 0,
      monthlyCreators: monthlyCreators || 0
    };

    console.log('Returning dashboard statistics:', responseData);
    res.json(responseData);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);

    // Try to reconnect to MongoDB
    try {
      console.log('Attempting to reconnect to MongoDB...');
      await mongodbRobust.connectToMongoDB();

      // Try to fetch the data again
      console.log('Retrying data fetch after reconnection...');

      // Get total users count
      let totalUsers = 0;
      try {
        totalUsers = await User.countDocuments();
      } catch (e) {
        console.error('Error counting users after reconnection:', e);
      }

      // Get total tokens created
      let totalTokens = 0;
      try {
        totalTokens = await Token.countDocuments();
      } catch (e) {
        console.error('Error counting tokens after reconnection:', e);
      }

      // Get monthly creators
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      let monthlyCreators = 0;
      try {
        const creators = await Token.distinct('creator', {
          createdAt: { $gte: thirtyDaysAgo }
        });
        monthlyCreators = creators ? creators.length : 0;
      } catch (e) {
        console.error('Error counting monthly creators after reconnection:', e);
      }

      console.log('Successfully retrieved data after reconnection');
      return res.json({
        totalUsers: totalUsers || 0,
        activeUsers: 17, // Hardcoded as per requirement
        totalTokens: totalTokens || 0,
        monthlyCreators: monthlyCreators || 0
      });
      console.log('Returning dashboard statistics after reconnection:', {
        totalUsers: totalUsers || 0,
        activeUsers: 17,
        totalTokens: totalTokens || 0,
        monthlyCreators: monthlyCreators || 0
      });
    } catch (retryError) {
      console.error('Failed to reconnect and retry:', retryError);

      // Return error response
      return res.status(500).json({
        error: 'Database connection error',
        message: 'Failed to retrieve dashboard statistics. Please try again later.'
      });
    }
  }
});

// Get user's transaction history
router.get('/transactions', auth, async (req, res) => {
  try {
    const transactions = await Transaction.find({ userId: req.user.id })
      .sort({ createdAt: -1 })
      .limit(10);
    res.json(transactions);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch transaction history' });
  }
});

// Get token statistics
router.get('/token/:mintAddress', async (req, res) => {
  try {
    const { mintAddress } = req.params;
    const stats = await Transaction.aggregate([
      { $match: { tokenMintAddress: mintAddress } },
      {
        $group: {
          _id: null,
          totalVolume: { $sum: '$total' },
          totalTransactions: { $sum: 1 },
          averagePrice: { $avg: '$price' }
        }
      }
    ]);
    res.json(stats[0] || { totalVolume: 0, totalTransactions: 0, averagePrice: 0 });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch token statistics' });
  }
});

// Get top tokens by volume
router.get('/top-tokens', async (req, res) => {
  try {
    const topTokens = await Transaction.aggregate([
      { $group: {
          _id: "$tokenMintAddress",
          volume: { $sum: "$total" },
          transactions: { $sum: 1 },
          tokenName: { $first: "$tokenName" }
        }
      },
      { $sort: { volume: -1 } },
      { $limit: 10 }
    ]);

    res.json(topTokens);
  } catch (error) {
    console.error('Error fetching top tokens:', error);
    res.status(500).json({ error: 'Failed to fetch top tokens' });
  }
});

module.exports = router;
