{"name": "@walletconnect/keyvaluestorage", "description": "Isomorphic Key-Value Storage", "version": "1.1.1", "author": "WalletConnect, Inc. <walletconnect.com>", "license": "MIT", "keywords": ["iso", "isomorphic", "store", "storage", "localstorage", "asyncstorage", "sequelize"], "files": ["dist"], "main": "dist/index.cjs.js", "module": "dist/index.es.js", "browser": "dist/index.es.js", "unpkg": "dist/index.umd.js", "types": "dist/types/index.d.ts", "react-native": "dist/react-native/index.js", "homepage": "https://github.com/walletconnect/walletconnect-utils", "repository": {"type": "git", "url": "git+https://github.com/walletconnect/walletconnect-utils.git"}, "bugs": {"url": "https://github.com/walletconnect/walletconnect-utils/issues"}, "scripts": {"clean": "rm -rf dist", "build:pre": "npm run clean", "build:types": "tsc", "build:source": "rollup --config rollup.config.js", "build": "npm run build:pre && npm run build:source && npm run build:types", "test": "rm -rf ./test/dbs/* && env TS_NODE_PROJECT=\"tsconfig.cjs.json\" mocha --timeout 5000 --exit -r ts-node/register ./test/**/*.test.ts", "npm-publish:latest": "npm publish --access public --tag latest", "npm-publish:canary": "npm publish --access public --tag canary", "prepublishOnly": "npm run test && npm run build", "prettier": "prettier --config ../../.prettierrc --check {src,test}/**/*.ts", "format": "prettier --config ../../.prettierrc --write {src,test}/**/*.ts"}, "dependencies": {"@walletconnect/safe-json": "^1.0.1", "idb-keyval": "^6.2.1", "unstorage": "^1.9.0"}, "peerDependencies": {"@react-native-async-storage/async-storage": "1.x"}, "peerDependenciesMeta": {"@react-native-async-storage/async-storage": {"optional": true}}, "devDependencies": {"@react-native-async-storage/async-storage": "^1.17.3", "@types/jest": "^26.0.15", "@types/node": "^14.14.7", "@walletconnect/time": "^1.0.2", "chai": "^4.3.6", "classic-level": "^1.2.0", "deepmerge": "^4.2.2", "mocha": "^10.0.0", "proxyquire": "^2.1.3", "ts-node": "^10.9.1", "webpack": "^4.41.6", "webpack-cli": "^3.3.11"}}