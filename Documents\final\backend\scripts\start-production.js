/**
 * Production Startup Script
 * 
 * This script performs pre-flight checks before starting the application in production mode.
 * It verifies that all required environment variables are set and that critical services are available.
 */

// Set production environment
process.env.NODE_ENV = 'production';

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('\n=== PRODUCTION STARTUP SCRIPT ===');
console.log('Performing pre-flight checks before starting the application in production mode...\n');

// Check for required environment variables
const requiredEnvVars = [
  'JWT_SECRET',
  'COOKIE_SECRET',
  'SESSION_SECRET',
  'MONGODB_URI',
  'POSTGRESQL_URI',
  'PLATFORM_WALLET_ADDRESS'
];

// Check if .env file exists and load it
const envPath = path.join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
  console.log('Loading environment variables from .env file...');
  require('dotenv').config({ path: envPath });
}

// Check for missing environment variables
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('\n❌ ERROR: Missing required environment variables:');
  missingVars.forEach(varName => {
    console.error(`   - ${varName}`);
  });
  console.error('\nPlease set these environment variables in your hosting platform or .env file.');
  process.exit(1);
}

console.log('✅ All required environment variables are set.');

// Check for weak secrets
const secretVars = ['JWT_SECRET', 'COOKIE_SECRET', 'SESSION_SECRET'];
const weakSecrets = secretVars.filter(varName => {
  const secret = process.env[varName];
  return secret && (secret.length < 32 || 
                   secret === 'your-jwt-secret-key' || 
                   secret === 'your-cookie-secret-key' || 
                   secret === 'your-session-secret');
});

if (weakSecrets.length > 0) {
  console.warn('\n⚠️ WARNING: Weak or default secrets detected:');
  weakSecrets.forEach(varName => {
    console.warn(`   - ${varName}`);
  });
  console.warn('\nConsider generating stronger secrets using:');
  console.warn('   npm run generate:secrets');
}

// Start the application
console.log('\n=== STARTING APPLICATION IN PRODUCTION MODE ===');
try {
  execSync('npm run start:prod', { stdio: 'inherit' });
} catch (error) {
  console.error('Failed to start application:', error);
  process.exit(1);
}
