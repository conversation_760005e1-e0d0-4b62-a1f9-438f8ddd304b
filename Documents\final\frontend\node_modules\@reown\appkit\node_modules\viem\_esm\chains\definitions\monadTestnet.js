import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const monadTestnet = /*#__PURE__*/ defineChain({
    id: 10_143,
    name: 'Monad Testnet',
    nativeCurrency: {
        name: 'Testnet MON Token',
        symbol: '<PERSON><PERSON>',
        decimals: 18,
    },
    rpcUrls: {
        default: {
            http: ['https://testnet-rpc.monad.xyz'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Monad Testnet explorer',
            url: 'https://testnet.monadexplorer.com',
        },
    },
    contracts: {
        multicall3: {
            address: '0xcA11bde05977b3631167028862bE2a173976CA11',
            blockCreated: 251449,
        },
    },
    testnet: true,
});
//# sourceMappingURL=monadTestnet.js.map