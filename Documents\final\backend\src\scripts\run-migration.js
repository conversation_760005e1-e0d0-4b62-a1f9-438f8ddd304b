/**
 * Run database migration script
 * 
 * This script runs the SQL migration to fix the transactions table schema
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

async function runMigration() {
  console.log('Running database migration...');
  
  // Create PostgreSQL connection
  const pool = new Pool({
    connectionString: process.env.POSTGRESQL_URI,
    ssl: { rejectUnauthorized: false }
  });
  
  try {
    // Read migration SQL file
    const migrationPath = path.join(__dirname, '../db/migrations/fix_transactions_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('Connecting to PostgreSQL...');
    const client = await pool.connect();
    
    try {
      console.log('Running migration SQL...');
      await client.query(migrationSQL);
      console.log('Migration completed successfully!');
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the migration
runMigration()
  .then(() => {
    console.log('Migration script completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error(`Migration script failed: ${error.message}`);
    process.exit(1);
  });
