import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const humanodeTestnet5 = /*#__PURE__*/ define<PERSON>hain({
    id: 14853,
    name: 'Humanode Testnet 5',
    nativeCurrency: { name: '<PERSON><PERSON>', symbol: '<PERSON><PERSON>', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://explorer-rpc-http.testnet5.stages.humanode.io'],
            webSocket: ['wss://explorer-rpc-ws.testnet5.stages.humanode.io'],
        },
    },
    contracts: {
        multicall3: {
            address: '0xca11bde05977b3631167028862be2a173976ca11',
        },
    },
});
//# sourceMappingURL=humanodeTestnet5.js.map