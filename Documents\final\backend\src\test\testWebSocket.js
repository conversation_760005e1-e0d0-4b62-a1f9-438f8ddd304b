const WebSocket = require('ws');

// Create a WebSocket connection to the C++ Order Matching Engine
const ws = new WebSocket('ws://localhost:9002', {
  handshakeTimeout: 5000,
  perMessageDeflate: false, // Disable compression for better compatibility
  skipUTF8Validation: true, // Skip UTF-8 validation for better compatibility
  maxPayload: 1024 * 1024 // 1MB max payload
});

// Handle connection to the C++ Order Matching Engine
ws.on('open', () => {
  console.log('Connected to C++ Order Matching Engine on port 9002');
  
  // Send a ping message
  setTimeout(() => {
    ws.send('ping');
    console.log('Sent ping to C++ Order Matching Engine');
  }, 1000);
});

// Handle messages from the C++ Order Matching Engine
ws.on('message', (data) => {
  console.log('Received message from C++ Order Matching Engine:', data.toString());
});

// Handle C++ Order Matching Engine disconnection
ws.on('close', (code, reason) => {
  console.log(`Disconnected from C++ Order Matching Engine: Code ${code}, Reason: ${reason || 'No reason provided'}`);
});

// Handle C++ Order Matching Engine errors
ws.on('error', (err) => {
  console.error('C++ Order Matching Engine error:', err.message);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('Shutting down test client');
  ws.close();
  process.exit(0);
});
