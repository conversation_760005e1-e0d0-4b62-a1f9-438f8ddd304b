"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var Z=require("events"),ee=require("@walletconnect/heartbeat"),ei=require("@walletconnect/keyvaluestorage"),b=require("@walletconnect/logger"),B=require("@walletconnect/types"),u=require("@walletconnect/time"),Ee=require("@walletconnect/safe-json"),De=require("@walletconnect/relay-auth"),c=require("@walletconnect/utils"),ti=require("uint8arrays"),ii=require("@walletconnect/jsonrpc-provider"),L=require("@walletconnect/jsonrpc-utils"),si=require("@walletconnect/jsonrpc-ws-connection"),ri=require("lodash.isequal"),ni=require("@walletconnect/window-getters");function le(r){return r&&typeof r=="object"&&"default"in r?r:{default:r}}function oi(r){if(r&&r.__esModule)return r;var e=Object.create(null);return r&&Object.keys(r).forEach(function(t){if(t!=="default"){var s=Object.getOwnPropertyDescriptor(r,t);Object.defineProperty(e,t,s.get?s:{enumerable:!0,get:function(){return r[t]}})}}),e.default=r,Object.freeze(e)}var ai=le(Z),ci=le(ei),ue=oi(De),hi=le(si),li=le(ri);const be="wc",me=2,re="core",K=`${be}@2:${re}:`,$e={name:re,logger:"error"},Ue={database:":memory:"},ze="crypto",_e="client_ed25519_seed",Me=u.ONE_DAY,Fe="keychain",ke="0.3",Ve="messages",Be="0.3",ve=u.SIX_HOURS,Ke="publisher",je="irn",qe="error",fe="wss://relay.walletconnect.org",Ye="relayer",C={message:"relayer_message",message_ack:"relayer_message_ack",connect:"relayer_connect",disconnect:"relayer_disconnect",error:"relayer_error",connection_stalled:"relayer_connection_stalled",transport_closed:"relayer_transport_closed",publish:"relayer_publish"},Ge="_subscription",z={payload:"payload",connect:"connect",disconnect:"disconnect",error:"error"},He=.1,ui={database:":memory:"},de="2.19.0",di=1e4,Q={link_mode:"link_mode",relay:"relay"},Je="0.3",Xe="WALLETCONNECT_CLIENT_ID",we="WALLETCONNECT_LINK_MODE_APPS",$={created:"subscription_created",deleted:"subscription_deleted",expired:"subscription_expired",disabled:"subscription_disabled",sync:"subscription_sync",resubscribed:"subscription_resubscribed"},gi=u.THIRTY_DAYS,We="subscription",Ze="0.3",Qe=u.FIVE_SECONDS*1e3,et="pairing",tt="0.3",pi=u.THIRTY_DAYS,te={wc_pairingDelete:{req:{ttl:u.ONE_DAY,prompt:!1,tag:1e3},res:{ttl:u.ONE_DAY,prompt:!1,tag:1001}},wc_pairingPing:{req:{ttl:u.THIRTY_SECONDS,prompt:!1,tag:1002},res:{ttl:u.THIRTY_SECONDS,prompt:!1,tag:1003}},unregistered_method:{req:{ttl:u.ONE_DAY,prompt:!1,tag:0},res:{ttl:u.ONE_DAY,prompt:!1,tag:0}}},ie={create:"pairing_create",expire:"pairing_expire",delete:"pairing_delete",ping:"pairing_ping"},F={created:"history_created",updated:"history_updated",deleted:"history_deleted",sync:"history_sync"},it="history",st="0.3",rt="expirer",k={created:"expirer_created",deleted:"expirer_deleted",expired:"expirer_expired",sync:"expirer_sync"},nt="0.3",yi=u.ONE_DAY,ot="verify-api",Ei="https://verify.walletconnect.com",at="https://verify.walletconnect.org",ne=at,ct=`${ne}/v3`,ht=[Ei,at],lt="echo",ut="https://echo.walletconnect.com",Di="event-client",Y={pairing_started:"pairing_started",pairing_uri_validation_success:"pairing_uri_validation_success",pairing_uri_not_expired:"pairing_uri_not_expired",store_new_pairing:"store_new_pairing",subscribing_pairing_topic:"subscribing_pairing_topic",subscribe_pairing_topic_success:"subscribe_pairing_topic_success",existing_pairing:"existing_pairing",pairing_not_expired:"pairing_not_expired",emit_inactive_pairing:"emit_inactive_pairing",emit_session_proposal:"emit_session_proposal",subscribing_to_pairing_topic:"subscribing_to_pairing_topic"},X={no_wss_connection:"no_wss_connection",no_internet_connection:"no_internet_connection",malformed_pairing_uri:"malformed_pairing_uri",active_pairing_already_exists:"active_pairing_already_exists",subscribe_pairing_topic_failure:"subscribe_pairing_topic_failure",pairing_expired:"pairing_expired",proposal_expired:"proposal_expired",proposal_listener_not_found:"proposal_listener_not_found"},bi={session_approve_started:"session_approve_started",proposal_not_expired:"proposal_not_expired",session_namespaces_validation_success:"session_namespaces_validation_success",create_session_topic:"create_session_topic",subscribing_session_topic:"subscribing_session_topic",subscribe_session_topic_success:"subscribe_session_topic_success",publishing_session_approve:"publishing_session_approve",session_approve_publish_success:"session_approve_publish_success",store_session:"store_session",publishing_session_settle:"publishing_session_settle",session_settle_publish_success:"session_settle_publish_success"},mi={no_internet_connection:"no_internet_connection",no_wss_connection:"no_wss_connection",proposal_expired:"proposal_expired",subscribe_session_topic_failure:"subscribe_session_topic_failure",session_approve_publish_failure:"session_approve_publish_failure",session_settle_publish_failure:"session_settle_publish_failure",session_approve_namespace_validation_failure:"session_approve_namespace_validation_failure",proposal_not_found:"proposal_not_found"},_i={authenticated_session_approve_started:"authenticated_session_approve_started",authenticated_session_not_expired:"authenticated_session_not_expired",chains_caip2_compliant:"chains_caip2_compliant",chains_evm_compliant:"chains_evm_compliant",create_authenticated_session_topic:"create_authenticated_session_topic",cacaos_verified:"cacaos_verified",store_authenticated_session:"store_authenticated_session",subscribing_authenticated_session_topic:"subscribing_authenticated_session_topic",subscribe_authenticated_session_topic_success:"subscribe_authenticated_session_topic_success",publishing_authenticated_session_approve:"publishing_authenticated_session_approve",authenticated_session_approve_publish_success:"authenticated_session_approve_publish_success"},vi={no_internet_connection:"no_internet_connection",no_wss_connection:"no_wss_connection",missing_session_authenticate_request:"missing_session_authenticate_request",session_authenticate_request_expired:"session_authenticate_request_expired",chains_caip2_compliant_failure:"chains_caip2_compliant_failure",chains_evm_compliant_failure:"chains_evm_compliant_failure",invalid_cacao:"invalid_cacao",subscribe_authenticated_session_topic_failure:"subscribe_authenticated_session_topic_failure",authenticated_session_approve_publish_failure:"authenticated_session_approve_publish_failure",authenticated_session_pending_request_not_found:"authenticated_session_pending_request_not_found"},dt=.1,gt="event-client",pt=86400,yt="https://pulse.walletconnect.org/batch";function fi(r,e){if(r.length>=255)throw new TypeError("Alphabet too long");for(var t=new Uint8Array(256),s=0;s<t.length;s++)t[s]=255;for(var i=0;i<r.length;i++){var n=r.charAt(i),o=n.charCodeAt(0);if(t[o]!==255)throw new TypeError(n+" is ambiguous");t[o]=i}var a=r.length,h=r.charAt(0),l=Math.log(a)/Math.log(256),g=Math.log(256)/Math.log(a);function E(d){if(d instanceof Uint8Array||(ArrayBuffer.isView(d)?d=new Uint8Array(d.buffer,d.byteOffset,d.byteLength):Array.isArray(d)&&(d=Uint8Array.from(d))),!(d instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(d.length===0)return"";for(var D=0,N=0,I=0,v=d.length;I!==v&&d[I]===0;)I++,D++;for(var M=(v-I)*g+1>>>0,R=new Uint8Array(M);I!==v;){for(var q=d[I],W=0,V=M-1;(q!==0||W<N)&&V!==-1;V--,W++)q+=256*R[V]>>>0,R[V]=q%a>>>0,q=q/a>>>0;if(q!==0)throw new Error("Non-zero carry");N=W,I++}for(var J=M-N;J!==M&&R[J]===0;)J++;for(var he=h.repeat(D);J<M;++J)he+=r.charAt(R[J]);return he}function _(d){if(typeof d!="string")throw new TypeError("Expected String");if(d.length===0)return new Uint8Array;var D=0;if(d[D]!==" "){for(var N=0,I=0;d[D]===h;)N++,D++;for(var v=(d.length-D)*l+1>>>0,M=new Uint8Array(v);d[D];){var R=t[d.charCodeAt(D)];if(R===255)return;for(var q=0,W=v-1;(R!==0||q<I)&&W!==-1;W--,q++)R+=a*M[W]>>>0,M[W]=R%256>>>0,R=R/256>>>0;if(R!==0)throw new Error("Non-zero carry");I=q,D++}if(d[D]!==" "){for(var V=v-I;V!==v&&M[V]===0;)V++;for(var J=new Uint8Array(N+(v-V)),he=N;V!==v;)J[he++]=M[V++];return J}}}function x(d){var D=_(d);if(D)return D;throw new Error(`Non-${e} character`)}return{encode:E,decodeUnsafe:_,decode:x}}var wi=fi,Ti=wi;const Et=r=>{if(r instanceof Uint8Array&&r.constructor.name==="Uint8Array")return r;if(r instanceof ArrayBuffer)return new Uint8Array(r);if(ArrayBuffer.isView(r))return new Uint8Array(r.buffer,r.byteOffset,r.byteLength);throw new Error("Unknown type, must be binary type")},Ii=r=>new TextEncoder().encode(r),Ri=r=>new TextDecoder().decode(r);class Ci{constructor(e,t,s){this.name=e,this.prefix=t,this.baseEncode=s}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class Si{constructor(e,t,s){if(this.name=e,this.prefix=t,t.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=t.codePointAt(0),this.baseDecode=s}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return Dt(this,e)}}class Pi{constructor(e){this.decoders=e}or(e){return Dt(this,e)}decode(e){const t=e[0],s=this.decoders[t];if(s)return s.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const Dt=(r,e)=>new Pi({...r.decoders||{[r.prefix]:r},...e.decoders||{[e.prefix]:e}});class Oi{constructor(e,t,s,i){this.name=e,this.prefix=t,this.baseEncode=s,this.baseDecode=i,this.encoder=new Ci(e,t,s),this.decoder=new Si(e,t,i)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const ge=({name:r,prefix:e,encode:t,decode:s})=>new Oi(r,e,t,s),oe=({prefix:r,name:e,alphabet:t})=>{const{encode:s,decode:i}=Ti(t,e);return ge({prefix:r,name:e,encode:s,decode:n=>Et(i(n))})},Ai=(r,e,t,s)=>{const i={};for(let g=0;g<e.length;++g)i[e[g]]=g;let n=r.length;for(;r[n-1]==="=";)--n;const o=new Uint8Array(n*t/8|0);let a=0,h=0,l=0;for(let g=0;g<n;++g){const E=i[r[g]];if(E===void 0)throw new SyntaxError(`Non-${s} character`);h=h<<t|E,a+=t,a>=8&&(a-=8,o[l++]=255&h>>a)}if(a>=t||255&h<<8-a)throw new SyntaxError("Unexpected end of data");return o},Ni=(r,e,t)=>{const s=e[e.length-1]==="=",i=(1<<t)-1;let n="",o=0,a=0;for(let h=0;h<r.length;++h)for(a=a<<8|r[h],o+=8;o>t;)o-=t,n+=e[i&a>>o];if(o&&(n+=e[i&a<<t-o]),s)for(;n.length*t&7;)n+="=";return n},S=({name:r,prefix:e,bitsPerChar:t,alphabet:s})=>ge({prefix:e,name:r,encode(i){return Ni(i,s,t)},decode(i){return Ai(i,s,t,r)}}),xi=ge({prefix:"\0",name:"identity",encode:r=>Ri(r),decode:r=>Ii(r)});var Li=Object.freeze({__proto__:null,identity:xi});const $i=S({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var Ui=Object.freeze({__proto__:null,base2:$i});const zi=S({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var Mi=Object.freeze({__proto__:null,base8:zi});const Fi=oe({prefix:"9",name:"base10",alphabet:"0123456789"});var ki=Object.freeze({__proto__:null,base10:Fi});const Vi=S({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),Bi=S({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var Ki=Object.freeze({__proto__:null,base16:Vi,base16upper:Bi});const ji=S({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),qi=S({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),Yi=S({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),Gi=S({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),Hi=S({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),Ji=S({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),Xi=S({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),Wi=S({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),Zi=S({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var Qi=Object.freeze({__proto__:null,base32:ji,base32upper:qi,base32pad:Yi,base32padupper:Gi,base32hex:Hi,base32hexupper:Ji,base32hexpad:Xi,base32hexpadupper:Wi,base32z:Zi});const es=oe({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),ts=oe({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var is=Object.freeze({__proto__:null,base36:es,base36upper:ts});const ss=oe({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),rs=oe({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var ns=Object.freeze({__proto__:null,base58btc:ss,base58flickr:rs});const os=S({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),as=S({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),cs=S({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),hs=S({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var ls=Object.freeze({__proto__:null,base64:os,base64pad:as,base64url:cs,base64urlpad:hs});const bt=Array.from("\u{1F680}\u{1FA90}\u2604\u{1F6F0}\u{1F30C}\u{1F311}\u{1F312}\u{1F313}\u{1F314}\u{1F315}\u{1F316}\u{1F317}\u{1F318}\u{1F30D}\u{1F30F}\u{1F30E}\u{1F409}\u2600\u{1F4BB}\u{1F5A5}\u{1F4BE}\u{1F4BF}\u{1F602}\u2764\u{1F60D}\u{1F923}\u{1F60A}\u{1F64F}\u{1F495}\u{1F62D}\u{1F618}\u{1F44D}\u{1F605}\u{1F44F}\u{1F601}\u{1F525}\u{1F970}\u{1F494}\u{1F496}\u{1F499}\u{1F622}\u{1F914}\u{1F606}\u{1F644}\u{1F4AA}\u{1F609}\u263A\u{1F44C}\u{1F917}\u{1F49C}\u{1F614}\u{1F60E}\u{1F607}\u{1F339}\u{1F926}\u{1F389}\u{1F49E}\u270C\u2728\u{1F937}\u{1F631}\u{1F60C}\u{1F338}\u{1F64C}\u{1F60B}\u{1F497}\u{1F49A}\u{1F60F}\u{1F49B}\u{1F642}\u{1F493}\u{1F929}\u{1F604}\u{1F600}\u{1F5A4}\u{1F603}\u{1F4AF}\u{1F648}\u{1F447}\u{1F3B6}\u{1F612}\u{1F92D}\u2763\u{1F61C}\u{1F48B}\u{1F440}\u{1F62A}\u{1F611}\u{1F4A5}\u{1F64B}\u{1F61E}\u{1F629}\u{1F621}\u{1F92A}\u{1F44A}\u{1F973}\u{1F625}\u{1F924}\u{1F449}\u{1F483}\u{1F633}\u270B\u{1F61A}\u{1F61D}\u{1F634}\u{1F31F}\u{1F62C}\u{1F643}\u{1F340}\u{1F337}\u{1F63B}\u{1F613}\u2B50\u2705\u{1F97A}\u{1F308}\u{1F608}\u{1F918}\u{1F4A6}\u2714\u{1F623}\u{1F3C3}\u{1F490}\u2639\u{1F38A}\u{1F498}\u{1F620}\u261D\u{1F615}\u{1F33A}\u{1F382}\u{1F33B}\u{1F610}\u{1F595}\u{1F49D}\u{1F64A}\u{1F639}\u{1F5E3}\u{1F4AB}\u{1F480}\u{1F451}\u{1F3B5}\u{1F91E}\u{1F61B}\u{1F534}\u{1F624}\u{1F33C}\u{1F62B}\u26BD\u{1F919}\u2615\u{1F3C6}\u{1F92B}\u{1F448}\u{1F62E}\u{1F646}\u{1F37B}\u{1F343}\u{1F436}\u{1F481}\u{1F632}\u{1F33F}\u{1F9E1}\u{1F381}\u26A1\u{1F31E}\u{1F388}\u274C\u270A\u{1F44B}\u{1F630}\u{1F928}\u{1F636}\u{1F91D}\u{1F6B6}\u{1F4B0}\u{1F353}\u{1F4A2}\u{1F91F}\u{1F641}\u{1F6A8}\u{1F4A8}\u{1F92C}\u2708\u{1F380}\u{1F37A}\u{1F913}\u{1F619}\u{1F49F}\u{1F331}\u{1F616}\u{1F476}\u{1F974}\u25B6\u27A1\u2753\u{1F48E}\u{1F4B8}\u2B07\u{1F628}\u{1F31A}\u{1F98B}\u{1F637}\u{1F57A}\u26A0\u{1F645}\u{1F61F}\u{1F635}\u{1F44E}\u{1F932}\u{1F920}\u{1F927}\u{1F4CC}\u{1F535}\u{1F485}\u{1F9D0}\u{1F43E}\u{1F352}\u{1F617}\u{1F911}\u{1F30A}\u{1F92F}\u{1F437}\u260E\u{1F4A7}\u{1F62F}\u{1F486}\u{1F446}\u{1F3A4}\u{1F647}\u{1F351}\u2744\u{1F334}\u{1F4A3}\u{1F438}\u{1F48C}\u{1F4CD}\u{1F940}\u{1F922}\u{1F445}\u{1F4A1}\u{1F4A9}\u{1F450}\u{1F4F8}\u{1F47B}\u{1F910}\u{1F92E}\u{1F3BC}\u{1F975}\u{1F6A9}\u{1F34E}\u{1F34A}\u{1F47C}\u{1F48D}\u{1F4E3}\u{1F942}"),us=bt.reduce((r,e,t)=>(r[t]=e,r),[]),ds=bt.reduce((r,e,t)=>(r[e.codePointAt(0)]=t,r),[]);function gs(r){return r.reduce((e,t)=>(e+=us[t],e),"")}function ps(r){const e=[];for(const t of r){const s=ds[t.codePointAt(0)];if(s===void 0)throw new Error(`Non-base256emoji character: ${t}`);e.push(s)}return new Uint8Array(e)}const ys=ge({prefix:"\u{1F680}",name:"base256emoji",encode:gs,decode:ps});var Es=Object.freeze({__proto__:null,base256emoji:ys}),Ds=_t,mt=128,bs=127,ms=~bs,_s=Math.pow(2,31);function _t(r,e,t){e=e||[],t=t||0;for(var s=t;r>=_s;)e[t++]=r&255|mt,r/=128;for(;r&ms;)e[t++]=r&255|mt,r>>>=7;return e[t]=r|0,_t.bytes=t-s+1,e}var vs=Te,fs=128,vt=127;function Te(r,s){var t=0,s=s||0,i=0,n=s,o,a=r.length;do{if(n>=a)throw Te.bytes=0,new RangeError("Could not decode varint");o=r[n++],t+=i<28?(o&vt)<<i:(o&vt)*Math.pow(2,i),i+=7}while(o>=fs);return Te.bytes=n-s,t}var ws=Math.pow(2,7),Ts=Math.pow(2,14),Is=Math.pow(2,21),Rs=Math.pow(2,28),Cs=Math.pow(2,35),Ss=Math.pow(2,42),Ps=Math.pow(2,49),Os=Math.pow(2,56),As=Math.pow(2,63),Ns=function(r){return r<ws?1:r<Ts?2:r<Is?3:r<Rs?4:r<Cs?5:r<Ss?6:r<Ps?7:r<Os?8:r<As?9:10},xs={encode:Ds,decode:vs,encodingLength:Ns},ft=xs;const wt=(r,e,t=0)=>(ft.encode(r,e,t),e),Tt=r=>ft.encodingLength(r),Ie=(r,e)=>{const t=e.byteLength,s=Tt(r),i=s+Tt(t),n=new Uint8Array(i+t);return wt(r,n,0),wt(t,n,s),n.set(e,i),new Ls(r,t,e,n)};class Ls{constructor(e,t,s,i){this.code=e,this.size=t,this.digest=s,this.bytes=i}}const It=({name:r,code:e,encode:t})=>new $s(r,e,t);class $s{constructor(e,t,s){this.name=e,this.code=t,this.encode=s}digest(e){if(e instanceof Uint8Array){const t=this.encode(e);return t instanceof Uint8Array?Ie(this.code,t):t.then(s=>Ie(this.code,s))}else throw Error("Unknown type, must be binary type")}}const Rt=r=>async e=>new Uint8Array(await crypto.subtle.digest(r,e)),Us=It({name:"sha2-256",code:18,encode:Rt("SHA-256")}),zs=It({name:"sha2-512",code:19,encode:Rt("SHA-512")});var Ms=Object.freeze({__proto__:null,sha256:Us,sha512:zs});const Ct=0,Fs="identity",St=Et,ks=r=>Ie(Ct,St(r)),Vs={code:Ct,name:Fs,encode:St,digest:ks};var Bs=Object.freeze({__proto__:null,identity:Vs});new TextEncoder,new TextDecoder;const Pt={...Li,...Ui,...Mi,...ki,...Ki,...Qi,...is,...ns,...ls,...Es};({...Ms,...Bs});function Ks(r=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?globalThis.Buffer.allocUnsafe(r):new Uint8Array(r)}function Ot(r,e,t,s){return{name:r,prefix:e,encoder:{name:r,prefix:e,encode:t},decoder:{decode:s}}}const At=Ot("utf8","u",r=>"u"+new TextDecoder("utf8").decode(r),r=>new TextEncoder().encode(r.substring(1))),Re=Ot("ascii","a",r=>{let e="a";for(let t=0;t<r.length;t++)e+=String.fromCharCode(r[t]);return e},r=>{r=r.substring(1);const e=Ks(r.length);for(let t=0;t<r.length;t++)e[t]=r.charCodeAt(t);return e}),js={utf8:At,"utf-8":At,hex:Pt.base16,latin1:Re,ascii:Re,binary:Re,...Pt};function qs(r,e="utf8"){const t=js[e];if(!t)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(r,"utf8"):t.decoder.decode(`${t.prefix}${r}`)}var Ys=Object.defineProperty,Gs=(r,e,t)=>e in r?Ys(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,G=(r,e,t)=>Gs(r,typeof e!="symbol"?e+"":e,t);class Nt{constructor(e,t){this.core=e,this.logger=t,G(this,"keychain",new Map),G(this,"name",Fe),G(this,"version",ke),G(this,"initialized",!1),G(this,"storagePrefix",K),G(this,"init",async()=>{if(!this.initialized){const s=await this.getKeyChain();typeof s<"u"&&(this.keychain=s),this.initialized=!0}}),G(this,"has",s=>(this.isInitialized(),this.keychain.has(s))),G(this,"set",async(s,i)=>{this.isInitialized(),this.keychain.set(s,i),await this.persist()}),G(this,"get",s=>{this.isInitialized();const i=this.keychain.get(s);if(typeof i>"u"){const{message:n}=c.getInternalError("NO_MATCHING_KEY",`${this.name}: ${s}`);throw new Error(n)}return i}),G(this,"del",async s=>{this.isInitialized(),this.keychain.delete(s),await this.persist()}),this.core=e,this.logger=b.generateChildLogger(t,this.name)}get context(){return b.getLoggerContext(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}async setKeyChain(e){await this.core.storage.setItem(this.storageKey,c.mapToObj(e))}async getKeyChain(){const e=await this.core.storage.getItem(this.storageKey);return typeof e<"u"?c.objToMap(e):void 0}async persist(){await this.setKeyChain(this.keychain)}isInitialized(){if(!this.initialized){const{message:e}=c.getInternalError("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Hs=Object.defineProperty,Js=(r,e,t)=>e in r?Hs(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,P=(r,e,t)=>Js(r,typeof e!="symbol"?e+"":e,t);class xt{constructor(e,t,s){this.core=e,this.logger=t,P(this,"name",ze),P(this,"keychain"),P(this,"randomSessionIdentifier",c.generateRandomBytes32()),P(this,"initialized",!1),P(this,"init",async()=>{this.initialized||(await this.keychain.init(),this.initialized=!0)}),P(this,"hasKeys",i=>(this.isInitialized(),this.keychain.has(i))),P(this,"getClientId",async()=>{this.isInitialized();const i=await this.getClientSeed(),n=ue.generateKeyPair(i);return ue.encodeIss(n.publicKey)}),P(this,"generateKeyPair",()=>{this.isInitialized();const i=c.generateKeyPair();return this.setPrivateKey(i.publicKey,i.privateKey)}),P(this,"signJWT",async i=>{this.isInitialized();const n=await this.getClientSeed(),o=ue.generateKeyPair(n),a=this.randomSessionIdentifier,h=Me;return await ue.signJWT(a,i,h,o)}),P(this,"generateSharedKey",(i,n,o)=>{this.isInitialized();const a=this.getPrivateKey(i),h=c.deriveSymKey(a,n);return this.setSymKey(h,o)}),P(this,"setSymKey",async(i,n)=>{this.isInitialized();const o=n||c.hashKey(i);return await this.keychain.set(o,i),o}),P(this,"deleteKeyPair",async i=>{this.isInitialized(),await this.keychain.del(i)}),P(this,"deleteSymKey",async i=>{this.isInitialized(),await this.keychain.del(i)}),P(this,"encode",async(i,n,o)=>{this.isInitialized();const a=c.validateEncoding(o),h=Ee.safeJsonStringify(n);if(c.isTypeTwoEnvelope(a))return c.encodeTypeTwoEnvelope(h,o?.encoding);if(c.isTypeOneEnvelope(a)){const _=a.senderPublicKey,x=a.receiverPublicKey;i=await this.generateSharedKey(_,x)}const l=this.getSymKey(i),{type:g,senderPublicKey:E}=a;return c.encrypt({type:g,symKey:l,message:h,senderPublicKey:E,encoding:o?.encoding})}),P(this,"decode",async(i,n,o)=>{this.isInitialized();const a=c.validateDecoding(n,o);if(c.isTypeTwoEnvelope(a)){const h=c.decodeTypeTwoEnvelope(n,o?.encoding);return Ee.safeJsonParse(h)}if(c.isTypeOneEnvelope(a)){const h=a.receiverPublicKey,l=a.senderPublicKey;i=await this.generateSharedKey(h,l)}try{const h=this.getSymKey(i),l=c.decrypt({symKey:h,encoded:n,encoding:o?.encoding});return Ee.safeJsonParse(l)}catch(h){this.logger.error(`Failed to decode message from topic: '${i}', clientId: '${await this.getClientId()}'`),this.logger.error(h)}}),P(this,"getPayloadType",(i,n=c.BASE64)=>{const o=c.deserialize({encoded:i,encoding:n});return c.decodeTypeByte(o.type)}),P(this,"getPayloadSenderPublicKey",(i,n=c.BASE64)=>{const o=c.deserialize({encoded:i,encoding:n});return o.senderPublicKey?ti.toString(o.senderPublicKey,c.BASE16):void 0}),this.core=e,this.logger=b.generateChildLogger(t,this.name),this.keychain=s||new Nt(this.core,this.logger)}get context(){return b.getLoggerContext(this.logger)}async setPrivateKey(e,t){return await this.keychain.set(e,t),e}getPrivateKey(e){return this.keychain.get(e)}async getClientSeed(){let e="";try{e=this.keychain.get(_e)}catch{e=c.generateRandomBytes32(),await this.keychain.set(_e,e)}return qs(e,"base16")}getSymKey(e){return this.keychain.get(e)}isInitialized(){if(!this.initialized){const{message:e}=c.getInternalError("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Xs=Object.defineProperty,Ws=(r,e,t)=>e in r?Xs(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,H=(r,e,t)=>Ws(r,typeof e!="symbol"?e+"":e,t);class Lt extends B.IMessageTracker{constructor(e,t){super(e,t),this.logger=e,this.core=t,H(this,"messages",new Map),H(this,"name",Ve),H(this,"version",Be),H(this,"initialized",!1),H(this,"storagePrefix",K),H(this,"init",async()=>{if(!this.initialized){this.logger.trace("Initialized");try{const s=await this.getRelayerMessages();typeof s<"u"&&(this.messages=s),this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",size:this.messages.size})}catch(s){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(s)}finally{this.initialized=!0}}}),H(this,"set",async(s,i)=>{this.isInitialized();const n=c.hashMessage(i);let o=this.messages.get(s);return typeof o>"u"&&(o={}),typeof o[n]<"u"||(o[n]=i,this.messages.set(s,o),await this.persist()),n}),H(this,"get",s=>{this.isInitialized();let i=this.messages.get(s);return typeof i>"u"&&(i={}),i}),H(this,"has",(s,i)=>{this.isInitialized();const n=this.get(s),o=c.hashMessage(i);return typeof n[o]<"u"}),H(this,"del",async s=>{this.isInitialized(),this.messages.delete(s),await this.persist()}),this.logger=b.generateChildLogger(e,this.name),this.core=t}get context(){return b.getLoggerContext(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}async setRelayerMessages(e){await this.core.storage.setItem(this.storageKey,c.mapToObj(e))}async getRelayerMessages(){const e=await this.core.storage.getItem(this.storageKey);return typeof e<"u"?c.objToMap(e):void 0}async persist(){await this.setRelayerMessages(this.messages)}isInitialized(){if(!this.initialized){const{message:e}=c.getInternalError("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Zs=Object.defineProperty,Qs=Object.defineProperties,er=Object.getOwnPropertyDescriptors,$t=Object.getOwnPropertySymbols,tr=Object.prototype.hasOwnProperty,ir=Object.prototype.propertyIsEnumerable,Ce=(r,e,t)=>e in r?Zs(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,pe=(r,e)=>{for(var t in e||(e={}))tr.call(e,t)&&Ce(r,t,e[t]);if($t)for(var t of $t(e))ir.call(e,t)&&Ce(r,t,e[t]);return r},Se=(r,e)=>Qs(r,er(e)),j=(r,e,t)=>Ce(r,typeof e!="symbol"?e+"":e,t);class sr extends B.IPublisher{constructor(e,t){super(e,t),this.relayer=e,this.logger=t,j(this,"events",new Z.EventEmitter),j(this,"name",Ke),j(this,"queue",new Map),j(this,"publishTimeout",u.toMiliseconds(u.ONE_MINUTE)),j(this,"initialPublishTimeout",u.toMiliseconds(u.ONE_SECOND*15)),j(this,"needsTransportRestart",!1),j(this,"publish",async(s,i,n)=>{var o;this.logger.debug("Publishing Payload"),this.logger.trace({type:"method",method:"publish",params:{topic:s,message:i,opts:n}});const a=n?.ttl||ve,h=c.getRelayProtocolName(n),l=n?.prompt||!1,g=n?.tag||0,E=n?.id||L.getBigIntRpcId().toString(),_={topic:s,message:i,opts:{ttl:a,relay:h,prompt:l,tag:g,id:E,attestation:n?.attestation,tvf:n?.tvf}},x=`Failed to publish payload, please try again. id:${E} tag:${g}`;try{const d=new Promise(async D=>{const N=({id:v})=>{_.opts.id===v&&(this.removeRequestFromQueue(v),this.relayer.events.removeListener(C.publish,N),D(_))};this.relayer.events.on(C.publish,N);const I=c.createExpiringPromise(new Promise((v,M)=>{this.rpcPublish({topic:s,message:i,ttl:a,prompt:l,tag:g,id:E,attestation:n?.attestation,tvf:n?.tvf}).then(v).catch(R=>{this.logger.warn(R,R?.message),M(R)})}),this.initialPublishTimeout,`Failed initial publish, retrying.... id:${E} tag:${g}`);try{await I,this.events.removeListener(C.publish,N)}catch(v){this.queue.set(E,Se(pe({},_),{attempt:1})),this.logger.warn(v,v?.message)}});this.logger.trace({type:"method",method:"publish",params:{id:E,topic:s,message:i,opts:n}}),await c.createExpiringPromise(d,this.publishTimeout,x)}catch(d){if(this.logger.debug("Failed to Publish Payload"),this.logger.error(d),(o=n?.internal)!=null&&o.throwOnFailedPublish)throw d}finally{this.queue.delete(E)}}),j(this,"on",(s,i)=>{this.events.on(s,i)}),j(this,"once",(s,i)=>{this.events.once(s,i)}),j(this,"off",(s,i)=>{this.events.off(s,i)}),j(this,"removeListener",(s,i)=>{this.events.removeListener(s,i)}),this.relayer=e,this.logger=b.generateChildLogger(t,this.name),this.registerEventListeners()}get context(){return b.getLoggerContext(this.logger)}async rpcPublish(e){var t,s,i,n;const{topic:o,message:a,ttl:h=ve,prompt:l,tag:g,id:E,attestation:_,tvf:x}=e,d={method:c.getRelayProtocolApi(c.getRelayProtocolName().protocol).publish,params:pe({topic:o,message:a,ttl:h,prompt:l,tag:g,attestation:_},x),id:E};c.isUndefined((t=d.params)==null?void 0:t.prompt)&&((s=d.params)==null||delete s.prompt),c.isUndefined((i=d.params)==null?void 0:i.tag)&&((n=d.params)==null||delete n.tag),this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"message",direction:"outgoing",request:d});const D=await this.relayer.request(d);return this.relayer.events.emit(C.publish,e),this.logger.debug("Successfully Published Payload"),D}removeRequestFromQueue(e){this.queue.delete(e)}checkQueue(){this.queue.forEach(async(e,t)=>{const s=e.attempt+1;this.queue.set(t,Se(pe({},e),{attempt:s}));const{topic:i,message:n,opts:o,attestation:a}=e;this.logger.warn({},`Publisher: queue->publishing: ${e.opts.id}, tag: ${e.opts.tag}, attempt: ${s}`),await this.rpcPublish(Se(pe({},e),{topic:i,message:n,ttl:o.ttl,prompt:o.prompt,tag:o.tag,id:o.id,attestation:a,tvf:o.tvf})),this.logger.warn({},`Publisher: queue->published: ${e.opts.id}`)})}registerEventListeners(){this.relayer.core.heartbeat.on(ee.HEARTBEAT_EVENTS.pulse,()=>{if(this.needsTransportRestart){this.needsTransportRestart=!1,this.relayer.events.emit(C.connection_stalled);return}this.checkQueue()}),this.relayer.on(C.message_ack,e=>{this.removeRequestFromQueue(e.id.toString())})}}var rr=Object.defineProperty,nr=(r,e,t)=>e in r?rr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,se=(r,e,t)=>nr(r,typeof e!="symbol"?e+"":e,t);class or{constructor(){se(this,"map",new Map),se(this,"set",(e,t)=>{const s=this.get(e);this.exists(e,t)||this.map.set(e,[...s,t])}),se(this,"get",e=>this.map.get(e)||[]),se(this,"exists",(e,t)=>this.get(e).includes(t)),se(this,"delete",(e,t)=>{if(typeof t>"u"){this.map.delete(e);return}if(!this.map.has(e))return;const s=this.get(e);if(!this.exists(e,t))return;const i=s.filter(n=>n!==t);if(!i.length){this.map.delete(e);return}this.map.set(e,i)}),se(this,"clear",()=>{this.map.clear()})}get topics(){return Array.from(this.map.keys())}}var ar=Object.defineProperty,cr=Object.defineProperties,hr=Object.getOwnPropertyDescriptors,Ut=Object.getOwnPropertySymbols,lr=Object.prototype.hasOwnProperty,ur=Object.prototype.propertyIsEnumerable,Pe=(r,e,t)=>e in r?ar(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,ae=(r,e)=>{for(var t in e||(e={}))lr.call(e,t)&&Pe(r,t,e[t]);if(Ut)for(var t of Ut(e))ur.call(e,t)&&Pe(r,t,e[t]);return r},Oe=(r,e)=>cr(r,hr(e)),m=(r,e,t)=>Pe(r,typeof e!="symbol"?e+"":e,t);class zt extends B.ISubscriber{constructor(e,t){super(e,t),this.relayer=e,this.logger=t,m(this,"subscriptions",new Map),m(this,"topicMap",new or),m(this,"events",new Z.EventEmitter),m(this,"name",We),m(this,"version",Ze),m(this,"pending",new Map),m(this,"cached",[]),m(this,"initialized",!1),m(this,"pendingSubscriptionWatchLabel","pending_sub_watch_label"),m(this,"pollingInterval",20),m(this,"storagePrefix",K),m(this,"subscribeTimeout",u.toMiliseconds(u.ONE_MINUTE)),m(this,"initialSubscribeTimeout",u.toMiliseconds(u.ONE_SECOND*15)),m(this,"clientId"),m(this,"batchSubscribeTopicsLimit",500),m(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),this.registerEventListeners(),await this.restore()),this.initialized=!0}),m(this,"subscribe",async(s,i)=>{this.isInitialized(),this.logger.debug("Subscribing Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:s,opts:i}});try{const n=c.getRelayProtocolName(i),o={topic:s,relay:n,transportType:i?.transportType};this.pending.set(s,o);const a=await this.rpcSubscribe(s,n,i);return typeof a=="string"&&(this.onSubscribe(a,o),this.logger.debug("Successfully Subscribed Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:s,opts:i}})),a}catch(n){throw this.logger.debug("Failed to Subscribe Topic"),this.logger.error(n),n}}),m(this,"unsubscribe",async(s,i)=>{this.isInitialized(),typeof i?.id<"u"?await this.unsubscribeById(s,i.id,i):await this.unsubscribeByTopic(s,i)}),m(this,"isSubscribed",async s=>{if(this.topics.includes(s))return!0;const i=`${this.pendingSubscriptionWatchLabel}_${s}`;return await new Promise((n,o)=>{const a=new u.Watch;a.start(i);const h=setInterval(()=>{(!this.pending.has(s)&&this.topics.includes(s)||this.cached.some(l=>l.topic===s))&&(clearInterval(h),a.stop(i),n(!0)),a.elapsed(i)>=Qe&&(clearInterval(h),a.stop(i),o(new Error("Subscription resolution timeout")))},this.pollingInterval)}).catch(()=>!1)}),m(this,"on",(s,i)=>{this.events.on(s,i)}),m(this,"once",(s,i)=>{this.events.once(s,i)}),m(this,"off",(s,i)=>{this.events.off(s,i)}),m(this,"removeListener",(s,i)=>{this.events.removeListener(s,i)}),m(this,"start",async()=>{await this.onConnect()}),m(this,"stop",async()=>{await this.onDisconnect()}),m(this,"restart",async()=>{await this.restore(),await this.onRestart()}),m(this,"checkPending",async()=>{if(this.pending.size===0&&(!this.initialized||!this.relayer.connected))return;const s=[];this.pending.forEach(i=>{s.push(i)}),await this.batchSubscribe(s)}),m(this,"registerEventListeners",()=>{this.relayer.core.heartbeat.on(ee.HEARTBEAT_EVENTS.pulse,async()=>{await this.checkPending()}),this.events.on($.created,async s=>{const i=$.created;this.logger.info(`Emitting ${i}`),this.logger.debug({type:"event",event:i,data:s}),await this.persist()}),this.events.on($.deleted,async s=>{const i=$.deleted;this.logger.info(`Emitting ${i}`),this.logger.debug({type:"event",event:i,data:s}),await this.persist()})}),this.relayer=e,this.logger=b.generateChildLogger(t,this.name),this.clientId=""}get context(){return b.getLoggerContext(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.relayer.core.customStoragePrefix+"//"+this.name}get length(){return this.subscriptions.size}get ids(){return Array.from(this.subscriptions.keys())}get values(){return Array.from(this.subscriptions.values())}get topics(){return this.topicMap.topics}get hasAnyTopics(){return this.topicMap.topics.length>0||this.pending.size>0||this.cached.length>0||this.subscriptions.size>0}hasSubscription(e,t){let s=!1;try{s=this.getSubscription(e).topic===t}catch{}return s}reset(){this.cached=[],this.initialized=!0}onDisable(){this.cached=this.values,this.subscriptions.clear(),this.topicMap.clear()}async unsubscribeByTopic(e,t){const s=this.topicMap.get(e);await Promise.all(s.map(async i=>await this.unsubscribeById(e,i,t)))}async unsubscribeById(e,t,s){this.logger.debug("Unsubscribing Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:e,id:t,opts:s}});try{const i=c.getRelayProtocolName(s);await this.restartToComplete({topic:e,id:t,relay:i}),await this.rpcUnsubscribe(e,t,i);const n=c.getSdkError("USER_DISCONNECTED",`${this.name}, ${e}`);await this.onUnsubscribe(e,t,n),this.logger.debug("Successfully Unsubscribed Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:e,id:t,opts:s}})}catch(i){throw this.logger.debug("Failed to Unsubscribe Topic"),this.logger.error(i),i}}async rpcSubscribe(e,t,s){var i;(!s||s?.transportType===Q.relay)&&await this.restartToComplete({topic:e,id:e,relay:t});const n={method:c.getRelayProtocolApi(t.protocol).subscribe,params:{topic:e}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:n});const o=(i=s?.internal)==null?void 0:i.throwOnFailedPublish;try{const a=await this.getSubscriptionId(e);if(s?.transportType===Q.link_mode)return setTimeout(()=>{(this.relayer.connected||this.relayer.connecting)&&this.relayer.request(n).catch(g=>this.logger.warn(g))},u.toMiliseconds(u.ONE_SECOND)),a;const h=new Promise(async g=>{const E=_=>{_.topic===e&&(this.events.removeListener($.created,E),g(_.id))};this.events.on($.created,E);try{const _=await c.createExpiringPromise(new Promise((x,d)=>{this.relayer.request(n).catch(D=>{this.logger.warn(D,D?.message),d(D)}).then(x)}),this.initialSubscribeTimeout,`Subscribing to ${e} failed, please try again`);this.events.removeListener($.created,E),g(_)}catch{}}),l=await c.createExpiringPromise(h,this.subscribeTimeout,`Subscribing to ${e} failed, please try again`);if(!l&&o)throw new Error(`Subscribing to ${e} failed, please try again`);return l?a:null}catch(a){if(this.logger.debug("Outgoing Relay Subscribe Payload stalled"),this.relayer.events.emit(C.connection_stalled),o)throw a}return null}async rpcBatchSubscribe(e){if(!e.length)return;const t=e[0].relay,s={method:c.getRelayProtocolApi(t.protocol).batchSubscribe,params:{topics:e.map(i=>i.topic)}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:s});try{await await c.createExpiringPromise(new Promise(i=>{this.relayer.request(s).catch(n=>this.logger.warn(n)).then(i)}),this.subscribeTimeout,"rpcBatchSubscribe failed, please try again")}catch{this.relayer.events.emit(C.connection_stalled)}}async rpcBatchFetchMessages(e){if(!e.length)return;const t=e[0].relay,s={method:c.getRelayProtocolApi(t.protocol).batchFetchMessages,params:{topics:e.map(n=>n.topic)}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:s});let i;try{i=await await c.createExpiringPromise(new Promise((n,o)=>{this.relayer.request(s).catch(a=>{this.logger.warn(a),o(a)}).then(n)}),this.subscribeTimeout,"rpcBatchFetchMessages failed, please try again")}catch{this.relayer.events.emit(C.connection_stalled)}return i}rpcUnsubscribe(e,t,s){const i={method:c.getRelayProtocolApi(s.protocol).unsubscribe,params:{topic:e,id:t}};return this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:i}),this.relayer.request(i)}onSubscribe(e,t){this.setSubscription(e,Oe(ae({},t),{id:e})),this.pending.delete(t.topic)}onBatchSubscribe(e){e.length&&e.forEach(t=>{this.setSubscription(t.id,ae({},t)),this.pending.delete(t.topic)})}async onUnsubscribe(e,t,s){this.events.removeAllListeners(t),this.hasSubscription(t,e)&&this.deleteSubscription(t,s),await this.relayer.messages.del(e)}async setRelayerSubscriptions(e){await this.relayer.core.storage.setItem(this.storageKey,e)}async getRelayerSubscriptions(){return await this.relayer.core.storage.getItem(this.storageKey)}setSubscription(e,t){this.logger.debug("Setting subscription"),this.logger.trace({type:"method",method:"setSubscription",id:e,subscription:t}),this.addSubscription(e,t)}addSubscription(e,t){this.subscriptions.set(e,ae({},t)),this.topicMap.set(t.topic,e),this.events.emit($.created,t)}getSubscription(e){this.logger.debug("Getting subscription"),this.logger.trace({type:"method",method:"getSubscription",id:e});const t=this.subscriptions.get(e);if(!t){const{message:s}=c.getInternalError("NO_MATCHING_KEY",`${this.name}: ${e}`);throw new Error(s)}return t}deleteSubscription(e,t){this.logger.debug("Deleting subscription"),this.logger.trace({type:"method",method:"deleteSubscription",id:e,reason:t});const s=this.getSubscription(e);this.subscriptions.delete(e),this.topicMap.delete(s.topic,e),this.events.emit($.deleted,Oe(ae({},s),{reason:t}))}async persist(){await this.setRelayerSubscriptions(this.values),this.events.emit($.sync)}async onRestart(){if(this.cached.length){const e=[...this.cached],t=Math.ceil(this.cached.length/this.batchSubscribeTopicsLimit);for(let s=0;s<t;s++){const i=e.splice(0,this.batchSubscribeTopicsLimit);await this.batchSubscribe(i)}}this.events.emit($.resubscribed)}async restore(){try{const e=await this.getRelayerSubscriptions();if(typeof e>"u"||!e.length)return;if(this.subscriptions.size){const{message:t}=c.getInternalError("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(t),this.logger.error(`${this.name}: ${JSON.stringify(this.values)}`),new Error(t)}this.cached=e,this.logger.debug(`Successfully Restored subscriptions for ${this.name}`),this.logger.trace({type:"method",method:"restore",subscriptions:this.values})}catch(e){this.logger.debug(`Failed to Restore subscriptions for ${this.name}`),this.logger.error(e)}}async batchSubscribe(e){e.length&&(await this.rpcBatchSubscribe(e),this.onBatchSubscribe(await Promise.all(e.map(async t=>Oe(ae({},t),{id:await this.getSubscriptionId(t.topic)})))))}async batchFetchMessages(e){if(!e.length)return;this.logger.trace(`Fetching batch messages for ${e.length} subscriptions`);const t=await this.rpcBatchFetchMessages(e);t&&t.messages&&(await c.sleep(u.toMiliseconds(u.ONE_SECOND)),await this.relayer.handleBatchMessageEvents(t.messages))}async onConnect(){await this.restart(),this.reset()}onDisconnect(){this.onDisable()}isInitialized(){if(!this.initialized){const{message:e}=c.getInternalError("NOT_INITIALIZED",this.name);throw new Error(e)}}async restartToComplete(e){!this.relayer.connected&&!this.relayer.connecting&&(this.cached.push(e),await this.relayer.transportOpen())}async getClientId(){return this.clientId||(this.clientId=await this.relayer.core.crypto.getClientId()),this.clientId}async getSubscriptionId(e){return c.hashMessage(e+await this.getClientId())}}var dr=Object.defineProperty,Mt=Object.getOwnPropertySymbols,gr=Object.prototype.hasOwnProperty,pr=Object.prototype.propertyIsEnumerable,Ae=(r,e,t)=>e in r?dr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,Ft=(r,e)=>{for(var t in e||(e={}))gr.call(e,t)&&Ae(r,t,e[t]);if(Mt)for(var t of Mt(e))pr.call(e,t)&&Ae(r,t,e[t]);return r},y=(r,e,t)=>Ae(r,typeof e!="symbol"?e+"":e,t);class kt extends B.IRelayer{constructor(e){super(e),y(this,"protocol","wc"),y(this,"version",2),y(this,"core"),y(this,"logger"),y(this,"events",new Z.EventEmitter),y(this,"provider"),y(this,"messages"),y(this,"subscriber"),y(this,"publisher"),y(this,"name",Ye),y(this,"transportExplicitlyClosed",!1),y(this,"initialized",!1),y(this,"connectionAttemptInProgress",!1),y(this,"relayUrl"),y(this,"projectId"),y(this,"packageName"),y(this,"bundleId"),y(this,"hasExperiencedNetworkDisruption",!1),y(this,"pingTimeout"),y(this,"heartBeatTimeout",u.toMiliseconds(u.THIRTY_SECONDS+u.FIVE_SECONDS)),y(this,"reconnectTimeout"),y(this,"connectPromise"),y(this,"reconnectInProgress",!1),y(this,"requestsInFlight",[]),y(this,"connectTimeout",u.toMiliseconds(u.ONE_SECOND*15)),y(this,"request",async t=>{var s,i;this.logger.debug("Publishing Request Payload");const n=t.id||L.getBigIntRpcId().toString();await this.toEstablishConnection();try{this.logger.trace({id:n,method:t.method,topic:(s=t.params)==null?void 0:s.topic},"relayer.request - publishing...");const o=`${n}:${((i=t.params)==null?void 0:i.tag)||""}`;this.requestsInFlight.push(o);const a=await this.provider.request(t);return this.requestsInFlight=this.requestsInFlight.filter(h=>h!==o),a}catch(o){throw this.logger.debug(`Failed to Publish Request: ${n}`),o}}),y(this,"resetPingTimeout",()=>{if(c.isNode())try{clearTimeout(this.pingTimeout),this.pingTimeout=setTimeout(()=>{var t,s,i;this.logger.debug({},"pingTimeout: Connection stalled, terminating..."),(i=(s=(t=this.provider)==null?void 0:t.connection)==null?void 0:s.socket)==null||i.terminate()},this.heartBeatTimeout)}catch(t){this.logger.warn(t,t?.message)}}),y(this,"onPayloadHandler",t=>{this.onProviderPayload(t),this.resetPingTimeout()}),y(this,"onConnectHandler",()=>{this.logger.warn({},"Relayer connected \u{1F6DC}"),this.startPingTimeout(),this.events.emit(C.connect)}),y(this,"onDisconnectHandler",()=>{this.logger.warn({},"Relayer disconnected \u{1F6D1}"),this.requestsInFlight=[],this.onProviderDisconnect()}),y(this,"onProviderErrorHandler",t=>{this.logger.fatal(`Fatal socket error: ${t.message}`),this.events.emit(C.error,t),this.logger.fatal("Fatal socket error received, closing transport"),this.transportClose()}),y(this,"registerProviderListeners",()=>{this.provider.on(z.payload,this.onPayloadHandler),this.provider.on(z.connect,this.onConnectHandler),this.provider.on(z.disconnect,this.onDisconnectHandler),this.provider.on(z.error,this.onProviderErrorHandler)}),this.core=e.core,this.logger=typeof e.logger<"u"&&typeof e.logger!="string"?b.generateChildLogger(e.logger,this.name):b.pino(b.getDefaultLoggerOptions({level:e.logger||qe})),this.messages=new Lt(this.logger,e.core),this.subscriber=new zt(this,this.logger),this.publisher=new sr(this,this.logger),this.relayUrl=e?.relayUrl||fe,this.projectId=e.projectId,c.isAndroid()?this.packageName=c.getAppId():c.isIos()&&(this.bundleId=c.getAppId()),this.provider={}}async init(){if(this.logger.trace("Initialized"),this.registerEventListeners(),await Promise.all([this.messages.init(),this.subscriber.init()]),this.initialized=!0,this.subscriber.hasAnyTopics)try{await this.transportOpen()}catch(e){this.logger.warn(e,e?.message)}}get context(){return b.getLoggerContext(this.logger)}get connected(){var e,t,s;return((s=(t=(e=this.provider)==null?void 0:e.connection)==null?void 0:t.socket)==null?void 0:s.readyState)===1||!1}get connecting(){var e,t,s;return((s=(t=(e=this.provider)==null?void 0:e.connection)==null?void 0:t.socket)==null?void 0:s.readyState)===0||this.connectPromise!==void 0||!1}async publish(e,t,s){this.isInitialized(),await this.publisher.publish(e,t,s),await this.recordMessageEvent({topic:e,message:t,publishedAt:Date.now(),transportType:Q.relay})}async subscribe(e,t){var s,i,n;this.isInitialized(),(!(t!=null&&t.transportType)||t?.transportType==="relay")&&await this.toEstablishConnection();const o=typeof((s=t?.internal)==null?void 0:s.throwOnFailedPublish)>"u"?!0:(i=t?.internal)==null?void 0:i.throwOnFailedPublish;let a=((n=this.subscriber.topicMap.get(e))==null?void 0:n[0])||"",h;const l=g=>{g.topic===e&&(this.subscriber.off($.created,l),h())};return await Promise.all([new Promise(g=>{h=g,this.subscriber.on($.created,l)}),new Promise(async(g,E)=>{a=await this.subscriber.subscribe(e,Ft({internal:{throwOnFailedPublish:o}},t)).catch(_=>{o&&E(_)})||a,g()})]),a}async unsubscribe(e,t){this.isInitialized(),await this.subscriber.unsubscribe(e,t)}on(e,t){this.events.on(e,t)}once(e,t){this.events.once(e,t)}off(e,t){this.events.off(e,t)}removeListener(e,t){this.events.removeListener(e,t)}async transportDisconnect(){this.provider.disconnect&&(this.hasExperiencedNetworkDisruption||this.connected)?await c.createExpiringPromise(this.provider.disconnect(),2e3,"provider.disconnect()").catch(()=>this.onProviderDisconnect()):this.onProviderDisconnect()}async transportClose(){this.transportExplicitlyClosed=!0,await this.transportDisconnect()}async transportOpen(e){if(!this.subscriber.hasAnyTopics){this.logger.warn("Starting WS connection skipped because the client has no topics to work with.");return}if(this.connectPromise?(this.logger.debug({},"Waiting for existing connection attempt to resolve..."),await this.connectPromise,this.logger.debug({},"Existing connection attempt resolved")):(this.connectPromise=new Promise(async(t,s)=>{await this.connect(e).then(t).catch(s).finally(()=>{this.connectPromise=void 0})}),await this.connectPromise),!this.connected)throw new Error(`Couldn't establish socket connection to the relay server: ${this.relayUrl}`)}async restartTransport(e){this.logger.debug({},"Restarting transport..."),!this.connectionAttemptInProgress&&(this.relayUrl=e||this.relayUrl,await this.confirmOnlineStateOrThrow(),await this.transportClose(),await this.transportOpen())}async confirmOnlineStateOrThrow(){if(!await c.isOnline())throw new Error("No internet connection detected. Please restart your network and try again.")}async handleBatchMessageEvents(e){if(e?.length===0){this.logger.trace("Batch message events is empty. Ignoring...");return}const t=e.sort((s,i)=>s.publishedAt-i.publishedAt);this.logger.debug(`Batch of ${t.length} message events sorted`);for(const s of t)try{await this.onMessageEvent(s)}catch(i){this.logger.warn(i,"Error while processing batch message event: "+i?.message)}this.logger.trace(`Batch of ${t.length} message events processed`)}async onLinkMessageEvent(e,t){const{topic:s}=e;if(!t.sessionExists){const i=c.calcExpiry(u.FIVE_MINUTES),n={topic:s,expiry:i,relay:{protocol:"irn"},active:!1};await this.core.pairing.pairings.set(s,n)}this.events.emit(C.message,e),await this.recordMessageEvent(e)}async connect(e){await this.confirmOnlineStateOrThrow(),e&&e!==this.relayUrl&&(this.relayUrl=e,await this.transportDisconnect()),this.connectionAttemptInProgress=!0,this.transportExplicitlyClosed=!1;let t=1;for(;t<6;){try{if(this.transportExplicitlyClosed)break;this.logger.debug({},`Connecting to ${this.relayUrl}, attempt: ${t}...`),await this.createProvider(),await new Promise(async(s,i)=>{const n=()=>{i(new Error("Connection interrupted while trying to subscribe"))};this.provider.once(z.disconnect,n),await c.createExpiringPromise(new Promise((o,a)=>{this.provider.connect().then(o).catch(a)}),this.connectTimeout,`Socket stalled when trying to connect to ${this.relayUrl}`).catch(o=>{i(o)}).finally(()=>{this.provider.off(z.disconnect,n),clearTimeout(this.reconnectTimeout)}),await new Promise(async(o,a)=>{const h=()=>{a(new Error("Connection interrupted while trying to subscribe"))};this.provider.once(z.disconnect,h),await this.subscriber.start().then(o).catch(a).finally(()=>{this.provider.off(z.disconnect,h)})}),this.hasExperiencedNetworkDisruption=!1,s()})}catch(s){await this.subscriber.stop();const i=s;this.logger.warn({},i.message),this.hasExperiencedNetworkDisruption=!0}finally{this.connectionAttemptInProgress=!1}if(this.connected){this.logger.debug({},`Connected to ${this.relayUrl} successfully on attempt: ${t}`);break}await new Promise(s=>setTimeout(s,u.toMiliseconds(t*1))),t++}}startPingTimeout(){var e,t,s,i,n;if(c.isNode())try{(t=(e=this.provider)==null?void 0:e.connection)!=null&&t.socket&&((n=(i=(s=this.provider)==null?void 0:s.connection)==null?void 0:i.socket)==null||n.on("ping",()=>{this.resetPingTimeout()})),this.resetPingTimeout()}catch(o){this.logger.warn(o,o?.message)}}async createProvider(){this.provider.connection&&this.unregisterProviderListeners();const e=await this.core.crypto.signJWT(this.relayUrl);this.provider=new ii.JsonRpcProvider(new hi.default(c.formatRelayRpcUrl({sdkVersion:de,protocol:this.protocol,version:this.version,relayUrl:this.relayUrl,projectId:this.projectId,auth:e,useOnCloseEvent:!0,bundleId:this.bundleId,packageName:this.packageName}))),this.registerProviderListeners()}async recordMessageEvent(e){const{topic:t,message:s}=e;await this.messages.set(t,s)}async shouldIgnoreMessageEvent(e){const{topic:t,message:s}=e;if(!s||s.length===0)return this.logger.warn(`Ignoring invalid/empty message: ${s}`),!0;if(!await this.subscriber.isSubscribed(t))return this.logger.warn(`Ignoring message for non-subscribed topic ${t}`),!0;const i=this.messages.has(t,s);return i&&this.logger.warn(`Ignoring duplicate message: ${s}`),i}async onProviderPayload(e){if(this.logger.debug("Incoming Relay Payload"),this.logger.trace({type:"payload",direction:"incoming",payload:e}),L.isJsonRpcRequest(e)){if(!e.method.endsWith(Ge))return;const t=e.params,{topic:s,message:i,publishedAt:n,attestation:o}=t.data,a={topic:s,message:i,publishedAt:n,transportType:Q.relay,attestation:o};this.logger.debug("Emitting Relayer Payload"),this.logger.trace(Ft({type:"event",event:t.id},a)),this.events.emit(t.id,a),await this.acknowledgePayload(e),await this.onMessageEvent(a)}else L.isJsonRpcResponse(e)&&this.events.emit(C.message_ack,e)}async onMessageEvent(e){await this.shouldIgnoreMessageEvent(e)||(this.events.emit(C.message,e),await this.recordMessageEvent(e))}async acknowledgePayload(e){const t=L.formatJsonRpcResult(e.id,!0);await this.provider.connection.send(t)}unregisterProviderListeners(){this.provider.off(z.payload,this.onPayloadHandler),this.provider.off(z.connect,this.onConnectHandler),this.provider.off(z.disconnect,this.onDisconnectHandler),this.provider.off(z.error,this.onProviderErrorHandler),clearTimeout(this.pingTimeout)}async registerEventListeners(){let e=await c.isOnline();c.subscribeToNetworkChange(async t=>{e!==t&&(e=t,t?await this.transportOpen().catch(s=>this.logger.error(s,s?.message)):(this.hasExperiencedNetworkDisruption=!0,await this.transportDisconnect(),this.transportExplicitlyClosed=!1))})}async onProviderDisconnect(){clearTimeout(this.pingTimeout),this.events.emit(C.disconnect),this.connectionAttemptInProgress=!1,!this.reconnectInProgress&&(this.reconnectInProgress=!0,await this.subscriber.stop(),this.subscriber.hasAnyTopics&&(this.transportExplicitlyClosed||(this.reconnectTimeout=setTimeout(async()=>{await this.transportOpen().catch(e=>this.logger.error(e,e?.message)),this.reconnectTimeout=void 0,this.reconnectInProgress=!1},u.toMiliseconds(He)))))}isInitialized(){if(!this.initialized){const{message:e}=c.getInternalError("NOT_INITIALIZED",this.name);throw new Error(e)}}async toEstablishConnection(){await this.confirmOnlineStateOrThrow(),!this.connected&&await this.connect()}}var yr=Object.defineProperty,Vt=Object.getOwnPropertySymbols,Er=Object.prototype.hasOwnProperty,Dr=Object.prototype.propertyIsEnumerable,Ne=(r,e,t)=>e in r?yr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,Bt=(r,e)=>{for(var t in e||(e={}))Er.call(e,t)&&Ne(r,t,e[t]);if(Vt)for(var t of Vt(e))Dr.call(e,t)&&Ne(r,t,e[t]);return r},U=(r,e,t)=>Ne(r,typeof e!="symbol"?e+"":e,t);class Kt extends B.IStore{constructor(e,t,s,i=K,n=void 0){super(e,t,s,i),this.core=e,this.logger=t,this.name=s,U(this,"map",new Map),U(this,"version",Je),U(this,"cached",[]),U(this,"initialized",!1),U(this,"getKey"),U(this,"storagePrefix",K),U(this,"recentlyDeleted",[]),U(this,"recentlyDeletedLimit",200),U(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(o=>{this.getKey&&o!==null&&!c.isUndefined(o)?this.map.set(this.getKey(o),o):c.isProposalStruct(o)?this.map.set(o.id,o):c.isSessionStruct(o)&&this.map.set(o.topic,o)}),this.cached=[],this.initialized=!0)}),U(this,"set",async(o,a)=>{this.isInitialized(),this.map.has(o)?await this.update(o,a):(this.logger.debug("Setting value"),this.logger.trace({type:"method",method:"set",key:o,value:a}),this.map.set(o,a),await this.persist())}),U(this,"get",o=>(this.isInitialized(),this.logger.debug("Getting value"),this.logger.trace({type:"method",method:"get",key:o}),this.getData(o))),U(this,"getAll",o=>(this.isInitialized(),o?this.values.filter(a=>Object.keys(o).every(h=>li.default(a[h],o[h]))):this.values)),U(this,"update",async(o,a)=>{this.isInitialized(),this.logger.debug("Updating value"),this.logger.trace({type:"method",method:"update",key:o,update:a});const h=Bt(Bt({},this.getData(o)),a);this.map.set(o,h),await this.persist()}),U(this,"delete",async(o,a)=>{this.isInitialized(),this.map.has(o)&&(this.logger.debug("Deleting value"),this.logger.trace({type:"method",method:"delete",key:o,reason:a}),this.map.delete(o),this.addToRecentlyDeleted(o),await this.persist())}),this.logger=b.generateChildLogger(t,this.name),this.storagePrefix=i,this.getKey=n}get context(){return b.getLoggerContext(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.map.size}get keys(){return Array.from(this.map.keys())}get values(){return Array.from(this.map.values())}addToRecentlyDeleted(e){this.recentlyDeleted.push(e),this.recentlyDeleted.length>=this.recentlyDeletedLimit&&this.recentlyDeleted.splice(0,this.recentlyDeletedLimit/2)}async setDataStore(e){await this.core.storage.setItem(this.storageKey,e)}async getDataStore(){return await this.core.storage.getItem(this.storageKey)}getData(e){const t=this.map.get(e);if(!t){if(this.recentlyDeleted.includes(e)){const{message:i}=c.getInternalError("MISSING_OR_INVALID",`Record was recently deleted - ${this.name}: ${e}`);throw this.logger.error(i),new Error(i)}const{message:s}=c.getInternalError("NO_MATCHING_KEY",`${this.name}: ${e}`);throw this.logger.error(s),new Error(s)}return t}async persist(){await this.setDataStore(this.values)}async restore(){try{const e=await this.getDataStore();if(typeof e>"u"||!e.length)return;if(this.map.size){const{message:t}=c.getInternalError("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(t),new Error(t)}this.cached=e,this.logger.debug(`Successfully Restored value for ${this.name}`),this.logger.trace({type:"method",method:"restore",value:this.values})}catch(e){this.logger.debug(`Failed to Restore value for ${this.name}`),this.logger.error(e)}}isInitialized(){if(!this.initialized){const{message:e}=c.getInternalError("NOT_INITIALIZED",this.name);throw new Error(e)}}}var br=Object.defineProperty,mr=(r,e,t)=>e in r?br(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,p=(r,e,t)=>mr(r,typeof e!="symbol"?e+"":e,t);class jt{constructor(e,t){this.core=e,this.logger=t,p(this,"name",et),p(this,"version",tt),p(this,"events",new ai.default),p(this,"pairings"),p(this,"initialized",!1),p(this,"storagePrefix",K),p(this,"ignoredPayloadTypes",[c.TYPE_1]),p(this,"registeredMethods",[]),p(this,"init",async()=>{this.initialized||(await this.pairings.init(),await this.cleanup(),this.registerRelayerEvents(),this.registerExpirerEvents(),this.initialized=!0,this.logger.trace("Initialized"))}),p(this,"register",({methods:s})=>{this.isInitialized(),this.registeredMethods=[...new Set([...this.registeredMethods,...s])]}),p(this,"create",async s=>{this.isInitialized();const i=c.generateRandomBytes32(),n=await this.core.crypto.setSymKey(i),o=c.calcExpiry(u.FIVE_MINUTES),a={protocol:je},h={topic:n,expiry:o,relay:a,active:!1,methods:s?.methods},l=c.formatUri({protocol:this.core.protocol,version:this.core.version,topic:n,symKey:i,relay:a,expiryTimestamp:o,methods:s?.methods});return this.events.emit(ie.create,h),this.core.expirer.set(n,o),await this.pairings.set(n,h),await this.core.relayer.subscribe(n,{transportType:s?.transportType}),{topic:n,uri:l}}),p(this,"pair",async s=>{this.isInitialized();const i=this.core.eventClient.createEvent({properties:{topic:s?.uri,trace:[Y.pairing_started]}});this.isValidPair(s,i);const{topic:n,symKey:o,relay:a,expiryTimestamp:h,methods:l}=c.parseUri(s.uri);i.props.properties.topic=n,i.addTrace(Y.pairing_uri_validation_success),i.addTrace(Y.pairing_uri_not_expired);let g;if(this.pairings.keys.includes(n)){if(g=this.pairings.get(n),i.addTrace(Y.existing_pairing),g.active)throw i.setError(X.active_pairing_already_exists),new Error(`Pairing already exists: ${n}. Please try again with a new connection URI.`);i.addTrace(Y.pairing_not_expired)}const E=h||c.calcExpiry(u.FIVE_MINUTES),_={topic:n,relay:a,expiry:E,active:!1,methods:l};this.core.expirer.set(n,E),await this.pairings.set(n,_),i.addTrace(Y.store_new_pairing),s.activatePairing&&await this.activate({topic:n}),this.events.emit(ie.create,_),i.addTrace(Y.emit_inactive_pairing),this.core.crypto.keychain.has(n)||await this.core.crypto.setSymKey(o,n),i.addTrace(Y.subscribing_pairing_topic);try{await this.core.relayer.confirmOnlineStateOrThrow()}catch{i.setError(X.no_internet_connection)}try{await this.core.relayer.subscribe(n,{relay:a})}catch(x){throw i.setError(X.subscribe_pairing_topic_failure),x}return i.addTrace(Y.subscribe_pairing_topic_success),_}),p(this,"activate",async({topic:s})=>{this.isInitialized();const i=c.calcExpiry(u.FIVE_MINUTES);this.core.expirer.set(s,i),await this.pairings.update(s,{active:!0,expiry:i})}),p(this,"ping",async s=>{this.isInitialized(),await this.isValidPing(s),this.logger.warn("ping() is deprecated and will be removed in the next major release.");const{topic:i}=s;if(this.pairings.keys.includes(i)){const n=await this.sendRequest(i,"wc_pairingPing",{}),{done:o,resolve:a,reject:h}=c.createDelayedPromise();this.events.once(c.engineEvent("pairing_ping",n),({error:l})=>{l?h(l):a()}),await o()}}),p(this,"updateExpiry",async({topic:s,expiry:i})=>{this.isInitialized(),await this.pairings.update(s,{expiry:i})}),p(this,"updateMetadata",async({topic:s,metadata:i})=>{this.isInitialized(),await this.pairings.update(s,{peerMetadata:i})}),p(this,"getPairings",()=>(this.isInitialized(),this.pairings.values)),p(this,"disconnect",async s=>{this.isInitialized(),await this.isValidDisconnect(s);const{topic:i}=s;this.pairings.keys.includes(i)&&(await this.sendRequest(i,"wc_pairingDelete",c.getSdkError("USER_DISCONNECTED")),await this.deletePairing(i))}),p(this,"formatUriFromPairing",s=>{this.isInitialized();const{topic:i,relay:n,expiry:o,methods:a}=s,h=this.core.crypto.keychain.get(i);return c.formatUri({protocol:this.core.protocol,version:this.core.version,topic:i,symKey:h,relay:n,expiryTimestamp:o,methods:a})}),p(this,"sendRequest",async(s,i,n)=>{const o=L.formatJsonRpcRequest(i,n),a=await this.core.crypto.encode(s,o),h=te[i].req;return this.core.history.set(s,o),this.core.relayer.publish(s,a,h),o.id}),p(this,"sendResult",async(s,i,n)=>{const o=L.formatJsonRpcResult(s,n),a=await this.core.crypto.encode(i,o),h=(await this.core.history.get(i,s)).request.method,l=te[h].res;await this.core.relayer.publish(i,a,l),await this.core.history.resolve(o)}),p(this,"sendError",async(s,i,n)=>{const o=L.formatJsonRpcError(s,n),a=await this.core.crypto.encode(i,o),h=(await this.core.history.get(i,s)).request.method,l=te[h]?te[h].res:te.unregistered_method.res;await this.core.relayer.publish(i,a,l),await this.core.history.resolve(o)}),p(this,"deletePairing",async(s,i)=>{await this.core.relayer.unsubscribe(s),await Promise.all([this.pairings.delete(s,c.getSdkError("USER_DISCONNECTED")),this.core.crypto.deleteSymKey(s),i?Promise.resolve():this.core.expirer.del(s)])}),p(this,"cleanup",async()=>{const s=this.pairings.getAll().filter(i=>c.isExpired(i.expiry));await Promise.all(s.map(i=>this.deletePairing(i.topic)))}),p(this,"onRelayEventRequest",s=>{const{topic:i,payload:n}=s;switch(n.method){case"wc_pairingPing":return this.onPairingPingRequest(i,n);case"wc_pairingDelete":return this.onPairingDeleteRequest(i,n);default:return this.onUnknownRpcMethodRequest(i,n)}}),p(this,"onRelayEventResponse",async s=>{const{topic:i,payload:n}=s,o=(await this.core.history.get(i,n.id)).request.method;switch(o){case"wc_pairingPing":return this.onPairingPingResponse(i,n);default:return this.onUnknownRpcMethodResponse(o)}}),p(this,"onPairingPingRequest",async(s,i)=>{const{id:n}=i;try{this.isValidPing({topic:s}),await this.sendResult(n,s,!0),this.events.emit(ie.ping,{id:n,topic:s})}catch(o){await this.sendError(n,s,o),this.logger.error(o)}}),p(this,"onPairingPingResponse",(s,i)=>{const{id:n}=i;setTimeout(()=>{L.isJsonRpcResult(i)?this.events.emit(c.engineEvent("pairing_ping",n),{}):L.isJsonRpcError(i)&&this.events.emit(c.engineEvent("pairing_ping",n),{error:i.error})},500)}),p(this,"onPairingDeleteRequest",async(s,i)=>{const{id:n}=i;try{this.isValidDisconnect({topic:s}),await this.deletePairing(s),this.events.emit(ie.delete,{id:n,topic:s})}catch(o){await this.sendError(n,s,o),this.logger.error(o)}}),p(this,"onUnknownRpcMethodRequest",async(s,i)=>{const{id:n,method:o}=i;try{if(this.registeredMethods.includes(o))return;const a=c.getSdkError("WC_METHOD_UNSUPPORTED",o);await this.sendError(n,s,a),this.logger.error(a)}catch(a){await this.sendError(n,s,a),this.logger.error(a)}}),p(this,"onUnknownRpcMethodResponse",s=>{this.registeredMethods.includes(s)||this.logger.error(c.getSdkError("WC_METHOD_UNSUPPORTED",s))}),p(this,"isValidPair",(s,i)=>{var n;if(!c.isValidParams(s)){const{message:a}=c.getInternalError("MISSING_OR_INVALID",`pair() params: ${s}`);throw i.setError(X.malformed_pairing_uri),new Error(a)}if(!c.isValidUrl(s.uri)){const{message:a}=c.getInternalError("MISSING_OR_INVALID",`pair() uri: ${s.uri}`);throw i.setError(X.malformed_pairing_uri),new Error(a)}const o=c.parseUri(s?.uri);if(!((n=o?.relay)!=null&&n.protocol)){const{message:a}=c.getInternalError("MISSING_OR_INVALID","pair() uri#relay-protocol");throw i.setError(X.malformed_pairing_uri),new Error(a)}if(!(o!=null&&o.symKey)){const{message:a}=c.getInternalError("MISSING_OR_INVALID","pair() uri#symKey");throw i.setError(X.malformed_pairing_uri),new Error(a)}if(o!=null&&o.expiryTimestamp&&u.toMiliseconds(o?.expiryTimestamp)<Date.now()){i.setError(X.pairing_expired);const{message:a}=c.getInternalError("EXPIRED","pair() URI has expired. Please try again with a new connection URI.");throw new Error(a)}}),p(this,"isValidPing",async s=>{if(!c.isValidParams(s)){const{message:n}=c.getInternalError("MISSING_OR_INVALID",`ping() params: ${s}`);throw new Error(n)}const{topic:i}=s;await this.isValidPairingTopic(i)}),p(this,"isValidDisconnect",async s=>{if(!c.isValidParams(s)){const{message:n}=c.getInternalError("MISSING_OR_INVALID",`disconnect() params: ${s}`);throw new Error(n)}const{topic:i}=s;await this.isValidPairingTopic(i)}),p(this,"isValidPairingTopic",async s=>{if(!c.isValidString(s,!1)){const{message:i}=c.getInternalError("MISSING_OR_INVALID",`pairing topic should be a string: ${s}`);throw new Error(i)}if(!this.pairings.keys.includes(s)){const{message:i}=c.getInternalError("NO_MATCHING_KEY",`pairing topic doesn't exist: ${s}`);throw new Error(i)}if(c.isExpired(this.pairings.get(s).expiry)){await this.deletePairing(s);const{message:i}=c.getInternalError("EXPIRED",`pairing topic: ${s}`);throw new Error(i)}}),this.core=e,this.logger=b.generateChildLogger(t,this.name),this.pairings=new Kt(this.core,this.logger,this.name,this.storagePrefix)}get context(){return b.getLoggerContext(this.logger)}isInitialized(){if(!this.initialized){const{message:e}=c.getInternalError("NOT_INITIALIZED",this.name);throw new Error(e)}}registerRelayerEvents(){this.core.relayer.on(C.message,async e=>{const{topic:t,message:s,transportType:i}=e;if(!this.pairings.keys.includes(t)||i===Q.link_mode||this.ignoredPayloadTypes.includes(this.core.crypto.getPayloadType(s)))return;const n=await this.core.crypto.decode(t,s);try{L.isJsonRpcRequest(n)?(this.core.history.set(t,n),this.onRelayEventRequest({topic:t,payload:n})):L.isJsonRpcResponse(n)&&(await this.core.history.resolve(n),await this.onRelayEventResponse({topic:t,payload:n}),this.core.history.delete(t,n.id))}catch(o){this.logger.error(o)}})}registerExpirerEvents(){this.core.expirer.on(k.expired,async e=>{const{topic:t}=c.parseExpirerTarget(e.target);t&&this.pairings.keys.includes(t)&&(await this.deletePairing(t,!0),this.events.emit(ie.expire,{topic:t}))})}}var _r=Object.defineProperty,vr=(r,e,t)=>e in r?_r(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,O=(r,e,t)=>vr(r,typeof e!="symbol"?e+"":e,t);class qt extends B.IJsonRpcHistory{constructor(e,t){super(e,t),this.core=e,this.logger=t,O(this,"records",new Map),O(this,"events",new Z.EventEmitter),O(this,"name",it),O(this,"version",st),O(this,"cached",[]),O(this,"initialized",!1),O(this,"storagePrefix",K),O(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(s=>this.records.set(s.id,s)),this.cached=[],this.registerEventListeners(),this.initialized=!0)}),O(this,"set",(s,i,n)=>{if(this.isInitialized(),this.logger.debug("Setting JSON-RPC request history record"),this.logger.trace({type:"method",method:"set",topic:s,request:i,chainId:n}),this.records.has(i.id))return;const o={id:i.id,topic:s,request:{method:i.method,params:i.params||null},chainId:n,expiry:c.calcExpiry(u.THIRTY_DAYS)};this.records.set(o.id,o),this.persist(),this.events.emit(F.created,o)}),O(this,"resolve",async s=>{if(this.isInitialized(),this.logger.debug("Updating JSON-RPC response history record"),this.logger.trace({type:"method",method:"update",response:s}),!this.records.has(s.id))return;const i=await this.getRecord(s.id);typeof i.response>"u"&&(i.response=L.isJsonRpcError(s)?{error:s.error}:{result:s.result},this.records.set(i.id,i),this.persist(),this.events.emit(F.updated,i))}),O(this,"get",async(s,i)=>(this.isInitialized(),this.logger.debug("Getting record"),this.logger.trace({type:"method",method:"get",topic:s,id:i}),await this.getRecord(i))),O(this,"delete",(s,i)=>{this.isInitialized(),this.logger.debug("Deleting record"),this.logger.trace({type:"method",method:"delete",id:i}),this.values.forEach(n=>{if(n.topic===s){if(typeof i<"u"&&n.id!==i)return;this.records.delete(n.id),this.events.emit(F.deleted,n)}}),this.persist()}),O(this,"exists",async(s,i)=>(this.isInitialized(),this.records.has(i)?(await this.getRecord(i)).topic===s:!1)),O(this,"on",(s,i)=>{this.events.on(s,i)}),O(this,"once",(s,i)=>{this.events.once(s,i)}),O(this,"off",(s,i)=>{this.events.off(s,i)}),O(this,"removeListener",(s,i)=>{this.events.removeListener(s,i)}),this.logger=b.generateChildLogger(t,this.name)}get context(){return b.getLoggerContext(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get size(){return this.records.size}get keys(){return Array.from(this.records.keys())}get values(){return Array.from(this.records.values())}get pending(){const e=[];return this.values.forEach(t=>{if(typeof t.response<"u")return;const s={topic:t.topic,request:L.formatJsonRpcRequest(t.request.method,t.request.params,t.id),chainId:t.chainId};return e.push(s)}),e}async setJsonRpcRecords(e){await this.core.storage.setItem(this.storageKey,e)}async getJsonRpcRecords(){return await this.core.storage.getItem(this.storageKey)}getRecord(e){this.isInitialized();const t=this.records.get(e);if(!t){const{message:s}=c.getInternalError("NO_MATCHING_KEY",`${this.name}: ${e}`);throw new Error(s)}return t}async persist(){await this.setJsonRpcRecords(this.values),this.events.emit(F.sync)}async restore(){try{const e=await this.getJsonRpcRecords();if(typeof e>"u"||!e.length)return;if(this.records.size){const{message:t}=c.getInternalError("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(t),new Error(t)}this.cached=e,this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",records:this.values})}catch(e){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(e)}}registerEventListeners(){this.events.on(F.created,e=>{const t=F.created;this.logger.info(`Emitting ${t}`),this.logger.debug({type:"event",event:t,record:e})}),this.events.on(F.updated,e=>{const t=F.updated;this.logger.info(`Emitting ${t}`),this.logger.debug({type:"event",event:t,record:e})}),this.events.on(F.deleted,e=>{const t=F.deleted;this.logger.info(`Emitting ${t}`),this.logger.debug({type:"event",event:t,record:e})}),this.core.heartbeat.on(ee.HEARTBEAT_EVENTS.pulse,()=>{this.cleanup()})}cleanup(){try{this.isInitialized();let e=!1;this.records.forEach(t=>{u.toMiliseconds(t.expiry||0)-Date.now()<=0&&(this.logger.info(`Deleting expired history log: ${t.id}`),this.records.delete(t.id),this.events.emit(F.deleted,t,!1),e=!0)}),e&&this.persist()}catch(e){this.logger.warn(e)}}isInitialized(){if(!this.initialized){const{message:e}=c.getInternalError("NOT_INITIALIZED",this.name);throw new Error(e)}}}var fr=Object.defineProperty,wr=(r,e,t)=>e in r?fr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,A=(r,e,t)=>wr(r,typeof e!="symbol"?e+"":e,t);class Yt extends B.IExpirer{constructor(e,t){super(e,t),this.core=e,this.logger=t,A(this,"expirations",new Map),A(this,"events",new Z.EventEmitter),A(this,"name",rt),A(this,"version",nt),A(this,"cached",[]),A(this,"initialized",!1),A(this,"storagePrefix",K),A(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(s=>this.expirations.set(s.target,s)),this.cached=[],this.registerEventListeners(),this.initialized=!0)}),A(this,"has",s=>{try{const i=this.formatTarget(s);return typeof this.getExpiration(i)<"u"}catch{return!1}}),A(this,"set",(s,i)=>{this.isInitialized();const n=this.formatTarget(s),o={target:n,expiry:i};this.expirations.set(n,o),this.checkExpiry(n,o),this.events.emit(k.created,{target:n,expiration:o})}),A(this,"get",s=>{this.isInitialized();const i=this.formatTarget(s);return this.getExpiration(i)}),A(this,"del",s=>{if(this.isInitialized(),this.has(s)){const i=this.formatTarget(s),n=this.getExpiration(i);this.expirations.delete(i),this.events.emit(k.deleted,{target:i,expiration:n})}}),A(this,"on",(s,i)=>{this.events.on(s,i)}),A(this,"once",(s,i)=>{this.events.once(s,i)}),A(this,"off",(s,i)=>{this.events.off(s,i)}),A(this,"removeListener",(s,i)=>{this.events.removeListener(s,i)}),this.logger=b.generateChildLogger(t,this.name)}get context(){return b.getLoggerContext(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.expirations.size}get keys(){return Array.from(this.expirations.keys())}get values(){return Array.from(this.expirations.values())}formatTarget(e){if(typeof e=="string")return c.formatTopicTarget(e);if(typeof e=="number")return c.formatIdTarget(e);const{message:t}=c.getInternalError("UNKNOWN_TYPE",`Target type: ${typeof e}`);throw new Error(t)}async setExpirations(e){await this.core.storage.setItem(this.storageKey,e)}async getExpirations(){return await this.core.storage.getItem(this.storageKey)}async persist(){await this.setExpirations(this.values),this.events.emit(k.sync)}async restore(){try{const e=await this.getExpirations();if(typeof e>"u"||!e.length)return;if(this.expirations.size){const{message:t}=c.getInternalError("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(t),new Error(t)}this.cached=e,this.logger.debug(`Successfully Restored expirations for ${this.name}`),this.logger.trace({type:"method",method:"restore",expirations:this.values})}catch(e){this.logger.debug(`Failed to Restore expirations for ${this.name}`),this.logger.error(e)}}getExpiration(e){const t=this.expirations.get(e);if(!t){const{message:s}=c.getInternalError("NO_MATCHING_KEY",`${this.name}: ${e}`);throw this.logger.warn(s),new Error(s)}return t}checkExpiry(e,t){const{expiry:s}=t;u.toMiliseconds(s)-Date.now()<=0&&this.expire(e,t)}expire(e,t){this.expirations.delete(e),this.events.emit(k.expired,{target:e,expiration:t})}checkExpirations(){this.core.relayer.connected&&this.expirations.forEach((e,t)=>this.checkExpiry(t,e))}registerEventListeners(){this.core.heartbeat.on(ee.HEARTBEAT_EVENTS.pulse,()=>this.checkExpirations()),this.events.on(k.created,e=>{const t=k.created;this.logger.info(`Emitting ${t}`),this.logger.debug({type:"event",event:t,data:e}),this.persist()}),this.events.on(k.expired,e=>{const t=k.expired;this.logger.info(`Emitting ${t}`),this.logger.debug({type:"event",event:t,data:e}),this.persist()}),this.events.on(k.deleted,e=>{const t=k.deleted;this.logger.info(`Emitting ${t}`),this.logger.debug({type:"event",event:t,data:e}),this.persist()})}isInitialized(){if(!this.initialized){const{message:e}=c.getInternalError("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Tr=Object.defineProperty,Ir=(r,e,t)=>e in r?Tr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,w=(r,e,t)=>Ir(r,typeof e!="symbol"?e+"":e,t);class Gt extends B.IVerify{constructor(e,t,s){super(e,t,s),this.core=e,this.logger=t,this.store=s,w(this,"name",ot),w(this,"abortController"),w(this,"isDevEnv"),w(this,"verifyUrlV3",ct),w(this,"storagePrefix",K),w(this,"version",me),w(this,"publicKey"),w(this,"fetchPromise"),w(this,"init",async()=>{var i;this.isDevEnv||(this.publicKey=await this.store.getItem(this.storeKey),this.publicKey&&u.toMiliseconds((i=this.publicKey)==null?void 0:i.expiresAt)<Date.now()&&(this.logger.debug("verify v2 public key expired"),await this.removePublicKey()))}),w(this,"register",async i=>{if(!c.isBrowser()||this.isDevEnv)return;const n=window.location.origin,{id:o,decryptedId:a}=i,h=`${this.verifyUrlV3}/attestation?projectId=${this.core.projectId}&origin=${n}&id=${o}&decryptedId=${a}`;try{const l=ni.getDocument(),g=this.startAbortTimer(u.ONE_SECOND*5),E=await new Promise((_,x)=>{const d=()=>{window.removeEventListener("message",N),l.body.removeChild(D),x("attestation aborted")};this.abortController.signal.addEventListener("abort",d);const D=l.createElement("iframe");D.src=h,D.style.display="none",D.addEventListener("error",d,{signal:this.abortController.signal});const N=I=>{if(I.data&&typeof I.data=="string")try{const v=JSON.parse(I.data);if(v.type==="verify_attestation"){if(De.decodeJWT(v.attestation).payload.id!==o)return;clearInterval(g),l.body.removeChild(D),this.abortController.signal.removeEventListener("abort",d),window.removeEventListener("message",N),_(v.attestation===null?"":v.attestation)}}catch(v){this.logger.warn(v)}};l.body.appendChild(D),window.addEventListener("message",N,{signal:this.abortController.signal})});return this.logger.debug("jwt attestation",E),E}catch(l){this.logger.warn(l)}return""}),w(this,"resolve",async i=>{if(this.isDevEnv)return"";const{attestationId:n,hash:o,encryptedId:a}=i;if(n===""){this.logger.debug("resolve: attestationId is empty, skipping");return}if(n){if(De.decodeJWT(n).payload.id!==a)return;const l=await this.isValidJwtAttestation(n);if(l){if(!l.isVerified){this.logger.warn("resolve: jwt attestation: origin url not verified");return}return l}}if(!o)return;const h=this.getVerifyUrl(i?.verifyUrl);return this.fetchAttestation(o,h)}),w(this,"fetchAttestation",async(i,n)=>{this.logger.debug(`resolving attestation: ${i} from url: ${n}`);const o=this.startAbortTimer(u.ONE_SECOND*5),a=await fetch(`${n}/attestation/${i}?v2Supported=true`,{signal:this.abortController.signal});return clearTimeout(o),a.status===200?await a.json():void 0}),w(this,"getVerifyUrl",i=>{let n=i||ne;return ht.includes(n)||(this.logger.info(`verify url: ${n}, not included in trusted list, assigning default: ${ne}`),n=ne),n}),w(this,"fetchPublicKey",async()=>{try{this.logger.debug(`fetching public key from: ${this.verifyUrlV3}`);const i=this.startAbortTimer(u.FIVE_SECONDS),n=await fetch(`${this.verifyUrlV3}/public-key`,{signal:this.abortController.signal});return clearTimeout(i),await n.json()}catch(i){this.logger.warn(i)}}),w(this,"persistPublicKey",async i=>{this.logger.debug("persisting public key to local storage",i),await this.store.setItem(this.storeKey,i),this.publicKey=i}),w(this,"removePublicKey",async()=>{this.logger.debug("removing verify v2 public key from storage"),await this.store.removeItem(this.storeKey),this.publicKey=void 0}),w(this,"isValidJwtAttestation",async i=>{const n=await this.getPublicKey();try{if(n)return this.validateAttestation(i,n)}catch(a){this.logger.error(a),this.logger.warn("error validating attestation")}const o=await this.fetchAndPersistPublicKey();try{if(o)return this.validateAttestation(i,o)}catch(a){this.logger.error(a),this.logger.warn("error validating attestation")}}),w(this,"getPublicKey",async()=>this.publicKey?this.publicKey:await this.fetchAndPersistPublicKey()),w(this,"fetchAndPersistPublicKey",async()=>{if(this.fetchPromise)return await this.fetchPromise,this.publicKey;this.fetchPromise=new Promise(async n=>{const o=await this.fetchPublicKey();o&&(await this.persistPublicKey(o),n(o))});const i=await this.fetchPromise;return this.fetchPromise=void 0,i}),w(this,"validateAttestation",(i,n)=>{const o=c.verifyP256Jwt(i,n.publicKey),a={hasExpired:u.toMiliseconds(o.exp)<Date.now(),payload:o};if(a.hasExpired)throw this.logger.warn("resolve: jwt attestation expired"),new Error("JWT attestation expired");return{origin:a.payload.origin,isScam:a.payload.isScam,isVerified:a.payload.isVerified}}),this.logger=b.generateChildLogger(t,this.name),this.abortController=new AbortController,this.isDevEnv=c.isTestRun(),this.init()}get storeKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//verify:public:key"}get context(){return b.getLoggerContext(this.logger)}startAbortTimer(e){return this.abortController=new AbortController,setTimeout(()=>this.abortController.abort(),u.toMiliseconds(e))}}var Rr=Object.defineProperty,Cr=(r,e,t)=>e in r?Rr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,Ht=(r,e,t)=>Cr(r,typeof e!="symbol"?e+"":e,t);class Jt extends B.IEchoClient{constructor(e,t){super(e,t),this.projectId=e,this.logger=t,Ht(this,"context",lt),Ht(this,"registerDeviceToken",async s=>{const{clientId:i,token:n,notificationType:o,enableEncrypted:a=!1}=s,h=`${ut}/${this.projectId}/clients`;await fetch(h,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({client_id:i,type:o,token:n,always_raw:a})})}),this.logger=b.generateChildLogger(t,this.context)}}var Sr=Object.defineProperty,Xt=Object.getOwnPropertySymbols,Pr=Object.prototype.hasOwnProperty,Or=Object.prototype.propertyIsEnumerable,xe=(r,e,t)=>e in r?Sr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,ce=(r,e)=>{for(var t in e||(e={}))Pr.call(e,t)&&xe(r,t,e[t]);if(Xt)for(var t of Xt(e))Or.call(e,t)&&xe(r,t,e[t]);return r},T=(r,e,t)=>xe(r,typeof e!="symbol"?e+"":e,t);class Wt extends B.IEventClient{constructor(e,t,s=!0){super(e,t,s),this.core=e,this.logger=t,T(this,"context",gt),T(this,"storagePrefix",K),T(this,"storageVersion",dt),T(this,"events",new Map),T(this,"shouldPersist",!1),T(this,"init",async()=>{if(!c.isTestRun())try{const i={eventId:c.uuidv4(),timestamp:Date.now(),domain:this.getAppDomain(),props:{event:"INIT",type:"",properties:{client_id:await this.core.crypto.getClientId(),user_agent:c.formatUA(this.core.relayer.protocol,this.core.relayer.version,de)}}};await this.sendEvent([i])}catch(i){this.logger.warn(i)}}),T(this,"createEvent",i=>{const{event:n="ERROR",type:o="",properties:{topic:a,trace:h}}=i,l=c.uuidv4(),g=this.core.projectId||"",E=Date.now(),_=ce({eventId:l,timestamp:E,props:{event:n,type:o,properties:{topic:a,trace:h}},bundleId:g,domain:this.getAppDomain()},this.setMethods(l));return this.telemetryEnabled&&(this.events.set(l,_),this.shouldPersist=!0),_}),T(this,"getEvent",i=>{const{eventId:n,topic:o}=i;if(n)return this.events.get(n);const a=Array.from(this.events.values()).find(h=>h.props.properties.topic===o);if(a)return ce(ce({},a),this.setMethods(a.eventId))}),T(this,"deleteEvent",i=>{const{eventId:n}=i;this.events.delete(n),this.shouldPersist=!0}),T(this,"setEventListeners",()=>{this.core.heartbeat.on(ee.HEARTBEAT_EVENTS.pulse,async()=>{this.shouldPersist&&await this.persist(),this.events.forEach(i=>{u.fromMiliseconds(Date.now())-u.fromMiliseconds(i.timestamp)>pt&&(this.events.delete(i.eventId),this.shouldPersist=!0)})})}),T(this,"setMethods",i=>({addTrace:n=>this.addTrace(i,n),setError:n=>this.setError(i,n)})),T(this,"addTrace",(i,n)=>{const o=this.events.get(i);o&&(o.props.properties.trace.push(n),this.events.set(i,o),this.shouldPersist=!0)}),T(this,"setError",(i,n)=>{const o=this.events.get(i);o&&(o.props.type=n,o.timestamp=Date.now(),this.events.set(i,o),this.shouldPersist=!0)}),T(this,"persist",async()=>{await this.core.storage.setItem(this.storageKey,Array.from(this.events.values())),this.shouldPersist=!1}),T(this,"restore",async()=>{try{const i=await this.core.storage.getItem(this.storageKey)||[];if(!i.length)return;i.forEach(n=>{this.events.set(n.eventId,ce(ce({},n),this.setMethods(n.eventId)))})}catch(i){this.logger.warn(i)}}),T(this,"submit",async()=>{if(!this.telemetryEnabled||this.events.size===0)return;const i=[];for(const[n,o]of this.events)o.props.type&&i.push(o);if(i.length!==0)try{if((await this.sendEvent(i)).ok)for(const n of i)this.events.delete(n.eventId),this.shouldPersist=!0}catch(n){this.logger.warn(n)}}),T(this,"sendEvent",async i=>{const n=this.getAppDomain()?"":"&sp=desktop";return await fetch(`${yt}?projectId=${this.core.projectId}&st=events_sdk&sv=js-${de}${n}`,{method:"POST",body:JSON.stringify(i)})}),T(this,"getAppDomain",()=>c.getAppMetadata().url),this.logger=b.generateChildLogger(t,this.context),this.telemetryEnabled=s,s?this.restore().then(async()=>{await this.submit(),this.setEventListeners()}):this.persist()}get storageKey(){return this.storagePrefix+this.storageVersion+this.core.customStoragePrefix+"//"+this.context}}var Ar=Object.defineProperty,Zt=Object.getOwnPropertySymbols,Nr=Object.prototype.hasOwnProperty,xr=Object.prototype.propertyIsEnumerable,Le=(r,e,t)=>e in r?Ar(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,Qt=(r,e)=>{for(var t in e||(e={}))Nr.call(e,t)&&Le(r,t,e[t]);if(Zt)for(var t of Zt(e))xr.call(e,t)&&Le(r,t,e[t]);return r},f=(r,e,t)=>Le(r,typeof e!="symbol"?e+"":e,t);class ye extends B.ICore{constructor(e){var t;super(e),f(this,"protocol",be),f(this,"version",me),f(this,"name",re),f(this,"relayUrl"),f(this,"projectId"),f(this,"customStoragePrefix"),f(this,"events",new Z.EventEmitter),f(this,"logger"),f(this,"heartbeat"),f(this,"relayer"),f(this,"crypto"),f(this,"storage"),f(this,"history"),f(this,"expirer"),f(this,"pairing"),f(this,"verify"),f(this,"echoClient"),f(this,"linkModeSupportedApps"),f(this,"eventClient"),f(this,"initialized",!1),f(this,"logChunkController"),f(this,"on",(o,a)=>this.events.on(o,a)),f(this,"once",(o,a)=>this.events.once(o,a)),f(this,"off",(o,a)=>this.events.off(o,a)),f(this,"removeListener",(o,a)=>this.events.removeListener(o,a)),f(this,"dispatchEnvelope",({topic:o,message:a,sessionExists:h})=>{if(!o||!a)return;const l={topic:o,message:a,publishedAt:Date.now(),transportType:Q.link_mode};this.relayer.onLinkMessageEvent(l,{sessionExists:h})}),this.projectId=e?.projectId,this.relayUrl=e?.relayUrl||fe,this.customStoragePrefix=e!=null&&e.customStoragePrefix?`:${e.customStoragePrefix}`:"";const s=b.getDefaultLoggerOptions({level:typeof e?.logger=="string"&&e.logger?e.logger:$e.logger,name:re}),{logger:i,chunkLoggerController:n}=b.generatePlatformLogger({opts:s,maxSizeInBytes:e?.maxLogBlobSizeInBytes,loggerOverride:e?.logger});this.logChunkController=n,(t=this.logChunkController)!=null&&t.downloadLogsBlobInBrowser&&(window.downloadLogsBlobInBrowser=async()=>{var o,a;(o=this.logChunkController)!=null&&o.downloadLogsBlobInBrowser&&((a=this.logChunkController)==null||a.downloadLogsBlobInBrowser({clientId:await this.crypto.getClientId()}))}),this.logger=b.generateChildLogger(i,this.name),this.heartbeat=new ee.HeartBeat,this.crypto=new xt(this,this.logger,e?.keychain),this.history=new qt(this,this.logger),this.expirer=new Yt(this,this.logger),this.storage=e!=null&&e.storage?e.storage:new ci.default(Qt(Qt({},Ue),e?.storageOptions)),this.relayer=new kt({core:this,logger:this.logger,relayUrl:this.relayUrl,projectId:this.projectId}),this.pairing=new jt(this,this.logger),this.verify=new Gt(this,this.logger,this.storage),this.echoClient=new Jt(this.projectId||"",this.logger),this.linkModeSupportedApps=[],this.eventClient=new Wt(this,this.logger,e?.telemetryEnabled)}static async init(e){const t=new ye(e);await t.initialize();const s=await t.crypto.getClientId();return await t.storage.setItem(Xe,s),t}get context(){return b.getLoggerContext(this.logger)}async start(){this.initialized||await this.initialize()}async getLogsBlob(){var e;return(e=this.logChunkController)==null?void 0:e.logsToBlob({clientId:await this.crypto.getClientId()})}async addLinkModeSupportedApp(e){this.linkModeSupportedApps.includes(e)||(this.linkModeSupportedApps.push(e),await this.storage.setItem(we,this.linkModeSupportedApps))}async initialize(){this.logger.trace("Initialized");try{await this.crypto.init(),await this.history.init(),await this.expirer.init(),await this.relayer.init(),await this.heartbeat.init(),await this.pairing.init(),this.linkModeSupportedApps=await this.storage.getItem(we)||[],this.initialized=!0,this.logger.info("Core Initialization Success")}catch(e){throw this.logger.warn(`Core Initialization Failure at epoch ${Date.now()}`,e),this.logger.error(e.message),e}}}const Lr=ye;exports.CORE_CONTEXT=re,exports.CORE_DEFAULT=$e,exports.CORE_PROTOCOL=be,exports.CORE_STORAGE_OPTIONS=Ue,exports.CORE_STORAGE_PREFIX=K,exports.CORE_VERSION=me,exports.CRYPTO_CLIENT_SEED=_e,exports.CRYPTO_CONTEXT=ze,exports.CRYPTO_JWT_TTL=Me,exports.Core=Lr,exports.Crypto=xt,exports.ECHO_CONTEXT=lt,exports.ECHO_URL=ut,exports.EVENTS_CLIENT_API_URL=yt,exports.EVENTS_STORAGE_CLEANUP_INTERVAL=pt,exports.EVENTS_STORAGE_CONTEXT=gt,exports.EVENTS_STORAGE_VERSION=dt,exports.EVENT_CLIENT_AUTHENTICATE_ERRORS=vi,exports.EVENT_CLIENT_AUTHENTICATE_TRACES=_i,exports.EVENT_CLIENT_CONTEXT=Di,exports.EVENT_CLIENT_PAIRING_ERRORS=X,exports.EVENT_CLIENT_PAIRING_TRACES=Y,exports.EVENT_CLIENT_SESSION_ERRORS=mi,exports.EVENT_CLIENT_SESSION_TRACES=bi,exports.EXPIRER_CONTEXT=rt,exports.EXPIRER_DEFAULT_TTL=yi,exports.EXPIRER_EVENTS=k,exports.EXPIRER_STORAGE_VERSION=nt,exports.EchoClient=Jt,exports.EventClient=Wt,exports.Expirer=Yt,exports.HISTORY_CONTEXT=it,exports.HISTORY_EVENTS=F,exports.HISTORY_STORAGE_VERSION=st,exports.JsonRpcHistory=qt,exports.KEYCHAIN_CONTEXT=Fe,exports.KEYCHAIN_STORAGE_VERSION=ke,exports.KeyChain=Nt,exports.MESSAGES_CONTEXT=Ve,exports.MESSAGES_STORAGE_VERSION=Be,exports.MessageTracker=Lt,exports.PAIRING_CONTEXT=et,exports.PAIRING_DEFAULT_TTL=pi,exports.PAIRING_EVENTS=ie,exports.PAIRING_RPC_OPTS=te,exports.PAIRING_STORAGE_VERSION=tt,exports.PENDING_SUB_RESOLUTION_TIMEOUT=Qe,exports.PUBLISHER_CONTEXT=Ke,exports.PUBLISHER_DEFAULT_TTL=ve,exports.Pairing=jt,exports.RELAYER_CONTEXT=Ye,exports.RELAYER_DEFAULT_LOGGER=qe,exports.RELAYER_DEFAULT_PROTOCOL=je,exports.RELAYER_DEFAULT_RELAY_URL=fe,exports.RELAYER_EVENTS=C,exports.RELAYER_PROVIDER_EVENTS=z,exports.RELAYER_RECONNECT_TIMEOUT=He,exports.RELAYER_SDK_VERSION=de,exports.RELAYER_STORAGE_OPTIONS=ui,exports.RELAYER_SUBSCRIBER_SUFFIX=Ge,exports.RELAYER_TRANSPORT_CUTOFF=di,exports.Relayer=kt,exports.STORE_STORAGE_VERSION=Je,exports.SUBSCRIBER_CONTEXT=We,exports.SUBSCRIBER_DEFAULT_TTL=gi,exports.SUBSCRIBER_EVENTS=$,exports.SUBSCRIBER_STORAGE_VERSION=Ze,exports.Store=Kt,exports.Subscriber=zt,exports.TRANSPORT_TYPES=Q,exports.TRUSTED_VERIFY_URLS=ht,exports.VERIFY_CONTEXT=ot,exports.VERIFY_SERVER=ne,exports.VERIFY_SERVER_V3=ct,exports.Verify=Gt,exports.WALLETCONNECT_CLIENT_ID=Xe,exports.WALLETCONNECT_LINK_MODE_APPS=we,exports.default=ye;
//# sourceMappingURL=index.cjs.js.map
