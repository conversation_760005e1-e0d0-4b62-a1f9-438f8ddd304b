import {Request} from '../lib/request';
import {Response} from '../lib/response';
import {AWSError} from '../lib/error';
import {Service} from '../lib/service';
import {ServiceConfigurationOptions} from '../lib/service';
import {ConfigBase as Config} from '../lib/config-base';
interface Blob {}
declare class Inspector extends Service {
  /**
   * Constructs a service object. This object has one method for each API operation.
   */
  constructor(options?: Inspector.Types.ClientConfiguration)
  config: Config & Inspector.Types.ClientConfiguration;
  /**
   * Assigns attributes (key and value pairs) to the findings that are specified by the ARNs of the findings.
   */
  addAttributesToFindings(params: Inspector.Types.AddAttributesToFindingsRequest, callback?: (err: AWSError, data: Inspector.Types.AddAttributesToFindingsResponse) => void): Request<Inspector.Types.AddAttributesToFindingsResponse, AWSError>;
  /**
   * Assigns attributes (key and value pairs) to the findings that are specified by the ARNs of the findings.
   */
  addAttributesToFindings(callback?: (err: AWSError, data: Inspector.Types.AddAttributesToFindingsResponse) => void): Request<Inspector.Types.AddAttributesToFindingsResponse, AWSError>;
  /**
   * Creates a new assessment target using the ARN of the resource group that is generated by CreateResourceGroup. If resourceGroupArn is not specified, all EC2 instances in the current AWS account and region are included in the assessment target. If the service-linked role isn’t already registered, this action also creates and registers a service-linked role to grant Amazon Inspector access to AWS Services needed to perform security assessments. You can create up to 50 assessment targets per AWS account. You can run up to 500 concurrent agents per AWS account. For more information, see  Amazon Inspector Assessment Targets.
   */
  createAssessmentTarget(params: Inspector.Types.CreateAssessmentTargetRequest, callback?: (err: AWSError, data: Inspector.Types.CreateAssessmentTargetResponse) => void): Request<Inspector.Types.CreateAssessmentTargetResponse, AWSError>;
  /**
   * Creates a new assessment target using the ARN of the resource group that is generated by CreateResourceGroup. If resourceGroupArn is not specified, all EC2 instances in the current AWS account and region are included in the assessment target. If the service-linked role isn’t already registered, this action also creates and registers a service-linked role to grant Amazon Inspector access to AWS Services needed to perform security assessments. You can create up to 50 assessment targets per AWS account. You can run up to 500 concurrent agents per AWS account. For more information, see  Amazon Inspector Assessment Targets.
   */
  createAssessmentTarget(callback?: (err: AWSError, data: Inspector.Types.CreateAssessmentTargetResponse) => void): Request<Inspector.Types.CreateAssessmentTargetResponse, AWSError>;
  /**
   * Creates an assessment template for the assessment target that is specified by the ARN of the assessment target. If the service-linked role isn’t already registered, this action also creates and registers a service-linked role to grant Amazon Inspector access to AWS Services needed to perform security assessments.
   */
  createAssessmentTemplate(params: Inspector.Types.CreateAssessmentTemplateRequest, callback?: (err: AWSError, data: Inspector.Types.CreateAssessmentTemplateResponse) => void): Request<Inspector.Types.CreateAssessmentTemplateResponse, AWSError>;
  /**
   * Creates an assessment template for the assessment target that is specified by the ARN of the assessment target. If the service-linked role isn’t already registered, this action also creates and registers a service-linked role to grant Amazon Inspector access to AWS Services needed to perform security assessments.
   */
  createAssessmentTemplate(callback?: (err: AWSError, data: Inspector.Types.CreateAssessmentTemplateResponse) => void): Request<Inspector.Types.CreateAssessmentTemplateResponse, AWSError>;
  /**
   * Starts the generation of an exclusions preview for the specified assessment template. The exclusions preview lists the potential exclusions (ExclusionPreview) that Inspector can detect before it runs the assessment. 
   */
  createExclusionsPreview(params: Inspector.Types.CreateExclusionsPreviewRequest, callback?: (err: AWSError, data: Inspector.Types.CreateExclusionsPreviewResponse) => void): Request<Inspector.Types.CreateExclusionsPreviewResponse, AWSError>;
  /**
   * Starts the generation of an exclusions preview for the specified assessment template. The exclusions preview lists the potential exclusions (ExclusionPreview) that Inspector can detect before it runs the assessment. 
   */
  createExclusionsPreview(callback?: (err: AWSError, data: Inspector.Types.CreateExclusionsPreviewResponse) => void): Request<Inspector.Types.CreateExclusionsPreviewResponse, AWSError>;
  /**
   * Creates a resource group using the specified set of tags (key and value pairs) that are used to select the EC2 instances to be included in an Amazon Inspector assessment target. The created resource group is then used to create an Amazon Inspector assessment target. For more information, see CreateAssessmentTarget.
   */
  createResourceGroup(params: Inspector.Types.CreateResourceGroupRequest, callback?: (err: AWSError, data: Inspector.Types.CreateResourceGroupResponse) => void): Request<Inspector.Types.CreateResourceGroupResponse, AWSError>;
  /**
   * Creates a resource group using the specified set of tags (key and value pairs) that are used to select the EC2 instances to be included in an Amazon Inspector assessment target. The created resource group is then used to create an Amazon Inspector assessment target. For more information, see CreateAssessmentTarget.
   */
  createResourceGroup(callback?: (err: AWSError, data: Inspector.Types.CreateResourceGroupResponse) => void): Request<Inspector.Types.CreateResourceGroupResponse, AWSError>;
  /**
   * Deletes the assessment run that is specified by the ARN of the assessment run.
   */
  deleteAssessmentRun(params: Inspector.Types.DeleteAssessmentRunRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the assessment run that is specified by the ARN of the assessment run.
   */
  deleteAssessmentRun(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the assessment target that is specified by the ARN of the assessment target.
   */
  deleteAssessmentTarget(params: Inspector.Types.DeleteAssessmentTargetRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the assessment target that is specified by the ARN of the assessment target.
   */
  deleteAssessmentTarget(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the assessment template that is specified by the ARN of the assessment template.
   */
  deleteAssessmentTemplate(params: Inspector.Types.DeleteAssessmentTemplateRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the assessment template that is specified by the ARN of the assessment template.
   */
  deleteAssessmentTemplate(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Describes the assessment runs that are specified by the ARNs of the assessment runs.
   */
  describeAssessmentRuns(params: Inspector.Types.DescribeAssessmentRunsRequest, callback?: (err: AWSError, data: Inspector.Types.DescribeAssessmentRunsResponse) => void): Request<Inspector.Types.DescribeAssessmentRunsResponse, AWSError>;
  /**
   * Describes the assessment runs that are specified by the ARNs of the assessment runs.
   */
  describeAssessmentRuns(callback?: (err: AWSError, data: Inspector.Types.DescribeAssessmentRunsResponse) => void): Request<Inspector.Types.DescribeAssessmentRunsResponse, AWSError>;
  /**
   * Describes the assessment targets that are specified by the ARNs of the assessment targets.
   */
  describeAssessmentTargets(params: Inspector.Types.DescribeAssessmentTargetsRequest, callback?: (err: AWSError, data: Inspector.Types.DescribeAssessmentTargetsResponse) => void): Request<Inspector.Types.DescribeAssessmentTargetsResponse, AWSError>;
  /**
   * Describes the assessment targets that are specified by the ARNs of the assessment targets.
   */
  describeAssessmentTargets(callback?: (err: AWSError, data: Inspector.Types.DescribeAssessmentTargetsResponse) => void): Request<Inspector.Types.DescribeAssessmentTargetsResponse, AWSError>;
  /**
   * Describes the assessment templates that are specified by the ARNs of the assessment templates.
   */
  describeAssessmentTemplates(params: Inspector.Types.DescribeAssessmentTemplatesRequest, callback?: (err: AWSError, data: Inspector.Types.DescribeAssessmentTemplatesResponse) => void): Request<Inspector.Types.DescribeAssessmentTemplatesResponse, AWSError>;
  /**
   * Describes the assessment templates that are specified by the ARNs of the assessment templates.
   */
  describeAssessmentTemplates(callback?: (err: AWSError, data: Inspector.Types.DescribeAssessmentTemplatesResponse) => void): Request<Inspector.Types.DescribeAssessmentTemplatesResponse, AWSError>;
  /**
   * Describes the IAM role that enables Amazon Inspector to access your AWS account.
   */
  describeCrossAccountAccessRole(callback?: (err: AWSError, data: Inspector.Types.DescribeCrossAccountAccessRoleResponse) => void): Request<Inspector.Types.DescribeCrossAccountAccessRoleResponse, AWSError>;
  /**
   * Describes the exclusions that are specified by the exclusions' ARNs.
   */
  describeExclusions(params: Inspector.Types.DescribeExclusionsRequest, callback?: (err: AWSError, data: Inspector.Types.DescribeExclusionsResponse) => void): Request<Inspector.Types.DescribeExclusionsResponse, AWSError>;
  /**
   * Describes the exclusions that are specified by the exclusions' ARNs.
   */
  describeExclusions(callback?: (err: AWSError, data: Inspector.Types.DescribeExclusionsResponse) => void): Request<Inspector.Types.DescribeExclusionsResponse, AWSError>;
  /**
   * Describes the findings that are specified by the ARNs of the findings.
   */
  describeFindings(params: Inspector.Types.DescribeFindingsRequest, callback?: (err: AWSError, data: Inspector.Types.DescribeFindingsResponse) => void): Request<Inspector.Types.DescribeFindingsResponse, AWSError>;
  /**
   * Describes the findings that are specified by the ARNs of the findings.
   */
  describeFindings(callback?: (err: AWSError, data: Inspector.Types.DescribeFindingsResponse) => void): Request<Inspector.Types.DescribeFindingsResponse, AWSError>;
  /**
   * Describes the resource groups that are specified by the ARNs of the resource groups.
   */
  describeResourceGroups(params: Inspector.Types.DescribeResourceGroupsRequest, callback?: (err: AWSError, data: Inspector.Types.DescribeResourceGroupsResponse) => void): Request<Inspector.Types.DescribeResourceGroupsResponse, AWSError>;
  /**
   * Describes the resource groups that are specified by the ARNs of the resource groups.
   */
  describeResourceGroups(callback?: (err: AWSError, data: Inspector.Types.DescribeResourceGroupsResponse) => void): Request<Inspector.Types.DescribeResourceGroupsResponse, AWSError>;
  /**
   * Describes the rules packages that are specified by the ARNs of the rules packages.
   */
  describeRulesPackages(params: Inspector.Types.DescribeRulesPackagesRequest, callback?: (err: AWSError, data: Inspector.Types.DescribeRulesPackagesResponse) => void): Request<Inspector.Types.DescribeRulesPackagesResponse, AWSError>;
  /**
   * Describes the rules packages that are specified by the ARNs of the rules packages.
   */
  describeRulesPackages(callback?: (err: AWSError, data: Inspector.Types.DescribeRulesPackagesResponse) => void): Request<Inspector.Types.DescribeRulesPackagesResponse, AWSError>;
  /**
   * Produces an assessment report that includes detailed and comprehensive results of a specified assessment run. 
   */
  getAssessmentReport(params: Inspector.Types.GetAssessmentReportRequest, callback?: (err: AWSError, data: Inspector.Types.GetAssessmentReportResponse) => void): Request<Inspector.Types.GetAssessmentReportResponse, AWSError>;
  /**
   * Produces an assessment report that includes detailed and comprehensive results of a specified assessment run. 
   */
  getAssessmentReport(callback?: (err: AWSError, data: Inspector.Types.GetAssessmentReportResponse) => void): Request<Inspector.Types.GetAssessmentReportResponse, AWSError>;
  /**
   * Retrieves the exclusions preview (a list of ExclusionPreview objects) specified by the preview token. You can obtain the preview token by running the CreateExclusionsPreview API.
   */
  getExclusionsPreview(params: Inspector.Types.GetExclusionsPreviewRequest, callback?: (err: AWSError, data: Inspector.Types.GetExclusionsPreviewResponse) => void): Request<Inspector.Types.GetExclusionsPreviewResponse, AWSError>;
  /**
   * Retrieves the exclusions preview (a list of ExclusionPreview objects) specified by the preview token. You can obtain the preview token by running the CreateExclusionsPreview API.
   */
  getExclusionsPreview(callback?: (err: AWSError, data: Inspector.Types.GetExclusionsPreviewResponse) => void): Request<Inspector.Types.GetExclusionsPreviewResponse, AWSError>;
  /**
   * Information about the data that is collected for the specified assessment run.
   */
  getTelemetryMetadata(params: Inspector.Types.GetTelemetryMetadataRequest, callback?: (err: AWSError, data: Inspector.Types.GetTelemetryMetadataResponse) => void): Request<Inspector.Types.GetTelemetryMetadataResponse, AWSError>;
  /**
   * Information about the data that is collected for the specified assessment run.
   */
  getTelemetryMetadata(callback?: (err: AWSError, data: Inspector.Types.GetTelemetryMetadataResponse) => void): Request<Inspector.Types.GetTelemetryMetadataResponse, AWSError>;
  /**
   * Lists the agents of the assessment runs that are specified by the ARNs of the assessment runs.
   */
  listAssessmentRunAgents(params: Inspector.Types.ListAssessmentRunAgentsRequest, callback?: (err: AWSError, data: Inspector.Types.ListAssessmentRunAgentsResponse) => void): Request<Inspector.Types.ListAssessmentRunAgentsResponse, AWSError>;
  /**
   * Lists the agents of the assessment runs that are specified by the ARNs of the assessment runs.
   */
  listAssessmentRunAgents(callback?: (err: AWSError, data: Inspector.Types.ListAssessmentRunAgentsResponse) => void): Request<Inspector.Types.ListAssessmentRunAgentsResponse, AWSError>;
  /**
   * Lists the assessment runs that correspond to the assessment templates that are specified by the ARNs of the assessment templates.
   */
  listAssessmentRuns(params: Inspector.Types.ListAssessmentRunsRequest, callback?: (err: AWSError, data: Inspector.Types.ListAssessmentRunsResponse) => void): Request<Inspector.Types.ListAssessmentRunsResponse, AWSError>;
  /**
   * Lists the assessment runs that correspond to the assessment templates that are specified by the ARNs of the assessment templates.
   */
  listAssessmentRuns(callback?: (err: AWSError, data: Inspector.Types.ListAssessmentRunsResponse) => void): Request<Inspector.Types.ListAssessmentRunsResponse, AWSError>;
  /**
   * Lists the ARNs of the assessment targets within this AWS account. For more information about assessment targets, see Amazon Inspector Assessment Targets.
   */
  listAssessmentTargets(params: Inspector.Types.ListAssessmentTargetsRequest, callback?: (err: AWSError, data: Inspector.Types.ListAssessmentTargetsResponse) => void): Request<Inspector.Types.ListAssessmentTargetsResponse, AWSError>;
  /**
   * Lists the ARNs of the assessment targets within this AWS account. For more information about assessment targets, see Amazon Inspector Assessment Targets.
   */
  listAssessmentTargets(callback?: (err: AWSError, data: Inspector.Types.ListAssessmentTargetsResponse) => void): Request<Inspector.Types.ListAssessmentTargetsResponse, AWSError>;
  /**
   * Lists the assessment templates that correspond to the assessment targets that are specified by the ARNs of the assessment targets.
   */
  listAssessmentTemplates(params: Inspector.Types.ListAssessmentTemplatesRequest, callback?: (err: AWSError, data: Inspector.Types.ListAssessmentTemplatesResponse) => void): Request<Inspector.Types.ListAssessmentTemplatesResponse, AWSError>;
  /**
   * Lists the assessment templates that correspond to the assessment targets that are specified by the ARNs of the assessment targets.
   */
  listAssessmentTemplates(callback?: (err: AWSError, data: Inspector.Types.ListAssessmentTemplatesResponse) => void): Request<Inspector.Types.ListAssessmentTemplatesResponse, AWSError>;
  /**
   * Lists all the event subscriptions for the assessment template that is specified by the ARN of the assessment template. For more information, see SubscribeToEvent and UnsubscribeFromEvent.
   */
  listEventSubscriptions(params: Inspector.Types.ListEventSubscriptionsRequest, callback?: (err: AWSError, data: Inspector.Types.ListEventSubscriptionsResponse) => void): Request<Inspector.Types.ListEventSubscriptionsResponse, AWSError>;
  /**
   * Lists all the event subscriptions for the assessment template that is specified by the ARN of the assessment template. For more information, see SubscribeToEvent and UnsubscribeFromEvent.
   */
  listEventSubscriptions(callback?: (err: AWSError, data: Inspector.Types.ListEventSubscriptionsResponse) => void): Request<Inspector.Types.ListEventSubscriptionsResponse, AWSError>;
  /**
   * List exclusions that are generated by the assessment run.
   */
  listExclusions(params: Inspector.Types.ListExclusionsRequest, callback?: (err: AWSError, data: Inspector.Types.ListExclusionsResponse) => void): Request<Inspector.Types.ListExclusionsResponse, AWSError>;
  /**
   * List exclusions that are generated by the assessment run.
   */
  listExclusions(callback?: (err: AWSError, data: Inspector.Types.ListExclusionsResponse) => void): Request<Inspector.Types.ListExclusionsResponse, AWSError>;
  /**
   * Lists findings that are generated by the assessment runs that are specified by the ARNs of the assessment runs.
   */
  listFindings(params: Inspector.Types.ListFindingsRequest, callback?: (err: AWSError, data: Inspector.Types.ListFindingsResponse) => void): Request<Inspector.Types.ListFindingsResponse, AWSError>;
  /**
   * Lists findings that are generated by the assessment runs that are specified by the ARNs of the assessment runs.
   */
  listFindings(callback?: (err: AWSError, data: Inspector.Types.ListFindingsResponse) => void): Request<Inspector.Types.ListFindingsResponse, AWSError>;
  /**
   * Lists all available Amazon Inspector rules packages.
   */
  listRulesPackages(params: Inspector.Types.ListRulesPackagesRequest, callback?: (err: AWSError, data: Inspector.Types.ListRulesPackagesResponse) => void): Request<Inspector.Types.ListRulesPackagesResponse, AWSError>;
  /**
   * Lists all available Amazon Inspector rules packages.
   */
  listRulesPackages(callback?: (err: AWSError, data: Inspector.Types.ListRulesPackagesResponse) => void): Request<Inspector.Types.ListRulesPackagesResponse, AWSError>;
  /**
   * Lists all tags associated with an assessment template.
   */
  listTagsForResource(params: Inspector.Types.ListTagsForResourceRequest, callback?: (err: AWSError, data: Inspector.Types.ListTagsForResourceResponse) => void): Request<Inspector.Types.ListTagsForResourceResponse, AWSError>;
  /**
   * Lists all tags associated with an assessment template.
   */
  listTagsForResource(callback?: (err: AWSError, data: Inspector.Types.ListTagsForResourceResponse) => void): Request<Inspector.Types.ListTagsForResourceResponse, AWSError>;
  /**
   * Previews the agents installed on the EC2 instances that are part of the specified assessment target.
   */
  previewAgents(params: Inspector.Types.PreviewAgentsRequest, callback?: (err: AWSError, data: Inspector.Types.PreviewAgentsResponse) => void): Request<Inspector.Types.PreviewAgentsResponse, AWSError>;
  /**
   * Previews the agents installed on the EC2 instances that are part of the specified assessment target.
   */
  previewAgents(callback?: (err: AWSError, data: Inspector.Types.PreviewAgentsResponse) => void): Request<Inspector.Types.PreviewAgentsResponse, AWSError>;
  /**
   * Registers the IAM role that grants Amazon Inspector access to AWS Services needed to perform security assessments.
   */
  registerCrossAccountAccessRole(params: Inspector.Types.RegisterCrossAccountAccessRoleRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Registers the IAM role that grants Amazon Inspector access to AWS Services needed to perform security assessments.
   */
  registerCrossAccountAccessRole(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Removes entire attributes (key and value pairs) from the findings that are specified by the ARNs of the findings where an attribute with the specified key exists.
   */
  removeAttributesFromFindings(params: Inspector.Types.RemoveAttributesFromFindingsRequest, callback?: (err: AWSError, data: Inspector.Types.RemoveAttributesFromFindingsResponse) => void): Request<Inspector.Types.RemoveAttributesFromFindingsResponse, AWSError>;
  /**
   * Removes entire attributes (key and value pairs) from the findings that are specified by the ARNs of the findings where an attribute with the specified key exists.
   */
  removeAttributesFromFindings(callback?: (err: AWSError, data: Inspector.Types.RemoveAttributesFromFindingsResponse) => void): Request<Inspector.Types.RemoveAttributesFromFindingsResponse, AWSError>;
  /**
   * Sets tags (key and value pairs) to the assessment template that is specified by the ARN of the assessment template.
   */
  setTagsForResource(params: Inspector.Types.SetTagsForResourceRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Sets tags (key and value pairs) to the assessment template that is specified by the ARN of the assessment template.
   */
  setTagsForResource(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Starts the assessment run specified by the ARN of the assessment template. For this API to function properly, you must not exceed the limit of running up to 500 concurrent agents per AWS account.
   */
  startAssessmentRun(params: Inspector.Types.StartAssessmentRunRequest, callback?: (err: AWSError, data: Inspector.Types.StartAssessmentRunResponse) => void): Request<Inspector.Types.StartAssessmentRunResponse, AWSError>;
  /**
   * Starts the assessment run specified by the ARN of the assessment template. For this API to function properly, you must not exceed the limit of running up to 500 concurrent agents per AWS account.
   */
  startAssessmentRun(callback?: (err: AWSError, data: Inspector.Types.StartAssessmentRunResponse) => void): Request<Inspector.Types.StartAssessmentRunResponse, AWSError>;
  /**
   * Stops the assessment run that is specified by the ARN of the assessment run.
   */
  stopAssessmentRun(params: Inspector.Types.StopAssessmentRunRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Stops the assessment run that is specified by the ARN of the assessment run.
   */
  stopAssessmentRun(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Enables the process of sending Amazon Simple Notification Service (SNS) notifications about a specified event to a specified SNS topic.
   */
  subscribeToEvent(params: Inspector.Types.SubscribeToEventRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Enables the process of sending Amazon Simple Notification Service (SNS) notifications about a specified event to a specified SNS topic.
   */
  subscribeToEvent(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Disables the process of sending Amazon Simple Notification Service (SNS) notifications about a specified event to a specified SNS topic.
   */
  unsubscribeFromEvent(params: Inspector.Types.UnsubscribeFromEventRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Disables the process of sending Amazon Simple Notification Service (SNS) notifications about a specified event to a specified SNS topic.
   */
  unsubscribeFromEvent(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Updates the assessment target that is specified by the ARN of the assessment target. If resourceGroupArn is not specified, all EC2 instances in the current AWS account and region are included in the assessment target.
   */
  updateAssessmentTarget(params: Inspector.Types.UpdateAssessmentTargetRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Updates the assessment target that is specified by the ARN of the assessment target. If resourceGroupArn is not specified, all EC2 instances in the current AWS account and region are included in the assessment target.
   */
  updateAssessmentTarget(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
}
declare namespace Inspector {
  export interface AddAttributesToFindingsRequest {
    /**
     * The ARNs that specify the findings that you want to assign attributes to.
     */
    findingArns: AddRemoveAttributesFindingArnList;
    /**
     * The array of attributes that you want to assign to specified findings.
     */
    attributes: UserAttributeList;
  }
  export interface AddAttributesToFindingsResponse {
    /**
     * Attribute details that cannot be described. An error code is provided for each failed item.
     */
    failedItems: FailedItems;
  }
  export type AddRemoveAttributesFindingArnList = Arn[];
  export interface AgentFilter {
    /**
     * The current health state of the agent. Values can be set to HEALTHY or UNHEALTHY.
     */
    agentHealths: AgentHealthList;
    /**
     * The detailed health state of the agent. Values can be set to IDLE, RUNNING, SHUTDOWN, UNHEALTHY, THROTTLED, and UNKNOWN. 
     */
    agentHealthCodes: AgentHealthCodeList;
  }
  export type AgentHealth = "HEALTHY"|"UNHEALTHY"|"UNKNOWN"|string;
  export type AgentHealthCode = "IDLE"|"RUNNING"|"SHUTDOWN"|"UNHEALTHY"|"THROTTLED"|"UNKNOWN"|string;
  export type AgentHealthCodeList = AgentHealthCode[];
  export type AgentHealthList = AgentHealth[];
  export type AgentId = string;
  export type AgentIdList = AgentId[];
  export interface AgentPreview {
    /**
     * The hostname of the EC2 instance on which the Amazon Inspector Agent is installed.
     */
    hostname?: Hostname;
    /**
     * The ID of the EC2 instance where the agent is installed.
     */
    agentId: AgentId;
    /**
     * The Auto Scaling group for the EC2 instance where the agent is installed.
     */
    autoScalingGroup?: AutoScalingGroup;
    /**
     * The health status of the Amazon Inspector Agent.
     */
    agentHealth?: AgentHealth;
    /**
     * The version of the Amazon Inspector Agent.
     */
    agentVersion?: AgentVersion;
    /**
     * The operating system running on the EC2 instance on which the Amazon Inspector Agent is installed.
     */
    operatingSystem?: OperatingSystem;
    /**
     * The kernel version of the operating system running on the EC2 instance on which the Amazon Inspector Agent is installed.
     */
    kernelVersion?: KernelVersion;
    /**
     * The IP address of the EC2 instance on which the Amazon Inspector Agent is installed.
     */
    ipv4Address?: Ipv4Address;
  }
  export type AgentPreviewList = AgentPreview[];
  export type AgentVersion = string;
  export type AmiId = string;
  export type Arn = string;
  export type ArnCount = number;
  export type AssessmentRulesPackageArnList = Arn[];
  export interface AssessmentRun {
    /**
     * The ARN of the assessment run.
     */
    arn: Arn;
    /**
     * The auto-generated name for the assessment run.
     */
    name: AssessmentRunName;
    /**
     * The ARN of the assessment template that is associated with the assessment run.
     */
    assessmentTemplateArn: Arn;
    /**
     * The state of the assessment run.
     */
    state: AssessmentRunState;
    /**
     * The duration of the assessment run.
     */
    durationInSeconds: AssessmentRunDuration;
    /**
     * The rules packages selected for the assessment run.
     */
    rulesPackageArns: AssessmentRulesPackageArnList;
    /**
     * The user-defined attributes that are assigned to every generated finding.
     */
    userAttributesForFindings: UserAttributeList;
    /**
     * The time when StartAssessmentRun was called.
     */
    createdAt: Timestamp;
    /**
     * The time when StartAssessmentRun was called.
     */
    startedAt?: Timestamp;
    /**
     * The assessment run completion time that corresponds to the rules packages evaluation completion time or failure.
     */
    completedAt?: Timestamp;
    /**
     * The last time when the assessment run's state changed.
     */
    stateChangedAt: Timestamp;
    /**
     * A Boolean value (true or false) that specifies whether the process of collecting data from the agents is completed.
     */
    dataCollected: Bool;
    /**
     * A list of the assessment run state changes.
     */
    stateChanges: AssessmentRunStateChangeList;
    /**
     * A list of notifications for the event subscriptions. A notification about a particular generated finding is added to this list only once.
     */
    notifications: AssessmentRunNotificationList;
    /**
     * Provides a total count of generated findings per severity.
     */
    findingCounts: AssessmentRunFindingCounts;
  }
  export interface AssessmentRunAgent {
    /**
     * The AWS account of the EC2 instance where the agent is installed.
     */
    agentId: AgentId;
    /**
     * The ARN of the assessment run that is associated with the agent.
     */
    assessmentRunArn: Arn;
    /**
     * The current health state of the agent.
     */
    agentHealth: AgentHealth;
    /**
     * The detailed health state of the agent.
     */
    agentHealthCode: AgentHealthCode;
    /**
     * The description for the agent health code.
     */
    agentHealthDetails?: Message;
    /**
     * The Auto Scaling group of the EC2 instance that is specified by the agent ID.
     */
    autoScalingGroup?: AutoScalingGroup;
    /**
     * The Amazon Inspector application data metrics that are collected by the agent.
     */
    telemetryMetadata: TelemetryMetadataList;
  }
  export type AssessmentRunAgentList = AssessmentRunAgent[];
  export type AssessmentRunDuration = number;
  export interface AssessmentRunFilter {
    /**
     * For a record to match a filter, an explicit value or a string containing a wildcard that is specified for this data type property must match the value of the assessmentRunName property of the AssessmentRun data type.
     */
    namePattern?: NamePattern;
    /**
     * For a record to match a filter, one of the values specified for this data type property must be the exact match of the value of the assessmentRunState property of the AssessmentRun data type.
     */
    states?: AssessmentRunStateList;
    /**
     * For a record to match a filter, the value that is specified for this data type property must inclusively match any value between the specified minimum and maximum values of the durationInSeconds property of the AssessmentRun data type.
     */
    durationRange?: DurationRange;
    /**
     * For a record to match a filter, the value that is specified for this data type property must be contained in the list of values of the rulesPackages property of the AssessmentRun data type.
     */
    rulesPackageArns?: FilterRulesPackageArnList;
    /**
     * For a record to match a filter, the value that is specified for this data type property must inclusively match any value between the specified minimum and maximum values of the startTime property of the AssessmentRun data type.
     */
    startTimeRange?: TimestampRange;
    /**
     * For a record to match a filter, the value that is specified for this data type property must inclusively match any value between the specified minimum and maximum values of the completedAt property of the AssessmentRun data type.
     */
    completionTimeRange?: TimestampRange;
    /**
     * For a record to match a filter, the value that is specified for this data type property must match the stateChangedAt property of the AssessmentRun data type.
     */
    stateChangeTimeRange?: TimestampRange;
  }
  export type AssessmentRunFindingCounts = {[key: string]: FindingCount};
  export type AssessmentRunList = AssessmentRun[];
  export type AssessmentRunName = string;
  export interface AssessmentRunNotification {
    /**
     * The date of the notification.
     */
    date: Timestamp;
    /**
     * The event for which a notification is sent.
     */
    event: InspectorEvent;
    /**
     * The message included in the notification.
     */
    message?: Message;
    /**
     * The Boolean value that specifies whether the notification represents an error.
     */
    error: Bool;
    /**
     * The SNS topic to which the SNS notification is sent.
     */
    snsTopicArn?: Arn;
    /**
     * The status code of the SNS notification.
     */
    snsPublishStatusCode?: AssessmentRunNotificationSnsStatusCode;
  }
  export type AssessmentRunNotificationList = AssessmentRunNotification[];
  export type AssessmentRunNotificationSnsStatusCode = "SUCCESS"|"TOPIC_DOES_NOT_EXIST"|"ACCESS_DENIED"|"INTERNAL_ERROR"|string;
  export type AssessmentRunState = "CREATED"|"START_DATA_COLLECTION_PENDING"|"START_DATA_COLLECTION_IN_PROGRESS"|"COLLECTING_DATA"|"STOP_DATA_COLLECTION_PENDING"|"DATA_COLLECTED"|"START_EVALUATING_RULES_PENDING"|"EVALUATING_RULES"|"FAILED"|"ERROR"|"COMPLETED"|"COMPLETED_WITH_ERRORS"|"CANCELED"|string;
  export interface AssessmentRunStateChange {
    /**
     * The last time the assessment run state changed.
     */
    stateChangedAt: Timestamp;
    /**
     * The assessment run state.
     */
    state: AssessmentRunState;
  }
  export type AssessmentRunStateChangeList = AssessmentRunStateChange[];
  export type AssessmentRunStateList = AssessmentRunState[];
  export interface AssessmentTarget {
    /**
     * The ARN that specifies the Amazon Inspector assessment target.
     */
    arn: Arn;
    /**
     * The name of the Amazon Inspector assessment target.
     */
    name: AssessmentTargetName;
    /**
     * The ARN that specifies the resource group that is associated with the assessment target.
     */
    resourceGroupArn?: Arn;
    /**
     * The time at which the assessment target is created.
     */
    createdAt: Timestamp;
    /**
     * The time at which UpdateAssessmentTarget is called.
     */
    updatedAt: Timestamp;
  }
  export interface AssessmentTargetFilter {
    /**
     * For a record to match a filter, an explicit value or a string that contains a wildcard that is specified for this data type property must match the value of the assessmentTargetName property of the AssessmentTarget data type.
     */
    assessmentTargetNamePattern?: NamePattern;
  }
  export type AssessmentTargetList = AssessmentTarget[];
  export type AssessmentTargetName = string;
  export interface AssessmentTemplate {
    /**
     * The ARN of the assessment template.
     */
    arn: Arn;
    /**
     * The name of the assessment template.
     */
    name: AssessmentTemplateName;
    /**
     * The ARN of the assessment target that corresponds to this assessment template.
     */
    assessmentTargetArn: Arn;
    /**
     * The duration in seconds specified for this assessment template. The default value is 3600 seconds (one hour). The maximum value is 86400 seconds (one day).
     */
    durationInSeconds: AssessmentRunDuration;
    /**
     * The rules packages that are specified for this assessment template.
     */
    rulesPackageArns: AssessmentTemplateRulesPackageArnList;
    /**
     * The user-defined attributes that are assigned to every generated finding from the assessment run that uses this assessment template.
     */
    userAttributesForFindings: UserAttributeList;
    /**
     * The Amazon Resource Name (ARN) of the most recent assessment run associated with this assessment template. This value exists only when the value of assessmentRunCount is greaterpa than zero.
     */
    lastAssessmentRunArn?: Arn;
    /**
     * The number of existing assessment runs associated with this assessment template. This value can be zero or a positive integer.
     */
    assessmentRunCount: ArnCount;
    /**
     * The time at which the assessment template is created.
     */
    createdAt: Timestamp;
  }
  export interface AssessmentTemplateFilter {
    /**
     * For a record to match a filter, an explicit value or a string that contains a wildcard that is specified for this data type property must match the value of the assessmentTemplateName property of the AssessmentTemplate data type.
     */
    namePattern?: NamePattern;
    /**
     * For a record to match a filter, the value specified for this data type property must inclusively match any value between the specified minimum and maximum values of the durationInSeconds property of the AssessmentTemplate data type.
     */
    durationRange?: DurationRange;
    /**
     * For a record to match a filter, the values that are specified for this data type property must be contained in the list of values of the rulesPackageArns property of the AssessmentTemplate data type.
     */
    rulesPackageArns?: FilterRulesPackageArnList;
  }
  export type AssessmentTemplateList = AssessmentTemplate[];
  export type AssessmentTemplateName = string;
  export type AssessmentTemplateRulesPackageArnList = Arn[];
  export interface AssetAttributes {
    /**
     * The schema version of this data type.
     */
    schemaVersion: NumericVersion;
    /**
     * The ID of the agent that is installed on the EC2 instance where the finding is generated.
     */
    agentId?: AgentId;
    /**
     * The Auto Scaling group of the EC2 instance where the finding is generated.
     */
    autoScalingGroup?: AutoScalingGroup;
    /**
     * The ID of the Amazon Machine Image (AMI) that is installed on the EC2 instance where the finding is generated.
     */
    amiId?: AmiId;
    /**
     * The hostname of the EC2 instance where the finding is generated.
     */
    hostname?: Hostname;
    /**
     * The list of IP v4 addresses of the EC2 instance where the finding is generated.
     */
    ipv4Addresses?: Ipv4AddressList;
    /**
     * The tags related to the EC2 instance where the finding is generated.
     */
    tags?: Tags;
    /**
     * An array of the network interfaces interacting with the EC2 instance where the finding is generated.
     */
    networkInterfaces?: NetworkInterfaces;
  }
  export type AssetType = "ec2-instance"|string;
  export interface Attribute {
    /**
     * The attribute key.
     */
    key: AttributeKey;
    /**
     * The value assigned to the attribute key.
     */
    value?: AttributeValue;
  }
  export type AttributeKey = string;
  export type AttributeList = Attribute[];
  export type AttributeValue = string;
  export type AutoScalingGroup = string;
  export type AutoScalingGroupList = AutoScalingGroup[];
  export type BatchDescribeArnList = Arn[];
  export type BatchDescribeExclusionsArnList = Arn[];
  export type Bool = boolean;
  export interface CreateAssessmentTargetRequest {
    /**
     * The user-defined name that identifies the assessment target that you want to create. The name must be unique within the AWS account.
     */
    assessmentTargetName: AssessmentTargetName;
    /**
     * The ARN that specifies the resource group that is used to create the assessment target. If resourceGroupArn is not specified, all EC2 instances in the current AWS account and region are included in the assessment target.
     */
    resourceGroupArn?: Arn;
  }
  export interface CreateAssessmentTargetResponse {
    /**
     * The ARN that specifies the assessment target that is created.
     */
    assessmentTargetArn: Arn;
  }
  export interface CreateAssessmentTemplateRequest {
    /**
     * The ARN that specifies the assessment target for which you want to create the assessment template.
     */
    assessmentTargetArn: Arn;
    /**
     * The user-defined name that identifies the assessment template that you want to create. You can create several assessment templates for an assessment target. The names of the assessment templates that correspond to a particular assessment target must be unique.
     */
    assessmentTemplateName: AssessmentTemplateName;
    /**
     * The duration of the assessment run in seconds.
     */
    durationInSeconds: AssessmentRunDuration;
    /**
     * The ARNs that specify the rules packages that you want to attach to the assessment template.
     */
    rulesPackageArns: AssessmentTemplateRulesPackageArnList;
    /**
     * The user-defined attributes that are assigned to every finding that is generated by the assessment run that uses this assessment template. An attribute is a key and value pair (an Attribute object). Within an assessment template, each key must be unique.
     */
    userAttributesForFindings?: UserAttributeList;
  }
  export interface CreateAssessmentTemplateResponse {
    /**
     * The ARN that specifies the assessment template that is created.
     */
    assessmentTemplateArn: Arn;
  }
  export interface CreateExclusionsPreviewRequest {
    /**
     * The ARN that specifies the assessment template for which you want to create an exclusions preview.
     */
    assessmentTemplateArn: Arn;
  }
  export interface CreateExclusionsPreviewResponse {
    /**
     * Specifies the unique identifier of the requested exclusions preview. You can use the unique identifier to retrieve the exclusions preview when running the GetExclusionsPreview API.
     */
    previewToken: UUID;
  }
  export interface CreateResourceGroupRequest {
    /**
     * A collection of keys and an array of possible values, '[{"key":"key1","values":["Value1","Value2"]},{"key":"Key2","values":["Value3"]}]'. For example,'[{"key":"Name","values":["TestEC2Instance"]}]'.
     */
    resourceGroupTags: ResourceGroupTags;
  }
  export interface CreateResourceGroupResponse {
    /**
     * The ARN that specifies the resource group that is created.
     */
    resourceGroupArn: Arn;
  }
  export interface DeleteAssessmentRunRequest {
    /**
     * The ARN that specifies the assessment run that you want to delete.
     */
    assessmentRunArn: Arn;
  }
  export interface DeleteAssessmentTargetRequest {
    /**
     * The ARN that specifies the assessment target that you want to delete.
     */
    assessmentTargetArn: Arn;
  }
  export interface DeleteAssessmentTemplateRequest {
    /**
     * The ARN that specifies the assessment template that you want to delete.
     */
    assessmentTemplateArn: Arn;
  }
  export interface DescribeAssessmentRunsRequest {
    /**
     * The ARN that specifies the assessment run that you want to describe.
     */
    assessmentRunArns: BatchDescribeArnList;
  }
  export interface DescribeAssessmentRunsResponse {
    /**
     * Information about the assessment run.
     */
    assessmentRuns: AssessmentRunList;
    /**
     * Assessment run details that cannot be described. An error code is provided for each failed item.
     */
    failedItems: FailedItems;
  }
  export interface DescribeAssessmentTargetsRequest {
    /**
     * The ARNs that specifies the assessment targets that you want to describe.
     */
    assessmentTargetArns: BatchDescribeArnList;
  }
  export interface DescribeAssessmentTargetsResponse {
    /**
     * Information about the assessment targets.
     */
    assessmentTargets: AssessmentTargetList;
    /**
     * Assessment target details that cannot be described. An error code is provided for each failed item.
     */
    failedItems: FailedItems;
  }
  export interface DescribeAssessmentTemplatesRequest {
    assessmentTemplateArns: BatchDescribeArnList;
  }
  export interface DescribeAssessmentTemplatesResponse {
    /**
     * Information about the assessment templates.
     */
    assessmentTemplates: AssessmentTemplateList;
    /**
     * Assessment template details that cannot be described. An error code is provided for each failed item.
     */
    failedItems: FailedItems;
  }
  export interface DescribeCrossAccountAccessRoleResponse {
    /**
     * The ARN that specifies the IAM role that Amazon Inspector uses to access your AWS account.
     */
    roleArn: Arn;
    /**
     * A Boolean value that specifies whether the IAM role has the necessary policies attached to enable Amazon Inspector to access your AWS account.
     */
    valid: Bool;
    /**
     * The date when the cross-account access role was registered.
     */
    registeredAt: Timestamp;
  }
  export interface DescribeExclusionsRequest {
    /**
     * The list of ARNs that specify the exclusions that you want to describe.
     */
    exclusionArns: BatchDescribeExclusionsArnList;
    /**
     * The locale into which you want to translate the exclusion's title, description, and recommendation.
     */
    locale?: Locale;
  }
  export interface DescribeExclusionsResponse {
    /**
     * Information about the exclusions.
     */
    exclusions: ExclusionMap;
    /**
     * Exclusion details that cannot be described. An error code is provided for each failed item.
     */
    failedItems: FailedItems;
  }
  export interface DescribeFindingsRequest {
    /**
     * The ARN that specifies the finding that you want to describe.
     */
    findingArns: BatchDescribeArnList;
    /**
     * The locale into which you want to translate a finding description, recommendation, and the short description that identifies the finding.
     */
    locale?: Locale;
  }
  export interface DescribeFindingsResponse {
    /**
     * Information about the finding.
     */
    findings: FindingList;
    /**
     * Finding details that cannot be described. An error code is provided for each failed item.
     */
    failedItems: FailedItems;
  }
  export interface DescribeResourceGroupsRequest {
    /**
     * The ARN that specifies the resource group that you want to describe.
     */
    resourceGroupArns: BatchDescribeArnList;
  }
  export interface DescribeResourceGroupsResponse {
    /**
     * Information about a resource group.
     */
    resourceGroups: ResourceGroupList;
    /**
     * Resource group details that cannot be described. An error code is provided for each failed item.
     */
    failedItems: FailedItems;
  }
  export interface DescribeRulesPackagesRequest {
    /**
     * The ARN that specifies the rules package that you want to describe.
     */
    rulesPackageArns: BatchDescribeArnList;
    /**
     * The locale that you want to translate a rules package description into.
     */
    locale?: Locale;
  }
  export interface DescribeRulesPackagesResponse {
    /**
     * Information about the rules package.
     */
    rulesPackages: RulesPackageList;
    /**
     * Rules package details that cannot be described. An error code is provided for each failed item.
     */
    failedItems: FailedItems;
  }
  export interface DurationRange {
    /**
     * The minimum value of the duration range. Must be greater than zero.
     */
    minSeconds?: AssessmentRunDuration;
    /**
     * The maximum value of the duration range. Must be less than or equal to 604800 seconds (1 week).
     */
    maxSeconds?: AssessmentRunDuration;
  }
  export interface EventSubscription {
    /**
     * The event for which Amazon Simple Notification Service (SNS) notifications are sent.
     */
    event: InspectorEvent;
    /**
     * The time at which SubscribeToEvent is called.
     */
    subscribedAt: Timestamp;
  }
  export type EventSubscriptionList = EventSubscription[];
  export interface Exclusion {
    /**
     * The ARN that specifies the exclusion.
     */
    arn: Arn;
    /**
     * The name of the exclusion.
     */
    title: Text;
    /**
     * The description of the exclusion.
     */
    description: Text;
    /**
     * The recommendation for the exclusion.
     */
    recommendation: Text;
    /**
     * The AWS resources for which the exclusion pertains.
     */
    scopes: ScopeList;
    /**
     * The system-defined attributes for the exclusion.
     */
    attributes?: AttributeList;
  }
  export type ExclusionMap = {[key: string]: Exclusion};
  export interface ExclusionPreview {
    /**
     * The name of the exclusion preview.
     */
    title: Text;
    /**
     * The description of the exclusion preview.
     */
    description: Text;
    /**
     * The recommendation for the exclusion preview.
     */
    recommendation: Text;
    /**
     * The AWS resources for which the exclusion preview pertains.
     */
    scopes: ScopeList;
    /**
     * The system-defined attributes for the exclusion preview.
     */
    attributes?: AttributeList;
  }
  export type ExclusionPreviewList = ExclusionPreview[];
  export interface FailedItemDetails {
    /**
     * The status code of a failed item.
     */
    failureCode: FailedItemErrorCode;
    /**
     * Indicates whether you can immediately retry a request for this item for a specified resource.
     */
    retryable: Bool;
  }
  export type FailedItemErrorCode = "INVALID_ARN"|"DUPLICATE_ARN"|"ITEM_DOES_NOT_EXIST"|"ACCESS_DENIED"|"LIMIT_EXCEEDED"|"INTERNAL_ERROR"|string;
  export type FailedItems = {[key: string]: FailedItemDetails};
  export type FilterRulesPackageArnList = Arn[];
  export interface Finding {
    /**
     * The ARN that specifies the finding.
     */
    arn: Arn;
    /**
     * The schema version of this data type.
     */
    schemaVersion?: NumericVersion;
    /**
     * The data element is set to "Inspector".
     */
    service?: ServiceName;
    /**
     * This data type is used in the Finding data type.
     */
    serviceAttributes?: InspectorServiceAttributes;
    /**
     * The type of the host from which the finding is generated.
     */
    assetType?: AssetType;
    /**
     * A collection of attributes of the host from which the finding is generated.
     */
    assetAttributes?: AssetAttributes;
    /**
     * The ID of the finding.
     */
    id?: FindingId;
    /**
     * The name of the finding.
     */
    title?: Text;
    /**
     * The description of the finding.
     */
    description?: Text;
    /**
     * The recommendation for the finding.
     */
    recommendation?: Text;
    /**
     * The finding severity. Values can be set to High, Medium, Low, and Informational.
     */
    severity?: Severity;
    /**
     * The numeric value of the finding severity.
     */
    numericSeverity?: NumericSeverity;
    /**
     * This data element is currently not used.
     */
    confidence?: IocConfidence;
    /**
     * This data element is currently not used.
     */
    indicatorOfCompromise?: Bool;
    /**
     * The system-defined attributes for the finding.
     */
    attributes: AttributeList;
    /**
     * The user-defined attributes that are assigned to the finding.
     */
    userAttributes: UserAttributeList;
    /**
     * The time when the finding was generated.
     */
    createdAt: Timestamp;
    /**
     * The time when AddAttributesToFindings is called.
     */
    updatedAt: Timestamp;
  }
  export type FindingCount = number;
  export interface FindingFilter {
    /**
     * For a record to match a filter, one of the values that is specified for this data type property must be the exact match of the value of the agentId property of the Finding data type.
     */
    agentIds?: AgentIdList;
    /**
     * For a record to match a filter, one of the values that is specified for this data type property must be the exact match of the value of the autoScalingGroup property of the Finding data type.
     */
    autoScalingGroups?: AutoScalingGroupList;
    /**
     * For a record to match a filter, one of the values that is specified for this data type property must be the exact match of the value of the ruleName property of the Finding data type.
     */
    ruleNames?: RuleNameList;
    /**
     * For a record to match a filter, one of the values that is specified for this data type property must be the exact match of the value of the severity property of the Finding data type.
     */
    severities?: SeverityList;
    /**
     * For a record to match a filter, one of the values that is specified for this data type property must be the exact match of the value of the rulesPackageArn property of the Finding data type.
     */
    rulesPackageArns?: FilterRulesPackageArnList;
    /**
     * For a record to match a filter, the list of values that are specified for this data type property must be contained in the list of values of the attributes property of the Finding data type.
     */
    attributes?: AttributeList;
    /**
     * For a record to match a filter, the value that is specified for this data type property must be contained in the list of values of the userAttributes property of the Finding data type.
     */
    userAttributes?: AttributeList;
    /**
     * The time range during which the finding is generated.
     */
    creationTimeRange?: TimestampRange;
  }
  export type FindingId = string;
  export type FindingList = Finding[];
  export interface GetAssessmentReportRequest {
    /**
     * The ARN that specifies the assessment run for which you want to generate a report.
     */
    assessmentRunArn: Arn;
    /**
     * Specifies the file format (html or pdf) of the assessment report that you want to generate.
     */
    reportFileFormat: ReportFileFormat;
    /**
     * Specifies the type of the assessment report that you want to generate. There are two types of assessment reports: a finding report and a full report. For more information, see Assessment Reports. 
     */
    reportType: ReportType;
  }
  export interface GetAssessmentReportResponse {
    /**
     * Specifies the status of the request to generate an assessment report. 
     */
    status: ReportStatus;
    /**
     * Specifies the URL where you can find the generated assessment report. This parameter is only returned if the report is successfully generated.
     */
    url?: Url;
  }
  export interface GetExclusionsPreviewRequest {
    /**
     * The ARN that specifies the assessment template for which the exclusions preview was requested.
     */
    assessmentTemplateArn: Arn;
    /**
     * The unique identifier associated of the exclusions preview.
     */
    previewToken: UUID;
    /**
     * You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the GetExclusionsPreviewRequest action. Subsequent calls to the action fill nextToken in the request with the value of nextToken from the previous response to continue listing data.
     */
    nextToken?: PaginationToken;
    /**
     * You can use this parameter to indicate the maximum number of items you want in the response. The default value is 100. The maximum value is 500.
     */
    maxResults?: ListMaxResults;
    /**
     * The locale into which you want to translate the exclusion's title, description, and recommendation.
     */
    locale?: Locale;
  }
  export interface GetExclusionsPreviewResponse {
    /**
     * Specifies the status of the request to generate an exclusions preview.
     */
    previewStatus: PreviewStatus;
    /**
     * Information about the exclusions included in the preview.
     */
    exclusionPreviews?: ExclusionPreviewList;
    /**
     * When a response is generated, if there is more data to be listed, this parameters is present in the response and contains the value to use for the nextToken parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.
     */
    nextToken?: PaginationToken;
  }
  export interface GetTelemetryMetadataRequest {
    /**
     * The ARN that specifies the assessment run that has the telemetry data that you want to obtain.
     */
    assessmentRunArn: Arn;
  }
  export interface GetTelemetryMetadataResponse {
    /**
     * Telemetry details.
     */
    telemetryMetadata: TelemetryMetadataList;
  }
  export type Hostname = string;
  export type InspectorEvent = "ASSESSMENT_RUN_STARTED"|"ASSESSMENT_RUN_COMPLETED"|"ASSESSMENT_RUN_STATE_CHANGED"|"FINDING_REPORTED"|"OTHER"|string;
  export interface InspectorServiceAttributes {
    /**
     * The schema version of this data type.
     */
    schemaVersion: NumericVersion;
    /**
     * The ARN of the assessment run during which the finding is generated.
     */
    assessmentRunArn?: Arn;
    /**
     * The ARN of the rules package that is used to generate the finding.
     */
    rulesPackageArn?: Arn;
  }
  export type IocConfidence = number;
  export type Ipv4Address = string;
  export type Ipv4AddressList = Ipv4Address[];
  export type Ipv6Addresses = Text[];
  export type KernelVersion = string;
  export interface ListAssessmentRunAgentsRequest {
    /**
     * The ARN that specifies the assessment run whose agents you want to list.
     */
    assessmentRunArn: Arn;
    /**
     * You can use this parameter to specify a subset of data to be included in the action's response. For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.
     */
    filter?: AgentFilter;
    /**
     * You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the ListAssessmentRunAgents action. Subsequent calls to the action fill nextToken in the request with the value of NextToken from the previous response to continue listing data.
     */
    nextToken?: PaginationToken;
    /**
     * You can use this parameter to indicate the maximum number of items that you want in the response. The default value is 10. The maximum value is 500.
     */
    maxResults?: ListMaxResults;
  }
  export interface ListAssessmentRunAgentsResponse {
    /**
     * A list of ARNs that specifies the agents returned by the action.
     */
    assessmentRunAgents: AssessmentRunAgentList;
    /**
     *  When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the nextToken parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.
     */
    nextToken?: PaginationToken;
  }
  export interface ListAssessmentRunsRequest {
    /**
     * The ARNs that specify the assessment templates whose assessment runs you want to list.
     */
    assessmentTemplateArns?: ListParentArnList;
    /**
     * You can use this parameter to specify a subset of data to be included in the action's response. For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.
     */
    filter?: AssessmentRunFilter;
    /**
     * You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the ListAssessmentRuns action. Subsequent calls to the action fill nextToken in the request with the value of NextToken from the previous response to continue listing data.
     */
    nextToken?: PaginationToken;
    /**
     * You can use this parameter to indicate the maximum number of items that you want in the response. The default value is 10. The maximum value is 500.
     */
    maxResults?: ListMaxResults;
  }
  export interface ListAssessmentRunsResponse {
    /**
     * A list of ARNs that specifies the assessment runs that are returned by the action.
     */
    assessmentRunArns: ListReturnedArnList;
    /**
     *  When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the nextToken parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.
     */
    nextToken?: PaginationToken;
  }
  export interface ListAssessmentTargetsRequest {
    /**
     * You can use this parameter to specify a subset of data to be included in the action's response. For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.
     */
    filter?: AssessmentTargetFilter;
    /**
     * You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the ListAssessmentTargets action. Subsequent calls to the action fill nextToken in the request with the value of NextToken from the previous response to continue listing data.
     */
    nextToken?: PaginationToken;
    /**
     * You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.
     */
    maxResults?: ListMaxResults;
  }
  export interface ListAssessmentTargetsResponse {
    /**
     * A list of ARNs that specifies the assessment targets that are returned by the action.
     */
    assessmentTargetArns: ListReturnedArnList;
    /**
     *  When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the nextToken parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.
     */
    nextToken?: PaginationToken;
  }
  export interface ListAssessmentTemplatesRequest {
    /**
     * A list of ARNs that specifies the assessment targets whose assessment templates you want to list.
     */
    assessmentTargetArns?: ListParentArnList;
    /**
     * You can use this parameter to specify a subset of data to be included in the action's response. For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.
     */
    filter?: AssessmentTemplateFilter;
    /**
     * You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the ListAssessmentTemplates action. Subsequent calls to the action fill nextToken in the request with the value of NextToken from the previous response to continue listing data.
     */
    nextToken?: PaginationToken;
    /**
     * You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.
     */
    maxResults?: ListMaxResults;
  }
  export interface ListAssessmentTemplatesResponse {
    /**
     * A list of ARNs that specifies the assessment templates returned by the action.
     */
    assessmentTemplateArns: ListReturnedArnList;
    /**
     *  When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the nextToken parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.
     */
    nextToken?: PaginationToken;
  }
  export type ListEventSubscriptionsMaxResults = number;
  export interface ListEventSubscriptionsRequest {
    /**
     * The ARN of the assessment template for which you want to list the existing event subscriptions.
     */
    resourceArn?: Arn;
    /**
     * You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the ListEventSubscriptions action. Subsequent calls to the action fill nextToken in the request with the value of NextToken from the previous response to continue listing data.
     */
    nextToken?: PaginationToken;
    /**
     * You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.
     */
    maxResults?: ListEventSubscriptionsMaxResults;
  }
  export interface ListEventSubscriptionsResponse {
    /**
     * Details of the returned event subscriptions.
     */
    subscriptions: SubscriptionList;
    /**
     *  When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the nextToken parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.
     */
    nextToken?: PaginationToken;
  }
  export interface ListExclusionsRequest {
    /**
     * The ARN of the assessment run that generated the exclusions that you want to list.
     */
    assessmentRunArn: Arn;
    /**
     * You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the ListExclusionsRequest action. Subsequent calls to the action fill nextToken in the request with the value of nextToken from the previous response to continue listing data.
     */
    nextToken?: PaginationToken;
    /**
     * You can use this parameter to indicate the maximum number of items you want in the response. The default value is 100. The maximum value is 500.
     */
    maxResults?: ListMaxResults;
  }
  export interface ListExclusionsResponse {
    /**
     * A list of exclusions' ARNs returned by the action.
     */
    exclusionArns: ListReturnedArnList;
    /**
     * When a response is generated, if there is more data to be listed, this parameters is present in the response and contains the value to use for the nextToken parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.
     */
    nextToken?: PaginationToken;
  }
  export interface ListFindingsRequest {
    /**
     * The ARNs of the assessment runs that generate the findings that you want to list.
     */
    assessmentRunArns?: ListParentArnList;
    /**
     * You can use this parameter to specify a subset of data to be included in the action's response. For a record to match a filter, all specified filter attributes must match. When multiple values are specified for a filter attribute, any of the values can match.
     */
    filter?: FindingFilter;
    /**
     * You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the ListFindings action. Subsequent calls to the action fill nextToken in the request with the value of NextToken from the previous response to continue listing data.
     */
    nextToken?: PaginationToken;
    /**
     * You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.
     */
    maxResults?: ListMaxResults;
  }
  export interface ListFindingsResponse {
    /**
     * A list of ARNs that specifies the findings returned by the action.
     */
    findingArns: ListReturnedArnList;
    /**
     *  When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the nextToken parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.
     */
    nextToken?: PaginationToken;
  }
  export type ListMaxResults = number;
  export type ListParentArnList = Arn[];
  export type ListReturnedArnList = Arn[];
  export interface ListRulesPackagesRequest {
    /**
     * You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the ListRulesPackages action. Subsequent calls to the action fill nextToken in the request with the value of NextToken from the previous response to continue listing data.
     */
    nextToken?: PaginationToken;
    /**
     * You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.
     */
    maxResults?: ListMaxResults;
  }
  export interface ListRulesPackagesResponse {
    /**
     * The list of ARNs that specifies the rules packages returned by the action.
     */
    rulesPackageArns: ListReturnedArnList;
    /**
     *  When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the nextToken parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.
     */
    nextToken?: PaginationToken;
  }
  export interface ListTagsForResourceRequest {
    /**
     * The ARN that specifies the assessment template whose tags you want to list.
     */
    resourceArn: Arn;
  }
  export interface ListTagsForResourceResponse {
    /**
     * A collection of key and value pairs.
     */
    tags: TagList;
  }
  export type Locale = "EN_US"|string;
  export type Long = number;
  export type Message = string;
  export type MessageType = string;
  export type NamePattern = string;
  export interface NetworkInterface {
    /**
     * The ID of the network interface.
     */
    networkInterfaceId?: Text;
    /**
     * The ID of a subnet associated with the network interface.
     */
    subnetId?: Text;
    /**
     * The ID of a VPC associated with the network interface.
     */
    vpcId?: Text;
    /**
     * The name of a private DNS associated with the network interface.
     */
    privateDnsName?: Text;
    /**
     * The private IP address associated with the network interface.
     */
    privateIpAddress?: Text;
    /**
     * A list of the private IP addresses associated with the network interface. Includes the privateDnsName and privateIpAddress.
     */
    privateIpAddresses?: PrivateIpAddresses;
    /**
     * The name of a public DNS associated with the network interface.
     */
    publicDnsName?: Text;
    /**
     * The public IP address from which the network interface is reachable.
     */
    publicIp?: Text;
    /**
     * The IP addresses associated with the network interface.
     */
    ipv6Addresses?: Ipv6Addresses;
    /**
     * A list of the security groups associated with the network interface. Includes the groupId and groupName.
     */
    securityGroups?: SecurityGroups;
  }
  export type NetworkInterfaces = NetworkInterface[];
  export type NumericSeverity = number;
  export type NumericVersion = number;
  export type OperatingSystem = string;
  export type PaginationToken = string;
  export type PreviewAgentsMaxResults = number;
  export interface PreviewAgentsRequest {
    /**
     * The ARN of the assessment target whose agents you want to preview.
     */
    previewAgentsArn: Arn;
    /**
     * You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the PreviewAgents action. Subsequent calls to the action fill nextToken in the request with the value of NextToken from the previous response to continue listing data.
     */
    nextToken?: PaginationToken;
    /**
     * You can use this parameter to indicate the maximum number of items you want in the response. The default value is 10. The maximum value is 500.
     */
    maxResults?: PreviewAgentsMaxResults;
  }
  export interface PreviewAgentsResponse {
    /**
     * The resulting list of agents.
     */
    agentPreviews: AgentPreviewList;
    /**
     *  When a response is generated, if there is more data to be listed, this parameter is present in the response and contains the value to use for the nextToken parameter in a subsequent pagination request. If there is no more data to be listed, this parameter is set to null.
     */
    nextToken?: PaginationToken;
  }
  export type PreviewStatus = "WORK_IN_PROGRESS"|"COMPLETED"|string;
  export interface PrivateIp {
    /**
     * The DNS name of the private IP address.
     */
    privateDnsName?: Text;
    /**
     * The full IP address of the network inteface.
     */
    privateIpAddress?: Text;
  }
  export type PrivateIpAddresses = PrivateIp[];
  export type ProviderName = string;
  export interface RegisterCrossAccountAccessRoleRequest {
    /**
     * The ARN of the IAM role that grants Amazon Inspector access to AWS Services needed to perform security assessments. 
     */
    roleArn: Arn;
  }
  export interface RemoveAttributesFromFindingsRequest {
    /**
     * The ARNs that specify the findings that you want to remove attributes from.
     */
    findingArns: AddRemoveAttributesFindingArnList;
    /**
     * The array of attribute keys that you want to remove from specified findings.
     */
    attributeKeys: UserAttributeKeyList;
  }
  export interface RemoveAttributesFromFindingsResponse {
    /**
     * Attributes details that cannot be described. An error code is provided for each failed item.
     */
    failedItems: FailedItems;
  }
  export type ReportFileFormat = "HTML"|"PDF"|string;
  export type ReportStatus = "WORK_IN_PROGRESS"|"FAILED"|"COMPLETED"|string;
  export type ReportType = "FINDING"|"FULL"|string;
  export interface ResourceGroup {
    /**
     * The ARN of the resource group.
     */
    arn: Arn;
    /**
     * The tags (key and value pairs) of the resource group. This data type property is used in the CreateResourceGroup action.
     */
    tags: ResourceGroupTags;
    /**
     * The time at which resource group is created.
     */
    createdAt: Timestamp;
  }
  export type ResourceGroupList = ResourceGroup[];
  export interface ResourceGroupTag {
    /**
     * A tag key.
     */
    key: TagKey;
    /**
     * The value assigned to a tag key.
     */
    value?: TagValue;
  }
  export type ResourceGroupTags = ResourceGroupTag[];
  export type RuleName = string;
  export type RuleNameList = RuleName[];
  export interface RulesPackage {
    /**
     * The ARN of the rules package.
     */
    arn: Arn;
    /**
     * The name of the rules package.
     */
    name: RulesPackageName;
    /**
     * The version ID of the rules package.
     */
    version: Version;
    /**
     * The provider of the rules package.
     */
    provider: ProviderName;
    /**
     * The description of the rules package.
     */
    description?: Text;
  }
  export type RulesPackageList = RulesPackage[];
  export type RulesPackageName = string;
  export interface Scope {
    /**
     * The type of the scope.
     */
    key?: ScopeType;
    /**
     * The resource identifier for the specified scope type.
     */
    value?: ScopeValue;
  }
  export type ScopeList = Scope[];
  export type ScopeType = "INSTANCE_ID"|"RULES_PACKAGE_ARN"|string;
  export type ScopeValue = string;
  export interface SecurityGroup {
    /**
     * The name of the security group.
     */
    groupName?: Text;
    /**
     * The ID of the security group.
     */
    groupId?: Text;
  }
  export type SecurityGroups = SecurityGroup[];
  export type ServiceName = string;
  export interface SetTagsForResourceRequest {
    /**
     * The ARN of the assessment template that you want to set tags to.
     */
    resourceArn: Arn;
    /**
     * A collection of key and value pairs that you want to set to the assessment template.
     */
    tags?: TagList;
  }
  export type Severity = "Low"|"Medium"|"High"|"Informational"|"Undefined"|string;
  export type SeverityList = Severity[];
  export interface StartAssessmentRunRequest {
    /**
     * The ARN of the assessment template of the assessment run that you want to start.
     */
    assessmentTemplateArn: Arn;
    /**
     * You can specify the name for the assessment run. The name must be unique for the assessment template whose ARN is used to start the assessment run.
     */
    assessmentRunName?: AssessmentRunName;
  }
  export interface StartAssessmentRunResponse {
    /**
     * The ARN of the assessment run that has been started.
     */
    assessmentRunArn: Arn;
  }
  export type StopAction = "START_EVALUATION"|"SKIP_EVALUATION"|string;
  export interface StopAssessmentRunRequest {
    /**
     * The ARN of the assessment run that you want to stop.
     */
    assessmentRunArn: Arn;
    /**
     * An input option that can be set to either START_EVALUATION or SKIP_EVALUATION. START_EVALUATION (the default value), stops the AWS agent from collecting data and begins the results evaluation and the findings generation process. SKIP_EVALUATION cancels the assessment run immediately, after which no findings are generated.
     */
    stopAction?: StopAction;
  }
  export interface SubscribeToEventRequest {
    /**
     * The ARN of the assessment template that is used during the event for which you want to receive SNS notifications.
     */
    resourceArn: Arn;
    /**
     * The event for which you want to receive SNS notifications.
     */
    event: InspectorEvent;
    /**
     * The ARN of the SNS topic to which the SNS notifications are sent.
     */
    topicArn: Arn;
  }
  export interface Subscription {
    /**
     * The ARN of the assessment template that is used during the event for which the SNS notification is sent.
     */
    resourceArn: Arn;
    /**
     * The ARN of the Amazon Simple Notification Service (SNS) topic to which the SNS notifications are sent.
     */
    topicArn: Arn;
    /**
     * The list of existing event subscriptions.
     */
    eventSubscriptions: EventSubscriptionList;
  }
  export type SubscriptionList = Subscription[];
  export interface Tag {
    /**
     * A tag key.
     */
    key: TagKey;
    /**
     * A value assigned to a tag key.
     */
    value?: TagValue;
  }
  export type TagKey = string;
  export type TagList = Tag[];
  export type TagValue = string;
  export type Tags = Tag[];
  export interface TelemetryMetadata {
    /**
     * A specific type of behavioral data that is collected by the agent.
     */
    messageType: MessageType;
    /**
     * The count of messages that the agent sends to the Amazon Inspector service.
     */
    count: Long;
    /**
     * The data size of messages that the agent sends to the Amazon Inspector service.
     */
    dataSize?: Long;
  }
  export type TelemetryMetadataList = TelemetryMetadata[];
  export type Text = string;
  export type Timestamp = Date;
  export interface TimestampRange {
    /**
     * The minimum value of the timestamp range.
     */
    beginDate?: Timestamp;
    /**
     * The maximum value of the timestamp range.
     */
    endDate?: Timestamp;
  }
  export type UUID = string;
  export interface UnsubscribeFromEventRequest {
    /**
     * The ARN of the assessment template that is used during the event for which you want to stop receiving SNS notifications.
     */
    resourceArn: Arn;
    /**
     * The event for which you want to stop receiving SNS notifications.
     */
    event: InspectorEvent;
    /**
     * The ARN of the SNS topic to which SNS notifications are sent.
     */
    topicArn: Arn;
  }
  export interface UpdateAssessmentTargetRequest {
    /**
     * The ARN of the assessment target that you want to update.
     */
    assessmentTargetArn: Arn;
    /**
     * The name of the assessment target that you want to update.
     */
    assessmentTargetName: AssessmentTargetName;
    /**
     * The ARN of the resource group that is used to specify the new resource group to associate with the assessment target.
     */
    resourceGroupArn?: Arn;
  }
  export type Url = string;
  export type UserAttributeKeyList = AttributeKey[];
  export type UserAttributeList = Attribute[];
  export type Version = string;
  /**
   * A string in YYYY-MM-DD format that represents the latest possible API version that can be used in this service. Specify 'latest' to use the latest possible version.
   */
  export type apiVersion = "2015-08-18"|"2016-02-16"|"latest"|string;
  export interface ClientApiVersions {
    /**
     * A string in YYYY-MM-DD format that represents the latest possible API version that can be used in this service. Specify 'latest' to use the latest possible version.
     */
    apiVersion?: apiVersion;
  }
  export type ClientConfiguration = ServiceConfigurationOptions & ClientApiVersions;
  /**
   * Contains interfaces for use with the Inspector client.
   */
  export import Types = Inspector;
}
export = Inspector;
