/**
 * Environment Switcher Script
 *
 * This script switches between development and production environments
 * by setting the NODE_ENV environment variable.
 *
 * SECURITY NOTE: In actual production deployments, environment variables should be set
 * directly in the hosting platform rather than using .env files for sensitive credentials.
 *
 * Usage:
 *   node switch-env.js development
 *   node switch-env.js production
 */

const fs = require('fs');
const path = require('path');

// Get environment from command line
const env = process.argv[2]?.toLowerCase();

// Validate environment
if (!env || (env !== 'development' && env !== 'production')) {
  console.error('Please specify environment: development or production');
  console.error('Usage: node switch-env.js [development|production]');
  process.exit(1);
}

// Create a simple .env.node_env file that sets NODE_ENV
const nodeEnvContent = `NODE_ENV=${env}`;
const nodeEnvFile = path.join(__dirname, '.env.node_env');

// Write the NODE_ENV setting to the file
try {
  fs.writeFileSync(nodeEnvFile, nodeEnvContent);
  console.log(`Successfully set NODE_ENV to ${env}`);
  console.log(`Created ${nodeEnvFile} with NODE_ENV=${env}`);
} catch (error) {
  console.error(`Error setting NODE_ENV: ${error.message}`);
  process.exit(1);
}

// Additional instructions
console.log('\nNext steps:');
console.log('1. Restart the server to apply changes');
console.log('2. Check the logs to ensure all services initialized correctly');

if (env === 'development') {
  console.log('\nDevelopment mode:');
  console.log('- Using settings from .env and .env.development');
  console.log('- API URLs from .env');
  console.log('- Development-specific settings from .env.development');
  console.log('- Relaxed rate limiting and security for development');
} else {
  console.log('\nProduction mode:');
  console.log('- Using settings from .env and .env.production');
  console.log('- API URLs from .env');
  console.log('- Production-specific settings from .env.production');
  console.log('- Strict security measures and rate limiting');

  console.log('\n⚠️ SECURITY WARNING ⚠️');
  console.log('For actual production deployments, sensitive credentials should be set');
  console.log('directly in your hosting platform environment variables, not in .env files.');
  console.log('Run `npm run generate:secrets` to create secure random strings for your secrets.');
}

console.log('\nTo switch back, run:');
console.log(`node switch-env.js ${env === 'development' ? 'production' : 'development'}`);

console.log('\nEnvironment files:');
console.log('- .env: Contains all API URLs and endpoints used in both environments');
console.log('- .env.development: Contains development-specific settings');
console.log('- .env.production: Contains production-specific settings');
console.log('- .env.node_env: Contains only the NODE_ENV setting (created by this script)');

