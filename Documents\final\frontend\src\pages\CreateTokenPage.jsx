import React, { useState, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { Transaction, Keypair } from '@solana/web3.js';
import Navbar from '../components/Navbar.jsx';
import TokenCard from '../components/TokenCard.jsx';
import '../styles/createToken.css';
import bitcoinLogo from '../assets/bitcoin-logo.png';
import { API_URL } from '../config/constants';
import axios from 'axios';

const CreateTokenPage = () => {
  const navigate = useNavigate();
  const fileInputRef = useRef(null);
  const { connected, publicKey, signTransaction } = useWallet();
  const { connection } = useConnection();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewImage, setPreviewImage] = useState(bitcoinLogo);
  const [tokenData, setTokenData] = useState({
    name: '',
    symbol: '',
    description: '',
    initialSupply: '',
    website: '',
    twitter: '',
    telegram: '',
    discord: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setTokenData(prev => ({
      ...prev,
      [name]: value
    }));
  };



  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewImage(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!tokenData.name || !tokenData.symbol || !tokenData.description || !tokenData.initialSupply) {
        alert('Please fill in all required fields');
        return;
      }

      // Check if wallet is connected
      if (!connected || !publicKey) {
        alert('Please connect your Solana wallet first');
        return;
      }

      // Validate initial supply
      const supply = parseInt(tokenData.initialSupply);
      if (isNaN(supply) || supply <= 0) {
        alert('Initial supply must be a positive number');
        return;
      }

      const formData = new FormData();
      formData.append('name', tokenData.name);
      formData.append('symbol', tokenData.symbol.toUpperCase());
      formData.append('description', tokenData.description);
      formData.append('initialSupply', tokenData.initialSupply);
      formData.append('website', tokenData.website);
      formData.append('twitter', tokenData.twitter);
      formData.append('telegram', tokenData.telegram);
      formData.append('discord', tokenData.discord);
      formData.append('creatorPublicKey', publicKey.toString());

      if (fileInputRef.current?.files[0]) {
        formData.append('image', fileInputRef.current.files[0]);
      }

      // Step 1: Get transaction from backend
      const url = `${API_URL}/tokens/create`;
      console.log('🔍 Debug URL construction:', {
        API_URL,
        url,
        typeof_API_URL: typeof API_URL
      });

      const response = await axios.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        // Step 2: Deserialize and sign transaction
        const transactionBuffer = Buffer.from(response.data.transaction, 'base64');
        const transaction = Transaction.from(transactionBuffer);

        // Add the mint keypair signature
        const mintKeypair = Keypair.fromSecretKey(new Uint8Array(response.data.mintKeypair));
        transaction.partialSign(mintKeypair);

        // Step 3: Sign with user's wallet
        const signedTransaction = await signTransaction(transaction);

        // Step 4: Send transaction
        const signature = await connection.sendRawTransaction(signedTransaction.serialize());

        // Step 5: Confirm transaction
        await connection.confirmTransaction(signature, 'confirmed');

        alert(`Token created successfully! Mint: ${response.data.token.mint}\nTransaction: ${signature}`);
        console.log('Token created:', response.data);
        console.log('Transaction signature:', signature);
        navigate('/home');
      } else {
        throw new Error(response.data.error || 'Failed to create token');
      }
    } catch (error) {
      console.error('Error creating token:', error);
      alert('Failed to create token: ' + (error.response?.data?.error || error.message));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="create-token-page">
      <Navbar />
      
      <div className="modal-overlay">
        <div className="modal-container">
          <div className="modal-header">
            <h1 className="gradient-text">Create Token</h1>
            <Link to="/home" className="close-button">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </Link>
          </div>

          <div className="modal-content">
            {/* Wallet Status */}
            {!connected && (
              <div className="wallet-warning">
                <p>⚠️ Please connect your Solana wallet in the navbar to create tokens</p>
              </div>
            )}

            <form className="token-form" onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="name">Token Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={tokenData.name}
                  onChange={handleInputChange}
                  placeholder="Enter token name"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="symbol">Token Symbol</label>
                <input
                  type="text"
                  id="symbol"
                  name="symbol"
                  value={tokenData.symbol}
                  onChange={handleInputChange}
                  placeholder="Enter token symbol (e.g., BTC)"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="description">Description</label>
                <textarea
                  id="description"
                  name="description"
                  value={tokenData.description}
                  onChange={handleInputChange}
                  placeholder="Describe your token"
                  rows="4"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="initialSupply">Initial Supply</label>
                <input
                  type="number"
                  id="initialSupply"
                  name="initialSupply"
                  value={tokenData.initialSupply}
                  onChange={handleInputChange}
                  placeholder="Enter initial supply"
                  required
                />
              </div>

              <div className="social-section">
                <h4 className="social-section-title">Social Links</h4>

                <div className="form-group">
                  <label htmlFor="website">🌐 Website (Optional)</label>
                  <input
                    type="url"
                    id="website"
                    name="website"
                    value={tokenData.website}
                    onChange={handleInputChange}
                    placeholder="https://yourwebsite.com"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="twitter">🐦 Twitter (Optional)</label>
                  <input
                    type="url"
                    id="twitter"
                    name="twitter"
                    value={tokenData.twitter}
                    onChange={handleInputChange}
                    placeholder="https://twitter.com/yourusername"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="telegram">📱 Telegram (Optional)</label>
                  <input
                    type="url"
                    id="telegram"
                    name="telegram"
                    value={tokenData.telegram}
                    onChange={handleInputChange}
                    placeholder="https://t.me/yourchannel"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="discord">🎮 Discord (Optional)</label>
                  <input
                    type="url"
                    id="discord"
                    name="discord"
                    value={tokenData.discord}
                    onChange={handleInputChange}
                    placeholder="https://discord.gg/yourinvite"
                  />
                </div>
              </div>

              <div className="form-group">
                <label>Token Image</label>
                <div className="image-upload-container">
                  <div className="image-preview" onClick={handleImageClick}>
                    <img src={previewImage} alt="Token preview" />
                    <div className="upload-overlay">
                      Click to upload image
                    </div>
                  </div>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleImageUpload}
                    accept="image/*"
                    style={{ display: 'none' }}
                  />
                </div>
              </div>

              <button
                type="submit"
                className="create-button"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Creating Token...' : 'Create Token'}
              </button>
            </form>

            <div className="token-preview-section">
              <h3 className="preview-title">Token Preview</h3>
              <div className="preview-card-container">
                <TokenCard
                  token={{
                    id: 'preview',
                    name: tokenData.name || 'Token Name',
                    symbol: tokenData.symbol ? tokenData.symbol.toUpperCase() : 'SYMBOL',
                    description: tokenData.description || 'Token description will appear here...',
                    price: '0.01',
                    marketCap: tokenData.initialSupply ?
                      (Number(tokenData.initialSupply) * 0.01).toLocaleString() :
                      '0',
                    volume: '0',
                    image: previewImage,
                    creator: 'You',
                    created_at: new Date().toISOString(),
                    website: tokenData.website,
                    twitter: tokenData.twitter,
                    telegram: tokenData.telegram,
                    discord: tokenData.discord
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateTokenPage;
