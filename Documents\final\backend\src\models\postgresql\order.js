/**
 * Order Model (PostgreSQL)
 * 
 * Stores all order data including:
 * - Buy and sell orders
 * - Order status
 * - Order matching information
 */

const { sequelize, Sequelize } = require('../../config/postgresql');

const Order = sequelize.define('Order', {
  // Order ID (primary key)
  id: {
    type: Sequelize.UUID,
    defaultValue: Sequelize.UUIDV4,
    primaryKey: true
  },
  
  // Order type (buy, sell)
  type: {
    type: Sequelize.ENUM('buy', 'sell'),
    allowNull: false
  },
  
  // User wallet address
  walletAddress: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Token mint address
  tokenMint: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Token amount (as string to preserve precision)
  tokenAmount: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Price per token (in SOL, as string)
  price: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Total order value (in SOL, as string)
  totalValue: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Order status
  status: {
    type: Sequelize.ENUM('open', 'partial', 'filled', 'cancelled', 'expired'),
    defaultValue: 'open',
    allowNull: false
  },
  
  // Filled amount (as string)
  filledAmount: {
    type: Sequelize.STRING,
    defaultValue: '0',
    allowNull: false
  },
  
  // Remaining amount (as string)
  remainingAmount: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Order expiration time
  expiresAt: {
    type: Sequelize.DATE,
    allowNull: true
  },
  
  // Additional data (JSON)
  metadata: {
    type: Sequelize.JSONB,
    defaultValue: {},
    allowNull: false
  }
}, {
  // Table configuration
  tableName: 'orders',
  timestamps: true,
  indexes: [
    {
      fields: ['walletAddress']
    },
    {
      fields: ['tokenMint']
    },
    {
      fields: ['type']
    },
    {
      fields: ['status']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['price']
    }
  ]
});

module.exports = Order;
