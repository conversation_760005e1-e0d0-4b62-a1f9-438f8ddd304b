import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const hoodi = /*#__PURE__*/ defineChain({
    id: 560048,
    name: '<PERSON><PERSON>',
    nativeCurrency: { name: '<PERSON><PERSON>ther', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.hoodi.ethpandaops.io'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Etherscan',
            url: 'https://hoodi.etherscan.io',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 2589,
        },
    },
    testnet: true,
});
//# sourceMappingURL=hoodi.js.map