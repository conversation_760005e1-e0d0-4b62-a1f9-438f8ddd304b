/**
 * Fix Transactions Table Schema
 * 
 * This script modifies the PostgreSQL database to fix the transactions table schema
 * to match the Sequelize model.
 */

require('dotenv').config();
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

async function fixTransactionsTable() {
  console.log('Fixing transactions table schema...');
  
  // Create PostgreSQL connection
  const pool = new Pool({
    connectionString: process.env.POSTGRESQL_URI,
    ssl: { rejectUnauthorized: false }
  });
  
  try {
    console.log('Connecting to PostgreSQL...');
    const client = await pool.connect();
    
    try {
      // Check if the transactions table exists
      const tableCheckResult = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'transactions'
        );
      `);
      
      const tableExists = tableCheckResult.rows[0].exists;
      
      if (!tableExists) {
        console.log('Transactions table does not exist. Creating it...');
        
        // Create the transactions table with the correct schema
        await client.query(`
          CREATE TABLE transactions (
            id UUID PRIMARY KEY,
            type VARCHAR(50) NOT NULL,
            tx_id VARCHAR(255) UNIQUE,
            signature VARCHAR(255),
            wallet_address VARCHAR(255),
            user_id VARCHAR(255),
            token_mint VARCHAR(255),
            token VARCHAR(50),
            amount DECIMAL(24, 9),
            token_amount VARCHAR(255),
            sol_amount VARCHAR(255),
            fee_amount VARCHAR(255),
            status VARCHAR(50) NOT NULL DEFAULT 'pending',
            block_time TIMESTAMP WITH TIME ZONE,
            metadata JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );
        `);
        
        // Create indexes
        await client.query(`CREATE INDEX idx_transactions_wallet_address ON transactions(wallet_address);`);
        await client.query(`CREATE INDEX idx_transactions_user_id ON transactions(user_id);`);
        await client.query(`CREATE INDEX idx_transactions_tx_id ON transactions(tx_id);`);
        await client.query(`CREATE INDEX idx_transactions_token_mint ON transactions(token_mint);`);
        await client.query(`CREATE INDEX idx_transactions_type ON transactions(type);`);
        await client.query(`CREATE INDEX idx_transactions_status ON transactions(status);`);
        await client.query(`CREATE INDEX idx_transactions_created_at ON transactions(created_at);`);
        
        console.log('Transactions table created successfully!');
      } else {
        console.log('Transactions table exists. Checking columns...');
        
        // Check if user_id column exists
        const userIdCheckResult = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'transactions' 
            AND column_name = 'user_id'
          );
        `);
        
        const userIdExists = userIdCheckResult.rows[0].exists;
        
        if (!userIdExists) {
          console.log('Adding user_id column to transactions table...');
          await client.query(`ALTER TABLE transactions ADD COLUMN user_id VARCHAR(255);`);
          console.log('user_id column added successfully!');
        } else {
          console.log('user_id column already exists.');
        }
        
        // Check if other required columns exist and add them if needed
        const requiredColumns = [
          { name: 'tx_id', type: 'VARCHAR(255)' },
          { name: 'wallet_address', type: 'VARCHAR(255)' },
          { name: 'token_mint', type: 'VARCHAR(255)' },
          { name: 'token_amount', type: 'VARCHAR(255)' },
          { name: 'sol_amount', type: 'VARCHAR(255)' },
          { name: 'fee_amount', type: 'VARCHAR(255)' },
          { name: 'block_time', type: 'TIMESTAMP WITH TIME ZONE' },
          { name: 'metadata', type: 'JSONB DEFAULT \'{}\'' }
        ];
        
        for (const column of requiredColumns) {
          const columnCheckResult = await client.query(`
            SELECT EXISTS (
              SELECT FROM information_schema.columns 
              WHERE table_schema = 'public' 
              AND table_name = 'transactions' 
              AND column_name = '${column.name}'
            );
          `);
          
          const columnExists = columnCheckResult.rows[0].exists;
          
          if (!columnExists) {
            console.log(`Adding ${column.name} column to transactions table...`);
            await client.query(`ALTER TABLE transactions ADD COLUMN ${column.name} ${column.type};`);
            console.log(`${column.name} column added successfully!`);
          }
        }
        
        // Create indexes if they don't exist
        const indexes = [
          { name: 'idx_transactions_wallet_address', column: 'wallet_address' },
          { name: 'idx_transactions_user_id', column: 'user_id' },
          { name: 'idx_transactions_tx_id', column: 'tx_id' },
          { name: 'idx_transactions_token_mint', column: 'token_mint' },
          { name: 'idx_transactions_type', column: 'type' },
          { name: 'idx_transactions_status', column: 'status' },
          { name: 'idx_transactions_created_at', column: 'created_at' }
        ];
        
        for (const index of indexes) {
          const indexCheckResult = await client.query(`
            SELECT EXISTS (
              SELECT FROM pg_indexes 
              WHERE schemaname = 'public' 
              AND tablename = 'transactions' 
              AND indexname = '${index.name}'
            );
          `);
          
          const indexExists = indexCheckResult.rows[0].exists;
          
          if (!indexExists) {
            console.log(`Creating index ${index.name} on transactions table...`);
            await client.query(`CREATE INDEX ${index.name} ON transactions(${index.column});`);
            console.log(`Index ${index.name} created successfully!`);
          }
        }
      }
      
      console.log('Transactions table schema fixed successfully!');
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error fixing transactions table schema:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the script
fixTransactionsTable()
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error(`Script failed: ${error.message}`);
    process.exit(1);
  });
