/**
 * Graduation Service
 * 
 * This service handles the graduation system for tokens.
 * Graduation is a process where tokens can be "graduated" to a higher status
 * after meeting certain criteria, with a fee of 2.3 SOL.
 */

const logger = require('../utils/logger');
const balanceService = require('./balanceService');
const platformWalletConfig = require('../config/platformWallet');
const { sequelize } = require('../config/postgresql');
const TokenModel = require('../models/Token');

// Graduation fee in SOL
const GRADUATION_FEE = 2.3;

class GraduationService {
  /**
   * Check if a token is eligible for graduation
   * @param {string} tokenSymbol - The token symbol
   * @returns {Promise<Object>} Eligibility status and requirements
   */
  async checkEligibility(tokenSymbol) {
    try {
      // Get token data
      const token = await TokenModel.findOne({ symbol: tokenSymbol });
      
      if (!token) {
        throw new Error(`Token ${tokenSymbol} not found`);
      }
      
      // Define graduation criteria
      const criteria = {
        minHolders: 50,         // Minimum number of holders
        minTrades: 100,         // Minimum number of trades
        minAgeInDays: 7,        // Minimum age in days
        minLiquidityInSol: 100  // Minimum liquidity in SOL
      };
      
      // Get token statistics
      const tokenStats = await this._getTokenStats(tokenSymbol);
      
      // Check if token meets all criteria
      const meetsHoldersCriteria = tokenStats.holders >= criteria.minHolders;
      const meetsTradesCriteria = tokenStats.trades >= criteria.minTrades;
      const meetsAgeCriteria = tokenStats.ageInDays >= criteria.minAgeInDays;
      const meetsLiquidityCriteria = tokenStats.liquidityInSol >= criteria.minLiquidityInSol;
      
      const isEligible = meetsHoldersCriteria && meetsTradesCriteria && 
                         meetsAgeCriteria && meetsLiquidityCriteria;
      
      return {
        isEligible,
        currentStatus: token.status || 'standard',
        fee: GRADUATION_FEE,
        requirements: {
          holders: {
            required: criteria.minHolders,
            current: tokenStats.holders,
            met: meetsHoldersCriteria
          },
          trades: {
            required: criteria.minTrades,
            current: tokenStats.trades,
            met: meetsTradesCriteria
          },
          age: {
            required: criteria.minAgeInDays,
            current: tokenStats.ageInDays,
            met: meetsAgeCriteria
          },
          liquidity: {
            required: criteria.minLiquidityInSol,
            current: tokenStats.liquidityInSol,
            met: meetsLiquidityCriteria
          }
        }
      };
    } catch (error) {
      logger.error(`Error checking graduation eligibility for ${tokenSymbol}: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Process graduation for a token
   * @param {string} tokenSymbol - The token symbol
   * @param {string} userId - The user ID requesting graduation
   * @returns {Promise<Object>} Graduation result
   */
  async graduateToken(tokenSymbol, userId) {
    const transaction = await sequelize.transaction();
    
    try {
      // Check eligibility
      const eligibility = await this.checkEligibility(tokenSymbol);
      
      if (!eligibility.isEligible) {
        throw new Error(`Token ${tokenSymbol} is not eligible for graduation`);
      }
      
      // Check if user has enough balance
      const userBalance = await balanceService.getUserBalance(userId);
      
      if (parseFloat(userBalance.solBalance) < GRADUATION_FEE) {
        throw new Error(`Insufficient balance. Graduation requires ${GRADUATION_FEE} SOL`);
      }
      
      // Deduct fee from user's balance
      await balanceService.updateSolBalance(userId, -GRADUATION_FEE, {
        type: 'fee',
        description: `Graduation fee for ${tokenSymbol}`
      }, transaction);
      
      // Update token status
      const token = await TokenModel.findOne({ symbol: tokenSymbol });
      token.status = 'graduated';
      token.graduatedAt = new Date();
      await token.save({ session: transaction });
      
      // Commit transaction
      await transaction.commit();
      
      logger.info(`Token ${tokenSymbol} graduated successfully by user ${userId}`);
      
      return {
        success: true,
        tokenSymbol,
        newStatus: 'graduated',
        fee: GRADUATION_FEE
      };
    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      logger.error(`Error graduating token ${tokenSymbol}: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Get token statistics for graduation eligibility
   * @param {string} tokenSymbol - The token symbol
   * @returns {Promise<Object>} Token statistics
   * @private
   */
  async _getTokenStats(tokenSymbol) {
    try {
      // Get token data
      const token = await TokenModel.findOne({ symbol: tokenSymbol });
      
      if (!token) {
        throw new Error(`Token ${tokenSymbol} not found`);
      }
      
      // Calculate token age in days
      const createdAt = new Date(token.createdAt);
      const now = new Date();
      const ageInDays = Math.floor((now - createdAt) / (1000 * 60 * 60 * 24));
      
      // Get holder count from database
      // This would typically come from a database query
      // For now, we'll use a placeholder implementation
      const holdersCount = await this._getTokenHoldersCount(tokenSymbol);
      
      // Get trade count from database
      const tradesCount = await this._getTokenTradesCount(tokenSymbol);
      
      // Get liquidity from database
      const liquidityInSol = await this._getTokenLiquidity(tokenSymbol);
      
      return {
        holders: holdersCount,
        trades: tradesCount,
        ageInDays,
        liquidityInSol
      };
    } catch (error) {
      logger.error(`Error getting token stats for ${tokenSymbol}: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Get token holders count
   * @param {string} tokenSymbol - The token symbol
   * @returns {Promise<number>} Holders count
   * @private
   */
  async _getTokenHoldersCount(tokenSymbol) {
    // This would typically be a database query
    // For now, we'll return a placeholder value
    return 100; // Placeholder
  }
  
  /**
   * Get token trades count
   * @param {string} tokenSymbol - The token symbol
   * @returns {Promise<number>} Trades count
   * @private
   */
  async _getTokenTradesCount(tokenSymbol) {
    // This would typically be a database query
    // For now, we'll return a placeholder value
    return 200; // Placeholder
  }
  
  /**
   * Get token liquidity
   * @param {string} tokenSymbol - The token symbol
   * @returns {Promise<number>} Liquidity in SOL
   * @private
   */
  async _getTokenLiquidity(tokenSymbol) {
    // This would typically be a database query
    // For now, we'll return a placeholder value
    return 150; // Placeholder
  }
}

module.exports = new GraduationService();
