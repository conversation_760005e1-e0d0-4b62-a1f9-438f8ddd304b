use anchor_lang::prelude::*;
use anchor_spl::{
    associated_token::AssociatedToken,
    metadata::{
        create_metadata_accounts_v3,
        mpl_token_metadata::types::DataV2,
        CreateMetadataAccountsV3,
        Metadata as MetadataProgram
    },
    token::{Mint, Token, TokenAccount, mint_to, burn, transfer, MintTo, Burn, Transfer}
};
use std::convert::TryFrom;

mod state;
mod context;
mod error;
mod utils;

use state::*;
use context::*;
use error::ErrorCode as PlatformErrorCode; // Use specific import to avoid conflict
use utils::*;

declare_id!("Fg6PaFpoGXkYsidMpWTK6W2BeZ7FEfcYkg476zPFsLnS"); // Replace with your program ID

#[program]
pub mod meme_coin_platform {
    use super::*;

    // Initialize the platform
    pub fn initialize_platform(
        ctx: Context<InitializePlatform>,
        platform_fee_percent: u16,
        token_creation_fee: u64,
    ) -> Result<()> {
        require!(platform_fee_percent <= 1000, PlatformPlatformErrorCode::FeeTooHigh); // Max 10%

        let platform_state = &mut ctx.accounts.platform_state;
        platform_state.authority = ctx.accounts.authority.key();
        platform_state.platform_wallet = ctx.accounts.platform_wallet.key();
        platform_state.platform_fee_percent = platform_fee_percent;
        platform_state.token_creation_fee = 0; // Set to 0 - no platform creation fees
        platform_state.token_count = 0;
        platform_state.total_volume = 0;
        platform_state.total_fees_collected = 0;
        platform_state.is_active = true;

        msg!("Platform initialized with trading fee: {}% and creation fee: 0 lamports (only Solana network fees apply)",
            platform_fee_percent as f64 / 100.0);
        Ok(())
    }

    // Create a new token with linear bonding curve
    pub fn create_token(
        ctx: Context<CreateToken>,
        name: String,
        symbol: String,
        uri: String,
        description: String,
        initial_supply: u64,
        decimals: u8,
        base_price: u64,
        curve_slope: u64,
        creator_fee_percent: u16,
        reserve_ratio: u16,
        max_supply: Option<u64>,
        minimum_liquidity: u64,
    ) -> Result<()> {
        // Validate inputs
        require!(name.len() <= 32, PlatformErrorCode::NameTooLong);
        require!(symbol.len() <= 10, PlatformErrorCode::SymbolTooLong);
        require!(description.len() <= 200, PlatformErrorCode::DescriptionTooLong);
        require!(decimals <= 9, PlatformErrorCode::DecimalsTooLarge);
        require!(creator_fee_percent <= 500, PlatformErrorCode::FeeTooHigh); // Max 5%
        require!(reserve_ratio >= 100 && reserve_ratio <= 10000, PlatformErrorCode::InvalidReserveRatio); // 1% to 100%
        require!(initial_supply > 0, PlatformErrorCode::InvalidSupply);
        require!(base_price > 0, PlatformErrorCode::InvalidAmount);
        require!(curve_slope > 0, PlatformErrorCode::InvalidAmount);
        require!(minimum_liquidity > 0, PlatformErrorCode::InvalidAmount);

        // Get platform state
        let platform_state = &ctx.accounts.platform_state;

        // No platform token creation fee - only Solana network fees apply
        msg!("Token creation - no platform fees charged, only Solana network fees apply");

        // Create token metadata
        let token_data = DataV2 {
            name: name.clone(),
            symbol: symbol.clone(),
            uri: uri.clone(),
            seller_fee_basis_points: 0,
            creators: None,
            collection: None,
            uses: None,
        };

        // Create metadata account
        let metadata_ctx = CpiContext::new(
            ctx.accounts.token_metadata_program.to_account_info(),
            CreateMetadataAccountsV3 {
                metadata: ctx.accounts.metadata.to_account_info(),
                mint: ctx.accounts.mint.to_account_info(),
                mint_authority: ctx.accounts.authority.to_account_info(),
                payer: ctx.accounts.creator.to_account_info(),
                update_authority: ctx.accounts.creator.to_account_info(),
                system_program: ctx.accounts.system_program.to_account_info(),
                rent: ctx.accounts.rent.to_account_info(),
            },
        );

        create_metadata_accounts_v3(
            metadata_ctx,
            token_data,
            true,
            true,
            None,
        )?;

        // Initialize bonding curve
        let bonding_curve = &mut ctx.accounts.bonding_curve;
        bonding_curve.mint = ctx.accounts.mint.key();
        bonding_curve.authority = ctx.accounts.authority.key();
        bonding_curve.creator = ctx.accounts.creator.key();
        bonding_curve.curve_type = CurveType::Linear; // Always linear
        bonding_curve.base_price = base_price;
        bonding_curve.curve_slope = curve_slope;
        bonding_curve.total_supply = initial_supply;
        bonding_curve.reserve_balance = 0;
        bonding_curve.reserve_ratio = reserve_ratio;
        bonding_curve.creator_fee_percent = creator_fee_percent;
        bonding_curve.platform_fee_percent = platform_state.platform_fee_percent;
        bonding_curve.buy_count = 0;
        bonding_curve.sell_count = 0;
        bonding_curve.total_volume = 0;
        bonding_curve.is_active = true;
        bonding_curve.is_tradeable = false; // Not tradeable until liquidity is added
        bonding_curve.created_at = Clock::get()?.unix_timestamp;
        bonding_curve.name = name;
        bonding_curve.symbol = symbol;
        bonding_curve.description = description;
        bonding_curve.decimals = decimals;
        bonding_curve.max_supply = max_supply;
        bonding_curve.last_trade_timestamp = Clock::get()?.unix_timestamp;
        bonding_curve.market_volatility_flag = false;
        bonding_curve.minimum_liquidity = minimum_liquidity;

        // Mint initial supply to creator
        let seeds = &[
            b"authority".as_ref(),
            &[ctx.bumps.authority],
        ];
        let signer = &[&seeds[..]];

        let mint_to_ctx = CpiContext::new_with_signer(
            ctx.accounts.token_program.to_account_info(),
            MintTo {
                mint: ctx.accounts.mint.to_account_info(),
                to: ctx.accounts.creator_token_account.to_account_info(),
                authority: ctx.accounts.authority.to_account_info(),
            },
            signer,
        );

        mint_to(mint_to_ctx, initial_supply)?;

        // Update platform state
        let platform_state = &mut ctx.accounts.platform_state;
        platform_state.token_count = platform_state.token_count.checked_add(1).unwrap();
        // No token creation fees collected since they are set to 0

        msg!("Token created: {} - Trading will be enabled after adding liquidity", symbol);
        Ok(())
    }

    // Create a new token and add initial liquidity in one transaction
    pub fn create_token_with_liquidity(
        ctx: Context<CreateTokenWithLiquidity>,
        name: String,
        symbol: String,
        uri: String,
        description: String,
        initial_supply: u64,
        decimals: u8,
        base_price: u64,
        curve_slope: u64,
        creator_fee_percent: u16,
        reserve_ratio: u16,
        max_supply: Option<u64>,
        initial_liquidity: u64,
    ) -> Result<()> {
        // Validate inputs (same as create_token)
        require!(name.len() <= 32, PlatformErrorCode::NameTooLong);
        require!(symbol.len() <= 10, PlatformErrorCode::SymbolTooLong);
        require!(description.len() <= 200, PlatformErrorCode::DescriptionTooLong);
        require!(decimals <= 9, PlatformErrorCode::DecimalsTooLarge);
        require!(creator_fee_percent <= 500, PlatformErrorCode::FeeTooHigh);
        require!(reserve_ratio >= 100 && reserve_ratio <= 10000, PlatformErrorCode::InvalidReserveRatio);
        require!(initial_supply > 0, PlatformErrorCode::InvalidSupply);
        require!(base_price > 0, PlatformErrorCode::InvalidAmount);
        require!(curve_slope > 0, PlatformErrorCode::InvalidAmount);
        require!(initial_liquidity > 0, PlatformErrorCode::InvalidAmount);

        let platform_state = &ctx.accounts.platform_state;

        // Create token metadata
        let token_data = DataV2 {
            name: name.clone(),
            symbol: symbol.clone(),
            uri: uri.clone(),
            seller_fee_basis_points: 0,
            creators: None,
            collection: None,
            uses: None,
        };

        // Create metadata account
        let metadata_ctx = CpiContext::new(
            ctx.accounts.token_metadata_program.to_account_info(),
            CreateMetadataAccountsV3 {
                metadata: ctx.accounts.metadata.to_account_info(),
                mint: ctx.accounts.mint.to_account_info(),
                mint_authority: ctx.accounts.authority.to_account_info(),
                payer: ctx.accounts.creator.to_account_info(),
                update_authority: ctx.accounts.creator.to_account_info(),
                system_program: ctx.accounts.system_program.to_account_info(),
                rent: ctx.accounts.rent.to_account_info(),
            },
        );

        create_metadata_accounts_v3(metadata_ctx, token_data, true, true, None)?;

        // Initialize bonding curve
        let bonding_curve = &mut ctx.accounts.bonding_curve;
        bonding_curve.mint = ctx.accounts.mint.key();
        bonding_curve.authority = ctx.accounts.authority.key();
        bonding_curve.creator = ctx.accounts.creator.key();
        bonding_curve.curve_type = CurveType::Linear;
        bonding_curve.base_price = base_price;
        bonding_curve.curve_slope = curve_slope;
        bonding_curve.total_supply = initial_supply;
        bonding_curve.reserve_balance = initial_liquidity;
        bonding_curve.reserve_ratio = reserve_ratio;
        bonding_curve.creator_fee_percent = creator_fee_percent;
        bonding_curve.platform_fee_percent = platform_state.platform_fee_percent;
        bonding_curve.buy_count = 0;
        bonding_curve.sell_count = 0;
        bonding_curve.total_volume = 0;
        bonding_curve.is_active = true;
        bonding_curve.is_tradeable = true; // Immediately tradeable
        bonding_curve.created_at = Clock::get()?.unix_timestamp;
        bonding_curve.name = name;
        bonding_curve.symbol = symbol;
        bonding_curve.description = description;
        bonding_curve.decimals = decimals;
        bonding_curve.max_supply = max_supply;
        bonding_curve.last_trade_timestamp = Clock::get()?.unix_timestamp;
        bonding_curve.market_volatility_flag = false;
        bonding_curve.minimum_liquidity = initial_liquidity;

        // Mint initial supply to creator
        let seeds = &[b"authority".as_ref(), &[ctx.bumps.authority]];
        let signer = &[&seeds[..]];

        let mint_to_ctx = CpiContext::new_with_signer(
            ctx.accounts.token_program.to_account_info(),
            MintTo {
                mint: ctx.accounts.mint.to_account_info(),
                to: ctx.accounts.creator_token_account.to_account_info(),
                authority: ctx.accounts.authority.to_account_info(),
            },
            signer,
        );

        mint_to(mint_to_ctx, initial_supply)?;

        // Transfer initial liquidity to reserve
        let transfer_to_reserve_ix = anchor_lang::solana_program::system_instruction::transfer(
            &ctx.accounts.creator.key(),
            &ctx.accounts.reserve.key(),
            initial_liquidity,
        );

        anchor_lang::solana_program::program::invoke(
            &transfer_to_reserve_ix,
            &[
                ctx.accounts.creator.to_account_info(),
                ctx.accounts.reserve.to_account_info(),
                ctx.accounts.system_program.to_account_info(),
            ],
        )?;

        // Update platform state
        let platform_state = &mut ctx.accounts.platform_state;
        platform_state.token_count = platform_state.token_count.checked_add(1).unwrap();

        msg!("Token created with liquidity: {} - Trading enabled immediately", symbol);
        Ok(())
    }
    // Add initial liquidity to the bonding curve
    pub fn add_initial_liquidity(
        ctx: Context<AddLiquidity>,
        sol_amount: u64,
    ) -> Result<()> {
        require!(sol_amount > 0, PlatformErrorCode::InvalidAmount);

        let bonding_curve = &mut ctx.accounts.bonding_curve;
        require!(bonding_curve.is_active, PlatformErrorCode::CurveInactive);
        require!(bonding_curve.reserve_balance == 0, PlatformErrorCode::LiquidityAlreadyAdded);
        require!(bonding_curve.creator == ctx.accounts.liquidity_provider.key(), PlatformErrorCode::Unauthorized);
        require!(sol_amount >= bonding_curve.minimum_liquidity, PlatformErrorCode::InsufficientBalance);

        // Transfer SOL to reserve
        let transfer_to_reserve_ix = anchor_lang::solana_program::system_instruction::transfer(
            &ctx.accounts.liquidity_provider.key(),
            &ctx.accounts.reserve.key(),
            sol_amount,
        );

        anchor_lang::solana_program::program::invoke(
            &transfer_to_reserve_ix,
            &[
                ctx.accounts.liquidity_provider.to_account_info(),
                ctx.accounts.reserve.to_account_info(),
                ctx.accounts.system_program.to_account_info(),
            ],
        )?;

        // Update bonding curve state
        bonding_curve.reserve_balance = bonding_curve.reserve_balance.checked_add(sol_amount).unwrap();

        // Enable trading if minimum liquidity is met
        if bonding_curve.reserve_balance >= bonding_curve.minimum_liquidity {
            bonding_curve.is_tradeable = true;
        }

        msg!("Initial liquidity of {} lamports added - Trading enabled: {}",
            sol_amount, bonding_curve.is_tradeable);
        Ok(())
    }

    // Buy tokens using the bonding curve
    pub fn buy_tokens(
        ctx: Context<BuyTokens>,
        sol_amount: u64,
    ) -> Result<()> {
        require!(sol_amount > 0, PlatformErrorCode::InvalidAmount);

        let bonding_curve = &mut ctx.accounts.bonding_curve;
        require!(bonding_curve.is_active, PlatformErrorCode::CurveInactive);
        require!(bonding_curve.is_tradeable, PlatformErrorCode::InsufficientReserve);

        // CIRCUIT BREAKER 0: Check for market volatility protection
        require!(
            !bonding_curve.market_volatility_flag,
            PlatformErrorCode::MarketVolatilityProtection
        );

        // Update last trade timestamp
        bonding_curve.last_trade_timestamp = Clock::get()?.unix_timestamp;

        // Get current supply
        let current_supply = bonding_curve.total_supply;

        // CIRCUIT BREAKER 1: Check for maximum supply limit if defined
        if let Some(max_supply) = bonding_curve.max_supply {
            // Estimate new tokens that would be minted
            let (estimated_token_amount, _) = calculate_buy_amount(
                current_supply,
                sol_amount,
                bonding_curve.curve_type,
                bonding_curve.base_price,
                bonding_curve.curve_slope,
                bonding_curve.reserve_ratio,
            )?;

            // Check if this would exceed max supply
            require!(
                current_supply.checked_add(estimated_token_amount).unwrap_or(u64::MAX) <= max_supply,
                PlatformErrorCode::MaxSupplyExceeded
            );
        }

        // CIRCUIT BREAKER 2: Check price impact
        // Estimate token amount to calculate price impact
        let (estimated_token_amount, _) = calculate_buy_amount(
            current_supply,
            sol_amount,
            bonding_curve.curve_type,
            bonding_curve.base_price,
            bonding_curve.curve_slope,
            bonding_curve.reserve_ratio,
        )?;

        let price_impact = calculate_price_impact(
            current_supply,
            estimated_token_amount as i64,
            bonding_curve.curve_type,
            bonding_curve.base_price,
            bonding_curve.curve_slope,
        )?;

        // Prevent excessive price impact (max 20%)
        require!(
            price_impact <= 2000,
            PlatformErrorCode::PriceImpactTooHigh
        );

        // CIRCUIT BREAKER 3: Prevent excessive growth in a single transaction
        // Limit buy to 20% of current supply
        if current_supply > 0 {
            let max_buy_percentage = current_supply.checked_div(5).unwrap_or(u64::MAX); // 20%
            require!(
                estimated_token_amount <= max_buy_percentage,
                PlatformErrorCode::InvalidAmount
            );
        }

        // Calculate final token amount based on bonding curve
        let (token_amount, average_price) = calculate_buy_amount(
            current_supply,
            sol_amount,
            bonding_curve.curve_type,
            bonding_curve.base_price,
            bonding_curve.curve_slope,
            bonding_curve.reserve_ratio,
        )?;

        require!(token_amount > 0, PlatformErrorCode::InvalidCalculation);

        // Calculate fees
        let platform_fee = (sol_amount as u128)
            .checked_mul(bonding_curve.platform_fee_percent as u128)
            .unwrap()
            .checked_div(10000)
            .unwrap();

        let creator_fee = (sol_amount as u128)
            .checked_mul(bonding_curve.creator_fee_percent as u128)
            .unwrap()
            .checked_div(10000)
            .unwrap();

        let reserve_amount = sol_amount
            .checked_sub(u64::try_from(platform_fee).unwrap())
            .unwrap()
            .checked_sub(u64::try_from(creator_fee).unwrap())
            .unwrap();

        // Transfer SOL to reserve
        let transfer_to_reserve_ix = anchor_lang::solana_program::system_instruction::transfer(
            &ctx.accounts.buyer.key(),
            &ctx.accounts.reserve.key(),
            reserve_amount,
        );

        anchor_lang::solana_program::program::invoke(
            &transfer_to_reserve_ix,
            &[
                ctx.accounts.buyer.to_account_info(),
                ctx.accounts.reserve.to_account_info(),
                ctx.accounts.system_program.to_account_info(),
            ],
        )?;

        // Transfer platform fee
        if platform_fee > 0 {
            let transfer_platform_fee_ix = anchor_lang::solana_program::system_instruction::transfer(
                &ctx.accounts.buyer.key(),
                &ctx.accounts.platform_wallet.key(),
                u64::try_from(platform_fee).unwrap(),
            );

            anchor_lang::solana_program::program::invoke(
                &transfer_platform_fee_ix,
                &[
                    ctx.accounts.buyer.to_account_info(),
                    ctx.accounts.platform_wallet.to_account_info(),
                    ctx.accounts.system_program.to_account_info(),
                ],
            )?;
        }

        // Transfer creator fee
        if creator_fee > 0 {
            let transfer_creator_fee_ix = anchor_lang::solana_program::system_instruction::transfer(
                &ctx.accounts.buyer.key(),
                &ctx.accounts.creator.key(),
                u64::try_from(creator_fee).unwrap(),
            );

            anchor_lang::solana_program::program::invoke(
                &transfer_creator_fee_ix,
                &[
                    ctx.accounts.buyer.to_account_info(),
                    ctx.accounts.creator.to_account_info(),
                    ctx.accounts.system_program.to_account_info(),
                ],
            )?;
        }

        // Mint tokens to buyer
        let seeds = &[
            b"authority".as_ref(),
            &[ctx.bumps.authority],
        ];
        let signer = &[&seeds[..]];

        let mint_to_ctx = CpiContext::new_with_signer(
            ctx.accounts.token_program.to_account_info(),
            MintTo {
                mint: ctx.accounts.mint.to_account_info(),
                to: ctx.accounts.buyer_token_account.to_account_info(),
                authority: ctx.accounts.authority.to_account_info(),
            },
            signer,
        );

        mint_to(mint_to_ctx, token_amount)?;

        // Update bonding curve state
        bonding_curve.total_supply = bonding_curve.total_supply.checked_add(token_amount).unwrap();
        bonding_curve.reserve_balance = bonding_curve.reserve_balance.checked_add(reserve_amount).unwrap();
        bonding_curve.buy_count = bonding_curve.buy_count.checked_add(1).unwrap();
        bonding_curve.total_volume = bonding_curve.total_volume.checked_add(sol_amount).unwrap();

        // Update platform state
        let platform_state = &mut ctx.accounts.platform_state;
        platform_state.total_volume = platform_state.total_volume.checked_add(sol_amount).unwrap();
        platform_state.total_fees_collected = platform_state.total_fees_collected
            .checked_add(u64::try_from(platform_fee).unwrap())
            .unwrap();

        msg!("Bought {} tokens at average price of {} lamports",
            token_amount, average_price);

        Ok(())
    }
    // Sell tokens back to the bonding curve
    pub fn sell_tokens(
        ctx: Context<SellTokens>,
        token_amount: u64,
    ) -> Result<()> {
        require!(token_amount > 0, PlatformErrorCode::InvalidAmount);

        let bonding_curve = &mut ctx.accounts.bonding_curve;
        require!(bonding_curve.is_active, PlatformErrorCode::CurveInactive);
        require!(bonding_curve.is_tradeable, PlatformErrorCode::InsufficientReserve);

        // CIRCUIT BREAKER 0: Check for market volatility protection
        require!(
            !bonding_curve.market_volatility_flag,
            PlatformErrorCode::MarketVolatilityProtection
        );

        // Update last trade timestamp
        bonding_curve.last_trade_timestamp = Clock::get()?.unix_timestamp;

        // CIRCUIT BREAKER 1: Prevent selling more than 10% of supply at once
        let current_supply = bonding_curve.total_supply;
        let max_sell_percentage = current_supply.checked_div(10).unwrap_or(0);
        require!(
            token_amount <= max_sell_percentage,
            PlatformErrorCode::SellAmountTooLarge
        );

        // CIRCUIT BREAKER 2: Check reserve health
        let theoretical_reserve = calculate_theoretical_reserve(
            current_supply,
            bonding_curve.curve_type,
            bonding_curve.base_price,
            bonding_curve.curve_slope,
            bonding_curve.reserve_ratio,
        )?;

        let reserve_health = if theoretical_reserve > 0 {
            ((bonding_curve.reserve_balance as u128)
                .checked_mul(10000)
                .unwrap_or(0)
                .checked_div(theoretical_reserve as u128)
                .unwrap_or(0)) as u16
        } else {
            10000 // 100% if theoretical reserve is 0
        };

        // Require at least 30% reserve health
        require!(
            reserve_health >= 3000,
            PlatformErrorCode::ReserveHealthTooLow
        );

        // CIRCUIT BREAKER 3: Check price impact
        let price_impact = calculate_price_impact(
            current_supply,
            -(token_amount as i64),
            bonding_curve.curve_type,
            bonding_curve.base_price,
            bonding_curve.curve_slope,
        )?;

        // Prevent excessive price impact (max 20%)
        require!(
            price_impact <= 2000,
            PlatformErrorCode::PriceImpactTooHigh
        );

        // Calculate SOL amount based on bonding curve
        let (sol_amount, average_price) = calculate_sell_amount(
            current_supply,
            token_amount,
            bonding_curve.curve_type,
            bonding_curve.base_price,
            bonding_curve.curve_slope,
            bonding_curve.reserve_ratio,
        )?;

        require!(sol_amount > 0, PlatformErrorCode::InvalidCalculation);

        // Check if reserve has enough SOL
        require!(
            bonding_curve.reserve_balance >= sol_amount,
            PlatformErrorCode::InsufficientReserve
        );

        // Calculate fees
        let platform_fee = (sol_amount as u128)
            .checked_mul(bonding_curve.platform_fee_percent as u128)
            .unwrap()
            .checked_div(10000)
            .unwrap();

        let creator_fee = (sol_amount as u128)
            .checked_mul(bonding_curve.creator_fee_percent as u128)
            .unwrap()
            .checked_div(10000)
            .unwrap();

        let seller_amount = sol_amount
            .checked_sub(u64::try_from(platform_fee).unwrap())
            .unwrap()
            .checked_sub(u64::try_from(creator_fee).unwrap())
            .unwrap();

        // Burn tokens from seller
        let burn_ctx = CpiContext::new(
            ctx.accounts.token_program.to_account_info(),
            Burn {
                mint: ctx.accounts.mint.to_account_info(),
                from: ctx.accounts.seller_token_account.to_account_info(),
                authority: ctx.accounts.seller.to_account_info(),
            },
        );

        burn(burn_ctx, token_amount)?;

        // Transfer SOL from reserve to seller
        let seeds = &[
            b"reserve".as_ref(),
            bonding_curve.mint.as_ref(),
            &[ctx.bumps.reserve],
        ];
        let signer = &[&seeds[..]];

        let transfer_to_seller_ix = anchor_lang::solana_program::system_instruction::transfer(
            &ctx.accounts.reserve.key(),
            &ctx.accounts.seller.key(),
            seller_amount,
        );

        anchor_lang::solana_program::program::invoke_signed(
            &transfer_to_seller_ix,
            &[
                ctx.accounts.reserve.to_account_info(),
                ctx.accounts.seller.to_account_info(),
                ctx.accounts.system_program.to_account_info(),
            ],
            signer,
        )?;

        // Transfer platform fee
        if platform_fee > 0 {
            let transfer_platform_fee_ix = anchor_lang::solana_program::system_instruction::transfer(
                &ctx.accounts.reserve.key(),
                &ctx.accounts.platform_wallet.key(),
                u64::try_from(platform_fee).unwrap(),
            );

            anchor_lang::solana_program::program::invoke_signed(
                &transfer_platform_fee_ix,
                &[
                    ctx.accounts.reserve.to_account_info(),
                    ctx.accounts.platform_wallet.to_account_info(),
                    ctx.accounts.system_program.to_account_info(),
                ],
                signer,
            )?;
        }

        // Transfer creator fee
        if creator_fee > 0 {
            let transfer_creator_fee_ix = anchor_lang::solana_program::system_instruction::transfer(
                &ctx.accounts.reserve.key(),
                &ctx.accounts.creator.key(),
                u64::try_from(creator_fee).unwrap(),
            );

            anchor_lang::solana_program::program::invoke_signed(
                &transfer_creator_fee_ix,
                &[
                    ctx.accounts.reserve.to_account_info(),
                    ctx.accounts.creator.to_account_info(),
                    ctx.accounts.system_program.to_account_info(),
                ],
                signer,
            )?;
        }

        // Update bonding curve state
        bonding_curve.total_supply = bonding_curve.total_supply.checked_sub(token_amount).unwrap();
        bonding_curve.reserve_balance = bonding_curve.reserve_balance.checked_sub(sol_amount).unwrap();
        bonding_curve.sell_count = bonding_curve.sell_count.checked_add(1).unwrap();
        bonding_curve.total_volume = bonding_curve.total_volume.checked_add(sol_amount).unwrap();

        // Update platform state
        let platform_state = &mut ctx.accounts.platform_state;
        platform_state.total_volume = platform_state.total_volume.checked_add(sol_amount).unwrap();
        platform_state.total_fees_collected = platform_state.total_fees_collected
            .checked_add(u64::try_from(platform_fee).unwrap())
            .unwrap();

        msg!("Sold {} tokens at average price of {} lamports",
            token_amount, average_price);

        Ok(())
    }

    // Get current token price from the bonding curve
    pub fn get_token_price(
        ctx: Context<GetTokenPrice>,
    ) -> Result<u64> {
        let bonding_curve = &ctx.accounts.bonding_curve;
        let current_supply = bonding_curve.total_supply;

        let price = calculate_price(
            current_supply,
            bonding_curve.curve_type,
            bonding_curve.base_price,
            bonding_curve.curve_slope,
        )?;

        Ok(price)
    }

    // Update bonding curve parameters (only by creator)
    pub fn update_curve_params(
        ctx: Context<UpdateCurveParams>,
        new_creator_fee_percent: Option<u16>,
        new_curve_slope: Option<u64>,
        new_base_price: Option<u64>,
        new_description: Option<String>,
    ) -> Result<()> {
        let bonding_curve = &mut ctx.accounts.bonding_curve;

        // Validate inputs
        if let Some(fee) = new_creator_fee_percent {
            require!(fee <= 500, PlatformErrorCode::FeeTooHigh); // Max 5%
            bonding_curve.creator_fee_percent = fee;
        }

        if let Some(slope) = new_curve_slope {
            bonding_curve.curve_slope = slope;
        }

        if let Some(base) = new_base_price {
            bonding_curve.base_price = base;
        }

        if let Some(desc) = new_description {
            require!(desc.len() <= 200, PlatformErrorCode::DescriptionTooLong);
            bonding_curve.description = desc;
        }

        msg!("Bonding curve parameters updated");
        Ok(())
    }

    // Pause/unpause a bonding curve (only by creator or platform)
    pub fn toggle_curve_status(
        ctx: Context<ToggleCurveStatus>,
        is_active: bool,
    ) -> Result<()> {
        let bonding_curve = &mut ctx.accounts.bonding_curve;
        bonding_curve.is_active = is_active;

        msg!("Bonding curve {} {}",
            bonding_curve.symbol,
            if is_active { "activated" } else { "deactivated" });

        Ok(())
    }

    /// Update platform parameters (only by platform authority)
    ///
    /// This function allows the platform authority to update fee percentages,
    /// token creation fee, and the platform wallet address.
    ///
    /// # Parameters
    /// * `new_platform_fee_percent` - Optional new platform fee in basis points (1/100 of a percent)
    /// * `new_token_creation_fee` - Optional new token creation fee in lamports
    /// * `new_platform_wallet` - Optional new platform wallet address
    pub fn update_platform_params(
        ctx: Context<UpdatePlatformParams>,
        new_platform_fee_percent: Option<u16>,
        new_token_creation_fee: Option<u64>,
        new_platform_wallet: Option<Pubkey>,
    ) -> Result<()> {
        // Verify the caller is the platform authority
        let platform_state = &mut ctx.accounts.platform_state;
        require!(
            platform_state.authority == ctx.accounts.authority.key(),
            PlatformErrorCode::Unauthorized
        );

        // Update platform fee percentage if provided
        if let Some(fee) = new_platform_fee_percent {
            // Ensure fee is not too high (max 10%)
            require!(fee <= 1000, PlatformErrorCode::FeeTooHigh);
            platform_state.platform_fee_percent = fee;
            msg!("Platform fee updated to {}%", fee as f64 / 100.0);
        }

        // Update token creation fee if provided
        if let Some(creation_fee) = new_token_creation_fee {
            platform_state.token_creation_fee = creation_fee;
            msg!("Token creation fee updated to {} lamports", creation_fee);
        }

        // Update platform wallet if provided
        if let Some(wallet) = new_platform_wallet {
            platform_state.platform_wallet = wallet;
            msg!("Platform wallet updated to {}", wallet);
        }

        msg!("Platform parameters updated successfully");
        Ok(())
    }

    /// Withdraw platform fees (only by platform authority)
    ///
    /// This function allows the platform authority to withdraw collected fees
    /// from the platform wallet to a specified recipient.
    ///
    /// # Parameters
    /// * `amount` - Amount of lamports to withdraw
    pub fn withdraw_platform_fees(
        ctx: Context<WithdrawPlatformFees>,
        amount: u64,
    ) -> Result<()> {
        // Verify the caller is the platform authority
        let platform_state = &ctx.accounts.platform_state;
        require!(
            platform_state.authority == ctx.accounts.authority.key(),
            PlatformErrorCode::Unauthorized
        );

        // Verify the amount is not zero
        require!(amount > 0, PlatformErrorCode::InvalidAmount);

        // Get platform wallet balance
        let platform_wallet_balance = ctx.accounts.platform_wallet.lamports();

        // Verify the platform wallet has enough balance
        require!(
            platform_wallet_balance >= amount,
            PlatformErrorCode::InsufficientBalance
        );

        // Transfer SOL from platform wallet to recipient
        let transfer_ix = anchor_lang::solana_program::system_instruction::transfer(
            &ctx.accounts.platform_wallet.key(),
            &ctx.accounts.recipient.key(),
            amount,
        );

        anchor_lang::solana_program::program::invoke(
            &transfer_ix,
            &[
                ctx.accounts.platform_wallet.to_account_info(),
                ctx.accounts.recipient.to_account_info(),
                ctx.accounts.system_program.to_account_info(),
            ],
        )?;

        msg!("Withdrew {} lamports from platform wallet to {}",
            amount, ctx.accounts.recipient.key());

        Ok(())
    }

    /// Toggle market volatility protection
    ///
    /// This function allows the platform authority to enable or disable
    /// the market volatility protection flag for a specific token.
    /// When enabled, trading is temporarily paused to protect users.
    ///
    /// # Parameters
    /// * `is_volatile` - Whether to enable (true) or disable (false) the protection
    pub fn toggle_market_volatility(
        ctx: Context<ToggleMarketVolatility>,
        is_volatile: bool,
    ) -> Result<()> {
        // Verify the caller is the platform authority
        let platform_state = &ctx.accounts.platform_state;
        require!(
            platform_state.authority == ctx.accounts.authority.key(),
            PlatformErrorCode::Unauthorized
        );

        // Update the market volatility flag
        let bonding_curve = &mut ctx.accounts.bonding_curve;
        bonding_curve.market_volatility_flag = is_volatile;

        msg!("Market volatility protection for {} {}",
            bonding_curve.symbol,
            if is_volatile { "enabled" } else { "disabled" });

        Ok(())
    }

    /// Get reserve health metrics
    ///
    /// This function calculates and returns the health of a bonding curve's reserve.
    /// It provides information about the current reserve balance, theoretical reserve
    /// needed for full buyback, and the health percentage.
    ///
    /// # Returns
    /// * `current_reserve` - Current reserve balance in lamports
    /// * `theoretical_reserve` - Theoretical reserve needed for full buyback
    /// * `health_percentage` - Reserve health as a percentage (in basis points)
    pub fn get_reserve_health(
        ctx: Context<GetReserveHealth>,
    ) -> Result<(u64, u64, u16)> {
        let bonding_curve = &ctx.accounts.bonding_curve;

        // Current reserve
        let current_reserve = bonding_curve.reserve_balance;

        // Calculate theoretical reserve needed for full buyback
        let theoretical_reserve = calculate_theoretical_reserve(
            bonding_curve.total_supply,
            bonding_curve.curve_type,
            bonding_curve.base_price,
            bonding_curve.curve_slope,
            bonding_curve.reserve_ratio,
        )?;

        // Calculate health percentage (in basis points)
        let health_percentage = if theoretical_reserve > 0 {
            ((current_reserve as u128)
                .checked_mul(10000)
                .unwrap()
                .checked_div(theoretical_reserve as u128)
                .unwrap_or(0)) as u16
        } else {
            10000 // 100% if theoretical reserve is 0
        };

        // Return (current_reserve, theoretical_reserve, health_percentage)
        Ok((current_reserve, theoretical_reserve, health_percentage))
    }
}
