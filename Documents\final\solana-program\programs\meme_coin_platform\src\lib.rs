use anchor_lang::prelude::*;
use anchor_lang::solana_program;
use anchor_spl::{
    associated_token::AssociatedToken,
    token::{Mint, Token, TokenAccount, mint_to, MintTo}
};

declare_id!("5bupaxUMC3Rz2RK1zgrmBGxaqAz9hsrcAZLnZUSnHYvw");

#[program]
pub mod meme_coin_platform {
    use super::*;

    pub fn initialize_platform(
        ctx: Context<InitializePlatform>,
        platform_fee_percent: u16,
        token_creation_fee: u64,
    ) -> Result<()> {
        let platform_state = &mut ctx.accounts.platform_state;
        platform_state.authority = ctx.accounts.authority.key();
        platform_state.platform_wallet = ctx.accounts.platform_wallet.key();
        platform_state.platform_fee_percent = platform_fee_percent;
        platform_state.token_creation_fee = token_creation_fee;
        platform_state.token_count = 0;
        platform_state.total_volume = 0;
        platform_state.total_fees_collected = 0;
        platform_state.is_active = true;
        Ok(())
    }

    pub fn create_token(
        ctx: Context<CreateToken>,
        name: String,
        symbol: String,
        uri: String,
        initial_supply: u64,
        decimals: u8,
    ) -> Result<()> {
        // Validate inputs
        require!(name.len() <= 16, CustomError::NameTooLong);
        require!(symbol.len() <= 8, CustomError::SymbolTooLong);
        require!(decimals <= 9, CustomError::DecimalsTooLarge);
        require!(initial_supply > 0, CustomError::InvalidSupply);

        // Initialize bonding curve
        let bonding_curve = &mut ctx.accounts.bonding_curve;
        bonding_curve.mint = ctx.accounts.mint.key();
        bonding_curve.authority = ctx.accounts.authority.key();
        bonding_curve.creator = ctx.accounts.creator.key();

        // Copy strings to fixed arrays
        let mut name_bytes = [0u8; 16];
        let name_bytes_slice = name.as_bytes();
        name_bytes[..name_bytes_slice.len()].copy_from_slice(name_bytes_slice);
        bonding_curve.name = name_bytes;

        let mut symbol_bytes = [0u8; 8];
        let symbol_bytes_slice = symbol.as_bytes();
        symbol_bytes[..symbol_bytes_slice.len()].copy_from_slice(symbol_bytes_slice);
        bonding_curve.symbol = symbol_bytes;
        bonding_curve.decimals = decimals;
        bonding_curve.total_supply = initial_supply;
        bonding_curve.reserve_balance = 0;
        bonding_curve.base_price = 1000000; // 0.001 SOL
        bonding_curve.curve_slope = 1000;
        bonding_curve.is_active = true;
        bonding_curve.is_tradeable = false; // Will be true after liquidity is added
        bonding_curve.buy_count = 0;
        bonding_curve.sell_count = 0;
        bonding_curve.total_volume = 0;
        bonding_curve.created_at = Clock::get()?.unix_timestamp;

        // Mint initial supply to creator
        let cpi_accounts = MintTo {
            mint: ctx.accounts.mint.to_account_info(),
            to: ctx.accounts.creator_token_account.to_account_info(),
            authority: ctx.accounts.authority.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_ctx = CpiContext::new(cpi_program, cpi_accounts);
        
        mint_to(cpi_ctx, initial_supply)?;

        // Update platform stats
        let platform_state = &mut ctx.accounts.platform_state;
        platform_state.token_count += 1;

        Ok(())
    }

    pub fn buy_tokens(
        ctx: Context<BuyTokens>,
        sol_amount: u64,
    ) -> Result<()> {
        require!(sol_amount > 0, CustomError::InvalidAmount);

        let bonding_curve = &mut ctx.accounts.bonding_curve;
        require!(bonding_curve.is_active, CustomError::CurveInactive);
        require!(bonding_curve.is_tradeable, CustomError::InsufficientReserve);

        // Simple linear pricing: tokens = sol_amount / base_price
        let token_amount = sol_amount
            .checked_mul(10u64.pow(bonding_curve.decimals as u32))
            .unwrap()
            .checked_div(bonding_curve.base_price)
            .unwrap();

        require!(token_amount > 0, CustomError::InvalidCalculation);

        // Transfer SOL to reserve
        let ix = solana_program::system_instruction::transfer(
            &ctx.accounts.buyer.key(),
            &ctx.accounts.reserve.key(),
            sol_amount,
        );
        solana_program::program::invoke(
            &ix,
            &[
                ctx.accounts.buyer.to_account_info(),
                ctx.accounts.reserve.to_account_info(),
            ],
        )?;

        // Mint tokens to buyer
        let cpi_accounts = MintTo {
            mint: ctx.accounts.mint.to_account_info(),
            to: ctx.accounts.buyer_token_account.to_account_info(),
            authority: ctx.accounts.authority.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_ctx = CpiContext::new(cpi_program, cpi_accounts);
        
        mint_to(cpi_ctx, token_amount)?;

        // Update stats
        bonding_curve.total_supply += token_amount;
        bonding_curve.reserve_balance += sol_amount;
        bonding_curve.buy_count += 1;
        bonding_curve.total_volume += sol_amount;

        Ok(())
    }
}

#[derive(Accounts)]
pub struct InitializePlatform<'info> {
    #[account(
        init,
        payer = payer,
        space = 8 + 32 + 32 + 2 + 8 + 8 + 8 + 8 + 1,
        seeds = [b"platform_state"],
        bump
    )]
    pub platform_state: Account<'info, PlatformState>,
    
    pub authority: Signer<'info>,
    
    /// CHECK: Platform wallet for collecting fees
    pub platform_wallet: AccountInfo<'info>,
    
    #[account(mut)]
    pub payer: Signer<'info>,
    
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct CreateToken<'info> {
    #[account(mut)]
    pub platform_state: Account<'info, PlatformState>,
    
    #[account(
        init,
        payer = creator,
        space = 8 + 32 + 32 + 32 + 16 + 8 + 1 + 8 + 8 + 8 + 8 + 1 + 1 + 4 + 4 + 8 + 8,
        seeds = [b"bonding_curve", mint.key().as_ref()],
        bump
    )]
    pub bonding_curve: Account<'info, BondingCurve>,
    
    #[account(
        init,
        payer = creator,
        mint::decimals = 9,
        mint::authority = authority,
    )]
    pub mint: Account<'info, Mint>,
    
    /// CHECK: Authority PDA
    #[account(
        seeds = [b"authority"],
        bump
    )]
    pub authority: AccountInfo<'info>,
    
    #[account(mut)]
    pub creator: Signer<'info>,
    
    #[account(
        init_if_needed,
        payer = creator,
        associated_token::mint = mint,
        associated_token::authority = creator,
    )]
    pub creator_token_account: Account<'info, TokenAccount>,
    
    pub token_program: Program<'info, Token>,
    pub associated_token_program: Program<'info, AssociatedToken>,
    pub system_program: Program<'info, System>,
    pub rent: Sysvar<'info, Rent>,
}

#[derive(Accounts)]
pub struct BuyTokens<'info> {
    #[account(mut)]
    pub platform_state: Account<'info, PlatformState>,
    
    #[account(mut)]
    pub bonding_curve: Account<'info, BondingCurve>,
    
    #[account(mut)]
    pub mint: Account<'info, Mint>,
    
    /// CHECK: Reserve PDA
    #[account(
        mut,
        seeds = [b"reserve", mint.key().as_ref()],
        bump
    )]
    pub reserve: AccountInfo<'info>,
    
    /// CHECK: Authority PDA
    #[account(
        seeds = [b"authority"],
        bump
    )]
    pub authority: AccountInfo<'info>,
    
    #[account(mut)]
    pub buyer: Signer<'info>,
    
    #[account(
        init_if_needed,
        payer = buyer,
        associated_token::mint = mint,
        associated_token::authority = buyer,
    )]
    pub buyer_token_account: Account<'info, TokenAccount>,
    
    pub token_program: Program<'info, Token>,
    pub associated_token_program: Program<'info, AssociatedToken>,
    pub system_program: Program<'info, System>,
}

#[account]
pub struct PlatformState {
    pub authority: Pubkey,
    pub platform_wallet: Pubkey,
    pub platform_fee_percent: u16,
    pub token_creation_fee: u64,
    pub token_count: u64,
    pub total_volume: u64,
    pub total_fees_collected: u64,
    pub is_active: bool,
}

#[account]
pub struct BondingCurve {
    pub mint: Pubkey,                    // 32 bytes
    pub authority: Pubkey,               // 32 bytes
    pub creator: Pubkey,                 // 32 bytes
    pub name: [u8; 16],                 // 16 bytes (reduced from 32)
    pub symbol: [u8; 8],                // 8 bytes (reduced from 10)
    pub decimals: u8,                   // 1 byte
    pub total_supply: u64,              // 8 bytes
    pub reserve_balance: u64,           // 8 bytes
    pub base_price: u64,                // 8 bytes
    pub curve_slope: u64,               // 8 bytes
    pub is_active: bool,                // 1 byte
    pub is_tradeable: bool,             // 1 byte
    pub buy_count: u32,                 // 4 bytes (reduced from u64)
    pub sell_count: u32,                // 4 bytes (reduced from u64)
    pub total_volume: u64,              // 8 bytes
    pub created_at: i64,                // 8 bytes
    // Removed description field - will store off-chain
}

#[error_code]
pub enum CustomError {
    #[msg("Name too long - max 16 characters")]
    NameTooLong,
    #[msg("Symbol too long - max 8 characters")]
    SymbolTooLong,
    #[msg("Decimals too large")]
    DecimalsTooLarge,
    #[msg("Invalid supply")]
    InvalidSupply,
    #[msg("Invalid amount")]
    InvalidAmount,
    #[msg("Invalid calculation")]
    InvalidCalculation,
    #[msg("Curve inactive")]
    CurveInactive,
    #[msg("Insufficient reserve")]
    InsufficientReserve,
}
