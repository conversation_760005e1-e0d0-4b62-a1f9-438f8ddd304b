import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js';
export const merlin = /*#__PURE__*/ defineChain({
    id: 4200,
    name: '<PERSON>',
    nativeCurrency: {
        name: '<PERSON><PERSON>',
        symbol: 'BTC',
        decimals: 18,
    },
    rpcUrls: {
        default: { http: ['https://rpc.merlinchain.io'] },
    },
    blockExplorers: {
        default: {
            name: 'blockscout',
            url: 'https://scan.merlinchain.io',
            apiUrl: 'https://scan.merlinchain.io/api',
        },
    },
});
//# sourceMappingURL=merlin.js.map