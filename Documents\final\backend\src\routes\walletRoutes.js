/**
 * Wallet Routes
 * Handles API endpoints related to wallet operations
 */

const express = require('express');
const router = express.Router();
const walletController = require('../controllers/walletController');
const { authenticateToken } = require('../middleware/authMiddleware');
const platformWalletConfig = require('../config/platformWallet');
const solanaService = require('../services/solanaService');
const logger = require('../utils/logger');

// Get user wallets
router.get('/', authenticateToken, walletController.getUserWallets);

// Get wallet transactions
router.get('/transactions', authenticateToken, walletController.getWalletTransactions);

// Create a new transaction
router.post('/transactions', authenticateToken, walletController.createTransaction);

// Get platform wallet address
router.get('/platform-address', async (req, res) => {
  try {
    let platformWalletAddress;

    try {
      // First try to get from solanaService
      if (solanaService && typeof solanaService.getPlatformWalletAddress === 'function') {
        platformWalletAddress = solanaService.getPlatformWalletAddress();
        logger.info(`Using platform wallet address from solanaService: ${platformWalletAddress}`);
      }
      // Then try to get directly from platformWalletConfig
      else {
        platformWalletAddress = platformWalletConfig.getPlatformWalletAddress();
        logger.info(`Using platform wallet address from platformWalletConfig: ${platformWalletAddress}`);
      }
    } catch (walletError) {
      logger.warn(`Error getting platform wallet address: ${walletError.message}`);
      // Use fallback address if both methods fail
      platformWalletAddress = 'CVHXGtveLFr54yPsc4R6PgUhLLYCY5YQbXiqcLqEdkcu'; // Default fallback address
      logger.warn(`Using fallback platform wallet address: ${platformWalletAddress}`);
    }

    res.json({
      success: true,
      address: platformWalletAddress
    });
  } catch (error) {
    logger.error(`Error in platform-address endpoint: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Failed to get platform wallet address'
    });
  }
});

module.exports = router;
