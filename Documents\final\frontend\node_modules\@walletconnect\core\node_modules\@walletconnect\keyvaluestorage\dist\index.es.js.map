{"version": 3, "file": "index.es.js", "sources": ["../../../node_modules/unstorage/drivers/utils/index.mjs", "../../../node_modules/unstorage/drivers/indexedb.mjs", "../src/browser/lib/indexedDb.ts", "../src/browser/lib/localStoragePrototype.js", "../src/shared/utils.ts", "../src/browser/lib/localStore.ts", "../src/browser/lib/browserMigration.ts", "../src/browser/index.ts"], "sourcesContent": ["export function defineDriver(factory) {\n  return factory;\n}\nexport function normalize<PERSON>ey(key) {\n  if (!key) {\n    return \"\";\n  }\n  return key.replace(/[/\\\\]/g, \":\").replace(/^:|:$/g, \"\");\n}\nexport function joinKeys(...keys) {\n  return keys.map(normalizeKey).filter(Boolean).join(\":\");\n}\nexport function createError(driver, message, opts) {\n  const err = new Error(`[unstorage] [${driver}] ${message}`, opts);\n  return err;\n}\nexport function createRequiredError(driver, name) {\n  if (Array.isArray(name)) {\n    return createError(\n      driver,\n      `Missing some of the required options ${name.map((n) => \"`\" + n + \"`\").join(\", \")}`\n    );\n  }\n  return createError(driver, `Missing required option \\`${name}\\`.`);\n}\n", "import { defineDriver } from \"./utils/index.mjs\";\nimport {\n  get,\n  set,\n  clear,\n  del,\n  keys,\n  createStore\n} from \"idb-keyval\";\nconst DRIVER_NAME = \"idb-keyval\";\nexport default defineDriver((opts = {}) => {\n  const base = opts.base && opts.base.length > 0 ? `${opts.base}:` : \"\";\n  const makeKey = (key) => base + key;\n  let customStore;\n  if (opts.dbName && opts.storeName) {\n    customStore = createStore(opts.dbName, opts.storeName);\n  }\n  return {\n    name: DRIVER_NAME,\n    options: opts,\n    async hasItem(key) {\n      const item = await get(makeKey(key), customStore);\n      return typeof item === \"undefined\" ? false : true;\n    },\n    async getItem(key) {\n      const item = await get(makeKey(key), customStore);\n      return item ?? null;\n    },\n    setItem(key, value) {\n      return set(makeKey(key), value, customStore);\n    },\n    removeItem(key) {\n      return del(makeKey(key), customStore);\n    },\n    getKeys() {\n      return keys(customStore);\n    },\n    clear() {\n      return clear(customStore);\n    }\n  };\n});\n", "import { createStorage } from \"unstorage\";\nimport indexedDbDriver from \"unstorage/drivers/indexedb\";\n\nimport { IKeyValueStorage } from \"../../shared\";\nimport { safeJsonStringify } from \"@walletconnect/safe-json\";\n\nconst DB_NAME = \"WALLET_CONNECT_V2_INDEXED_DB\";\nconst DB_STORE_NAME = \"keyvaluestorage\";\n\nexport class IndexedDb implements IKeyValueStorage {\n  private indexedDb;\n  constructor() {\n    this.indexedDb = createStorage({\n      driver: indexedDbDriver({ dbName: DB_NAME, storeName: DB_STORE_NAME }),\n    });\n  }\n\n  public async getKeys(): Promise<string[]> {\n    return this.indexedDb.getKeys();\n  }\n\n  public async getEntries<T = any>(): Promise<[string, T][]> {\n    const entries = await this.indexedDb.getItems(await this.indexedDb.getKeys());\n    return entries.map((item: any) => [item.key, item.value] as [string, T]);\n  }\n\n  public async getItem<T = any>(key: string): Promise<T | undefined> {\n    const item = await this.indexedDb.getItem(key);\n    if (item === null) {\n      return undefined;\n    }\n    return item as T;\n  }\n\n  public async setItem<T = any>(key: string, value: T): Promise<void> {\n    await this.indexedDb.setItem(key, safeJsonStringify(value));\n  }\n\n  public async removeItem(key: string): Promise<void> {\n    await this.indexedDb.removeItem(key);\n  }\n}\n", "/**\n * Source: https://git.coolaj86.com/coolaj86/local-storage.js/src/branch/local-storage/lib/localStorage.js\n */\n\n/* eslint-disable */\n\n(function () {\n  \"use strict\";\n\n  let db;\n\n  function LocalStorage() {}\n  db = LocalStorage;\n\n  db.prototype.getItem = function (key) {\n    if (this.hasOwnProperty(key)) {\n      return String(this[key]);\n    }\n    return null;\n  };\n\n  db.prototype.setItem = function (key, val) {\n    this[key] = String(val);\n  };\n\n  db.prototype.removeItem = function (key) {\n    delete this[key];\n  };\n\n  db.prototype.clear = function () {\n    const self = this;\n    Object.keys(self).forEach(function (key) {\n      self[key] = undefined;\n      delete self[key];\n    });\n  };\n\n  db.prototype.key = function (i) {\n    i = i || 0;\n    return Object.keys(this)[i];\n  };\n\n  db.prototype.__defineGetter__(\"length\", function () {\n    return Object.keys(this).length;\n  });\n\n  if (typeof global !== \"undefined\" && global.localStorage) {\n    module.exports = global.localStorage;\n  } else if (typeof window !== \"undefined\" && window.localStorage) {\n    module.exports = window.localStorage;\n  } else {\n    module.exports = new LocalStorage();\n  }\n})();\n", "import { safeJsonParse } from \"@walletconnect/safe-json\";\n\nexport function parseEntry(entry: [string, string | null]): [string, any] {\n  return [entry[0], safeJsonParse(entry[1] ?? \"\")];\n}\n", "import { safeJsonParse, safeJsonStringify } from \"@walletconnect/safe-json\";\n// @ts-expect-error\nimport localStorage from \"./localStoragePrototype\";\nimport { IKeyValueStorage, parseEntry } from \"../../shared\";\n\nexport class LocalStore implements IKeyValueStorage {\n  private readonly localStorage: Storage = localStorage;\n\n  public async getKeys(): Promise<string[]> {\n    return Object.keys(this.localStorage);\n  }\n\n  public async getEntries<T = any>(): Promise<[string, T][]> {\n    return Object.entries(this.localStorage).map(parseEntry);\n  }\n\n  public async getItem<T = any>(key: string): Promise<T | undefined> {\n    const item = this.localStorage.getItem(key);\n    if (item === null) {\n      return undefined;\n    }\n    return safeJsonParse(item) as T;\n  }\n\n  public async setItem<T = any>(key: string, value: T): Promise<void> {\n    this.localStorage.setItem(key, safeJsonStringify(value));\n  }\n\n  public async removeItem(key: string): Promise<void> {\n    this.localStorage.removeItem(key);\n  }\n}\n", "import { IKeyValueStorage } from \"../../shared\";\n\nconst VERSION_KEY = \"wc_storage_version\";\nconst DB_VERSION = 1;\n\nexport const migrate = async (\n  fromStore: IKeyValueStorage,\n  toStore: IKeyValueStorage,\n  callback: (store: IKeyValueStorage) => void,\n) => {\n  const versionKey = VERSION_KEY;\n  const currentVersion = await toStore.getItem<number>(versionKey);\n  if (currentVersion && currentVersion >= DB_VERSION) {\n    callback(toStore);\n    return;\n  }\n  const keys = await fromStore.getKeys();\n  if (!keys.length) {\n    callback(toStore);\n    return;\n  }\n  const keysToCleanup: string[] = [];\n  while (keys.length) {\n    const key = keys.shift();\n    if (!key) continue;\n    const formattedKey = key.toLowerCase();\n    if (\n      formattedKey.includes(\"wc@\") ||\n      formattedKey.includes(\"walletconnect\") ||\n      formattedKey.includes(\"wc_\") ||\n      formattedKey.includes(\"wallet_connect\")\n    ) {\n      const item = await fromStore.getItem(key);\n      await toStore.setItem(key, item);\n      keysToCleanup.push(key);\n    }\n  }\n\n  await toStore.setItem(versionKey, DB_VERSION);\n  callback(toStore);\n  cleanup(fromStore, keysToCleanup);\n};\n\nconst cleanup = async (store: IKeyValueStorage, keysToCleanup: string[]) => {\n  if (!keysToCleanup.length) {\n    return;\n  }\n\n  keysToCleanup.forEach(async (key: string) => {\n    await store.removeItem(key);\n  });\n};\n", "import { IKeyValueStorage } from \"../shared\";\nimport { IndexedDb } from \"./lib/indexedDb\";\nimport { LocalStore } from \"./lib/localStore\";\nimport { migrate } from \"./lib/browserMigration\";\n\nexport class KeyValueStorage implements IKeyValueStorage {\n  private storage: IKeyValueStorage;\n  private initialized = false;\n\n  constructor() {\n    const local = new LocalStore();\n    this.storage = local;\n    try {\n      const indexed = new IndexedDb();\n      migrate(local, indexed, this.setInitialized);\n      // indexedDb isn't available in node env so this will throw\n    } catch (e) {\n      this.initialized = true;\n    }\n  }\n\n  private setInitialized = (store: IKeyValueStorage) => {\n    this.storage = store;\n    this.initialized = true;\n  };\n\n  public async getKeys(): Promise<string[]> {\n    await this.initialize();\n    return this.storage.getKeys();\n  }\n\n  public async getEntries<T = any>(): Promise<[string, T][]> {\n    await this.initialize();\n    return this.storage.getEntries();\n  }\n\n  public async getItem<T = any>(key: string): Promise<T | undefined> {\n    await this.initialize();\n    return this.storage.getItem(key);\n  }\n\n  public async setItem<T = any>(key: string, value: T): Promise<void> {\n    await this.initialize();\n    return this.storage.setItem(key, value);\n  }\n\n  public async removeItem(key: string): Promise<void> {\n    await this.initialize();\n    return this.storage.removeItem(key);\n  }\n\n  private async initialize() {\n    if (this.initialized) {\n      return;\n    }\n    await new Promise<void>((resolve) => {\n      const interval = setInterval(() => {\n        if (this.initialized) {\n          clearInterval(interval);\n          resolve();\n        }\n      }, 20);\n    });\n  }\n}\n\nexport default KeyValueStorage;\n"], "names": ["DB_NAME", "DB_STORE_NAME", "createStorage", "indexedDbDriver", "item", "key", "value", "safeJsonStringify", "db", "LocalStorage", "val", "self", "i", "global", "localStoragePrototypeModule", "entry", "_a", "safeJsonParse", "localStorage", "parseEntry", "VERSION_KEY", "DB_VERSION", "fromStore", "toStore", "callback", "version<PERSON>ey", "currentVersion", "keys", "keysToCleanup", "formattedKey", "cleanup", "store", "local", "LocalStore", "indexed", "IndexedDb", "migrate", "e", "resolve", "interval"], "mappings": ";;;;AAAO,SAAS,YAAY,CAAC,OAAO,EAAE;AACtC,EAAE,OAAO,OAAO,CAAC;AACjB;;ACOA,MAAM,WAAW,GAAG,YAAY,CAAC;AACjC,QAAe,YAAY,CAAC,CAAC,IAAI,GAAG,EAAE,KAAK;AAC3C,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACxE,EAAE,MAAM,OAAO,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC;AACtC,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE;AACrC,IAAI,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3D,GAAG;AACH,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,MAAM,OAAO,CAAC,GAAG,EAAE;AACvB,MAAM,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC;AACxD,MAAM,OAAO,OAAO,IAAI,KAAK,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC;AACxD,KAAK;AACL,IAAI,MAAM,OAAO,CAAC,GAAG,EAAE;AACvB,MAAM,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC;AACxD,MAAM,OAAO,IAAI,IAAI,IAAI,CAAC;AAC1B,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE;AACxB,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,UAAU,CAAC,GAAG,EAAE;AACpB,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,OAAO,GAAG;AACd,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC;AAC/B,KAAK;AACL,IAAI,KAAK,GAAG;AACZ,MAAM,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC;AAChC,KAAK;AACL,GAAG,CAAC;AACJ,CAAC,CAAC;;ACnCF,MAAMA,CAAAA,CAAU,+BACVC,CAAgB,CAAA,iBAAA,CAET,MAAA,SAAsC,CAEjD,WAAA,EAAc,CACZ,IAAA,CAAK,SAAYC,CAAAA,aAAAA,CAAc,CAC7B,MAAA,CAAQC,CAAgB,CAAA,CAAE,OAAQH,CAAS,CAAA,SAAA,CAAWC,CAAc,CAAC,CACvE,CAAC,EACH,CAEA,MAAa,OAA6B,EAAA,CACxC,OAAO,IAAA,CAAK,UAAU,OAAQ,EAChC,CAEA,MAAa,UAA8C,EAAA,CAEzD,OADgB,CAAA,MAAM,IAAK,CAAA,SAAA,CAAU,QAAS,CAAA,MAAM,IAAK,CAAA,SAAA,CAAU,SAAS,CAAA,EAC7D,GAAKG,CAAAA,CAAAA,EAAc,CAACA,CAAAA,CAAK,IAAKA,CAAK,CAAA,KAAK,CAAgB,CACzE,CAEA,MAAa,QAAiBC,CAAqC,CAAA,CACjE,MAAMD,CAAAA,CAAO,MAAM,IAAA,CAAK,SAAU,CAAA,OAAA,CAAQC,CAAG,CAAA,CAC7C,GAAID,CAAAA,GAAS,IAGb,CAAA,OAAOA,CACT,CAEA,MAAa,OAAiBC,CAAAA,CAAAA,CAAaC,CAAyB,CAAA,CAClE,MAAM,IAAK,CAAA,SAAA,CAAU,OAAQD,CAAAA,CAAAA,CAAKE,iBAAkBD,CAAAA,CAAK,CAAC,EAC5D,CAEA,MAAa,UAAA,CAAWD,CAA4B,CAAA,CAClD,MAAM,IAAA,CAAK,SAAU,CAAA,UAAA,CAAWA,CAAG,EACrC,CACF;;;;;;ACnCC,CAAA,UAAY,CAGX,IAAIG,CAEJ,CAAA,SAASC,GAAe,EACxBD,CAAKC,CAAAA,CAAAA,CAELD,CAAG,CAAA,SAAA,CAAU,OAAU,CAAA,SAAUH,EAAK,CACpC,OAAI,IAAK,CAAA,cAAA,CAAeA,CAAG,CAClB,CAAA,MAAA,CAAO,IAAKA,CAAAA,CAAG,CAAC,CAElB,CAAA,IACX,CAEEG,CAAAA,CAAAA,CAAG,SAAU,CAAA,OAAA,CAAU,SAAUH,CAAAA,CAAKK,EAAK,CACzC,IAAA,CAAKL,CAAG,CAAA,CAAI,OAAOK,CAAG,EAC1B,CAEEF,CAAAA,CAAAA,CAAG,UAAU,UAAa,CAAA,SAAUH,CAAK,CAAA,CACvC,OAAO,IAAKA,CAAAA,CAAG,EACnB,CAAA,CAEEG,EAAG,SAAU,CAAA,KAAA,CAAQ,UAAY,CAC/B,MAAMG,CAAO,CAAA,IAAA,CACb,MAAO,CAAA,IAAA,CAAKA,CAAI,CAAE,CAAA,OAAA,CAAQ,SAAUN,CAAAA,CAAK,CACvCM,CAAAA,CAAKN,CAAG,CAAA,CAAI,OACZ,OAAOM,CAAAA,CAAKN,CAAG,EACrB,CAAK,EACL,CAAA,CAEEG,CAAG,CAAA,SAAA,CAAU,IAAM,SAAUI,CAAAA,CAAG,CAC9B,OAAAA,CAAIA,CAAAA,CAAAA,EAAK,CACF,CAAA,MAAA,CAAO,KAAK,IAAI,CAAA,CAAEA,CAAC,CAC9B,EAEEJ,CAAG,CAAA,SAAA,CAAU,gBAAiB,CAAA,QAAA,CAAU,UAAY,CAClD,OAAO,MAAO,CAAA,IAAA,CAAK,IAAI,CAAA,CAAE,MAC7B,CAAG,EAEG,OAAOK,cAAW,EAAA,WAAA,EAAeA,cAAO,CAAA,YAAA,CAC1CC,qBAAc,CAAA,OAAA,CAAGD,cAAO,CAAA,YAAA,CACf,OAAO,MAAA,EAAW,aAAe,MAAO,CAAA,YAAA,CACjDC,qBAAc,CAAA,OAAA,CAAG,OAAO,YAExBA,CAAAA,qBAAAA,CAAc,OAAG,CAAA,IAAIL,EAEzB,CAAI,GAAA;;ACnDG,SAAS,WAAWM,CAA+C,CAAA,CAF1E,IAAAC,CAAAA,CAGE,OAAO,CAACD,EAAM,CAAC,CAAA,CAAGE,aAAcD,CAAAA,CAAAA,CAAAA,CAAAD,CAAM,CAAA,CAAC,IAAP,IAAAC,CAAAA,CAAAA,CAAY,EAAE,CAAC,CACjD;;ACCa,MAAA,UAAuC,CAA7C,WACL,EAAA,CAAA,IAAA,CAAiB,aAAwBE,8BAEzC,CAAA,MAAa,OAA6B,EAAA,CACxC,OAAO,MAAO,CAAA,IAAA,CAAK,KAAK,YAAY,CACtC,CAEA,MAAa,UAAA,EAA8C,CACzD,OAAO,OAAO,OAAQ,CAAA,IAAA,CAAK,YAAY,CAAE,CAAA,GAAA,CAAIC,UAAU,CACzD,CAEA,MAAa,OAAA,CAAiBd,EAAqC,CACjE,MAAMD,EAAO,IAAK,CAAA,YAAA,CAAa,QAAQC,CAAG,CAAA,CAC1C,GAAID,CAAAA,GAAS,KAGb,OAAOa,aAAAA,CAAcb,CAAI,CAC3B,CAEA,MAAa,OAAiBC,CAAAA,CAAAA,CAAaC,CAAyB,CAAA,CAClE,KAAK,YAAa,CAAA,OAAA,CAAQD,EAAKE,iBAAkBD,CAAAA,CAAK,CAAC,EACzD,CAEA,MAAa,UAAA,CAAWD,EAA4B,CAClD,IAAA,CAAK,aAAa,UAAWA,CAAAA,CAAG,EAClC,CACF;;AC7BA,MAAMe,CAAc,CAAA,oBAAA,CACdC,CAAa,CAAA,CAAA,CAEN,MAAA,OAAA,CAAU,MACrBC,CAAAA,CACAC,CACAC,CAAAA,CAAAA,GACG,CACH,MAAMC,EAAaL,CACbM,CAAAA,CAAAA,CAAiB,MAAMH,CAAAA,CAAQ,OAAgBE,CAAAA,CAAU,CAC/D,CAAA,GAAIC,CAAkBA,EAAAA,CAAAA,EAAkBL,CAAY,CAAA,CAClDG,CAASD,CAAAA,CAAO,CAChB,CAAA,MAAA,CAEF,MAAMI,CAAO,CAAA,MAAML,CAAU,CAAA,OAAA,EAC7B,CAAA,GAAI,CAACK,CAAAA,CAAK,MAAQ,CAAA,CAChBH,CAASD,CAAAA,CAAO,CAChB,CAAA,MAAA,CAEF,MAAMK,CAAAA,CAA0B,EAChC,CAAA,KAAOD,CAAK,CAAA,MAAA,EAAQ,CAClB,MAAMtB,CAAMsB,CAAAA,CAAAA,CAAK,OACjB,CAAA,GAAI,CAACtB,CAAAA,CAAK,SACV,MAAMwB,CAAexB,CAAAA,CAAAA,CAAI,aACzB,CAAA,GACEwB,CAAa,CAAA,QAAA,CAAS,KAAK,CAAA,EAC3BA,CAAa,CAAA,QAAA,CAAS,eAAe,CAAA,EACrCA,CAAa,CAAA,QAAA,CAAS,KAAK,CAAA,EAC3BA,CAAa,CAAA,QAAA,CAAS,gBAAgB,CACtC,CAAA,CACA,MAAMzB,CAAAA,CAAO,MAAMkB,CAAAA,CAAU,OAAQjB,CAAAA,CAAG,CACxC,CAAA,MAAMkB,CAAQ,CAAA,OAAA,CAAQlB,CAAKD,CAAAA,CAAI,CAC/BwB,CAAAA,CAAAA,CAAc,KAAKvB,CAAG,EAAA,CAAA,CAI1B,MAAMkB,CAAAA,CAAQ,OAAQE,CAAAA,CAAAA,CAAYJ,CAAU,CAAA,CAC5CG,CAASD,CAAAA,CAAO,CAChBO,CAAAA,CAAAA,CAAQR,CAAWM,CAAAA,CAAa,EAClC,CAAA,CAEA,MAAME,CAAU,CAAA,MAAOC,CAAyBH,CAAAA,CAAAA,GAA4B,CACrEA,CAAAA,CAAc,MAInBA,EAAAA,CAAAA,CAAc,QAAQ,MAAOvB,CAAAA,EAAgB,CAC3C,MAAM0B,CAAM,CAAA,UAAA,CAAW1B,CAAG,EAC5B,CAAC,EACH,CAAA;;AC9CO,MAAM,eAA4C,CAIvD,aAAc,CAFd,IAAA,CAAQ,WAAc,CAAA,CAAA,CAAA,CActB,KAAQ,cAAkB0B,CAAAA,CAAAA,EAA4B,CACpD,IAAA,CAAK,QAAUA,CACf,CAAA,IAAA,CAAK,WAAc,CAAA,CAAA,EACrB,EAdE,MAAMC,CAAAA,CAAQ,IAAIC,UAAAA,CAClB,KAAK,OAAUD,CAAAA,CAAAA,CACf,GAAI,CACF,MAAME,CAAU,CAAA,IAAIC,SACpBC,CAAAA,OAAAA,CAAQJ,EAAOE,CAAS,CAAA,IAAA,CAAK,cAAc,EAE7C,OAASG,CAAP,CAAA,CACA,IAAK,CAAA,WAAA,CAAc,GACrB,CACF,CAOA,MAAa,OAAA,EAA6B,CACxC,OAAM,MAAA,IAAA,CAAK,UAAW,EAAA,CACf,KAAK,OAAQ,CAAA,OAAA,EACtB,CAEA,MAAa,UAA8C,EAAA,CACzD,OAAM,MAAA,IAAA,CAAK,YACJ,CAAA,IAAA,CAAK,OAAQ,CAAA,UAAA,EACtB,CAEA,MAAa,OAAA,CAAiBhC,EAAqC,CACjE,OAAA,MAAM,IAAK,CAAA,UAAA,GACJ,IAAK,CAAA,OAAA,CAAQ,OAAQA,CAAAA,CAAG,CACjC,CAEA,MAAa,OAAiBA,CAAAA,CAAAA,CAAaC,EAAyB,CAClE,OAAA,MAAM,IAAK,CAAA,UAAA,GACJ,IAAK,CAAA,OAAA,CAAQ,OAAQD,CAAAA,CAAAA,CAAKC,CAAK,CACxC,CAEA,MAAa,UAAA,CAAWD,EAA4B,CAClD,OAAA,MAAM,IAAK,CAAA,UAAA,GACJ,IAAK,CAAA,OAAA,CAAQ,UAAWA,CAAAA,CAAG,CACpC,CAEA,MAAc,UAAa,EAAA,CACrB,KAAK,WAGT,EAAA,MAAM,IAAI,OAAA,CAAeiC,GAAY,CACnC,MAAMC,CAAW,CAAA,WAAA,CAAY,IAAM,CAC7B,IAAA,CAAK,WACP,GAAA,aAAA,CAAcA,CAAQ,CACtBD,CAAAA,CAAAA,EAEJ,EAAA,CAAA,CAAG,EAAE,EACP,CAAC,EACH,CACF;;;;"}