{"name": "whatwg-url", "version": "7.1.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "main": "lib/public-api.js", "files": ["lib/"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "jsdom/whatwg-url", "dependencies": {"lodash.sortby": "^4.7.0", "tr46": "^1.0.1", "webidl-conversions": "^4.0.2"}, "devDependencies": {"browserify": "^16.2.2", "domexception": "^1.0.1", "eslint": "^5.4.0", "got": "^9.2.2", "jest": "^23.5.0", "recast": "^0.15.3", "webidl2js": "^9.0.1"}, "scripts": {"build": "node scripts/transform.js && node scripts/convert-idl.js", "coverage": "jest --coverage", "lint": "eslint .", "prepublish": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js && node scripts/convert-idl.js", "build-live-viewer": "browserify lib/public-api.js --standalone whatwgURL > live-viewer/whatwg-url.js", "test": "jest"}, "jest": {"collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}}