Migration Log - 2025-04-19T16:37:01.198Z

Starting migration scripts

=== Running createBalanceRecords.js ===

Running script: C:\Users\<USER>\Documents\project V\wb-swap\backend\src\scripts\createBalanceRecords.js
STDERR: node:internal/modules/cjs/loader:1137
  throw err;
  ^

Error: Cannot find module 'C:\Users\<USER>\Documents\project'
    at Module._resolveFilename (node:internal/modules/cjs/loader:1134:15)
    at Module._load (node:internal/modules/cjs/loader:975:27)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49 {
  code: 'MODULE_NOT_FOUND',
  requireStack: []
}

Node.js v18.19.1
<PERSON>ript exited with code 1

=== Running migrateOrdersToUserIds.js ===

Running script: C:\Users\<USER>\Documents\project V\wb-swap\backend\src\scripts\migrateOrdersToUserIds.js
STDERR: node:internal/modules/cjs/loader:1137
  throw err;
  ^

Error: Cannot find module 'C:\Users\<USER>\Documents\project'
    at Module._resolveFilename (node:internal/modules/cjs/loader:1134:15)
    at Module._load (node:internal/modules/cjs/loader:975:27)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49 {
  code: 'MODULE_NOT_FOUND',
  requireStack: []
}

Node.js v18.19.1
Script exited with code 1

All migration scripts completed successfully
Log file written to: C:\Users\<USER>\Documents\project V\wb-swap\backend\src\scripts\migration_log.txt
