/**
 * Solana Service
 * Handles interactions with the Solana blockchain
 */

const {
  Connection,
  PublicKey,
  Keypair,
  Transaction,
  TransactionInstruction,
  SystemProgram,
  sendAndConfirmTransaction
} = require('@solana/web3.js');
const {
  createMint,
  getOrCreateAssociatedTokenAccount,
  mintTo,
  getAccount,
  TOKEN_PROGRAM_ID
} = require('@solana/spl-token');
const fs = require('fs');
const { IS_PROD, FEATURES, config, isFeatureEnabled } = require('../config/environment');
const platformWalletConfig = require('../config/platformWallet');
const BN = require('bn.js'); // For handling big numbers
const postgresqlService = require('./postgresqlService'); // PostgreSQL service for transaction data
// const balanceService = require('./balanceService'); // REMOVED - Balance system removed

// Class for Solana blockchain interactions
class SolanaService {
  constructor() {
    this.connection = null;
    this.platformWallet = null;
    this.initialized = false;
    this.isMockMode = false;
  }

  /**
   * Initialize the Solana service
   * @returns {Promise<boolean>} True if initialized successfully
   */
  async initialize() {
    try {
      console.log('Initializing Solana service...');

      // Check if already initialized
      if (this.initialized) {
        console.log('Solana service already initialized');
        return true;
      }

      // Get configuration
      const solanaConfig = config.solana;

      // Connect to Solana network
      const rpcUrl = solanaConfig.rpcUrl || 'https://api.devnet.solana.com';
      console.log(`Connecting to Solana network at ${rpcUrl}`);

      this.connection = new Connection(rpcUrl, 'confirmed');

      // Load or create platform wallet
      await this._initializePlatformWallet(solanaConfig);

      // Mark as initialized
      this.initialized = true;
      console.log('Solana service initialized successfully');

      return true;
    } catch (error) {
      console.error('Failed to initialize Solana service:', error);

      // In development, we can use mock mode
      if (!IS_PROD) {
        console.warn('Falling back to mock mode for Solana service');
        this.isMockMode = true;
        this.initialized = true;
        return true;
      }

      return false;
    }
  }

  /**
   * Initialize the platform wallet
   * @param {Object} config - Solana configuration
   * @returns {Promise<void>}
   * @private
   */
  async _initializePlatformWallet(config) {
    try {
      // Use the centralized platform wallet configuration
      const { keypair, publicKey } = platformWalletConfig.loadPlatformWallet();
      this.platformWallet = keypair;

      console.log(`Platform wallet initialized: ${publicKey}`);
    } catch (error) {
      console.error('Error initializing platform wallet:', error);
      throw error;
    }
  }

  /**
   * Ensure the service is initialized
   * @private
   */
  _ensureInitialized() {
    if (!this.initialized) {
      throw new Error('Solana service not initialized');
    }
  }

  /**
   * Get the platform wallet address
   * @returns {string} Platform wallet public key
   */
  getPlatformWalletAddress() {
    this._ensureInitialized();

    if (this.platformWallet) {
      return this.platformWallet.publicKey.toString();
    }

    // Fallback to the centralized configuration
    return platformWalletConfig.getPlatformWalletAddress();
  }

  /**
   * Process a deposit from the blockchain
   * @param {string} txId - Transaction ID
   * @param {string} fromAddress - Source address
   * @param {number} amount - Amount deposited
   * @param {string} token - Token symbol or mint address (default: 'SOL')
   * @param {string} memo - Optional memo to identify the user
   * @returns {Promise<Object>} Deposit details
   */
  async processDeposit(txId, fromAddress, amount, token = 'SOL', memo = '') {
    this._ensureInitialized();

    try {
      console.log(`Processing deposit: ${amount} ${token} from ${fromAddress}`);

      // In a real implementation, you would verify the transaction on the blockchain
      // For this demo, we'll just return the deposit details

      // Extract user ID from memo if available
      let userId = null;
      if (memo && memo.startsWith('DEPOSIT-')) {
        userId = memo.substring(8);
      }

      // If we have a user ID, update their balance
      if (userId) {
        try {
          // Process the deposit using the balance service
          await balanceService.processDeposit(userId, txId, parseFloat(amount), token);
          console.log(`Updated balance for user ${userId}: +${amount} ${token}`);
        } catch (balanceError) {
          console.error(`Error updating balance for user ${userId}:`, balanceError);
          // Continue despite balance error
        }
      }

      return {
        txId,
        fromAddress,
        amount: parseFloat(amount),
        token,
        timestamp: new Date(),
        userId,
        status: 'confirmed'
      };
    } catch (error) {
      console.error('Error processing deposit:', error);
      throw new Error(`Failed to process deposit: ${error.message}`);
    }
  }

  /**
   * Process a withdrawal to the blockchain
   * @param {string} userId - User ID
   * @param {string} toAddress - Destination address
   * @param {number} amount - Amount to withdraw
   * @param {string} token - Token symbol or mint address (default: 'SOL')
   * @returns {Promise<Object>} Withdrawal details
   */
  async processWithdrawal(userId, toAddress, amount, token = 'SOL') {
    this._ensureInitialized();

    try {
      console.log(`Processing withdrawal: ${amount} ${token} to ${toAddress} for user ${userId}`);

      // Validate amount
      if (!amount || amount <= 0) {
        throw new Error('Invalid withdrawal amount');
      }

      // Validate destination address
      if (!toAddress) {
        throw new Error('Destination address is required');
      }

      // Check if user has enough balance
      try {
        const userBalance = await balanceService.getUserBalance(userId);

        if (token === 'SOL') {
          if (parseFloat(userBalance.solBalance) < amount) {
            throw new Error(`Insufficient SOL balance. Available: ${userBalance.solBalance}, Requested: ${amount}`);
          }
        } else {
          const tokenBalances = userBalance.tokenBalances || {};
          const tokenBalance = parseFloat(tokenBalances[token] || '0');
          if (tokenBalance < amount) {
            throw new Error(`Insufficient ${token} balance. Available: ${tokenBalance}, Requested: ${amount}`);
          }
        }
      } catch (balanceError) {
        console.error(`Error checking balance for user ${userId}:`, balanceError);
        throw new Error(`Failed to check balance: ${balanceError.message}`);
      }

      // Process the withdrawal using the balance service
      try {
        await balanceService.processWithdrawal(userId, amount, toAddress, token);
        console.log(`Updated balance for user ${userId}: -${amount} ${token}`);
      } catch (balanceError) {
        console.error(`Error updating balance for user ${userId}:`, balanceError);
        throw new Error(`Failed to update balance: ${balanceError.message}`);
      }

      // In a real implementation, you would send a transaction on the blockchain
      // For this demo, we'll simulate the transaction

      // For SOL transfers
      if (token === 'SOL') {
        // Create a transaction
        const transaction = new Transaction().add(
          SystemProgram.transfer({
            fromPubkey: this.platformWallet.publicKey,
            toPubkey: new PublicKey(toAddress),
            lamports: Math.round(amount * 1e9) // Convert SOL to lamports
          })
        );

        // In development mode, just simulate the transaction
        if (!IS_PROD || this.isMockMode) {
          console.log('Simulating SOL withdrawal transaction');
          const txId = 'sim-tx-' + Date.now();

          return {
            txId,
            userId,
            toAddress,
            amount,
            token,
            timestamp: new Date(),
            status: 'completed',
            simulated: true
          };
        }

        // Sign and send the transaction
        const signature = await sendAndConfirmTransaction(
          this.connection,
          transaction,
          [this.platformWallet],
          { commitment: 'confirmed' }
        );

        console.log(`Withdrawal transaction sent: ${signature}`);

        return {
          txId: signature,
          userId,
          toAddress,
          amount,
          token,
          timestamp: new Date(),
          status: 'completed'
        };
      }

      // For token transfers (SPL tokens)
      // This would be implemented for token withdrawals
      throw new Error('Token withdrawals not implemented yet');
    } catch (error) {
      console.error('Error processing withdrawal:', error);
      throw new Error(`Failed to process withdrawal: ${error.message}`);
    }
  }
}

// Create and export singleton instance
const solanaService = new SolanaService();
module.exports = solanaService;
