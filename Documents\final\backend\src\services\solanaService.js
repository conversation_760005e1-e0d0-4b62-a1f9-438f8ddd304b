/**
 * Solana Service
 * Handles interactions with the Solana blockchain and Anchor programs
 */

const {
  Connection,
  PublicKey,
  Keypair,
  Transaction,
  TransactionInstruction,
  SystemProgram,
  LAMPORTS_PER_SOL
} = require('@solana/web3.js');
const {
  TOKEN_PROGRAM_ID,
  ASSOCIATED_TOKEN_PROGRAM_ID,
  getAssociatedTokenAddress,
  createAssociatedTokenAccountInstruction
} = require('@solana/spl-token');
const anchor = require('@coral-xyz/anchor');
const { AnchorProvider, Wallet, BN, Program } = anchor;
const { IS_PROD, FEATURES, config, isFeatureEnabled } = require('../config/environment');
const platformWalletConfig = require('../config/platformWallet');

// Load IDL and Program ID
const IDL = require('../config/meme_coin_platform.json');
const PROGRAM_ID = new PublicKey('5bupaxUMC3Rz2RK1zgrmBGxaqAz9hsrcAZLnZUSnHYvw');
const METADATA_PROGRAM_ID = new PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s');

// Class for Solana blockchain interactions
class SolanaService {
  constructor() {
    this.connection = null;
    this.platformWallet = null;
    this.program = null;
    this.programId = PROGRAM_ID;
    this.initialized = false;
    this.isMockMode = false;
  }

  /**
   * Initialize the Solana service
   * @returns {Promise<boolean>} True if initialized successfully
   */
  async initialize() {
    try {
      // Check if already initialized
      if (this.initialized) {
        return true;
      }

      // Get configuration
      const solanaConfig = config.solana;

      // Connect to Solana network
      const rpcUrl = solanaConfig.rpcUrl || 'https://api.devnet.solana.com';
      this.connection = new Connection(rpcUrl, 'confirmed');

      // Load or create platform wallet
      await this._initializePlatformWallet(solanaConfig);

      // Initialize Anchor program
      await this._initializeAnchorProgram();

      // Mark as initialized
      this.initialized = true;

      return true;
    } catch (error) {
      console.error('Failed to initialize Solana service:', error);

      // In development, we can use mock mode
      if (!IS_PROD) {
        console.warn('Falling back to mock mode for Solana service');
        this.isMockMode = true;
        this.initialized = true;
        return true;
      }

      return false;
    }
  }

  /**
   * Initialize the platform wallet
   * @param {Object} config - Solana configuration
   * @returns {Promise<void>}
   * @private
   */
  async _initializePlatformWallet(config) {
    try {
      // Use the centralized platform wallet configuration
      const { keypair, publicKey } = platformWalletConfig.loadPlatformWallet();
      this.platformWallet = keypair;

      console.log(`Platform wallet initialized: ${publicKey}`);
    } catch (error) {
      console.error('Error initializing platform wallet:', error);
      throw error;
    }
  }

  /**
   * Initialize the Anchor program
   * @returns {Promise<void>}
   * @private
   */
  async _initializeAnchorProgram() {
    try {
      // Create wallet and provider
      const wallet = new Wallet(this.platformWallet);
      const provider = new AnchorProvider(this.connection, wallet, {
        commitment: 'confirmed',
        preflightCommitment: 'confirmed',
      });

      // Initialize the Anchor program with IDL
      this.program = new Program(IDL, PROGRAM_ID, provider);
      this.provider = provider;

      console.log('✅ Anchor program initialized:', this.programId.toString());
      console.log('✅ Program methods available:', Object.keys(this.program.methods));
    } catch (error) {
      console.error('Error initializing Anchor program:', error);
      // Don't throw error, allow service to continue without program
    }
  }

  /**
   * Ensure the service is initialized
   * @private
   */
  _ensureInitialized() {
    if (!this.initialized) {
      throw new Error('Solana service not initialized');
    }
  }

  /**
   * Ensure the platform is initialized
   * @returns {Promise<void>}
   * @private
   */
  async _ensurePlatformInitialized() {
    try {
      const [platformState] = PublicKey.findProgramAddressSync(
        [Buffer.from('platform_state')],
        this.programId
      );

      // Check if platform state account exists
      const accountInfo = await this.connection.getAccountInfo(platformState);

      if (!accountInfo) {
        console.log('🔧 Platform not initialized, initializing now...');

        // Initialize the platform
        const tx = await this.program.methods
          .initializePlatform()
          .accounts({
            platformState,
            authority: this.platformWallet.publicKey,
            platformWallet: this.platformWallet.publicKey,
            systemProgram: SystemProgram.programId,
          })
          .signers([this.platformWallet])
          .rpc();

        console.log('✅ Platform initialized! Transaction:', tx);
      } else {
        console.log('✅ Platform already initialized');
      }
    } catch (error) {
      console.error('❌ Failed to ensure platform initialization:', error);
      throw error;
    }
  }

  /**
   * Get the platform wallet address
   * @returns {string} Platform wallet public key
   */
  getPlatformWalletAddress() {
    this._ensureInitialized();

    if (this.platformWallet) {
      return this.platformWallet.publicKey.toString();
    }

    // Fallback to the centralized configuration
    return platformWalletConfig.getPlatformWalletAddress();
  }

  /**
   * Get platform state PDA
   * @returns {PublicKey} Platform state PDA
   */
  getPlatformStatePDA() {
    const [platformState] = PublicKey.findProgramAddressSync(
      [Buffer.from('platform_state')],
      this.programId
    );
    return platformState;
  }

  /**
   * Get authority PDA
   * @returns {PublicKey} Authority PDA
   */
  getAuthorityPDA() {
    const [authority] = PublicKey.findProgramAddressSync(
      [Buffer.from('authority')],
      this.programId
    );
    return authority;
  }

  /**
   * Get bonding curve PDA
   * @param {PublicKey} mint - Token mint address
   * @returns {PublicKey} Bonding curve PDA
   */
  getBondingCurvePDA(mint) {
    const [bondingCurve] = PublicKey.findProgramAddressSync(
      [Buffer.from('bonding_curve'), mint.toBuffer()],
      this.programId
    );
    return bondingCurve;
  }

  /**
   * Get reserve PDA
   * @param {PublicKey} mint - Token mint address
   * @returns {PublicKey} Reserve PDA
   */
  getReservePDA(mint) {
    const [reserve] = PublicKey.findProgramAddressSync(
      [Buffer.from('reserve'), mint.toBuffer()],
      this.programId
    );
    return reserve;
  }

  /**
   * Get metadata PDA
   * @param {PublicKey} mint - Token mint address
   * @returns {PublicKey} Metadata PDA
   */
  getMetadataPDA(mint) {
    const [metadata] = PublicKey.findProgramAddressSync(
      [
        Buffer.from('metadata'),
        METADATA_PROGRAM_ID.toBuffer(),
        mint.toBuffer(),
      ],
      METADATA_PROGRAM_ID
    );
    return metadata;
  }

  /**
   * Initialize the platform (only needs to be done once)
   * @param {number} platformFeePercent - Platform fee percentage (in basis points)
   * @param {number} tokenCreationFee - Token creation fee in lamports
   * @returns {Promise<Object>} Result object
   */
  async initializePlatform(platformFeePercent = 100, tokenCreationFee = 0) {
    this._ensureInitialized();

    try {
      if (!this.program) {
        throw new Error('Anchor program not initialized');
      }

      const platformState = this.getPlatformStatePDA();

      const tx = await this.program.methods
        .initializePlatform(platformFeePercent, new BN(tokenCreationFee))
        .accounts({
          platformState,
          authority: this.platformWallet.publicKey,
          platformWallet: this.platformWallet.publicKey,
          payer: this.platformWallet.publicKey,
          systemProgram: SystemProgram.programId,
        })
        .signers([this.platformWallet])
        .rpc();

      console.log('Platform initialized. Transaction:', tx);
      return { success: true, transaction: tx };
    } catch (error) {
      console.error('Failed to initialize platform:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a new token transaction (for user to sign)
   * @param {Object} tokenData - Token creation parameters
   * @returns {Promise<Object>} Result object with transaction
   */
  async createTokenTransaction(tokenData) {
    this._ensureInitialized();

    try {
      const {
        name,
        symbol,
        initialSupply,
        decimals = 9,
        uri = '',
        creatorPublicKey
      } = tokenData;

      console.log('🚀 Creating real Solana token:', { name, symbol, initialSupply });

      if (!this.program) {
        throw new Error('Anchor program not initialized');
      }

      // First, ensure platform is initialized
      await this._ensurePlatformInitialized();

      // Generate new mint keypair
      const mintKeypair = Keypair.generate();
      const mint = mintKeypair.publicKey;

      // Calculate PDAs for your deployed program
      const [platformState] = PublicKey.findProgramAddressSync(
        [Buffer.from('platform_state')],
        this.programId
      );

      const [authority] = PublicKey.findProgramAddressSync(
        [Buffer.from('authority')],
        this.programId
      );

      const [bondingCurve] = PublicKey.findProgramAddressSync(
        [Buffer.from('bonding_curve'), mint.toBuffer()],
        this.programId
      );

      // Get creator's associated token account
      const creatorTokenAccount = await getAssociatedTokenAddress(
        mint,
        new PublicKey(creatorPublicKey)
      );

      // Use Anchor to build the transaction
      const tx = await this.program.methods
        .createToken(
          name,
          symbol,
          uri,
          new BN(initialSupply),
          decimals
        )
        .accounts({
          platformState,
          bondingCurve,
          mint,
          authority,
          creator: new PublicKey(creatorPublicKey),
          creatorTokenAccount,
          tokenProgram: TOKEN_PROGRAM_ID,
          associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
          systemProgram: SystemProgram.programId,
          rent: new PublicKey('SysvarRent111111111111111111111111111111111'),
        })
        .signers([mintKeypair])
        .transaction();

      // Get recent blockhash and set fee payer
      const { blockhash } = await this.connection.getLatestBlockhash();
      tx.recentBlockhash = blockhash;
      tx.feePayer = new PublicKey(creatorPublicKey);

      // Calculate transaction fee
      const fee = await this.connection.getFeeForMessage(tx.compileMessage());
      console.log('💰 Transaction fee:', fee.value / LAMPORTS_PER_SOL, 'SOL');

      // Check creator's balance
      const balance = await this.connection.getBalance(new PublicKey(creatorPublicKey));
      console.log('💳 Creator balance:', balance / LAMPORTS_PER_SOL, 'SOL');

      // If on devnet and balance is low, try to airdrop
      const isDevnet = this.connection.rpcEndpoint.includes('devnet');
      const minRequiredBalance = fee.value + (0.01 * LAMPORTS_PER_SOL); // Fee + 0.01 SOL buffer

      if (balance < minRequiredBalance) {
        if (isDevnet) {
          console.log('💧 Low balance detected, requesting devnet airdrop...');
          try {
            const airdropSignature = await this.connection.requestAirdrop(
              new PublicKey(creatorPublicKey),
              2 * LAMPORTS_PER_SOL // 2 SOL
            );
            await this.connection.confirmTransaction(airdropSignature);
            console.log('✅ Airdrop successful!');

            // Check balance again
            const newBalance = await this.connection.getBalance(new PublicKey(creatorPublicKey));
            console.log('💳 New balance after airdrop:', newBalance / LAMPORTS_PER_SOL, 'SOL');
          } catch (airdropError) {
            console.error('❌ Airdrop failed:', airdropError);
            return {
              success: false,
              error: `Insufficient SOL balance. Need ${fee.value / LAMPORTS_PER_SOL} SOL for transaction fees, but only have ${balance / LAMPORTS_PER_SOL} SOL. Please get devnet SOL from https://faucet.solana.com/`
            };
          }
        } else {
          return {
            success: false,
            error: `Insufficient SOL balance. Need ${fee.value / LAMPORTS_PER_SOL} SOL for transaction fees, but only have ${balance / LAMPORTS_PER_SOL} SOL.`
          };
        }
      }

      // Serialize transaction for frontend (already signed by mint keypair via Anchor)
      const serializedTransaction = tx.serialize({
        requireAllSignatures: false,
        verifySignatures: false
      });

      console.log('✅ Real Solana transaction created for mint:', mint.toString());
      return {
        success: true,
        transaction: serializedTransaction.toString('base64'),
        mint: mint.toString(),
        bondingCurve: bondingCurve.toString(),
        metadata: {
          name: name.substring(0, 16),
          symbol: symbol.substring(0, 8),
          initialSupply,
          decimals,
          uri
        }
      };
    } catch (error) {
      console.error('Failed to create token transaction:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Build instruction data for create_token instruction
   * @param {string} name - Token name (max 16 chars)
   * @param {string} symbol - Token symbol (max 8 chars)
   * @param {string} uri - Metadata URI
   * @param {BN} initialSupply - Initial token supply
   * @param {number} decimals - Token decimals
   * @returns {Buffer} Instruction data
   * @private
   */
  _buildCreateTokenInstructionData(name, symbol, uri, initialSupply, decimals) {
    const crypto = require('crypto');

    // Calculate Anchor instruction discriminator for "global:create_token"
    const hash = crypto.createHash('sha256').update('global:create_token').digest();
    const discriminator = hash.slice(0, 8);

    // Encode parameters
    const nameBuffer = Buffer.alloc(16);
    Buffer.from(name.substring(0, 16), 'utf8').copy(nameBuffer);

    const symbolBuffer = Buffer.alloc(8);
    Buffer.from(symbol.substring(0, 8), 'utf8').copy(symbolBuffer);

    const uriBuffer = Buffer.alloc(200); // Assuming max 200 chars for URI
    Buffer.from(uri.substring(0, 200), 'utf8').copy(uriBuffer);

    // Convert numbers to little-endian buffers
    const supplyBuffer = Buffer.alloc(8);
    supplyBuffer.writeBigUInt64LE(BigInt(initialSupply.toString()), 0);

    const decimalsBuffer = Buffer.alloc(1);
    decimalsBuffer.writeUInt8(decimals, 0);

    // Combine all data
    return Buffer.concat([
      discriminator,
      nameBuffer,
      symbolBuffer,
      uriBuffer,
      supplyBuffer,
      decimalsBuffer
    ]);
  }

  /**
   * Create a new token (legacy method - executes transaction)
   * @param {Object} tokenData - Token creation parameters
   * @returns {Promise<Object>} Result object
   */
  async createToken(tokenData) {
    this._ensureInitialized();

    try {
      if (!this.program) {
        throw new Error('Anchor program not initialized');
      }

      const {
        name,
        symbol,
        description,
        initialSupply,
        decimals = 9,
        basePrice = 1000000, // 0.001 SOL in lamports
        curveSlope = 1000,
        creatorFeePercent = 100, // 1%
        reserveRatio = 5000, // 50%
        maxSupply = null,
        minimumLiquidity = *********, // 0.1 SOL
        uri = '',
        creatorPublicKey
      } = tokenData;

      // Generate new mint keypair
      const mintKeypair = Keypair.generate();
      const mint = mintKeypair.publicKey;

      // Get PDAs
      const platformState = this.getPlatformStatePDA();
      const authority = this.getAuthorityPDA();
      const bondingCurve = this.getBondingCurvePDA(mint);
      const metadata = this.getMetadataPDA(mint);

      // Get creator's associated token account
      const creatorTokenAccount = await getAssociatedTokenAddress(
        mint,
        new PublicKey(creatorPublicKey)
      );

      const tx = await this.program.methods
        .createToken(
          name,
          symbol,
          uri,
          description,
          new BN(initialSupply),
          decimals,
          new BN(basePrice),
          new BN(curveSlope),
          creatorFeePercent,
          reserveRatio,
          maxSupply ? new BN(maxSupply) : null,
          new BN(minimumLiquidity)
        )
        .accounts({
          platformState,
          bondingCurve,
          mint,
          metadata,
          authority,
          creator: new PublicKey(creatorPublicKey),
          creatorTokenAccount,
          tokenProgram: TOKEN_PROGRAM_ID,
          tokenMetadataProgram: METADATA_PROGRAM_ID,
          associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
          systemProgram: SystemProgram.programId,
          rent: new PublicKey('SysvarRent111111111111111111111111111111111'),
        })
        .preInstructions([
          // Create associated token account if it doesn't exist
          createAssociatedTokenAccountInstruction(
            new PublicKey(creatorPublicKey),
            creatorTokenAccount,
            new PublicKey(creatorPublicKey),
            mint
          )
        ])
        .signers([mintKeypair])
        .rpc();

      console.log('Token created. Transaction:', tx);
      return {
        success: true,
        transaction: tx,
        mint: mint.toString(),
        bondingCurve: bondingCurve.toString(),
        metadata: {
          name,
          symbol,
          description,
          initialSupply,
          decimals,
          basePrice,
          curveSlope,
          creatorFeePercent,
          reserveRatio,
          maxSupply,
          minimumLiquidity
        }
      };
    } catch (error) {
      console.error('Failed to create token:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Buy tokens using the bonding curve
   * @param {string} mint - Token mint address
   * @param {number} solAmount - Amount of SOL to spend (in lamports)
   * @param {string} buyerPublicKey - Buyer's public key
   * @returns {Promise<Object>} Result object
   */
  async buyTokens(mint, solAmount, buyerPublicKey) {
    this._ensureInitialized();

    try {
      if (!this.program) {
        throw new Error('Anchor program not initialized');
      }

      const mintPubkey = new PublicKey(mint);
      const buyerPubkey = new PublicKey(buyerPublicKey);

      const platformState = this.getPlatformStatePDA();
      const authority = this.getAuthorityPDA();
      const bondingCurve = this.getBondingCurvePDA(mintPubkey);
      const reserve = this.getReservePDA(mintPubkey);

      // Get buyer's associated token account
      const buyerTokenAccount = await getAssociatedTokenAddress(
        mintPubkey,
        buyerPubkey
      );

      // Get bonding curve data to find creator
      const bondingCurveData = await this.program.account.bondingCurve.fetch(bondingCurve);
      const creator = bondingCurveData.creator;

      const tx = await this.program.methods
        .buyTokens(new BN(solAmount))
        .accounts({
          platformState,
          bondingCurve,
          mint: mintPubkey,
          reserve,
          authority,
          buyer: buyerPubkey,
          buyerTokenAccount,
          creator,
          platformWallet: this.platformWallet.publicKey,
          tokenProgram: TOKEN_PROGRAM_ID,
          systemProgram: SystemProgram.programId,
        })
        .preInstructions([
          // Create associated token account if it doesn't exist
          createAssociatedTokenAccountInstruction(
            buyerPubkey,
            buyerTokenAccount,
            buyerPubkey,
            mintPubkey
          )
        ])
        .rpc();

      console.log('Tokens bought. Transaction:', tx);
      return { success: true, transaction: tx };
    } catch (error) {
      console.error('Failed to buy tokens:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get token price from bonding curve
   * @param {string} mint - Token mint address
   * @returns {Promise<Object>} Result object with price
   */
  async getTokenPrice(mint) {
    this._ensureInitialized();

    try {
      if (!this.program) {
        throw new Error('Anchor program not initialized');
      }

      const bondingCurve = this.getBondingCurvePDA(new PublicKey(mint));

      const price = await this.program.methods
        .getTokenPrice()
        .accounts({
          bondingCurve,
        })
        .view();

      return { success: true, price: price.toString() };
    } catch (error) {
      console.error('Failed to get token price:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get bonding curve data
   * @param {string} mint - Token mint address
   * @returns {Promise<Object>} Result object with bonding curve data
   */
  async getBondingCurveData(mint) {
    this._ensureInitialized();

    try {
      if (!this.program) {
        throw new Error('Anchor program not initialized');
      }

      const bondingCurve = this.getBondingCurvePDA(new PublicKey(mint));
      const data = await this.program.account.bondingCurve.fetch(bondingCurve);

      return {
        success: true,
        data: {
          mint: data.mint.toString(),
          creator: data.creator.toString(),
          name: data.name,
          symbol: data.symbol,
          description: data.description,
          totalSupply: data.totalSupply.toString(),
          reserveBalance: data.reserveBalance.toString(),
          basePrice: data.basePrice.toString(),
          curveSlope: data.curveSlope.toString(),
          isActive: data.isActive,
          isTradeable: data.isTradeable,
          buyCount: data.buyCount.toString(),
          sellCount: data.sellCount.toString(),
          totalVolume: data.totalVolume.toString(),
          createdAt: data.createdAt.toString(),
        }
      };
    } catch (error) {
      console.error('Failed to get bonding curve data:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate a new keypair for users
   * @returns {Object} Object with publicKey and secretKey
   */
  generateKeypair() {
    const keypair = Keypair.generate();
    return {
      publicKey: keypair.publicKey.toString(),
      secretKey: Array.from(keypair.secretKey)
    };
  }

  /**
   * Get SOL balance
   * @param {string} publicKey - Public key to check balance for
   * @returns {Promise<Object>} Result object with balance
   */
  async getBalance(publicKey) {
    this._ensureInitialized();

    try {
      const balance = await this.connection.getBalance(new PublicKey(publicKey));
      return { success: true, balance: balance / LAMPORTS_PER_SOL };
    } catch (error) {
      console.error('Failed to get balance:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Airdrop SOL (devnet only)
   * @param {string} publicKey - Public key to airdrop to
   * @param {number} amount - Amount of SOL to airdrop
   * @returns {Promise<Object>} Result object
   */
  async airdrop(publicKey, amount = 1) {
    this._ensureInitialized();

    try {
      const network = process.env.SOLANA_NETWORK || 'devnet';
      if (network !== 'devnet') {
        throw new Error('Airdrop only available on devnet');
      }

      const signature = await this.connection.requestAirdrop(
        new PublicKey(publicKey),
        amount * LAMPORTS_PER_SOL
      );

      await this.connection.confirmTransaction(signature);
      return { success: true, signature };
    } catch (error) {
      console.error('Failed to airdrop:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get all tokens created on the platform
   * @returns {Promise<Object>} Result object with tokens list
   */
  async getAllTokens() {
    this._ensureInitialized();

    try {
      if (!this.program) {
        throw new Error('Anchor program not initialized');
      }

      // Get all bonding curve accounts
      const bondingCurves = await this.program.account.bondingCurve.all();

      const tokens = bondingCurves.map(curve => ({
        mint: curve.account.mint.toString(),
        creator: curve.account.creator.toString(),
        name: curve.account.name,
        symbol: curve.account.symbol,
        description: curve.account.description,
        totalSupply: curve.account.totalSupply.toString(),
        reserveBalance: curve.account.reserveBalance.toString(),
        basePrice: curve.account.basePrice.toString(),
        isActive: curve.account.isActive,
        isTradeable: curve.account.isTradeable,
        buyCount: curve.account.buyCount.toString(),
        sellCount: curve.account.sellCount.toString(),
        totalVolume: curve.account.totalVolume.toString(),
        createdAt: curve.account.createdAt.toString(),
        publicKey: curve.publicKey.toString()
      }));

      return { success: true, tokens };
    } catch (error) {
      console.error('Failed to get all tokens:', error);
      return { success: false, error: error.message };
    }
  }
}

// Create and export singleton instance
const solanaService = new SolanaService();
module.exports = solanaService;
