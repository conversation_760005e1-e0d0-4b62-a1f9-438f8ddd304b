/**
 * Wallet Controller
 * Handles wallet-related operations
 */

const User = require('../models/User');
const Transaction = require('../models/Transaction');
const { getErrorResponse } = require('../utils/responseUtils');

/**
 * Get user wallets
 * @route GET /api/wallets
 * @access Private
 */
async function getUserWallets(req, res) {
  try {
    const userId = req.user.id;

    // Get user from database
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prepare wallets array
    const wallets = [];

    // Add Solana wallet if exists
    if (user.walletAddress) {
      wallets.push({
        id: 'solana-main',
        name: 'Solana Wallet',
        type: 'SOL',
        address: user.walletAddress,
        balance: user.testWallet?.balance ? `${user.testWallet.balance} SOL` : '0 SOL',
        value: user.testWallet?.balance ? `$${(user.testWallet.balance * 133.75).toFixed(2)}` : '$0.00',
        default: true,
        isTestWallet: user.testWallet?.isTestWallet || false
      });
    }

    // Return wallets
    return res.json({
      success: true,
      wallets
    });
  } catch (error) {
    console.error('Error fetching user wallets:', error);
    return getErrorResponse(res, 'Failed to fetch user wallets', error);
  }
}

/**
 * Get wallet transactions
 * @route GET /api/wallets/transactions
 * @access Private
 */
async function getWalletTransactions(req, res) {
  try {
    const userId = req.user.id;
    const limit = parseInt(req.query.limit) || 10;
    const page = parseInt(req.query.page) || 1;
    const skip = (page - 1) * limit;

    // Get transactions from database
    const transactions = await Transaction.find({ userId })
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limit);

    // Count total transactions
    const total = await Transaction.countDocuments({ userId });

    // Format transactions for frontend
    const formattedTransactions = transactions.map(tx => ({
      id: tx._id,
      type: tx.type.charAt(0).toUpperCase() + tx.type.slice(1), // Capitalize first letter
      wallet: 'Solana Wallet',
      amount: `${tx.type === 'buy' || tx.type === 'deposit' ? '+' : '-'}${tx.amount.toFixed(4)} ${tx.tokenSymbol || 'SOL'}`,
      value: `${tx.type === 'buy' || tx.type === 'deposit' ? '+' : '-'}$${tx.total.toFixed(2)}`,
      date: tx.timestamp.toLocaleString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }),
      status: tx.status.charAt(0).toUpperCase() + tx.status.slice(1), // Capitalize first letter
      txHash: tx.signature ? tx.signature.substring(0, 4) + '...' + tx.signature.substring(tx.signature.length - 4) : 'N/A'
    }));

    // Return transactions with pagination
    return res.json({
      success: true,
      transactions: formattedTransactions,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching wallet transactions:', error);
    return getErrorResponse(res, 'Failed to fetch wallet transactions', error);
  }
}

/**
 * Create a new transaction
 * @route POST /api/wallets/transactions
 * @access Private
 */
async function createTransaction(req, res) {
  try {
    const userId = req.user.id;
    const { type, tokenSymbol, amount, price, total, status = 'completed', signature } = req.body;

    // Validate required fields
    if (!type || !tokenSymbol || !amount || !price || !total) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Create new transaction
    const transaction = new Transaction({
      userId,
      type,
      tokenSymbol,
      amount: parseFloat(amount),
      price: parseFloat(price),
      total: parseFloat(total),
      status,
      signature
    });

    // Save transaction
    await transaction.save();

    // Update user's wallet balance if it's a test wallet
    if (status === 'completed') {
      const user = await User.findById(userId);
      if (user && user.testWallet && user.testWallet.isTestWallet && tokenSymbol === 'SOL') {
        // Update balance based on transaction type
        if (type === 'buy' || type === 'deposit') {
          user.testWallet.balance += parseFloat(amount);
        } else if (type === 'sell' || type === 'withdraw') {
          user.testWallet.balance -= parseFloat(amount);
        }
        await user.save();
      }
    }

    // Return created transaction
    return res.status(201).json({
      success: true,
      transaction: {
        id: transaction._id,
        type: transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1),
        wallet: 'Solana Wallet',
        amount: `${transaction.type === 'buy' || transaction.type === 'deposit' ? '+' : '-'}${transaction.amount.toFixed(4)} ${transaction.tokenSymbol}`,
        value: `${transaction.type === 'buy' || transaction.type === 'deposit' ? '+' : '-'}$${transaction.total.toFixed(2)}`,
        date: transaction.timestamp.toLocaleString('en-US', { 
          year: 'numeric', 
          month: 'short', 
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }),
        status: transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1),
        txHash: transaction.signature ? transaction.signature.substring(0, 4) + '...' + transaction.signature.substring(transaction.signature.length - 4) : 'N/A'
      }
    });
  } catch (error) {
    console.error('Error creating transaction:', error);
    return getErrorResponse(res, 'Failed to create transaction', error);
  }
}

module.exports = {
  getUserWallets,
  getWalletTransactions,
  createTransaction
};
