{"name": "@walletconnect/solana-adapter", "version": "0.0.8", "license": "Apache-2.0", "type": "module", "main": "./dist/adapter.js", "types": "./dist/types/adapter.d.ts", "exports": {".": {"types": "./dist/types/adapter.d.ts", "import": "./dist/adapter.js", "default": "./dist/adapter.js"}, "./core": {"types": "./dist/types/core.d.ts", "import": "./dist/core.js", "default": "./dist/core.js"}}, "files": ["dist", "src", "LICENSE"], "publishConfig": {"access": "public"}, "devDependencies": {"@solana/wallet-adapter-base": "^0.9.23", "@solana/web3.js": "1.98.0", "@types/node": "22.14.1", "@walletconnect/types": "2.19.0", "typescript": "^5.5.2"}, "dependencies": {"@reown/appkit": "1.7.2", "@walletconnect/universal-provider": "2.19.0", "@walletconnect/utils": "2.19.0", "bs58": "6.0.0"}, "peerDependencies": {"@solana/wallet-adapter-base": "0.x", "@solana/web3.js": "1.x"}, "repository": {"type": "git", "url": "https://github.com/WalletConnect/walletconnect-solana-adapter"}, "scripts": {"clean": "rm -rf dist", "watch": "tsc --watch", "build": "pnpm run clean && tsc"}}