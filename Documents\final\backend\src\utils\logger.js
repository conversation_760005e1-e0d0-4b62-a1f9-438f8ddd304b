/**
 * Logger utility for consistent logging across the application
 * Supports different log levels and formats
 */

// Log levels
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
  TRACE: 4
};

// Current log level - can be set via environment variable
const currentLogLevel = process.env.LOG_LEVEL 
  ? LOG_LEVELS[process.env.LOG_LEVEL.toUpperCase()] || LOG_LEVELS.INFO
  : process.env.NODE_ENV === 'production' ? LOG_LEVELS.ERROR : LOG_LEVELS.INFO;

/**
 * Format a log message with timestamp and additional context
 * @param {string} level - Log level
 * @param {string} message - Log message
 * @param {Object} [context] - Additional context
 * @returns {string} Formatted log message
 */
const formatLogMessage = (level, message, context) => {
  const timestamp = new Date().toISOString();
  let formattedMessage = `[${timestamp}] [${level}] ${message}`;
  
  if (context) {
    // For errors, extract the message and stack
    if (context instanceof Error) {
      formattedMessage += `\n  Error: ${context.message}`;
      if (context.stack) {
        formattedMessage += `\n  Stack: ${context.stack.split('\n').slice(1, 3).join('\n    ')}`;
      }
    } else if (typeof context === 'object') {
      // For objects, stringify them but limit the size
      try {
        const contextStr = JSON.stringify(context, (key, value) => {
          // Truncate long strings
          if (typeof value === 'string' && value.length > 200) {
            return value.substring(0, 200) + '...';
          }
          return value;
        }, 2);
        
        // Only add context if it's not too large
        if (contextStr.length < 500) {
          formattedMessage += `\n  Context: ${contextStr}`;
        } else {
          formattedMessage += `\n  Context: [Object too large to display]`;
        }
      } catch (err) {
        formattedMessage += `\n  Context: [Error stringifying context: ${err.message}]`;
      }
    } else {
      formattedMessage += `\n  Context: ${context}`;
    }
  }
  
  return formattedMessage;
};

/**
 * Log an error message
 * @param {string} message - Log message
 * @param {Object} [context] - Additional context
 */
const error = (message, context) => {
  if (currentLogLevel >= LOG_LEVELS.ERROR) {
    console.error(formatLogMessage('ERROR', message, context));
  }
};

/**
 * Log a warning message
 * @param {string} message - Log message
 * @param {Object} [context] - Additional context
 */
const warn = (message, context) => {
  if (currentLogLevel >= LOG_LEVELS.WARN) {
    console.warn(formatLogMessage('WARN', message, context));
  }
};

/**
 * Log an info message
 * @param {string} message - Log message
 * @param {Object} [context] - Additional context
 */
const info = (message, context) => {
  if (currentLogLevel >= LOG_LEVELS.INFO) {
    console.log(formatLogMessage('INFO', message, context));
  }
};

/**
 * Log a debug message
 * @param {string} message - Log message
 * @param {Object} [context] - Additional context
 */
const debug = (message, context) => {
  if (currentLogLevel >= LOG_LEVELS.DEBUG) {
    console.log(formatLogMessage('DEBUG', message, context));
  }
};

/**
 * Log a trace message
 * @param {string} message - Log message
 * @param {Object} [context] - Additional context
 */
const trace = (message, context) => {
  if (currentLogLevel >= LOG_LEVELS.TRACE) {
    console.log(formatLogMessage('TRACE', message, context));
  }
};

module.exports = {
  LOG_LEVELS,
  error,
  warn,
  info,
  debug,
  trace
};
