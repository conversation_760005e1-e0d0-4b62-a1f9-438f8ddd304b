-- Token Holdings Schema for PostgreSQL

-- Create token_holdings table
CREATE TABLE IF NOT EXISTS token_holdings (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    username VARCHAR(255),
    symbol VARCHAR(50) NOT NULL,
    balance DECIMAL(24, 8) NOT NULL DEFAULT 0,
    value DECIMAL(24, 2) NOT NULL DEFAULT 0,
    percentage DECIMAL(8, 4) NOT NULL DEFAULT 0, -- Percentage of total supply
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, symbol)
);

-- Create index on symbol for faster lookups
CREATE INDEX IF NOT EXISTS idx_token_holdings_symbol ON token_holdings(symbol);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_token_holdings_user_id ON token_holdings(user_id);

-- Create index on balance for sorting by largest holders
CREATE INDEX IF NOT EXISTS idx_token_holdings_balance ON token_holdings(balance DESC);

-- Create token_supply table to track total supply
CREATE TABLE IF NOT EXISTS token_supply (
    symbol VARCHAR(50) PRIMARY KEY,
    total_supply DECIMAL(36, 8) NOT NULL,
    circulating_supply DECIMAL(36, 8) NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
