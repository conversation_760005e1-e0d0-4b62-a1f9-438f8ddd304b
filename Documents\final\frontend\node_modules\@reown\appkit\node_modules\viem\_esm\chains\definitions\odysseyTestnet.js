import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js';
export const odysseyTestnet = /*#__PURE__*/ define<PERSON>hain({
    id: 911867,
    name: 'Odyssey Testnet',
    nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: { http: ['https://odyssey.ithaca.xyz'] },
    },
    blockExplorers: {
        default: {
            name: 'Odyssey Explorer',
            url: 'https://odyssey-explorer.ithaca.xyz',
            apiUrl: 'https://odyssey-explorer.ithaca.xyz/api',
        },
    },
    testnet: true,
});
//# sourceMappingURL=odysseyTestnet.js.map