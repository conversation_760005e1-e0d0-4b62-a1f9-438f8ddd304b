/* Dashboard Page Styles */

.dashboard-container {
  max-width: 100vw;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-title {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 20px;
  color: #ccc;
}

.search-bar {
  margin-bottom: 20px;
}

.search-bar input {
  width: 100%;
  max-width: 300px;
  padding: 8px 15px;
  border-radius: 20px;
  border: 1px solid #333;
  background-color: #1e1e1e;
  color: white;
  font-size: 0.9rem;
}

.search-bar input::placeholder {
  color: #777;
}

.dashboard-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #333;
  padding-bottom: 10px;
}

.tab-button {
  background-color: #1e1e1e;
  color: #ccc;
  border: none;
  padding: 8px 15px;
  border-radius: 5px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.tab-button:hover {
  background-color: #333;
}

.tab-button.active {
  background-color: #333;
  color: white;
}

.dashboard-content {
  background-color: #111;
  border-radius: 10px;
  padding: 10px;
  border: 1px solid #333;
}

.token-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #222;
  background-color: #1a1a1a;
  margin-bottom: 10px;
  border-radius: 5px;
}

.token-row:last-child {
  border-bottom: none;
}

.dashboard-token-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.token-image-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #333;
}

.token-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.token-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: white;
}

.token-name.orange {
  color: #FF6B00;
}

.token-symbol {
  font-size: 0.8rem;
  color: #777;
}

.token-price {
  font-size: 0.9rem;
  color: #aaa;
}

.token-price.green {
  color: #00C853;
}

.token-change, .token-profit {
  font-size: 0.9rem;
  font-weight: 500;
}

.green {
  color: #00C853;
}

.red {
  color: #FF3D00;
}

.orange {
  color: #FF6B00;
}

.gradient-text {
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.total-profit-loss {
  display: flex;
  justify-content: space-between;
  padding: 15px;
  background-color: #1a1a1a;
  margin-bottom: 10px;
  border-radius: 5px;
  font-weight: 500;
}

.token-orders {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.order-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.order-label {
  font-size: 0.8rem;
  color: #aaa;
}

.order-price {
  font-size: 0.9rem;
  font-weight: 500;
}

.token-stats {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.stat-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stat-label {
  font-size: 0.8rem;
  color: #aaa;
}

.stat-value {
  font-size: 0.8rem;
  color: white;
}
