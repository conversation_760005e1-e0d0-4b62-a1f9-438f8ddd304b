/**
 * Graduation Routes
 * 
 * API routes for token graduation
 */

const express = require('express');
const router = express.Router();
const graduationController = require('../controllers/graduationController');
const { authenticateToken } = require('../middleware/authMiddleware');

/**
 * @route GET /api/graduation/:tokenSymbol/eligibility
 * @desc Check if a token is eligible for graduation
 * @access Public
 */
router.get('/:tokenSymbol/eligibility', graduationController.checkEligibility);

/**
 * @route POST /api/graduation/:tokenSymbol/graduate
 * @desc Graduate a token
 * @access Private (requires authentication)
 */
router.post('/:tokenSymbol/graduate', authenticateToken, graduationController.graduateToken);

module.exports = router;
