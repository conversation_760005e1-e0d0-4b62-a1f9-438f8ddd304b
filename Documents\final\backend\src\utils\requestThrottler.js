/**
 * Request throttler to prevent excessive API calls
 * Implements a simple in-memory cache with rate limiting
 */

const logger = require('./logger');

class RequestThrottler {
  constructor() {
    this.requestCache = new Map();
    this.requestTimestamps = new Map();
    this.rateLimits = new Map();
  }

  /**
   * Set rate limit for a specific endpoint
   * @param {string} endpoint - Endpoint identifier
   * @param {number} requestsPerMinute - Maximum requests per minute
   * @param {number} cacheTTL - Cache TTL in seconds
   */
  setRateLimit(endpoint, requestsPerMinute, cacheTTL = 60) {
    this.rateLimits.set(endpoint, {
      requestsPerMinute,
      cacheTTL: cacheTTL * 1000 // Convert to milliseconds
    });
  }

  /**
   * Check if a request should be throttled
   * @param {string} endpoint - Endpoint identifier
   * @returns {boolean} Whether the request should be throttled
   */
  shouldThrottle(endpoint) {
    const rateLimit = this.rateLimits.get(endpoint);
    if (!rateLimit) return false;

    const now = Date.now();
    const timestamps = this.requestTimestamps.get(endpoint) || [];
    
    // Filter timestamps to only include those within the last minute
    const recentTimestamps = timestamps.filter(ts => now - ts < 60000);
    
    // Update timestamps
    this.requestTimestamps.set(endpoint, [...recentTimestamps, now]);
    
    // Check if we've exceeded the rate limit
    return recentTimestamps.length >= rateLimit.requestsPerMinute;
  }

  /**
   * Get cached response for an endpoint
   * @param {string} endpoint - Endpoint identifier
   * @param {string} key - Cache key
   * @returns {any} Cached response or undefined
   */
  getCachedResponse(endpoint, key) {
    const cacheKey = `${endpoint}:${key}`;
    const cachedItem = this.requestCache.get(cacheKey);
    
    if (cachedItem) {
      const { data, timestamp } = cachedItem;
      const rateLimit = this.rateLimits.get(endpoint);
      const ttl = rateLimit ? rateLimit.cacheTTL : 60000; // Default to 1 minute
      
      // Check if cache is still valid
      if (Date.now() - timestamp < ttl) {
        logger.debug(`Cache hit for ${cacheKey}`);
        return data;
      }
      
      // Cache expired
      logger.debug(`Cache expired for ${cacheKey}`);
      this.requestCache.delete(cacheKey);
    }
    
    return undefined;
  }

  /**
   * Cache response for an endpoint
   * @param {string} endpoint - Endpoint identifier
   * @param {string} key - Cache key
   * @param {any} data - Response data to cache
   */
  cacheResponse(endpoint, key, data) {
    const cacheKey = `${endpoint}:${key}`;
    this.requestCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
    logger.debug(`Cached response for ${cacheKey}`);
  }

  /**
   * Clear cache for an endpoint
   * @param {string} endpoint - Endpoint identifier
   * @param {string} [key] - Optional cache key to clear specific item
   */
  clearCache(endpoint, key) {
    if (key) {
      const cacheKey = `${endpoint}:${key}`;
      this.requestCache.delete(cacheKey);
      logger.debug(`Cleared cache for ${cacheKey}`);
    } else {
      // Clear all cache entries for this endpoint
      for (const cacheKey of this.requestCache.keys()) {
        if (cacheKey.startsWith(`${endpoint}:`)) {
          this.requestCache.delete(cacheKey);
        }
      }
      logger.debug(`Cleared all cache for ${endpoint}`);
    }
  }
}

module.exports = new RequestThrottler();
