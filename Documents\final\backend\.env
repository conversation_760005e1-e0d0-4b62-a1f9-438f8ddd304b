# ===========================================
# MEME COIN PLATFORM - ENVIRONMENT CONFIG
# ===========================================

# Environment
NODE_ENV=development

# Server Configuration
PORT=3001
FRONTEND_URL=http://localhost:3000

# Database Configuration
MONGODB_URI=mongodb+srv://swaptradepvt:<EMAIL>/swap?retryWrites=true&w=majority
MONGODB_DB_NAME=swap

# Solana Configuration
SOLANA_RPC_URL=https://api.devnet.solana.com
SOLANA_PROGRAM_ID=5bupaxUMC3Rz2RK1zgrmBGxaqAz9hsrcAZLnZUSnHYvw
PLATFORM_WALLET_ADDRESS=D2QixKfNDDZspEXCBiFLiVLeQPDTZ8NmFMLnaSHeaMga

# Security
JWT_SECRET=your-super-secret-jwt-key-change-in-production
SESSION_SECRET=your-super-secret-session-key-change-in-production

