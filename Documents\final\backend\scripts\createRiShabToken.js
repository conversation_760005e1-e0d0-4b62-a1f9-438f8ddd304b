const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });
const Token = require('../models/Token');
const User = require('../models/User');

console.log('MongoDB URI:', process.env.MONGODB_URI ? 'Found' : 'Not found');
console.log('Current directory:', __dirname);

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

async function createToken() {
  try {
    // First, check if a user with username ri_shab exists
    let user = await User.findOne({ username: 'ri_shab' });

    // If user doesn't exist, create one
    if (!user) {
      user = new User({
        username: 'ri_shab',
        email: '<EMAIL>',
        password: 'password123', // This will be hashed by the pre-save hook
        walletAddress: '0x' + '1'.repeat(40), // Dummy wallet address
        isVerified: true
      });

      await user.save();
      console.log('Created new user: ri_shab');
    }

    // Check if token already exists
    const existingToken = await Token.findOne({ name: 'ri_shab Token' });
    if (existingToken) {
      console.log('Token already exists with name: ri_shab Token');
      process.exit(0);
    }

    // Create the token
    const token = new Token({
      name: 'ri_shab Token',
      symbol: 'RISHAB',
      description: 'The official token of ri_shab',
      creator: user._id,
      tokenAddress: '0x' + '2'.repeat(40), // Dummy token address
      initialPrice: 0.01,
      currentPrice: 0.05,
      totalSupply: 1000000,
      circulatingSupply: 500000,
      imageUrl: 'https://via.placeholder.com/150',
      marketCap: 25000, // currentPrice * circulatingSupply
      volume24h: 5000,
      priceChange24h: 15, // 15% increase
      holders: 120,
      transactions: 450,
      trending: true,
      trendingScore: 95,
      bondingCurveParams: {
        type: 'linear',
        slope: 0.0001
      }
    });

    await token.save();

    // Update user's createdTokens array
    user.createdTokens.push(token._id);
    await user.save();

    console.log('Successfully created token: ri_shab Token');
    console.log('Token details:', token);

  } catch (error) {
    console.error('Error creating token:', error);
  } finally {
    // Close the connection
    mongoose.connection.close();
  }
}

createToken();
