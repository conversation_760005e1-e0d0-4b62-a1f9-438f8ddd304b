# Meme Coin Platform Technical Documentation

This document provides detailed technical information about the Meme Coin Platform Solana program, including architecture, security features, and implementation details.

## Architecture Overview

The Meme Coin Platform is built on Solana using the Anchor framework. It implements a bonding curve-based token creation and trading system with the following key components:

1. **Platform State**: Central configuration for platform-wide settings
2. **Bonding Curves**: Mathematical models for token price discovery
3. **Token Creation**: Custom SPL token creation with metadata
4. **Trading Mechanism**: Buy/sell functionality through bonding curves
5. **Fee Collection**: Platform and creator fee distribution
6. **Security Features**: Circuit breakers and safety mechanisms

## Account Structure

### PlatformState

The platform state account stores global configuration:

```rust
#[account]
pub struct PlatformState {
    pub authority: Pubkey,           // Platform admin
    pub platform_wallet: Pubkey,     // Wallet for collecting fees
    pub platform_fee_percent: u16,   // Platform fee in basis points
    pub token_creation_fee: u64,     // Fee to create a new token
    pub token_count: u64,            // Number of tokens created
    pub total_volume: u64,           // Total trading volume
    pub total_fees_collected: u64,   // Total fees collected
    pub is_active: bool,             // Platform active status
}
```

### BondingCurve

Each token has a bonding curve account that manages its pricing and parameters:

```rust
#[account]
pub struct BondingCurve {
    pub mint: Pubkey,                // Token mint address
    pub authority: Pubkey,           // Mint authority (PDA)
    pub creator: Pubkey,             // Token creator
    pub curve_type: CurveType,       // Type of bonding curve
    pub base_price: u64,             // Base price in lamports
    pub curve_slope: u64,            // Slope parameter for the curve
    pub total_supply: u64,           // Current token supply
    pub reserve_balance: u64,        // SOL in reserve
    pub reserve_ratio: u16,          // Reserve ratio in basis points
    pub creator_fee_percent: u16,    // Creator fee in basis points
    pub platform_fee_percent: u16,   // Platform fee in basis points
    pub buy_count: u64,              // Number of buy transactions
    pub sell_count: u64,             // Number of sell transactions
    pub total_volume: u64,           // Total trading volume for this token
    pub is_active: bool,             // Trading active status
    pub created_at: i64,             // Creation timestamp
    pub name: String,                // Token name
    pub symbol: String,              // Token symbol
    pub description: String,         // Token description
    pub decimals: u8,                // Token decimals
    pub max_supply: Option<u64>,     // Optional maximum supply limit
    pub last_trade_timestamp: i64,   // Timestamp of last trade
    pub market_volatility_flag: bool, // Flag for market volatility protection
}
```

## Bonding Curve Implementation

The platform supports three types of bonding curves:

### Linear Curve

Price increases linearly with supply:
```
Price = base_price + (slope * supply / PRECISION)
```

### Exponential Curve

Price increases exponentially with supply:
```
Price = base_price * (1 + slope * supply / PRECISION)
```

### Logarithmic Curve

Price increases logarithmically with supply:
```
Price = base_price * (1 + sqrt(supply / PRECISION))
```

## Security Features

### Circuit Breakers

The program implements several circuit breakers to protect against market manipulation and extreme conditions:

1. **Maximum Sell Limit**: Prevents selling more than 10% of the total supply in a single transaction
   ```rust
   require!(
       token_amount <= max_sell_percentage,
       ErrorCode::SellAmountTooLarge
   );
   ```

2. **Reserve Health Check**: Ensures the reserve has sufficient funds to maintain stability
   ```rust
   require!(
       reserve_health >= 3000, // 30%
       ErrorCode::ReserveHealthTooLow
   );
   ```

3. **Price Impact Limit**: Prevents trades that would cause excessive price movement
   ```rust
   require!(
       price_impact <= 2000, // 20%
       ErrorCode::PriceImpactTooHigh
   );
   ```

4. **Maximum Supply Limit**: Optional cap on the total token supply
   ```rust
   require!(
       current_supply.checked_add(estimated_token_amount).unwrap_or(u64::MAX) <= max_supply,
       ErrorCode::MaxSupplyExceeded
   );
   ```

5. **Market Volatility Protection**: Platform-wide emergency stop for specific tokens
   ```rust
   require!(
       !bonding_curve.market_volatility_flag,
       ErrorCode::MarketVolatilityProtection
   );
   ```

### Arithmetic Safety

All mathematical operations use checked arithmetic to prevent overflows:

```rust
let platform_fee = (sol_amount as u128)
    .checked_mul(bonding_curve.platform_fee_percent as u128)
    .unwrap_or(0)
    .checked_div(10000)
    .unwrap_or(0);
```

## Reserve Ratio Mechanism

The reserve ratio determines what percentage of the theoretical value of all tokens is backed by SOL in the reserve:

```rust
// Apply reserve ratio
let actual_reserve = theoretical_full_reserve
    .checked_mul(reserve_ratio as u128)
    .unwrap_or(0)
    .checked_div(10000)
    .unwrap_or(0) as u64;
```

A reserve ratio of 5000 (50%) means that if all tokens were to be sold back, they would receive 50% of their theoretical value based on the current price.

## Fee Structure

The program implements a low-fee structure:

1. **Token Creation**: No platform fees - only standard Solana network fees for account creation and token minting

2. **Platform Trading Fee**: Very low 0.1% fee collected by the platform on all trades
   ```rust
   let platform_fee = (sol_amount as u128)
       .checked_mul(bonding_curve.platform_fee_percent as u128) // 10 basis points (0.1%)
       .unwrap()
       .checked_div(10000)
       .unwrap();
   ```

3. **Creator Fee**: Collected by the token creator on all trades (configurable up to 5%)
   ```rust
   let creator_fee = (sol_amount as u128)
       .checked_mul(bonding_curve.creator_fee_percent as u128)
       .unwrap()
       .checked_div(10000)
       .unwrap();
   ```

## Program Derived Addresses (PDAs)

The program uses several PDAs to manage different aspects:

1. **Platform State**: `[b"platform_state"]`
2. **Authority**: `[b"authority"]`
3. **Bonding Curve**: `[b"bonding_curve", mint.key().as_ref()]`
4. **Reserve**: `[b"reserve", mint.key().as_ref()]`

## Error Handling

The program uses a comprehensive error system with detailed error messages:

```rust
#[error_code]
pub enum ErrorCode {
    #[msg("Name too long - maximum 32 characters allowed")]
    NameTooLong,
    
    #[msg("Symbol too long - maximum 10 characters allowed")]
    SymbolTooLong,
    
    // ... other errors
    
    #[msg("Market volatility protection active - try again later")]
    MarketVolatilityProtection,
}
```

## Theoretical Reserve Calculation

The program calculates the theoretical reserve needed for full buyback based on the curve type:

```rust
pub fn calculate_theoretical_reserve(
    supply: u64,
    curve_type: CurveType,
    base_price: u64,
    slope: u64,
    reserve_ratio: u16,
) -> Result<u64> {
    // Implementation details...
}
```

## Testing

The program includes comprehensive tests for all functionality, including:

1. Basic functionality tests
2. Circuit breaker tests
3. Edge case tests
4. Security tests

## Deployment Process

To deploy the program:

1. Build the program: `anchor build`
2. Get the program ID: `solana address -k target/deploy/meme_coin_platform-keypair.json`
3. Update the program ID in `lib.rs` and `Anchor.toml`
4. Deploy to the desired cluster: `anchor deploy --provider.cluster devnet`

## Security Considerations

Before deploying to production:

1. Conduct a professional security audit
2. Test extensively on devnet
3. Start with conservative parameters
4. Implement monitoring for unusual activity
5. Have an emergency response plan for security incidents
