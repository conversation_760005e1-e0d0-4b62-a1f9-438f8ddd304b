/// <reference types="node" />
import { ErrorResponse, JsonRpcRequest, JsonRpcResponse, JsonRpc<PERSON><PERSON>ult, JsonRpcError } from "@walletconnect/jsonrpc-types";
import EventEmitter from "events";
import { ICore, CoreTypes } from "./core";
import { IStore } from "./store";
import { RelayerTypes } from "../core/relayer";
import { Logger } from "@walletconnect/logger";
export declare namespace PairingTypes {
    interface Struct {
        topic: string;
        expiry: number;
        relay: RelayerTypes.ProtocolOptions;
        active: boolean;
        peerMetadata?: CoreTypes.Metadata;
        methods?: string[];
    }
}
export declare namespace PairingJsonRpcTypes {
    type DefaultResponse = true | ErrorResponse;
    type WcMethod = "wc_pairingDelete" | "wc_pairingPing";
    type Error = ErrorResponse;
    interface RequestParams {
        wc_pairingDelete: {
            code: number;
            message: string;
        };
        wc_pairingPing: Record<string, unknown>;
    }
    interface Results {
        wc_pairingDelete: true;
        wc_pairingPing: true;
    }
    interface EventCallback<T extends JsonRpcRequest | JsonRpcResponse> {
        topic: string;
        payload: T;
    }
}
export declare type IPairingStore = IStore<string, PairingTypes.Struct>;
export declare abstract class IPairing {
    logger: Logger;
    core: ICore;
    abstract name: string;
    abstract readonly context: string;
    abstract events: EventEmitter;
    abstract pairings: IPairingStore;
    constructor(logger: Logger, core: ICore);
    abstract init(): Promise<void>;
    abstract pair(params: {
        uri: string;
        activatePairing?: boolean;
    }): Promise<PairingTypes.Struct>;
    abstract create(params?: {
        methods?: string[];
        transportType?: RelayerTypes.SubscribeOptions["transportType"];
    }): Promise<{
        topic: string;
        uri: string;
    }>;
    abstract activate(params: {
        topic: string;
    }): Promise<void>;
    abstract register(params: {
        methods: string[];
    }): void;
    abstract updateExpiry(params: {
        topic: string;
        expiry: number;
    }): Promise<void>;
    abstract updateMetadata(params: {
        topic: string;
        metadata: CoreTypes.Metadata;
    }): Promise<void>;
    abstract getPairings(): PairingTypes.Struct[];
    abstract ping(params: {
        topic: string;
    }): Promise<void>;
    abstract disconnect(params: {
        topic: string;
    }): Promise<void>;
    abstract formatUriFromPairing(pairing: PairingTypes.Struct): string;
}
export interface IPairingPrivate {
    sendRequest<M extends PairingJsonRpcTypes.WcMethod>(topic: string, method: M, params: PairingJsonRpcTypes.RequestParams[M]): Promise<number>;
    sendResult<M extends PairingJsonRpcTypes.WcMethod>(id: number, topic: string, result: PairingJsonRpcTypes.Results[M]): Promise<void>;
    sendError(id: number, topic: string, error: PairingJsonRpcTypes.Error): Promise<void>;
    onRelayEventRequest(event: PairingJsonRpcTypes.EventCallback<JsonRpcRequest>): void;
    onRelayEventResponse(event: PairingJsonRpcTypes.EventCallback<JsonRpcResponse>): Promise<void>;
    onPairingPingRequest(topic: string, payload: JsonRpcRequest<PairingJsonRpcTypes.RequestParams["wc_pairingPing"]>): Promise<void>;
    onPairingPingResponse(topic: string, payload: JsonRpcResult<PairingJsonRpcTypes.Results["wc_pairingPing"]> | JsonRpcError): void;
    onPairingDeleteRequest(topic: string, payload: JsonRpcRequest<PairingJsonRpcTypes.RequestParams["wc_pairingDelete"]>): Promise<void>;
    onUnknownRpcMethodRequest(topic: string, payload: JsonRpcRequest): Promise<void>;
    onUnknownRpcMethodResponse(method: string): void;
    deletePairing(topic: string, expirerHasDeleted?: boolean): Promise<void>;
}
//# sourceMappingURL=pairing.d.ts.map