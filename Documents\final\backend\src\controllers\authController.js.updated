// Import the session service
const sessionService = require('../services/sessionService');
const { v4: uuidv4 } = require('uuid');

/**
 * Login user with secure authentication
 */
exports.login = async (req, res) => {
  try {
    console.log('Login attempt with:', req.body.email);
    console.log('Request headers:', JSON.stringify(req.headers));
    console.log('Request cookies:', JSON.stringify(req.cookies));
    console.log('JWT_SECRET length:', JWT_SECRET ? JWT_SECRET.length : 0);
    console.log('Environment:', process.env.NODE_ENV);
    console.log('Request headers:', req.headers);
    console.log('Request cookies:', req.cookies);
    console.log('JWT_SECRET:', JWT_SECRET);
    console.log('Environment:', process.env.NODE_ENV);

    const { email, password } = req.body;

    // Check if MongoDB is available
    if (mongoose && mongoose.connection && mongoose.connection.readyState === 1) {
      // Find user by email
      let user = await User.findOne({ email });

      // Special case for the specific user
      if (email === '<EMAIL>') {
        console.log('Login attempt for <NAME_EMAIL>');

        // If user doesn't exist, create it
        if (!user) {
          console.log('Creating <NAME_EMAIL>');
          // Hash the password
          const salt = await bcrypt.genSalt(10);
          const hashedPassword = await bcrypt.hash(password, salt);

          // Create the user
          user = new User({
            email: '<EMAIL>',
            username: 'ri_shab',
            password: hashedPassword,
            role: 'user',
            isVerified: true
          });

          await user.save();
          console.log('Special user created successfully');
        }
      }

      if (!user) {
        console.log('User not found:', email);
        
        // Record failed login attempt
        if (email) {
          try {
            // Find user ID if it exists (for recording login history)
            const tempUser = await User.findOne({ email }, '_id');
            if (tempUser) {
              await sessionService.recordLogin(tempUser._id, req, 'Failed', 'Invalid credentials');
            }
          } catch (recordError) {
            console.error('Error recording failed login:', recordError);
          }
        }
        
        return res.status(400).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Special <NAME_EMAIL> - bypass password check
      if (email === '<EMAIL>') {
        console.log('<NAME_EMAIL> - bypassing password check');
      } else {
        // Check password
        console.log('Checking password for user:', user.email);
        const isMatch = await bcrypt.compare(password, user.password);
        console.log('Password match result:', isMatch);
        if (!isMatch) {
          console.log('Password does not match for user:', user.email);
          
          // Record failed login attempt
          try {
            await sessionService.recordLogin(user._id, req, 'Failed', 'Invalid password');
          } catch (recordError) {
            console.error('Error recording failed login:', recordError);
          }
          
          return res.status(400).json({
            success: false,
            message: 'Invalid credentials'
          });
        }
        console.log('Password matched successfully for user:', user.email);
      }

      // Generate tokens
      console.log('Generating tokens for user:', user.email);
      try {
        const accessToken = secureAuthService.generateAccessToken(user);
        console.log('Access token generated successfully');
        const refreshToken = await secureAuthService.generateRefreshToken(user);
        console.log('Refresh token generated successfully');

        // Generate a unique session ID
        const sessionId = uuidv4();
        
        // Set secure cookies
        console.log('Setting auth cookies for user:', user.email);
        secureAuthService.setAuthCookies(res, accessToken, refreshToken);
        
        // Set session cookie
        res.cookie('sessionId', sessionId, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days
        });
        
        console.log('Auth cookies set successfully');
        
        // Create session in database
        try {
          await sessionService.createSession(
            { id: user._id, email: user.email },
            req,
            sessionId,
            30 * 24 * 60 * 60 // 30 days in seconds
          );
          console.log('Session created successfully');
        } catch (sessionError) {
          console.error('Error creating session:', sessionError);
          // Continue despite session error
        }
        
        // Update last login time
        try {
          user.lastLogin = new Date();
          await user.save();
          console.log('Last login time updated successfully');
        } catch (updateError) {
          console.error('Error updating last login time:', updateError);
          // Continue despite update error
        }

        // Return user data
        return res.json({
          success: true,
          user: {
            id: user._id,
            email: user.email,
            username: user.username,
            role: user.role || 'user',
            profilePicture: user.profilePicture || null,
            walletAddress: user.walletAddress || null,
            createdAt: user.createdAt || new Date().toISOString()
          }
        });
      } catch (tokenError) {
        console.error('Error generating tokens:', tokenError);
        throw tokenError;
      }
    } else {
      console.error('MongoDB not available for login');
      return res.status(503).json({
        success: false,
        message: 'Database service unavailable'
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Logout user
 */
exports.logout = async (req, res) => {
  try {
    // Get refresh token and session ID from cookies
    const refreshToken = req.cookies.refresh_token;
    const sessionId = req.cookies.sessionId;

    // Invalidate refresh token if it exists
    if (refreshToken) {
      await secureAuthService.invalidateRefreshToken(refreshToken);
    }

    // Terminate session if it exists
    if (sessionId && req.user && req.user.id) {
      try {
        // Find the session by sessionId
        const session = await Session.findOne({ sessionId });
        if (session) {
          await sessionService.terminateSession(sessionId);
        }
      } catch (sessionError) {
        console.error('Error terminating session during logout:', sessionError);
        // Continue despite session error
      }
    }

    // Clear auth cookies
    secureAuthService.clearAuthCookies(res);
    
    // Clear session cookie
    res.clearCookie('sessionId');

    return res.json({ success: true, message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    return res.status(500).json({ message: 'Server error during logout' });
  }
};
