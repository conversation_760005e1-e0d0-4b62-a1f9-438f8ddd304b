# Production Deployment Guide

This guide provides instructions for securely deploying the application to a production environment.

## Security Measures

1. **Environment Variables**: Never commit sensitive credentials to your repository. Use environment variables set in your hosting platform.
2. **Secrets Management**: Generate secure random strings for JWT, cookie, and session secrets.
3. **Database Security**: Use strong passwords and restrict network access to your databases.
4. **SSL/TLS**: Ensure all connections use HTTPS/SSL.
5. **Wallet Security**: Keep wallet private keys secure and never expose them in client-side code.

## Pre-Deployment Steps

### 1. Generate Secure Secrets

Run the secret generator script to create secure random strings:

```bash
node scripts/generate-secrets.js
```

Copy the generated secrets for use in your production environment.

### 2. Prepare Environment Variables

Use the `.env.production.template` file as a reference for the environment variables you need to set in your production environment. Do NOT use the template file directly in production.

### 3. Database Setup

1. **MongoDB**: Ensure your MongoDB Atlas cluster is properly configured with:
   - Network access restrictions
   - Strong password
   - Proper user roles and permissions

2. **PostgreSQL**: Configure your Render PostgreSQL database with:
   - SSL enabled
   - Strong password
   - Proper connection string in environment variables

## Deployment Steps

### Hosting Platform Setup (e.g., Render)

1. Create a new Web Service for your backend
2. Connect to your GitHub repository
3. Set the build command: `npm install`
4. Set the start command: `npm run start:prod`
5. Set all environment variables from your `.env.production.template`
6. Enable auto-deploy from your main/production branch

### Environment Variables to Set

Set all the variables listed in `.env.production.template` with your actual production values, including:

- Database connection strings
- JWT and session secrets
- API keys
- Platform wallet address
- Frontend URL
- Solana network configuration

### SSL Configuration

Ensure SSL is enabled for your production deployment to encrypt all traffic.

## Post-Deployment Verification

After deployment, verify:

1. The application is running correctly
2. Database connections are established
3. Redis connections are working
4. Order matching engine is functioning
5. Wallet operations are secure

## Monitoring and Maintenance

1. Set up logging and monitoring
2. Configure alerts for critical errors
3. Regularly rotate secrets and credentials
4. Keep dependencies updated

## Security Best Practices

1. Regularly audit your code for security vulnerabilities
2. Use rate limiting to prevent abuse
3. Implement proper input validation
4. Keep all dependencies updated
5. Regularly rotate secrets and API keys
6. Monitor for suspicious activity

## Troubleshooting

If you encounter issues with your production deployment:

1. Check the application logs
2. Verify environment variables are set correctly
3. Ensure database connections are working
4. Check for network connectivity issues
5. Verify SSL certificates are valid
