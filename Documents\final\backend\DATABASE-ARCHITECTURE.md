# Database Architecture

This document outlines the database architecture for the WB Swap platform, which uses a hybrid approach with multiple databases for different purposes.

## Overview

The platform uses three main databases:

1. **MongoDB Atlas** - For user and token data
2. **PostgreSQL Render** - For trading and transaction data
3. **Redis Upstash** - For real-time updates and caching

## MongoDB Atlas

MongoDB Atlas is used for storing user-related and token-related data.

### Collections:

- **users**: User accounts, profiles, and authentication data
- **tokens**: Token metadata, descriptions, and configuration
- **comments**: User comments and social interactions
- **notifications**: User notifications
- **sessions**: User session data

### Connection:

```javascript
// MongoDB connection string from environment variables
const MONGODB_URI = process.env.MONGODB_URI;

// Connect to MongoDB
mongoose.connect(MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});
```

## PostgreSQL Render

PostgreSQL Render is used for trading and transaction-related data that benefits from ACID compliance and relational structure.

### Tables:

- **transactions**: All financial transactions
- **orders**: Order book entries
- **trades**: Executed trades
- **market_data**: Price and volume history

### Connection:

```javascript
// PostgreSQL connection string from environment variables
const POSTGRESQL_URI = process.env.POSTGRESQL_URI;

// Create Sequelize instance
const sequelize = new Sequelize(POSTGRESQL_URI, {
  dialect: 'postgres',
  logging: false,
  dialectOptions: {
    ssl: {
      require: true,
      rejectUnauthorized: false
    }
  }
});
```

## Redis Upstash

Redis Upstash is used for real-time data and caching.

### Data Structures:

- **Caches**: Frequently accessed data
- **Pub/Sub Channels**: Real-time updates
- **Sorted Sets**: Order book snapshots
- **Lists**: Recent trades

### Connection:

```javascript
// Redis connection from environment variables
const UPSTASH_REDIS_REST_URL = process.env.UPSTASH_REDIS_REST_URL;
const UPSTASH_REDIS_REST_TOKEN = process.env.UPSTASH_REDIS_REST_TOKEN;

// Create Redis client
const redis = new Redis({
  url: UPSTASH_REDIS_REST_URL,
  token: UPSTASH_REDIS_REST_TOKEN
});
```

## Data Flow

1. **User Data Flow**:
   - User registers/logs in → MongoDB
   - User profile updates → MongoDB
   - User session management → Redis + MongoDB

2. **Token Data Flow**:
   - Token creation → MongoDB + Solana
   - Token metadata → MongoDB
   - Token price history → PostgreSQL

3. **Trading Data Flow**:
   - Order placement → PostgreSQL + Order Matching Engine
   - Trade execution → PostgreSQL + Order Matching Engine
   - Order book updates → Redis (real-time) + PostgreSQL (persistence)

4. **Transaction Data Flow**:
   - Transaction creation → PostgreSQL
   - Transaction confirmation → PostgreSQL + Solana
   - Transaction history → PostgreSQL

## Migration

A migration script is provided to move trading and transaction data from MongoDB to PostgreSQL:

```bash
node src/scripts/migrate-to-postgresql.js
```

## Environment Configuration

Configure the database connections in your `.env` file:

```
# MongoDB Atlas for user and token data
MONGODB_URI=mongodb+srv://username:<EMAIL>/database

# PostgreSQL Render for trading and transaction data
POSTGRESQL_URI=postgres://username:<EMAIL>:5432/database

# Redis Upstash for real-time updates and caching
UPSTASH_REDIS_REST_URL=https://your-redis.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-token
```

## Best Practices

1. **Data Access**:
   - Use the appropriate database for each data type
   - Access MongoDB for user and token data
   - Access PostgreSQL for trading and transaction data
   - Access Redis for real-time and cached data

2. **Transactions**:
   - Use PostgreSQL transactions for financial operations
   - Ensure ACID compliance for critical operations

3. **Caching**:
   - Cache frequently accessed data in Redis
   - Implement cache invalidation strategies

4. **Scaling**:
   - MongoDB Atlas and PostgreSQL Render can be scaled as needed
   - Redis Upstash provides serverless scaling
