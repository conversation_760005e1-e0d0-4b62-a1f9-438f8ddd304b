import React, { useState, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Navbar from '../components/Navbar';
import '../styles/createToken.css';
import bitcoinLogo from '../assets/bitcoin-logo.png';
import { API_URL } from '../config/constants';
import axios from 'axios';

const CreateTokenPage = () => {
  const navigate = useNavigate();
  const [tokenData, setTokenData] = useState({
    name: '',
    symbol: '',
    description: '',
    ticker: '',
    initialSupply: '',
    bondingCurve: 'linear', // Default bonding curve type
    reserveRatio: '50',     // Default reserve ratio (%)
    initialPrice: '0.01',   // Default initial price
    image: null
  });

  const [previewImage, setPreviewImage] = useState(bitcoinLogo);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fileInputRef = useRef(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setTokenData({
      ...tokenData,
      [name]: value
    });
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result);
        setTokenData({
          ...tokenData,
          image: file
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form data
    if (!tokenData.name || !tokenData.symbol || !tokenData.initialSupply) {
      alert('Please fill in all required fields: Name, Symbol, and Initial Supply');
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare form data for backend
      const formData = new FormData();
      formData.append('name', tokenData.name);
      formData.append('symbol', tokenData.symbol.toUpperCase());
      formData.append('description', tokenData.description);
      formData.append('totalSupply', tokenData.initialSupply);
      formData.append('decimals', 9); // Standard for Solana tokens
      formData.append('bondingCurve', tokenData.bondingCurve);
      formData.append('reserveRatio', tokenData.reserveRatio);
      formData.append('initialPrice', tokenData.initialPrice);

      if (tokenData.image) {
        formData.append('image', tokenData.image);
      }

      // Submit to backend
      const response = await axios.post(`${API_URL}/tokens/create`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        withCredentials: true // Important for authentication
      });

      if (response.data.success) {
        alert(`Token ${tokenData.name} (${tokenData.symbol}) created successfully!`);
        // Navigate to the token's trading page
        navigate(`/trade/${response.data.token.id}`);
      } else {
        throw new Error(response.data.message || 'Token creation failed');
      }
    } catch (error) {
      console.error('Token creation error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to create token. Please try again.';
      alert(`Error: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="create-token-page">
      <Navbar />

      <div className="create-token-container">
        <div className="page-header">
          <h1 className="gradient-text">Create Token</h1>
          <Link to="/home" className="back-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </Link>
        </div>

        <div className="create-token-content">
          <form className="token-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="name">Token Name</label>
              <input
                type="text"
                id="name"
                name="name"
                value={tokenData.name}
                onChange={handleInputChange}
                placeholder="Enter token name"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="symbol">Token Symbol</label>
              <input
                type="text"
                id="symbol"
                name="symbol"
                value={tokenData.symbol}
                onChange={handleInputChange}
                placeholder="Enter token symbol (e.g., BTC)"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                name="description"
                value={tokenData.description}
                onChange={handleInputChange}
                placeholder="Describe your token"
                rows="4"
              />
            </div>

            <div className="form-group">
              <label htmlFor="ticker">Ticker ($)</label>
              <input
                type="text"
                id="ticker"
                name="ticker"
                value={tokenData.ticker}
                onChange={handleInputChange}
                placeholder="Enter ticker symbol"
              />
            </div>

            <div className="form-group">
              <label htmlFor="initialSupply">Initial Token Supply</label>
              <input
                type="text"
                id="initialSupply"
                name="initialSupply"
                value={tokenData.initialSupply}
                onChange={handleInputChange}
                placeholder="Enter initial supply (e.g., 1000000)"
              />
            </div>

            <div className="form-group">
              <label htmlFor="bondingCurve">Bonding Curve Type</label>
              <select
                id="bondingCurve"
                name="bondingCurve"
                value={tokenData.bondingCurve}
                onChange={handleInputChange}
                className="form-select"
              >
                <option value="linear">Linear</option>
                <option value="polynomial">Polynomial</option>
                <option value="logarithmic">Logarithmic</option>
                <option value="exponential">Exponential</option>
              </select>
              <div className="form-help-text">
                The bonding curve determines how token price changes with supply
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="reserveRatio">Reserve Ratio (%)</label>
              <input
                type="number"
                id="reserveRatio"
                name="reserveRatio"
                value={tokenData.reserveRatio}
                onChange={handleInputChange}
                placeholder="Enter reserve ratio (e.g., 50)"
                min="1"
                max="100"
              />
              <div className="form-help-text">
                Determines price sensitivity to supply changes (1-100%)
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="initialPrice">Initial Token Price (USD)</label>
              <input
                type="text"
                id="initialPrice"
                name="initialPrice"
                value={tokenData.initialPrice}
                onChange={handleInputChange}
                placeholder="Enter initial price (e.g., 0.01)"
              />
            </div>

            <div className="form-group image-upload">
              <label>Token Image</label>
              <div className="image-upload-container">
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleImageChange}
                  accept="image/*"
                  style={{ display: 'none' }}
                />
                <div className="token-image-preview" onClick={triggerFileInput}>
                  <img src={previewImage} alt="Token Preview" />
                  <div className="image-overlay">
                    <span>Click to upload</span>
                  </div>
                </div>
                <button type="button" className="upload-button" onClick={triggerFileInput}>
                  Upload Image
                </button>
              </div>
            </div>

            <button type="submit" className="create-button" disabled={isSubmitting}>
              {isSubmitting ? 'Creating Token...' : 'Create Token'}
            </button>
          </form>

          <div className="token-preview">
            <h3>Token Preview</h3>
            <div className="preview-card">
              <div className="preview-card-header">
                <div className="preview-image-container">
                  <img src={previewImage} alt="Token" className="preview-image" />
                </div>
              </div>
              <div className="preview-card-content">
                <div className="preview-token-name">
                  {tokenData.name || "Bitcoin"}
                </div>
                <div className="preview-token-symbol">
                  {tokenData.symbol || "BTC"}
                </div>
                <div className="preview-token-price">
                  $ 13.25
                </div>
                <div className="preview-token-details">
                  <div className="preview-detail">
                    <span className="detail-label">Initial Supply:</span>
                    <span className="detail-value">{tokenData.initialSupply || "1,000,000"}</span>
                  </div>
                  <div className="preview-detail">
                    <span className="detail-label">Bonding Curve:</span>
                    <span className="detail-value">{tokenData.bondingCurve || "Linear"}</span>
                  </div>
                  <div className="preview-detail">
                    <span className="detail-label">Reserve Ratio:</span>
                    <span className="detail-value">{tokenData.reserveRatio || "50"}%</span>
                  </div>
                  <div className="preview-detail">
                    <span className="detail-label">Initial Price:</span>
                    <span className="detail-value">${tokenData.initialPrice || "0.01"}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateTokenPage;
