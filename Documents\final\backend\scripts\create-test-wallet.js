/**
 * <PERSON><PERSON><PERSON> to create a test <NAME_EMAIL>
 * This wallet will be used for testing trading functionality
 */
require('dotenv').config();
const mongoose = require('mongoose');
const { Keypair } = require('@solana/web3.js');
// No need for bs58

// Import User model
const User = require('../src/models/User');

// Generate a new Solana keypair for testing
const generateTestKeypair = () => {
  const keypair = Keypair.generate();
  return {
    publicKey: keypair.publicKey.toString(),
    secretKey: Buffer.from(keypair.secretKey).toString('base64'),
  };
};

// Connect to MongoDB
async function connectToMongoDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');
    return true;
  } catch (error) {
    console.error('MongoDB connection error:', error);
    return false;
  }
}

// Add test <NAME_EMAIL> user
async function addTestWalletToUser() {
  try {
    // Find the user
    const user = await User.findOne({ email: '<EMAIL>' });

    if (!user) {
      console.error(`User <NAME_EMAIL> not found`);
      return false;
    }

    // Generate test wallet
    const testWallet = generateTestKeypair();

    // Update user with test wallet
    user.walletAddress = testWallet.publicKey;
    user.testWallet = {
      publicKey: testWallet.publicKey,
      secretKey: testWallet.secretKey,
      isTestWallet: true,
      balance: 1000, // Add some test SOL
    };

    // Save the updated user
    await user.save();

    console.log(`Test wallet added <NAME_EMAIL>`);
    console.log(`Wallet address: ${testWallet.publicKey}`);
    console.log(`Secret key (base64): ${testWallet.secretKey.substring(0, 20)}...`);
    console.log(`Test balance: 1000 SOL`);

    return true;
  } catch (error) {
    console.error('Error adding test wallet:', error);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Connect to MongoDB
    const connected = await connectToMongoDB();

    if (!connected) {
      console.error('Failed to connect to MongoDB. Exiting...');
      process.exit(1);
    }

    console.log('MongoDB connection string:', process.env.MONGODB_URI);

    // Add test <NAME_EMAIL> user
    const walletAdded = await addTestWalletToUser();

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');

    if (walletAdded) {
      console.log('Test wallet setup completed successfully');
    } else {
      console.error('Failed to set up test wallet');
    }
  } catch (error) {
    console.error('Unhandled error in main function:', error);
  }
}

// Run the main function
main().catch(console.error);
