error: init_if_needed requires that anchor-lang be imported with the init-if-needed cargo feature enabled. Carefully read the init_if_needed docs before using this feature to make sure you know how to protect yourself against re-initialization attacks.
  --> src/context.rs:77:9
   |
77 |         init_if_needed,
   |         ^^^^^^^^^^^^^^

error: init_if_needed requires that anchor-lang be imported with the init-if-needed cargo feature enabled. Carefully read the init_if_needed docs before using this feature to make sure you know how to protect yourself against re-initialization attacks.
   --> src/context.rs:159:9
    |
159 |         init_if_needed,
    |         ^^^^^^^^^^^^^^

error: init_if_needed requires that anchor-lang be imported with the init-if-needed cargo feature enabled. Carefully read the init_if_needed docs before using this feature to make sure you know how to protect yourself against re-initialization attacks.
   --> src/context.rs:271:9
    |
271 |         init_if_needed,
    |         ^^^^^^^^^^^^^^

error[E0433]: failed to resolve: could not find `metadata` in `anchor_spl`
 --> src/lib.rs:4:5
  |
4 |     metadata::{
  |     ^^^^^^^^ could not find `metadata` in `anchor_spl`
  |
note: found an item that was configured out
 --> src/lib.rs:29:9
note: the item is gated behind the `metadata` feature
 --> src/lib.rs:28:7

error[E0432]: unresolved import `anchor_spl::metadata`
 --> src/lib.rs:4:5
  |
4 |     metadata::{
  |     ^^^^^^^^ could not find `metadata` in `anchor_spl`
  |
note: found an item that was configured out
 --> src/lib.rs:29:9
note: the item is gated behind the `metadata` feature
 --> src/lib.rs:28:7

error[E0432]: unresolved import `crate`
  --> src/lib.rs:26:1
   |
26 | #[program]
   | ^^^^^^^^^^ could not find `__client_accounts_buy_tokens` in the crate root
   |
   = note: this error originates in the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unexpected `cfg` condition value: `custom-heap`
  --> src/lib.rs:26:1
   |
26 | #[program]
   | ^^^^^^^^^^
   |
   = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
   = help: consider adding `custom-heap` as a feature in `Cargo.toml`
   = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
   = note: `#[warn(unexpected_cfgs)]` on by default
   = note: this warning originates in the macro `$crate::custom_heap_default` which comes from the expansion of the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unexpected `cfg` condition value: `custom-panic`
  --> src/lib.rs:26:1
   |
26 | #[program]
   | ^^^^^^^^^^
   |
   = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
   = help: consider adding `custom-panic` as a feature in `Cargo.toml`
   = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
   = note: this warning originates in the macro `$crate::custom_panic_default` which comes from the expansion of the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: `ErrorCode` is ambiguous
  --> src/lib.rs:36:48
   |
36 |         require!(platform_fee_percent <= 1000, ErrorCode::FeeTooHigh); // Max 10%
   |                                                ^^^^^^^^^ ambiguous name
   |
   = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
   = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
  --> src/lib.rs:1:5
   |
1  | use anchor_lang::prelude::*;
   |     ^^^^^^^^^^^^^^^^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
  --> src/lib.rs:21:5
   |
21 | use error::*;
   |     ^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate
   = note: `#[warn(ambiguous_glob_imports)]` on by default

warning: `ErrorCode` is ambiguous
  --> src/lib.rs:70:36
   |
70 |         require!(name.len() <= 32, ErrorCode::NameTooLong);
   |                                    ^^^^^^^^^ ambiguous name
   |
   = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
   = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
  --> src/lib.rs:1:5
   |
1  | use anchor_lang::prelude::*;
   |     ^^^^^^^^^^^^^^^^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
  --> src/lib.rs:21:5
   |
21 | use error::*;
   |     ^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
  --> src/lib.rs:71:38
   |
71 |         require!(symbol.len() <= 10, ErrorCode::SymbolTooLong);
   |                                      ^^^^^^^^^ ambiguous name
   |
   = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
   = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
  --> src/lib.rs:1:5
   |
1  | use anchor_lang::prelude::*;
   |     ^^^^^^^^^^^^^^^^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
  --> src/lib.rs:21:5
   |
21 | use error::*;
   |     ^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
  --> src/lib.rs:72:44
   |
72 |         require!(description.len() <= 200, ErrorCode::DescriptionTooLong);
   |                                            ^^^^^^^^^ ambiguous name
   |
   = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
   = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
  --> src/lib.rs:1:5
   |
1  | use anchor_lang::prelude::*;
   |     ^^^^^^^^^^^^^^^^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
  --> src/lib.rs:21:5
   |
21 | use error::*;
   |     ^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
  --> src/lib.rs:73:33
   |
73 |         require!(decimals <= 9, ErrorCode::DecimalsTooLarge);
   |                                 ^^^^^^^^^ ambiguous name
   |
   = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
   = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
  --> src/lib.rs:1:5
   |
1  | use anchor_lang::prelude::*;
   |     ^^^^^^^^^^^^^^^^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
  --> src/lib.rs:21:5
   |
21 | use error::*;
   |     ^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
  --> src/lib.rs:74:46
   |
74 |         require!(creator_fee_percent <= 500, ErrorCode::FeeTooHigh); // Max 5%
   |                                              ^^^^^^^^^ ambiguous name
   |
   = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
   = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
  --> src/lib.rs:1:5
   |
1  | use anchor_lang::prelude::*;
   |     ^^^^^^^^^^^^^^^^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
  --> src/lib.rs:21:5
   |
21 | use error::*;
   |     ^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
  --> src/lib.rs:75:66
   |
75 |         require!(reserve_ratio >= 100 && reserve_ratio <= 10000, ErrorCode::InvalidReserveRatio); // 1% to 100%
   |                                                                  ^^^^^^^^^ ambiguous name
   |
   = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
   = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
  --> src/lib.rs:1:5
   |
1  | use anchor_lang::prelude::*;
   |     ^^^^^^^^^^^^^^^^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
  --> src/lib.rs:21:5
   |
21 | use error::*;
   |     ^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
  --> src/lib.rs:76:38
   |
76 |         require!(initial_supply > 0, ErrorCode::InvalidSupply);
   |                                      ^^^^^^^^^ ambiguous name
   |
   = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
   = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
  --> src/lib.rs:1:5
   |
1  | use anchor_lang::prelude::*;
   |     ^^^^^^^^^^^^^^^^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
  --> src/lib.rs:21:5
   |
21 | use error::*;
   |     ^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
  --> src/lib.rs:77:34
   |
77 |         require!(base_price > 0, ErrorCode::InvalidAmount);
   |                                  ^^^^^^^^^ ambiguous name
   |
   = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
   = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
  --> src/lib.rs:1:5
   |
1  | use anchor_lang::prelude::*;
   |     ^^^^^^^^^^^^^^^^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
  --> src/lib.rs:21:5
   |
21 | use error::*;
   |     ^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
  --> src/lib.rs:78:35
   |
78 |         require!(curve_slope > 0, ErrorCode::InvalidAmount);
   |                                   ^^^^^^^^^ ambiguous name
   |
   = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
   = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
  --> src/lib.rs:1:5
   |
1  | use anchor_lang::prelude::*;
   |     ^^^^^^^^^^^^^^^^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
  --> src/lib.rs:21:5
   |
21 | use error::*;
   |     ^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
  --> src/lib.rs:79:41
   |
79 |         require!(minimum_liquidity > 0, ErrorCode::InvalidAmount);
   |                                         ^^^^^^^^^ ambiguous name
   |
   = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
   = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
  --> src/lib.rs:1:5
   |
1  | use anchor_lang::prelude::*;
   |     ^^^^^^^^^^^^^^^^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
  --> src/lib.rs:21:5
   |
21 | use error::*;
   |     ^^^^^^^^
   = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:193:36
    |
193 |         require!(name.len() <= 32, ErrorCode::NameTooLong);
    |                                    ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:194:38
    |
194 |         require!(symbol.len() <= 10, ErrorCode::SymbolTooLong);
    |                                      ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:195:44
    |
195 |         require!(description.len() <= 200, ErrorCode::DescriptionTooLong);
    |                                            ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:196:33
    |
196 |         require!(decimals <= 9, ErrorCode::DecimalsTooLarge);
    |                                 ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:197:46
    |
197 |         require!(creator_fee_percent <= 500, ErrorCode::FeeTooHigh);
    |                                              ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:198:66
    |
198 |         require!(reserve_ratio >= 100 && reserve_ratio <= 10000, ErrorCode::InvalidReserveRatio);
    |                                                                  ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:199:38
    |
199 |         require!(initial_supply > 0, ErrorCode::InvalidSupply);
    |                                      ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:200:34
    |
200 |         require!(base_price > 0, ErrorCode::InvalidAmount);
    |                                  ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:201:35
    |
201 |         require!(curve_slope > 0, ErrorCode::InvalidAmount);
    |                                   ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:202:41
    |
202 |         require!(initial_liquidity > 0, ErrorCode::InvalidAmount);
    |                                         ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:305:34
    |
305 |         require!(sol_amount > 0, ErrorCode::InvalidAmount);
    |                                  ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:308:43
    |
308 |         require!(bonding_curve.is_active, ErrorCode::CurveInactive);
    |                                           ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:309:54
    |
309 |         require!(bonding_curve.reserve_balance == 0, ErrorCode::LiquidityAlreadyAdded);
    |                                                      ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:310:82
    |
310 |         require!(bonding_curve.creator == ctx.accounts.liquidity_provider.key(), ErrorCode::Unauthorized);
    |                                                                                  ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:311:65
    |
311 |         require!(sol_amount >= bonding_curve.minimum_liquidity, ErrorCode::InsufficientBalance);
    |                                                                 ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:347:34
    |
347 |         require!(sol_amount > 0, ErrorCode::InvalidAmount);
    |                                  ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:350:43
    |
350 |         require!(bonding_curve.is_active, ErrorCode::CurveInactive);
    |                                           ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:351:46
    |
351 |         require!(bonding_curve.is_tradeable, ErrorCode::InsufficientReserve);
    |                                              ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:356:13
    |
356 |             ErrorCode::MarketVolatilityProtection
    |             ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:380:17
    |
380 |                 ErrorCode::MaxSupplyExceeded
    |                 ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:406:13
    |
406 |             ErrorCode::PriceImpactTooHigh
    |             ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:415:17
    |
415 |                 ErrorCode::InvalidAmount
    |                 ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:429:36
    |
429 |         require!(token_amount > 0, ErrorCode::InvalidCalculation);
    |                                    ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:544:36
    |
544 |         require!(token_amount > 0, ErrorCode::InvalidAmount);
    |                                    ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:547:43
    |
547 |         require!(bonding_curve.is_active, ErrorCode::CurveInactive);
    |                                           ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:548:46
    |
548 |         require!(bonding_curve.is_tradeable, ErrorCode::InsufficientReserve);
    |                                              ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:553:13
    |
553 |             ErrorCode::MarketVolatilityProtection
    |             ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:564:13
    |
564 |             ErrorCode::SellAmountTooLarge
    |             ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:589:13
    |
589 |             ErrorCode::ReserveHealthTooLow
    |             ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:604:13
    |
604 |             ErrorCode::PriceImpactTooHigh
    |             ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:617:34
    |
617 |         require!(sol_amount > 0, ErrorCode::InvalidCalculation);
    |                                  ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:622:13
    |
622 |             ErrorCode::InsufficientReserve
    |             ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:766:34
    |
766 |             require!(fee <= 500, ErrorCode::FeeTooHigh); // Max 5%
    |                                  ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:779:41
    |
779 |             require!(desc.len() <= 200, ErrorCode::DescriptionTooLong);
    |                                         ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:821:13
    |
821 |             ErrorCode::Unauthorized
    |             ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:827:35
    |
827 |             require!(fee <= 1000, ErrorCode::FeeTooHigh);
    |                                   ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:863:13
    |
863 |             ErrorCode::Unauthorized
    |             ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:867:30
    |
867 |         require!(amount > 0, ErrorCode::InvalidAmount);
    |                              ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:875:13
    |
875 |             ErrorCode::InsufficientBalance
    |             ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: `ErrorCode` is ambiguous
   --> src/lib.rs:916:13
    |
916 |             ErrorCode::Unauthorized
    |             ^^^^^^^^^ ambiguous name
    |
    = warning: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!
    = note: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `ErrorCode` could refer to the enum imported here
   --> src/lib.rs:1:5
    |
1   | use anchor_lang::prelude::*;
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate
note: `ErrorCode` could also refer to the enum imported here
   --> src/lib.rs:21:5
    |
21  | use error::*;
    |     ^^^^^^^^
    = help: consider adding an explicit import of `ErrorCode` to disambiguate

warning: unused imports: `Mint`, `TokenAccount`, `Token`, `Transfer`, `associated_token::AssociatedToken`, and `transfer`
  --> src/lib.rs:3:5
   |
3  |     associated_token::AssociatedToken,
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
10 |     token::{Mint, Token, TokenAccount, mint_to, burn, transfer, MintTo, Burn, Transfer}
   |             ^^^^  ^^^^^  ^^^^^^^^^^^^                 ^^^^^^^^                ^^^^^^^^
   |
   = note: `#[warn(unused_imports)]` on by default

warning: unexpected `cfg` condition value: `anchor-debug`
 --> src/context.rs:9:10
  |
9 | #[derive(Accounts)]
  |          ^^^^^^^^
  |
  = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
  = help: consider adding `anchor-debug` as a feature in `Cargo.toml`
  = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
  = note: this warning originates in the derive macro `Accounts` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unexpected `cfg` condition value: `anchor-debug`
   --> src/context.rs:194:10
    |
194 | #[derive(Accounts)]
    |          ^^^^^^^^
    |
    = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
    = help: consider adding `anchor-debug` as a feature in `Cargo.toml`
    = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
    = note: this warning originates in the derive macro `Accounts` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unexpected `cfg` condition value: `anchor-debug`
   --> src/context.rs:301:10
    |
301 | #[derive(Accounts)]
    |          ^^^^^^^^
    |
    = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
    = help: consider adding `anchor-debug` as a feature in `Cargo.toml`
    = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
    = note: this warning originates in the derive macro `Accounts` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unexpected `cfg` condition value: `anchor-debug`
   --> src/context.rs:361:10
    |
361 | #[derive(Accounts)]
    |          ^^^^^^^^
    |
    = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
    = help: consider adding `anchor-debug` as a feature in `Cargo.toml`
    = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
    = note: this warning originates in the derive macro `Accounts` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unexpected `cfg` condition value: `anchor-debug`
   --> src/context.rs:375:10
    |
375 | #[derive(Accounts)]
    |          ^^^^^^^^
    |
    = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
    = help: consider adding `anchor-debug` as a feature in `Cargo.toml`
    = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
    = note: this warning originates in the derive macro `Accounts` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unexpected `cfg` condition value: `anchor-debug`
   --> src/context.rs:394:10
    |
394 | #[derive(Accounts)]
    |          ^^^^^^^^
    |
    = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
    = help: consider adding `anchor-debug` as a feature in `Cargo.toml`
    = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
    = note: this warning originates in the derive macro `Accounts` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unexpected `cfg` condition value: `anchor-debug`
   --> src/context.rs:421:10
    |
421 | #[derive(Accounts)]
    |          ^^^^^^^^
    |
    = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
    = help: consider adding `anchor-debug` as a feature in `Cargo.toml`
    = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
    = note: this warning originates in the derive macro `Accounts` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unexpected `cfg` condition value: `anchor-debug`
   --> src/context.rs:436:10
    |
436 | #[derive(Accounts)]
    |          ^^^^^^^^
    |
    = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
    = help: consider adding `anchor-debug` as a feature in `Cargo.toml`
    = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
    = note: this warning originates in the derive macro `Accounts` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unexpected `cfg` condition value: `anchor-debug`
   --> src/context.rs:463:10
    |
463 | #[derive(Accounts)]
    |          ^^^^^^^^
    |
    = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
    = help: consider adding `anchor-debug` as a feature in `Cargo.toml`
    = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
    = note: this warning originates in the derive macro `Accounts` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unexpected `cfg` condition value: `anchor-debug`
   --> src/context.rs:478:10
    |
478 | #[derive(Accounts)]
    |          ^^^^^^^^
    |
    = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
    = help: consider adding `anchor-debug` as a feature in `Cargo.toml`
    = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
    = note: this warning originates in the derive macro `Accounts` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unused import: `error::*`
  --> src/lib.rs:21:5
   |
21 | use error::*;
   |     ^^^^^^^^

warning: unexpected `cfg` condition value: `anchor-debug`
  --> src/lib.rs:26:1
   |
26 | #[program]
   | ^^^^^^^^^^
   |
   = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
   = help: consider adding `anchor-debug` as a feature in `Cargo.toml`
   = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
   = note: this warning originates in the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unexpected `cfg` condition value: `anchor-debug`
  --> src/lib.rs:26:1
   |
26 | #[program]
   | ^^^^^^^^^^
   |
   = note: expected values for `feature` are: `cpi`, `default`, `no-entrypoint`, `no-idl`, and `no-log-ix-name`
   = help: consider adding `anchor-debug` as a feature in `Cargo.toml`
   = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
   = note: this warning originates in the derive macro `Accounts` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0599]: no variant or associated item named `PlatformInactive` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/context.rs:200:60
    |
200 |         constraint = platform_state.is_active @ ErrorCode::PlatformInactive
    |                                                            ^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `CurveInactive` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/context.rs:208:59
    |
208 |         constraint = bonding_curve.is_active @ ErrorCode::CurveInactive
    |                                                           ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `PlatformInactive` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/context.rs:307:60
    |
307 |         constraint = platform_state.is_active @ ErrorCode::PlatformInactive
    |                                                            ^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `CurveInactive` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/context.rs:315:59
    |
315 |         constraint = bonding_curve.is_active @ ErrorCode::CurveInactive
    |                                                           ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `Unauthorized` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/context.rs:381:74
    |
381 |         constraint = bonding_curve.creator == creator.key() @ ErrorCode::Unauthorized
    |                                                                          ^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `Unauthorized` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/context.rs:401:81
    |
401 |                       platform_state.authority == authority.key()) @ ErrorCode::Unauthorized
    |                                                                                 ^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `Unauthorized` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/context.rs:427:79
    |
427 |         constraint = platform_state.authority == authority.key() @ ErrorCode::Unauthorized
    |                                                                               ^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `Unauthorized` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/context.rs:441:79
    |
441 |         constraint = platform_state.authority == authority.key() @ ErrorCode::Unauthorized
    |                                                                               ^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `Unauthorized` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/context.rs:483:79
    |
483 |         constraint = platform_state.authority == authority.key() @ ErrorCode::Unauthorized
    |                                                                               ^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `Exponential` found for enum `state::CurveType` in the current scope
   --> src/utils.rs:248:20
    |
248 |         CurveType::Exponential | CurveType::Logarithmic => {
    |                    ^^^^^^^^^^^ variant or associated item not found in `CurveType`
    |
   ::: src/state.rs:5:1
    |
5   | pub enum CurveType {
    | ------------------ variant or associated item `Exponential` not found for this enum

error[E0599]: no variant or associated item named `Logarithmic` found for enum `state::CurveType` in the current scope
   --> src/utils.rs:248:45
    |
248 |         CurveType::Exponential | CurveType::Logarithmic => {
    |                                             ^^^^^^^^^^^ variant or associated item not found in `CurveType`
    |
   ::: src/state.rs:5:1
    |
5   | pub enum CurveType {
    | ------------------ variant or associated item `Logarithmic` not found for this enum

error[E0599]: no function or associated item named `try_accounts` found for struct `context::CreateToken` in the current scope
  --> src/lib.rs:26:1
   |
26 | #[program]
   | ^^^^^^^^^^ function or associated item not found in `CreateToken<'_>`
   |
  ::: src/context.rs:31:1
   |
31 | pub struct CreateToken<'info> {
   | ----------------------------- function or associated item `try_accounts` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following trait defines an item `try_accounts`, perhaps you need to implement it:
           candidate #1: `anchor_lang::Accounts`
help: there is a method `try_into` with a similar name, but with different arguments
  --> /home/<USER>/work/platform-tools/platform-tools/out/rust/library/core/src/convert/mod.rs:611:5
   = note: this error originates in the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `context::CreateToken<'_>: anchor_lang::Accounts<'_>` is not satisfied
  --> src/lib.rs:26:1
   |
26 | #[program]
   | ^^^^^^^^^^ the trait `anchor_lang::Accounts<'_>` is not implemented for `context::CreateToken<'_>`
   |
   = help: the following other types implement trait `anchor_lang::Accounts<'info>`:
             AccountLoader<'info, T>
             Box<T>
             CreateNonceAccount<'info>
             CreateNonceAccountWithSeed<'info>
             Interface<'info, T>
             InterfaceAccount<'info, T>
             Option<T>
             SystemAccount<'info>
           and 71 others
note: required by a bound in `anchor_lang::context::Context::<'a, 'b, 'c, 'info, T>::new`
  --> src/context.rs:51:5
   = note: this error originates in the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0599]: no function or associated item named `try_accounts` found for struct `context::CreateTokenWithLiquidity` in the current scope
   --> src/lib.rs:26:1
    |
26  | #[program]
    | ^^^^^^^^^^ function or associated item not found in `CreateTokenWithLiquidity<'_>`
    |
   ::: src/context.rs:113:1
    |
113 | pub struct CreateTokenWithLiquidity<'info> {
    | ------------------------------------------ function or associated item `try_accounts` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `try_accounts`, perhaps you need to implement it:
            candidate #1: `anchor_lang::Accounts`
help: there is a method `try_into` with a similar name, but with different arguments
   --> /home/<USER>/work/platform-tools/platform-tools/out/rust/library/core/src/convert/mod.rs:611:5
    = note: this error originates in the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `context::CreateTokenWithLiquidity<'_>: anchor_lang::Accounts<'_>` is not satisfied
  --> src/lib.rs:26:1
   |
26 | #[program]
   | ^^^^^^^^^^ the trait `anchor_lang::Accounts<'_>` is not implemented for `context::CreateTokenWithLiquidity<'_>`
   |
   = help: the following other types implement trait `anchor_lang::Accounts<'info>`:
             AccountLoader<'info, T>
             Box<T>
             CreateNonceAccount<'info>
             CreateNonceAccountWithSeed<'info>
             Interface<'info, T>
             InterfaceAccount<'info, T>
             Option<T>
             SystemAccount<'info>
           and 71 others
note: required by a bound in `anchor_lang::context::Context::<'a, 'b, 'c, 'info, T>::new`
  --> src/context.rs:51:5
   = note: this error originates in the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0599]: no function or associated item named `try_accounts` found for struct `context::BuyTokens` in the current scope
   --> src/lib.rs:26:1
    |
26  | #[program]
    | ^^^^^^^^^^ function or associated item not found in `BuyTokens<'_>`
    |
   ::: src/context.rs:232:1
    |
232 | pub struct BuyTokens<'info> {
    | --------------------------- function or associated item `try_accounts` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `try_accounts`, perhaps you need to implement it:
            candidate #1: `anchor_lang::Accounts`
help: there is a method `try_into` with a similar name, but with different arguments
   --> /home/<USER>/work/platform-tools/platform-tools/out/rust/library/core/src/convert/mod.rs:611:5
    = note: this error originates in the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `context::BuyTokens<'_>: anchor_lang::Accounts<'_>` is not satisfied
  --> src/lib.rs:26:1
   |
26 | #[program]
   | ^^^^^^^^^^ the trait `anchor_lang::Accounts<'_>` is not implemented for `context::BuyTokens<'_>`
   |
   = help: the following other types implement trait `anchor_lang::Accounts<'info>`:
             AccountLoader<'info, T>
             Box<T>
             CreateNonceAccount<'info>
             CreateNonceAccountWithSeed<'info>
             Interface<'info, T>
             InterfaceAccount<'info, T>
             Option<T>
             SystemAccount<'info>
           and 71 others
note: required by a bound in `anchor_lang::context::Context::<'a, 'b, 'c, 'info, T>::new`
  --> src/context.rs:51:5
   = note: this error originates in the attribute macro `program` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0599]: no variant or associated item named `FeeTooHigh` found for enum `anchor_lang::error::ErrorCode` in the current scope
  --> src/lib.rs:36:59
   |
36 |         require!(platform_fee_percent <= 1000, ErrorCode::FeeTooHigh); // Max 10%
   |                                                           ^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `NameTooLong` found for enum `anchor_lang::error::ErrorCode` in the current scope
  --> src/lib.rs:70:47
   |
70 |         require!(name.len() <= 32, ErrorCode::NameTooLong);
   |                                               ^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `SymbolTooLong` found for enum `anchor_lang::error::ErrorCode` in the current scope
  --> src/lib.rs:71:49
   |
71 |         require!(symbol.len() <= 10, ErrorCode::SymbolTooLong);
   |                                                 ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `DescriptionTooLong` found for enum `anchor_lang::error::ErrorCode` in the current scope
  --> src/lib.rs:72:55
   |
72 |         require!(description.len() <= 200, ErrorCode::DescriptionTooLong);
   |                                                       ^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `DecimalsTooLarge` found for enum `anchor_lang::error::ErrorCode` in the current scope
  --> src/lib.rs:73:44
   |
73 |         require!(decimals <= 9, ErrorCode::DecimalsTooLarge);
   |                                            ^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `FeeTooHigh` found for enum `anchor_lang::error::ErrorCode` in the current scope
  --> src/lib.rs:74:57
   |
74 |         require!(creator_fee_percent <= 500, ErrorCode::FeeTooHigh); // Max 5%
   |                                                         ^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidReserveRatio` found for enum `anchor_lang::error::ErrorCode` in the current scope
  --> src/lib.rs:75:77
   |
75 |         require!(reserve_ratio >= 100 && reserve_ratio <= 10000, ErrorCode::InvalidReserveRatio); // 1% to 100%
   |                                                                             ^^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidSupply` found for enum `anchor_lang::error::ErrorCode` in the current scope
  --> src/lib.rs:76:49
   |
76 |         require!(initial_supply > 0, ErrorCode::InvalidSupply);
   |                                                 ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidAmount` found for enum `anchor_lang::error::ErrorCode` in the current scope
  --> src/lib.rs:77:45
   |
77 |         require!(base_price > 0, ErrorCode::InvalidAmount);
   |                                             ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidAmount` found for enum `anchor_lang::error::ErrorCode` in the current scope
  --> src/lib.rs:78:46
   |
78 |         require!(curve_slope > 0, ErrorCode::InvalidAmount);
   |                                              ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidAmount` found for enum `anchor_lang::error::ErrorCode` in the current scope
  --> src/lib.rs:79:52
   |
79 |         require!(minimum_liquidity > 0, ErrorCode::InvalidAmount);
   |                                                    ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0609]: no field `authority` on type `BTreeMap<String, u8>`
   --> src/lib.rs:151:25
    |
151 |             &[ctx.bumps.authority],
    |                         ^^^^^^^^^ unknown field

error[E0599]: no variant or associated item named `NameTooLong` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:193:47
    |
193 |         require!(name.len() <= 32, ErrorCode::NameTooLong);
    |                                               ^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `SymbolTooLong` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:194:49
    |
194 |         require!(symbol.len() <= 10, ErrorCode::SymbolTooLong);
    |                                                 ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `DescriptionTooLong` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:195:55
    |
195 |         require!(description.len() <= 200, ErrorCode::DescriptionTooLong);
    |                                                       ^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `DecimalsTooLarge` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:196:44
    |
196 |         require!(decimals <= 9, ErrorCode::DecimalsTooLarge);
    |                                            ^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `FeeTooHigh` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:197:57
    |
197 |         require!(creator_fee_percent <= 500, ErrorCode::FeeTooHigh);
    |                                                         ^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidReserveRatio` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:198:77
    |
198 |         require!(reserve_ratio >= 100 && reserve_ratio <= 10000, ErrorCode::InvalidReserveRatio);
    |                                                                             ^^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidSupply` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:199:49
    |
199 |         require!(initial_supply > 0, ErrorCode::InvalidSupply);
    |                                                 ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidAmount` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:200:45
    |
200 |         require!(base_price > 0, ErrorCode::InvalidAmount);
    |                                             ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidAmount` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:201:46
    |
201 |         require!(curve_slope > 0, ErrorCode::InvalidAmount);
    |                                              ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidAmount` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:202:52
    |
202 |         require!(initial_liquidity > 0, ErrorCode::InvalidAmount);
    |                                                    ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0609]: no field `authority` on type `BTreeMap<String, u8>`
   --> src/lib.rs:262:58
    |
262 |         let seeds = &[b"authority".as_ref(), &[ctx.bumps.authority]];
    |                                                          ^^^^^^^^^ unknown field

error[E0599]: no variant or associated item named `InvalidAmount` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:305:45
    |
305 |         require!(sol_amount > 0, ErrorCode::InvalidAmount);
    |                                             ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `CurveInactive` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:308:54
    |
308 |         require!(bonding_curve.is_active, ErrorCode::CurveInactive);
    |                                                      ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `LiquidityAlreadyAdded` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:309:65
    |
309 |         require!(bonding_curve.reserve_balance == 0, ErrorCode::LiquidityAlreadyAdded);
    |                                                                 ^^^^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `Unauthorized` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:310:93
    |
310 |         require!(bonding_curve.creator == ctx.accounts.liquidity_provider.key(), ErrorCode::Unauthorized);
    |                                                                                             ^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InsufficientBalance` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:311:76
    |
311 |         require!(sol_amount >= bonding_curve.minimum_liquidity, ErrorCode::InsufficientBalance);
    |                                                                            ^^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidAmount` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:347:45
    |
347 |         require!(sol_amount > 0, ErrorCode::InvalidAmount);
    |                                             ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `CurveInactive` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:350:54
    |
350 |         require!(bonding_curve.is_active, ErrorCode::CurveInactive);
    |                                                      ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InsufficientReserve` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:351:57
    |
351 |         require!(bonding_curve.is_tradeable, ErrorCode::InsufficientReserve);
    |                                                         ^^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `MarketVolatilityProtection` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:356:24
    |
356 |             ErrorCode::MarketVolatilityProtection
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `MaxSupplyExceeded` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:380:28
    |
380 |                 ErrorCode::MaxSupplyExceeded
    |                            ^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `PriceImpactTooHigh` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:406:24
    |
406 |             ErrorCode::PriceImpactTooHigh
    |                        ^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidAmount` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:415:28
    |
415 |                 ErrorCode::InvalidAmount
    |                            ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidCalculation` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:429:47
    |
429 |         require!(token_amount > 0, ErrorCode::InvalidCalculation);
    |                                               ^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0609]: no field `authority` on type `BTreeMap<String, u8>`
   --> src/lib.rs:505:25
    |
505 |             &[ctx.bumps.authority],
    |                         ^^^^^^^^^ unknown field

error[E0599]: no variant or associated item named `InvalidAmount` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:544:47
    |
544 |         require!(token_amount > 0, ErrorCode::InvalidAmount);
    |                                               ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `CurveInactive` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:547:54
    |
547 |         require!(bonding_curve.is_active, ErrorCode::CurveInactive);
    |                                                      ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InsufficientReserve` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:548:57
    |
548 |         require!(bonding_curve.is_tradeable, ErrorCode::InsufficientReserve);
    |                                                         ^^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `MarketVolatilityProtection` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:553:24
    |
553 |             ErrorCode::MarketVolatilityProtection
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `SellAmountTooLarge` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:564:24
    |
564 |             ErrorCode::SellAmountTooLarge
    |                        ^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `ReserveHealthTooLow` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:589:24
    |
589 |             ErrorCode::ReserveHealthTooLow
    |                        ^^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `PriceImpactTooHigh` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:604:24
    |
604 |             ErrorCode::PriceImpactTooHigh
    |                        ^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidCalculation` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:617:45
    |
617 |         require!(sol_amount > 0, ErrorCode::InvalidCalculation);
    |                                             ^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InsufficientReserve` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:622:24
    |
622 |             ErrorCode::InsufficientReserve
    |                        ^^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0609]: no field `reserve` on type `BTreeMap<String, u8>`
   --> src/lib.rs:660:25
    |
660 |             &[ctx.bumps.reserve],
    |                         ^^^^^^^ unknown field

error[E0599]: no variant or associated item named `FeeTooHigh` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:766:45
    |
766 |             require!(fee <= 500, ErrorCode::FeeTooHigh); // Max 5%
    |                                             ^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `DescriptionTooLong` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:779:52
    |
779 |             require!(desc.len() <= 200, ErrorCode::DescriptionTooLong);
    |                                                    ^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `Unauthorized` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:821:24
    |
821 |             ErrorCode::Unauthorized
    |                        ^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `FeeTooHigh` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:827:46
    |
827 |             require!(fee <= 1000, ErrorCode::FeeTooHigh);
    |                                              ^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `Unauthorized` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:863:24
    |
863 |             ErrorCode::Unauthorized
    |                        ^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InvalidAmount` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:867:41
    |
867 |         require!(amount > 0, ErrorCode::InvalidAmount);
    |                                         ^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `InsufficientBalance` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:875:24
    |
875 |             ErrorCode::InsufficientBalance
    |                        ^^^^^^^^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

error[E0599]: no variant or associated item named `Unauthorized` found for enum `anchor_lang::error::ErrorCode` in the current scope
   --> src/lib.rs:916:24
    |
916 |             ErrorCode::Unauthorized
    |                        ^^^^^^^^^^^^ variant or associated item not found in `ErrorCode`

Some errors have detailed explanations: E0277, E0432, E0433, E0599, E0609.
For more information about an error, try `rustc --explain E0277`.
warning: `meme_coin_platform` (lib) generated 72 warnings (5 duplicates)
error: could not compile `meme_coin_platform` (lib) due to 78 previous errors; 72 warnings emitted