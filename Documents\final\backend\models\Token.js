const mongoose = require('mongoose');

const TokenSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  symbol: {
    type: String,
    required: true,
    trim: true,
    uppercase: true
  },
  description: {
    type: String,
    default: ''
  },
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  tokenAddress: {
    type: String,
    required: true,
    unique: true
  },
  initialPrice: {
    type: Number,
    required: true
  },
  currentPrice: {
    type: Number,
    required: true
  },
  totalSupply: {
    type: Number,
    required: true
  },
  circulatingSupply: {
    type: Number,
    required: true
  },
  imageUrl: {
    type: String,
    default: ''
  },
  marketCap: {
    type: Number,
    default: 0
  },
  volume24h: {
    type: Number,
    default: 0
  },
  priceChange24h: {
    type: Number,
    default: 0
  },
  holders: {
    type: Number,
    default: 0
  },
  transactions: {
    type: Number,
    default: 0
  },
  trending: {
    type: Boolean,
    default: false
  },
  trendingScore: {
    type: Number,
    default: 0
  },
  bondingCurveParams: {
    type: Object,
    default: {}
  }
}, {
  timestamps: true
});

// Virtual for calculating market cap
TokenSchema.virtual('calculatedMarketCap').get(function() {
  return this.currentPrice * this.circulatingSupply;
});

// Index for faster queries
TokenSchema.index({ symbol: 1 });
TokenSchema.index({ creator: 1 });
TokenSchema.index({ trending: 1, trendingScore: -1 });
TokenSchema.index({ createdAt: -1 });

module.exports = mongoose.model('Token', TokenSchema);
