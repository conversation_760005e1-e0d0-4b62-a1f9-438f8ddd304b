/**
 * Asset Routes
 */
const express = require('express');
const router = express.Router();
const assetController = require('../controllers/assetController');
const { authenticateToken } = require('../middleware/authMiddleware');
const { validateRequest } = require('../middleware/validationMiddleware');

// Get all assets for the authenticated user
router.get(
  '/',
  authenticateToken,
  assetController.getUserAssets
);

// Get a specific asset for the authenticated user
router.get(
  '/:symbol',
  authenticateToken,
  assetController.getUserAsset
);

// Create or update an asset (admin only)
router.post(
  '/',
  authenticateToken,
  validateRequest({
    body: {
      userId: { type: 'string', required: true },
      symbol: { type: 'string', required: true },
      balance: { type: 'number', required: false },
      value: { type: 'number', required: false },
      change_24h: { type: 'number', required: false }
    }
  }),
  assetController.upsertAsset
);

// Update asset prices (admin only)
router.post(
  '/update-prices',
  authenticateToken,
  validateRequest({
    body: {
      priceUpdates: { 
        type: 'array', 
        required: true,
        items: {
          type: 'object',
          properties: {
            symbol: { type: 'string', required: true },
            price: { type: 'number', required: true }
          }
        }
      }
    }
  }),
  assetController.updateAssetPrices
);

// Delete an asset (admin only)
router.delete(
  '/:userId/:symbol',
  authenticateToken,
  assetController.deleteAsset
);

module.exports = router;
