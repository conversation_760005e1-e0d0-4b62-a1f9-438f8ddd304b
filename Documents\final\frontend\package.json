{"name": "solana-token-app", "version": "0.1.0", "private": true, "dependencies": {"@metaplex-foundation/mpl-token-metadata": "^3.4.0", "@metaplex-foundation/umi": "^1.2.0", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-base": "^0.9.26", "@solana/wallet-adapter-react": "^0.15.38", "@solana/wallet-adapter-react-ui": "^0.9.38", "@solana/wallet-adapter-wallets": "^0.19.36", "@solana/web3.js": "^1.98.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "bs58": "^6.0.0", "crypto-browserify": "^3.12.1", "express": "^5.1.0", "pino": "^9.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "stream-browserify": "^3.0.0", "vite-plugin-node-polyfills": "^0.23.0", "web-vitals": "^2.1.4"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "react-scripts start", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node server.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@vitejs/plugin-react": "^4.4.1", "vite": "^6.3.5"}}