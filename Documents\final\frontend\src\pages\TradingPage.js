import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Navbar from '../components/Navbar';
import '../styles/tradingPage.css';
import bitcoinLogo from '../assets/bitcoin-logo.png';

// Mock chart data
const generateChartData = () => {
  const data = [];
  const now = new Date();
  for (let i = 30; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(now.getDate() - i);

    // Generate a random price with an upward trend
    const basePrice = 10 + Math.random() * 5;
    const trendFactor = 1 + (30 - i) * 0.01;
    const price = basePrice * trendFactor + (Math.random() - 0.5) * 2;

    data.push({
      date: date.toISOString().split('T')[0],
      price: price.toFixed(2)
    });
  }
  return data;
};

const TradingPage = () => {
  const { tokenId } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('buy');
  const [amount, setAmount] = useState('');
  const [price, setPrice] = useState('');
  const [commentText, setCommentText] = useState('');
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true);
  const [comments, setComments] = useState([
    {
      id: 1,
      user: 'Trader123',
      text: 'This token has great potential! I\'m bullish on it.',
      timestamp: '2 hours ago',
      likes: 5
    },
    {
      id: 2,
      user: 'CryptoWhale',
      text: 'I\'ve been holding this for months. The team is very responsive.',
      timestamp: '5 hours ago',
      likes: 12
    },
    {
      id: 3,
      user: 'BlockchainDev',
      text: 'The technology behind this project is solid. Looking forward to the next update.',
      timestamp: '1 day ago',
      likes: 8
    }
  ]);

  useEffect(() => {
    setLoading(true);
    fetch(`/api/tokens/${tokenId}`)
      .then(res => res.json())
      .then(data => {
        setToken(data);
        setLoading(false);
      })
      .catch(() => setLoading(false));
  }, [tokenId]);

  if (loading) return <div>Loading...</div>;
  if (!token) return <div>Token not found.</div>;

  // Mock chart data
  const chartData = generateChartData();

  const handleBack = () => {
    navigate(-1);
  };

  const handleBuySell = (e) => {
    e.preventDefault();
    const action = activeTab === 'buy' ? 'bought' : 'sold';
    alert(`You have ${action} ${amount} ${token.symbol} at $${price} per token.`);
    setAmount('');
    setPrice('');
  };

  const handleAddComment = (e) => {
    e.preventDefault();
    if (!commentText.trim()) return;

    const newComment = {
      id: comments.length + 1,
      user: 'You',
      text: commentText,
      timestamp: 'Just now',
      likes: 0
    };

    setComments([newComment, ...comments]);
    setCommentText('');
  };

  const handleLikeComment = (commentId) => {
    setComments(comments.map(comment =>
      comment.id === commentId
        ? { ...comment, likes: comment.likes + 1 }
        : comment
    ));
  };

  // Function to render the TradingView chart
  const renderTradingViewChart = () => {
    return (
      <div className="tp-chart-container">
        <div className="tp-chart-header">
          <h3>Price Chart</h3>
          <div className="tp-chart-timeframes">
            <button className="tp-timeframe-btn tp-active">1D</button>
            <button className="tp-timeframe-btn">1W</button>
            <button className="tp-timeframe-btn">1M</button>
            <button className="tp-timeframe-btn">3M</button>
            <button className="tp-timeframe-btn">1Y</button>
            <button className="tp-timeframe-btn">All</button>
          </div>
        </div>

        <div className="tp-tradingview-chart">
          {/* TradingView Chart Placeholder */}
          <div className="tp-tradingview-widget">
            <div className="tp-chart-placeholder">
              <div className="tp-chart-placeholder-header">
                <div className="tp-chart-symbol">{token.symbol}/USD</div>
                <div className="tp-chart-timeframe">1D</div>
              </div>
              <div className="tp-chart-placeholder-content">
                <div className="tp-chart-candles">
                  {Array(10).fill().map((_, i) => (
                    <div
                      key={i}
                      className={`tp-chart-candle ${i % 2 === 0 ? 'tp-candle-up' : 'tp-candle-down'}`}
                      style={{
                        height: `${30 + Math.random() * 70}px`,
                        left: `${i * 10}%`
                      }}
                    >
                      <div className="tp-candle-wick" style={{ height: `${20 + Math.random() * 30}px` }}></div>
                    </div>
                  ))}
                </div>
                <div className="tp-chart-volume">
                  {Array(10).fill().map((_, i) => (
                    <div
                      key={i}
                      className={`tp-volume-bar ${i % 2 === 0 ? 'tp-volume-up' : 'tp-volume-down'}`}
                      style={{
                        height: `${10 + Math.random() * 40}px`,
                        left: `${i * 10}%`
                      }}
                    ></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="tp-trading-page">
      <Navbar />

      <div className="tp-trading-container">
        <div className="tp-trading-header">
          <div className="tp-header-left">
            <button className="tp-back-button" onClick={handleBack}>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="19" y1="12" x2="5" y2="12"></line>
                <polyline points="12 19 5 12 12 5"></polyline>
              </svg>
              Back
            </button>

            <div className="tp-token-name">{token.name}</div>
          </div>

          <div className="tp-token-price-info">
            <div className="tp-current-price">${token.price.toFixed(2)}</div>
            <div className="tp-price-change tp-green">{token.change}</div>
            <div className="tp-token-volume">Volume: ${token.volume}</div>
          </div>
        </div>

        <div className="tp-trading-content">
          <div className="tp-trading-main">
            <div className="tp-trading-chart">
              {renderTradingViewChart()}

              <div className="tp-token-stats">
                <div className="tp-stat-item">
                  <span className="tp-stat-label">Market Cap</span>
                  <span className="tp-stat-value">${token.marketCap}</span>
                </div>
                <div className="tp-stat-item">
                  <span className="tp-stat-label">Volume (24h)</span>
                  <span className="tp-stat-value">${token.volume}</span>
                </div>
                <div className="tp-stat-item">
                  <span className="tp-stat-label">Holders</span>
                  <span className="tp-stat-value">{token.holders}</span>
                </div>
              </div>
            </div>

            <div className="tp-trading-panel">
              <div className="tp-panel-tabs">
                <button
                  className={`tp-panel-tab ${activeTab === 'buy' ? 'tp-active' : ''}`}
                  onClick={() => setActiveTab('buy')}
                >
                  Buy
                </button>
                <button
                  className={`tp-panel-tab ${activeTab === 'sell' ? 'tp-active' : ''}`}
                  onClick={() => setActiveTab('sell')}
                >
                  Sell
                </button>
              </div>

              <form className="tp-trading-form" onSubmit={handleBuySell}>
                <div className="tp-form-group">
                  <label htmlFor="amount">Amount ({token.symbol})</label>
                  <input
                    type="number"
                    id="amount"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder={`Enter ${token.symbol} amount`}
                    required
                  />
                </div>

                <div className="tp-form-group">
                  <label htmlFor="price">Price (USD)</label>
                  <input
                    type="number"
                    id="price"
                    value={price}
                    onChange={(e) => setPrice(e.target.value)}
                    placeholder="Enter price per token"
                    required
                  />
                </div>

                <div className="tp-form-group">
                  <label>Total</label>
                  <div className="tp-total-amount">
                    ${amount && price ? (parseFloat(amount) * parseFloat(price)).toFixed(2) : '0.00'}
                  </div>
                </div>

                <button
                  type="submit"
                  className={`tp-submit-btn ${activeTab === 'buy' ? 'tp-buy-btn' : 'tp-sell-btn'}`}
                >
                  {activeTab === 'buy' ? 'Buy' : 'Sell'} {token.symbol}
                </button>
              </form>

              <div className="tp-wallet-balance">
                <div className="tp-balance-item">
                  <span className="tp-balance-label">Your {token.symbol} Balance:</span>
                  <span className="tp-balance-value">10.5 {token.symbol}</span>
                </div>
                <div className="tp-balance-item">
                  <span className="tp-balance-label">Your SOL Balance:</span>
                  <span className="tp-balance-value">25.75 SOL</span>
                </div>
              </div>
            </div>
          </div>

          <div className="tp-token-description">
            <h3>About {token.name}</h3>
            <p>{token.description}</p>
          </div>

          <div className="tp-comments-section">
            <h3>Comments</h3>

            <form className="tp-comment-form" onSubmit={handleAddComment}>
              <textarea
                placeholder="Add a comment..."
                value={commentText}
                onChange={(e) => setCommentText(e.target.value)}
                required
              ></textarea>
              <button type="submit" className="tp-comment-btn">Post Comment</button>
            </form>

            <div className="tp-comments-list">
              {comments.map(comment => (
                <div key={comment.id} className="tp-comment">
                  <div className="tp-comment-header">
                    <div className="tp-comment-user">{comment.user}</div>
                    <div className="tp-comment-time">{comment.timestamp}</div>
                  </div>
                  <div className="tp-comment-text">{comment.text}</div>
                  <div className="tp-comment-actions">
                    <button
                      className="tp-like-btn"
                      onClick={() => handleLikeComment(comment.id)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                      </svg>
                      <span>{comment.likes}</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TradingPage;
