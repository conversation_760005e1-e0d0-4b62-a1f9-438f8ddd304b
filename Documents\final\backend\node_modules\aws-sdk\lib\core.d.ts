export {Config} from './config';
export {HTTPOptions, ConfigurationOptions} from './config-base';
export {Credentials} from './credentials';
export {CognitoIdentityCredentials} from './credentials/cognito_identity_credentials';
export {CredentialProvider<PERSON>hain} from './credentials/credential_provider_chain';
export {EC2MetadataCredentials} from './credentials/ec2_metadata_credentials';
export {RemoteCredentials} from './credentials/remote_credentials';
export {ECSCredentials} from './credentials/ecs_credentials';
export {EnvironmentCredentials} from './credentials/environment_credentials';
export {FileSystemCredentials} from './credentials/file_system_credentials';
export {SAMLCredentials} from './credentials/saml_credentials';
export {SharedIniFileCredentials} from './credentials/shared_ini_file_credentials';
export {SsoCredentials} from './credentials/sso_credentials';
export {ProcessCredentials} from './credentials/process_credentials';
export {TemporaryCredentials} from './credentials/temporary_credentials';
export {ChainableTemporaryCredentials} from './credentials/chainable_temporary_credentials';
export {WebIdentityCredentials} from './credentials/web_identity_credentials';
export {TokenFileWebIdentityCredentials} from './credentials/token_file_web_identity_credentials';
export {Token} from './token';
export {StaticTokenProvider} from './token/static_token_provider';
export {SSOTokenProvider} from './token/sso_token_provider';
export {TokenProviderChain} from './token/token_provider_chain';
export {Endpoint} from './endpoint';
export {EventListeners} from './event_listeners';
export {HttpRequest} from './http_request';
export {HttpResponse} from './http_response';
export {MetadataService} from './metadata_service';
export {Request} from './request';
export {Response} from './response';
export {Service} from './service';
export {AWSError} from './error';
export {IniLoader} from './shared-ini/ini-loader';
export {DocumentType} from './model';
