/**
 * <PERSON>ript to test database connections
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { Sequelize } = require('sequelize');

async function testConnections() {
  console.log('Starting database connection test...');

  // Test MongoDB connection
  try {
    console.log('Attempting to connect to MongoDB...');
    console.log('MongoDB URI:', process.env.MONGODB_URI);
    
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    console.log('Successfully connected to MongoDB!');
    
    // Get the list of collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('MongoDB collections:', collections.map(c => c.name));
    
    // Close MongoDB connection
    await mongoose.disconnect();
    console.log('MongoDB connection closed');
  } catch (error) {
    console.error('MongoDB connection error:', error.message);
  }

  // Test PostgreSQL connection
  try {
    console.log('\nAttempting to connect to PostgreSQL...');
    
    const pgConfig = {
      host: process.env.POSTGRESQL_HOST,
      port: process.env.POSTGRESQL_PORT,
      database: process.env.POSTGRESQL_DATABASE,
      username: process.env.POSTGRESQL_USER,
      password: process.env.POSTGRESQL_PASSWORD,
      dialect: 'postgres',
      logging: console.log
    };
    
    console.log('PostgreSQL config:', {
      host: pgConfig.host,
      port: pgConfig.port,
      database: pgConfig.database,
      username: pgConfig.username,
      dialect: pgConfig.dialect
    });
    
    const sequelize = new Sequelize(
      pgConfig.database,
      pgConfig.username,
      pgConfig.password,
      {
        host: pgConfig.host,
        port: pgConfig.port,
        dialect: pgConfig.dialect,
        logging: false
      }
    );
    
    await sequelize.authenticate();
    console.log('Successfully connected to PostgreSQL!');
    
    // Get the list of tables
    const tables = await sequelize.getQueryInterface().showAllTables();
    console.log('PostgreSQL tables:', tables);
    
    // Close PostgreSQL connection
    await sequelize.close();
    console.log('PostgreSQL connection closed');
  } catch (error) {
    console.error('PostgreSQL connection error:', error.message);
  }

  console.log('\nDatabase connection test complete');
}

// Run the test
testConnections()
  .then(() => {
    console.log('Test script completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('Test script failed:', error.message);
    process.exit(1);
  });
