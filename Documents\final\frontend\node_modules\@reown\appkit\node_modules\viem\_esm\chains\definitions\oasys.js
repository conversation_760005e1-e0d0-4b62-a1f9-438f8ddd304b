import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const oasys = /*#__PURE__*/ defineChain({
    id: 248,
    name: 'Oasys',
    nativeCurrency: { name: 'Oas<PERSON>', symbol: 'OAS', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.mainnet.oasys.games'],
        },
    },
    blockExplorers: {
        default: {
            name: 'OasysScan',
            url: 'https://scan.oasys.games',
            apiUrl: 'https://scan.oasys.games/api',
        },
    },
});
//# sourceMappingURL=oasys.js.map