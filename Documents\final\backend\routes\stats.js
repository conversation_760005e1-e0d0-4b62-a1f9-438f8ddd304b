const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Token = require('../models/Token');

// Dashboard statistics endpoint
router.get('/dashboard', async (req, res) => {
  try {
    // Check if we should use mock data (for development/testing)
    if (process.env.USE_MOCK_DATABASE === 'true') {
      return res.status(200).json({
        totalUsers: 1250,
        activeUsers: 780,
        totalTokens: 325,
        monthlyCreators: 48
      });
    }

    // Get current date and first day of current month
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get total users count
    const totalUsers = await User.countDocuments();
    console.log('Total users count:', totalUsers);

    // Get active users this month (users who logged in this month)
    const activeUsers = await User.countDocuments({
      updatedAt: { $gte: firstDayOfMonth }
    });
    console.log('Active users count:', activeUsers);

    // Get total tokens count
    const totalTokens = await Token.countDocuments();
    console.log('Total tokens count:', totalTokens);

    // Get monthly creators (users who created tokens this month)
    const monthlyCreatorsResult = await Token.aggregate([
      { $match: { createdAt: { $gte: firstDayOfMonth } } },
      { $group: { _id: '$creator' } },
      { $count: 'count' }
    ]);

    const monthlyCreators = monthlyCreatorsResult.length > 0 ? monthlyCreatorsResult[0].count : 0;
    console.log('Monthly creators count:', monthlyCreators);

    res.status(200).json({
      totalUsers,
      activeUsers,
      totalTokens,
      monthlyCreators
    });
  } catch (error) {
    console.error('Error fetching dashboard statistics:', error);
    // Return zeros instead of error to prevent UI issues
    res.status(200).json({
      totalUsers: 0,
      activeUsers: 0,
      totalTokens: 0,
      monthlyCreators: 0
    });
  }
});

module.exports = router;
