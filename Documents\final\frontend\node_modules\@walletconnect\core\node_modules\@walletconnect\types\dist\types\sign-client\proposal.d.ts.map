{"version": 3, "file": "proposal.d.ts", "sourceRoot": "", "sources": ["../../../src/sign-client/proposal.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAEvC,MAAM,CAAC,OAAO,WAAW,aAAa,CAAC;IACrC,UAAU,qBAAqB;QAC7B,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;QAClB,OAAO,EAAE,MAAM,EAAE,CAAC;QAClB,MAAM,EAAE,MAAM,EAAE,CAAC;KAClB;IAED,KAAK,iBAAiB,GAAG,qBAAqB,CAAC;IAE/C,KAAK,kBAAkB,GAAG,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;IAC5D,KAAK,kBAAkB,GAAG,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;IAC5D,KAAK,iBAAiB,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAEhD,UAAiB,MAAM;QACrB,EAAE,EAAE,MAAM,CAAC;QAIX,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,eAAe,EAAE,MAAM,CAAC;QACxB,MAAM,EAAE,YAAY,CAAC,eAAe,EAAE,CAAC;QACvC,QAAQ,EAAE;YACR,SAAS,EAAE,MAAM,CAAC;YAClB,QAAQ,EAAE,eAAe,CAAC,QAAQ,CAAC;SACpC,CAAC;QACF,kBAAkB,EAAE,kBAAkB,CAAC;QACvC,kBAAkB,EAAE,kBAAkB,CAAC;QACvC,iBAAiB,CAAC,EAAE,iBAAiB,CAAC;QACtC,YAAY,EAAE,MAAM,CAAC;KACtB;CACF;AAED,oBAAY,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC"}