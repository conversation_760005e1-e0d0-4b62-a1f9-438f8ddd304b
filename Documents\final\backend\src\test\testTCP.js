const net = require('net');
const { v4: uuidv4 } = require('uuid');

// Create a TCP socket connection to the C++ Order Matching Engine
const client = new net.Socket();

// Connect to the C++ Order Matching Engine on port 9002
client.connect(9002, 'localhost', () => {
  console.log('Connected to C++ Order Matching Engine on port 9002');
  
  // Send a properly formatted JSON message
  setTimeout(() => {
    const message = JSON.stringify({
      type: 'getOrderBook',
      requestId: uuidv4(),
      symbol: 'SOL/USDC',
      timestamp: Date.now()
    });
    client.write(message);
    console.log('Sent JSON message to C++ Order Matching Engine:', message);
  }, 1000);
});

// Handle data from the C++ Order Matching Engine
client.on('data', (data) => {
  console.log('Received data from C++ Order Matching Engine:', data.toString());
});

// Handle C++ Order Matching Engine disconnection
client.on('close', () => {
  console.log('Disconnected from C++ Order Matching Engine');
});

// Handle C++ Order Matching Engine errors
client.on('error', (err) => {
  console.error('C++ Order Matching Engine error:', err.message);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('Shutting down test client');
  client.destroy();
  process.exit(0);
});
