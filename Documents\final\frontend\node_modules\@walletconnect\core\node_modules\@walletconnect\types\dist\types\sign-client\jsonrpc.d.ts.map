{"version": 3, "file": "jsonrpc.d.ts", "sourceRoot": "", "sources": ["../../../src/sign-client/jsonrpc.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,WAAW,CAAC;AACzC,OAAO,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAC3C,OAAO,EAAE,SAAS,EAAE,MAAM,GAAG,CAAC;AAE9B,MAAM,CAAC,OAAO,WAAW,YAAY,CAAC;IAEpC,KAAY,eAAe,GAAG,IAAI,GAAG,aAAa,CAAC;IAEnD,KAAY,QAAQ,GAChB,mBAAmB,GACnB,kBAAkB,GAClB,kBAAkB,GAClB,kBAAkB,GAClB,kBAAkB,GAClB,gBAAgB,GAChB,mBAAmB,GACnB,iBAAiB,GACjB,wBAAwB,CAAC;IAI7B,UAAiB,aAAa;QAC5B,gBAAgB,EAAE;YAChB,IAAI,EAAE,MAAM,CAAC;YACb,OAAO,EAAE,MAAM,CAAC;SACjB,CAAC;QACF,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACxC,iBAAiB,EAAE;YACjB,MAAM,EAAE,YAAY,CAAC,eAAe,EAAE,CAAC;YACvC,kBAAkB,EAAE,aAAa,CAAC,kBAAkB,CAAC;YACrD,kBAAkB,EAAE,aAAa,CAAC,kBAAkB,CAAC;YACrD,iBAAiB,CAAC,EAAE,aAAa,CAAC,iBAAiB,CAAC;YACpD,QAAQ,EAAE;gBACR,SAAS,EAAE,MAAM,CAAC;gBAClB,QAAQ,EAAE,eAAe,CAAC,QAAQ,CAAC;aACpC,CAAC;YACF,eAAe,CAAC,EAAE,MAAM,CAAC;SAC1B,CAAC;QACF,gBAAgB,EAAE;YAChB,KAAK,EAAE,YAAY,CAAC,eAAe,CAAC;YACpC,UAAU,EAAE,YAAY,CAAC,UAAU,CAAC;YACpC,iBAAiB,CAAC,EAAE,aAAa,CAAC,iBAAiB,CAAC;YACpD,aAAa,CAAC,EAAE,YAAY,CAAC,aAAa,CAAC;YAC3C,MAAM,EAAE,MAAM,CAAC;YACf,UAAU,EAAE;gBACV,SAAS,EAAE,MAAM,CAAC;gBAClB,QAAQ,EAAE,eAAe,CAAC,QAAQ,CAAC;aACpC,CAAC;SACH,CAAC;QACF,gBAAgB,EAAE;YAChB,UAAU,EAAE,YAAY,CAAC,UAAU,CAAC;SACrC,CAAC;QACF,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC1C,gBAAgB,EAAE;YAChB,IAAI,EAAE,MAAM,CAAC;YACb,OAAO,EAAE,MAAM,CAAC;SACjB,CAAC;QACF,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACxC,iBAAiB,EAAE;YACjB,OAAO,EAAE;gBACP,MAAM,EAAE,MAAM,CAAC;gBACf,MAAM,EAAE,GAAG,CAAC;gBACZ,eAAe,CAAC,EAAE,MAAM,CAAC;aAC1B,CAAC;YACF,OAAO,EAAE,MAAM,CAAC;SACjB,CAAC;QACF,eAAe,EAAE;YACf,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM,CAAC;gBACb,IAAI,EAAE,OAAO,CAAC;aACf,CAAC;YACF,OAAO,EAAE,MAAM,CAAC;SACjB,CAAC;QACF,sBAAsB,EAAE,SAAS,CAAC,gCAAgC,CAAC;KACpE;IAGD,UAAiB,OAAO;QACtB,gBAAgB,EAAE,IAAI,CAAC;QACvB,cAAc,EAAE,IAAI,CAAC;QACrB,iBAAiB,EAAE;YACjB,KAAK,EAAE,YAAY,CAAC,eAAe,CAAC;YACpC,kBAAkB,EAAE,MAAM,CAAC;SAC5B,CAAC;QACF,gBAAgB,EAAE,IAAI,CAAC;QACvB,gBAAgB,EAAE,IAAI,CAAC;QACvB,gBAAgB,EAAE,IAAI,CAAC;QACvB,gBAAgB,EAAE,IAAI,CAAC;QACvB,cAAc,EAAE,IAAI,CAAC;QACrB,iBAAiB,EAAE,aAAa,CAAC;QACjC,eAAe,EAAE,IAAI,CAAC;QACtB,sBAAsB,EAAE,SAAS,CAAC,iCAAiC,CAAC;KACrE;IAED,KAAY,KAAK,GAAG,aAAa,CAAC;CACnC"}