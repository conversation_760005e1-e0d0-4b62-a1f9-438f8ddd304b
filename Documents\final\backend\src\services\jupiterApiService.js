const axios = require('axios');
const { upstashClient } = require('../config/redis');
const logger = require('../utils/logger');
const requestThrottler = require('../utils/requestThrottler');

// Cache TTL in seconds
const CACHE_TTL = 60; // 1 minute for price data
const MARKET_CACHE_TTL = 300; // 5 minutes for market data
const CHART_CACHE_TTL = 600; // 10 minutes for chart data

// Set rate limits for different API endpoints
requestThrottler.setRateLimit('price', 30, 10); // 30 requests per minute, 10s cache
requestThrottler.setRateLimit('prices', 20, 30); // 20 requests per minute, 30s cache
requestThrottler.setRateLimit('chart', 10, 60); // 10 requests per minute, 60s cache

/**
 * Service for interacting with Jupiter API
 */
class JupiterApiService {
  constructor() {
    // Use more reliable API endpoints
    this.baseUrl = 'https://quote-api.jup.ag/v6';
    this.jupiterUrl = 'https://quote-api.jup.ag/v6';
    this.tokenListUrl = 'https://token.jup.ag/all';
    this.initialized = false;
    this.tokenList = [];
    this.initializationPromise = null;

    // Enhanced caching system
    this.priceCache = new Map();
    this.lastUpdated = new Map();
    this.chartCache = new Map();
    this.chartCacheExpiry = new Map();
    this.tokenDetailsCache = new Map();
    this.tokenDetailsCacheExpiry = new Map();

    // Cache management
    this.MAX_CACHE_SIZE = 1000; // Maximum items in each cache
    this.PRICE_CACHE_TTL = 30 * 1000; // 30 seconds for price data
    this.CHART_CACHE_TTL = 5 * 60 * 1000; // 5 minutes for chart data
    this.TOKEN_DETAILS_CACHE_TTL = 10 * 60 * 1000; // 10 minutes for token details

    // Redis request tracking
    this.redisRequestCount = 0;
    this.lastRedisReset = Date.now();

    // Track API request counts
    this.apiRequestCount = {
      price: 0,
      prices: 0,
      chart: 0,
      tokenList: 0,
      total: 0
    };

    // Log stats periodically
    setInterval(() => {
      const now = Date.now();
      const hoursSinceReset = (now - this.lastRedisReset) / (1000 * 60 * 60);

      if (this.apiRequestCount.total > 0 || this.redisRequestCount > 0) {
        logger.info(`API request stats (last minute): ${JSON.stringify(this.apiRequestCount)}`);
        logger.info(`Redis requests in the last ${hoursSinceReset.toFixed(2)} hours: ${this.redisRequestCount}`);

        // Reset counters
        this.apiRequestCount = {
          price: 0,
          prices: 0,
          chart: 0,
          tokenList: 0,
          total: 0
        };
        this.redisRequestCount = 0;
        this.lastRedisReset = now;
      }

      // Clean up expired cache entries
      this._cleanupCaches();
    }, 60000); // Log every minute
  }

  /**
   * Clean up expired cache entries
   * @private
   */
  _cleanupCaches() {
    const now = Date.now();

    // Clean up price cache
    for (const [key, timestamp] of this.lastUpdated.entries()) {
      if (now - timestamp > this.PRICE_CACHE_TTL) {
        this.priceCache.delete(key);
        this.lastUpdated.delete(key);
      }
    }

    // Clean up chart cache
    for (const [key, expiry] of this.chartCacheExpiry.entries()) {
      if (now > expiry) {
        this.chartCache.delete(key);
        this.chartCacheExpiry.delete(key);
      }
    }

    // Clean up token details cache
    for (const [key, expiry] of this.tokenDetailsCacheExpiry.entries()) {
      if (now > expiry) {
        this.tokenDetailsCache.delete(key);
        this.tokenDetailsCacheExpiry.delete(key);
      }
    }
  }

  /**
   * Initialize the service
   */
  async initialize() {
    // If already initialized, return immediately
    if (this.initialized) return;

    // If initialization is in progress, wait for it to complete
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    // Start initialization
    this.initializationPromise = (async () => {
      try {
        logger.info('Initializing Jupiter API service...');

        // Check if we have cached token list in Redis
        const cacheKey = 'jupiter:token_list';
        try {
          const cachedData = await upstashClient.get(cacheKey);
          if (cachedData && typeof cachedData === 'string') {
            try {
              this.tokenList = JSON.parse(cachedData);
              this.initialized = true;
              logger.info(`Jupiter API service initialized with ${this.tokenList.length} tokens from cache`);
              return;
            } catch (parseError) {
              logger.warn('Error parsing cached token list, fetching fresh data', parseError);
              await upstashClient.del(cacheKey);
            }
          }
        } catch (cacheError) {
          logger.warn('Cache error getting token list, fetching fresh data', cacheError);
        }

        // Load token list from the endpoint
        this.apiRequestCount.tokenList++;
        this.apiRequestCount.total++;
        const response = await axios.get(this.tokenListUrl, { timeout: 5000 });

        if (response.data) {
          // Handle the new Jupiter token list format which has a 'tokens' array
          if (Array.isArray(response.data.tokens)) {
            this.tokenList = response.data.tokens;
          } else if (Array.isArray(response.data)) {
            // Fallback for old format
            this.tokenList = response.data;
          } else {
            throw new Error('Invalid token list format');
          }

          this.initialized = true;

          // Cache the token list in Redis
          try {
            await upstashClient.set(cacheKey, JSON.stringify(this.tokenList), { ex: 3600 }); // Cache for 1 hour
          } catch (cacheError) {
            logger.warn('Error caching token list', cacheError);
          }

          logger.info(`Jupiter API service initialized with ${this.tokenList.length} tokens from API`);
        } else {
          throw new Error('Invalid response format from token list API');
        }
      } catch (error) {
        logger.error('Error initializing Jupiter API service', error);
        // Initialize with an empty token list instead of throwing
        this.tokenList = [];
        this.initialized = true;
        logger.warn('Initialized with empty token list due to API error');
      } finally {
        this.initializationPromise = null;
      }
    })();

    return this.initializationPromise;
  }

  /**
   * Get token price from Jupiter API
   * @param {string} tokenMint - Token mint address or symbol
   * @returns {Promise<Object>} Token price data
   */
  async getTokenPrice(tokenMint) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // For real-time price updates, use a very short cache TTL
      const REAL_TIME_CACHE_TTL = 10; // 10 seconds for real-time price data

      // Create a unique cache key
      const cacheKey = `jupiter:price:${tokenMint}`;

      // Check request throttler cache first (in-memory)
      const cachedResponse = requestThrottler.getCachedResponse('price', tokenMint);
      if (cachedResponse) {
        return cachedResponse;
      }

      // Check if we should throttle this request
      if (requestThrottler.shouldThrottle('price')) {
        logger.debug(`Throttling price request for ${tokenMint}`);

        // Return from in-memory cache if available
        if (this.priceCache.has(tokenMint)) {
          return this.priceCache.get(tokenMint);
        }

        // Generate mock data if no cache available
        const mockPrice = this._generateMockPrice(tokenMint);
        return {
          price: mockPrice,
          id: tokenMint,
          symbol: tokenMint,
          lastUpdated: new Date().toISOString(),
          source: 'throttled'
        };
      }

      // Try to get from Redis cache
      try {
        const cachedData = await upstashClient.get(cacheKey);
        if (cachedData && typeof cachedData === 'string') {
          try {
            const parsedData = JSON.parse(cachedData);
            // Cache in the request throttler
            requestThrottler.cacheResponse('price', tokenMint, parsedData);
            return parsedData;
          } catch (parseError) {
            logger.debug(`Error parsing cache data for ${cacheKey}`, parseError);
            // Delete invalid cache entry
            await upstashClient.del(cacheKey);
          }
        }
      } catch (cacheError) {
        // Only log cache errors at debug level to reduce noise
        logger.debug(`Cache error for ${cacheKey}`, cacheError);
      }

      // Check in-memory cache
      if (this.priceCache.has(tokenMint)) {
        const cacheTime = this.lastUpdated.get(tokenMint);
        if (Date.now() - cacheTime < REAL_TIME_CACHE_TTL * 1000) {
          const cachedData = this.priceCache.get(tokenMint);
          // Cache in the request throttler
          requestThrottler.cacheResponse('price', tokenMint, cachedData);
          return cachedData;
        }
      }

      // Find token in our list if symbol is provided
      let mintAddress = tokenMint;
      if (!tokenMint.includes('1111')) {
        const token = this.tokenList.find(t => t.symbol === tokenMint);
        if (token) {
          mintAddress = token.address;
        }
      }

      let priceData;

      // Try to get real price data from Jupiter API
      try {
        // For SOL and other major tokens, we can use Jupiter's price API
        if (mintAddress === 'So11111111111111111111111111111111111111112' ||
            mintAddress === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v' || // USDC
            mintAddress === 'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So' || // mSOL
            mintAddress === 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263') { // BONK

          // Track API request
          this.apiRequestCount.price++;
          this.apiRequestCount.total++;

          // Fetch from Jupiter API using the updated endpoint with timeout
          const response = await axios.get(`https://price.jup.ag/v4/price?ids=${mintAddress}`, {
            timeout: 3000 // 3 second timeout
          });

          if (!response.data.data || !response.data.data[mintAddress]) {
            throw new Error(`Price data not available for ${tokenMint}`);
          }

          priceData = {
            price: response.data.data[mintAddress].price,
            id: mintAddress,
            symbol: tokenMint,
            lastUpdated: new Date().toISOString(),
            source: 'api'
          };
        } else {
          // For custom tokens or tokens not supported by Jupiter's price API,
          // we'll try to get data from another source or fall back to mock data

          // Check if this is a custom token on our platform
          const isCustomToken = await this._isCustomToken(mintAddress);

          if (isCustomToken) {
            // Generate a consistent price for custom tokens
            const seed = mintAddress.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
            const basePrice = (Math.sin(seed * 9999) * 10000) % 10; // Generate a price between 0-10
            const randomFactor = 0.98 + (Math.random() * 0.04); // Small random factor for "real-time" feel

            priceData = {
              price: Math.max(0.00001, Math.abs(basePrice) * randomFactor),
              id: mintAddress,
              symbol: tokenMint,
              lastUpdated: new Date().toISOString(),
              isCustomToken: true,
              source: 'custom'
            };
          } else {
            // Fall back to mock data for unsupported tokens
            const price = this._generateMockPrice(tokenMint);
            priceData = {
              price,
              id: mintAddress,
              symbol: tokenMint,
              lastUpdated: new Date().toISOString(),
              source: 'mock'
            };
          }
        }
      } catch (apiError) {
        // Log API errors at debug level to reduce noise
        logger.debug(`Error fetching price data from API for ${tokenMint}`, apiError);

        // Fall back to mock data if API fails
        const price = this._generateMockPrice(tokenMint);
        priceData = {
          price,
          id: mintAddress,
          symbol: tokenMint,
          lastUpdated: new Date().toISOString(),
          source: 'fallback'
        };
      }

      // Cache the result with a short TTL for real-time data
      try {
        await upstashClient.set(cacheKey, JSON.stringify(priceData), { ex: REAL_TIME_CACHE_TTL });
      } catch (cacheError) {
        logger.debug(`Cache error setting ${cacheKey}`, cacheError);
      }

      // Update in-memory cache
      this.priceCache.set(tokenMint, priceData);
      this.lastUpdated.set(tokenMint, Date.now());

      // Cache in the request throttler
      requestThrottler.cacheResponse('price', tokenMint, priceData);

      return priceData;
    } catch (error) {
      logger.error(`Error getting price for ${tokenMint}`, error);

      // Return last cached value if available
      if (this.priceCache.has(tokenMint)) {
        return this.priceCache.get(tokenMint);
      }

      // Return default values if no cache available
      return {
        price: 0,
        id: tokenMint,
        symbol: tokenMint,
        lastUpdated: new Date().toISOString(),
        source: 'error'
      };
    }
  }

  /**
   * Get multiple token prices at once
   * @param {string[]} tokenMints - Array of token mint addresses or symbols
   * @returns {Promise<Object>} Object with token prices
   */
  async getMultipleTokenPrices(tokenMints) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Convert symbols to mint addresses if needed
      const mintAddresses = tokenMints.map(mint => {
        if (!mint.includes('1111')) {
          const token = this.tokenList.find(t => t.symbol === mint);
          return token ? token.address : mint;
        }
        return mint;
      });

      // Join mint addresses for API call
      const mintAddressesStr = mintAddresses.join(',');

      // Create a cache key for this specific request
      const cacheKey = `jupiter:prices:${mintAddressesStr}`;

      // Check request throttler cache first (in-memory)
      const cachedResponse = requestThrottler.getCachedResponse('prices', mintAddressesStr);
      if (cachedResponse) {
        return cachedResponse;
      }

      // Check if we should throttle this request
      if (requestThrottler.shouldThrottle('prices')) {
        logger.debug(`Throttling prices request for ${mintAddresses.length} tokens`);

        // Return empty object if throttled
        return {};
      }

      // Try to get from Redis cache first
      try {
        const cachedData = await upstashClient.get(cacheKey);
        if (cachedData && typeof cachedData === 'string') {
          try {
            const parsedData = JSON.parse(cachedData);
            // Cache in the request throttler
            requestThrottler.cacheResponse('prices', mintAddressesStr, parsedData);
            return parsedData;
          } catch (parseError) {
            logger.debug(`Error parsing cache data for ${cacheKey}`, parseError);
            // Delete invalid cache entry
            await upstashClient.del(cacheKey);
          }
        }
      } catch (cacheError) {
        logger.debug(`Cache error for ${cacheKey}`, cacheError);
      }

      // Track API request
      this.apiRequestCount.prices++;
      this.apiRequestCount.total++;

      // Fetch from Jupiter API using the updated endpoint with timeout
      const response = await axios.get(`https://price.jup.ag/v4/price?ids=${mintAddressesStr}`, {
        timeout: 5000 // 5 second timeout
      });

      if (!response.data.data) {
        throw new Error('Price data not available');
      }

      const priceData = response.data.data;

      // Cache the result
      try {
        await upstashClient.set(cacheKey, JSON.stringify(priceData), { ex: CACHE_TTL });
      } catch (cacheError) {
        logger.debug(`Cache error setting ${cacheKey}`, cacheError);
      }

      // Cache in the request throttler
      requestThrottler.cacheResponse('prices', mintAddressesStr, priceData);

      return priceData;
    } catch (error) {
      logger.error('Error getting multiple token prices', error);

      // Generate mock data for each token
      const mockData = {};
      for (const mint of tokenMints) {
        mockData[mint] = {
          price: this._generateMockPrice(mint),
          source: 'error-fallback'
        };
      }

      return mockData;
    }
  }

  /**
   * Get token details
   * @param {string} tokenMint - Token mint address or symbol
   * @returns {Promise<Object>} Token details
   */
  async getTokenDetails(tokenMint) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Create a unique cache key
      const cacheKey = `token:${tokenMint}`;

      // Check in-memory cache first (most efficient)
      if (this.tokenDetailsCache.has(cacheKey)) {
        const expiry = this.tokenDetailsCacheExpiry.get(cacheKey);
        if (expiry > Date.now()) {
          return this.tokenDetailsCache.get(cacheKey);
        }
        // Expired, remove from cache
        this.tokenDetailsCache.delete(cacheKey);
        this.tokenDetailsCacheExpiry.delete(cacheKey);
      }

      // Try to get from Redis cache as last resort before processing
      const redisCacheKey = `jupiter:token:${tokenMint}`;
      try {
        // Track Redis request
        this.redisRequestCount++;

        const cachedData = await upstashClient.get(redisCacheKey);
        if (cachedData && typeof cachedData === 'string') {
          try {
            const parsedData = JSON.parse(cachedData);

            // Update in-memory cache
            this.tokenDetailsCache.set(cacheKey, parsedData);
            this.tokenDetailsCacheExpiry.set(cacheKey, Date.now() + this.TOKEN_DETAILS_CACHE_TTL);

            return parsedData;
          } catch (parseError) {
            logger.debug(`Error parsing cache data for ${redisCacheKey}`, parseError);
            // Delete invalid cache entry
            await upstashClient.del(redisCacheKey);
          }
        }
      } catch (cacheError) {
        // Just log at debug level and continue
        logger.debug(`Cache error for ${redisCacheKey}`);
      }

      // Find token in our list
      let token;
      if (tokenMint.includes('1111')) {
        token = this.tokenList.find(t => t.address === tokenMint);
      } else {
        token = this.tokenList.find(t => t.symbol === tokenMint);
      }

      if (!token) {
        throw new Error(`Token ${tokenMint} not found`);
      }

      // Get token price
      const priceData = await this.getTokenPrice(token.address);

      // Format token details
      const tokenDetails = {
        id: token.address,
        name: token.name,
        symbol: token.symbol,
        logoUrl: token.logoURI,
        decimals: token.decimals,
        price: priceData.price || 0,
        tags: token.tags || []
      };

      // Update in-memory cache first (most efficient)
      this.tokenDetailsCache.set(cacheKey, tokenDetails);
      this.tokenDetailsCacheExpiry.set(cacheKey, Date.now() + this.TOKEN_DETAILS_CACHE_TTL);

      // Cache in Redis with a longer TTL to reduce future Redis calls
      try {
        // Track Redis request
        this.redisRequestCount++;

        await upstashClient.set(redisCacheKey, JSON.stringify(tokenDetails), { ex: MARKET_CACHE_TTL * 2 }); // Double the TTL
      } catch (cacheError) {
        // Just log at debug level and continue
        logger.debug(`Cache error setting ${redisCacheKey}`);
      }

      return tokenDetails;
    } catch (error) {
      console.error(`Error getting details for token ${tokenMint}:`, error);
      return null;
    }
  }

  /**
   * Get popular tokens
   * @param {number} limit - Number of tokens to return
   * @returns {Promise<Array>} Array of popular tokens
   */
  async getPopularTokens(limit = 30) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Try to get from Redis cache first
      const cacheKey = `jupiter:popular:${limit}`;
      try {
        const cachedData = await upstashClient.get(cacheKey);
        if (cachedData && typeof cachedData === 'string') {
          try {
            return JSON.parse(cachedData);
          } catch (parseError) {
            console.warn(`Error parsing cache data for ${cacheKey}:`, parseError);
            // Delete invalid cache entry
            await upstashClient.del(cacheKey);
          }
        }
      } catch (cacheError) {
        console.warn(`Cache error for ${cacheKey}:`, cacheError);
      }

      // Get popular tokens from our list
      // For now, we'll just use the first few tokens in the list
      // In a real implementation, you'd want to sort by volume or market cap
      const popularTokens = this.tokenList
        .filter(token => token.tags && token.tags.includes('popular'))
        .slice(0, limit);

      // If we don't have enough popular tokens, add some well-known ones
      if (popularTokens.length < limit) {
        const wellKnownSymbols = ['SOL', 'USDC', 'BONK', 'JUP', 'RAY', 'ORCA', 'MNGO', 'SRM', 'SAMO', 'USDT', 'ETH', 'BTC', 'PYTH', 'MSOL', 'STSOL', 'JSOL', 'SLND', 'DUST', 'MEAN', 'WOOF', 'BSOL', 'RENDER', 'RNDR', 'COPE', 'FIDA', 'MAPS', 'STEP', 'ATLAS', 'POLIS', 'GOFX', 'TULIP', 'SHDW', 'GENE', 'AUDIO', 'SLCL', 'PRISM', 'REAL', 'AURY', 'MEDIA', 'LARIX'];

        for (const symbol of wellKnownSymbols) {
          if (popularTokens.length >= limit) break;

          const token = this.tokenList.find(t => t.symbol === symbol);
          if (token && !popularTokens.some(p => p.address === token.address)) {
            popularTokens.push(token);
          }
        }
      }

      // If we still don't have enough, just take the first few from the list
      if (popularTokens.length < limit) {
        const remainingNeeded = limit - popularTokens.length;
        const additionalTokens = this.tokenList
          .filter(token => !popularTokens.some(p => p.address === token.address))
          .slice(0, remainingNeeded);

        popularTokens.push(...additionalTokens);
      }

      // Get prices for all tokens at once
      const tokenMints = popularTokens.map(token => token.address);
      const priceData = await this.getMultipleTokenPrices(tokenMints);

      // Format tokens with prices
      const formattedTokens = popularTokens.map(token => {
        const price = priceData[token.address] ? priceData[token.address].price : 0;

        return {
          id: token.address,
          name: token.name,
          symbol: token.symbol,
          description: token.tags ? token.tags.join(', ') : '',
          logoUrl: token.logoURI,
          price: price,
          volume: 0, // Jupiter doesn't provide this directly
          marketCap: 0 // Jupiter doesn't provide this directly
        };
      });

      // Cache the result
      try {
        await upstashClient.set(cacheKey, JSON.stringify(formattedTokens), { ex: MARKET_CACHE_TTL });
      } catch (cacheError) {
        console.warn(`Cache error setting ${cacheKey}:`, cacheError);
      }

      return formattedTokens;
    } catch (error) {
      console.error('Error getting popular tokens:', error);
      return [];
    }
  }

  /**
   * Get available markets
   * @returns {Promise<Array>} Array of markets
   */
  async getMarkets() {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Try to get from Redis cache first
      const cacheKey = 'jupiter:markets';
      try {
        const cachedData = await upstashClient.get(cacheKey);
        if (cachedData && typeof cachedData === 'string') {
          try {
            return JSON.parse(cachedData);
          } catch (parseError) {
            console.warn(`Error parsing cache data for ${cacheKey}:`, parseError);
            // Delete invalid cache entry
            await upstashClient.del(cacheKey);
          }
        }
      } catch (cacheError) {
        console.warn(`Cache error for ${cacheKey}:`, cacheError);
      }

      // Get popular tokens to create markets
      const popularTokens = await this.getPopularTokens(30);

      // Create markets with USDC as quote currency
      const markets = popularTokens.map(token => {
        return {
          id: `${token.symbol}-USDC`,
          name: `${token.name} / USD Coin`,
          baseSymbol: token.symbol,
          quoteSymbol: 'USDC',
          price: token.price,
          volume24h: 0, // Jupiter doesn't provide this directly
          priceChange24h: 0, // Jupiter doesn't provide this directly
          isPlatformToken: token.symbol === 'SOL',
          tokenId: token.id,
          tokenDescription: token.description || `${token.name} token on Solana blockchain`
        };
      });

      // Cache the result
      try {
        await upstashClient.set(cacheKey, JSON.stringify(markets), { ex: MARKET_CACHE_TTL });
      } catch (cacheError) {
        console.warn(`Cache error setting ${cacheKey}:`, cacheError);
      }

      return markets;
    } catch (error) {
      console.error('Error getting markets:', error);
      return [];
    }
  }

  /**
   * Get chart data for a token
   * @param {string} tokenMint - Token mint address or symbol
   * @param {string} timeframe - Timeframe (1h, 1d, 1w, 1m)
   * @returns {Promise<Object>} Chart data
   */
  async getChartData(tokenMint, timeframe = '1d') {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Convert timeframe to interval for API
      const interval = this._timeframeToInterval(timeframe);

      // Create a unique cache key
      const cacheKey = `${tokenMint}:${timeframe}`;

      // Check in-memory cache first (most efficient)
      if (this.chartCache.has(cacheKey)) {
        const expiry = this.chartCacheExpiry.get(cacheKey);
        if (expiry > Date.now()) {
          return this.chartCache.get(cacheKey);
        }
        // Expired, remove from cache
        this.chartCache.delete(cacheKey);
        this.chartCacheExpiry.delete(cacheKey);
      }

      // Check request throttler cache second
      const throttlerKey = `${tokenMint}:${timeframe}`;
      const cachedResponse = requestThrottler.getCachedResponse('chart', throttlerKey);
      if (cachedResponse) {
        // Update our in-memory cache
        this.chartCache.set(cacheKey, cachedResponse);
        this.chartCacheExpiry.set(cacheKey, Date.now() + this.CHART_CACHE_TTL);
        return cachedResponse;
      }

      // Check if we should throttle this request
      if (requestThrottler.shouldThrottle('chart')) {
        logger.debug(`Throttling chart request for ${tokenMint} (${timeframe})`);

        // Generate mock data if throttled
        const mockData = this._generateMockChartData(tokenMint, timeframe);
        mockData.source = 'throttled';

        // Cache the mock data
        this.chartCache.set(cacheKey, mockData);
        this.chartCacheExpiry.set(cacheKey, Date.now() + (60 * 1000)); // Short TTL for throttled data

        return mockData;
      }

      // Try to get from Redis cache as last resort before API call
      const redisCacheKey = `jupiter:chart:${tokenMint}:${timeframe}`;
      const cacheTTL = interval === '1m' ? 60 : CHART_CACHE_TTL; // 1 minute cache for 1m interval

      try {
        // Track Redis request
        this.redisRequestCount++;

        const cachedData = await upstashClient.get(redisCacheKey);
        if (cachedData && typeof cachedData === 'string') {
          try {
            const parsedData = JSON.parse(cachedData);

            // Update both caches
            this.chartCache.set(cacheKey, parsedData);
            this.chartCacheExpiry.set(cacheKey, Date.now() + this.CHART_CACHE_TTL);
            requestThrottler.cacheResponse('chart', throttlerKey, parsedData);

            return parsedData;
          } catch (parseError) {
            logger.debug(`Error parsing cache data for ${redisCacheKey}`, parseError);
            // Delete invalid cache entry
            await upstashClient.del(redisCacheKey);
          }
        }
      } catch (cacheError) {
        // Just log at debug level and continue
        logger.debug(`Cache error for ${redisCacheKey}`);
      }

      // Find token in our list if symbol is provided
      let mintAddress = tokenMint;
      if (!tokenMint.includes('1111')) {
        const token = this.tokenList.find(t => t.symbol === tokenMint);
        if (token) {
          mintAddress = token.address;
        }
      }

      let chartData;

      // Try to get real price data from Jupiter API
      try {
        // For SOL and other major tokens, we can use Jupiter's price API
        if (mintAddress === 'So11111111111111111111111111111111111111112' ||
            mintAddress === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v' || // USDC
            mintAddress === 'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So' || // mSOL
            mintAddress === 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263') { // BONK

          // Get price history from Jupiter API
          const now = Math.floor(Date.now() / 1000);
          const startTime = this._getStartTimeForTimeframe(now, timeframe);

          // Track API request
          this.apiRequestCount.chart++;
          this.apiRequestCount.total++;

          // Use Jupiter's price API with updated endpoint and timeout
          const response = await axios.get(`https://price.jup.ag/v4/price/history?ids=${mintAddress}&interval=${interval}&start_time=${startTime}&end_time=${now}`, {
            timeout: 8000 // 8 second timeout (chart data can be larger)
          });

          if (response.data && response.data.data && response.data.data[mintAddress]) {
            const priceData = response.data.data[mintAddress];
            chartData = this._convertJupiterDataToChartData(priceData, timeframe);
            chartData.source = 'api';
          } else {
            throw new Error('Invalid response format from Jupiter API');
          }
        } else {
          // For custom tokens or tokens not supported by Jupiter's price API,
          // we'll try to get data from another source or fall back to mock data

          // Check if this is a custom token on our platform
          const isCustomToken = await this._isCustomToken(mintAddress);

          if (isCustomToken) {
            // Get custom token price history from our database
            chartData = await this._getCustomTokenChartData(mintAddress, timeframe);
            chartData.source = 'custom';
          } else {
            // Fall back to mock data for unsupported tokens
            logger.debug(`Using mock data for token ${tokenMint} (${mintAddress})`);
            chartData = this._generateMockChartData(tokenMint, timeframe);
            chartData.source = 'mock';
          }
        }
      } catch (apiError) {
        logger.debug(`Error fetching price data from API for ${tokenMint}`, apiError);
        // Fall back to mock data if API fails
        chartData = this._generateMockChartData(tokenMint, timeframe);
        chartData.source = 'fallback';
      }

      // Update in-memory cache first (most efficient)
      this.chartCache.set(cacheKey, chartData);
      this.chartCacheExpiry.set(cacheKey, Date.now() + this.CHART_CACHE_TTL);

      // Cache in the request throttler
      requestThrottler.cacheResponse('chart', throttlerKey, chartData);

      // Cache in Redis with a longer TTL to reduce future Redis calls
      try {
        // Track Redis request
        this.redisRequestCount++;

        await upstashClient.set(redisCacheKey, JSON.stringify(chartData), { ex: cacheTTL * 2 }); // Double the TTL
      } catch (cacheError) {
        // Just log at debug level and continue
        logger.debug(`Cache error setting ${redisCacheKey}`);
      }

      return chartData;
    } catch (error) {
      logger.error(`Error getting chart data for ${tokenMint}`, error);
      const emptyData = { candles: [], volumes: [], source: 'error' };
      return emptyData;
    }
  }

  /**
   * Generate mock chart data
   * @param {string} symbol - Token symbol
   * @param {string} timeframe - Timeframe
   * @returns {Object} Mock chart data
   * @private
   */
  _generateMockChartData(symbol, timeframe = '1d') {
    const getBasePrice = (symbol) => {
      if (symbol.includes('SOL')) return 150.45;
      if (symbol.includes('USDC')) return 1.00;
      if (symbol.includes('BONK')) return 0.00002;
      if (symbol.includes('JUP')) return 1.78;
      if (symbol.includes('RAY')) return 0.47;
      return 1.00;
    };

    const basePrice = getBasePrice(symbol);
    const volatility = basePrice * 0.02; // 2% volatility

    const now = Math.floor(Date.now() / 1000); // Current time in seconds
    let timeStep;
    let dataPoints;

    // Set time step based on timeframe
    if (timeframe === '1h') {
      timeStep = 60; // 1 minute
      dataPoints = 60;
    } else if (timeframe === '4h') {
      timeStep = 240; // 4 minutes
      dataPoints = 60;
    } else if (timeframe === '1d') {
      timeStep = 900; // 15 minutes
      dataPoints = 96;
    } else if (timeframe === '1w') {
      timeStep = 3600; // 1 hour
      dataPoints = 168;
    } else if (timeframe === '1m') {
      timeStep = 14400; // 4 hours
      dataPoints = 180;
    } else {
      timeStep = 900; // 15 minutes
      dataPoints = 96;
    }

    // Generate candles
    const candles = [];
    let lastClose = basePrice;
    let lastVolume = Math.random() * 1000000;

    for (let i = 0; i < dataPoints; i++) {
      const time = now - (dataPoints - i) * timeStep;

      // Generate random price movement
      const change = (Math.random() - 0.5) * 2 * volatility;
      const open = lastClose;
      const close = Math.max(0.00001, open + change);

      // High and low should be beyond open/close
      const high = Math.max(open, close) + Math.random() * volatility;
      const low = Math.min(open, close) - Math.random() * volatility;

      // Volume should be somewhat correlated with price change
      const volumeChange = (Math.random() - 0.5) * 0.2; // 20% max change
      const volume = Math.max(100, lastVolume * (1 + volumeChange));
      lastVolume = volume;

      candles.push({
        time,
        open,
        high,
        low,
        close,
        volume
      });

      lastClose = close;
    }

    return {
      candles,
      volumes: candles.map(candle => ({
        time: candle.time,
        value: candle.volume,
        color: candle.close >= candle.open ? '#26a69a' : '#ef5350'
      }))
    };
  }

  /**
   * Convert timeframe to interval for Jupiter API
   * @param {string} timeframe - Timeframe (1h, 4h, 1d, 1w, 1m)
   * @returns {string} Interval for Jupiter API
   * @private
   */
  _timeframeToInterval(timeframe) {
    switch (timeframe) {
      case '1h': return '1m'; // 1 minute intervals for 1 hour
      case '4h': return '5m'; // 5 minute intervals for 4 hours
      case '1d': return '15m'; // 15 minute intervals for 1 day
      case '1w': return '1h'; // 1 hour intervals for 1 week
      case '1m': return '4h'; // 4 hour intervals for 1 month
      default: return '15m'; // Default to 15 minute intervals
    }
  }

  /**
   * Get start time for timeframe
   * @param {number} endTime - End time in seconds
   * @param {string} timeframe - Timeframe (1h, 4h, 1d, 1w, 1m)
   * @returns {number} Start time in seconds
   * @private
   */
  _getStartTimeForTimeframe(endTime, timeframe) {
    switch (timeframe) {
      case '1h': return endTime - 3600; // 1 hour ago
      case '4h': return endTime - 14400; // 4 hours ago
      case '1d': return endTime - 86400; // 1 day ago
      case '1w': return endTime - 604800; // 1 week ago
      case '1m': return endTime - 2592000; // 30 days ago
      default: return endTime - 86400; // Default to 1 day ago
    }
  }

  /**
   * Convert Jupiter price history data to chart data
   * @param {Object} priceData - Price data from Jupiter API
   * @param {string} timeframe - Timeframe
   * @returns {Object} Chart data
   * @private
   */
  _convertJupiterDataToChartData(priceData, timeframe) {
    if (!priceData || !priceData.items || !Array.isArray(priceData.items)) {
      return { candles: [], volumes: [] };
    }

    // Sort items by time
    const sortedItems = [...priceData.items].sort((a, b) => a.unixTime - b.unixTime);

    // Generate candles from price data
    const candles = [];
    const volumes = [];

    // Track the last close price for gap filling
    let lastClose = null;

    for (let i = 0; i < sortedItems.length; i++) {
      const item = sortedItems[i];
      const time = item.unixTime;

      // If we have a price, create a candle
      if (item.price !== undefined && item.price !== null) {
        // For the first item, use the price for all OHLC values
        // For subsequent items, use more realistic values
        let open, high, low, close;

        if (i === 0 || lastClose === null) {
          open = item.price;
          high = item.price * 1.001; // Slightly higher
          low = item.price * 0.999; // Slightly lower
          close = item.price;
        } else {
          // Use the last close as this candle's open
          open = lastClose;
          close = item.price;
          high = Math.max(open, close) * 1.001;
          low = Math.min(open, close) * 0.999;
        }

        // Generate a realistic volume based on price and timeframe
        const volume = this._generateRealisticVolume(item.price, timeframe);

        candles.push({
          time,
          open,
          high,
          low,
          close,
          volume
        });

        volumes.push({
          time,
          value: volume,
          color: close >= open ? '#26a69a' : '#ef5350'
        });

        lastClose = close;
      }
    }

    return { candles, volumes };
  }

  /**
   * Generate realistic volume based on price and timeframe
   * @param {number} price - Token price
   * @param {string} timeframe - Timeframe
   * @returns {number} Volume
   * @private
   */
  _generateRealisticVolume(price, timeframe) {
    // Base volume depends on price - lower priced tokens tend to have higher volume
    let baseVolume;
    if (price < 0.0001) baseVolume = 1000000000;
    else if (price < 0.01) baseVolume = 10000000;
    else if (price < 1) baseVolume = 1000000;
    else if (price < 10) baseVolume = 100000;
    else if (price < 100) baseVolume = 10000;
    else baseVolume = 1000;

    // Adjust volume based on timeframe
    let timeMultiplier;
    switch (timeframe) {
      case '1h': timeMultiplier = 0.1; break;
      case '4h': timeMultiplier = 0.4; break;
      case '1d': timeMultiplier = 1; break;
      case '1w': timeMultiplier = 7; break;
      case '1m': timeMultiplier = 30; break;
      default: timeMultiplier = 1;
    }

    // Add some randomness
    const randomFactor = 0.5 + Math.random();

    return Math.floor(baseVolume * timeMultiplier * randomFactor);
  }

  // In-memory cache for custom token checks to reduce Redis calls
  #customTokenCache = new Map();
  #customTokenCacheTTL = new Map();

  /**
   * Check if a token is a custom token on our platform
   * @param {string} mintAddress - Token mint address
   * @returns {Promise<boolean>} Whether the token is a custom token
   * @private
   */
  async _isCustomToken(mintAddress) {
    try {
      // Check in-memory cache first (most efficient)
      if (this.#customTokenCache.has(mintAddress)) {
        const expiry = this.#customTokenCacheTTL.get(mintAddress);
        if (expiry > Date.now()) {
          return this.#customTokenCache.get(mintAddress);
        }
        // Expired, remove from cache
        this.#customTokenCache.delete(mintAddress);
        this.#customTokenCacheTTL.delete(mintAddress);
      }

      // Simple check for known patterns without hitting Redis
      if (mintAddress.includes('WBTOKEN')) {
        // Cache the result in memory
        this.#customTokenCache.set(mintAddress, true);
        this.#customTokenCacheTTL.set(mintAddress, Date.now() + (3600 * 1000)); // 1 hour TTL
        return true;
      }

      // Check token list in memory
      const isCustomFromList = this.tokenList.some(t =>
        t.address === mintAddress &&
        (t.name?.includes('WBTOKEN') || t.symbol?.includes('WBTOKEN')));

      if (isCustomFromList) {
        // Cache the result in memory
        this.#customTokenCache.set(mintAddress, true);
        this.#customTokenCacheTTL.set(mintAddress, Date.now() + (3600 * 1000)); // 1 hour TTL
        return true;
      }

      // Only check Redis as a last resort
      try {
        const cacheKey = `custom_token:${mintAddress}`;
        const cachedResult = await upstashClient.get(cacheKey);

        if (cachedResult !== null) {
          const isCustom = cachedResult === 'true';
          // Update in-memory cache
          this.#customTokenCache.set(mintAddress, isCustom);
          this.#customTokenCacheTTL.set(mintAddress, Date.now() + (3600 * 1000)); // 1 hour TTL
          return isCustom;
        }

        // Not found in Redis, assume not custom
        const isCustom = false;

        // Cache in memory
        this.#customTokenCache.set(mintAddress, isCustom);
        this.#customTokenCacheTTL.set(mintAddress, Date.now() + (3600 * 1000)); // 1 hour TTL

        // Cache in Redis with a longer TTL to reduce future Redis calls
        try {
          await upstashClient.set(cacheKey, isCustom ? 'true' : 'false', { ex: 86400 }); // Cache for 24 hours
        } catch (cacheError) {
          // Just log at debug level and continue
          console.debug(`Cache error setting custom token ${mintAddress}`);
        }

        return isCustom;
      } catch (redisError) {
        // Redis error, use in-memory check only
        console.debug(`Redis error checking custom token ${mintAddress}`);
        const isCustom = false;

        // Cache in memory
        this.#customTokenCache.set(mintAddress, isCustom);
        this.#customTokenCacheTTL.set(mintAddress, Date.now() + (3600 * 1000)); // 1 hour TTL

        return isCustom;
      }
    } catch (error) {
      console.error(`Error checking if ${mintAddress} is a custom token:`, error);
      return false;
    }
  }

  /**
   * Get chart data for a custom token
   * @param {string} mintAddress - Token mint address
   * @param {string} timeframe - Timeframe
   * @returns {Promise<Object>} Chart data
   * @private
   */
  async _getCustomTokenChartData(mintAddress, timeframe) {
    try {
      // In a real implementation, you would fetch from your database
      // For now, we'll generate more realistic mock data for custom tokens

      // Use a consistent seed based on the mint address for deterministic results
      const seed = mintAddress.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
      const random = (min, max) => {
        const x = Math.sin(seed * 9999) * 10000;
        const r = x - Math.floor(x);
        return min + r * (max - min);
      };

      // Generate a base price that's consistent for this token
      const basePrice = random(0.1, 10);

      // Generate candles with a trend
      const now = Math.floor(Date.now() / 1000);
      const startTime = this._getStartTimeForTimeframe(now, timeframe);
      const interval = this._timeframeToInterval(timeframe);

      // Calculate number of data points based on timeframe and interval
      let dataPoints;
      switch (interval) {
        case '1m': dataPoints = (now - startTime) / 60; break;
        case '5m': dataPoints = (now - startTime) / 300; break;
        case '15m': dataPoints = (now - startTime) / 900; break;
        case '1h': dataPoints = (now - startTime) / 3600; break;
        case '4h': dataPoints = (now - startTime) / 14400; break;
        default: dataPoints = 100;
      }

      // Limit to a reasonable number
      dataPoints = Math.min(Math.ceil(dataPoints), 500);

      // Generate a trend direction (up or down)
      const trendDirection = random(0, 1) > 0.5 ? 1 : -1;
      const trendStrength = random(0.0001, 0.001); // How strong the trend is

      const candles = [];
      const volumes = [];
      let lastClose = basePrice;

      for (let i = 0; i < dataPoints; i++) {
        const time = startTime + ((now - startTime) / dataPoints) * i;

        // Generate price movement with trend
        const trendComponent = trendDirection * trendStrength * i;
        const randomComponent = (random(0, 1) - 0.5) * basePrice * 0.02; // 2% random movement

        const open = lastClose;
        const close = Math.max(0.00001, open + trendComponent + randomComponent);
        const high = Math.max(open, close) * (1 + random(0, 0.01)); // Up to 1% higher
        const low = Math.min(open, close) * (1 - random(0, 0.01)); // Up to 1% lower

        // Generate volume
        const volume = this._generateRealisticVolume(close, timeframe) * (1 + random(-0.5, 0.5));

        candles.push({ time, open, high, low, close, volume });
        volumes.push({
          time,
          value: volume,
          color: close >= open ? '#26a69a' : '#ef5350'
        });

        lastClose = close;
      }

      return { candles, volumes };
    } catch (error) {
      console.error(`Error getting custom token chart data for ${mintAddress}:`, error);
      return this._generateMockChartData(mintAddress, timeframe);
    }
  }

  /**
   * Generate mock price for a token
   * @param {string} symbol - Token symbol
   * @returns {number} Mock price
   * @private
   */
  _generateMockPrice(symbol) {
    // Base price depends on the token
    let basePrice;
    if (symbol.includes('SOL')) basePrice = 150.45;
    else if (symbol.includes('USDC')) basePrice = 1.00;
    else if (symbol.includes('BONK')) basePrice = 0.00002;
    else if (symbol.includes('JUP')) basePrice = 1.78;
    else if (symbol.includes('RAY')) basePrice = 0.47;
    else if (symbol.includes('WBTOKEN')) {
      // Generate a consistent price for custom tokens
      const seed = symbol.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
      basePrice = Math.abs((Math.sin(seed * 9999) * 10000) % 10); // 0-10 range
    }
    else basePrice = 1.00 + (Math.random() * 5); // Random price between 1 and 6

    // Add a small random variation for real-time feel
    const variation = basePrice * (0.98 + (Math.random() * 0.04)); // ±2% variation

    return Math.max(0.00001, variation);
  }

  /**
   * Search for tokens
   * @param {string} query - Search query
   * @param {number} limit - Number of results to return
   * @returns {Promise<Array>} Array of matching tokens
   */
  async searchTokens(query, limit = 20) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      if (!query || query.trim() === '') {
        return this.getPopularTokens(limit);
      }

      // Normalize query
      const normalizedQuery = query.toLowerCase().trim();

      // Search in our token list
      const matchingTokens = this.tokenList
        .filter(token =>
          token.symbol.toLowerCase().includes(normalizedQuery) ||
          token.name.toLowerCase().includes(normalizedQuery) ||
          token.address.toLowerCase() === normalizedQuery
        )
        .slice(0, limit);

      // Get prices for matching tokens
      const tokenMints = matchingTokens.map(token => token.address);
      const priceData = await this.getMultipleTokenPrices(tokenMints);

      // Format tokens with prices
      return matchingTokens.map(token => {
        const price = priceData[token.address] ? priceData[token.address].price : 0;

        return {
          id: token.address,
          name: token.name,
          symbol: token.symbol,
          description: token.tags ? token.tags.join(', ') : '',
          logoUrl: token.logoURI,
          price: price,
          volume: 0, // Jupiter doesn't provide this directly
          marketCap: 0 // Jupiter doesn't provide this directly
        };
      });
    } catch (error) {
      console.error('Error searching tokens:', error);
      return [];
    }
  }
}

module.exports = new JupiterApiService();
