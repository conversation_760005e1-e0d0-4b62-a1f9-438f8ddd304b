const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const authController = require('../controllers/authController');
const { auth, checkSession } = require('../middleware/auth');
const { loginLimiter, passwordResetLimiter } = require('../middleware/rateLimiter');
const cookieParser = require('cookie-parser');
const { uploadProfilePicture } = require('../middleware/profilePictureUpload');

// Use cookie parser middleware
router.use(cookieParser());

// Validation middleware
const registerValidation = [
  body('username').trim().isLength({ min: 3, max: 20 }).withMessage('Username must be between 3 and 20 characters'),
  body('email').isEmail().withMessage('Please enter a valid email'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number and one special character'),
  body('confirmPassword').custom((value, { req }) => {
    if (value !== req.body.password) {
      throw new Error('Password confirmation does not match password');
    }
    return true;
  })
];

const loginValidation = [
  body('email').isEmail().withMessage('Please enter a valid email'),
  body('password').notEmpty().withMessage('Password is required')
];

// Routes
router.post('/register', registerValidation, authController.register);
router.post('/verify-email', authController.verifyEmail);
router.post('/login', loginValidation, loginLimiter, authController.login);
router.post('/refresh', authController.refreshToken);
// Make logout route accessible without authentication
router.post('/logout', (req, res, next) => {
  console.log('Logout route accessed');
  // Set CORS headers explicitly for this route
  res.header('Access-Control-Allow-Origin', req.headers.origin || 'http://localhost:3000');
  res.header('Access-Control-Allow-Credentials', 'true');
  next();
}, authController.logout);
router.get('/verify', authController.verify);
router.get('/profile', auth, checkSession, authController.getProfile);

// Profile picture routes
router.post('/profile/picture', auth, uploadProfilePicture, authController.updateProfilePicture);
router.delete('/profile/picture/remove', auth, authController.removeProfilePicture);

module.exports = router;
