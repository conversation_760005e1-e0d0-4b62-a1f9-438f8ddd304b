{"name": "@walletconnect/sign-client", "description": "Sign Client for WalletConnect Protocol", "version": "2.19.0", "author": "WalletConnect, Inc. <walletconnect.com>", "homepage": "https://github.com/walletconnect/walletconnect-monorepo/", "license": "Apache-2.0", "main": "dist/index.cjs.js", "module": "dist/index.es.js", "unpkg": "dist/index.umd.js", "types": "dist/types/index.d.ts", "sideEffects": false, "files": ["dist"], "keywords": ["wallet", "walletconnect"], "scripts": {"clean": "rm -rf dist", "build:pre": "npm run clean", "build:types": "tsc", "build:source": "rollup --config rollup.config.js", "build": "npm run build:pre; npm run build:source; npm run build:types", "test:pre": "rm -rf ./test/tmp && mkdir ./test/tmp", "test:run": "vitest run --dir test/sdk", "test:concurrency": "vitest run --dir test/concurrency", "test:xregion": "vitest run --dir test/xregion -- --dangerouslyIgnoreUnhandledErrors --segfault-retry=3", "test": "npm run test:pre; npm run test:run", "test:integration": "vitest run --dir test/sdk/integration", "test:ignoreUnhandled": "npm run test:pre; npm run test:integration -- --dangerouslyIgnoreUnhandledErrors --segfault-retry=3", "test:canary": "vitest run --dir test/canary", "test:lifecycle": "vitest run --dir test/lifecycle -- --dangerouslyIgnoreUnhandledErrors --segfault-retry=3", "canary": "npm run test:pre; npm run test:canary", "loadtest": "npm run test:pre; npm run test:concurrency", "lint": "eslint -c '../../.eslintrc' --fix './src/**/*.ts'", "prettier": "prettier --check '{src,test}/**/*.{js,ts,jsx,tsx}'"}, "dependencies": {"@walletconnect/core": "2.19.0", "@walletconnect/events": "1.0.1", "@walletconnect/heartbeat": "1.2.2", "@walletconnect/jsonrpc-utils": "1.0.8", "@walletconnect/logger": "2.1.2", "@walletconnect/time": "1.0.2", "@walletconnect/types": "2.19.0", "@walletconnect/utils": "2.19.0", "events": "3.3.0"}, "devDependencies": {"@aws-sdk/client-cloudwatch": "3.450.0"}}