/**
 * Balance Service
 * Handles operations related to user balances in the centralized wallet system
 */

const User = require('../models/User');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const solanaService = require('./solanaService');
// const postgresqlService = require('./postgresqlService'); // REMOVED - PostgreSQL no longer used
const platformWalletConfig = require('../config/platformWallet');
const balanceUpdateService = require('./balanceUpdateService');
const transactionReceiptService = require('./transactionReceiptService');
const blockchainMonitorService = require('./blockchainMonitorService');

class BalanceService {
  /**
   * Get a user's balance
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User balance object
   */
  async getUserBalance(userId) {
    try {
      // PostgreSQL removed - using MongoDB-only balance management
      // Return mock balance for now (should be implemented with MongoDB models)
      const mockBalance = {
        userId: userId,
        solBalance: '0',
        tokenBalances: {},
        pendingDeposits: [],
        pendingWithdrawals: [],
        lastUpdated: new Date()
      };

      logger.info(`Retrieved user balance for ${userId} (MongoDB-only mode)`);
      return mockBalance;

      // // Find or create user balance using PostgreSQL service
      // const userBalance = await postgresqlService.getUserBalance(userId);
      // return userBalance;
    } catch (error) {
      logger.error(`Error getting user balance: ${error.message}`);
      throw error;
    }
  }



  /**
   * Process a deposit
   * @param {string} userId - User ID
   * @param {string} txId - Transaction ID
   * @param {number} amount - Deposit amount
   * @param {string} token - Token symbol (default: 'SOL')
   * @returns {Promise<Object>} Updated user balance
   */
  async processDeposit(userId, txId, amount, token = 'SOL') {
    try {
      // Validate amount
      if (!amount || amount <= 0) {
        throw new Error('Invalid deposit amount');
      }

      // Create deposit data
      const depositData = {
        txId,
        amount: amount.toString(),
        token,
        timestamp: new Date(),
        userId
      };

      // PostgreSQL removed - mock pending deposit functionality
      // await postgresqlService.addPendingDeposit(userId, depositData);
      logger.info(`Mock: Added pending deposit for user ${userId} (PostgreSQL removed)`);

      logger.info(`Added pending deposit for user ${userId}: ${amount} ${token}`);

      // Register with blockchain monitor service
      blockchainMonitorService.registerPendingDeposit(userId, depositData);

      // In a real system, you would now monitor the blockchain for this transaction
      // For demo purposes, we'll confirm it immediately
      await this.confirmDeposit(userId, txId);

      // Generate a receipt for the deposit
      await transactionReceiptService.generateDepositReceipt({
        ...depositData,
        status: 'completed'
      });

      // PostgreSQL removed - mock updated balance
      // const userBalance = await postgresqlService.getUserBalance(userId);
      const userBalance = await this.getUserBalance(userId); // Use our mock implementation

      // Send real-time balance update
      await balanceUpdateService.sendBalanceUpdate(userId);

      return userBalance;
    } catch (error) {
      logger.error(`Error processing deposit: ${error.message}`);
      throw error;
    }
  }

  /**
   * Confirm a deposit
   * @param {string} userId - User ID
   * @param {string} txId - Transaction ID
   * @returns {Promise<Object>} Updated user balance
   */
  async confirmDeposit(userId, txId) {
    try {
      // PostgreSQL removed - mock deposit confirmation
      // const userBalance = await postgresqlService.confirmDeposit(userId, txId);
      const userBalance = await this.getUserBalance(userId); // Use our mock implementation

      logger.info(`Confirmed deposit for user ${userId} with transaction ID ${txId} (PostgreSQL removed)`);

      return userBalance;
    } catch (error) {
      logger.error(`Error confirming deposit: ${error.message}`);
      throw error;
    }
  }

  /**
   * Process a withdrawal
   * @param {string} userId - User ID
   * @param {number} amount - Withdrawal amount
   * @param {string} destinationAddress - Destination wallet address
   * @param {string} token - Token symbol (default: 'SOL')
   * @returns {Promise<Object>} Withdrawal details
   */
  async processWithdrawal(userId, amount, destinationAddress, token = 'SOL') {
    try {
      // Validate amount
      if (!amount || amount <= 0) {
        throw new Error('Invalid withdrawal amount');
      }

      // Validate destination address
      if (!destinationAddress) {
        throw new Error('Destination address is required');
      }

      // Check if token is SOL (only supporting SOL for now)
      if (token !== 'SOL') {
        throw new Error('Only SOL withdrawals are supported at this time');
      }

      // Check if user has sufficient balance
      const userBalance = await this.getUserBalance(userId);
      const solBalance = parseFloat(userBalance.solBalance || 0);

      if (solBalance < amount) {
        throw new Error(`Insufficient balance. Available: ${solBalance} SOL`);
      }

      // Generate transaction ID
      const txId = uuidv4();

      // PostgreSQL removed - mock pending withdrawal functionality
      // await postgresqlService.addPendingWithdrawal(userId, {
      //   txId,
      //   amount: amount.toString(),
      //   token,
      //   destinationAddress,
      //   timestamp: new Date()
      // });
      logger.info(`Mock: Added pending withdrawal for user ${userId} (PostgreSQL removed)`);

      logger.info(`Added pending withdrawal for user ${userId}: ${amount} ${token} to ${destinationAddress}`);

      try {
        // Deduct from user's balance first (optimistic approach)
        await this.updateSolBalance(userId, -amount, {
          type: 'withdrawal',
          txId,
          description: `Withdrawal of ${amount} SOL to ${destinationAddress}`
        });

        // Send the actual transaction to the blockchain
        let blockchainTxId;
        try {
          // Ensure Solana service is initialized
          if (!solanaService.initialized) {
            await solanaService.initialize();
          }

          // Send the transaction
          blockchainTxId = await solanaService.sendTransaction(destinationAddress, amount, token);
          logger.info(`Sent withdrawal transaction to blockchain: ${blockchainTxId}`);
        } catch (sendError) {
          // If sending fails, revert the balance deduction
          logger.error(`Error sending withdrawal transaction: ${sendError.message}`);
          await this.updateSolBalance(userId, amount, {
            type: 'withdrawal_reversal',
            txId: uuidv4(),
            relatedTxId: txId,
            description: `Reversal of failed withdrawal: ${sendError.message}`
          });
          throw sendError;
        }

        // PostgreSQL removed - mock withdrawal status update
        // await postgresqlService.updateWithdrawalStatus(userId, txId, 'completed', { blockchainTxId });
        logger.info(`Mock: Updated withdrawal status to completed for user ${userId} (PostgreSQL removed)`);

        // Generate a receipt for the withdrawal
        const withdrawalReceipt = await transactionReceiptService.generateWithdrawalReceipt({
          txId,
          blockchainTxId,
          userId,
          amount,
          token,
          destinationAddress,
          status: 'completed'
        });

        // Send real-time balance update
        await balanceUpdateService.sendBalanceUpdate(userId);

        // Return the completed withdrawal details with receipt
        return {
          txId,
          blockchainTxId,
          userId,
          amount,
          token,
          destinationAddress,
          status: 'completed',
          receipt: withdrawalReceipt
        };
      } catch (processingError) {
        // PostgreSQL removed - mock withdrawal status update
        // await postgresqlService.updateWithdrawalStatus(userId, txId, 'failed', {
        //   error: processingError.message
        // });
        logger.info(`Mock: Updated withdrawal status to failed for user ${userId} (PostgreSQL removed)`);

        logger.error(`Withdrawal processing failed: ${processingError.message}`);
        throw new Error(`Withdrawal failed: ${processingError.message}`);
      }
    } catch (error) {
      logger.error(`Error processing withdrawal: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update a user's SOL balance
   * @param {string} userId - User ID
   * @param {number} amount - Amount to add (positive) or subtract (negative)
   * @param {Object} txData - Transaction data for history
   * @returns {Promise<Object>} Updated user balance
   */
  async updateSolBalance(userId, amount, txData = {}) {
    try {
      // PostgreSQL removed - mock SOL balance update
      // const userBalance = await postgresqlService.updateSolBalance(userId, amount);
      const userBalance = await this.getUserBalance(userId); // Use our mock implementation

      // PostgreSQL removed - mock balance transaction
      // await postgresqlService.addBalanceTransaction({
      //   userId,
      //   type: txData.type || 'adjustment',
      //   token: 'SOL',
      //   amount: amount.toString(),
      //   txId: txData.txId || uuidv4(),
      //   relatedTxId: txData.relatedTxId,
      //   description: txData.description || 'Balance adjustment',
      //   status: 'completed'
      // });
      logger.info(`Mock: Updated SOL balance and added transaction for user ${userId} (PostgreSQL removed)`);

      logger.info(`Updated SOL balance for user ${userId}: ${amount > 0 ? '+' : ''}${amount}`);

      // Send real-time balance update via WebSocket
      await balanceUpdateService.sendBalanceUpdate(userId);

      return userBalance;
    } catch (error) {
      logger.error(`Error updating SOL balance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update a user's token balance
   * @param {string} userId - User ID
   * @param {string} token - Token symbol or mint address
   * @param {number} amount - Amount to add (positive) or subtract (negative)
   * @param {Object} txData - Transaction data for history
   * @returns {Promise<Object>} Updated user balance
   */
  async updateTokenBalance(userId, token, amount, txData = {}) {
    try {
      // PostgreSQL removed - mock token balance update
      // const userBalance = await postgresqlService.updateTokenBalance(userId, token, amount);
      const userBalance = await this.getUserBalance(userId); // Use our mock implementation

      // PostgreSQL removed - mock balance transaction
      // await postgresqlService.addBalanceTransaction({
      //   userId,
      //   type: txData.type || 'adjustment',
      //   token,
      //   amount: amount.toString(),
      //   txId: txData.txId || uuidv4(),
      //   relatedTxId: txData.relatedTxId,
      //   description: txData.description || 'Balance adjustment',
      //   status: 'completed'
      // });
      logger.info(`Mock: Updated ${token} balance and added transaction for user ${userId} (PostgreSQL removed)`);

      logger.info(`Updated ${token} balance for user ${userId}: ${amount > 0 ? '+' : ''}${amount}`);

      // Send real-time balance update via WebSocket
      await balanceUpdateService.sendBalanceUpdate(userId);

      return userBalance;
    } catch (error) {
      logger.error(`Error updating token balance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get deposit address for a user
   * This would typically generate a unique deposit address or instruction
   * For this implementation, we'll just return the platform's main wallet address
   * @param {string} userId - User ID
   * @param {string} token - Token symbol (default: 'SOL')
   * @returns {Promise<Object>} Deposit address details
   */
  async getDepositAddress(userId, token = 'SOL') {
    try {
      // Get the platform wallet address from the centralized configuration
      let platformWalletAddress;

      try {
        // First try to get from solanaService
        if (solanaService && typeof solanaService.getPlatformWalletAddress === 'function') {
          platformWalletAddress = solanaService.getPlatformWalletAddress();
          logger.info(`Using platform wallet address from solanaService: ${platformWalletAddress}`);
        }
        // Then try to get directly from platformWalletConfig
        else {
          platformWalletAddress = platformWalletConfig.getPlatformWalletAddress();
          logger.info(`Using platform wallet address from platformWalletConfig: ${platformWalletAddress}`);
        }
      } catch (walletError) {
        logger.warn(`Error getting platform wallet address: ${walletError.message}`);
        // Use fallback address if both methods fail
        platformWalletAddress = 'CVHXGtveLFr54yPsc4R6PgUhLLYCY5YQbXiqcLqEdkcu'; // Default fallback address
        logger.warn(`Using fallback platform wallet address: ${platformWalletAddress}`);
      }

      // Log the address we're using
      logger.info(`Using deposit address for user ${userId}: ${platformWalletAddress}`);

      // Create the deposit info object
      const depositInfo = {
        userId,
        token,
        depositAddress: platformWalletAddress,
        memo: `DEPOSIT-${userId}`, // Use memo to identify the user
        instructions: `Send ${token} to this address with the memo "${userId}" to credit your account.`
      };

      // Double-check that we have a valid address
      if (!depositInfo.depositAddress || depositInfo.depositAddress.includes('Unavailable')) {
        depositInfo.depositAddress = 'CVHXGtveLFr54yPsc4R6PgUhLLYCY5YQbXiqcLqEdkcu'; // Ensure we have a fallback
        logger.warn(`Had to use fallback address for user ${userId}`);
      }

      return depositInfo;
    } catch (error) {
      logger.error(`Error getting deposit address: ${error.message}`);

      // Even in case of error, return a valid deposit info
      return {
        userId,
        token,
        depositAddress: 'CVHXGtveLFr54yPsc4R6PgUhLLYCY5YQbXiqcLqEdkcu', // Fallback address
        memo: `DEPOSIT-${userId}`,
        instructions: `The deposit service is currently experiencing issues. Please try again later or contact support.`
      };
    }
  }

  /**
   * Get transaction history for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options (limit, offset, type)
   * @returns {Promise<Array>} Transaction history
   */
  async getTransactionHistory(userId, options = {}) {
    try {
      // PostgreSQL removed - mock transaction history
      // const result = await postgresqlService.getBalanceTransactions(userId, options);
      const mockResult = {
        rows: [], // Empty transaction history for now
        count: 0
      };

      logger.info(`Mock: Retrieved transaction history for user ${userId} (PostgreSQL removed)`);

      return {
        transactions: mockResult.rows,
        total: mockResult.count,
        limit: options.limit || 50,
        offset: options.offset || 0
      };
    } catch (error) {
      logger.error(`Error getting transaction history: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get all balances for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} All user balances
   */
  async getAllBalances(userId) {
    try {
      const userBalance = await this.getUserBalance(userId);

      return {
        userId,
        solBalance: parseFloat(userBalance.solBalance),
        tokenBalances: userBalance.tokenBalances,
        pendingDeposits: userBalance.pendingDeposits,
        pendingWithdrawals: userBalance.pendingWithdrawals
      };
    } catch (error) {
      logger.error(`Error getting all balances: ${error.message}`);
      throw error;
    }
  }
}

// Create and export singleton instance
const balanceService = new BalanceService();
module.exports = balanceService;
