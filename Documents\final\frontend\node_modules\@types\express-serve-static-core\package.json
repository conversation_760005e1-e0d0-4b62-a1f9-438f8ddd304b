{"name": "@types/express-serve-static-core", "version": "5.0.6", "description": "TypeScript definitions for express-serve-static-core", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>", "url": "https://github.com/micksatana"}, {"name": "<PERSON>", "githubUsername": "JoseLion", "url": "https://github.com/JoseLion"}, {"name": "<PERSON>", "githubUsername": "dwrss", "url": "https://github.com/dwrss"}, {"name": "<PERSON>", "githubUsername": "andoshin11", "url": "https://github.com/andoshin11"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-serve-static-core"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "44d5cdf58e5ee1073bddfbf6110c8e09cc6e0712ad27c9ed54d367643bee193b", "typeScriptVersion": "5.0"}