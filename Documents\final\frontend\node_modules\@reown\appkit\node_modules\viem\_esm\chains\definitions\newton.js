import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const newton = /*#__PURE__*/ defineChain({
    id: 1012,
    name: '<PERSON>',
    nativeCurrency: {
        name: '<PERSON>',
        symbol: 'NEW',
        decimals: 18,
    },
    rpcUrls: {
        default: {
            http: ['https://global.rpc.mainnet.newtonproject.org'],
        },
    },
    blockExplorers: {
        default: {
            name: 'NewFi explorer',
            url: 'https://explorer.newtonproject.org/',
        },
    },
    testnet: false,
});
//# sourceMappingURL=newton.js.map