# Environment Setup Guide

This document explains how the environment configuration works in this application.

## Environment Files Structure

We use a structured approach to environment variables with three main files:

1. **`.env`** - Contains all API URLs and endpoints used in both environments
2. **`.env.development`** - Contains development-specific settings
3. **`.env.production`** - Contains production-specific settings

## How It Works

The application loads environment variables in this order:

1. First, it loads the main `.env` file with all API URLs and endpoints
2. Then, based on the `NODE_ENV` value, it loads either:
   - `.env.development` for development mode
   - `.env.production` for production mode

This way, environment-specific settings override the base settings, but all environments share the same API URLs.

## Switching Environments

To switch between development and production environments, use the `switch-env.js` script:

```bash
# Switch to development mode
node switch-env.js development

# Switch to production mode
node switch-env.js production
```

This script creates a `.env.node_env` file that sets the `NODE_ENV` variable, which determines which environment-specific file gets loaded.

## File Contents

### `.env`

Contains all actual API URLs and endpoints used in both environments:
- MongoDB connection string
- PostgreSQL connection string
- Redis connection details
- Solana RPC URL
- Platform wallet address
- Order matching engine path

### `.env.development`

Contains development-specific settings:
- Development server configuration
- Development security settings (not secure, for development only)
- Feature flags for development
- Relaxed rate limiting
- Debug-level logging

### `.env.production`

Contains production-specific settings:
- Production server configuration
- Placeholders for secure credentials (to be replaced in actual deployment)
- Feature flags for production
- Strict rate limiting
- Production-level logging
- TLS configuration

## Security Notes

For actual production deployments:
1. Do not store sensitive credentials in `.env` files
2. Set sensitive credentials directly in your hosting platform's environment variables
3. Use the `generate:secrets` script to create secure random strings for secrets:
   ```bash
   npm run generate:secrets
   ```

## Starting the Server

### Development Mode
```bash
npm run start:dev
```

### Production Mode
```bash
npm run start:production
```

The production startup script performs pre-flight checks to ensure all required environment variables are set and that critical services are available.
