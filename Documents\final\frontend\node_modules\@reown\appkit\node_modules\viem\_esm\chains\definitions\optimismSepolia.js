import { chainConfig } from '../../op-stack/chainConfig.js';
import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
const sourceId = 11_155_111; // sepolia
export const optimismSepolia = /*#__PURE__*/ defineChain({
    ...chainConfig,
    id: 11155420,
    name: 'OP Sepolia',
    nativeCurrency: { name: 'Sepolia Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://sepolia.optimism.io'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Blockscout',
            url: 'https://optimism-sepolia.blockscout.com',
            apiUrl: 'https://optimism-sepolia.blockscout.com/api',
        },
    },
    contracts: {
        ...chainConfig.contracts,
        disputeGameFactory: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        l2OutputOracle: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        multicall3: {
            address: '******************************************',
            blockCreated: 1620204,
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '0xFBb0621E0B23b5478B630BD55a5f21f67730B0F1',
            },
        },
    },
    testnet: true,
    sourceId,
});
//# sourceMappingURL=optimismSepolia.js.map