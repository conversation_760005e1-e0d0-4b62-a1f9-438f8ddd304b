/**
 * Balance Transaction Model (PostgreSQL)
 * 
 * Stores all balance transactions for the centralized wallet system
 */

const { sequelize, Sequelize } = require('../../config/postgresql');

const BalanceTransaction = sequelize.define('BalanceTransaction', {
  // Transaction ID (primary key)
  id: {
    type: Sequelize.UUID,
    defaultValue: Sequelize.UUIDV4,
    primaryKey: true
  },
  
  // User ID (from MongoDB)
  userId: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Transaction type (deposit, withdrawal, trade, fee, reward, adjustment)
  type: {
    type: Sequelize.ENUM('deposit', 'withdrawal', 'trade', 'fee', 'reward', 'adjustment', 'refund'),
    allowNull: false
  },
  
  // Token symbol or mint address
  token: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Amount (as string to preserve precision)
  amount: {
    type: Sequelize.STRING,
    allowNull: false
  },
  
  // Fee amount (as string)
  fee: {
    type: Sequelize.STRING,
    defaultValue: '0',
    allowNull: false
  },
  
  // Blockchain transaction ID (if applicable)
  txId: {
    type: Sequelize.STRING,
    allowNull: true
  },
  
  // Related transaction ID (for refunds, etc.)
  relatedTxId: {
    type: Sequelize.STRING,
    allowNull: true
  },
  
  // Description
  description: {
    type: Sequelize.STRING,
    allowNull: true
  },
  
  // Status (pending, completed, failed)
  status: {
    type: Sequelize.ENUM('pending', 'completed', 'failed'),
    defaultValue: 'completed',
    allowNull: false
  },
  
  // Additional data (JSONB)
  metadata: {
    type: Sequelize.JSONB,
    defaultValue: {},
    allowNull: false
  }
}, {
  // Table configuration
  tableName: 'balance_transactions',
  timestamps: true,
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['type']
    },
    {
      fields: ['token']
    },
    {
      fields: ['txId']
    },
    {
      fields: ['createdAt']
    }
  ]
});

module.exports = BalanceTransaction;
