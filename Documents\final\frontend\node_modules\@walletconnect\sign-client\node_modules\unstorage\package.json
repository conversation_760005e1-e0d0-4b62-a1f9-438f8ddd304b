{"name": "unstorage", "version": "1.16.0", "description": "Universal Storage Layer", "homepage": "https://unstorage.unjs.io", "repository": "unjs/unstorage", "license": "MIT", "sideEffects": false, "exports": {"./drivers/*": {"types": "./drivers/*.d.ts", "import": "./drivers/*.mjs", "require": "./drivers/*.cjs"}, ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./server": {"types": "./dist/server.d.ts", "import": "./dist/server.mjs", "require": "./dist/server.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "drivers", "server.d.ts"], "dependencies": {"anymatch": "^3.1.3", "chokidar": "^4.0.3", "destr": "^2.0.5", "h3": "^1.15.2", "lru-cache": "^10.4.3", "node-fetch-native": "^1.6.6", "ofetch": "^1.4.1", "ufo": "^1.6.1"}, "devDependencies": {"@azure/app-configuration": "^1.9.0", "@azure/cosmos": "^4.3.0", "@azure/data-tables": "^13.3.0", "@azure/identity": "^4.9.1", "@azure/keyvault-secrets": "^4.9.0", "@azure/storage-blob": "^12.27.0", "@capacitor/preferences": "^7.0.1", "@cloudflare/workers-types": "^4.20250427.0", "@deno/kv": "^0.10.0", "@electric-sql/pglite": "^0.3.0", "@libsql/client": "^0.15.4", "@netlify/blobs": "^8.2.0", "@planetscale/database": "^1.19.0", "@types/deno": "^2.2.0", "@types/ioredis-mock": "^8.2.5", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.2", "@upstash/redis": "^1.34.8", "@vercel/blob": "^1.0.0", "@vercel/kv": "^3.0.0", "@vitest/coverage-v8": "^3.1.2", "aws4fetch": "^1.0.20", "azurite": "^3.34.0", "better-sqlite3": "^11.9.1", "changelogen": "^0.6.1", "citty": "^0.1.6", "db0": "^0.3.2", "eslint": "^9.25.1", "eslint-config-unjs": "^0.4.2", "fake-indexeddb": "^6.0.0", "get-port-please": "^3.1.2", "idb-keyval": "^6.2.1", "ioredis": "^5.6.1", "ioredis-mock": "^8.9.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "listhen": "^1.9.0", "mitata": "^1.0.34", "mlly": "^1.7.4", "mongodb": "^6.16.0", "mongodb-memory-server": "^10.1.4", "prettier": "^3.5.3", "scule": "^1.3.0", "types-cloudflare-worker": "^1.2.0", "typescript": "^5.8.3", "unbuild": "^3.5.0", "uploadthing": "^7.6.0", "vite": "^6.3.3", "vitest": "^3.1.2", "wrangler": "^4.13.2"}, "peerDependencies": {"@azure/app-configuration": "^1.8.0", "@azure/cosmos": "^4.2.0", "@azure/data-tables": "^13.3.0", "@azure/identity": "^4.6.0", "@azure/keyvault-secrets": "^4.9.0", "@azure/storage-blob": "^12.26.0", "@capacitor/preferences": "^6.0.3 || ^7.0.0", "@deno/kv": ">=0.9.0", "@netlify/blobs": "^6.5.0 || ^7.0.0 || ^8.1.0", "@planetscale/database": "^1.19.0", "@upstash/redis": "^1.34.3", "@vercel/blob": ">=0.27.1", "@vercel/kv": "^1.0.1", "aws4fetch": "^1.0.20", "db0": ">=0.2.1", "idb-keyval": "^6.2.1", "ioredis": "^5.4.2", "uploadthing": "^7.4.4"}, "peerDependenciesMeta": {"@azure/app-configuration": {"optional": true}, "@azure/cosmos": {"optional": true}, "@azure/data-tables": {"optional": true}, "@azure/identity": {"optional": true}, "@azure/keyvault-secrets": {"optional": true}, "@azure/storage-blob": {"optional": true}, "@capacitor/preferences": {"optional": true}, "@deno/kv": {"optional": true}, "@netlify/blobs": {"optional": true}, "@planetscale/database": {"optional": true}, "@upstash/redis": {"optional": true}, "@vercel/blob": {"optional": true}, "@vercel/kv": {"optional": true}, "aws4fetch": {"optional": true}, "db0": {"optional": true}, "idb-keyval": {"optional": true}, "ioredis": {"optional": true}, "uploadthing": {"optional": true}}, "scripts": {"bench": "jiti test/server.bench.ts", "build": "pnpm gen-drivers && unbuild", "gen-drivers": "jiti scripts/gen-drivers.ts", "dev": "vitest", "lint": "eslint . && prettier -c .", "lint:fix": "eslint . --fix && prettier -w .", "release": "pnpm test && changelogen --release && git push --follow-tags && pnpm publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage", "test:types": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "unstorage": "pnpm jiti src/cli"}}