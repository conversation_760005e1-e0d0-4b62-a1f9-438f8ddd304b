import React from 'react';
import { useNavigate } from 'react-router-dom';

const TokenCard = ({ token }) => {
  const navigate = useNavigate();

  const handleCardClick = () => {
    navigate(`/trade/${token.id}`);
  };

  return (
    <div className="token-card" onClick={handleCardClick} style={{ cursor: 'pointer' }}>
      <div className="token-card-header">
        <div className="token-image-container">
          <img src={token.image} alt={token.name} className="token-image" />
        </div>
        <div className="token-created">Created: {token.created}</div>
      </div>

      <div className="token-info">
        <div className="token-name">
          {token.name} <span className="token-symbol">{token.symbol}</span>
        </div>
        <div className="token-creator">
          <div className="creator-icon"></div>
          <span>created by: <strong>{token.creator}</strong></span>
        </div>
        <div className="token-description">{token.description}</div>
      </div>

      <div className="token-price">${token.price}</div>

      <div className="token-stats">
        <div className="token-market-cap">
          market cap:<span className="token-value">${token.marketCap}</span>
        </div>
        <div className="token-volume">
          volume:<span className="token-value">${token.volume}</span>
        </div>
        {token.trending && <div className="token-trending">trending</div>}
      </div>
    </div>
  );
};

export default TokenCard;
