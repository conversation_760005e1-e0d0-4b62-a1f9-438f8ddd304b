import { chainConfig } from '../../op-stack/chainConfig.js';
import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
const sourceId = 11_155_111; // sepolia
export const inkSepolia = /*#__PURE__*/ defineChain({
    ...chainConfig,
    id: 763373,
    name: 'Ink Sepolia',
    nativeCurrency: { name: 'Sepolia Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc-gel-sepolia.inkonchain.com'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Blockscout',
            url: 'https://explorer-sepolia.inkonchain.com/',
            apiUrl: 'https://explorer-sepolia.inkonchain.com/api/v2',
        },
    },
    contracts: {
        ...chainConfig.contracts,
        multicall3: {
            address: '******************************************',
            blockCreated: 0,
        },
        disputeGameFactory: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
            },
        },
    },
    testnet: true,
    sourceId,
});
//# sourceMappingURL=inkSepolia.js.map