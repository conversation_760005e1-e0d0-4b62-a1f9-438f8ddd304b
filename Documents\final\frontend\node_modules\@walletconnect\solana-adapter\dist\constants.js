/* eslint-disable no-shadow */
export var WalletConnectChainID;
(function (WalletConnectChainID) {
    WalletConnectChainID["Mainnet"] = "solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp";
    WalletConnectChainID["Devnet"] = "solana:EtWTRABZaYq6iMfeYKouRu166VU2xqa1";
    WalletConnectChainID["Deprecated_Mainnet"] = "solana:4sGjMW1sUnHzSxGspuhpqLDx6wiyjNtZ";
    WalletConnectChainID["Deprecated_Devnet"] = "solana:8E9rvCKLFQia2Y35HXjjpWzj8weVo44K";
})(WalletConnectChainID || (WalletConnectChainID = {}));
export var WalletConnectRPCMethods;
(function (WalletConnectRPCMethods) {
    WalletConnectRPCMethods["signTransaction"] = "solana_signTransaction";
    WalletConnectRPCMethods["signMessage"] = "solana_signMessage";
    WalletConnectRPCMethods["signAndSendTransaction"] = "solana_signAndSendTransaction";
    WalletConnectRPCMethods["signAndSendAllTransactions"] = "solana_signAndSendAllTransactions";
    WalletConnectRPCMethods["signAllTransactions"] = "solana_signAllTransactions";
})(WalletConnectRPCMethods || (WalletConnectRPCMethods = {}));
export const SolanaChainIDs = {
    Mainnet: 'solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp',
    Devnet: 'solana:EtWTRABZaYq6iMfeYKouRu166VU2xqa1',
    Deprecated_Mainnet: 'solana:4sGjMW1sUnHzSxGspuhpqLDx6wiyjNtZ',
    Deprecated_Devnet: 'solana:8E9rvCKLFQia2Y35HXjjpWzj8weVo44K'
};
//# sourceMappingURL=constants.js.map