const { PublicKey } = require('@solana/web3.js');
const { BondingCurveClient, CURVE_TYPE_LINEAR, CURVE_TYPE_EXPONENTIAL, CURVE_TYPE_LOGARITHMIC, CURVE_TYPE_BANCOR, PRECISION } = require('../../solana-contracts/js/bonding-curve-client');
const BN = require('bn.js');
const logger = require('../utils/logger');
const { orderMatchingService } = require('./orderMatchingServiceTCP');
const walletIntegration = require('../../src/wallet-integration');

class BondingCurveService {
  constructor() {
    // Initialize Solana connection using wallet integration
    this.connection = walletIntegration.initializeConnection(process.env.SOLANA_NETWORK || 'devnet');

    // Initialize program ID
    this.programId = new PublicKey(process.env.BONDING_CURVE_PROGRAM_ID || 'YOUR_PROGRAM_ID_HERE');

    // Initialize wallet if private key is available
    const privateKey = process.env.SOLANA_PRIVATE_KEY;
    if (privateKey) {
      walletIntegration.connectWithPrivateKey(privateKey);
    } else {
      // For development, generate a test wallet
      const testWallet = walletIntegration.generateTestWallet();
      walletIntegration.connectWithKeypair(testWallet);
    }

    // Get the wallet from the integration
    this.wallet = walletIntegration.wallet;

    // Initialize bonding curve client
    this.client = new BondingCurveClient(this.connection, this.programId);

    logger.info(`BondingCurveService initialized with wallet: ${this.wallet.publicKey.toString()}`);
  }

  /**
   * Create a new token with bonding curve
   * @param {Object} tokenData - Token data
   * @param {string} tokenData.name - Token name
   * @param {string} tokenData.symbol - Token symbol
   * @param {number} tokenData.decimals - Token decimals
   * @param {number} tokenData.initialSupply - Initial token supply
   * @param {Object} bondingCurveParams - Bonding curve parameters
   * @param {string} bondingCurveParams.curveType - Curve type (linear, exponential, logarithmic, bancor)
   * @param {number} bondingCurveParams.basePrice - Base price in SOL
   * @param {number} bondingCurveParams.slope - Slope parameter
   * @param {number} bondingCurveParams.reserveRatio - Reserve ratio (for Bancor curves)
   * @param {number} bondingCurveParams.maxSupply - Maximum supply (0 for unlimited)
   * @returns {Promise<Object>} Token data
   */
  async createToken(tokenData, bondingCurveParams) {
    try {
      logger.info(`Creating token: ${tokenData.name} (${tokenData.symbol})`);

      // Convert curve type string to number
      let curveType;
      switch (bondingCurveParams.curveType.toLowerCase()) {
        case 'linear':
          curveType = CURVE_TYPE_LINEAR;
          break;
        case 'exponential':
          curveType = CURVE_TYPE_EXPONENTIAL;
          break;
        case 'logarithmic':
          curveType = CURVE_TYPE_LOGARITHMIC;
          break;
        case 'bancor':
          curveType = CURVE_TYPE_BANCOR;
          break;
        default:
          throw new Error(`Invalid curve type: ${bondingCurveParams.curveType}`);
      }

      // Convert parameters to BN
      const initialSupply = new BN(tokenData.initialSupply * Math.pow(10, tokenData.decimals));
      const basePrice = new BN(bondingCurveParams.basePrice * PRECISION);
      const slope = new BN(bondingCurveParams.slope * PRECISION);
      const reserveRatio = new BN(bondingCurveParams.reserveRatio * PRECISION);
      const maxSupply = new BN(bondingCurveParams.maxSupply * Math.pow(10, tokenData.decimals));

      // Create token
      const result = await this.client.createToken(
        this.wallet,
        tokenData.name,
        tokenData.symbol,
        tokenData.decimals,
        initialSupply,
        curveType,
        basePrice,
        slope,
        reserveRatio,
        maxSupply
      );

      logger.info(`Token created successfully: ${result.mint.toString()}`);

      // Return token data
      return {
        name: tokenData.name,
        symbol: tokenData.symbol,
        decimals: tokenData.decimals,
        initialSupply: tokenData.initialSupply,
        mint: result.mint.toString(),
        metadata: result.metadata.toString(),
        bondingCurve: result.bondingCurve.toString(),
        bondingCurveParams: {
          curveType: bondingCurveParams.curveType,
          basePrice: bondingCurveParams.basePrice,
          slope: bondingCurveParams.slope,
          reserveRatio: bondingCurveParams.reserveRatio,
          maxSupply: bondingCurveParams.maxSupply
        }
      };
    } catch (error) {
      logger.error('Error creating token:', error);
      throw new Error(`Failed to create token: ${error.message}`);
    }
  }

  /**
   * Get current token price
   * @param {string} bondingCurveAddress - Bonding curve account address
   * @returns {Promise<number>} Current token price in SOL
   */
  async getTokenPrice(bondingCurveAddress) {
    try {
      // First try to get the price from the C++ order matching engine
      try {
        const priceData = await orderMatchingService.getTokenPrice(bondingCurveAddress);
        return priceData.price;
      } catch (engineError) {
        logger.warn(`Failed to get price from C++ engine, falling back to Solana: ${engineError.message}`);

        // Fall back to Solana implementation
        const bondingCurve = new PublicKey(bondingCurveAddress);
        const price = await this.client.getCurrentPrice(bondingCurve);

        // Convert from lamports to SOL
        return price.toNumber() / PRECISION.toNumber();
      }
    } catch (error) {
      logger.error(`Error getting token price for ${bondingCurveAddress}:`, error);
      throw new Error(`Failed to get token price: ${error.message}`);
    }
  }

  /**
   * Calculate tokens to buy
   * @param {string} bondingCurveAddress - Bonding curve account address
   * @param {number} paymentAmount - Payment amount in SOL
   * @returns {Promise<number>} Number of tokens that can be bought
   */
  async calculateTokensToBuy(bondingCurveAddress, paymentAmount) {
    try {
      // First try to calculate using the C++ order matching engine
      try {
        const result = await orderMatchingService.submitBondingCurveOrder(
          'buy',
          bondingCurveAddress,
          paymentAmount,
          'simulation' // Use a special wallet address to indicate this is a simulation
        );
        return result.tokenAmount;
      } catch (engineError) {
        logger.warn(`Failed to calculate with C++ engine, falling back to Solana: ${engineError.message}`);

        // Fall back to Solana implementation
        const bondingCurve = new PublicKey(bondingCurveAddress);

        // Convert SOL to lamports
        const paymentLamports = new BN(paymentAmount * PRECISION.toNumber());

        const tokens = await this.client.calculateTokensToBuy(bondingCurve, paymentLamports);

        return tokens.toNumber() / PRECISION.toNumber();
      }
    } catch (error) {
      logger.error(`Error calculating tokens to buy for ${bondingCurveAddress}:`, error);
      throw new Error(`Failed to calculate tokens to buy: ${error.message}`);
    }
  }

  /**
   * Calculate payment for selling tokens
   * @param {string} bondingCurveAddress - Bonding curve account address
   * @param {number} tokenAmount - Token amount to sell
   * @returns {Promise<number>} Payment amount in SOL
   */
  async calculatePaymentForSelling(bondingCurveAddress, tokenAmount) {
    try {
      // First try to calculate using the C++ order matching engine
      try {
        const result = await orderMatchingService.submitBondingCurveOrder(
          'sell',
          bondingCurveAddress,
          tokenAmount,
          'simulation' // Use a special wallet address to indicate this is a simulation
        );
        return result.paymentAmount;
      } catch (engineError) {
        logger.warn(`Failed to calculate with C++ engine, falling back to Solana: ${engineError.message}`);

        // Fall back to Solana implementation
        const bondingCurve = new PublicKey(bondingCurveAddress);

        // Convert token amount to smallest unit
        const tokenAmountBN = new BN(tokenAmount * PRECISION.toNumber());

        const payment = await this.client.calculatePaymentForSelling(bondingCurve, tokenAmountBN);

        // Convert from lamports to SOL
        return payment.toNumber() / PRECISION.toNumber();
      }
    } catch (error) {
      logger.error(`Error calculating payment for selling tokens for ${bondingCurveAddress}:`, error);
      throw new Error(`Failed to calculate payment for selling tokens: ${error.message}`);
    }
  }
}

module.exports = new BondingCurveService();
