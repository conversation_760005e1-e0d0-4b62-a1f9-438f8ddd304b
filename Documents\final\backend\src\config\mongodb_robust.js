/**
 * Robust MongoDB Connection Manager
 *
 * Features:
 * - Connection pooling with optimal settings
 * - Exponential backoff retry mechanism
 * - Automatic reconnection on failure
 * - Connection health monitoring
 * - Proper error handling without fallbacks
 * - Event-based connection state management
 */

const mongoose = require('mongoose');
const { MongoClient, GridFSBucket } = require('mongodb');
const fs = require('fs');
const path = require('path');
const dns = require('dns');
const EventEmitter = require('events');

// Set DNS resolution to prefer IPv4 to avoid TLS issues
dns.setDefaultResultOrder('ipv4first');
console.log('DNS result order set to IPv4 first');

// Connection state constants
const CONNECTION_STATES = {
  DISCONNECTED: 0,
  CONNECTED: 1,
  CONNECTING: 2,
  DISCONNECTING: 3
};

// Create a connection event emitter
class ConnectionEventEmitter extends EventEmitter {}
const connectionEvents = new ConnectionEventEmitter();

// MongoDB connection configuration
const mongodbConfig = {
  uri: process.env.MONGODB_URI,
  dbName: process.env.MONGODB_DB_NAME || 'swap',

  // Validate required environment variables in production
  validate: function() {
    if (!this.uri && process.env.NODE_ENV === 'production') {
      throw new Error('MONGODB_URI environment variable is required in production mode');
    }
    return true;
  },
  options: {
    // Connection pool settings
    maxPoolSize: 10,
    minPoolSize: 2,
    maxIdleTimeMS: 30000,

    // Timeout settings
    connectTimeoutMS: 30000,
    socketTimeoutMS: 45000,
    serverSelectionTimeoutMS: 30000,

    // Retry settings
    retryWrites: true,
    retryReads: true,

    // Write concern
    w: 'majority',
    wtimeoutMS: 10000,

    // Read preference
    readPreference: 'primaryPreferred',

    // TLS/SSL settings for MongoDB Atlas
    ssl: true,
    tls: true,
    tlsAllowInvalidCertificates: true,
    tlsAllowInvalidHostnames: true,

    // Heartbeat
    heartbeatFrequencyMS: 10000,

    // Buffer commands - removed deprecated options
    // These options are not supported in MongoDB driver v4+

    // Application name for monitoring
    appName: 'wb-swap-backend'
  }
};

// MongoDB client instances
let mongoClient = null;
let db = null;
let gridFSBucket = null;
let isConnecting = false;
let connectionAttempts = 0;
const MAX_RETRY_ATTEMPTS = 5;

/**
 * Calculate exponential backoff time
 * @param {number} attempt - The current attempt number
 * @returns {number} - Time to wait in milliseconds
 */
function getBackoffTime(attempt) {
  // Base delay is 1 second, max delay is 30 seconds
  const baseDelay = 1000;
  const maxDelay = 30000;
  const delay = Math.min(maxDelay, baseDelay * Math.pow(2, attempt));

  // Add some jitter to prevent all clients from retrying at the same time
  return delay + Math.floor(Math.random() * 1000);
}

/**
 * Connect to MongoDB with robust retry logic
 * @returns {Promise<Object>} - MongoDB client and database
 */
async function connectToMongoDB() {
  // Prevent multiple simultaneous connection attempts
  if (isConnecting) {
    console.log('Connection attempt already in progress, waiting...');
    return new Promise((resolve, reject) => {
      connectionEvents.once('connected', () => {
        resolve({ client: mongoClient, db });
      });
      connectionEvents.once('connection_failed', (error) => {
        reject(error);
      });
    });
  }

  isConnecting = true;
  connectionAttempts = 0;

  try {
    console.log('Initializing robust MongoDB connection...');

    // Validate configuration
    mongodbConfig.validate();

    if (!mongodbConfig.uri) {
      throw new Error('MongoDB URI is not configured. Please set MONGODB_URI environment variable.');
    }

    console.log('Using connection URI:', mongodbConfig.uri.replace(/\/.*@/, '//<credentials>@'));

    // Load CA certificate if available
    const caFilePath = path.resolve(__dirname, '../../rds-ca.pem');
    if (fs.existsSync(caFilePath)) {
      try {
        const ca = fs.readFileSync(caFilePath);
        console.log('CA certificate loaded successfully');
        mongodbConfig.options.ca = ca;
      } catch (err) {
        console.warn('Failed to load CA certificate:', err.message);
      }
    } else {
      console.warn('CA certificate file not found at:', caFilePath);
    }

    // Create MongoDB client
    mongoClient = new MongoClient(mongodbConfig.uri, mongodbConfig.options);

    // Set up connection event listeners
    mongoClient.on('serverDescriptionChanged', event => {
      if (event.newDescription.type === 'Unknown') {
        console.warn(`MongoDB server at ${event.address} is now unreachable`);
      } else if (event.previousDescription.type === 'Unknown') {
        console.log(`MongoDB server at ${event.address} is now reachable`);
      }
    });

    mongoClient.on('connectionPoolCreated', event => {
      console.log(`MongoDB connection pool created with ${event.options.maxPoolSize} max connections`);
    });

    mongoClient.on('connectionPoolClosed', event => {
      console.log(`MongoDB connection pool closed for ${event.address}`);
    });

    // Connect with retry logic
    while (connectionAttempts < MAX_RETRY_ATTEMPTS) {
      try {
        connectionAttempts++;
        console.log(`MongoDB connection attempt ${connectionAttempts}/${MAX_RETRY_ATTEMPTS}...`);

        await mongoClient.connect();

        // Get database and create GridFS bucket
        db = mongoClient.db(mongodbConfig.dbName);
        gridFSBucket = new GridFSBucket(db);
        console.log('GridFS connection established');

        // Verify connection with a ping
        await db.command({ ping: 1 });
        console.log('MongoDB connection verified with ping');

        // Connection successful
        console.log(`MongoDB connected successfully after ${connectionAttempts} attempt(s)`);
        isConnecting = false;
        connectionEvents.emit('connected');

        // Set up automatic reconnection
        setupReconnection();

        return { client: mongoClient, db, gridFSBucket };
      } catch (error) {
        console.error(`MongoDB connection attempt ${connectionAttempts} failed:`, error.message);

        if (connectionAttempts < MAX_RETRY_ATTEMPTS) {
          const backoffTime = getBackoffTime(connectionAttempts);
          console.log(`Retrying in ${backoffTime/1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, backoffTime));
        }
      }
    }

    // All connection attempts failed
    throw new Error(`Failed to connect to MongoDB after ${MAX_RETRY_ATTEMPTS} attempts`);
  } catch (error) {
    console.error('MongoDB connection failed:', error.message);
    isConnecting = false;
    connectionEvents.emit('connection_failed', error);
    throw error;
  }
}

/**
 * Set up automatic reconnection for MongoDB
 */
function setupReconnection() {
  // Monitor for topology changes that might indicate connection issues
  mongoClient.on('topologyDescriptionChanged', event => {
    const previousState = event.previousDescription.type;
    const newState = event.newDescription.type;

    if (previousState === 'ReplicaSetWithPrimary' && newState !== 'ReplicaSetWithPrimary') {
      console.warn('MongoDB primary server is no longer available');
    } else if (previousState !== 'ReplicaSetWithPrimary' && newState === 'ReplicaSetWithPrimary') {
      console.log('MongoDB primary server is now available');
    }

    // If all servers are unreachable, attempt reconnection
    if (newState === 'Unknown') {
      console.error('MongoDB cluster is unreachable, scheduling reconnection...');
      scheduleReconnection();
    }
  });
}

/**
 * Schedule a reconnection attempt with exponential backoff
 */
function scheduleReconnection() {
  if (isConnecting) return;

  const backoffTime = getBackoffTime(Math.min(connectionAttempts, 5));
  console.log(`Scheduling MongoDB reconnection in ${backoffTime/1000} seconds...`);

  setTimeout(async () => {
    try {
      console.log('Attempting MongoDB reconnection...');
      await connectToMongoDB();
      console.log('MongoDB reconnection successful');
    } catch (error) {
      console.error('MongoDB reconnection failed:', error.message);
      // Schedule another reconnection attempt
      connectionAttempts++;
      scheduleReconnection();
    }
  }, backoffTime);
}

/**
 * Get the MongoDB database instance
 * @returns {Object} MongoDB database
 */
function getDB() {
  if (!db) {
    throw new Error('MongoDB not connected. Call connectToMongoDB() first.');
  }
  return db;
}

/**
 * Get the MongoDB client
 * @returns {Object} MongoDB client
 */
function getClient() {
  if (!mongoClient) {
    throw new Error('MongoDB not connected. Call connectToMongoDB() first.');
  }
  return mongoClient;
}

/**
 * Get the GridFS bucket
 * @returns {Object} GridFS bucket
 */
function getGridFSBucket() {
  if (!gridFSBucket) {
    throw new Error('MongoDB not connected. Call connectToMongoDB() first.');
  }
  return gridFSBucket;
}

/**
 * Check if MongoDB is connected
 * @returns {boolean} True if connected, false otherwise
 */
function isConnected() {
  return mongoClient && mongoClient.topology && mongoClient.topology.isConnected();
}

/**
 * Get the current connection state
 * @returns {number} Connection state
 */
function getConnectionState() {
  if (!mongoClient || !mongoClient.topology) {
    return CONNECTION_STATES.DISCONNECTED;
  }

  if (mongoClient.topology.isConnected()) {
    return CONNECTION_STATES.CONNECTED;
  }

  if (isConnecting) {
    return CONNECTION_STATES.CONNECTING;
  }

  return CONNECTION_STATES.DISCONNECTED;
}

/**
 * Close the MongoDB connection
 * @returns {Promise<void>}
 */
async function closeConnection() {
  if (mongoClient) {
    try {
      await mongoClient.close();
      console.log('MongoDB connection closed');
      mongoClient = null;
      db = null;
      gridFSBucket = null;
    } catch (error) {
      console.error('Error closing MongoDB connection:', error);
      throw error;
    }
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('Received SIGINT signal, closing MongoDB connection...');
  try {
    await closeConnection();
    console.log('MongoDB connection closed successfully');
  } catch (error) {
    console.error('Error during MongoDB shutdown:', error);
  }
  process.exit(0);
});

module.exports = {
  connectToMongoDB,
  getDB,
  getClient,
  getGridFSBucket,
  isConnected,
  getConnectionState,
  closeConnection,
  CONNECTION_STATES,
  connectionEvents,
  mongodbConfig
};
