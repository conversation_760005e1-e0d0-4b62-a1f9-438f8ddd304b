/**
 * <PERSON><PERSON>t to check if database models are properly defined
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');

// Create a log file
const logFile = path.join(__dirname, 'check_models_log.txt');
fs.writeFileSync(logFile, `Check Models Log - ${new Date().toISOString()}\n\n`);

// Function to append to log file
function log(message) {
  fs.appendFileSync(logFile, message + '\n');
  console.log(message);
}

// Main function
async function checkModels() {
  log('Starting model check...');

  try {
    // Check MongoDB User model
    log('Checking MongoDB User model...');
    const User = require('../models/User');
    log(`User model: ${typeof User}`);
    log(`User schema: ${User.schema ? 'defined' : 'undefined'}`);
    
    if (User.schema) {
      log(`User schema paths: ${Object.keys(User.schema.paths).join(', ')}`);
    }
    
    // Check PostgreSQL models
    log('\nChecking PostgreSQL models...');
    
    // Check if the Order model exists
    try {
      const Order = require('../models/postgresql/order');
      log(`Order model: ${typeof Order}`);
      log(`Order attributes: ${Order.rawAttributes ? Object.keys(Order.rawAttributes).join(', ') : 'undefined'}`);
    } catch (error) {
      log(`Error loading Order model: ${error.message}`);
    }
    
    // Check if the UserBalance model exists
    try {
      const UserBalance = require('../models/postgresql/userBalance');
      log(`UserBalance model: ${typeof UserBalance}`);
      log(`UserBalance attributes: ${UserBalance.rawAttributes ? Object.keys(UserBalance.rawAttributes).join(', ') : 'undefined'}`);
    } catch (error) {
      log(`Error loading UserBalance model: ${error.message}`);
    }
    
    log('\nModel check completed successfully');
  } catch (error) {
    log(`Error checking models: ${error.message}`);
    log(error.stack);
  }
  
  log(`Log file written to: ${logFile}`);
}

// Run the check
checkModels()
  .then(() => {
    console.log('Check completed');
    process.exit(0);
  })
  .catch(error => {
    console.error(`Check failed: ${error.message}`);
    process.exit(1);
  });
