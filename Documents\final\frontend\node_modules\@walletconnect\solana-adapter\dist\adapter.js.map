{"version": 3, "file": "adapter.js", "sourceRoot": "", "sources": ["../src/adapter.ts"], "names": [], "mappings": "AACA,OAAO,EACL,uBAAuB,EACvB,oBAAoB,EACpB,wBAAwB,EACxB,uBAAuB,EACvB,mBAAmB,EACnB,gBAAgB,EAChB,sBAAsB,EACtB,0BAA0B,EAC1B,uBAAuB,EACxB,MAAM,6BAA6B,CAAA;AAQpC,OAAO,EAAE,oBAAoB,EAAE,MAAM,gBAAgB,CAAA;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,WAAW,CAAA;AAG/C,MAAM,CAAC,MAAM,uBAAuB,GAAG,eAA8C,CAAA;AAMrF,MAAM,OAAO,0BAA2B,SAAQ,uBAAuB;IACrE,IAAI,GAAG,uBAAuB,CAAA;IAC9B,GAAG,GAAG,2BAA2B,CAAA;IACjC,IAAI,GACF,whEAAwhE,CAAA;IAC1hE;;;OAGG;IACM,4BAA4B,GAAoC,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAA;IAEvF,UAAU,CAAkB;IAC5B,WAAW,CAAS;IACpB,OAAO,CAA4B;IACnC,OAAO,CAAkC;IACzC,WAAW,CAAkB;IAE7B,aAAa,CAAsD;IAE3E,YAAY,MAAwC;QAClD,KAAK,EAAE,CAAA;QAEP,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;QACxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAA;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;QACnB,IAAI,CAAC,WAAW;YACd,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAA;QAE1F,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACjD,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAA;IACxB,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAA;IACzB,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAA;IACzB,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtC,OAAM;YACR,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,KAAK,gBAAgB,CAAC,QAAQ,EAAE,CAAC;gBACnD,MAAM,IAAI,mBAAmB,EAAE,CAAA;YACjC,CAAC;YAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;YACvB,IAAI,CAAC,OAAO,GAAG,IAAI,mBAAmB,CAAC;gBACrC,OAAO,EACL,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,oBAAoB,CAAC,OAAO;oBACnD,CAAC,CAAC,oBAAoB,CAAC,OAAO;oBAC9B,CAAC,CAAC,oBAAoB,CAAC,MAAM;gBACjC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;aAC9B,CAAC,CAAA;YACF,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;YAClD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;YAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;YAC/B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,IAAK,KAAe,CAAC,WAAW,CAAC,IAAI,KAAK,kBAAkB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBAC5H,MAAM,IAAI,uBAAuB,EAAE,CAAA;YACrC,CAAC;YACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;YACxB,MAAM,KAAK,CAAA;QACb,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;QAC1B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QAC3B,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;YACvD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;YAEtB,IAAI,CAAC;gBACH,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBAC1B,MAAM,MAAM,CAAC,UAAU,EAAE,CAAA;gBAC3B,CAAC;YACH,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACxB,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAA;gBACpE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,wBAAwB,CAAE,KAAe,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAA;YACpF,CAAC;QACH,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;IACzB,CAAC;IAED,KAAK,CAAC,eAAe,CAA+C,WAAc;QAChF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;YAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,uBAAuB,EAAE,CAAA;YACrC,CAAC;YAED,IAAI,CAAC;gBACH,OAAO,MAAM,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAA;YAClD,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACxB,MAAM,IAAI,0BAA0B,CAAE,KAAe,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;YACxE,CAAC;QACH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAoB,CAAC,CAAA;YACxC,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAmB;QACnC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;YAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,uBAAuB,EAAE,CAAA;YACrC,CAAC;YAED,IAAI,CAAC;gBACH,OAAO,MAAM,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;YAC1C,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACxB,MAAM,IAAI,sBAAsB,CAAE,KAAe,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;YACpE,CAAC;QACH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAoB,CAAC,CAAA;YACxC,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,WAAc;QAEd,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;YAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,uBAAuB,EAAE,CAAA;YACrC,CAAC;YAED,IAAI,CAAC;gBACH,OAAO,MAAM,MAAM,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAA;YACzD,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACxB,MAAM,IAAI,0BAA0B,CAAE,KAAe,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;YACxE,CAAC;QACH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAoB,CAAC,CAAA;YACxC,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAEQ,KAAK,CAAC,mBAAmB,CAChC,YAAiB;QAEjB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;YAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,uBAAuB,EAAE,CAAA;YACrC,CAAC;YAED,IAAI,CAAC;gBACH,OAAO,MAAM,MAAM,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAA;YACvD,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACxB,MAAM,IAAI,0BAA0B,CAAE,KAAe,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;YACxE,CAAC;QACH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAoB,CAAC,CAAA;YACxC,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;CACF"}