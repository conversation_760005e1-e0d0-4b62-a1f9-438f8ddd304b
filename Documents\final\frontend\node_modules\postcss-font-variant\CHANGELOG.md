# 5.0.0 - 2021-01-12

- Changed: use PostCSS 7 API
- Updated: Node support to 10.0.0 (major)

# 4.0.1 - 2020-10-28

- Fixed: Incorrect conversion of small-caps ([#15](https://github.com/postcss/postcss-font-variant/pull/15))

# 4.0.0 - 2018-09-17

- Changed: use PostCSS 7 API

# 3.0.0 - 2017-05-11

- Changed: use PostCSS 6 API

# 2.0.1 - 2016-07-08

- Fixed: existing font-feature-settings being duplicated.
  ([#8](https://github.com/postcss/postcss-font-variant/pull/8) - @ChaosExAnima)

# 2.0.0 - 2015-09-08

- Added: compatibility with postcss v5.x
- Removed: compatiblity with postcss v4.x

# 1.2.0 - 2015-08-13

- Added: compatibility with postcss v4.1.x
  ([#5](https://github.com/postcss/postcss-font-variant/pull/5))

# 1.1.0 - 2015-01-29

  - Fixed: Properly handle font-variant-position:normal ([#3](https://github.com/postcss/postcss-font-variant/pull/3))
  - Added: support font-kerning ([#2](https://github.com/postcss/postcss-font-variant/pull/2))

# 1.0.2 - 2015-01-27

- Fixed: Re–use existing font-feature-settings declarations to avoid creating multiples that override themselves ([#1](https://github.com/postcss/postcss-font-variant/pull/1))

# 1.0.1 - 2014-11-11

- Fixed: wrong space char that breaks on some environnements

# 1.0.0 - 2014-10-09

✨ First release based on [rework-font-variant](https://github.com/ianstormtaylor/rework-font-variant) v1.0.1
