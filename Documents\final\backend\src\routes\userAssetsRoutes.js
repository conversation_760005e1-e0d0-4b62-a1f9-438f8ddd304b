/**
 * User Assets Routes
 */

const express = require('express');
const router = express.Router();
const userAssetsService = require('../services/userAssetsService');
const { auth } = require('../middleware/auth');

/**
 * Get user assets
 * @route GET /api/user-assets
 * @access Private
 */
router.get('/', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const assets = await userAssetsService.getUserAssets(userId);

    res.json({
      success: true,
      assets
    });
  } catch (error) {
    console.error('Error fetching user assets:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user assets',
      error: error.message
    });
  }
});

module.exports = router;
