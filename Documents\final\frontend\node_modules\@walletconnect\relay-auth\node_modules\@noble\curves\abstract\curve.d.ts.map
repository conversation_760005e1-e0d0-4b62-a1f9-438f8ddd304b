{"version": 3, "file": "curve.d.ts", "sourceRoot": "", "sources": ["../src/abstract/curve.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,sEAAsE;AACtE,OAAO,EAAE,MAAM,EAA0B,MAAM,cAAc,CAAC;AAM9D,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI;IAC3B,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,CAAC;CACN,GAAG;IAAE,CAAC,CAAC,EAAE,KAAK,CAAC;IAAC,CAAC,CAAC,EAAE,KAAK,CAAA;CAAE,CAAC;AAE7B,MAAM,WAAW,KAAK,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC;IACvC,MAAM,IAAI,CAAC,CAAC;IACZ,MAAM,IAAI,CAAC,CAAC;IACZ,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACjB,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACtB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,OAAO,CAAC;IAC1B,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC;CAC7B;AAED,MAAM,MAAM,gBAAgB,CAAC,CAAC,IAAI;IAChC,IAAI,EAAE,CAAC,CAAC;IACR,IAAI,EAAE,CAAC,CAAC;CACT,CAAC;AACF,MAAM,MAAM,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC;AAyCxC,MAAM,MAAM,KAAK,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,IAAI;IACtC,eAAe,EAAE,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;IACxE,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC;IAChC,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAC1C,gBAAgB,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;IAChD,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,GAAG;QAAE,CAAC,EAAE,CAAC,CAAC;QAAC,CAAC,EAAE,CAAC,CAAA;KAAE,CAAC;IAC7D,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAC/D,cAAc,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IAC3D,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG;QAAE,CAAC,EAAE,CAAC,CAAC;QAAC,CAAC,EAAE,CAAC,CAAA;KAAE,CAAC;IAClE,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACrE,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACtC,CAAC;AAEF;;;;;;;;;;;;;GAaG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAgLvF;AAED;;;;;;;;;GASG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,EAC1C,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EACtB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EACtB,MAAM,EAAE,CAAC,EAAE,EACX,OAAO,EAAE,MAAM,EAAE,GAChB,CAAC,CAmCH;AACD;;;;;;GAMG;AACH,wBAAgB,mBAAmB,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,EACpD,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EACtB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EACtB,MAAM,EAAE,CAAC,EAAE,EACX,UAAU,EAAE,MAAM,GACjB,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAoE1B;AAED;;;GAGG;AACH,MAAM,MAAM,UAAU,CAAC,CAAC,IAAI;IAC1B,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,EAAE,MAAM,CAAC;IACV,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,CAAC,EAAE,MAAM,CAAC;IACV,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,EAAE,EAAE,CAAC,CAAC;IACN,EAAE,EAAE,CAAC,CAAC;IACN,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAC9B,CAAC;AAEF,wBAAgB,aAAa,CAAC,EAAE,EAAE,CAAC,EACjC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC,GAAG,CAAC,GACxB,QAAQ,CACT;IACE,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;IAC5B,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC;CAC9B,GAAG,UAAU,CAAC,EAAE,CAAC,GAChB,CAAC,GAAG;IACF,CAAC,EAAE,MAAM,CAAC;CACX,CACJ,CAqBA"}