(function(s,o){typeof exports=="object"&&typeof module<"u"?o(exports,require("fs")):typeof define=="function"&&define.amd?define(["exports","fs"],o):(s=typeof globalThis<"u"?globalThis:s||self,o(s["@walletconnect/keyvaluestorage"]={},s.a$1))})(this,function(s,o){"use strict";function A(t){return t&&typeof t=="object"&&"default"in t?t:{default:t}}var u=A(o);const S=t=>JSON.stringify(t,(e,a)=>typeof a=="bigint"?a.toString()+"n":a),k=t=>{const e=/([\[:])?(\d{17,}|(?:[9](?:[1-9]07199254740991|0[1-9]7199254740991|00[8-9]199254740991|007[2-9]99254740991|007199[3-9]54740991|0071992[6-9]4740991|00719925[5-9]740991|007199254[8-9]40991|0071992547[5-9]0991|00719925474[1-9]991|00719925474099[2-9])))([,\}\]])/g,a=t.replace(e,'$1"$2n"$3');return JSON.parse(a,(n,i)=>typeof i=="string"&&i.match(/^\d+n$/)?BigInt(i.substring(0,i.length-1)):i)};function f(t){if(typeof t!="string")throw new Error(`Cannot safe json parse value of type ${typeof t}`);try{return k(t)}catch{return t}}function z(t){return typeof t=="string"?t:S(t)||""}function _(){try{const t=require("unstorage"),e=require("unstorage/drivers/fs-lite");return{db:t,driver:e}}catch{throw new Error(`To use WalletConnect server side, you'll need to install the "unstorage" dependency. If you are seeing this error during a build / in an SSR environment, you can add "unstorage" as a devDependency to make this error go away.`)}}let c;const l=":memory:",r=class{constructor(t){this.writeActionsQueue={state:"idle",actions:[]},c||(c=_()),t?.dbName===l?this.database=c.db.createStorage():this.database=c.db.createStorage({driver:c.driver({base:t?.dbName})})}static create(t){const e=t.dbName;return e===l?new r(t):(r.instances[e]||(r.instances[e]=new r(t)),r.instances[e])}async getKeys(){return this.database.getKeys()}async getEntries(){return(await this.database.getItems(await this.database.getKeys())).map(t=>[t.key,t.value])}async onWriteAction(t){const{key:e,value:a,action:n}=t;let i=()=>({});const d=new Promise(h=>i=h);this.writeActionsQueue.actions.push({key:e,value:a,action:n,callback:i}),this.writeActionsQueue.state==="idle"&&this.startWriteActions(),await d}async startWriteActions(){if(this.writeActionsQueue.actions.length===0){this.writeActionsQueue.state="idle";return}for(this.writeActionsQueue.state="active";this.writeActionsQueue.actions.length>0;){const t=this.writeActionsQueue.actions.shift();if(!t)continue;const{key:e,value:a,action:n,callback:i}=t;switch(n){case"setItem":await this.database.setItem(e),await this.database.setItem(e,z(a));break;case"removeItem":await this.database.removeItem(e);break}i()}this.writeActionsQueue.state="idle"}async getItem(t){const e=await this.database.getItem(t);if(e!==null)return e}async setItem(t,e){await this.onWriteAction({key:t,value:e,action:"setItem"})}async removeItem(t){await this.onWriteAction({key:t,action:"removeItem"})}};let w=r;w.instances={};const E="wc_storage_version",g=".to_migrate",K=".migrated",b=1,Q=async(t,e,a)=>{var n;if(t===l){a();return}const i=E,d=await e.getItem(i);if(d&&d>=b){a();return}const h=await N(`${t}${g}`);if(!h){a();return}const m=f(h);if(!m){a();return}const p=(n=m?.collections)==null?void 0:n[0],y=p?.data;if(!y||!y.length){a();return}for(;y.length;){const $=y.shift();if(!$)continue;const{id:O,value:P}=$;await e.setItem(O,f(P))}await e.setItem(i,b),v(`${t}${g}`,`${t}${K}`),a()},N=async t=>await new Promise(e=>{u.default.readFile(t,{encoding:"utf8"},(a,n)=>{a&&e(void 0),e(n)})}),W=t=>{t!==l&&u.default.existsSync(t)&&(u.default.lstatSync(t).isDirectory()||v(t,`${t}${g}`))},v=(t,e)=>{try{u.default.renameSync(t,e)}catch{}},j="walletconnect.db";class I{constructor(e){this.initialized=!1,this.setInitialized=()=>{this.initialized=!0};const a=e?.database||e?.table||j;W(a),this.database=w.create({dbName:a}),Q(a,this.database,this.setInitialized)}async getKeys(){return await this.initialize(),this.database.getKeys()}async getEntries(){return await this.initialize(),this.database.getEntries()}async getItem(e){return await this.initialize(),this.database.getItem(e)}async setItem(e,a){await this.initialize(),await this.database.setItem(e,a)}async removeItem(e){await this.initialize(),await this.database.removeItem(e)}async initialize(){this.initialized||await new Promise(e=>{const a=setInterval(()=>{this.initialized&&(clearInterval(a),e())},20)})}}class D{}function M(t){var e;return[t[0],f((e=t[1])!=null?e:"")]}s.IKeyValueStorage=D,s.KeyValueStorage=I,s.default=I,s.parseEntry=M,Object.defineProperty(s,"__esModule",{value:!0})});
//# sourceMappingURL=index.umd.js.map
