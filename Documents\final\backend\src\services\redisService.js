const Redis = require('ioredis');
const config = require('../config');

class RedisService {
  constructor() {
    this.client = new Redis({
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      db: config.redis.db
    });

    this.client.on('error', (error) => {
      console.error('Redis connection error:', error);
    });

    this.client.on('connect', () => {
      console.log('Connected to Redis');
    });
  }

  // Rate limiting
  async checkRateLimit(key, limit, windowMs) {
    const current = await this.client.incr(key);
    if (current === 1) {
      await this.client.expire(key, windowMs / 1000);
    }
    return current <= limit;
  }

  // Cache token data
  async cacheTokenData(mintAddress, data, ttl = 60) {
    const key = `token:${mintAddress}`;
    await this.client.setex(key, ttl, JSON.stringify(data));
  }

  // Get cached token data
  async getCachedTokenData(mintAddress) {
    const key = `token:${mintAddress}`;
    const data = await this.client.get(key);
    return data ? JSON.parse(data) : null;
  }

  // Cache market data
  async cacheMarketData(mintAddress, data, ttl = 60) {
    const key = `market:${mintAddress}`;
    await this.client.setex(key, ttl, JSON.stringify(data));
  }

  // Get cached market data
  async getCachedMarketData(mintAddress) {
    const key = `market:${mintAddress}`;
    const data = await this.client.get(key);
    return data ? JSON.parse(data) : null;
  }

  // Cache user data
  async cacheUserData(userId, data, ttl = 300) {
    const key = `user:${userId}`;
    await this.client.setex(key, ttl, JSON.stringify(data));
  }

  // Get cached user data
  async getCachedUserData(userId) {
    const key = `user:${userId}`;
    const data = await this.client.get(key);
    return data ? JSON.parse(data) : null;
  }

  // Delete cached data
  async deleteCache(key) {
    await this.client.del(key);
  }

  // Clear all cache
  async clearCache() {
    await this.client.flushall();
  }

  // Cache order book
  async cacheOrderBook(mintAddress, data, ttl = 60) {
    const key = `orderbook:${mintAddress}`;
    await this.client.setex(key, ttl, JSON.stringify(data));
  }

  async getCachedOrderBook(mintAddress) {
    const key = `orderbook:${mintAddress}`;
    const data = await this.client.get(key);
    return data ? JSON.parse(data) : null;
  }

  // Cache recent trades
  async cacheRecentTrades(mintAddress, trades, ttl = 300) {
    const key = `trades:${mintAddress}`;
    await this.client.setex(key, ttl, JSON.stringify(trades));
  }

  async getCachedRecentTrades(mintAddress) {
    const key = `trades:${mintAddress}`;
    const data = await this.client.get(key);
    return data ? JSON.parse(data) : null;
  }

  // User session management
  async setUserSession(userId, sessionData, ttl = 86400) {
    const key = `session:${userId}`;
    await this.client.setex(key, ttl, JSON.stringify(sessionData));
  }

  async getUserSession(userId) {
    const key = `session:${userId}`;
    const data = await this.client.get(key);
    return data ? JSON.parse(data) : null;
  }

  async deleteUserSession(userId) {
    const key = `session:${userId}`;
    await this.client.del(key);
  }
}

module.exports = new RedisService(); 