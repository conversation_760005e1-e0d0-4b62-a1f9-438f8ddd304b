import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const polygonZkEvmCardona = /*#__PURE__*/ defineChain({
    id: 2442,
    name: 'Polygon zkEVM Cardona',
    nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.cardona.zkevm-rpc.com'],
        },
    },
    blockExplorers: {
        default: {
            name: 'PolygonScan',
            url: 'https://cardona-zkevm.polygonscan.com',
            apiUrl: 'https://cardona-zkevm.polygonscan.com/api',
        },
    },
    testnet: true,
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 114091,
        },
    },
});
//# sourceMappingURL=polygonZkEvmCardona.js.map