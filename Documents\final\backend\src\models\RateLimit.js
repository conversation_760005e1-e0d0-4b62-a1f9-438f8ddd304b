const mongoose = require('mongoose');

const rateLimitSchema = new mongoose.Schema({
  ip: {
    type: String,
    required: true,
    index: true
  },
  endpoint: {
    type: String,
    required: true,
    index: true
  },
  attempts: {
    type: Number,
    default: 1
  },
  lastAttempt: {
    type: Date,
    default: Date.now
  },
  blockedUntil: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: 3600 // Document will be automatically deleted after 1 hour
  }
}, {
  timestamps: true
});

// Index for querying by IP and endpoint
rateLimitSchema.index({ ip: 1, endpoint: 1 });

// Method to check if IP is blocked
rateLimitSchema.methods.isBlocked = function() {
  return this.blockedUntil && this.blockedUntil > Date.now();
};

// Method to increment attempts
rateLimitSchema.methods.incrementAttempts = async function() {
  this.attempts += 1;
  this.lastAttempt = Date.now();
  
  // Block for 15 minutes after 5 failed attempts
  if (this.attempts >= 5) {
    this.blockedUntil = new Date(Date.now() + 15 * 60 * 1000);
  }
  
  await this.save();
};

// Method to reset attempts
rateLimitSchema.methods.resetAttempts = async function() {
  this.attempts = 1;
  this.lastAttempt = Date.now();
  this.blockedUntil = undefined;
  await this.save();
};

// Static method to clean up old records
rateLimitSchema.statics.cleanup = async function() {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  await this.deleteMany({
    createdAt: { $lt: oneHourAgo }
  });
};

const RateLimit = mongoose.model('RateLimit', rateLimitSchema);

module.exports = RateLimit; 