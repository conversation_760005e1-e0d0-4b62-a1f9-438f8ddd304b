/**
 * Application Startup Script
 * Initializes all services in the correct order
 */

const { IS_PROD, IS_DEV, ENV } = require('./config/environment');
const databaseService = require('./services/databaseService');
const solanaService = require('./services/solanaService');
const postgresqlService = require('./services/postgresqlService');

// Order matching engine removed - using bonding curve smart contracts instead

/**
 * Initialize all services
 */
async function initializeServices() {
  console.log(`Starting application in ${ENV} mode`);

  try {
    // Step 1: Initialize MongoDB database connections
    console.log('Initializing MongoDB database service...');

    // Try multiple times to initialize the database service
    let dbInitialized = false;
    let dbAttempts = 0;
    const maxDbAttempts = 3;

    while (!dbInitialized && dbAttempts < maxDbAttempts) {
      dbAttempts++;
      try {
        console.log(`Database initialization attempt ${dbAttempts}/${maxDbAttempts}...`);
        dbInitialized = await databaseService.initialize();

        if (dbInitialized) {
          console.log('Database service initialized successfully');
          break;
        }
      } catch (dbError) {
        console.error(`Database initialization attempt ${dbAttempts} failed:`, dbError.message);
        if (dbAttempts < maxDbAttempts) {
          // Wait before retrying
          const waitTime = 2000 * dbAttempts;
          console.log(`Waiting ${waitTime}ms before retrying...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }

    if (!dbInitialized && IS_PROD) {
      throw new Error('Failed to initialize MongoDB database service in production mode');
    }

    // Step 2: Initialize PostgreSQL database connections
    console.log('Initializing PostgreSQL database service...');
    const postgresqlInitialized = await postgresqlService.initialize();

    if (!postgresqlInitialized && IS_PROD) {
      throw new Error('Failed to initialize PostgreSQL database service in production mode');
    }

    // Step 3: Initialize Solana service
    console.log('Initializing Solana service...');
    const solanaInitialized = await solanaService.initialize();

    if (!solanaInitialized && IS_PROD) {
      throw new Error('Failed to initialize Solana service in production mode');
    }

    // Step 4: Order matching engine removed - using bonding curve smart contracts
    console.log('Skipping order matching engine initialization - using bonding curve smart contracts');

    // All services initialized
    console.log('All services initialized successfully');

    // Log development mode warning
    if (IS_DEV) {
      console.log('\n==================================================');
      console.log('RUNNING IN DEVELOPMENT MODE');
      console.log('Some services may be using mock implementations');
      console.log('==================================================\n');
    }

    return true;
  } catch (error) {
    console.error('Failed to initialize services:', error);

    // In development, we can continue with limited functionality
    if (!IS_PROD) {
      console.warn('Continuing with limited functionality in development mode');
      return true;
    }

    return false;
  }
}

/**
 * Gracefully shut down all services
 */
async function shutdownServices() {
  console.log('Shutting down services...');

  try {
    // Close database connections
    await databaseService.close();

    // Other cleanup as needed

    console.log('All services shut down successfully');
    return true;
  } catch (error) {
    console.error('Error shutting down services:', error);
    return false;
  }
}

// Export functions
module.exports = {
  initializeServices,
  shutdownServices
};
