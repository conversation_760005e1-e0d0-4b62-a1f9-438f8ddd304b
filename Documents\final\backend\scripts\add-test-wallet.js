/**
 * Simple script to add a test wallet <NAME_EMAIL> user
 */
require('dotenv').config();
const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(async () => {
  console.log('Connected to MongoDB');
  
  // Import User model after connection is established
  const User = require('../src/models/User');
  
  try {
    // Find the user
    const user = await User.findOne({ email: '<EMAIL>' });
    
    if (!user) {
      console.error('User <NAME_EMAIL> not found');
      process.exit(1);
    }
    
    console.log('Found user:', user.username);
    
    // Create a test wallet (hardcoded for simplicity)
    const testWallet = {
      publicKey: 'TestWalletPublicKey123456789',
      secretKey: 'TestWalletSecretKey987654321',
      isTestWallet: true,
      balance: 1000
    };
    
    // Update user with test wallet
    user.walletAddress = testWallet.publicKey;
    user.testWallet = testWallet;
    
    // Save the updated user
    await user.save();
    
    console.log('Test wallet added <NAME_EMAIL>');
    console.log('Wallet address:', testWallet.publicKey);
    console.log('Test balance: 1000 SOL');
    
    console.log('Test wallet setup completed successfully');
  } catch (error) {
    console.error('Error adding test wallet:', error);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
})
.catch(err => {
  console.error('MongoDB connection error:', err);
});
