{"version": 3, "file": "index.js", "sources": ["../../../safe-json/dist/index.es.js", "../../src/shared/utils.ts", "../../src/react-native/index.ts"], "sourcesContent": ["const s=n=>JSON.stringify(n,(r,t)=>typeof t==\"bigint\"?t.toString()+\"n\":t),o=n=>{const r=/([\\[:])?(\\d{17,}|(?:[9](?:[1-9]07199254740991|0[1-9]7199254740991|00[8-9]199254740991|007[2-9]99254740991|007199[3-9]54740991|0071992[6-9]4740991|00719925[5-9]740991|007199254[8-9]40991|0071992547[5-9]0991|00719925474[1-9]991|00719925474099[2-9])))([,\\}\\]])/g,t=n.replace(r,'$1\"$2n\"$3');return JSON.parse(t,(g,e)=>typeof e==\"string\"&&e.match(/^\\d+n$/)?BigInt(e.substring(0,e.length-1)):e)};function i(n){if(typeof n!=\"string\")throw new Error(`Cannot safe json parse value of type ${typeof n}`);try{return o(n)}catch{return n}}function f(n){return typeof n==\"string\"?n:s(n)||\"\"}export{i as safeJsonParse,f as safeJsonStringify};\n//# sourceMappingURL=index.es.js.map\n", "import { safeJsonParse } from \"@walletconnect/safe-json\";\n\nexport function parseEntry(entry: [string, string | null]): [string, any] {\n  return [entry[0], safeJsonParse(entry[1] ?? \"\")];\n}\n", "import AsyncStorage from \"@react-native-async-storage/async-storage\";\nimport { safeJsonParse, safeJsonStringify } from \"@walletconnect/safe-json\";\n\nimport { parseEntry, IKeyValueStorage } from \"../shared\";\n\nexport class KeyValueStorage implements IKeyValueStorage {\n  private readonly asyncStorage = AsyncStorage;\n\n  public async getKeys(): Promise<string[]> {\n    // AsyncStorage.getAllKeys technically has `Promise<readonly string[]>` as return type.\n    // Using an explicit cast here to avoid the `readonly` causing breakage with `IKeyValueStorage`.\n    return this.asyncStorage.getAllKeys() as Promise<string[]>;\n  }\n\n  public async getEntries<T = any>(): Promise<[string, T][]> {\n    const keys = await this.getKeys();\n    const entries = await this.asyncStorage.multiGet(keys);\n    return entries.map(parseEntry);\n  }\n\n  public async getItem<T = any>(key: string): Promise<T | undefined> {\n    const item = await this.asyncStorage.getItem(key);\n    if (typeof item === \"undefined\" || item === null) {\n      return undefined;\n    }\n    // TODO: fix this annoying type casting\n    return safeJsonParse(item) as T;\n  }\n\n  public async setItem<T = any>(key: string, value: T): Promise<void> {\n    await this.asyncStorage.setItem(key, safeJsonStringify(value));\n  }\n\n  public async removeItem(key: string): Promise<void> {\n    await this.asyncStorage.removeItem(key);\n  }\n}\n\nexport default KeyValueStorage;\n"], "names": ["s", "n", "r", "t", "o", "g", "e", "entry", "_a", "safeJsonParse", "AsyncStorage", "keys", "parseEntry", "key", "item", "value", "safeJsonStringify"], "mappings": ";;;;;;;;;;AAAA,MAAMA,CAAAA,CAAEC,GAAG,IAAK,CAAA,SAAA,CAAUA,EAAE,CAACC,CAAAA,CAAEC,CAAI,GAAA,OAAOA,CAAG,EAAA,QAAA,CAASA,EAAE,QAAS,EAAA,CAAE,IAAIA,CAAC,CAAA,CAAEC,EAAEH,CAAG,EAAA,CAAC,MAAMC,CAAAA,CAAE,oQAAqQC,CAAAA,CAAAA,CAAEF,EAAE,OAAQC,CAAAA,CAAAA,CAAE,WAAW,CAAE,CAAA,OAAO,KAAK,KAAMC,CAAAA,CAAAA,CAAE,CAACE,CAAAA,CAAEC,CAAI,GAAA,OAAOA,GAAG,QAAUA,EAAAA,CAAAA,CAAE,MAAM,QAAQ,CAAA,CAAE,OAAOA,CAAE,CAAA,SAAA,CAAU,CAAEA,CAAAA,CAAAA,CAAE,MAAO,CAAA,CAAC,CAAC,CAAEA,CAAAA,CAAC,CAAC,CAAE,CAAA,SAAS,EAAEL,CAAE,CAAA,CAAC,GAAG,OAAOA,CAAG,EAAA,QAAA,CAAS,MAAM,IAAI,KAAA,CAAM,wCAAwC,OAAOA,CAAAA,CAAAA,CAAG,EAAE,GAAG,CAAC,OAAOG,CAAAA,CAAEH,CAAC,CAAC,OAAMK,CAAL,CAAA,CAAM,OAAOL,CAAC,CAAC,CAAC,SAAS,CAAA,CAAEA,CAAE,CAAA,CAAC,OAAO,OAAOA,GAAG,QAASA,CAAAA,CAAAA,CAAED,EAAEC,CAAC,CAAA,EAAG,EAAE;;ACElpB,SAAS,WAAWM,CAA+C,CAAA,CAF1E,IAAAC,CAAAA,CAGE,OAAO,CAACD,EAAM,CAAC,CAAA,CAAGE,CAAcD,CAAAA,CAAAA,CAAAA,CAAAD,CAAM,CAAA,CAAC,IAAP,IAAAC,CAAAA,CAAAA,CAAY,EAAE,CAAC,CACjD;;ACCO,MAAM,eAA4C,CAAlD,WAAA,EAAA,CACL,KAAiB,YAAeE,CAAAA,sBAAAA,CAEhC,MAAa,OAAA,EAA6B,CAGxC,OAAO,KAAK,YAAa,CAAA,UAAA,EAC3B,CAEA,MAAa,YAA8C,CACzD,MAAMC,CAAO,CAAA,MAAM,IAAK,CAAA,OAAA,GAExB,OADgB,CAAA,MAAM,KAAK,YAAa,CAAA,QAAA,CAASA,CAAI,CACtC,EAAA,GAAA,CAAIC,UAAU,CAC/B,CAEA,MAAa,QAAiBC,CAAqC,CAAA,CACjE,MAAMC,CAAO,CAAA,MAAM,KAAK,YAAa,CAAA,OAAA,CAAQD,CAAG,CAChD,CAAA,GAAI,SAAOC,CAAS,EAAA,WAAA,EAAeA,IAAS,IAI5C,CAAA,CAAA,OAAOL,EAAcK,CAAI,CAC3B,CAEA,MAAa,OAAiBD,CAAAA,CAAAA,CAAaE,EAAyB,CAClE,MAAM,KAAK,YAAa,CAAA,OAAA,CAAQF,EAAKG,CAAkBD,CAAAA,CAAK,CAAC,EAC/D,CAEA,MAAa,WAAWF,CAA4B,CAAA,CAClD,MAAM,IAAK,CAAA,YAAA,CAAa,WAAWA,CAAG,EACxC,CACF;;;;;"}