/**
 * Notification Service
 * Handles user notifications and delivery via different channels
 */

const mongoose = require('mongoose');
const redis = require('../config/redis');
const emailService = require('./emailService');

// Notification types
const NOTIFICATION_TYPES = {
  SYSTEM: 'system',
  TRADE: 'trade',
  ORDER: 'order',
  SECURITY: 'security',
  PRICE_ALERT: 'price_alert',
  TOKEN_LISTING: 'token_listing',
  ACCOUNT: 'account'
};

class NotificationService {
  constructor() {
    // In-memory notification storage as fallback
    this.notifications = new Map();

    // Initialize Redis subscription for real-time notifications
    this._initRedisSubscription();
  }

  /**
   * Initialize Redis subscription for real-time notifications
   * @private
   */
  _initRedisSubscription() {
    if (redis.subscriber) {
      // Subscribe to notification channels
      redis.subscriber.subscribe('notifications');
      redis.subscriber.subscribe('trade:notifications');
      redis.subscriber.subscribe('order:notifications');
      redis.subscriber.subscribe('security:notifications');

      // Handle incoming notifications
      redis.subscriber.on('message', (channel, message) => {
        try {
          const notification = JSON.parse(message);

          // Process notification based on channel
          if (channel === 'notifications') {
            // Broadcast to all connected clients via Socket.IO
            if (global.io) {
              global.io.emit('notification', notification);
            }
          } else if (channel.endsWith(':notifications')) {
            // Send to specific user(s)
            if (notification.userId && global.io) {
              global.io.to(`user:${notification.userId}`).emit('notification', notification);
            }
          }
        } catch (error) {
          console.error('Error processing Redis notification:', error);
        }
      });
    }
  }

  /**
   * Create a new notification
   * @param {Object} notification - Notification data
   * @param {string} notification.userId - User ID (optional for system notifications)
   * @param {string} notification.type - Notification type
   * @param {string} notification.title - Notification title
   * @param {string} notification.message - Notification message
   * @param {Object} notification.data - Additional data (optional)
   * @param {boolean} notification.isRead - Whether notification is read (default: false)
   * @returns {Promise<Object>} - Created notification
   */
  async createNotification(notification) {
    try {
      const { userId, type, title, message, data = {}, isRead = false } = notification;

      // Create notification object
      const newNotification = {
        id: mongoose.Types.ObjectId().toString(),
        userId,
        type,
        title,
        message,
        data,
        isRead,
        createdAt: new Date()
      };

      // Store in database if available
      if (mongoose.connection.readyState === 1) {
        // Assuming we have a Notification model
        // await Notification.create(newNotification);
      }

      // Store in Redis if available
      if (redis.client) {
        const key = `notifications:${userId || 'system'}`;
        await redis.client.lpush(key, JSON.stringify(newNotification));
        await redis.client.ltrim(key, 0, 99); // Keep only the 100 most recent notifications
      }

      // Store in memory as fallback
      if (!this.notifications.has(userId)) {
        this.notifications.set(userId, []);
      }

      const userNotifications = this.notifications.get(userId);
      userNotifications.unshift(newNotification);

      // Trim to 100 notifications
      if (userNotifications.length > 100) {
        userNotifications.length = 100;
      }

      // Publish to Redis for real-time delivery
      if (redis.client) {
        await redis.client.publish('notifications', JSON.stringify(newNotification));

        if (userId) {
          await redis.client.publish(`user:${userId}:notifications`, JSON.stringify(newNotification));
        }
      }

      return newNotification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Get user notifications
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {number} options.limit - Maximum number of notifications to return (default: 20)
   * @param {number} options.offset - Number of notifications to skip (default: 0)
   * @param {boolean} options.unreadOnly - Whether to return only unread notifications (default: false)
   * @returns {Promise<Array>} - User notifications
   */
  async getUserNotifications(userId, options = {}) {
    try {
      const { limit = 20, offset = 0, unreadOnly = false } = options;

      // Try to get from Redis first
      if (redis.client) {
        const key = `notifications:${userId}`;
        const notifications = await redis.client.lrange(key, offset, offset + limit - 1);

        if (notifications && notifications.length > 0) {
          const parsedNotifications = notifications.map(n => JSON.parse(n));

          if (unreadOnly) {
            return parsedNotifications.filter(n => !n.isRead);
          }

          return parsedNotifications;
        }
      }

      // Fall back to in-memory storage
      if (this.notifications.has(userId)) {
        let userNotifications = this.notifications.get(userId);

        if (unreadOnly) {
          userNotifications = userNotifications.filter(n => !n.isRead);
        }

        return userNotifications.slice(offset, offset + limit);
      }

      return [];
    } catch (error) {
      console.error('Error getting user notifications:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   * @param {string} userId - User ID
   * @param {string} notificationId - Notification ID
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async markNotificationAsRead(userId, notificationId) {
    try {
      // Update in Redis if available
      if (redis.client) {
        const key = `notifications:${userId}`;
        const notifications = await redis.client.lrange(key, 0, -1);

        for (let i = 0; i < notifications.length; i++) {
          const notification = JSON.parse(notifications[i]);

          if (notification.id === notificationId) {
            notification.isRead = true;
            await redis.client.lset(key, i, JSON.stringify(notification));
            break;
          }
        }
      }

      // Update in memory
      if (this.notifications.has(userId)) {
        const userNotifications = this.notifications.get(userId);
        const notification = userNotifications.find(n => n.id === notificationId);

        if (notification) {
          notification.isRead = true;
        }
      }

      return true;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all user notifications as read
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async markAllNotificationsAsRead(userId) {
    try {
      // Update in Redis if available
      if (redis.client) {
        const key = `notifications:${userId}`;
        const notifications = await redis.client.lrange(key, 0, -1);

        for (let i = 0; i < notifications.length; i++) {
          const notification = JSON.parse(notifications[i]);
          notification.isRead = true;
          await redis.client.lset(key, i, JSON.stringify(notification));
        }
      }

      // Update in memory
      if (this.notifications.has(userId)) {
        const userNotifications = this.notifications.get(userId);

        for (const notification of userNotifications) {
          notification.isRead = true;
        }
      }

      return true;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Delete a notification
   * @param {string} userId - User ID
   * @param {string} notificationId - Notification ID
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async deleteNotification(userId, notificationId) {
    try {
      // Delete from Redis if available
      if (redis.client) {
        const key = `notifications:${userId}`;
        const notifications = await redis.client.lrange(key, 0, -1);

        for (let i = 0; i < notifications.length; i++) {
          const notification = JSON.parse(notifications[i]);

          if (notification.id === notificationId) {
            // Remove the notification at index i
            await redis.client.lrem(key, 1, notifications[i]);
            break;
          }
        }
      }

      // Delete from memory
      if (this.notifications.has(userId)) {
        const userNotifications = this.notifications.get(userId);
        const index = userNotifications.findIndex(n => n.id === notificationId);

        if (index !== -1) {
          userNotifications.splice(index, 1);
        }
      }

      return true;
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  /**
   * Send a notification via email
   * @param {string} userId - User ID
   * @param {Object} notification - Notification data
   * @returns {Promise<boolean>} - Whether the email was sent successfully
   */
  async sendEmailNotification(userId, notification) {
    try {
      // Get user email and preferences
      const user = await mongoose.model('User').findById(userId);

      if (!user || !user.email) {
        return false;
      }

      // Check if user has opted out of email notifications
      if (user.notificationPreferences &&
          user.notificationPreferences.emailNotifications === false) {
        console.log(`User ${userId} has opted out of email notifications`);
        return false;
      }

      // If MongoDB is not available, check Redis for preferences
      if (!user.notificationPreferences && redis.client) {
        try {
          const prefsJson = await redis.client.get(`user:${userId}:notification-preferences`);
          if (prefsJson) {
            const prefs = JSON.parse(prefsJson);
            if (prefs.emailNotifications === false) {
              console.log(`User ${userId} has opted out of email notifications (Redis)`);
              return false;
            }
          }
        } catch (redisError) {
          console.error('Error checking notification preferences in Redis:', redisError);
          // Continue with sending email as default behavior
        }
      }

      // Send email
      await emailService.sendEmail({
        to: user.email,
        subject: notification.title,
        html: `
          <h1>${notification.title}</h1>
          <p>${notification.message}</p>
          ${notification.data.actionUrl ? `<a href="${notification.data.actionUrl}">View Details</a>` : ''}
        `
      });

      return true;
    } catch (error) {
      console.error('Error sending email notification:', error);
      return false;
    }
  }

  /**
   * Send a notification via SMS
   * @param {string} userId - User ID
   * @param {Object} notification - Notification data
   * @returns {Promise<boolean>} - Whether the SMS was sent successfully
   */
  async sendSMSNotification(userId, notification) {
    try {
      // Get user phone and preferences
      const user = await mongoose.model('User').findById(userId);

      if (!user || !user.phone) {
        return false;
      }

      // Check if user has opted out of SMS notifications
      if (user.notificationPreferences &&
          user.notificationPreferences.smsNotifications === false) {
        console.log(`User ${userId} has opted out of SMS notifications`);
        return false;
      }

      // If MongoDB is not available, check Redis for preferences
      if (!user.notificationPreferences && redis.client) {
        try {
          const prefsJson = await redis.client.get(`user:${userId}:notification-preferences`);
          if (prefsJson) {
            const prefs = JSON.parse(prefsJson);
            if (prefs.smsNotifications === false) {
              console.log(`User ${userId} has opted out of SMS notifications (Redis)`);
              return false;
            }
          }
        } catch (redisError) {
          console.error('Error checking notification preferences in Redis:', redisError);
          // Continue with sending SMS as default behavior
        }
      }

      // Format phone number with country code
      const phoneNumber = user.countryCode ? `${user.countryCode}${user.phone}` : user.phone;

      // Send SMS - this is a placeholder for an actual SMS service
      console.log(`Sending SMS to ${phoneNumber}: ${notification.title} - ${notification.message}`);

      // Here you would integrate with an SMS service like Twilio, Nexmo, etc.
      // For example with Twilio:
      // await twilioClient.messages.create({
      //   body: `${notification.title}: ${notification.message}`,
      //   from: process.env.TWILIO_PHONE_NUMBER,
      //   to: phoneNumber
      // });

      return true;
    } catch (error) {
      console.error('Error sending SMS notification:', error);
      return false;
    }
  }

  /**
   * Send a browser push notification
   * @param {string} userId - User ID
   * @param {Object} notification - Notification data
   * @returns {Promise<boolean>} - Whether the push notification was sent successfully
   */
  async sendPushNotification(userId, notification) {
    try {
      // Get user preferences
      const user = await mongoose.model('User').findById(userId);

      if (!user) {
        return false;
      }

      // Check if user has opted out of push notifications
      if (user.notificationPreferences &&
          user.notificationPreferences.pushNotifications === false) {
        console.log(`User ${userId} has opted out of push notifications`);
        return false;
      }

      // If MongoDB is not available, check Redis for preferences
      if (!user.notificationPreferences && redis.client) {
        try {
          const prefsJson = await redis.client.get(`user:${userId}:notification-preferences`);
          if (prefsJson) {
            const prefs = JSON.parse(prefsJson);
            if (prefs.pushNotifications === false) {
              console.log(`User ${userId} has opted out of push notifications (Redis)`);
              return false;
            }
          }
        } catch (redisError) {
          console.error('Error checking notification preferences in Redis:', redisError);
          // Continue with sending push notification as default behavior
        }
      }

      // Send push notification via Socket.IO if available
      if (global.io) {
        global.io.to(`user:${userId}`).emit('notification', {
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          timestamp: new Date()
        });

        console.log(`Push notification sent to user ${userId}`);
        return true;
      }

      // If Socket.IO is not available, we can't send push notifications
      console.log('Socket.IO not available for push notifications');
      return false;
    } catch (error) {
      console.error('Error sending push notification:', error);
      return false;
    }
  }
}

module.exports = new NotificationService();
