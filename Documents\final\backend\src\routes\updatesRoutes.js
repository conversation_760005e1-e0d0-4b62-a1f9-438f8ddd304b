const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Token = require('../models/Token');
const mongodbRobust = require('../config/mongodb_robust');

// Get latest token created
router.get('/latest-token', async (req, res) => {
  try {
    console.log('Fetching latest token from MongoDB...');

    // Ensure MongoDB connection is available
    if (!mongodbRobust.isConnected()) {
      try {
        await mongodbRobust.connectToMongoDB();
      } catch (error) {
        console.error('Failed to connect to MongoDB:', error.message);
        return res.status(500).json({ error: 'Database connection error' });
      }
    }

    // Find the latest token created
    const latestToken = await Token.findOne()
      .sort({ createdAt: -1 })
      .populate('creator', 'username profilePicture')
      .lean();

    if (latestToken) {
      console.log('Latest token found:', latestToken.name);
      res.json(latestToken);
    } else {
      console.log('No tokens found in the database');
      res.json(null);
    }
  } catch (error) {
    console.error('Error fetching latest token:', error);
    res.status(500).json({ error: 'Failed to fetch latest token' });
  }
});

// Get trending token (token with highest volume/activity)
router.get('/trending-token', async (req, res) => {
  try {
    console.log('Fetching trending token from MongoDB...');

    // Ensure MongoDB connection is available
    if (!mongodbRobust.isConnected()) {
      try {
        await mongodbRobust.connectToMongoDB();
      } catch (error) {
        console.error('Failed to connect to MongoDB:', error.message);
        return res.status(500).json({ error: 'Database connection error' });
      }
    }

    // In a real implementation, this would find the token with the highest trading volume
    // For now, we'll just get a random token and add some trending data to it
    const tokens = await Token.find()
      .populate('creator', 'username profilePicture')
      .limit(10)
      .lean();

    if (tokens && tokens.length > 0) {
      // Get a random token from the list
      const randomIndex = Math.floor(Math.random() * tokens.length);
      const trendingToken = tokens[randomIndex];

      // Add trending data
      trendingToken.priceChange = Math.random() * 20 - 10; // Random price change between -10% and +10%
      trendingToken.volume = Math.floor(Math.random() * 10000) + 1000; // Random volume between 1000 and 11000

      console.log('Trending token found:', trendingToken.name);
      res.json(trendingToken);
    } else {
      console.log('No tokens found in the database');
      res.json(null);
    }
  } catch (error) {
    console.error('Error fetching trending token:', error);
    res.status(500).json({ error: 'Failed to fetch trending token' });
  }
});

// Get latest registered user
router.get('/latest-user', async (req, res) => {
  try {
    console.log('Fetching latest user from MongoDB...');

    // Ensure MongoDB connection is available
    if (!mongodbRobust.isConnected()) {
      try {
        await mongodbRobust.connectToMongoDB();
      } catch (error) {
        console.error('Failed to connect to MongoDB:', error.message);
        return res.status(500).json({ error: 'Database connection error' });
      }
    }

    // Find the latest user registered
    const latestUser = await User.findOne()
      .sort({ createdAt: -1 })
      .select('username profilePicture createdAt')
      .lean();

    if (latestUser) {
      console.log('Latest user found:', latestUser.username);
      res.json(latestUser);
    } else {
      console.log('No users found in the database');
      res.json(null);
    }
  } catch (error) {
    console.error('Error fetching latest user:', error);
    res.status(500).json({ error: 'Failed to fetch latest user' });
  }
});

module.exports = router;
