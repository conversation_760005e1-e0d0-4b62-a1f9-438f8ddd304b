/**
 * Platform Wallet Configuration
 *
 * This file centralizes all platform wallet configuration.
 * All components should import this file to access wallet credentials.
 *
 * IMPORTANT: Never commit actual private keys to version control.
 * Use environment variables in production.
 */

const fs = require('fs');
const path = require('path');
const { Keypair } = require('@solana/web3.js');
const logger = require('../utils/logger');

// Default wallet path (relative to project root)
const DEFAULT_WALLET_PATH = process.env.SOLANA_WALLET_PATH || './scripts/solana-keypair.json';

// Get wallet configuration from environment variables
const walletConfig = {
  // Public address of the platform wallet
  address: process.env.PLATFORM_WALLET_ADDRESS,

  // Source of the wallet credentials: 'file', 'env', or 'hardware'
  source: process.env.WALLET_SOURCE || 'file',

  // Path to the wallet keypair file (if source is 'file')
  path: process.env.WALLET_PATH || DEFAULT_WALLET_PATH,
};

/**
 * Load the platform wallet keypair
 * @returns {Object} Wallet keypair and public key
 */
function loadPlatformWallet() {
  try {
    let platformWallet = null;

    // Load wallet based on source
    if (walletConfig.source === 'file') {
      // Resolve the wallet path relative to project root
      const walletPath = path.resolve(process.cwd(), walletConfig.path);

      if (fs.existsSync(walletPath)) {
        logger.info(`Loading platform wallet from ${walletPath}`);
        const secretKey = new Uint8Array(JSON.parse(fs.readFileSync(walletPath, 'utf8')));
        platformWallet = Keypair.fromSecretKey(secretKey);
      } else {
        logger.warn(`Wallet file not found at ${walletPath}, generating new wallet`);
        platformWallet = Keypair.generate();

        // Create directory if it doesn't exist
        const dir = path.dirname(walletPath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }

        // Save the new wallet
        fs.writeFileSync(walletPath, JSON.stringify(Array.from(platformWallet.secretKey)));
        logger.info(`Generated and saved new platform wallet to ${walletPath}`);
      }
    } else if (walletConfig.source === 'env') {
      // Load from environment variable
      const secretKeyString = process.env.PLATFORM_WALLET_PRIVATE_KEY;

      if (!secretKeyString) {
        throw new Error('PLATFORM_WALLET_PRIVATE_KEY environment variable not set');
      }

      // Handle different formats of private key
      let secretKey;
      try {
        if (secretKeyString.includes('[')) {
          // JSON array format
          secretKey = new Uint8Array(JSON.parse(secretKeyString));
        } else {
          // Base58 encoded format
          const bs58 = require('bs58');
          secretKey = new Uint8Array(bs58.decode(secretKeyString));
        }
        platformWallet = Keypair.fromSecretKey(secretKey);
      } catch (error) {
        throw new Error(`Invalid private key format: ${error.message}`);
      }
    } else if (walletConfig.source === 'hardware') {
      // Hardware wallet integration would go here
      // This is a placeholder for future implementation
      throw new Error('Hardware wallet integration not implemented');
    } else {
      throw new Error(`Unknown wallet source: ${walletConfig.source}`);
    }

    // Verify the wallet address matches the expected address (if provided)
    if (walletConfig.address && platformWallet.publicKey.toString() !== walletConfig.address) {
      // If the expected address is our production wallet, use it directly
      if (walletConfig.address === 'CVHXGtveLFr54yPsc4R6PgUhLLYCY5YQbXiqcLqEdkcu') {
        logger.info(`Using expected platform wallet address: ${walletConfig.address}`);
        return {
          keypair: platformWallet,
          publicKey: walletConfig.address
        };
      }
      logger.warn(`Loaded wallet address ${platformWallet.publicKey.toString()} does not match expected address ${walletConfig.address}`);
    }

    return {
      keypair: platformWallet,
      publicKey: platformWallet.publicKey.toString(),
    };
  } catch (error) {
    logger.error(`Error loading platform wallet: ${error.message}`);
    throw error;
  }
}

module.exports = {
  walletConfig,
  loadPlatformWallet,

  // Helper method to get just the public key
  getPlatformWalletAddress: () => {
    try {
      const { publicKey } = loadPlatformWallet();
      return publicKey;
    } catch (error) {
      logger.error(`Error getting platform wallet address: ${error.message}`);
      return null;
    }
  }
};
