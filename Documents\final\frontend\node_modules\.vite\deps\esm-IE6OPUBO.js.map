{"version": 3, "sources": ["../../@solflare-wallet/metamask-sdk/node_modules/eventemitter3/index.js", "../../@solflare-wallet/metamask-sdk/node_modules/base-x/src/index.js", "../../@solflare-wallet/metamask-sdk/node_modules/bs58/index.js", "../../@solflare-wallet/metamask-sdk/lib/esm/index.js", "../../@solflare-wallet/metamask-sdk/node_modules/eventemitter3/index.mjs", "../../@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/rng.js", "../../@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/regex.js", "../../@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/validate.js", "../../@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/stringify.js", "../../@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/parse.js", "../../@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/v35.js", "../../@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/md5.js", "../../@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/v3.js", "../../@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/native.js", "../../@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/v4.js", "../../@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/sha1.js", "../../@solflare-wallet/metamask-sdk/node_modules/uuid/dist/esm-browser/v5.js", "../../@solflare-wallet/metamask-sdk/lib/esm/utils.js", "../../@solflare-wallet/metamask-sdk/lib/esm/detectProvider.js", "../../@solflare-wallet/metamask-sdk/lib/esm/standard/solana.js", "../../@solflare-wallet/metamask-sdk/lib/esm/standard/account.js"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "'use strict'\n// base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\nfunction base (ALPHABET) {\n  if (ALPHABET.length >= 255) { throw new TypeError('Alphabet too long') }\n  var BASE_MAP = new Uint8Array(256)\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i)\n    var xc = x.charCodeAt(0)\n    if (BASE_MAP[xc] !== 255) { throw new TypeError(x + ' is ambiguous') }\n    BASE_MAP[xc] = i\n  }\n  var BASE = ALPHABET.length\n  var LEADER = ALPHABET.charAt(0)\n  var FACTOR = Math.log(BASE) / Math.log(256) // log(BASE) / log(256), rounded up\n  var iFACTOR = Math.log(256) / Math.log(BASE) // log(256) / log(BASE), rounded up\n  function encode (source) {\n    if (source instanceof Uint8Array) {\n    } else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength)\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source)\n    }\n    if (!(source instanceof Uint8Array)) { throw new TypeError('Expected Uint8Array') }\n    if (source.length === 0) { return '' }\n        // Skip & count leading zeroes.\n    var zeroes = 0\n    var length = 0\n    var pbegin = 0\n    var pend = source.length\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++\n      zeroes++\n    }\n        // Allocate enough space in big-endian base58 representation.\n    var size = ((pend - pbegin) * iFACTOR + 1) >>> 0\n    var b58 = new Uint8Array(size)\n        // Process the bytes.\n    while (pbegin !== pend) {\n      var carry = source[pbegin]\n            // Apply \"b58 = b58 * 256 + ch\".\n      var i = 0\n      for (var it1 = size - 1; (carry !== 0 || i < length) && (it1 !== -1); it1--, i++) {\n        carry += (256 * b58[it1]) >>> 0\n        b58[it1] = (carry % BASE) >>> 0\n        carry = (carry / BASE) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      pbegin++\n    }\n        // Skip leading zeroes in base58 result.\n    var it2 = size - length\n    while (it2 !== size && b58[it2] === 0) {\n      it2++\n    }\n        // Translate the result into a string.\n    var str = LEADER.repeat(zeroes)\n    for (; it2 < size; ++it2) { str += ALPHABET.charAt(b58[it2]) }\n    return str\n  }\n  function decodeUnsafe (source) {\n    if (typeof source !== 'string') { throw new TypeError('Expected String') }\n    if (source.length === 0) { return new Uint8Array() }\n    var psz = 0\n        // Skip and count leading '1's.\n    var zeroes = 0\n    var length = 0\n    while (source[psz] === LEADER) {\n      zeroes++\n      psz++\n    }\n        // Allocate enough space in big-endian base256 representation.\n    var size = (((source.length - psz) * FACTOR) + 1) >>> 0 // log(58) / log(256), rounded up.\n    var b256 = new Uint8Array(size)\n        // Process the characters.\n    while (source[psz]) {\n            // Find code of next character\n      var charCode = source.charCodeAt(psz)\n            // Base map can not be indexed using char code\n      if (charCode > 255) { return }\n            // Decode character\n      var carry = BASE_MAP[charCode]\n            // Invalid character\n      if (carry === 255) { return }\n      var i = 0\n      for (var it3 = size - 1; (carry !== 0 || i < length) && (it3 !== -1); it3--, i++) {\n        carry += (BASE * b256[it3]) >>> 0\n        b256[it3] = (carry % 256) >>> 0\n        carry = (carry / 256) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      psz++\n    }\n        // Skip leading zeroes in b256.\n    var it4 = size - length\n    while (it4 !== size && b256[it4] === 0) {\n      it4++\n    }\n    var vch = new Uint8Array(zeroes + (size - it4))\n    var j = zeroes\n    while (it4 !== size) {\n      vch[j++] = b256[it4++]\n    }\n    return vch\n  }\n  function decode (string) {\n    var buffer = decodeUnsafe(string)\n    if (buffer) { return buffer }\n    throw new Error('Non-base' + BASE + ' character')\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  }\n}\nmodule.exports = base\n", "const basex = require('base-x')\nconst ALPHABET = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz'\n\nmodule.exports = basex(ALPHABET)\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { PublicKey, Transaction, VersionedTransaction } from '@solana/web3.js';\nimport EventEmitter from 'eventemitter3';\nimport bs58 from 'bs58';\nimport { v4 as uuidv4 } from 'uuid';\nimport { isLegacyTransactionInstance, serializeTransaction } from './utils';\nimport { detectProvider } from './detectProvider';\nimport { StandardSolflareMetaMaskWalletAccount } from './standard/account';\nimport { isSolanaChain } from './standard/solana';\nexport * from './types';\nexport * from './standard/account';\nclass SolflareMetaMask extends EventEmitter {\n    constructor(config) {\n        super();\n        this._network = 'mainnet-beta';\n        this._iframeParams = {};\n        this._element = null;\n        this._iframe = null;\n        this._publicKey = null;\n        this._account = null;\n        this._isConnected = false;\n        this._connectHandler = null;\n        this._messageHandlers = {};\n        this._handleEvent = (event) => {\n            var _a, _b;\n            switch (event.type) {\n                case 'connect': {\n                    this._collapseIframe();\n                    if ((_a = event.data) === null || _a === void 0 ? void 0 : _a.publicKey) {\n                        this._publicKey = event.data.publicKey;\n                        this._isConnected = true;\n                        if (this._connectHandler) {\n                            this._connectHandler.resolve();\n                            this._connectHandler = null;\n                        }\n                        this._connected();\n                    }\n                    else {\n                        if (this._connectHandler) {\n                            this._connectHandler.reject();\n                            this._connectHandler = null;\n                        }\n                        this._disconnected();\n                    }\n                    return;\n                }\n                case 'disconnect': {\n                    if (this._connectHandler) {\n                        this._connectHandler.reject();\n                        this._connectHandler = null;\n                    }\n                    this._disconnected();\n                    return;\n                }\n                case 'accountChanged': {\n                    if ((_b = event.data) === null || _b === void 0 ? void 0 : _b.publicKey) {\n                        this._publicKey = event.data.publicKey;\n                        this.emit('accountChanged', this.publicKey);\n                        this._standardConnected();\n                    }\n                    else {\n                        this.emit('accountChanged', undefined);\n                        this._standardDisconnected();\n                    }\n                    return;\n                }\n                default: {\n                    return;\n                }\n            }\n        };\n        this._handleResize = (data) => {\n            if (data.resizeMode === 'full') {\n                if (data.params.mode === 'fullscreen') {\n                    this._expandIframe();\n                }\n                else if (data.params.mode === 'hide') {\n                    this._collapseIframe();\n                }\n            }\n            else if (data.resizeMode === 'coordinates') {\n                this._resizeIframe(data.params);\n            }\n        };\n        this._handleMessage = (event) => {\n            var _a;\n            if (((_a = event.data) === null || _a === void 0 ? void 0 : _a.channel) !== 'solflareIframeToWalletAdapter') {\n                return;\n            }\n            const data = event.data.data || {};\n            if (data.type === 'event') {\n                this._handleEvent(data.event);\n            }\n            else if (data.type === 'resize') {\n                this._handleResize(data);\n            }\n            else if (data.type === 'response') {\n                if (this._messageHandlers[data.id]) {\n                    const { resolve, reject } = this._messageHandlers[data.id];\n                    delete this._messageHandlers[data.id];\n                    if (data.error) {\n                        reject(data.error);\n                    }\n                    else {\n                        resolve(data.result);\n                    }\n                }\n            }\n        };\n        this._removeElement = () => {\n            if (this._element) {\n                this._element.remove();\n                this._element = null;\n            }\n        };\n        this._removeDanglingElements = () => {\n            const elements = document.getElementsByClassName('solflare-metamask-wallet-adapter-iframe');\n            for (const element of elements) {\n                if (element.parentElement) {\n                    element.remove();\n                }\n            }\n        };\n        this._injectElement = () => {\n            this._removeElement();\n            this._removeDanglingElements();\n            const params = Object.assign(Object.assign({}, this._iframeParams), { mm: true, v: 1, cluster: this._network || 'mainnet-beta', origin: window.location.origin || '', title: document.title || '' });\n            const queryString = Object.keys(params)\n                .map((key) => `${key}=${encodeURIComponent(params[key])}`)\n                .join('&');\n            const iframeUrl = `${SolflareMetaMask.IFRAME_URL}?${queryString}`;\n            this._element = document.createElement('div');\n            this._element.className = 'solflare-metamask-wallet-adapter-iframe';\n            this._element.innerHTML = `\n      <iframe src='${iframeUrl}' style='position: fixed; top: 0; bottom: 0; left: 0; right: 0; width: 100%; height: 100%; border: none; border-radius: 0; z-index: 99999; color-scheme: auto;' allowtransparency='true'></iframe>\n    `;\n            document.body.appendChild(this._element);\n            this._iframe = this._element.querySelector('iframe');\n            window.addEventListener('message', this._handleMessage, false);\n        };\n        this._collapseIframe = () => {\n            if (this._iframe) {\n                this._iframe.style.top = '';\n                this._iframe.style.right = '';\n                this._iframe.style.height = '2px';\n                this._iframe.style.width = '2px';\n            }\n        };\n        this._expandIframe = () => {\n            if (this._iframe) {\n                this._iframe.style.top = '0px';\n                this._iframe.style.bottom = '0px';\n                this._iframe.style.left = '0px';\n                this._iframe.style.right = '0px';\n                this._iframe.style.width = '100%';\n                this._iframe.style.height = '100%';\n            }\n        };\n        this._resizeIframe = (params) => {\n            if (!this._iframe) {\n                return;\n            }\n            this._iframe.style.top = isFinite(params.top) ? `${params.top}px` : '';\n            this._iframe.style.bottom = isFinite(params.bottom) ? `${params.bottom}px` : '';\n            this._iframe.style.left = isFinite(params.left) ? `${params.left}px` : '';\n            this._iframe.style.right = isFinite(params.right) ? `${params.right}px` : '';\n            this._iframe.style.width = isFinite(params.width)\n                ? `${params.width}px`\n                : params.width;\n            this._iframe.style.height = isFinite(params.height)\n                ? `${params.height}px`\n                : params.height;\n        };\n        this._sendIframeMessage = (data) => {\n            if (!this.connected || !this.publicKey) {\n                throw new Error('Wallet not connected');\n            }\n            return new Promise((resolve, reject) => {\n                var _a, _b;\n                const messageId = uuidv4();\n                this._messageHandlers[messageId] = { resolve, reject };\n                (_b = (_a = this._iframe) === null || _a === void 0 ? void 0 : _a.contentWindow) === null || _b === void 0 ? void 0 : _b.postMessage({\n                    channel: 'solflareWalletAdapterToIframe',\n                    data: Object.assign({ id: messageId }, data)\n                }, '*');\n            });\n        };\n        this._connected = () => {\n            this._isConnected = true;\n            this.emit('connect', this.publicKey);\n            this._standardConnected();\n        };\n        this._disconnected = () => {\n            this._publicKey = null;\n            this._isConnected = false;\n            window.removeEventListener('message', this._handleMessage, false);\n            this._removeElement();\n            this.emit('disconnect');\n            this._standardDisconnected();\n        };\n        this._standardConnected = () => {\n            if (!this.publicKey) {\n                return;\n            }\n            const address = this.publicKey.toString();\n            if (!this._account || this._account.address !== address) {\n                this._account = new StandardSolflareMetaMaskWalletAccount({\n                    address,\n                    publicKey: this.publicKey.toBytes()\n                });\n                this.emit('standard_change', { accounts: this.standardAccounts });\n            }\n        };\n        this._standardDisconnected = () => {\n            if (this._account) {\n                this._account = null;\n                this.emit('standard_change', { accounts: this.standardAccounts });\n            }\n        };\n        if (config === null || config === void 0 ? void 0 : config.network) {\n            this._network = config === null || config === void 0 ? void 0 : config.network;\n        }\n        if (window.SolflareMetaMaskParams) {\n            this._iframeParams = Object.assign(Object.assign({}, this._iframeParams), window.SolflareMetaMaskParams);\n        }\n        if (config === null || config === void 0 ? void 0 : config.params) {\n            this._iframeParams = Object.assign(Object.assign({}, this._iframeParams), config === null || config === void 0 ? void 0 : config.params);\n        }\n    }\n    get publicKey() {\n        return this._publicKey ? new PublicKey(this._publicKey) : null;\n    }\n    get standardAccount() {\n        return this._account;\n    }\n    get standardAccounts() {\n        return this._account ? [this._account] : [];\n    }\n    get isConnected() {\n        return this._isConnected;\n    }\n    get connected() {\n        return this.isConnected;\n    }\n    get autoApprove() {\n        return false;\n    }\n    connect() {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this.connected) {\n                return;\n            }\n            this._injectElement();\n            yield new Promise((resolve, reject) => {\n                this._connectHandler = { resolve, reject };\n            });\n        });\n    }\n    disconnect() {\n        return __awaiter(this, void 0, void 0, function* () {\n            yield this._sendIframeMessage({\n                method: 'disconnect'\n            });\n            this._disconnected();\n        });\n    }\n    signTransaction(transaction) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected || !this.publicKey) {\n                throw new Error('Wallet not connected');\n            }\n            try {\n                const serializedTransaction = serializeTransaction(transaction);\n                const response = yield this._sendIframeMessage({\n                    method: 'signTransactionV2',\n                    params: {\n                        transaction: bs58.encode(serializedTransaction)\n                    }\n                });\n                const { transaction: signedTransaction } = response;\n                return isLegacyTransactionInstance(transaction) ? Transaction.from(bs58.decode(signedTransaction)) : VersionedTransaction.deserialize(bs58.decode(signedTransaction));\n            }\n            catch (e) {\n                throw new Error(((_a = e === null || e === void 0 ? void 0 : e.toString) === null || _a === void 0 ? void 0 : _a.call(e)) || 'Failed to sign transaction');\n            }\n        });\n    }\n    signAllTransactions(transactions) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected || !this.publicKey) {\n                throw new Error('Wallet not connected');\n            }\n            try {\n                const serializedTransactions = transactions.map((transaction) => serializeTransaction(transaction));\n                const { transactions: signedTransactions } = yield this._sendIframeMessage({\n                    method: 'signAllTransactionsV2',\n                    params: {\n                        transactions: serializedTransactions.map((transaction) => bs58.encode(transaction))\n                    }\n                });\n                return signedTransactions.map((signedTransaction, index) => {\n                    return isLegacyTransactionInstance(transactions[index]) ? Transaction.from(bs58.decode(signedTransaction)) : VersionedTransaction.deserialize(bs58.decode(signedTransaction));\n                });\n            }\n            catch (e) {\n                throw new Error(((_a = e === null || e === void 0 ? void 0 : e.toString) === null || _a === void 0 ? void 0 : _a.call(e)) || 'Failed to sign transactions');\n            }\n        });\n    }\n    signAndSendTransaction(transaction, options) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected || !this.publicKey) {\n                throw new Error('Wallet not connected');\n            }\n            try {\n                const serializedTransaction = serializeTransaction(transaction);\n                const { signature } = yield this._sendIframeMessage({\n                    method: 'signAndSendTransaction',\n                    params: {\n                        transaction: bs58.encode(serializedTransaction),\n                        options\n                    }\n                });\n                return signature;\n            }\n            catch (e) {\n                throw new Error(((_a = e === null || e === void 0 ? void 0 : e.toString) === null || _a === void 0 ? void 0 : _a.call(e)) || 'Failed to sign and send transaction');\n            }\n        });\n    }\n    signMessage(data, display = 'utf8') {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected || !this.publicKey) {\n                throw new Error('Wallet not connected');\n            }\n            try {\n                const { signature } = yield this._sendIframeMessage({\n                    method: 'signMessage',\n                    params: {\n                        data: bs58.encode(data),\n                        display\n                    }\n                });\n                return Uint8Array.from(bs58.decode(signature));\n            }\n            catch (e) {\n                throw new Error(((_a = e === null || e === void 0 ? void 0 : e.toString) === null || _a === void 0 ? void 0 : _a.call(e)) || 'Failed to sign message');\n            }\n        });\n    }\n    sign(data, display = 'utf8') {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield this.signMessage(data, display);\n        });\n    }\n    static isSupported() {\n        return __awaiter(this, void 0, void 0, function* () {\n            const provider = yield detectProvider();\n            return !!provider;\n        });\n    }\n    standardSignAndSendTransaction(...inputs) {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected)\n                throw new Error('not connected');\n            const outputs = [];\n            if (inputs.length === 1) {\n                const { transaction, account, chain, options } = inputs[0];\n                const { minContextSlot, preflightCommitment, skipPreflight, maxRetries } = options || {};\n                if (account !== this._account)\n                    throw new Error('invalid account');\n                if (!isSolanaChain(chain))\n                    throw new Error('invalid chain');\n                const signature = yield this.signAndSendTransaction(VersionedTransaction.deserialize(transaction), {\n                    preflightCommitment,\n                    minContextSlot,\n                    maxRetries,\n                    skipPreflight\n                });\n                outputs.push({ signature: bs58.decode(signature) });\n            }\n            else if (inputs.length > 1) {\n                for (const input of inputs) {\n                    outputs.push(...(yield this.standardSignAndSendTransaction(input)));\n                }\n            }\n            return outputs;\n        });\n    }\n    standardSignTransaction(...inputs) {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected)\n                throw new Error('not connected');\n            const outputs = [];\n            if (inputs.length === 1) {\n                const { transaction, account, chain } = inputs[0];\n                if (account !== this._account)\n                    throw new Error('invalid account');\n                if (chain && !isSolanaChain(chain))\n                    throw new Error('invalid chain');\n                const signedTransaction = yield this.signTransaction(VersionedTransaction.deserialize(transaction));\n                outputs.push({ signedTransaction: signedTransaction.serialize() });\n            }\n            else if (inputs.length > 1) {\n                let chain;\n                for (const input of inputs) {\n                    if (input.account !== this._account)\n                        throw new Error('invalid account');\n                    if (input.chain) {\n                        if (!isSolanaChain(input.chain))\n                            throw new Error('invalid chain');\n                        if (chain) {\n                            if (input.chain !== chain)\n                                throw new Error('conflicting chain');\n                        }\n                        else {\n                            chain = input.chain;\n                        }\n                    }\n                }\n                const transactions = inputs.map(({ transaction }) => VersionedTransaction.deserialize(transaction));\n                const signedTransactions = yield this.signAllTransactions(transactions);\n                outputs.push(...signedTransactions.map((signedTransaction) => ({\n                    signedTransaction: signedTransaction.serialize()\n                })));\n            }\n            return outputs;\n        });\n    }\n    standardSignMessage(...inputs) {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.connected)\n                throw new Error('not connected');\n            const outputs = [];\n            if (inputs.length === 1) {\n                const { message, account } = inputs[0];\n                if (account !== this._account)\n                    throw new Error('invalid account');\n                const signature = yield this.signMessage(message);\n                outputs.push({ signedMessage: message, signature });\n            }\n            else if (inputs.length > 1) {\n                for (const input of inputs) {\n                    outputs.push(...(yield this.standardSignMessage(input)));\n                }\n            }\n            return outputs;\n        });\n    }\n}\nSolflareMetaMask.IFRAME_URL = 'https://widget.solflare.com/';\nexport default SolflareMetaMask;\n", "import EventEmitter from './index.js'\n\nexport { EventEmitter }\nexport default EventEmitter\n", "// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}", "export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;", "import REGEX from './regex.js';\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\n\nexport default validate;", "import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;", "import validate from './validate.js';\n\nfunction parse(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  let v;\n  const arr = new Uint8Array(16); // Parse ########-....-....-....-............\n\n  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;\n  arr[1] = v >>> 16 & 0xff;\n  arr[2] = v >>> 8 & 0xff;\n  arr[3] = v & 0xff; // Parse ........-####-....-....-............\n\n  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;\n  arr[5] = v & 0xff; // Parse ........-....-####-....-............\n\n  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;\n  arr[7] = v & 0xff; // Parse ........-....-....-####-............\n\n  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;\n  arr[9] = v & 0xff; // Parse ........-....-....-....-############\n  // (Use \"/\" to avoid 32-bit truncation when bit-shifting high-order bytes)\n\n  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff;\n  arr[11] = v / 0x100000000 & 0xff;\n  arr[12] = v >>> 24 & 0xff;\n  arr[13] = v >>> 16 & 0xff;\n  arr[14] = v >>> 8 & 0xff;\n  arr[15] = v & 0xff;\n  return arr;\n}\n\nexport default parse;", "import { unsafeStringify } from './stringify.js';\nimport parse from './parse.js';\n\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  const bytes = [];\n\n  for (let i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n\n  return bytes;\n}\n\nexport const DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport const URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function v35(name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    var _namespace;\n\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n\n    if (typeof namespace === 'string') {\n      namespace = parse(namespace);\n    }\n\n    if (((_namespace = namespace) === null || _namespace === void 0 ? void 0 : _namespace.length) !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    } // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n\n\n    let bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n\n    if (buf) {\n      offset = offset || 0;\n\n      for (let i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n\n      return buf;\n    }\n\n    return unsafeStringify(bytes);\n  } // Function#name is not settable on some platforms (#270)\n\n\n  try {\n    generateUUID.name = name; // eslint-disable-next-line no-empty\n  } catch (err) {} // For CommonJS default export support\n\n\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}", "/*\n * Browser-compatible JavaScript MD5\n *\n * Modification of JavaScript MD5\n * https://github.com/blueimp/JavaScript-MD5\n *\n * Copyright 2011, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n *\n * Based on\n * A JavaScript implementation of the RSA Data Security, Inc. MD5 Message\n * Digest Algorithm, as defined in RFC 1321.\n * Version 2.2 Copyright (C) <PERSON> 1999 - 2009\n * Other contributors: <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for more info.\n */\nfunction md5(bytes) {\n  if (typeof bytes === 'string') {\n    const msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = new Uint8Array(msg.length);\n\n    for (let i = 0; i < msg.length; ++i) {\n      bytes[i] = msg.charCodeAt(i);\n    }\n  }\n\n  return md5ToHexEncodedArray(wordsToMd5(bytesToWords(bytes), bytes.length * 8));\n}\n/*\n * Convert an array of little-endian words to an array of bytes\n */\n\n\nfunction md5ToHexEncodedArray(input) {\n  const output = [];\n  const length32 = input.length * 32;\n  const hexTab = '0123456789abcdef';\n\n  for (let i = 0; i < length32; i += 8) {\n    const x = input[i >> 5] >>> i % 32 & 0xff;\n    const hex = parseInt(hexTab.charAt(x >>> 4 & 0x0f) + hexTab.charAt(x & 0x0f), 16);\n    output.push(hex);\n  }\n\n  return output;\n}\n/**\n * Calculate output length with padding and bit length\n */\n\n\nfunction getOutputLength(inputLength8) {\n  return (inputLength8 + 64 >>> 9 << 4) + 14 + 1;\n}\n/*\n * Calculate the MD5 of an array of little-endian words, and a bit length.\n */\n\n\nfunction wordsToMd5(x, len) {\n  /* append padding */\n  x[len >> 5] |= 0x80 << len % 32;\n  x[getOutputLength(len) - 1] = len;\n  let a = 1732584193;\n  let b = -271733879;\n  let c = -1732584194;\n  let d = 271733878;\n\n  for (let i = 0; i < x.length; i += 16) {\n    const olda = a;\n    const oldb = b;\n    const oldc = c;\n    const oldd = d;\n    a = md5ff(a, b, c, d, x[i], 7, -680876936);\n    d = md5ff(d, a, b, c, x[i + 1], 12, -389564586);\n    c = md5ff(c, d, a, b, x[i + 2], 17, 606105819);\n    b = md5ff(b, c, d, a, x[i + 3], 22, -1044525330);\n    a = md5ff(a, b, c, d, x[i + 4], 7, -176418897);\n    d = md5ff(d, a, b, c, x[i + 5], 12, 1200080426);\n    c = md5ff(c, d, a, b, x[i + 6], 17, -1473231341);\n    b = md5ff(b, c, d, a, x[i + 7], 22, -45705983);\n    a = md5ff(a, b, c, d, x[i + 8], 7, 1770035416);\n    d = md5ff(d, a, b, c, x[i + 9], 12, -1958414417);\n    c = md5ff(c, d, a, b, x[i + 10], 17, -42063);\n    b = md5ff(b, c, d, a, x[i + 11], 22, -1990404162);\n    a = md5ff(a, b, c, d, x[i + 12], 7, 1804603682);\n    d = md5ff(d, a, b, c, x[i + 13], 12, -40341101);\n    c = md5ff(c, d, a, b, x[i + 14], 17, -1502002290);\n    b = md5ff(b, c, d, a, x[i + 15], 22, 1236535329);\n    a = md5gg(a, b, c, d, x[i + 1], 5, -165796510);\n    d = md5gg(d, a, b, c, x[i + 6], 9, -1069501632);\n    c = md5gg(c, d, a, b, x[i + 11], 14, 643717713);\n    b = md5gg(b, c, d, a, x[i], 20, -373897302);\n    a = md5gg(a, b, c, d, x[i + 5], 5, -701558691);\n    d = md5gg(d, a, b, c, x[i + 10], 9, 38016083);\n    c = md5gg(c, d, a, b, x[i + 15], 14, -660478335);\n    b = md5gg(b, c, d, a, x[i + 4], 20, -405537848);\n    a = md5gg(a, b, c, d, x[i + 9], 5, 568446438);\n    d = md5gg(d, a, b, c, x[i + 14], 9, -1019803690);\n    c = md5gg(c, d, a, b, x[i + 3], 14, -187363961);\n    b = md5gg(b, c, d, a, x[i + 8], 20, 1163531501);\n    a = md5gg(a, b, c, d, x[i + 13], 5, -1444681467);\n    d = md5gg(d, a, b, c, x[i + 2], 9, -51403784);\n    c = md5gg(c, d, a, b, x[i + 7], 14, 1735328473);\n    b = md5gg(b, c, d, a, x[i + 12], 20, -1926607734);\n    a = md5hh(a, b, c, d, x[i + 5], 4, -378558);\n    d = md5hh(d, a, b, c, x[i + 8], 11, -2022574463);\n    c = md5hh(c, d, a, b, x[i + 11], 16, 1839030562);\n    b = md5hh(b, c, d, a, x[i + 14], 23, -35309556);\n    a = md5hh(a, b, c, d, x[i + 1], 4, -1530992060);\n    d = md5hh(d, a, b, c, x[i + 4], 11, 1272893353);\n    c = md5hh(c, d, a, b, x[i + 7], 16, -155497632);\n    b = md5hh(b, c, d, a, x[i + 10], 23, -1094730640);\n    a = md5hh(a, b, c, d, x[i + 13], 4, 681279174);\n    d = md5hh(d, a, b, c, x[i], 11, -358537222);\n    c = md5hh(c, d, a, b, x[i + 3], 16, -722521979);\n    b = md5hh(b, c, d, a, x[i + 6], 23, 76029189);\n    a = md5hh(a, b, c, d, x[i + 9], 4, -640364487);\n    d = md5hh(d, a, b, c, x[i + 12], 11, -421815835);\n    c = md5hh(c, d, a, b, x[i + 15], 16, 530742520);\n    b = md5hh(b, c, d, a, x[i + 2], 23, -995338651);\n    a = md5ii(a, b, c, d, x[i], 6, -198630844);\n    d = md5ii(d, a, b, c, x[i + 7], 10, 1126891415);\n    c = md5ii(c, d, a, b, x[i + 14], 15, -1416354905);\n    b = md5ii(b, c, d, a, x[i + 5], 21, -57434055);\n    a = md5ii(a, b, c, d, x[i + 12], 6, 1700485571);\n    d = md5ii(d, a, b, c, x[i + 3], 10, -1894986606);\n    c = md5ii(c, d, a, b, x[i + 10], 15, -1051523);\n    b = md5ii(b, c, d, a, x[i + 1], 21, -2054922799);\n    a = md5ii(a, b, c, d, x[i + 8], 6, 1873313359);\n    d = md5ii(d, a, b, c, x[i + 15], 10, -30611744);\n    c = md5ii(c, d, a, b, x[i + 6], 15, -1560198380);\n    b = md5ii(b, c, d, a, x[i + 13], 21, 1309151649);\n    a = md5ii(a, b, c, d, x[i + 4], 6, -145523070);\n    d = md5ii(d, a, b, c, x[i + 11], 10, -1120210379);\n    c = md5ii(c, d, a, b, x[i + 2], 15, 718787259);\n    b = md5ii(b, c, d, a, x[i + 9], 21, -343485551);\n    a = safeAdd(a, olda);\n    b = safeAdd(b, oldb);\n    c = safeAdd(c, oldc);\n    d = safeAdd(d, oldd);\n  }\n\n  return [a, b, c, d];\n}\n/*\n * Convert an array bytes to an array of little-endian words\n * Characters >255 have their high-byte silently ignored.\n */\n\n\nfunction bytesToWords(input) {\n  if (input.length === 0) {\n    return [];\n  }\n\n  const length8 = input.length * 8;\n  const output = new Uint32Array(getOutputLength(length8));\n\n  for (let i = 0; i < length8; i += 8) {\n    output[i >> 5] |= (input[i / 8] & 0xff) << i % 32;\n  }\n\n  return output;\n}\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */\n\n\nfunction safeAdd(x, y) {\n  const lsw = (x & 0xffff) + (y & 0xffff);\n  const msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return msw << 16 | lsw & 0xffff;\n}\n/*\n * Bitwise rotate a 32-bit number to the left.\n */\n\n\nfunction bitRotateLeft(num, cnt) {\n  return num << cnt | num >>> 32 - cnt;\n}\n/*\n * These functions implement the four basic operations the algorithm uses.\n */\n\n\nfunction md5cmn(q, a, b, x, s, t) {\n  return safeAdd(bitRotateLeft(safeAdd(safeAdd(a, q), safeAdd(x, t)), s), b);\n}\n\nfunction md5ff(a, b, c, d, x, s, t) {\n  return md5cmn(b & c | ~b & d, a, b, x, s, t);\n}\n\nfunction md5gg(a, b, c, d, x, s, t) {\n  return md5cmn(b & d | c & ~d, a, b, x, s, t);\n}\n\nfunction md5hh(a, b, c, d, x, s, t) {\n  return md5cmn(b ^ c ^ d, a, b, x, s, t);\n}\n\nfunction md5ii(a, b, c, d, x, s, t) {\n  return md5cmn(c ^ (b | ~d), a, b, x, s, t);\n}\n\nexport default md5;", "import v35 from './v35.js';\nimport md5 from './md5.js';\nconst v3 = v35('v3', 0x30, md5);\nexport default v3;", "const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default {\n  randomUUID\n};", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return unsafeStringify(rnds);\n}\n\nexport default v4;", "// Adapted from <PERSON>' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\nfunction f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n\n    case 1:\n      return x ^ y ^ z;\n\n    case 2:\n      return x & y ^ x & z ^ y & z;\n\n    case 3:\n      return x ^ y ^ z;\n  }\n}\n\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\n\nfunction sha1(bytes) {\n  const K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  const H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n\n  if (typeof bytes === 'string') {\n    const msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = [];\n\n    for (let i = 0; i < msg.length; ++i) {\n      bytes.push(msg.charCodeAt(i));\n    }\n  } else if (!Array.isArray(bytes)) {\n    // Convert Array-like to Array\n    bytes = Array.prototype.slice.call(bytes);\n  }\n\n  bytes.push(0x80);\n  const l = bytes.length / 4 + 2;\n  const N = Math.ceil(l / 16);\n  const M = new Array(N);\n\n  for (let i = 0; i < N; ++i) {\n    const arr = new Uint32Array(16);\n\n    for (let j = 0; j < 16; ++j) {\n      arr[j] = bytes[i * 64 + j * 4] << 24 | bytes[i * 64 + j * 4 + 1] << 16 | bytes[i * 64 + j * 4 + 2] << 8 | bytes[i * 64 + j * 4 + 3];\n    }\n\n    M[i] = arr;\n  }\n\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n\n  for (let i = 0; i < N; ++i) {\n    const W = new Uint32Array(80);\n\n    for (let t = 0; t < 16; ++t) {\n      W[t] = M[i][t];\n    }\n\n    for (let t = 16; t < 80; ++t) {\n      W[t] = ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n    }\n\n    let a = H[0];\n    let b = H[1];\n    let c = H[2];\n    let d = H[3];\n    let e = H[4];\n\n    for (let t = 0; t < 80; ++t) {\n      const s = Math.floor(t / 20);\n      const T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[t] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n\n  return [H[0] >> 24 & 0xff, H[0] >> 16 & 0xff, H[0] >> 8 & 0xff, H[0] & 0xff, H[1] >> 24 & 0xff, H[1] >> 16 & 0xff, H[1] >> 8 & 0xff, H[1] & 0xff, H[2] >> 24 & 0xff, H[2] >> 16 & 0xff, H[2] >> 8 & 0xff, H[2] & 0xff, H[3] >> 24 & 0xff, H[3] >> 16 & 0xff, H[3] >> 8 & 0xff, H[3] & 0xff, H[4] >> 24 & 0xff, H[4] >> 16 & 0xff, H[4] >> 8 & 0xff, H[4] & 0xff];\n}\n\nexport default sha1;", "import v35 from './v35.js';\nimport sha1 from './sha1.js';\nconst v5 = v35('v5', 0x50, sha1);\nexport default v5;", "export function isLegacyTransactionInstance(transaction) {\n    return transaction.version === undefined;\n}\nexport function serializeTransaction(transaction) {\n    return isLegacyTransactionInstance(transaction)\n        ? transaction.serialize({\n            verifySignatures: false,\n            requireAllSignatures: false\n        })\n        : transaction.serialize();\n}\nexport function serializeTransactionMessage(transaction) {\n    return isLegacyTransactionInstance(transaction)\n        ? transaction.serializeMessage()\n        : transaction.message.serialize();\n}\nexport function addSignature(transaction, publicKey, signature) {\n    if (isLegacyTransactionInstance(transaction)) {\n        transaction.addSignature(publicKey, Buffer.from(signature));\n    }\n    else {\n        const signerPubkeys = transaction.message.staticAccountKeys.slice(0, transaction.message.header.numRequiredSignatures);\n        const signerIndex = signerPubkeys.findIndex((pubkey) => pubkey.equals(publicKey));\n        if (signerIndex >= 0) {\n            transaction.signatures[signerIndex] = signature;\n        }\n    }\n}\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nexport function isSnapSupported(provider) {\n    return __awaiter(this, void 0, void 0, function* () {\n        try {\n            yield provider.request({ method: 'wallet_getSnaps' });\n            return true;\n        }\n        catch (error) {\n            return false;\n        }\n    });\n}\nexport function detectProvider() {\n    return __awaiter(this, void 0, void 0, function* () {\n        try {\n            const provider = window.ethereum;\n            if (!provider) {\n                return null;\n            }\n            if (provider.providers && Array.isArray(provider.providers)) {\n                const providers = provider.providers;\n                for (const provider of providers) {\n                    if (yield isSnapSupported(provider)) {\n                        return provider;\n                    }\n                }\n            }\n            if (provider.detected && Array.isArray(provider.detected)) {\n                const providers = provider.detected;\n                for (const provider of providers) {\n                    if (yield isSnapSupported(provider)) {\n                        return provider;\n                    }\n                }\n            }\n            if (yield isSnapSupported(provider)) {\n                return provider;\n            }\n            return null;\n        }\n        catch (error) {\n            console.error(error);\n            return null;\n        }\n    });\n}\n", "// This is copied from @solana/wallet-standard-chains\n/** Solana Mainnet (beta) cluster, e.g. https://api.mainnet-beta.solana.com */\nexport const SOLANA_MAINNET_CHAIN = 'solana:mainnet';\n/** Solana Devnet cluster, e.g. https://api.devnet.solana.com */\nexport const SOLANA_DEVNET_CHAIN = 'solana:devnet';\n/** Solana Testnet cluster, e.g. https://api.testnet.solana.com */\nexport const SOLANA_TESTNET_CHAIN = 'solana:testnet';\n/** Solana Localnet cluster, e.g. http://localhost:8899 */\nexport const SOLANA_LOCALNET_CHAIN = 'solana:localnet';\n/** Array of all Solana clusters */\nexport const SOLANA_CHAINS = [\n    SOLANA_MAINNET_CHAIN,\n    SOLANA_DEVNET_CHAIN,\n    SOLANA_TESTNET_CHAIN,\n    SOLANA_LOCALNET_CHAIN\n];\n/**\n * Check if a chain corresponds with one of the Solana clusters.\n */\nexport function is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(chain) {\n    return SOLANA_CHAINS.includes(chain);\n}\n", "// This is copied with modification from @wallet-standard/wallet\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar _StandardSolflareMetaMaskWalletAccount_address, _StandardSolflareMetaMaskWalletAccount_publicKey, _StandardSolflareMetaMaskWalletAccount_chains, _StandardSolflareMetaMaskWalletAccount_features, _StandardSolflareMetaMaskWalletAccount_label, _StandardSolflareMetaMaskWalletAccount_icon;\nimport { SolanaSignAndSendTransaction, SolanaSignMessage, SolanaSignTransaction } from '@solana/wallet-standard-features';\nimport { SOLANA_CHAINS } from './solana.js';\nconst chains = SOLANA_CHAINS;\nconst features = [SolanaSignAndSendTransaction, SolanaSignTransaction, SolanaSignMessage];\nexport class StandardSolflareMetaMaskWalletAccount {\n    get address() {\n        return __classPrivateFieldGet(this, _StandardSolflareMetaMaskWalletAccount_address, \"f\");\n    }\n    get publicKey() {\n        return __classPrivateFieldGet(this, _StandardSolflareMetaMaskWalletAccount_publicKey, \"f\").slice();\n    }\n    get chains() {\n        return __classPrivateFieldGet(this, _StandardSolflareMetaMaskWalletAccount_chains, \"f\").slice();\n    }\n    get features() {\n        return __classPrivateFieldGet(this, _StandardSolflareMetaMaskWalletAccount_features, \"f\").slice();\n    }\n    get label() {\n        return __classPrivateFieldGet(this, _StandardSolflareMetaMaskWalletAccount_label, \"f\");\n    }\n    get icon() {\n        return __classPrivateFieldGet(this, _StandardSolflareMetaMaskWalletAccount_icon, \"f\");\n    }\n    constructor({ address, publicKey, label, icon }) {\n        _StandardSolflareMetaMaskWalletAccount_address.set(this, void 0);\n        _StandardSolflareMetaMaskWalletAccount_publicKey.set(this, void 0);\n        _StandardSolflareMetaMaskWalletAccount_chains.set(this, void 0);\n        _StandardSolflareMetaMaskWalletAccount_features.set(this, void 0);\n        _StandardSolflareMetaMaskWalletAccount_label.set(this, void 0);\n        _StandardSolflareMetaMaskWalletAccount_icon.set(this, void 0);\n        if (new.target === StandardSolflareMetaMaskWalletAccount) {\n            Object.freeze(this);\n        }\n        __classPrivateFieldSet(this, _StandardSolflareMetaMaskWalletAccount_address, address, \"f\");\n        __classPrivateFieldSet(this, _StandardSolflareMetaMaskWalletAccount_publicKey, publicKey, \"f\");\n        __classPrivateFieldSet(this, _StandardSolflareMetaMaskWalletAccount_chains, chains, \"f\");\n        __classPrivateFieldSet(this, _StandardSolflareMetaMaskWalletAccount_features, features, \"f\");\n        __classPrivateFieldSet(this, _StandardSolflareMetaMaskWalletAccount_label, label, \"f\");\n        __classPrivateFieldSet(this, _StandardSolflareMetaMaskWalletAccount_icon, icon, \"f\");\n    }\n}\n_StandardSolflareMetaMaskWalletAccount_address = new WeakMap(), _StandardSolflareMetaMaskWalletAccount_publicKey = new WeakMap(), _StandardSolflareMetaMaskWalletAccount_chains = new WeakMap(), _StandardSolflareMetaMaskWalletAccount_features = new WeakMap(), _StandardSolflareMetaMaskWalletAccount_label = new WeakMap(), _StandardSolflareMetaMaskWalletAccount_icon = new WeakMap();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI,SAAS;AASb,aAAS,SAAS;AAAA,IAAC;AASnB,QAAI,OAAO,QAAQ;AACjB,aAAO,YAAY,uBAAO,OAAO,IAAI;AAMrC,UAAI,CAAC,IAAI,OAAO,EAAE,UAAW,UAAS;AAAA,IACxC;AAWA,aAAS,GAAG,IAAI,SAAS,MAAM;AAC7B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AAAA,IACtB;AAaA,aAAS,YAAY,SAAS,OAAO,IAAI,SAAS,MAAM;AACtD,UAAI,OAAO,OAAO,YAAY;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,WAAW,IAAI,GAAG,IAAI,WAAW,SAAS,IAAI,GAC9C,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,QAAQ,QAAQ,GAAG,EAAG,SAAQ,QAAQ,GAAG,IAAI,UAAU,QAAQ;AAAA,eAC3D,CAAC,QAAQ,QAAQ,GAAG,EAAE,GAAI,SAAQ,QAAQ,GAAG,EAAE,KAAK,QAAQ;AAAA,UAChE,SAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG,QAAQ;AAE3D,aAAO;AAAA,IACT;AASA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,EAAE,QAAQ,iBAAiB,EAAG,SAAQ,UAAU,IAAI,OAAO;AAAA,UAC1D,QAAO,QAAQ,QAAQ,GAAG;AAAA,IACjC;AASA,aAASA,gBAAe;AACtB,WAAK,UAAU,IAAI,OAAO;AAC1B,WAAK,eAAe;AAAA,IACtB;AASA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,UAAI,QAAQ,CAAC,GACT,QACA;AAEJ,UAAI,KAAK,iBAAiB,EAAG,QAAO;AAEpC,WAAK,QAAS,SAAS,KAAK,SAAU;AACpC,YAAI,IAAI,KAAK,QAAQ,IAAI,EAAG,OAAM,KAAK,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAEA,UAAI,OAAO,uBAAuB;AAChC,eAAO,MAAM,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,OAAO;AAC3D,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,WAAW,KAAK,QAAQ,GAAG;AAE/B,UAAI,CAAC,SAAU,QAAO,CAAC;AACvB,UAAI,SAAS,GAAI,QAAO,CAAC,SAAS,EAAE;AAEpC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK;AAClE,WAAG,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,gBAAgB,SAAS,cAAc,OAAO;AACnE,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,CAAC,UAAW,QAAO;AACvB,UAAI,UAAU,GAAI,QAAO;AACzB,aAAO,UAAU;AAAA,IACnB;AASA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AACrE,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG,EAAG,QAAO;AAE/B,UAAI,YAAY,KAAK,QAAQ,GAAG,GAC5B,MAAM,UAAU,QAChB,MACA;AAEJ,UAAI,UAAU,IAAI;AAChB,YAAI,UAAU,KAAM,MAAK,eAAe,OAAO,UAAU,IAAI,QAAW,IAAI;AAE5E,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,OAAO,GAAG;AAAA,UACrD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,EAAE,GAAG;AAAA,UACzD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,EAAE,GAAG;AAAA,UAC7D,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,UACjE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,UACrE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3E;AAEA,aAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAClD,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC3B;AAEA,kBAAU,GAAG,MAAM,UAAU,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,YAAI,SAAS,UAAU,QACnB;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,cAAI,UAAU,CAAC,EAAE,KAAM,MAAK,eAAe,OAAO,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAElF,kBAAQ,KAAK;AAAA,YACX,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,OAAO;AAAG;AAAA,YACpD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,EAAE;AAAG;AAAA,YACxD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,EAAE;AAAG;AAAA,YAC5D,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,IAAI,EAAE;AAAG;AAAA,YAChE;AACE,kBAAI,CAAC,KAAM,MAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAC7D,qBAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,cAC3B;AAEA,wBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC,EAAE,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,IAAAA,cAAa,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS;AAC1D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,KAAK;AAAA,IACpD;AAWA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS;AAC9D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,IACnD;AAYA,IAAAA,cAAa,UAAU,iBAAiB,SAAS,eAAe,OAAO,IAAI,SAAS,MAAM;AACxF,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG,EAAG,QAAO;AAC/B,UAAI,CAAC,IAAI;AACP,mBAAW,MAAM,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,UAAU,IAAI;AAChB,YACE,UAAU,OAAO,OAChB,CAAC,QAAQ,UAAU,UACnB,CAAC,WAAW,UAAU,YAAY,UACnC;AACA,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACvE,cACE,UAAU,CAAC,EAAE,OAAO,MACnB,QAAQ,CAAC,UAAU,CAAC,EAAE,QACtB,WAAW,UAAU,CAAC,EAAE,YAAY,SACrC;AACA,mBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAKA,YAAI,OAAO,OAAQ,MAAK,QAAQ,GAAG,IAAI,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA,YACpE,YAAW,MAAM,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,qBAAqB,SAAS,mBAAmB,OAAO;AAC7E,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,SAAS,SAAS,QAAQ;AAChC,YAAI,KAAK,QAAQ,GAAG,EAAG,YAAW,MAAM,GAAG;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,IAAI,OAAO;AAC1B,aAAK,eAAe;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAKA,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AACpD,IAAAA,cAAa,UAAU,cAAcA,cAAa,UAAU;AAK5D,IAAAA,cAAa,WAAW;AAKxB,IAAAA,cAAa,eAAeA;AAK5B,QAAI,gBAAgB,OAAO,QAAQ;AACjC,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;AC/UA;AAAA;AAAA;AAMA,aAAS,KAAM,UAAU;AACvB,UAAI,SAAS,UAAU,KAAK;AAAE,cAAM,IAAI,UAAU,mBAAmB;AAAA,MAAE;AACvE,UAAI,WAAW,IAAI,WAAW,GAAG;AACjC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,iBAAS,CAAC,IAAI;AAAA,MAChB;AACA,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,IAAI,SAAS,OAAO,CAAC;AACzB,YAAI,KAAK,EAAE,WAAW,CAAC;AACvB,YAAI,SAAS,EAAE,MAAM,KAAK;AAAE,gBAAM,IAAI,UAAU,IAAI,eAAe;AAAA,QAAE;AACrE,iBAAS,EAAE,IAAI;AAAA,MACjB;AACA,UAAI,OAAO,SAAS;AACpB,UAAI,SAAS,SAAS,OAAO,CAAC;AAC9B,UAAI,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAC1C,UAAI,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAC3C,eAAS,OAAQ,QAAQ;AACvB,YAAI,kBAAkB,YAAY;AAAA,QAClC,WAAW,YAAY,OAAO,MAAM,GAAG;AACrC,mBAAS,IAAI,WAAW,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;AAAA,QAC7E,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,mBAAS,WAAW,KAAK,MAAM;AAAA,QACjC;AACA,YAAI,EAAE,kBAAkB,aAAa;AAAE,gBAAM,IAAI,UAAU,qBAAqB;AAAA,QAAE;AAClF,YAAI,OAAO,WAAW,GAAG;AAAE,iBAAO;AAAA,QAAG;AAErC,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,OAAO,OAAO;AAClB,eAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,GAAG;AAC9C;AACA;AAAA,QACF;AAEA,YAAI,QAAS,OAAO,UAAU,UAAU,MAAO;AAC/C,YAAI,MAAM,IAAI,WAAW,IAAI;AAE7B,eAAO,WAAW,MAAM;AACtB,cAAI,QAAQ,OAAO,MAAM;AAEzB,cAAIC,KAAI;AACR,mBAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAI,WAAY,QAAQ,IAAK,OAAOA,MAAK;AAChF,qBAAU,MAAM,IAAI,GAAG,MAAO;AAC9B,gBAAI,GAAG,IAAK,QAAQ,SAAU;AAC9B,oBAAS,QAAQ,SAAU;AAAA,UAC7B;AACA,cAAI,UAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAAE;AACrD,mBAASA;AACT;AAAA,QACF;AAEA,YAAI,MAAM,OAAO;AACjB,eAAO,QAAQ,QAAQ,IAAI,GAAG,MAAM,GAAG;AACrC;AAAA,QACF;AAEA,YAAI,MAAM,OAAO,OAAO,MAAM;AAC9B,eAAO,MAAM,MAAM,EAAE,KAAK;AAAE,iBAAO,SAAS,OAAO,IAAI,GAAG,CAAC;AAAA,QAAE;AAC7D,eAAO;AAAA,MACT;AACA,eAAS,aAAc,QAAQ;AAC7B,YAAI,OAAO,WAAW,UAAU;AAAE,gBAAM,IAAI,UAAU,iBAAiB;AAAA,QAAE;AACzE,YAAI,OAAO,WAAW,GAAG;AAAE,iBAAO,IAAI,WAAW;AAAA,QAAE;AACnD,YAAI,MAAM;AAEV,YAAI,SAAS;AACb,YAAI,SAAS;AACb,eAAO,OAAO,GAAG,MAAM,QAAQ;AAC7B;AACA;AAAA,QACF;AAEA,YAAI,QAAU,OAAO,SAAS,OAAO,SAAU,MAAO;AACtD,YAAI,OAAO,IAAI,WAAW,IAAI;AAE9B,eAAO,OAAO,GAAG,GAAG;AAElB,cAAI,WAAW,OAAO,WAAW,GAAG;AAEpC,cAAI,WAAW,KAAK;AAAE;AAAA,UAAO;AAE7B,cAAI,QAAQ,SAAS,QAAQ;AAE7B,cAAI,UAAU,KAAK;AAAE;AAAA,UAAO;AAC5B,cAAIA,KAAI;AACR,mBAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAI,WAAY,QAAQ,IAAK,OAAOA,MAAK;AAChF,qBAAU,OAAO,KAAK,GAAG,MAAO;AAChC,iBAAK,GAAG,IAAK,QAAQ,QAAS;AAC9B,oBAAS,QAAQ,QAAS;AAAA,UAC5B;AACA,cAAI,UAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAAE;AACrD,mBAASA;AACT;AAAA,QACF;AAEA,YAAI,MAAM,OAAO;AACjB,eAAO,QAAQ,QAAQ,KAAK,GAAG,MAAM,GAAG;AACtC;AAAA,QACF;AACA,YAAI,MAAM,IAAI,WAAW,UAAU,OAAO,IAAI;AAC9C,YAAIC,KAAI;AACR,eAAO,QAAQ,MAAM;AACnB,cAAIA,IAAG,IAAI,KAAK,KAAK;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,eAAS,OAAQ,QAAQ;AACvB,YAAI,SAAS,aAAa,MAAM;AAChC,YAAI,QAAQ;AAAE,iBAAO;AAAA,QAAO;AAC5B,cAAM,IAAI,MAAM,aAAa,OAAO,YAAY;AAAA,MAClD;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;AC5HjB;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,WAAW;AAEjB,WAAO,UAAU,MAAM,QAAQ;AAAA;AAAA;;;ACM/B;;;ACTA,mBAAyB;AAGzB,IAAO,wBAAQ,aAAAC;;;ADQf,kBAAiB;;;AERjB,IAAI;AACJ,IAAM,QAAQ,IAAI,WAAW,EAAE;AAChB,SAAR,MAAuB;AAE5B,MAAI,CAAC,iBAAiB;AAEpB,sBAAkB,OAAO,WAAW,eAAe,OAAO,mBAAmB,OAAO,gBAAgB,KAAK,MAAM;AAE/G,QAAI,CAAC,iBAAiB;AACpB,YAAM,IAAI,MAAM,0GAA0G;AAAA,IAC5H;AAAA,EACF;AAEA,SAAO,gBAAgB,KAAK;AAC9B;;;ACjBA,IAAO,gBAAQ;;;ACEf,SAAS,SAAS,MAAM;AACtB,SAAO,OAAO,SAAS,YAAY,cAAM,KAAK,IAAI;AACpD;AAEA,IAAO,mBAAQ;;;ACAf,IAAM,YAAY,CAAC;AAEnB,SAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,YAAU,MAAM,IAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;AAClD;AAEO,SAAS,gBAAgB,KAAK,SAAS,GAAG;AAG/C,SAAO,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC;AACnf;;;ACdA,SAAS,MAAM,MAAM;AACnB,MAAI,CAAC,iBAAS,IAAI,GAAG;AACnB,UAAM,UAAU,cAAc;AAAA,EAChC;AAEA,MAAI;AACJ,QAAM,MAAM,IAAI,WAAW,EAAE;AAE7B,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,OAAO;AAClD,MAAI,CAAC,IAAI,MAAM,KAAK;AACpB,MAAI,CAAC,IAAI,MAAM,IAAI;AACnB,MAAI,CAAC,IAAI,IAAI;AAEb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,EAAE,GAAG,EAAE,OAAO;AACnD,MAAI,CAAC,IAAI,IAAI;AAEb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO;AACpD,MAAI,CAAC,IAAI,IAAI;AAEb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO;AACpD,MAAI,CAAC,IAAI,IAAI;AAGb,MAAI,EAAE,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,KAAK,gBAAgB;AACnE,MAAI,EAAE,IAAI,IAAI,aAAc;AAC5B,MAAI,EAAE,IAAI,MAAM,KAAK;AACrB,MAAI,EAAE,IAAI,MAAM,KAAK;AACrB,MAAI,EAAE,IAAI,MAAM,IAAI;AACpB,MAAI,EAAE,IAAI,IAAI;AACd,SAAO;AACT;AAEA,IAAO,gBAAQ;;;AC/Bf,SAAS,cAAc,KAAK;AAC1B,QAAM,SAAS,mBAAmB,GAAG,CAAC;AAEtC,QAAM,QAAQ,CAAC;AAEf,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,UAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,EAC9B;AAEA,SAAO;AACT;AAEO,IAAM,MAAM;AACZ,IAAM,MAAM;AACJ,SAAR,IAAqB,MAAM,SAAS,UAAU;AACnD,WAAS,aAAa,OAAO,WAAW,KAAK,QAAQ;AACnD,QAAI;AAEJ,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,cAAc,KAAK;AAAA,IAC7B;AAEA,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,cAAM,SAAS;AAAA,IAC7B;AAEA,UAAM,aAAa,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,YAAY,IAAI;AACpG,YAAM,UAAU,kEAAkE;AAAA,IACpF;AAKA,QAAI,QAAQ,IAAI,WAAW,KAAK,MAAM,MAAM;AAC5C,UAAM,IAAI,SAAS;AACnB,UAAM,IAAI,OAAO,UAAU,MAAM;AACjC,YAAQ,SAAS,KAAK;AACtB,UAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAO;AAC7B,UAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAO;AAE7B,QAAI,KAAK;AACP,eAAS,UAAU;AAEnB,eAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,YAAI,SAAS,CAAC,IAAI,MAAM,CAAC;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,gBAAgB,KAAK;AAAA,EAC9B;AAGA,MAAI;AACF,iBAAa,OAAO;AAAA,EACtB,SAAS,KAAK;AAAA,EAAC;AAGf,eAAa,MAAM;AACnB,eAAa,MAAM;AACnB,SAAO;AACT;;;AC7CA,SAAS,IAAI,OAAO;AAClB,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,MAAM,SAAS,mBAAmB,KAAK,CAAC;AAE9C,YAAQ,IAAI,WAAW,IAAI,MAAM;AAEjC,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAM,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,IAC7B;AAAA,EACF;AAEA,SAAO,qBAAqB,WAAW,aAAa,KAAK,GAAG,MAAM,SAAS,CAAC,CAAC;AAC/E;AAMA,SAAS,qBAAqB,OAAO;AACnC,QAAM,SAAS,CAAC;AAChB,QAAM,WAAW,MAAM,SAAS;AAChC,QAAM,SAAS;AAEf,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AACpC,UAAM,IAAI,MAAM,KAAK,CAAC,MAAM,IAAI,KAAK;AACrC,UAAM,MAAM,SAAS,OAAO,OAAO,MAAM,IAAI,EAAI,IAAI,OAAO,OAAO,IAAI,EAAI,GAAG,EAAE;AAChF,WAAO,KAAK,GAAG;AAAA,EACjB;AAEA,SAAO;AACT;AAMA,SAAS,gBAAgB,cAAc;AACrC,UAAQ,eAAe,OAAO,KAAK,KAAK,KAAK;AAC/C;AAMA,SAAS,WAAW,GAAG,KAAK;AAE1B,IAAE,OAAO,CAAC,KAAK,OAAQ,MAAM;AAC7B,IAAE,gBAAgB,GAAG,IAAI,CAAC,IAAI;AAC9B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AAER,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,IAAI;AACrC,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO;AACb,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,MAAM;AAC3C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,OAAO;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AAAA,EACrB;AAEA,SAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AACpB;AAOA,SAAS,aAAa,OAAO;AAC3B,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,CAAC;AAAA,EACV;AAEA,QAAM,UAAU,MAAM,SAAS;AAC/B,QAAM,SAAS,IAAI,YAAY,gBAAgB,OAAO,CAAC;AAEvD,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,WAAO,KAAK,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,QAAS,IAAI;AAAA,EACjD;AAEA,SAAO;AACT;AAOA,SAAS,QAAQ,GAAG,GAAG;AACrB,QAAM,OAAO,IAAI,UAAW,IAAI;AAChC,QAAM,OAAO,KAAK,OAAO,KAAK,OAAO,OAAO;AAC5C,SAAO,OAAO,KAAK,MAAM;AAC3B;AAMA,SAAS,cAAc,KAAK,KAAK;AAC/B,SAAO,OAAO,MAAM,QAAQ,KAAK;AACnC;AAMA,SAAS,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,QAAQ,cAAc,QAAQ,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AAC3E;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7C;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7C;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACxC;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3C;AAEA,IAAO,cAAQ;;;ACpNf,IAAM,KAAK,IAAI,MAAM,IAAM,WAAG;;;ACF9B,IAAM,aAAa,OAAO,WAAW,eAAe,OAAO,cAAc,OAAO,WAAW,KAAK,MAAM;AACtG,IAAO,iBAAQ;AAAA,EACb;AACF;;;ACCA,SAAS,GAAG,SAAS,KAAK,QAAQ;AAChC,MAAI,eAAO,cAAc,CAAC,OAAO,CAAC,SAAS;AACzC,WAAO,eAAO,WAAW;AAAA,EAC3B;AAEA,YAAU,WAAW,CAAC;AACtB,QAAM,OAAO,QAAQ,WAAW,QAAQ,OAAO,KAAK;AAEpD,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAC3B,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAE3B,MAAI,KAAK;AACP,aAAS,UAAU;AAEnB,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAI,SAAS,CAAC,IAAI,KAAK,CAAC;AAAA,IAC1B;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,IAAI;AAC7B;AAEA,IAAO,aAAQ;;;AC1Bf,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AACrB,UAAQ,GAAG;AAAA,IACT,KAAK;AACH,aAAO,IAAI,IAAI,CAAC,IAAI;AAAA,IAEtB,KAAK;AACH,aAAO,IAAI,IAAI;AAAA,IAEjB,KAAK;AACH,aAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,IAE7B,KAAK;AACH,aAAO,IAAI,IAAI;AAAA,EACnB;AACF;AAEA,SAAS,KAAK,GAAG,GAAG;AAClB,SAAO,KAAK,IAAI,MAAM,KAAK;AAC7B;AAEA,SAAS,KAAK,OAAO;AACnB,QAAM,IAAI,CAAC,YAAY,YAAY,YAAY,UAAU;AACzD,QAAM,IAAI,CAAC,YAAY,YAAY,YAAY,WAAY,UAAU;AAErE,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,MAAM,SAAS,mBAAmB,KAAK,CAAC;AAE9C,YAAQ,CAAC;AAET,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF,WAAW,CAAC,MAAM,QAAQ,KAAK,GAAG;AAEhC,YAAQ,MAAM,UAAU,MAAM,KAAK,KAAK;AAAA,EAC1C;AAEA,QAAM,KAAK,GAAI;AACf,QAAM,IAAI,MAAM,SAAS,IAAI;AAC7B,QAAM,IAAI,KAAK,KAAK,IAAI,EAAE;AAC1B,QAAM,IAAI,IAAI,MAAM,CAAC;AAErB,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAM,MAAM,IAAI,YAAY,EAAE;AAE9B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAI,CAAC,IAAI,MAAM,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,IACpI;AAEA,MAAE,CAAC,IAAI;AAAA,EACT;AAEA,IAAE,IAAI,CAAC,EAAE,EAAE,KAAK,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE;AACtD,IAAE,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;AACtC,IAAE,IAAI,CAAC,EAAE,EAAE,KAAK,MAAM,SAAS,KAAK,IAAI;AAExC,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAM,IAAI,IAAI,YAAY,EAAE;AAE5B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,QAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,IACf;AAEA,aAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAC5B,QAAE,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;AAAA,IAC5D;AAEA,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AAEX,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,YAAM,IAAI,KAAK,MAAM,IAAI,EAAE;AAC3B,YAAM,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM;AAC3D,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,GAAG,EAAE,MAAM;AACpB,UAAI;AACJ,UAAI;AAAA,IACN;AAEA,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AAAA,EACtB;AAEA,SAAO,CAAC,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,GAAI;AACjW;AAEA,IAAO,eAAQ;;;AC7Ff,IAAM,KAAK,IAAI,MAAM,IAAM,YAAI;;;ACFxB,SAAS,4BAA4B,aAAa;AACrD,SAAO,YAAY,YAAY;AACnC;AACO,SAAS,qBAAqB,aAAa;AAC9C,SAAO,4BAA4B,WAAW,IACxC,YAAY,UAAU;AAAA,IACpB,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,EAC1B,CAAC,IACC,YAAY,UAAU;AAChC;;;ACVA,IAAI,YAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AACO,SAAS,gBAAgB,UAAU;AACtC,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,QAAI;AACA,YAAM,SAAS,QAAQ,EAAE,QAAQ,kBAAkB,CAAC;AACpD,aAAO;AAAA,IACX,SACO,OAAO;AACV,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AACO,SAAS,iBAAiB;AAC7B,SAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,QAAI;AACA,YAAM,WAAW,OAAO;AACxB,UAAI,CAAC,UAAU;AACX,eAAO;AAAA,MACX;AACA,UAAI,SAAS,aAAa,MAAM,QAAQ,SAAS,SAAS,GAAG;AACzD,cAAM,YAAY,SAAS;AAC3B,mBAAWC,aAAY,WAAW;AAC9B,cAAI,MAAM,gBAAgBA,SAAQ,GAAG;AACjC,mBAAOA;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,SAAS,YAAY,MAAM,QAAQ,SAAS,QAAQ,GAAG;AACvD,cAAM,YAAY,SAAS;AAC3B,mBAAWA,aAAY,WAAW;AAC9B,cAAI,MAAM,gBAAgBA,SAAQ,GAAG;AACjC,mBAAOA;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,MAAM,gBAAgB,QAAQ,GAAG;AACjC,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX,SACO,OAAO;AACV,cAAQ,MAAM,KAAK;AACnB,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;;;ACnDO,IAAM,uBAAuB;AAE7B,IAAM,sBAAsB;AAE5B,IAAM,uBAAuB;AAE7B,IAAM,wBAAwB;AAE9B,IAAM,gBAAgB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAIO,SAAS,cAAc,OAAO;AACjC,SAAO,cAAc,SAAS,KAAK;AACvC;;;ACpBA,IAAI,yBAAkE,SAAU,UAAU,OAAO,MAAMC,IAAG;AACtG,MAAI,SAAS,OAAO,CAACA,GAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAACA,KAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,0EAA0E;AACjL,SAAO,SAAS,MAAMA,KAAI,SAAS,MAAMA,GAAE,KAAK,QAAQ,IAAIA,KAAIA,GAAE,QAAQ,MAAM,IAAI,QAAQ;AAChG;AACA,IAAI,yBAAkE,SAAU,UAAU,OAAO,OAAO,MAAMA,IAAG;AAC7G,MAAI,SAAS,IAAK,OAAM,IAAI,UAAU,gCAAgC;AACtE,MAAI,SAAS,OAAO,CAACA,GAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAACA,KAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,yEAAyE;AAChL,SAAQ,SAAS,MAAMA,GAAE,KAAK,UAAU,KAAK,IAAIA,KAAIA,GAAE,QAAQ,QAAQ,MAAM,IAAI,UAAU,KAAK,GAAI;AACxG;AACA,IAAI;AAAJ,IAAoD;AAApD,IAAsG;AAAtG,IAAqJ;AAArJ,IAAsM;AAAtM,IAAoP;AAGpP,IAAM,SAAS;AACf,IAAM,WAAW,CAAC,8BAA8B,uBAAuB,iBAAiB;AACjF,IAAM,wCAAN,MAAM,uCAAsC;AAAA,EAC/C,IAAI,UAAU;AACV,WAAO,uBAAuB,MAAM,gDAAgD,GAAG;AAAA,EAC3F;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,uBAAuB,MAAM,kDAAkD,GAAG,EAAE,MAAM;AAAA,EACrG;AAAA,EACA,IAAI,SAAS;AACT,WAAO,uBAAuB,MAAM,+CAA+C,GAAG,EAAE,MAAM;AAAA,EAClG;AAAA,EACA,IAAI,WAAW;AACX,WAAO,uBAAuB,MAAM,iDAAiD,GAAG,EAAE,MAAM;AAAA,EACpG;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,uBAAuB,MAAM,8CAA8C,GAAG;AAAA,EACzF;AAAA,EACA,IAAI,OAAO;AACP,WAAO,uBAAuB,MAAM,6CAA6C,GAAG;AAAA,EACxF;AAAA,EACA,YAAY,EAAE,SAAS,WAAW,OAAO,KAAK,GAAG;AAC7C,mDAA+C,IAAI,MAAM,MAAM;AAC/D,qDAAiD,IAAI,MAAM,MAAM;AACjE,kDAA8C,IAAI,MAAM,MAAM;AAC9D,oDAAgD,IAAI,MAAM,MAAM;AAChE,iDAA6C,IAAI,MAAM,MAAM;AAC7D,gDAA4C,IAAI,MAAM,MAAM;AAC5D,QAAI,eAAe,wCAAuC;AACtD,aAAO,OAAO,IAAI;AAAA,IACtB;AACA,2BAAuB,MAAM,gDAAgD,SAAS,GAAG;AACzF,2BAAuB,MAAM,kDAAkD,WAAW,GAAG;AAC7F,2BAAuB,MAAM,+CAA+C,QAAQ,GAAG;AACvF,2BAAuB,MAAM,iDAAiD,UAAU,GAAG;AAC3F,2BAAuB,MAAM,8CAA8C,OAAO,GAAG;AACrF,2BAAuB,MAAM,6CAA6C,MAAM,GAAG;AAAA,EACvF;AACJ;AACA,iDAAiD,oBAAI,QAAQ,GAAG,mDAAmD,oBAAI,QAAQ,GAAG,gDAAgD,oBAAI,QAAQ,GAAG,kDAAkD,oBAAI,QAAQ,GAAG,+CAA+C,oBAAI,QAAQ,GAAG,8CAA8C,oBAAI,QAAQ;;;AjBtD1X,IAAIC,aAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAWA,IAAM,mBAAN,MAAM,0BAAyB,sBAAa;AAAA,EACxC,YAAY,QAAQ;AAChB,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,gBAAgB,CAAC;AACtB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB,CAAC;AACzB,SAAK,eAAe,CAAC,UAAU;AAC3B,UAAI,IAAI;AACR,cAAQ,MAAM,MAAM;AAAA,QAChB,KAAK,WAAW;AACZ,eAAK,gBAAgB;AACrB,eAAK,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AACrE,iBAAK,aAAa,MAAM,KAAK;AAC7B,iBAAK,eAAe;AACpB,gBAAI,KAAK,iBAAiB;AACtB,mBAAK,gBAAgB,QAAQ;AAC7B,mBAAK,kBAAkB;AAAA,YAC3B;AACA,iBAAK,WAAW;AAAA,UACpB,OACK;AACD,gBAAI,KAAK,iBAAiB;AACtB,mBAAK,gBAAgB,OAAO;AAC5B,mBAAK,kBAAkB;AAAA,YAC3B;AACA,iBAAK,cAAc;AAAA,UACvB;AACA;AAAA,QACJ;AAAA,QACA,KAAK,cAAc;AACf,cAAI,KAAK,iBAAiB;AACtB,iBAAK,gBAAgB,OAAO;AAC5B,iBAAK,kBAAkB;AAAA,UAC3B;AACA,eAAK,cAAc;AACnB;AAAA,QACJ;AAAA,QACA,KAAK,kBAAkB;AACnB,eAAK,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AACrE,iBAAK,aAAa,MAAM,KAAK;AAC7B,iBAAK,KAAK,kBAAkB,KAAK,SAAS;AAC1C,iBAAK,mBAAmB;AAAA,UAC5B,OACK;AACD,iBAAK,KAAK,kBAAkB,MAAS;AACrC,iBAAK,sBAAsB;AAAA,UAC/B;AACA;AAAA,QACJ;AAAA,QACA,SAAS;AACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,gBAAgB,CAAC,SAAS;AAC3B,UAAI,KAAK,eAAe,QAAQ;AAC5B,YAAI,KAAK,OAAO,SAAS,cAAc;AACnC,eAAK,cAAc;AAAA,QACvB,WACS,KAAK,OAAO,SAAS,QAAQ;AAClC,eAAK,gBAAgB;AAAA,QACzB;AAAA,MACJ,WACS,KAAK,eAAe,eAAe;AACxC,aAAK,cAAc,KAAK,MAAM;AAAA,MAClC;AAAA,IACJ;AACA,SAAK,iBAAiB,CAAC,UAAU;AAC7B,UAAI;AACJ,YAAM,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,iCAAiC;AACzG;AAAA,MACJ;AACA,YAAM,OAAO,MAAM,KAAK,QAAQ,CAAC;AACjC,UAAI,KAAK,SAAS,SAAS;AACvB,aAAK,aAAa,KAAK,KAAK;AAAA,MAChC,WACS,KAAK,SAAS,UAAU;AAC7B,aAAK,cAAc,IAAI;AAAA,MAC3B,WACS,KAAK,SAAS,YAAY;AAC/B,YAAI,KAAK,iBAAiB,KAAK,EAAE,GAAG;AAChC,gBAAM,EAAE,SAAS,OAAO,IAAI,KAAK,iBAAiB,KAAK,EAAE;AACzD,iBAAO,KAAK,iBAAiB,KAAK,EAAE;AACpC,cAAI,KAAK,OAAO;AACZ,mBAAO,KAAK,KAAK;AAAA,UACrB,OACK;AACD,oBAAQ,KAAK,MAAM;AAAA,UACvB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,iBAAiB,MAAM;AACxB,UAAI,KAAK,UAAU;AACf,aAAK,SAAS,OAAO;AACrB,aAAK,WAAW;AAAA,MACpB;AAAA,IACJ;AACA,SAAK,0BAA0B,MAAM;AACjC,YAAM,WAAW,SAAS,uBAAuB,yCAAyC;AAC1F,iBAAW,WAAW,UAAU;AAC5B,YAAI,QAAQ,eAAe;AACvB,kBAAQ,OAAO;AAAA,QACnB;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,iBAAiB,MAAM;AACxB,WAAK,eAAe;AACpB,WAAK,wBAAwB;AAC7B,YAAM,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,aAAa,GAAG,EAAE,IAAI,MAAM,GAAG,GAAG,SAAS,KAAK,YAAY,gBAAgB,QAAQ,OAAO,SAAS,UAAU,IAAI,OAAO,SAAS,SAAS,GAAG,CAAC;AACnM,YAAM,cAAc,OAAO,KAAK,MAAM,EACjC,IAAI,CAAC,QAAQ,GAAG,GAAG,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,EACxD,KAAK,GAAG;AACb,YAAM,YAAY,GAAG,kBAAiB,UAAU,IAAI,WAAW;AAC/D,WAAK,WAAW,SAAS,cAAc,KAAK;AAC5C,WAAK,SAAS,YAAY;AAC1B,WAAK,SAAS,YAAY;AAAA,qBACjB,SAAS;AAAA;AAElB,eAAS,KAAK,YAAY,KAAK,QAAQ;AACvC,WAAK,UAAU,KAAK,SAAS,cAAc,QAAQ;AACnD,aAAO,iBAAiB,WAAW,KAAK,gBAAgB,KAAK;AAAA,IACjE;AACA,SAAK,kBAAkB,MAAM;AACzB,UAAI,KAAK,SAAS;AACd,aAAK,QAAQ,MAAM,MAAM;AACzB,aAAK,QAAQ,MAAM,QAAQ;AAC3B,aAAK,QAAQ,MAAM,SAAS;AAC5B,aAAK,QAAQ,MAAM,QAAQ;AAAA,MAC/B;AAAA,IACJ;AACA,SAAK,gBAAgB,MAAM;AACvB,UAAI,KAAK,SAAS;AACd,aAAK,QAAQ,MAAM,MAAM;AACzB,aAAK,QAAQ,MAAM,SAAS;AAC5B,aAAK,QAAQ,MAAM,OAAO;AAC1B,aAAK,QAAQ,MAAM,QAAQ;AAC3B,aAAK,QAAQ,MAAM,QAAQ;AAC3B,aAAK,QAAQ,MAAM,SAAS;AAAA,MAChC;AAAA,IACJ;AACA,SAAK,gBAAgB,CAAC,WAAW;AAC7B,UAAI,CAAC,KAAK,SAAS;AACf;AAAA,MACJ;AACA,WAAK,QAAQ,MAAM,MAAM,SAAS,OAAO,GAAG,IAAI,GAAG,OAAO,GAAG,OAAO;AACpE,WAAK,QAAQ,MAAM,SAAS,SAAS,OAAO,MAAM,IAAI,GAAG,OAAO,MAAM,OAAO;AAC7E,WAAK,QAAQ,MAAM,OAAO,SAAS,OAAO,IAAI,IAAI,GAAG,OAAO,IAAI,OAAO;AACvE,WAAK,QAAQ,MAAM,QAAQ,SAAS,OAAO,KAAK,IAAI,GAAG,OAAO,KAAK,OAAO;AAC1E,WAAK,QAAQ,MAAM,QAAQ,SAAS,OAAO,KAAK,IAC1C,GAAG,OAAO,KAAK,OACf,OAAO;AACb,WAAK,QAAQ,MAAM,SAAS,SAAS,OAAO,MAAM,IAC5C,GAAG,OAAO,MAAM,OAChB,OAAO;AAAA,IACjB;AACA,SAAK,qBAAqB,CAAC,SAAS;AAChC,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,WAAW;AACpC,cAAM,IAAI,MAAM,sBAAsB;AAAA,MAC1C;AACA,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,YAAI,IAAI;AACR,cAAM,YAAY,WAAO;AACzB,aAAK,iBAAiB,SAAS,IAAI,EAAE,SAAS,OAAO;AACrD,SAAC,MAAM,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AAAA,UACjI,SAAS;AAAA,UACT,MAAM,OAAO,OAAO,EAAE,IAAI,UAAU,GAAG,IAAI;AAAA,QAC/C,GAAG,GAAG;AAAA,MACV,CAAC;AAAA,IACL;AACA,SAAK,aAAa,MAAM;AACpB,WAAK,eAAe;AACpB,WAAK,KAAK,WAAW,KAAK,SAAS;AACnC,WAAK,mBAAmB;AAAA,IAC5B;AACA,SAAK,gBAAgB,MAAM;AACvB,WAAK,aAAa;AAClB,WAAK,eAAe;AACpB,aAAO,oBAAoB,WAAW,KAAK,gBAAgB,KAAK;AAChE,WAAK,eAAe;AACpB,WAAK,KAAK,YAAY;AACtB,WAAK,sBAAsB;AAAA,IAC/B;AACA,SAAK,qBAAqB,MAAM;AAC5B,UAAI,CAAC,KAAK,WAAW;AACjB;AAAA,MACJ;AACA,YAAM,UAAU,KAAK,UAAU,SAAS;AACxC,UAAI,CAAC,KAAK,YAAY,KAAK,SAAS,YAAY,SAAS;AACrD,aAAK,WAAW,IAAI,sCAAsC;AAAA,UACtD;AAAA,UACA,WAAW,KAAK,UAAU,QAAQ;AAAA,QACtC,CAAC;AACD,aAAK,KAAK,mBAAmB,EAAE,UAAU,KAAK,iBAAiB,CAAC;AAAA,MACpE;AAAA,IACJ;AACA,SAAK,wBAAwB,MAAM;AAC/B,UAAI,KAAK,UAAU;AACf,aAAK,WAAW;AAChB,aAAK,KAAK,mBAAmB,EAAE,UAAU,KAAK,iBAAiB,CAAC;AAAA,MACpE;AAAA,IACJ;AACA,QAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAChE,WAAK,WAAW,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AAAA,IAC3E;AACA,QAAI,OAAO,wBAAwB;AAC/B,WAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,aAAa,GAAG,OAAO,sBAAsB;AAAA,IAC3G;AACA,QAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,QAAQ;AAC/D,WAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,aAAa,GAAG,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,MAAM;AAAA,IAC3I;AAAA,EACJ;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK,aAAa,IAAI,UAAU,KAAK,UAAU,IAAI;AAAA,EAC9D;AAAA,EACA,IAAI,kBAAkB;AAClB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,mBAAmB;AACnB,WAAO,KAAK,WAAW,CAAC,KAAK,QAAQ,IAAI,CAAC;AAAA,EAC9C;AAAA,EACA,IAAI,cAAc;AACd,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,cAAc;AACd,WAAO;AAAA,EACX;AAAA,EACA,UAAU;AACN,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAI,KAAK,WAAW;AAChB;AAAA,MACJ;AACA,WAAK,eAAe;AACpB,YAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACnC,aAAK,kBAAkB,EAAE,SAAS,OAAO;AAAA,MAC7C,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA,EACA,aAAa;AACT,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,YAAM,KAAK,mBAAmB;AAAA,QAC1B,QAAQ;AAAA,MACZ,CAAC;AACD,WAAK,cAAc;AAAA,IACvB,CAAC;AAAA,EACL;AAAA,EACA,gBAAgB,aAAa;AACzB,QAAI;AACJ,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,WAAW;AACpC,cAAM,IAAI,MAAM,sBAAsB;AAAA,MAC1C;AACA,UAAI;AACA,cAAM,wBAAwB,qBAAqB,WAAW;AAC9D,cAAM,WAAW,MAAM,KAAK,mBAAmB;AAAA,UAC3C,QAAQ;AAAA,UACR,QAAQ;AAAA,YACJ,aAAa,YAAAC,QAAK,OAAO,qBAAqB;AAAA,UAClD;AAAA,QACJ,CAAC;AACD,cAAM,EAAE,aAAa,kBAAkB,IAAI;AAC3C,eAAO,4BAA4B,WAAW,IAAI,YAAY,KAAK,YAAAA,QAAK,OAAO,iBAAiB,CAAC,IAAI,qBAAqB,YAAY,YAAAA,QAAK,OAAO,iBAAiB,CAAC;AAAA,MACxK,SACO,GAAG;AACN,cAAM,IAAI,QAAQ,KAAK,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,CAAC,MAAM,4BAA4B;AAAA,MAC7J;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB,cAAc;AAC9B,QAAI;AACJ,WAAOD,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,WAAW;AACpC,cAAM,IAAI,MAAM,sBAAsB;AAAA,MAC1C;AACA,UAAI;AACA,cAAM,yBAAyB,aAAa,IAAI,CAAC,gBAAgB,qBAAqB,WAAW,CAAC;AAClG,cAAM,EAAE,cAAc,mBAAmB,IAAI,MAAM,KAAK,mBAAmB;AAAA,UACvE,QAAQ;AAAA,UACR,QAAQ;AAAA,YACJ,cAAc,uBAAuB,IAAI,CAAC,gBAAgB,YAAAC,QAAK,OAAO,WAAW,CAAC;AAAA,UACtF;AAAA,QACJ,CAAC;AACD,eAAO,mBAAmB,IAAI,CAAC,mBAAmB,UAAU;AACxD,iBAAO,4BAA4B,aAAa,KAAK,CAAC,IAAI,YAAY,KAAK,YAAAA,QAAK,OAAO,iBAAiB,CAAC,IAAI,qBAAqB,YAAY,YAAAA,QAAK,OAAO,iBAAiB,CAAC;AAAA,QAChL,CAAC;AAAA,MACL,SACO,GAAG;AACN,cAAM,IAAI,QAAQ,KAAK,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,CAAC,MAAM,6BAA6B;AAAA,MAC9J;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,uBAAuB,aAAa,SAAS;AACzC,QAAI;AACJ,WAAOD,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,WAAW;AACpC,cAAM,IAAI,MAAM,sBAAsB;AAAA,MAC1C;AACA,UAAI;AACA,cAAM,wBAAwB,qBAAqB,WAAW;AAC9D,cAAM,EAAE,UAAU,IAAI,MAAM,KAAK,mBAAmB;AAAA,UAChD,QAAQ;AAAA,UACR,QAAQ;AAAA,YACJ,aAAa,YAAAC,QAAK,OAAO,qBAAqB;AAAA,YAC9C;AAAA,UACJ;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX,SACO,GAAG;AACN,cAAM,IAAI,QAAQ,KAAK,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,CAAC,MAAM,qCAAqC;AAAA,MACtK;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,YAAY,MAAM,UAAU,QAAQ;AAChC,QAAI;AACJ,WAAOD,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,WAAW;AACpC,cAAM,IAAI,MAAM,sBAAsB;AAAA,MAC1C;AACA,UAAI;AACA,cAAM,EAAE,UAAU,IAAI,MAAM,KAAK,mBAAmB;AAAA,UAChD,QAAQ;AAAA,UACR,QAAQ;AAAA,YACJ,MAAM,YAAAC,QAAK,OAAO,IAAI;AAAA,YACtB;AAAA,UACJ;AAAA,QACJ,CAAC;AACD,eAAO,WAAW,KAAK,YAAAA,QAAK,OAAO,SAAS,CAAC;AAAA,MACjD,SACO,GAAG;AACN,cAAM,IAAI,QAAQ,KAAK,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,CAAC,MAAM,wBAAwB;AAAA,MACzJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,KAAK,MAAM,UAAU,QAAQ;AACzB,WAAOD,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,aAAO,MAAM,KAAK,YAAY,MAAM,OAAO;AAAA,IAC/C,CAAC;AAAA,EACL;AAAA,EACA,OAAO,cAAc;AACjB,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,YAAM,WAAW,MAAM,eAAe;AACtC,aAAO,CAAC,CAAC;AAAA,IACb,CAAC;AAAA,EACL;AAAA,EACA,kCAAkC,QAAQ;AACtC,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAI,CAAC,KAAK;AACN,cAAM,IAAI,MAAM,eAAe;AACnC,YAAM,UAAU,CAAC;AACjB,UAAI,OAAO,WAAW,GAAG;AACrB,cAAM,EAAE,aAAa,SAAS,OAAO,QAAQ,IAAI,OAAO,CAAC;AACzD,cAAM,EAAE,gBAAgB,qBAAqB,eAAe,WAAW,IAAI,WAAW,CAAC;AACvF,YAAI,YAAY,KAAK;AACjB,gBAAM,IAAI,MAAM,iBAAiB;AACrC,YAAI,CAAC,cAAc,KAAK;AACpB,gBAAM,IAAI,MAAM,eAAe;AACnC,cAAM,YAAY,MAAM,KAAK,uBAAuB,qBAAqB,YAAY,WAAW,GAAG;AAAA,UAC/F;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AACD,gBAAQ,KAAK,EAAE,WAAW,YAAAC,QAAK,OAAO,SAAS,EAAE,CAAC;AAAA,MACtD,WACS,OAAO,SAAS,GAAG;AACxB,mBAAW,SAAS,QAAQ;AACxB,kBAAQ,KAAK,GAAI,MAAM,KAAK,+BAA+B,KAAK,CAAE;AAAA,QACtE;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA,EACA,2BAA2B,QAAQ;AAC/B,WAAOD,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAI,CAAC,KAAK;AACN,cAAM,IAAI,MAAM,eAAe;AACnC,YAAM,UAAU,CAAC;AACjB,UAAI,OAAO,WAAW,GAAG;AACrB,cAAM,EAAE,aAAa,SAAS,MAAM,IAAI,OAAO,CAAC;AAChD,YAAI,YAAY,KAAK;AACjB,gBAAM,IAAI,MAAM,iBAAiB;AACrC,YAAI,SAAS,CAAC,cAAc,KAAK;AAC7B,gBAAM,IAAI,MAAM,eAAe;AACnC,cAAM,oBAAoB,MAAM,KAAK,gBAAgB,qBAAqB,YAAY,WAAW,CAAC;AAClG,gBAAQ,KAAK,EAAE,mBAAmB,kBAAkB,UAAU,EAAE,CAAC;AAAA,MACrE,WACS,OAAO,SAAS,GAAG;AACxB,YAAI;AACJ,mBAAW,SAAS,QAAQ;AACxB,cAAI,MAAM,YAAY,KAAK;AACvB,kBAAM,IAAI,MAAM,iBAAiB;AACrC,cAAI,MAAM,OAAO;AACb,gBAAI,CAAC,cAAc,MAAM,KAAK;AAC1B,oBAAM,IAAI,MAAM,eAAe;AACnC,gBAAI,OAAO;AACP,kBAAI,MAAM,UAAU;AAChB,sBAAM,IAAI,MAAM,mBAAmB;AAAA,YAC3C,OACK;AACD,sBAAQ,MAAM;AAAA,YAClB;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,eAAe,OAAO,IAAI,CAAC,EAAE,YAAY,MAAM,qBAAqB,YAAY,WAAW,CAAC;AAClG,cAAM,qBAAqB,MAAM,KAAK,oBAAoB,YAAY;AACtE,gBAAQ,KAAK,GAAG,mBAAmB,IAAI,CAAC,uBAAuB;AAAA,UAC3D,mBAAmB,kBAAkB,UAAU;AAAA,QACnD,EAAE,CAAC;AAAA,MACP;AACA,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA,EACA,uBAAuB,QAAQ;AAC3B,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAI,CAAC,KAAK;AACN,cAAM,IAAI,MAAM,eAAe;AACnC,YAAM,UAAU,CAAC;AACjB,UAAI,OAAO,WAAW,GAAG;AACrB,cAAM,EAAE,SAAS,QAAQ,IAAI,OAAO,CAAC;AACrC,YAAI,YAAY,KAAK;AACjB,gBAAM,IAAI,MAAM,iBAAiB;AACrC,cAAM,YAAY,MAAM,KAAK,YAAY,OAAO;AAChD,gBAAQ,KAAK,EAAE,eAAe,SAAS,UAAU,CAAC;AAAA,MACtD,WACS,OAAO,SAAS,GAAG;AACxB,mBAAW,SAAS,QAAQ;AACxB,kBAAQ,KAAK,GAAI,MAAM,KAAK,oBAAoB,KAAK,CAAE;AAAA,QAC3D;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AACJ;AACA,iBAAiB,aAAa;AAC9B,IAAO,cAAQ;", "names": ["EventEmitter", "i", "j", "EventEmitter", "provider", "f", "__awaiter", "bs58"]}