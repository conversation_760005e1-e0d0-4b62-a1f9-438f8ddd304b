{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../../src/sign-client/client.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,YAAY,MAAM,QAAQ,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,cAAc,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,UAAU,CAAC;AACnC,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AACtD,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,WAAW,CAAC;AACnD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AAC1C,OAAO,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAEvC,MAAM,CAAC,OAAO,WAAW,eAAe,CAAC;IACvC,KAAK,KAAK,GACN,kBAAkB,GAClB,gBAAgB,GAChB,gBAAgB,GAChB,cAAc,GACd,gBAAgB,GAChB,gBAAgB,GAChB,iBAAiB,GACjB,sBAAsB,GACtB,eAAe,GACf,sBAAsB,GACtB,iBAAiB,GACjB,wBAAwB,GACxB,iBAAiB,CAAC;IAEtB,UAAU,aAAa,CAAC,CAAC,GAAG,OAAO;QACjC,EAAE,EAAE,MAAM,CAAC;QACX,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,CAAC,CAAC;KACX;IACD,UAAU,cAAc;QACtB,gBAAgB,EAAE;YAChB,aAAa,EAAE,MAAM,CAAC,OAAO,CAAC;SAC/B,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;QACvD,cAAc,EAAE,aAAa,CAAC;YAAE,UAAU,EAAE,YAAY,CAAC,UAAU,CAAA;SAAE,CAAC,CAAC;QACvE,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAC9C,YAAY,EAAE,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAC5C,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAC9C,cAAc,EAAE;YAAE,KAAK,EAAE,MAAM,CAAA;SAAE,CAAC;QAClC,eAAe,EAAE;YACf,aAAa,EAAE,MAAM,CAAC,OAAO,CAAC;SAC/B,GAAG,aAAa,CAAC;YAChB,OAAO,EAAE;gBAAE,MAAM,EAAE,MAAM,CAAC;gBAAC,MAAM,EAAE,GAAG,CAAC;gBAAC,eAAe,CAAC,EAAE,MAAM,CAAA;aAAE,CAAC;YACnE,OAAO,EAAE,MAAM,CAAC;SACjB,CAAC,CAAC;QACH,oBAAoB,EAAE;YACpB,OAAO,EAAE;gBAAE,MAAM,EAAE,MAAM,CAAC;gBAAC,MAAM,EAAE,GAAG,CAAA;aAAE,CAAC;YACzC,KAAK,EAAE,MAAM,CAAC;YACd,OAAO,EAAE,MAAM,CAAC;YAChB,EAAE,EAAE,MAAM,CAAC;SACZ,CAAC;QACF,aAAa,EAAE,aAAa,CAAC;YAC3B,KAAK,EAAE;gBAAE,IAAI,EAAE,MAAM,CAAC;gBAAC,IAAI,EAAE,GAAG,CAAA;aAAE,CAAC;YACnC,OAAO,EAAE,MAAM,CAAC;SACjB,CAAC,CAAC;QACH,oBAAoB,EAAE;YACpB,aAAa,EAAE,MAAM,CAAC,OAAO,CAAC;YAC9B,aAAa,CAAC,EAAE,YAAY,CAAC,aAAa,CAAC;SAC5C,GAAG,aAAa,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QAClD,eAAe,EAAE;YAAE,EAAE,EAAE,MAAM,CAAA;SAAE,CAAC;QAChC,sBAAsB,EAAE;YAAE,EAAE,EAAE,MAAM,CAAA;SAAE,CAAC;QACvC,eAAe,EAAE;YAAE,OAAO,EAAE,YAAY,CAAC,MAAM,CAAA;SAAE,CAAC;KACnD;IAED,KAAK,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;IAEnC,KAAK,UAAU,GAAG;QAChB,mBAAmB,CAAC,EAAE,OAAO,CAAC;KAC/B,CAAC;IAEF,UAAU,OAAQ,SAAQ,SAAS,CAAC,OAAO;QACzC,IAAI,CAAC,EAAE,KAAK,CAAC;QACb,QAAQ,CAAC,EAAE,QAAQ,CAAC;QACpB,UAAU,CAAC,EAAE,UAAU,CAAC;KACzB;CACF;AAED,8BAAsB,iBAAkB,SAAQ,YAAY;;IAK1D,SAAgB,IAAI,EAAE,CAAC,CAAC,SAAS,eAAe,CAAC,KAAK,EACpD,KAAK,EAAE,CAAC,EACR,IAAI,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,KACpC,OAAO,CAAC;IAEb,SAAgB,EAAE,EAAE,CAAC,CAAC,SAAS,eAAe,CAAC,KAAK,EAClD,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,GAAG,KACvD,IAAI,CAAC;IAEV,SAAgB,IAAI,EAAE,CAAC,CAAC,SAAS,eAAe,CAAC,KAAK,EACpD,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,GAAG,KACvD,IAAI,CAAC;IAEV,SAAgB,GAAG,EAAE,CAAC,CAAC,SAAS,eAAe,CAAC,KAAK,EACnD,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,GAAG,KACvD,IAAI,CAAC;IAEV,SAAgB,cAAc,EAAE,CAAC,CAAC,SAAS,eAAe,CAAC,KAAK,EAC9D,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,GAAG,KACvD,IAAI,CAAC;IAEV,SAAgB,kBAAkB,EAAE,CAAC,CAAC,SAAS,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC;CACzF;AAED,8BAAsB,WAAW;IAkBZ,IAAI,CAAC;IAjBxB,SAAgB,QAAQ,QAAQ;IAChC,SAAgB,OAAO,KAAK;IAE5B,kBAAyB,IAAI,EAAE,MAAM,CAAC;IACtC,kBAAyB,OAAO,EAAE,MAAM,CAAC;IACzC,kBAAyB,QAAQ,EAAE,eAAe,CAAC,QAAQ,CAAC;IAE5D,SAAgB,IAAI,EAAE,KAAK,CAAC;IAC5B,SAAgB,MAAM,EAAE,MAAM,CAAC;IAC/B,SAAgB,MAAM,EAAE,iBAAiB,CAAC;IAC1C,SAAgB,MAAM,EAAE,OAAO,CAAC;IAChC,SAAgB,OAAO,EAAE,QAAQ,CAAC;IAClC,SAAgB,QAAQ,EAAE,SAAS,CAAC;IACpC,SAAgB,cAAc,EAAE,eAAe,CAAC;IAChD,SAAgB,IAAI,EAAE,KAAK,CAAC;IAC5B,SAAgB,UAAU,CAAC,EAAE,eAAe,CAAC,UAAU,CAAC;gBAErC,IAAI,CAAC,qCAAyB;IAEjD,SAAgB,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC5C,SAAgB,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IACtC,SAAgB,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC5C,SAAgB,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC1C,SAAgB,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC1C,SAAgB,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC1C,SAAgB,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC5C,SAAgB,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC5C,SAAgB,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IACtC,SAAgB,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IACtC,SAAgB,UAAU,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;IAClD,SAAgB,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IACtC,SAAgB,yBAAyB,EAAE,OAAO,CAAC,2BAA2B,CAAC,CAAC;IAChF,SAAgB,YAAY,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IACtD,SAAgB,iBAAiB,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAChE,SAAgB,0BAA0B,EAAE,OAAO,CAAC,4BAA4B,CAAC,CAAC;IAClF,SAAgB,yBAAyB,EAAE,OAAO,CAAC,2BAA2B,CAAC,CAAC;CACjF"}