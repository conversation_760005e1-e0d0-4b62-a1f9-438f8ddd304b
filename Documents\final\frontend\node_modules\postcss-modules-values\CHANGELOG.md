# Change Log

All notable changes to this project will be documented in this file.
This project adheres to [Semantic Versioning](http://semver.org/).

## [4.0.0](https://github.com/postcss-modules-local-by-default/compare/v4.0.0-rc.5...v4.0.0) - 2020-13-08

### Fixes

- compatibility with other plugins

## [4.0.0-rc.5](https://github.com/postcss-modules-local-by-default/compare/v4.0.0-rc.4...v4.0.0-rc.5) - 2020-11-08

### Fixes

- compatibility with other plugins

## [4.0.0-rc.4](https://github.com/postcss-modules-local-by-default/compare/v4.0.0-rc.3...v4.0.0-rc.4) - 2020-10-08

### Fixes

- perf
- compatibility with empty custom properties
- works with `options.createImportedName`

## [4.0.0-rc.3](https://github.com/postcss-modules-local-by-default/compare/v4.0.0-rc.2...v4.0.0-rc.3) - 2020-10-08

### BREAKING CHANGE

- minimum supported `postcss` version is `^8.1.0`

### Fixes

- minimum supported `Node.js` version is `^10 || ^12 || >= 14`
- compatibility with PostCSS 8

## [4.0.0-rc.2](https://github.com/postcss-modules-local-by-default/compare/v4.0.0-rc.1...v4.0.0-rc.2) - 2020-09-22

### Fixes

- avoid using `postcss` directly to create decls and rules

## [4.0.0-rc.1](https://github.com/postcss-modules-local-by-default/compare/v4.0.0-rc.0...v4.0.0-rc.1) - 2020-09-22

### BREAKING CHANGE

- update `icss-utils` for PostCSS 8 compatibility

## [4.0.0-rc.0](https://github.com/postcss-modules-local-by-default/compare/v3.0.0...v4.0.0-rc.1) - 2020-09-18

### BREAKING CHANGE

- minimum supported `Node.js` version is `>= 10.13.0 || >= 12.13.0 || >= 14`
- minimum supported `postcss` version is `^8.0.3`
- `postcss` was moved to `peerDependencies`, you need to install `postcss` in your project before use the plugin
