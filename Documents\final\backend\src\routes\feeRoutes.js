const express = require('express');
const router = express.Router();
const feeController = require('../controllers/feeController');

/**
 * @route GET /api/fees
 * @desc Get fee structure
 * @access Public
 */
router.get('/', feeController.getFeeStructure);

/**
 * @route POST /api/fees/calculate-trading
 * @desc Calculate trading fee
 * @access Public
 */
router.post('/calculate-trading', feeController.calculateTradingFee);

/**
 * @route GET /api/fees/calculate-token-creation
 * @desc Calculate token creation fee
 * @access Public
 */
router.get('/calculate-token-creation', feeController.calculateTokenCreationFee);

/**
 * @route GET /api/fees/token-creation
 * @desc Calculate token creation fee (alias for backward compatibility)
 * @access Public
 */
router.get('/token-creation', feeController.calculateTokenCreationFee);

/**
 * @route POST /api/fees/raydium
 * @desc Calculate Raydium fees
 * @access Public
 */
router.post('/raydium', feeController.calculateRaydiumFees);

module.exports = router;
