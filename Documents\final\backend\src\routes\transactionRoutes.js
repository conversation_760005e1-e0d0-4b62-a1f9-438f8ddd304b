/**
 * Transaction Routes
 */
const express = require('express');
const router = express.Router();
const transactionController = require('../controllers/transactionController');
const { authenticateToken } = require('../middleware/authMiddleware');
const { validateRequest } = require('../middleware/validationMiddleware');

// Get all transactions for the authenticated user
router.get(
  '/',
  authenticateToken,
  transactionController.getUserTransactions
);

// Get transaction statistics for the authenticated user
router.get(
  '/stats',
  authenticateToken,
  transactionController.getTransactionStats
);

// Get a specific transaction
router.get(
  '/:id',
  authenticateToken,
  transactionController.getTransaction
);

// Create a new transaction
router.post(
  '/',
  authenticateToken,
  validateRequest({
    body: {
      type: { 
        type: 'string', 
        required: true,
        enum: ['buy', 'sell', 'swap', 'deposit', 'withdrawal']
      },
      asset: { type: 'string', required: true },
      amount: { type: 'number', required: true },
      value: { type: 'number', required: true },
      fee: { type: 'number', required: false },
      status: { 
        type: 'string', 
        required: false,
        enum: ['pending', 'completed', 'failed', 'cancelled']
      }
    }
  }),
  transactionController.createTransaction
);

// Update a transaction's status
router.patch(
  '/:id/status',
  authenticateToken,
  validateRequest({
    body: {
      status: { 
        type: 'string', 
        required: true,
        enum: ['pending', 'completed', 'failed', 'cancelled']
      }
    }
  }),
  transactionController.updateTransactionStatus
);

module.exports = router;
