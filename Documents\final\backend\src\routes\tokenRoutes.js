const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { Token, TOKEN_PROGRAM_ID } = require('@solana/spl-token');
const TokenModel = require('../models/Token');
const raydiumService = require('../services/raydiumService');
const { auth } = require('../middleware/auth');
const multer = require('multer');
const { gridFsStorage } = require('../config/gridfs');
const mongoose = require('mongoose');
// const balanceService = require('../services/balanceService'); // REMOVED - Balance system removed
const logger = require('../utils/logger');

// Initialize GridFS storage for token images
const tokenImageUpload = multer({
  storage: gridFsStorage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB max file size
  },
  fileFilter: (req, file, cb) => {
    // Accept only image files
    if (!file.originalname.match(/\.(jpg|jpeg|png|gif|webp)$/)) {
      return cb(new Error('Only image files are allowed!'), false);
    }
    cb(null, true);
  }
});

// Validation middleware
const createTokenValidation = [
  body('name').trim().notEmpty().withMessage('Name is required'),
  body('symbol').trim().notEmpty().withMessage('Symbol is required'),
  body('totalSupply').isNumeric().withMessage('Total supply must be a number'),
  body('decimals').optional().isInt({ min: 0, max: 9 }).withMessage('Decimals must be between 0 and 9'),
  body('description').optional().trim(),
  body('website').optional().isURL().withMessage('Invalid website URL'),
  body('social.twitter').optional().isURL().withMessage('Invalid Twitter URL'),
  body('social.telegram').optional().isURL().withMessage('Invalid Telegram URL'),
  body('social.discord').optional().isURL().withMessage('Invalid Discord URL'),
  body('logo').optional().isURL().withMessage('Invalid logo URL')
];

// Upload token image route
router.post('/upload-image', tokenImageUpload.single('tokenImage'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No image file provided' });
    }

    // Get file information from GridFS
    const fileId = req.file.id;
    const filename = req.file.filename;

    // Generate a URL for retrieving the image
    const imageUrl = `/api/images/${filename}`;

    res.json({
      success: true,
      message: 'Token image uploaded successfully',
      imageUrl,
      filename,
      fileId
    });
  } catch (error) {
    console.error('Error uploading token image:', error);
    res.status(500).json({ message: 'Server error during image upload' });
  }
});

// Get trending tokens
router.get('/trending', async (req, res) => {
  try {
    // Get trending tokens from database or use mock data
    const trendingTokens = [
      {
        id: 101,
        name: 'MemeRocket',
        symbol: 'MRKT',
        price: 0.12,
        change: '+25%',
        volume: 120000,
        holders: 5000,
        marketCap: 1000000,
        description: 'A trending meme coin!',
        volume_24h: 120000,
        image: 'https://via.placeholder.com/40'
      },
      {
        id: 102,
        name: 'SolanaPepe',
        symbol: 'SPEPE',
        price: 0.05,
        change: '+18%',
        volume: 95000,
        holders: 3200,
        marketCap: 800000,
        description: 'Pepe on Solana!',
        volume_24h: 95000,
        image: 'https://via.placeholder.com/40'
      }
    ];

    res.json(trendingTokens);
  } catch (error) {
    console.error('Error fetching trending tokens:', error);
    res.status(500).json({ message: 'Failed to fetch trending tokens' });
  }
});

// Get explore tokens (new tokens)
router.get('/explore', async (req, res) => {
  try {
    // Get new/explore tokens from database or use mock data
    const exploreTokens = [
      {
        id: 201,
        name: 'FreshMeme',
        symbol: 'FMEME',
        price: 0.01,
        change: '+2%',
        volume: 10000,
        holders: 100,
        marketCap: 20000,
        description: 'A new meme coin!',
        created_at: new Date().toISOString(),
        image: 'https://via.placeholder.com/40',
        creator: 'User'
      },
      {
        id: 202,
        name: 'SolanaDog',
        symbol: 'SDOG',
        price: 0.03,
        change: '+5%',
        volume: 20000,
        holders: 250,
        marketCap: 60000,
        description: 'Dog coin on Solana!',
        created_at: new Date().toISOString(),
        image: 'https://via.placeholder.com/40',
        creator: 'User'
      }
    ];

    res.json(exploreTokens);
  } catch (error) {
    console.error('Error fetching explore tokens:', error);
    res.status(500).json({ message: 'Failed to fetch explore tokens' });
  }
});

// Get individual token by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Mock token data - in real implementation, fetch from database
    const token = {
      id: parseInt(id),
      name: 'Sample Token',
      symbol: 'SAMPLE',
      price: 0.05,
      change: '+10%',
      volume: 50000,
      holders: 1500,
      marketCap: 500000,
      description: 'A sample token for trading',
      volume_24h: 50000,
      image: 'https://via.placeholder.com/40',
      creator: 'TokenCreator',
      created_at: new Date().toISOString()
    };

    res.json(token);
  } catch (error) {
    console.error('Error fetching token:', error);
    res.status(500).json({ message: 'Failed to fetch token' });
  }
});

// Simple health check for token routes
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Token routes are working',
    endpoints: ['/api/tokens', '/api/tokens/trending', '/api/tokens/explore']
  });
});

// Balance check removed - users will pay directly from their wallets
// router.get('/check-balance', auth, async (req, res) => {
//   try {
//     // Verify user is authenticated
//     if (!req.user || !req.user.id) {
//       return res.status(401).json({
//         success: false,
//         message: 'Authentication required. Please log in to check your balance.'
//       });
//     }

//     const userId = req.user.id;
//     const tokenCreationFee = 0.1; // 0.1 SOL fee for creating a token
//     const networkFee = 0.000005; // Solana network fee
//     const totalFee = tokenCreationFee + networkFee;

//     // Get user balance
//     try {
//       const userBalance = await balanceService.getUserBalance(userId);
//       const solBalance = parseFloat(userBalance.solBalance) || 0;

//       // Check if user has enough balance
//       const hasEnoughBalance = solBalance >= totalFee;

//       return res.status(200).json({
//         success: true,
//         data: {
//           userBalance: solBalance,
//           requiredFee: totalFee,
//           hasEnoughBalance,
//           tokenCreationFee,
//           networkFee
//         }
//       });
//     } catch (balanceError) {
//       logger.error(`Error fetching user balance: ${balanceError.message}`);
//       return res.status(400).json({
//         success: false,
//         message: `Unable to retrieve your balance: ${balanceError.message}`
//       });
//     }
//   } catch (error) {
//     logger.error(`Error checking balance for token creation: ${error.message}`);
//     return res.status(500).json({
//       success: false,
//       message: 'Failed to check balance. Please try again later.',
//       error: error.message
//     });
//   }
// });

// Create new token
router.post('/create', auth, createTokenValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      name,
      symbol,
      totalSupply,
      decimals = 9,
      description,
      website,
      social,
      logo,
      imageUrl,
      mint,
      owner
    } = req.body;

    // Check if token symbol already exists
    const existingToken = await TokenModel.findOne({ symbol: symbol.toUpperCase() });
    if (existingToken) {
      return res.status(400).json({ message: 'Token symbol already exists' });
    }

    // Define token creation fee
    const tokenCreationFee = 0.1; // 0.1 SOL fee for creating a token

    // Balance check removed - users will pay directly from their wallets
    // try {
    //   const userId = req.user.id;
    //   const userBalance = await balanceService.getUserBalance(userId);

    //   if (parseFloat(userBalance.solBalance) < tokenCreationFee) {
    //     return res.status(400).json({
    //       success: false,
    //       message: `Insufficient balance. Token creation requires ${tokenCreationFee} SOL.`
    //     });
    //   }

    //   // Deduct fee from user's balance
    //   await balanceService.updateSolBalance(userId, -tokenCreationFee, {
    //     type: 'fee',
    //     description: `Fee for creating token ${symbol.toUpperCase()}`
    //   });

    //   logger.info(`Deducted ${tokenCreationFee} SOL from user ${userId} for token creation`);

    const userId = req.user.id;
    logger.info(`Token creation initiated by user ${userId} - payment will be handled directly from wallet`);
    // } catch (balanceError) {
    //   logger.error(`Error processing token creation fee: ${balanceError.message}`);
    //   return res.status(500).json({
    //     success: false,
    //     message: 'Error processing token creation fee',
    //     error: balanceError.message
    //   });
    // }

    // Create new token in database
    const newToken = new TokenModel({
      name,
      symbol: symbol.toUpperCase(),
      totalSupply,
      decimals,
      mintAddress: mint || 'placeholder-for-development',
      creator: req.user ? req.user.id : 'development-user',
      description,
      website,
      social,
      logo: imageUrl || logo, // Use the uploaded image URL
      isVerified: false,
      status: 'ACTIVE'
    });

    await newToken.save();

    res.status(201).json({
      success: true,
      token: newToken,
      fee: tokenCreationFee
    });
  } catch (error) {
    console.error('Error creating token:', error);
    res.status(500).json({ message: 'Server error during token creation' });
  }
});

module.exports = router;