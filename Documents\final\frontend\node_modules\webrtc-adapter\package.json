{"name": "webrtc-adapter", "version": "7.7.1", "description": "A shim to insulate apps from WebRTC spec changes and browser prefix differences", "license": "BSD-3-<PERSON><PERSON>", "main": "./dist/adapter_core.js", "types": "./index.d.ts", "module": "./src/js/adapter_core.js", "repository": {"type": "git", "url": "https://github.com/webrtchacks/adapter.git"}, "authors": ["The WebRTC project authors (https://www.webrtc.org/)", "The adapter.js project authors (https://github.com/webrtchacks/adapter/)"], "scripts": {"preversion": "git stash && npm install && npm update && BROWSER=chrome BVER=stable CI=true npm test && git checkout -B bumpVersion && grunt build && grunt copyForPublish && git add package.json release/* && git commit -m 'Add adapter artifacts' --allow-empty", "version": "", "postversion": "export GITTAG=\"echo $(git describe --abbrev=0 --tags | sed 's/^v//')\" && git push --force --set-upstream origin bumpVersion --follow-tags && git checkout gh-pages && git pull && cp out/adapter.js adapter.js && cp adapter.js adapter-`$GITTAG`.js && rm adapter-latest.js && ln -s adapter-`$GITTAG`.js adapter-latest.js && mkdir -p adapter-`$GITTAG`-variants && cp out/adapter.js adapter-`$GITTAG`-variants/ && cp out/adapter_*.js adapter-`$GITTAG`-variants/ && git add adapter.js adapter-latest.js adapter-`$GITTAG`.js adapter-`$GITTAG`-variants && git commit -m `$GITTAG` && git push --set-upstream origin gh-pages && git checkout master", "prepare": "grunt build", "prepublishonly": "npm test", "test": "grunt && grunt downloadBrowser && mocha test/unit && karma start test/karma.conf.js", "lint-and-unit-tests": "grunt && mocha test/unit", "e2e-tests": "grunt && grunt downloadBrowser && karma start test/karma.conf.js"}, "dependencies": {"rtcpeerconnection-shim": "^1.2.15", "sdp": "^2.12.0"}, "engines": {"npm": ">=3.10.0", "node": ">=6.0.0"}, "devDependencies": {"babel-core": "^6.26.3", "babel-preset-env": "^1.7.0", "brfs": "^1.5.0", "chai": "^3.5.0", "grunt": "^1.1.0", "grunt-babel": "^7.0.0", "grunt-browserify": "^5.3.0", "grunt-cli": "^1.3.1", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-copy": "^1.0.0", "grunt-eslint": "^19.0.0", "grunt-shell": "^2.1.0", "karma": "^4.4.1", "karma-browserify": "^5.2.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-edge-launcher": "^0.4.1", "karma-firefox-launcher": "^1.3.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.3", "karma-safari-launcher": "^1.0.0", "karma-stability-reporter": "^3.0.1", "mocha": "^5.2.0", "sinon": "^2.2.0", "sinon-chai": "^2.14.0", "travis-multirunner": "^4.6.0"}}