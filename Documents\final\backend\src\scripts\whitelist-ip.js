/**
 * This script helps whitelist your current IP address in MongoDB Atlas
 * 
 * To use this script:
 * 1. Go to MongoDB Atlas: https://cloud.mongodb.com/
 * 2. Log in with your credentials
 * 3. Go to Network Access under Security
 * 4. Click "Add IP Address"
 * 5. Click "Add Current IP Address"
 * 6. Click "Confirm"
 * 
 * Alternatively, you can add 0.0.0.0/0 to allow access from anywhere (not recommended for production)
 */

const https = require('https');

// Function to get the current public IP address
function getCurrentIP() {
  return new Promise((resolve, reject) => {
    https.get('https://api.ipify.org', (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve(data.trim());
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// Main function
async function main() {
  try {
    const ip = await getCurrentIP();
    console.log(`Your current public IP address is: ${ip}`);
    console.log('\nTo whitelist this IP in MongoDB Atlas:');
    console.log('1. Go to MongoDB Atlas: https://cloud.mongodb.com/');
    console.log('2. Log in with your credentials');
    console.log('3. Go to Network Access under Security');
    console.log('4. Click "Add IP Address"');
    console.log('5. Enter your IP address: ' + ip);
    console.log('6. Click "Confirm"');
    console.log('\nAlternatively, for development purposes only, you can allow access from anywhere:');
    console.log('1. Follow steps 1-4 above');
    console.log('2. Enter 0.0.0.0/0 as the IP address');
    console.log('3. Click "Confirm"');
    console.log('\nWARNING: Allowing access from anywhere (0.0.0.0/0) is not recommended for production environments.');
  } catch (error) {
    console.error('Error getting your IP address:', error.message);
  }
}

// Run the main function
main();
