/**
 * MongoDB to PostgreSQL Migration Script
 * 
 * This script migrates trading and transaction data from MongoDB to PostgreSQL.
 * It preserves user and token data in MongoDB.
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');
const postgresqlService = require('../services/postgresqlService');
const logger = require('../utils/logger');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/swap-platform';

// Connect to MongoDB
async function connectToMongoDB() {
  try {
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    logger.info('Connected to MongoDB');
    return true;
  } catch (error) {
    logger.error(`MongoDB connection error: ${error.message}`);
    return false;
  }
}

// Migrate transactions from MongoDB to PostgreSQL
async function migrateTransactions() {
  try {
    logger.info('Starting transaction migration...');
    
    // Get transactions from MongoDB
    const TransactionModel = mongoose.model('Transaction', new mongoose.Schema({}, { strict: false }));
    const transactions = await TransactionModel.find({}).lean();
    
    logger.info(`Found ${transactions.length} transactions in MongoDB`);
    
    // Migrate each transaction to PostgreSQL
    let migratedCount = 0;
    let errorCount = 0;
    
    for (const tx of transactions) {
      try {
        // Map MongoDB transaction to PostgreSQL format
        const pgTransaction = {
          id: uuidv4(),
          type: mapTransactionType(tx.type),
          walletAddress: tx.walletAddress || tx.user_wallet || '',
          tokenMint: tx.tokenMint || tx.asset || '',
          tokenAmount: tx.tokenAmount ? tx.tokenAmount.toString() : null,
          solAmount: tx.solAmount || tx.value ? tx.solAmount.toString() || tx.value.toString() : null,
          feeAmount: tx.feeAmount || tx.fee ? tx.feeAmount.toString() || tx.fee.toString() : null,
          signature: tx.signature || tx.txid || null,
          status: mapTransactionStatus(tx.status),
          blockTime: tx.blockTime || tx.timestamp || new Date(),
          metadata: {
            originalId: tx._id.toString(),
            userId: tx.userId || tx.user_id,
            createdAt: tx.createdAt || new Date()
          }
        };
        
        // Create transaction in PostgreSQL
        await postgresqlService.createTransaction(pgTransaction);
        migratedCount++;
        
        if (migratedCount % 100 === 0) {
          logger.info(`Migrated ${migratedCount} transactions...`);
        }
      } catch (error) {
        logger.error(`Error migrating transaction ${tx._id}: ${error.message}`);
        errorCount++;
      }
    }
    
    logger.info(`Transaction migration complete. Migrated: ${migratedCount}, Errors: ${errorCount}`);
    return { migratedCount, errorCount };
  } catch (error) {
    logger.error(`Transaction migration failed: ${error.message}`);
    throw error;
  }
}

// Migrate orders from MongoDB to PostgreSQL
async function migrateOrders() {
  try {
    logger.info('Starting order migration...');
    
    // Get orders from MongoDB
    const OrderModel = mongoose.model('Order', new mongoose.Schema({}, { strict: false }));
    const orders = await OrderModel.find({}).lean();
    
    logger.info(`Found ${orders.length} orders in MongoDB`);
    
    // Migrate each order to PostgreSQL
    let migratedCount = 0;
    let errorCount = 0;
    
    for (const order of orders) {
      try {
        // Map MongoDB order to PostgreSQL format
        const pgOrder = {
          id: uuidv4(),
          type: order.side ? order.side.toLowerCase() : 'buy',
          walletAddress: order.walletAddress || order.user_wallet || '',
          tokenMint: order.tokenMint || (order.symbol ? order.symbol.split('/')[0] : ''),
          tokenAmount: order.quantity ? order.quantity.toString() : '0',
          price: order.price ? order.price.toString() : '0',
          totalValue: order.price && order.quantity ? (order.price * order.quantity).toString() : '0',
          status: mapOrderStatus(order.status),
          filledAmount: order.filledAmount ? order.filledAmount.toString() : '0',
          remainingAmount: order.remainingAmount ? order.remainingAmount.toString() : 
            (order.quantity ? order.quantity.toString() : '0'),
          expiresAt: order.expiresAt || null,
          metadata: {
            originalId: order._id.toString(),
            userId: order.userId || order.user_id,
            orderType: order.type || 'limit',
            symbol: order.symbol || '',
            createdAt: order.createdAt || new Date()
          }
        };
        
        // Create order in PostgreSQL
        await postgresqlService.createOrder(pgOrder);
        migratedCount++;
        
        if (migratedCount % 100 === 0) {
          logger.info(`Migrated ${migratedCount} orders...`);
        }
      } catch (error) {
        logger.error(`Error migrating order ${order._id}: ${error.message}`);
        errorCount++;
      }
    }
    
    logger.info(`Order migration complete. Migrated: ${migratedCount}, Errors: ${errorCount}`);
    return { migratedCount, errorCount };
  } catch (error) {
    logger.error(`Order migration failed: ${error.message}`);
    throw error;
  }
}

// Migrate trades from MongoDB to PostgreSQL
async function migrateTrades() {
  try {
    logger.info('Starting trade migration...');
    
    // Get trades from MongoDB
    const TradeModel = mongoose.model('Trade', new mongoose.Schema({}, { strict: false }));
    const trades = await TradeModel.find({}).lean();
    
    logger.info(`Found ${trades.length} trades in MongoDB`);
    
    // Migrate each trade to PostgreSQL
    let migratedCount = 0;
    let errorCount = 0;
    
    for (const trade of trades) {
      try {
        // Map MongoDB trade to PostgreSQL format
        const pgTrade = {
          id: uuidv4(),
          buyOrderId: trade.buyOrderId || uuidv4(),
          sellOrderId: trade.sellOrderId || uuidv4(),
          tokenMint: trade.tokenMint || (trade.symbol ? trade.symbol.split('/')[0] : ''),
          buyerAddress: trade.buyerAddress || trade.buyer || '',
          sellerAddress: trade.sellerAddress || trade.seller || '',
          tokenAmount: trade.quantity ? trade.quantity.toString() : '0',
          price: trade.price ? trade.price.toString() : '0',
          totalValue: trade.price && trade.quantity ? (trade.price * trade.quantity).toString() : '0',
          feeAmount: trade.feeAmount ? trade.feeAmount.toString() : null,
          signature: trade.signature || trade.txid || null,
          status: trade.status || 'completed',
          settledAt: trade.settledAt || trade.timestamp || new Date(),
          metadata: {
            originalId: trade._id.toString(),
            symbol: trade.symbol || '',
            createdAt: trade.createdAt || new Date()
          }
        };
        
        // Create trade in PostgreSQL
        await postgresqlService.createTrade(pgTrade);
        migratedCount++;
        
        if (migratedCount % 100 === 0) {
          logger.info(`Migrated ${migratedCount} trades...`);
        }
      } catch (error) {
        logger.error(`Error migrating trade ${trade._id}: ${error.message}`);
        errorCount++;
      }
    }
    
    logger.info(`Trade migration complete. Migrated: ${migratedCount}, Errors: ${errorCount}`);
    return { migratedCount, errorCount };
  } catch (error) {
    logger.error(`Trade migration failed: ${error.message}`);
    throw error;
  }
}

// Helper function to map transaction types
function mapTransactionType(type) {
  const typeMap = {
    'buy': 'buy',
    'sell': 'sell',
    'swap': 'transfer',
    'deposit': 'transfer',
    'withdrawal': 'transfer',
    'fee': 'fee',
    'transfer': 'transfer'
  };
  
  return typeMap[type] || 'transfer';
}

// Helper function to map transaction status
function mapTransactionStatus(status) {
  const statusMap = {
    'completed': 'confirmed',
    'pending': 'pending',
    'failed': 'failed',
    'cancelled': 'failed'
  };
  
  return statusMap[status] || 'pending';
}

// Helper function to map order status
function mapOrderStatus(status) {
  const statusMap = {
    'open': 'open',
    'filled': 'filled',
    'partial': 'partial',
    'cancelled': 'cancelled',
    'expired': 'expired'
  };
  
  return statusMap[status] || 'open';
}

// Main migration function
async function runMigration() {
  try {
    logger.info('Starting MongoDB to PostgreSQL migration...');
    
    // Initialize PostgreSQL
    const pgInitialized = await postgresqlService.initialize();
    if (!pgInitialized) {
      throw new Error('Failed to initialize PostgreSQL');
    }
    
    // Connect to MongoDB
    const mongoConnected = await connectToMongoDB();
    if (!mongoConnected) {
      throw new Error('Failed to connect to MongoDB');
    }
    
    // Run migrations
    const transactionResults = await migrateTransactions();
    const orderResults = await migrateOrders();
    const tradeResults = await migrateTrades();
    
    // Log results
    logger.info('Migration complete!');
    logger.info(`Transactions: ${transactionResults.migratedCount} migrated, ${transactionResults.errorCount} errors`);
    logger.info(`Orders: ${orderResults.migratedCount} migrated, ${orderResults.errorCount} errors`);
    logger.info(`Trades: ${tradeResults.migratedCount} migrated, ${tradeResults.errorCount} errors`);
    
    // Close connections
    await mongoose.disconnect();
    logger.info('MongoDB connection closed');
    
    return {
      transactions: transactionResults,
      orders: orderResults,
      trades: tradeResults
    };
  } catch (error) {
    logger.error(`Migration failed: ${error.message}`);
    // Try to close MongoDB connection
    try {
      await mongoose.disconnect();
    } catch (e) {
      // Ignore
    }
    process.exit(1);
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  runMigration()
    .then(() => {
      logger.info('Migration script completed successfully');
      process.exit(0);
    })
    .catch(error => {
      logger.error(`Migration script failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = {
  runMigration,
  migrateTransactions,
  migrateOrders,
  migrateTrades
};
