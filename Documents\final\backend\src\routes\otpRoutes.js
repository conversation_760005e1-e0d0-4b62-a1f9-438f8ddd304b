const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const otpController = require('../controllers/otpController');
const { otpRequestLimiter } = require('../middleware/rateLimiter');

// Validation middleware
const sendEmailOTPValidation = [
  body('email').isEmail().withMessage('Please enter a valid email'),
  body('purpose')
    .isIn(['registration', 'login', 'passwordReset', 'emailChange', 'twoFactor'])
    .withMessage('Invalid purpose for OTP')
];

const sendSmsOTPValidation = [
  body('phoneNumber')
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^[0-9+\s()-]{8,15}$/)
    .withMessage('Please enter a valid phone number (8-15 digits)'),
  body('purpose')
    .isIn(['registration', 'login', 'passwordReset', 'phoneChange', 'twoFactor'])
    .withMessage('Invalid purpose for OTP')
];

const verifyOTPValidation = [
  body('identifier').notEmpty().withMessage('Email or phone number is required'),
  body('otp')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('OTP must be 6 digits'),
  body('purpose')
    .isIn(['registration', 'login', 'passwordReset', 'emailChange', 'phoneChange', 'twoFactor'])
    .withMessage('Invalid purpose for OTP')
];

const resendOTPValidation = [
  body('identifier').notEmpty().withMessage('Email or phone number is required'),
  body('purpose')
    .isIn(['registration', 'login', 'passwordReset', 'emailChange', 'phoneChange', 'twoFactor'])
    .withMessage('Invalid purpose for OTP'),
  body('method')
    .optional()
    .isIn(['email', 'sms'])
    .withMessage('Method must be either email or sms')
];

// Routes
router.post('/send-email', sendEmailOTPValidation, otpRequestLimiter, otpController.sendEmailOTP);
router.post('/send-sms', sendSmsOTPValidation, otpRequestLimiter, otpController.sendSmsOTP);
router.post('/verify', verifyOTPValidation, otpController.verifyOTP);
router.post('/resend', resendOTPValidation, otpRequestLimiter, otpController.resendOTP);

module.exports = router;
