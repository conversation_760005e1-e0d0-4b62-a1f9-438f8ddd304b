const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// PostgreSQL connection configuration
const postgresConfig = {
  host: process.env.POSTGRES_HOST,
  port: process.env.POSTGRES_PORT || 5432,
  user: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  database: process.env.POSTGRES_DB,
  ssl: process.env.POSTGRES_SSL === 'true' ? {
    rejectUnauthorized: false,
    // Optionally load CA certificate if available
    ca: process.env.POSTGRES_CA_CERT ? 
      fs.readFileSync(path.resolve(__dirname, process.env.POSTGRES_CA_CERT)) : undefined
  } : false,
  // Connection pool settings
  max: 20, // Maximum number of clients
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
};

// Create a new pool instance
const pgPool = new Pool(postgresConfig);

// Pool error handling
pgPool.on('error', (err) => {
  console.error('Unexpected error on idle PostgreSQL client', err);
  process.exit(-1);
});

// Test connection function
async function testPostgresConnection() {
  const client = await pgPool.connect();
  try {
    const result = await client.query('SELECT NOW()');
    console.log('PostgreSQL connection successful:', result.rows[0]);
    return true;
  } catch (error) {
    console.error('PostgreSQL connection error:', error);
    return false;
  } finally {
    client.release();
  }
}

// Initialize PostgreSQL connection
async function initializePostgres() {
  try {
    // Test connection
    const connected = await testPostgresConnection();
    
    if (connected) {
      // Verify that required tables exist
      await ensureTablesExist();
      console.log('PostgreSQL database initialized successfully');
    } else {
      console.error('Failed to initialize PostgreSQL connection');
    }
  } catch (error) {
    console.error('Error initializing PostgreSQL:', error);
    throw error;
  }
}

// Verify and create tables if they don't exist
async function ensureTablesExist() {
  const client = await pgPool.connect();
  try {
    await client.query('BEGIN');
    
    // Create users table with encryption for sensitive fields
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        uuid UUID UNIQUE NOT NULL DEFAULT gen_random_uuid(),
        username VARCHAR(100) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        phone_number VARCHAR(20) UNIQUE,
        wallet_address VARCHAR(255) UNIQUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP WITH TIME ZONE,
        is_verified BOOLEAN DEFAULT FALSE,
        verification_level INT DEFAULT 0,
        role VARCHAR(50) DEFAULT 'user',
        failed_login_attempts INT DEFAULT 0,
        account_locked_until TIMESTAMP WITH TIME ZONE
      );
    `);
    
    // Create KYC data table with encryption for PII
    await client.query(`
      CREATE TABLE IF NOT EXISTS kyc_data (
        id SERIAL PRIMARY KEY,
        user_id INT REFERENCES users(id) ON DELETE CASCADE,
        full_name VARCHAR(255),
        date_of_birth DATE,
        address_line1 VARCHAR(255),
        address_line2 VARCHAR(255),
        city VARCHAR(100),
        state VARCHAR(100),
        postal_code VARCHAR(20),
        country VARCHAR(100),
        id_type VARCHAR(50),
        id_number VARCHAR(100),
        id_expiry_date DATE,
        verification_status VARCHAR(50) DEFAULT 'pending',
        verification_date TIMESTAMP WITH TIME ZONE,
        verification_provider VARCHAR(100),
        reference_id VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    // Create account preferences table
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_preferences (
        id SERIAL PRIMARY KEY,
        user_id INT REFERENCES users(id) ON DELETE CASCADE,
        notification_email BOOLEAN DEFAULT TRUE,
        notification_sms BOOLEAN DEFAULT FALSE,
        notification_push BOOLEAN DEFAULT TRUE,
        ui_theme VARCHAR(50) DEFAULT 'light',
        language VARCHAR(10) DEFAULT 'en',
        timezone VARCHAR(100) DEFAULT 'UTC',
        trading_view_settings JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    // Create session tracking table
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id SERIAL PRIMARY KEY,
        user_id INT REFERENCES users(id) ON DELETE CASCADE,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        device_fingerprint VARCHAR(255),
        location JSONB,
        started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        is_active BOOLEAN DEFAULT TRUE
      );
    `);
    
    // Create function to update timestamp
    await client.query(`
      CREATE OR REPLACE FUNCTION update_modified_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    // Create triggers for updating timestamp
    await client.query(`
      DROP TRIGGER IF EXISTS update_users_timestamp ON users;
      CREATE TRIGGER update_users_timestamp
      BEFORE UPDATE ON users
      FOR EACH ROW
      EXECUTE FUNCTION update_modified_column();
    `);
    
    await client.query(`
      DROP TRIGGER IF EXISTS update_kyc_timestamp ON kyc_data;
      CREATE TRIGGER update_kyc_timestamp
      BEFORE UPDATE ON kyc_data
      FOR EACH ROW
      EXECUTE FUNCTION update_modified_column();
    `);
    
    await client.query(`
      DROP TRIGGER IF EXISTS update_preferences_timestamp ON user_preferences;
      CREATE TRIGGER update_preferences_timestamp
      BEFORE UPDATE ON user_preferences
      FOR EACH ROW
      EXECUTE FUNCTION update_modified_column();
    `);
    
    await client.query('COMMIT');
    console.log('PostgreSQL tables verified/created successfully');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error creating PostgreSQL tables:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Export the pool and functions
module.exports = {
  pgPool,
  initializePostgres,
  testPostgresConnection
}; 