/**
 * Trading Controller
 * Handles WebSocket messages for trading functionality
 */

const { orderMatchingService } = require('../services/orderMatchingServiceTCP');
const { verifyJWT } = require('../services/authService');
const postgresqlService = require('../services/postgresqlService');
const { v4: uuidv4 } = require('uuid');

// Handle WebSocket connection and message routing
const handleConnection = (ws, req) => {
  console.log('WebSocket client connected');

  // Store user data if authentication is provided later
  let userData = null;

  // Set up message handler
  ws.on('message', async (message) => {
    try {
      const data = JSON.parse(message);

      // Check for authentication in the message
      if (data.token && !userData) {
        try {
          userData = await verifyJWT(data.token);
          // Send authentication confirmation
          ws.send(JSON.stringify({
            type: 'auth',
            status: 'authenticated',
            userId: userData.id,
            walletAddress: userData.walletAddress
          }));
        } catch (error) {
          ws.send(JSON.stringify({
            type: 'auth',
            status: 'error',
            message: 'Invalid authentication token'
          }));
        }
      }

      // Route messages based on type
      switch (data.type) {
        case 'subscribe':
          handleSubscription(ws, data);
          break;

        case 'unsubscribe':
          handleUnsubscription(ws, data);
          break;

        case 'order':
          // Require authentication for order operations
          if (!userData) {
            return ws.send(JSON.stringify({
              type: 'error',
              code: 'UNAUTHORIZED',
              message: 'Authentication required for order operations'
            }));
          }
          await handleOrder(ws, data, userData);
          break;

        case 'cancelOrder':
          // Require authentication for order operations
          if (!userData) {
            return ws.send(JSON.stringify({
              type: 'error',
              code: 'UNAUTHORIZED',
              message: 'Authentication required for order operations'
            }));
          }
          await handleCancelOrder(ws, data, userData);
          break;

        case 'getOrderBook':
          await handleGetOrderBook(ws, data);
          break;

        case 'getMarketData':
          await handleGetMarketData(ws, data);
          break;

        case 'getOrderBookDepth':
          await handleGetOrderBookDepth(ws, data);
          break;

        case 'ping':
          handlePing(ws);
          break;

        default:
          ws.send(JSON.stringify({
            type: 'error',
            message: 'Unknown message type'
          }));
      }
    } catch (error) {
      console.error('WebSocket message error:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Failed to process message'
      }));
    }
  });

  // Handle disconnection
  ws.on('close', () => {
    console.log('WebSocket client disconnected');
    // Clean up any resources or subscriptions
  });

  // Send initial connection confirmation
  ws.send(JSON.stringify({
    type: 'connection',
    status: 'connected',
    timestamp: new Date().toISOString()
  }));
};

// Handle subscription requests
const handleSubscription = (ws, data) => {
  const { channel } = data;

  if (!channel) {
    return ws.send(JSON.stringify({
      type: 'error',
      message: 'Channel is required for subscription'
    }));
  }

  // Store the subscription in the WebSocket object
  if (!ws.subscriptions) {
    ws.subscriptions = new Set();
  }

  ws.subscriptions.add(channel);

  // Send confirmation
  ws.send(JSON.stringify({
    type: 'subscription',
    status: 'subscribed',
    channel,
    timestamp: new Date().toISOString()
  }));

  // If this is an orderbook subscription, send initial data
  if (channel.startsWith('orderbook:')) {
    const symbol = channel.split(':')[1];

    orderMatchingService.getOrderBook(symbol)
      .then(orderBook => {
        ws.send(JSON.stringify({
          type: 'orderbook',
          symbol,
          data: orderBook,
          timestamp: new Date().toISOString()
        }));
      })
      .catch(error => {
        console.error('Error fetching initial orderbook data:', error);
      });
  }

  // If this is a trades subscription, send recent trades
  if (channel.startsWith('trades:')) {
    const symbol = channel.split(':')[1];

    // Use async getRecentTrades method
    orderMatchingService.getRecentTrades(symbol, 50)
      .then(trades => {
        ws.send(JSON.stringify({
          type: 'trades',
          symbol,
          data: trades,
          timestamp: new Date().toISOString()
        }));
      })
      .catch(error => {
        console.error('Error fetching recent trades:', error);
      });
  }
};

// Handle unsubscription requests
const handleUnsubscription = (ws, data) => {
  const { channel } = data;

  if (!channel) {
    return ws.send(JSON.stringify({
      type: 'error',
      message: 'Channel is required for unsubscription'
    }));
  }

  // Remove the subscription
  if (ws.subscriptions) {
    ws.subscriptions.delete(channel);
  }

  // Send confirmation
  ws.send(JSON.stringify({
    type: 'subscription',
    status: 'unsubscribed',
    channel,
    timestamp: new Date().toISOString()
  }));
};

// Import balance service
const balanceService = require('../services/balanceService');

// Handle order requests
const handleOrder = async (ws, data, userData) => {
  try {
    const { symbol, side, type, price, quantity } = data;

    // Validate order parameters
    if (!symbol || !side || !type || !quantity) {
      return ws.send(JSON.stringify({
        type: 'error',
        message: 'Missing required order parameters'
      }));
    }

    // For limit orders, price is required
    if (type.toLowerCase() === 'limit' && !price) {
      return ws.send(JSON.stringify({
        type: 'error',
        message: 'Price is required for limit orders'
      }));
    }

    // Get token symbols from the trading pair
    const [baseToken, quoteToken] = symbol.split('/');

    // Check if user has sufficient balance for the order
    const userBalance = await balanceService.getUserBalance(userData.id);

    // For buy orders, check if user has enough quote token (usually USDC)
    // For sell orders, check if user has enough base token
    if (side.toLowerCase() === 'buy') {
      const totalCost = price * quantity;
      const quoteBalance = quoteToken === 'SOL' ?
        userBalance.solBalance :
        (userBalance.tokenBalances.get(quoteToken) || 0);

      if (quoteBalance < totalCost) {
        return ws.send(JSON.stringify({
          type: 'error',
          message: `Insufficient ${quoteToken} balance. Required: ${totalCost}, Available: ${quoteBalance}`
        }));
      }

      // Reserve the funds by reducing the balance
      if (quoteToken === 'SOL') {
        await balanceService.updateSolBalance(userData.id, -totalCost, {
          type: 'trade',
          description: `Reserved for ${side} order of ${quantity} ${baseToken}`
        });
      } else {
        await balanceService.updateTokenBalance(userData.id, quoteToken, -totalCost, {
          type: 'trade',
          description: `Reserved for ${side} order of ${quantity} ${baseToken}`
        });
      }
    } else { // sell order
      const baseBalance = baseToken === 'SOL' ?
        userBalance.solBalance :
        (userBalance.tokenBalances.get(baseToken) || 0);

      if (baseBalance < quantity) {
        return ws.send(JSON.stringify({
          type: 'error',
          message: `Insufficient ${baseToken} balance. Required: ${quantity}, Available: ${baseBalance}`
        }));
      }

      // Reserve the tokens by reducing the balance
      if (baseToken === 'SOL') {
        await balanceService.updateSolBalance(userData.id, -quantity, {
          type: 'trade',
          description: `Reserved for ${side} order of ${quantity} ${baseToken}`
        });
      } else {
        await balanceService.updateTokenBalance(userData.id, baseToken, -quantity, {
          type: 'trade',
          description: `Reserved for ${side} order of ${quantity} ${baseToken}`
        });
      }
    }

    // Submit order to matching engine
    const trades = await orderMatchingService.submitOrder({
      symbol,
      side,
      type,
      price: price || 0, // For market orders, price can be 0
      quantity,
      userId: userData.id
    });

    // Store order in PostgreSQL
    const orderId = uuidv4();
    const tokenMint = symbol.split('/')[0]; // Extract token symbol from trading pair

    await postgresqlService.createOrder({
      id: orderId,
      type: side.toLowerCase(), // 'buy' or 'sell'
      walletAddress: userData.walletAddress,
      tokenMint,
      tokenAmount: quantity.toString(),
      price: price ? price.toString() : '0',
      totalValue: (price * quantity).toString(),
      status: 'open',
      remainingAmount: quantity.toString(),
      metadata: {
        userId: userData.id,
        orderType: type.toLowerCase(),
        symbol
      }
    });

    // Store trades in PostgreSQL if any were executed
    if (trades && trades.length > 0) {
      for (const trade of trades) {
        const tradeId = uuidv4();
        const [baseToken, quoteToken] = symbol.split('/');
        const tradeValue = trade.price * trade.quantity;

        await postgresqlService.createTrade({
          id: tradeId,
          buyOrderId: side.toLowerCase() === 'buy' ? orderId : trade.matchedOrderId,
          sellOrderId: side.toLowerCase() === 'sell' ? orderId : trade.matchedOrderId,
          tokenMint,
          buyerAddress: side.toLowerCase() === 'buy' ? userData.id : trade.counterpartyId,
          sellerAddress: side.toLowerCase() === 'sell' ? userData.id : trade.counterpartyId,
          tokenAmount: trade.quantity.toString(),
          price: trade.price.toString(),
          totalValue: tradeValue.toString(),
          status: 'completed',
          settledAt: new Date(),
          metadata: {
            symbol,
            matchedAt: new Date()
          }
        });

        // Update order status
        await postgresqlService.updateOrderStatus(orderId,
          trades.length === 1 && trade.quantity === quantity ? 'filled' : 'partial',
          {
            filledAmount: trade.quantity.toString(),
            remainingAmount: (quantity - trade.quantity).toString()
          }
        );

        // Update user balances based on the trade
        if (side.toLowerCase() === 'buy') {
          // Buyer receives base tokens
          await balanceService.updateTokenBalance(userData.id, baseToken, trade.quantity, {
            txId: tradeId,
            type: 'trade',
            description: `Received ${trade.quantity} ${baseToken} from trade`
          });

          // Note: The quote tokens were already reserved when the order was placed
          // If the trade used less than the reserved amount, refund the difference
          const reservedAmount = price * quantity;
          const usedAmount = trade.price * trade.quantity;

          if (reservedAmount > usedAmount) {
            const refundAmount = reservedAmount - usedAmount;
            if (quoteToken === 'SOL') {
              await balanceService.updateSolBalance(userData.id, refundAmount, {
                txId: `refund-${tradeId}`,
                type: 'refund',
                description: `Refund of unused ${quoteToken} from trade`,
                relatedTxId: tradeId
              });
            } else {
              await balanceService.updateTokenBalance(userData.id, quoteToken, refundAmount, {
                txId: `refund-${tradeId}`,
                type: 'refund',
                description: `Refund of unused ${quoteToken} from trade`,
                relatedTxId: tradeId
              });
            }
          }
        } else { // sell order
          // Seller receives quote tokens
          if (quoteToken === 'SOL') {
            await balanceService.updateSolBalance(userData.id, tradeValue, {
              txId: tradeId,
              type: 'trade',
              description: `Received ${tradeValue} ${quoteToken} from trade`
            });
          } else {
            await balanceService.updateTokenBalance(userData.id, quoteToken, tradeValue, {
              txId: tradeId,
              type: 'trade',
              description: `Received ${tradeValue} ${quoteToken} from trade`
            });
          }

          // Note: The base tokens were already reserved when the order was placed
          // If the trade used less than the reserved amount, refund the difference
          if (quantity > trade.quantity) {
            const refundAmount = quantity - trade.quantity;
            if (baseToken === 'SOL') {
              await balanceService.updateSolBalance(userData.id, refundAmount, {
                txId: `refund-${tradeId}`,
                type: 'refund',
                description: `Refund of unused ${baseToken} from trade`,
                relatedTxId: tradeId
              });
            } else {
              await balanceService.updateTokenBalance(userData.id, baseToken, refundAmount, {
                txId: `refund-${tradeId}`,
                type: 'refund',
                description: `Refund of unused ${baseToken} from trade`,
                relatedTxId: tradeId
              });
            }
          }
        }

        // If we have the counterparty ID, update their balance too
        if (trade.counterpartyId) {
          if (side.toLowerCase() === 'buy') {
            // Counterparty is the seller, they receive quote tokens
            if (quoteToken === 'SOL') {
              await balanceService.updateSolBalance(trade.counterpartyId, tradeValue, {
                txId: tradeId,
                type: 'trade',
                description: `Received ${tradeValue} ${quoteToken} from trade`
              });
            } else {
              await balanceService.updateTokenBalance(trade.counterpartyId, quoteToken, tradeValue, {
                txId: tradeId,
                type: 'trade',
                description: `Received ${tradeValue} ${quoteToken} from trade`
              });
            }
          } else {
            // Counterparty is the buyer, they receive base tokens
            await balanceService.updateTokenBalance(trade.counterpartyId, baseToken, trade.quantity, {
              txId: tradeId,
              type: 'trade',
              description: `Received ${trade.quantity} ${baseToken} from trade`
            });
          }
        }
      }
    }

    // Send confirmation to client
    ws.send(JSON.stringify({
      type: 'order',
      status: 'submitted',
      orderId,
      trades,
      timestamp: new Date().toISOString()
    }));
  } catch (error) {
    console.error('Error processing order:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Failed to process order: ' + error.message
    }));
  }
};

// Handle cancel order requests
const handleCancelOrder = async (ws, data, userData) => {
  try {
    const { orderId } = data;

    if (!orderId) {
      return ws.send(JSON.stringify({
        type: 'error',
        message: 'Order ID is required'
      }));
    }

    // Get order details from PostgreSQL to know what to refund
    let orderDetails;
    try {
      orderDetails = await postgresqlService.getOrderById(orderId);

      if (!orderDetails) {
        return ws.send(JSON.stringify({
          type: 'error',
          message: 'Order not found'
        }));
      }

      // Verify the order belongs to this user
      if (orderDetails.metadata && orderDetails.metadata.userId !== userData.id) {
        return ws.send(JSON.stringify({
          type: 'error',
          message: 'You can only cancel your own orders'
        }));
      }
    } catch (dbError) {
      console.warn(`Could not get order details from PostgreSQL: ${dbError.message}`);
      // Continue with cancellation but won't be able to refund
    }

    // Cancel the order
    await orderMatchingService.cancelOrder(orderId);

    // Update order status in PostgreSQL
    try {
      await postgresqlService.updateOrderStatus(orderId, 'cancelled');
    } catch (dbError) {
      console.warn(`Could not update order status in PostgreSQL: ${dbError.message}`);
      // Continue even if PostgreSQL update fails
    }

    // Refund the reserved funds if we have order details
    if (orderDetails) {
      try {
        const symbol = orderDetails.metadata.symbol;
        const [baseToken, quoteToken] = symbol.split('/');
        const orderType = orderDetails.type; // 'buy' or 'sell'
        const remainingAmount = parseFloat(orderDetails.remainingAmount || orderDetails.tokenAmount);
        const price = parseFloat(orderDetails.price);

        if (orderType === 'buy') {
          // Refund the quote tokens (e.g., USDC) that were reserved
          const refundAmount = remainingAmount * price;

          if (quoteToken === 'SOL') {
            await balanceService.updateSolBalance(userData.id, refundAmount, {
              txId: `cancel-${orderId}`,
              type: 'refund',
              description: `Refund from cancelled ${orderType} order`,
              relatedTxId: orderId
            });
          } else {
            await balanceService.updateTokenBalance(userData.id, quoteToken, refundAmount, {
              txId: `cancel-${orderId}`,
              type: 'refund',
              description: `Refund from cancelled ${orderType} order`,
              relatedTxId: orderId
            });
          }
        } else { // sell order
          // Refund the base tokens that were reserved
          if (baseToken === 'SOL') {
            await balanceService.updateSolBalance(userData.id, remainingAmount, {
              txId: `cancel-${orderId}`,
              type: 'refund',
              description: `Refund from cancelled ${orderType} order`,
              relatedTxId: orderId
            });
          } else {
            await balanceService.updateTokenBalance(userData.id, baseToken, remainingAmount, {
              txId: `cancel-${orderId}`,
              type: 'refund',
              description: `Refund from cancelled ${orderType} order`,
              relatedTxId: orderId
            });
          }
        }
      } catch (refundError) {
        console.error('Error refunding from cancelled order:', refundError);
        // Continue even if refund fails - we'll need manual intervention
      }
    }

    // Send confirmation to client
    ws.send(JSON.stringify({
      type: 'cancelOrder',
      status: 'cancelled',
      orderId,
      timestamp: new Date().toISOString()
    }));
  } catch (error) {
    console.error('Error cancelling order:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Failed to cancel order: ' + error.message
    }));
  }
};

// Handle order book requests
const handleGetOrderBook = async (ws, data) => {
  try {
    const { symbol } = data;

    if (!symbol) {
      return ws.send(JSON.stringify({
        type: 'error',
        message: 'Symbol is required'
      }));
    }

    // Get order book
    const orderBook = await orderMatchingService.getOrderBook(symbol);

    // Send order book to client
    ws.send(JSON.stringify({
      type: 'orderBook',
      symbol,
      data: orderBook,
      timestamp: new Date().toISOString()
    }));
  } catch (error) {
    console.error('Error fetching order book:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Failed to fetch order book: ' + error.message
    }));
  }
};

// Handle market data requests
const handleGetMarketData = async (ws, data) => {
  try {
    const { symbol } = data;

    if (!symbol) {
      return ws.send(JSON.stringify({
        type: 'error',
        message: 'Symbol is required'
      }));
    }

    // Get market data
    const marketData = await orderMatchingService.getMarketData(symbol);

    // Send market data to client
    ws.send(JSON.stringify({
      type: 'marketData',
      symbol,
      data: marketData,
      timestamp: new Date().toISOString()
    }));
  } catch (error) {
    console.error('Error fetching market data:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Failed to fetch market data: ' + error.message
    }));
  }
};

// Handle order book depth requests
const handleGetOrderBookDepth = async (ws, data) => {
  try {
    const { symbol, levels } = data;

    if (!symbol) {
      return ws.send(JSON.stringify({
        type: 'error',
        message: 'Symbol is required'
      }));
    }

    // Get order book depth
    const depth = await orderMatchingService.getOrderBookDepth(symbol, levels || 10);

    // Send order book depth to client
    ws.send(JSON.stringify({
      type: 'orderBookDepth',
      symbol,
      data: depth,
      timestamp: new Date().toISOString()
    }));
  } catch (error) {
    console.error('Error fetching order book depth:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Failed to fetch order book depth: ' + error.message
    }));
  }
};

// Handle ping requests (keepalive)
const handlePing = (ws) => {
  ws.send(JSON.stringify({
    type: 'pong',
    timestamp: new Date().toISOString()
  }));
};

// Broadcast message to all clients with a specific subscription
const broadcast = (wss, channel, message) => {
  wss.clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN &&
        client.subscriptions &&
        client.subscriptions.has(channel)) {
      client.send(JSON.stringify(message));
    }
  });
};

module.exports = {
  handleConnection,
  broadcast,
  handleGetOrderBookDepth // Export for direct use if needed
};