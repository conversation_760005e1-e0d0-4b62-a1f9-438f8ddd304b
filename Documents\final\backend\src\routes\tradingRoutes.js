const express = require('express');
const router = express.Router();
// Order matching service removed - using bonding curve smart contracts
const { auth } = require('../middleware/auth');
const jupiterApiService = require('../services/jupiterApiService');
const logger = require('../utils/logger');
// const balanceService = require('../services/balanceService'); // REMOVED - Balance system removed

// Get current market data for available tokens
router.get('/markets', async (req, res) => {
  try {
    // Fetch available markets from Jupiter API
    const markets = await jupiterApiService.getMarkets();

    if (!markets || markets.length === 0) {
      // Use static fallback data for bonding curve tokens
      const fallbackMarkets = [
        {
          id: 'SOL-USDC',
          baseSymbol: 'SOL',
          quoteSymbol: 'USDC',
          name: 'Solana/USD Coin',
          baseDecimals: 9,
          quoteDecimals: 6,
          minOrderSize: 0.01,
          status: 'active',
          price: 100,
          priceChange24h: 5.2,
          volume24h: 1000000
        }
      ];

      return res.json({ success: true, markets: fallbackMarkets });
    }

    res.json({ success: true, markets });
  } catch (error) {
    logger.error('Error fetching markets', error);

    // Fallback to static data if service is unavailable
    const markets = [
      {
        id: 'SOL-USDC',
        baseSymbol: 'SOL',
        quoteSymbol: 'USDC',
        name: 'Solana/USD Coin',
        baseDecimals: 9,
        quoteDecimals: 6,
        minOrderSize: 0.01,
        status: 'active',
        price: 165.23,
        priceChange24h: 2.5,
        volume24h: 14500000
      },
      {
        id: 'ETH-USDC',
        baseSymbol: 'ETH',
        quoteSymbol: 'USDC',
        name: 'Ethereum/USD Coin',
        baseDecimals: 18,
        quoteDecimals: 6,
        minOrderSize: 0.001,
        status: 'active',
        price: 3356.78,
        priceChange24h: -1.2,
        volume24h: 25600000
      }
    ];

    // Log error but still return static data
    res.json({
      success: true,
      markets,
      notice: 'Using static market data. Market service currently unavailable.'
    });
  }
});

// Get bonding curve data for a specific market (replaces order book)
router.get('/orderbook/:marketId', async (req, res) => {
  try {
    const { marketId } = req.params;

    // For bonding curve, we return curve data instead of order book
    const bondingCurveData = {
      marketId,
      currentPrice: 0.05, // Current price on the bonding curve
      totalSupply: 1000000, // Total tokens in circulation
      reserveBalance: 50000, // SOL/USDC in reserve
      curveProgress: 0.25, // 25% of the way up the curve
      nextPricePoint: 0.051, // Next price point
      bids: [], // Empty for bonding curve
      asks: [] // Empty for bonding curve
    };

    res.json({ success: true, orderBook: bondingCurveData });
  } catch (error) {
    logger.error(`Error fetching bonding curve data for ${req.params.marketId}`, error);
    res.status(500).json({ success: false, error: error.message || 'Failed to fetch bonding curve data' });
  }
});

// Get recent trades for a specific market (bonding curve transactions)
router.get('/trades/:marketId', async (req, res) => {
  try {
    const { marketId } = req.params;
    const limit = parseInt(req.query.limit) || 50;

    // Mock recent bonding curve trades
    const trades = [
      {
        id: '1',
        marketId,
        side: 'buy',
        price: 0.049,
        size: 1000,
        timestamp: Date.now() - 60000,
        txHash: 'mock_tx_hash_1'
      },
      {
        id: '2',
        marketId,
        side: 'sell',
        price: 0.048,
        size: 500,
        timestamp: Date.now() - 120000,
        txHash: 'mock_tx_hash_2'
      }
    ].slice(0, limit);

    res.json({ success: true, trades });
  } catch (error) {
    logger.error(`Error fetching trades for ${req.params.marketId}`, error);
    res.status(500).json({ success: false, error: 'Failed to fetch recent trades' });
  }
});

// Get market data for a specific market (bonding curve data)
router.get('/market/:marketId', async (req, res) => {
  try {
    const { marketId } = req.params;

    // Mock bonding curve market data
    const marketData = {
      marketId,
      symbol: marketId.split('-')[0],
      price: 0.05,
      priceChange24h: 2.5,
      volume24h: 25000,
      high24h: 0.052,
      low24h: 0.048,
      totalSupply: 1000000,
      marketCap: 50000,
      bondingCurveProgress: 0.25,
      reserveBalance: 50000
    };

    res.json({ success: true, marketData });
  } catch (error) {
    logger.error(`Error fetching market data for ${req.params.marketId}`, error);
    res.status(500).json({ success: false, error: error.message || 'Failed to fetch market data' });
  }
});

// Submit a new order - requires authentication
router.post('/orders', auth, async (req, res) => {
  try {
    const { symbol, side, type, price, quantity } = req.body;

    // Validate required fields
    if (!symbol || !side || !type || !quantity) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    // Validate order type
    if (type.toLowerCase() === 'limit' && !price) {
      return res.status(400).json({
        success: false,
        error: 'Price is required for limit orders'
      });
    }

    // Define trading fee
    const tradingFee = 0.01; // 0.01 SOL fee for trading

    // Balance check removed - users will pay directly from their wallets
    // try {
    //   const userId = req.user.id;
    //   const userBalance = await balanceService.getUserBalance(userId);

    //   if (parseFloat(userBalance.solBalance) < tradingFee) {
    //     return res.status(400).json({
    //       success: false,
    //       message: `Insufficient balance. Trading requires ${tradingFee} SOL fee.`
    //     });
    //   }

    //   // Deduct fee from user's balance
    //   await balanceService.updateSolBalance(userId, -tradingFee, {
    //     type: 'fee',
    //     description: `Trading fee for ${symbol} ${side.toLowerCase()} order`
    //   });

    //   logger.info(`Deducted ${tradingFee} SOL from user ${userId} for trading`);

    const userId = req.user.id;
    logger.info(`Trading initiated by user ${userId} - payment will be handled directly from wallet`);

    // Add user info to the order
    const order = {
      ...req.body,
      userId: req.user.id
    };

    // Submit to bonding curve smart contract (mock implementation)
    // In real implementation, this would interact with Solana bonding curve program
    const mockTrade = {
      id: `trade_${Date.now()}`,
      orderId: `order_${Date.now()}`,
      marketId: order.symbol,
      side: order.side,
      price: order.price,
      size: order.quantity,
      timestamp: Date.now(),
      userId: order.userId,
      txHash: `mock_tx_${Date.now()}`
    };

    res.json({
      success: true,
      message: 'Bonding curve trade executed successfully',
      trades: [mockTrade],
      fee: tradingFee
    });
  } catch (error) {
    logger.error('Error submitting order', error);
    res.status(500).json({ success: false, error: error.message || 'Failed to submit order' });
  }
});

// Cancel an existing order - requires authentication (not applicable for bonding curve)
router.delete('/orders/:orderId', auth, async (req, res) => {
  try {
    // Bonding curve trades are instant, so cancellation is not applicable
    res.json({
      success: false,
      message: 'Order cancellation not available for bonding curve trades (trades are instant)'
    });
  } catch (error) {
    logger.error(`Error with cancel request for ${req.params.orderId}`, error);
    res.status(500).json({ success: false, error: error.message || 'Failed to process cancel request' });
  }
});

// Get user orders - requires authentication (bonding curve trade history)
router.get('/user/orders', auth, async (req, res) => {
  try {
    // Mock user trade history for bonding curve
    const orders = [
      {
        id: `order_${Date.now() - 86400000}`,
        marketId: 'SAMPLE-USDC',
        side: 'buy',
        price: 0.048,
        quantity: 1000,
        status: 'filled',
        timestamp: Date.now() - 86400000,
        txHash: 'mock_tx_hash_1'
      }
    ];

    res.json({ success: true, orders });
  } catch (error) {
    logger.error('Error fetching user orders', error);
    res.status(500).json({ success: false, error: error.message || 'Failed to fetch orders' });
  }
});

// Get order status - requires authentication (bonding curve trade status)
router.get('/orders/:orderId', auth, async (req, res) => {
  try {
    const { orderId } = req.params;

    // Mock order status for bonding curve (all trades are instant)
    const status = {
      orderId,
      status: 'filled',
      filledQuantity: 1000,
      remainingQuantity: 0,
      averagePrice: 0.05,
      timestamp: Date.now() - 60000,
      txHash: `mock_tx_${orderId}`
    };

    res.json({ success: true, status });
  } catch (error) {
    logger.error(`Error fetching order status for ${req.params.orderId}`, error);
    res.status(500).json({ success: false, error: error.message || 'Failed to fetch order status' });
  }
});

// Get chart data for a specific market
router.get('/chart/:marketId', async (req, res) => {
  try {
    const { marketId } = req.params;
    const { timeframe = '1d' } = req.query;

    // Extract token symbol from market ID
    const tokenSymbol = marketId.split('-')[0];

    // Get chart data from Jupiter API
    const chartData = await jupiterApiService.getChartData(tokenSymbol, timeframe);

    // Add real-time flag for short timeframes
    const isRealTime = timeframe === '1h' || timeframe === '4h';

    res.json({
      success: true,
      data: chartData,
      meta: {
        isRealTime,
        updateInterval: isRealTime ? (timeframe === '1h' ? 10 : 30) : null, // seconds
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error(`Error fetching chart data for ${req.params.marketId}`, error);
    res.status(500).json({ success: false, error: 'Failed to fetch chart data' });
  }
});

// Get latest price for a token (for real-time updates)
router.get('/price/:tokenSymbol', async (req, res) => {
  try {
    const { tokenSymbol } = req.params;

    // Get token price from Jupiter API
    const priceData = await jupiterApiService.getTokenPrice(tokenSymbol);

    res.json({
      success: true,
      price: priceData.price || 0,
      timestamp: Math.floor(Date.now() / 1000)
    });
  } catch (error) {
    logger.error(`Error fetching price for ${req.params.tokenSymbol}`, error);
    res.status(500).json({ success: false, error: 'Failed to fetch price data' });
  }
});

// Search for tokens
router.get('/search', async (req, res) => {
  try {
    const { query, limit = 10 } = req.query;

    // Search tokens using Jupiter API
    const tokens = await jupiterApiService.searchTokens(query, parseInt(limit));

    res.json({ success: true, tokens });
  } catch (error) {
    logger.error('Error searching tokens', error);
    res.status(500).json({ success: false, error: 'Failed to search tokens' });
  }
});

// Get token details
router.get('/token/:tokenId', async (req, res) => {
  try {
    const { tokenId } = req.params;

    // Get token details from Jupiter API
    const tokenDetails = await jupiterApiService.getTokenDetails(tokenId);

    if (!tokenDetails) {
      return res.status(404).json({ success: false, error: 'Token not found' });
    }

    res.json({ success: true, token: tokenDetails });
  } catch (error) {
    logger.error(`Error fetching token details for ${req.params.tokenId}`, error);
    res.status(500).json({ success: false, error: 'Failed to fetch token details' });
  }
});

// Get user trade history - requires authentication
router.get('/user/trades', auth, async (req, res) => {
  try {
    // Mock data for now - this would fetch from the database
    // We'll need to implement a database query for user trade history
    const trades = [
      {
        id: 'tr1',
        symbol: 'SOL-USDC',
        side: 'buy',
        price: 162.5,
        quantity: 0.5,
        total: 81.25,
        fee: 0.2,
        timestamp: Date.now() - 86400000
      },
      {
        id: 'tr2',
        symbol: 'ETH-USDC',
        side: 'sell',
        price: 3350.0,
        quantity: 0.05,
        total: 167.5,
        fee: 0.5,
        timestamp: Date.now() - 172800000
      }
    ];

    res.json({ success: true, trades });
  } catch (error) {
    logger.error('Error fetching user trades', error);
    res.status(500).json({ success: false, error: 'Failed to fetch trades' });
  }
});

module.exports = router;