import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const merlinErigonTestnet = /*#__PURE__*/ define<PERSON>hain({
    id: 4203,
    name: '<PERSON> Testnet',
    nativeCurrency: {
        name: '<PERSON><PERSON>',
        symbol: 'BTC',
        decimals: 18,
    },
    rpcUrls: {
        default: { http: ['https://testnet-erigon-rpc.merlinchain.io'] },
    },
    blockExplorers: {
        default: {
            name: 'blockscout',
            url: 'https://testnet-erigon-scan.merlinchain.io',
            apiUrl: 'https://testnet-erigon-scan.merlinchain.io/api',
        },
    },
    testnet: true,
});
//# sourceMappingURL=merlinErigonTestnet.js.map