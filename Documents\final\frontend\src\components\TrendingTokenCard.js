import React from 'react';
import { useNavigate } from 'react-router-dom';

const TrendingTokenCard = ({ token }) => {
  const navigate = useNavigate();

  const handleCardClick = () => {
    navigate(`/trade/${token.id}`);
  };

  return (
    <div className="trending-token-card" onClick={handleCardClick} style={{ cursor: 'pointer' }}>
      <div className="trending-token-content">
        <div className="trending-token-left">
          <div className="trending-token-image-container">
            <img src={token.image || '/default-token.png'} alt={token.name} className="trending-token-image" />
          </div>
        </div>
        <div className="trending-token-right">
          <div className="trending-token-header">
            <div className="trending-token-name">{token.name}</div>
            <div className="trending-token-symbol">{token.symbol}</div>
          </div>
          <div className="trending-token-stats">
            <div className="trending-token-market-cap">
              Market Cap: <span className="trending-token-value">${token.marketCap || 'N/A'}</span>
            </div>
            <div className="trending-token-holders">
              <span className="trending-token-value">{token.holders || 0}</span> Holders
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrendingTokenCard;
