/**
 * PostgreSQL Data Access Layer for Assets
 */
const { Pool } = require('pg');
const config = require('../config/postgres');

// Get the PostgreSQL pool from the config
const pool = config.getPool();

/**
 * Get all assets for a user
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} - Array of assets
 */
async function getUserAssets(userId) {
  const query = `
    SELECT * FROM assets 
    WHERE user_id = $1 
    ORDER BY value DESC
  `;
  
  try {
    const result = await pool.query(query, [userId]);
    return result.rows;
  } catch (error) {
    console.error('Error fetching user assets:', error);
    throw error;
  }
}

/**
 * Get a specific asset for a user
 * @param {string} userId - The user ID
 * @param {string} symbol - The asset symbol
 * @returns {Promise<Object>} - The asset
 */
async function getUserAsset(userId, symbol) {
  const query = `
    SELECT * FROM assets 
    WHERE user_id = $1 AND symbol = $2
  `;
  
  try {
    const result = await pool.query(query, [userId, symbol]);
    return result.rows[0];
  } catch (error) {
    console.error('Error fetching user asset:', error);
    throw error;
  }
}

/**
 * Create or update an asset for a user
 * @param {Object} asset - The asset object
 * @returns {Promise<Object>} - The created or updated asset
 */
async function upsertAsset(asset) {
  const { user_id, symbol, balance, value, change_24h } = asset;
  
  const query = `
    INSERT INTO assets (user_id, symbol, balance, value, change_24h, last_updated)
    VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
    ON CONFLICT (user_id, symbol) 
    DO UPDATE SET 
      balance = $3,
      value = $4,
      change_24h = $5,
      last_updated = CURRENT_TIMESTAMP
    RETURNING *
  `;
  
  try {
    const result = await pool.query(query, [user_id, symbol, balance, value, change_24h]);
    return result.rows[0];
  } catch (error) {
    console.error('Error upserting asset:', error);
    throw error;
  }
}

/**
 * Update asset balance
 * @param {string} userId - The user ID
 * @param {string} symbol - The asset symbol
 * @param {number} amount - The amount to add (positive) or subtract (negative)
 * @returns {Promise<Object>} - The updated asset
 */
async function updateAssetBalance(userId, symbol, amount) {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    // First check if the asset exists
    const checkQuery = `
      SELECT * FROM assets 
      WHERE user_id = $1 AND symbol = $2
      FOR UPDATE
    `;
    
    const checkResult = await client.query(checkQuery, [userId, symbol]);
    
    let result;
    if (checkResult.rows.length === 0) {
      // Asset doesn't exist, create it
      const insertQuery = `
        INSERT INTO assets (user_id, symbol, balance, value, last_updated)
        VALUES ($1, $2, $3, 0, CURRENT_TIMESTAMP)
        RETURNING *
      `;
      
      result = await client.query(insertQuery, [userId, symbol, amount]);
    } else {
      // Asset exists, update it
      const updateQuery = `
        UPDATE assets 
        SET balance = balance + $3,
            last_updated = CURRENT_TIMESTAMP
        WHERE user_id = $1 AND symbol = $2
        RETURNING *
      `;
      
      result = await client.query(updateQuery, [userId, symbol, amount]);
    }
    
    await client.query('COMMIT');
    return result.rows[0];
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error updating asset balance:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Delete an asset
 * @param {string} userId - The user ID
 * @param {string} symbol - The asset symbol
 * @returns {Promise<boolean>} - True if deleted, false otherwise
 */
async function deleteAsset(userId, symbol) {
  const query = `
    DELETE FROM assets 
    WHERE user_id = $1 AND symbol = $2
    RETURNING id
  `;
  
  try {
    const result = await pool.query(query, [userId, symbol]);
    return result.rowCount > 0;
  } catch (error) {
    console.error('Error deleting asset:', error);
    throw error;
  }
}

/**
 * Update asset prices and values
 * @param {Array} priceUpdates - Array of {symbol, price} objects
 * @returns {Promise<Array>} - Array of updated assets
 */
async function updateAssetPrices(priceUpdates) {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    const updatedAssets = [];
    
    for (const update of priceUpdates) {
      const { symbol, price } = update;
      
      // Update asset values
      const updateQuery = `
        UPDATE assets 
        SET value = balance * $2,
            last_updated = CURRENT_TIMESTAMP
        WHERE symbol = $1
        RETURNING *
      `;
      
      const result = await client.query(updateQuery, [symbol, price]);
      updatedAssets.push(...result.rows);
      
      // Record price history
      const historyQuery = `
        INSERT INTO asset_price_history (symbol, price)
        VALUES ($1, $2)
      `;
      
      await client.query(historyQuery, [symbol, price]);
    }
    
    await client.query('COMMIT');
    return updatedAssets;
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error updating asset prices:', error);
    throw error;
  } finally {
    client.release();
  }
}

module.exports = {
  getUserAssets,
  getUserAsset,
  upsertAsset,
  updateAssetBalance,
  deleteAsset,
  updateAssetPrices
};
