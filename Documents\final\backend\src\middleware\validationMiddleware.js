/**
 * Validation Middleware
 * Validates request parameters, query strings, and body
 */

/**
 * Validate a value against a schema
 * @param {*} value - The value to validate
 * @param {Object} schema - The schema to validate against
 * @returns {Object} - Validation result
 */
function validateValue(value, schema) {
  const { type, required, enum: enumValues, min, max, minLength, maxLength, pattern, items } = schema;
  
  // Check if required
  if (required && (value === undefined || value === null || value === '')) {
    return { valid: false, message: 'Value is required' };
  }
  
  // If not required and not provided, it's valid
  if (!required && (value === undefined || value === null || value === '')) {
    return { valid: true };
  }
  
  // Type validation
  if (type) {
    if (type === 'string' && typeof value !== 'string') {
      return { valid: false, message: 'Value must be a string' };
    }
    
    if (type === 'number') {
      const num = Number(value);
      if (isNaN(num)) {
        return { valid: false, message: 'Value must be a number' };
      }
      
      // Check min/max
      if (min !== undefined && num < min) {
        return { valid: false, message: `Value must be at least ${min}` };
      }
      
      if (max !== undefined && num > max) {
        return { valid: false, message: `Value must be at most ${max}` };
      }
    }
    
    if (type === 'boolean' && typeof value !== 'boolean') {
      // Try to convert string to boolean
      if (value === 'true' || value === 'false') {
        return { valid: true };
      }
      return { valid: false, message: 'Value must be a boolean' };
    }
    
    if (type === 'array' && !Array.isArray(value)) {
      return { valid: false, message: 'Value must be an array' };
    }
    
    if (type === 'object' && (typeof value !== 'object' || Array.isArray(value) || value === null)) {
      return { valid: false, message: 'Value must be an object' };
    }
  }
  
  // String validations
  if (typeof value === 'string') {
    if (minLength !== undefined && value.length < minLength) {
      return { valid: false, message: `Value must be at least ${minLength} characters` };
    }
    
    if (maxLength !== undefined && value.length > maxLength) {
      return { valid: false, message: `Value must be at most ${maxLength} characters` };
    }
    
    if (pattern && !new RegExp(pattern).test(value)) {
      return { valid: false, message: 'Value does not match required pattern' };
    }
  }
  
  // Enum validation
  if (enumValues && !enumValues.includes(value)) {
    return { valid: false, message: `Value must be one of: ${enumValues.join(', ')}` };
  }
  
  // Array item validation
  if (Array.isArray(value) && items) {
    for (let i = 0; i < value.length; i++) {
      const itemResult = validateValue(value[i], items);
      if (!itemResult.valid) {
        return { valid: false, message: `Item at index ${i}: ${itemResult.message}` };
      }
    }
  }
  
  // Object property validation
  if (typeof value === 'object' && !Array.isArray(value) && value !== null && schema.properties) {
    for (const [propName, propSchema] of Object.entries(schema.properties)) {
      const propResult = validateValue(value[propName], propSchema);
      if (!propResult.valid) {
        return { valid: false, message: `Property '${propName}': ${propResult.message}` };
      }
    }
  }
  
  return { valid: true };
}

/**
 * Validate request middleware
 * @param {Object} schema - Validation schema
 * @returns {Function} - Express middleware
 */
function validateRequest(schema) {
  return (req, res, next) => {
    const errors = [];
    
    // Validate request parameters
    if (schema.params) {
      for (const [paramName, paramSchema] of Object.entries(schema.params)) {
        const result = validateValue(req.params[paramName], paramSchema);
        if (!result.valid) {
          errors.push(`Parameter '${paramName}': ${result.message}`);
        }
      }
    }
    
    // Validate query string
    if (schema.query) {
      for (const [queryName, querySchema] of Object.entries(schema.query)) {
        const result = validateValue(req.query[queryName], querySchema);
        if (!result.valid) {
          errors.push(`Query parameter '${queryName}': ${result.message}`);
        }
      }
    }
    
    // Validate request body
    if (schema.body) {
      for (const [fieldName, fieldSchema] of Object.entries(schema.body)) {
        const result = validateValue(req.body[fieldName], fieldSchema);
        if (!result.valid) {
          errors.push(`Field '${fieldName}': ${result.message}`);
        }
      }
    }
    
    // If there are validation errors, return a 400 response
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
    }
    
    // If validation passes, continue to the next middleware
    next();
  };
}

module.exports = {
  validateRequest
};
