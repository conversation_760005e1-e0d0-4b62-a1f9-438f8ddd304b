const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const Comment = require('../models/Comment');
const { auth } = require('../middleware/auth');
const rateLimit = require('express-rate-limit');
const crypto = require('crypto');
const ensureMongooseConnected = require('../middleware/ensureMongooseConnected');
const mongooseConnection = require('../config/mongoose_connection');
const commentMongoService = require('../services/commentMongoService');
const jwt = require('jsonwebtoken');

// Create a rate limiter specifically for comments
const commentLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 10, // limit each IP to 10 requests per minute
  message: { success: false, error: 'Too many comments posted. Please try again later.' },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

// Apply the rate limiter to the POST endpoint only
const postLimiter = (req, res, next) => {
  // Skip rate limiting for admin users
  if (req.token === 'special-admin-token' ||
      (req.user && req.user.role === 'admin')) {
    return next();
  }
  return commentLimiter(req, res, next);
};

// Batch endpoint to get like status for multiple comments
router.get('/liked-status', ensureMongooseConnected, async (req, res) => {
  // Set CORS headers explicitly for this route
  res.header('Access-Control-Allow-Origin', req.headers.origin || 'http://localhost:3000');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');

  // Manual authentication check
  const accessToken = req.cookies?.access_token;
  const headerToken = req.header('Authorization')?.replace('Bearer ', '');
  const token = accessToken || headerToken;

  if (!token) {
    console.error('No authentication token provided for batch like status');
    return res.status(401).json({
      success: false,
      error: 'You must be logged in to check like status',
      code: 'AUTH_REQUIRED'
    });
  }

  // Verify token manually
  try {
    const JWT_SECRET = process.env.JWT_SECRET || 'swap_secure_jwt_secret_key_2024';
    const decoded = jwt.verify(token, JWT_SECRET);

    // Set the user in the request object
    req.user = decoded;
    console.log('User authenticated successfully for batch like status:', req.user);
  } catch (err) {
    console.error('Token verification error:', err);
    return res.status(401).json({
      success: false,
      error: 'Authentication failed. Please log in again.',
      code: 'AUTH_FAILED'
    });
  }
  try {
    const { ids } = req.query;

    if (!ids) {
      return res.status(400).json({ success: false, error: 'Comment IDs are required' });
    }

    const commentIds = ids.split(',');
    const userId = req.user.id || req.user._id;

    // Get all the specified comments
    const comments = await Comment.find({ _id: { $in: commentIds } });

    // Create objects to store like status and counts
    const likedStatus = {};
    const likeCounts = {};

    // Check each comment for like status
    comments.forEach(comment => {
      // Store the like count
      likeCounts[comment._id] = comment.likes.count;

      // Check if user has liked this comment
      const hasLiked = comment.likes.users.some(id => id.toString() === userId.toString());
      likedStatus[comment._id] = hasLiked;
    });

    res.json({
      success: true,
      likedStatus,
      likeCounts
    });
  } catch (error) {
    console.error('Error fetching batch like status:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch like statuses' });
  }
});

// Get comments for a specific token - no auth required for viewing comments
router.get('/:tokenSymbol', async (req, res) => {
  // Set CORS headers explicitly for this route
  res.header('Access-Control-Allow-Origin', req.headers.origin || 'http://localhost:3000');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Cache-Control, Pragma');
  try {
    const { tokenSymbol } = req.params;
    const limit = parseInt(req.query.limit) || 50;

    // Simple logging for development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Fetching comments for token: ${tokenSymbol}`);
    }

    // Log MongoDB fetching in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log(`Fetching comments for ${tokenSymbol} from MongoDB (connection state: ${mongoose.connection.readyState})`);
    }

    // If MongoDB is not connected, try to connect
    if (mongoose.connection.readyState !== 1) {
      try {
        await mongooseConnection.initializeMongoose();
        console.log('MongoDB connection established successfully');
      } catch (connError) {
        console.error('Failed to connect to MongoDB:', connError);
        return res.status(503).json({
          success: false,
          error: 'Database connection unavailable. Please try again later.'
        });
      }
    }

    // Get comments directly from database
    let comments = await Comment.find({ tokenSymbol })
      .sort({ createdAt: -1 })
      .limit(limit)
      .populate('user', 'username profileImage profilePicture')
      .lean();

    // Ensure the comments array is valid
    if (!comments) comments = [];

    // Process comments to handle admin users properly and ensure likes count is correct
    comments = comments.map(comment => {
      // Ensure likes count matches the users array length if it exists
      if (comment.likes && comment.likes.users) {
        comment.likes.count = comment.likes.users.length;
      } else if (!comment.likes) {
        comment.likes = { count: 0, users: [] };
      } else if (!comment.likes.users) {
        comment.likes.users = [];
        comment.likes.count = 0;
      }

      // If user is null or undefined, check if it's an admin comment
      if (!comment.user) {
        // This is likely an admin comment where the user reference doesn't exist
        return {
          ...comment,
          user: {
            _id: 'admin123',
            username: 'Admin',
            profileImage: 'https://via.placeholder.com/40',
            profilePicture: null // Will be fetched from database if available
          }
        };
      }
      return comment;
    });

    // Return the comments to the client with proper format
    if (process.env.NODE_ENV === 'development') {
      console.log(`Returning ${comments.length} comments for ${tokenSymbol} from database`);
    }
    return res.json({ success: true, comments });

  } catch (error) {
    console.error('Error retrieving comments:', error);

    // Set CORS headers even for error responses
    res.header('Access-Control-Allow-Origin', req.headers.origin || 'http://localhost:3000');
    res.header('Access-Control-Allow-Credentials', 'true');

    // Provide more detailed error information
    return res.status(500).json({
      success: false,
      error: 'Failed to retrieve comments. Please try again later.',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
      code: error.code || 'INTERNAL_ERROR'
    });
  }
});

// Post a new comment
router.post('/', async (req, res) => {
  console.log('=== COMMENT POST REQUEST ===');
  console.log('Headers:', JSON.stringify(req.headers));
  console.log('Cookies:', req.cookies);
  console.log('Body:', req.body);

  // Manual authentication check
  const accessToken = req.cookies?.access_token;
  const headerToken = req.header('Authorization')?.replace('Bearer ', '');
  const token = accessToken || headerToken;

  if (!token) {
    console.error('No authentication token provided for comment post');
    return res.status(401).json({
      success: false,
      error: 'You must be logged in to post a comment',
      code: 'AUTH_REQUIRED'
    });
  }

  // Verify token manually
  try {
    const JWT_SECRET = process.env.JWT_SECRET || 'swap_secure_jwt_secret_key_2024';
    const decoded = jwt.verify(token, JWT_SECRET);

    // Set the user in the request object
    req.user = decoded;
    console.log('User authenticated successfully:', req.user);
  } catch (err) {
    console.error('Token verification error:', err);
    return res.status(401).json({
      success: false,
      error: 'Authentication failed. Please log in again.',
      code: 'AUTH_FAILED'
    });
  }
  // Set CORS headers explicitly for this route
  res.header('Access-Control-Allow-Origin', req.headers.origin || 'http://localhost:3000');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');

  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Log all request details for debugging
  console.log('Comment post request details:');
  console.log('Headers:', req.headers);
  console.log('Cookies:', req.cookies);
  console.log('Body:', req.body);
  console.log('User:', req.user);

  // Apply rate limiting
  try {
    await new Promise((resolve, reject) => {
      postLimiter(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  } catch (err) {
    return res.status(429).json({
      success: false,
      error: 'Too many comments posted. Please wait a moment and try again.'
    });
  }


  try {
    console.log('Received comment post request:', req.body);

    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      console.error('User not authenticated for comment post');
      return res.status(401).json({
        success: false,
        error: 'You must be logged in to post a comment',
        code: 'AUTH_REQUIRED'
      });
    }

    console.log('User in request:', req.user);
    console.log('MongoDB connection state:', mongoose.connection.readyState);

    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      try {
        await mongooseConnection.initializeMongoose();
        console.log('MongoDB connection established successfully');
      } catch (connError) {
        console.error('Failed to connect to MongoDB:', connError);
        return res.status(503).json({
          success: false,
          error: 'Database connection unavailable. Please try again later.'
        });
      }
    }

    const { tokenSymbol, content } = req.body;
    const userId = req.user.id || req.user._id;

    // Validate input
    if (!tokenSymbol || !content.trim()) {
      return res.status(400).json({
        success: false,
        error: 'Token symbol and comment content are required'
      });
    }

    if (content.length > 1000) {
      return res.status(400).json({
        success: false,
        error: 'Comment content cannot exceed 1000 characters'
      });
    }

    // Create a new comment
    const newComment = new Comment({
      tokenSymbol,
      content: content.trim(),
      user: userId,
      likes: { count: 0, users: [] },
      createdAt: new Date()
    });

    // Save to database
    await newComment.save();

    // Populate user information
    await newComment.populate('user', 'username profileImage profilePicture');

    // Log successful comment creation
    if (process.env.NODE_ENV === 'development') {
      console.log(`Comment ${newComment._id} for ${tokenSymbol} saved successfully`);
    }

    // Return the created comment
    return res.status(201).json({
      success: true,
      comment: newComment
    });

  } catch (error) {
    console.error('Error posting comment:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to post comment. Please try again later.'
    });
  }
});

// Delete a comment (requires authentication and ownership or admin privileges)
router.delete('/:commentId', auth, ensureMongooseConnected, async (req, res) => {
  try {
    const { commentId } = req.params;
    const userId = req.user.id || req.user._id;

    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      try {
        await mongooseConnection.initializeMongoose();
        console.log('MongoDB connection established successfully for delete');
      } catch (connError) {
        console.error('Failed to connect to MongoDB for delete:', connError);
        return res.status(503).json({
          success: false,
          error: 'Database connection unavailable. Please try again later.'
        });
      }
    }

    // Find the comment
    const comment = await Comment.findById(commentId);

    // If comment doesn't exist, return error
    if (!comment) {
      return res.status(404).json({
        success: false,
        error: 'Comment not found'
      });
    }

    // Check if user is authorized to delete this comment
    const isAdmin = req.user.role === 'admin';
    const isCommentOwner = comment.user && comment.user.toString() === userId.toString();

    if (!isAdmin && !isCommentOwner) {
      return res.status(403).json({
        success: false,
        error: 'You are not authorized to delete this comment'
      });
    }

    // Delete the comment
    await Comment.findByIdAndDelete(commentId);

    // Log successful comment deletion
    if (process.env.NODE_ENV === 'development') {
      console.log(`Comment ${commentId} deleted successfully`);
    }

    return res.json({ success: true, message: 'Comment deleted successfully' });

  } catch (error) {
    console.error('Error deleting comment:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to delete comment. Please try again later.'
    });
  }
});

// Like a comment
router.post('/:commentId/like', ensureMongooseConnected, async (req, res) => {
  // Set CORS headers explicitly for this route
  res.header('Access-Control-Allow-Origin', req.headers.origin || 'http://localhost:3000');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');

  // Manual authentication check
  const accessToken = req.cookies?.access_token;
  const headerToken = req.header('Authorization')?.replace('Bearer ', '');
  const token = accessToken || headerToken;

  if (!token) {
    console.error('No authentication token provided for like action');
    return res.status(401).json({
      success: false,
      error: 'You must be logged in to like comments',
      code: 'AUTH_REQUIRED'
    });
  }

  // Verify token manually
  try {
    const JWT_SECRET = process.env.JWT_SECRET || 'swap_secure_jwt_secret_key_2024';
    const decoded = jwt.verify(token, JWT_SECRET);

    // Set the user in the request object
    req.user = decoded;
    console.log('User authenticated successfully for like action:', req.user);
  } catch (err) {
    console.error('Token verification error:', err);
    return res.status(401).json({
      success: false,
      error: 'Authentication failed. Please log in again.',
      code: 'AUTH_FAILED'
    });
  }
  try {
    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      try {
        await mongooseConnection.initializeMongoose();
      } catch (connError) {
        return res.status(503).json({
          success: false,
          error: 'Database connection unavailable. Please try again later.'
        });
      }
    }

    const comment = await Comment.findById(req.params.commentId);

    if (!comment) {
      return res.status(404).json({ success: false, error: 'Comment not found' });
    }

    const userId = req.user.id || req.user._id;

    // Check if user has already liked the comment
    const alreadyLiked = comment.likes.users.some(id => id.toString() === userId.toString());

    if (alreadyLiked) {
      return res.json({
        success: true,
        message: 'You have already liked this comment',
        liked: true,
        likes: comment.likes.count
      });
    }

    // Add user to likes array
    comment.likes.users.push(userId);
    comment.likes.count = comment.likes.users.length;

    await comment.save();

    // Update the like status in Redis
    try {
      await commentRedisService.cacheLikeStatus(req.params.commentId, userId, true);
      // Also invalidate the comments list cache so it's updated
      await commentRedisService.invalidateCommentsCache(comment.tokenSymbol);
    } catch (redisError) {
      console.error('Error updating Redis cache for like:', redisError);
      // Continue even if Redis update fails
    }

    return res.json({
      success: true,
      message: 'Comment liked successfully',
      liked: true,
      likes: comment.likes.count
    });

  } catch (error) {
    console.error('Error liking comment:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to like comment. Please try again later.'
    });
  }
});

// Unlike a comment
router.post('/:commentId/unlike', ensureMongooseConnected, async (req, res) => {
  // Set CORS headers explicitly for this route
  res.header('Access-Control-Allow-Origin', req.headers.origin || 'http://localhost:3000');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');

  // Manual authentication check
  const accessToken = req.cookies?.access_token;
  const headerToken = req.header('Authorization')?.replace('Bearer ', '');
  const token = accessToken || headerToken;

  if (!token) {
    console.error('No authentication token provided for unlike action');
    return res.status(401).json({
      success: false,
      error: 'You must be logged in to unlike comments',
      code: 'AUTH_REQUIRED'
    });
  }

  // Verify token manually
  try {
    const JWT_SECRET = process.env.JWT_SECRET || 'swap_secure_jwt_secret_key_2024';
    const decoded = jwt.verify(token, JWT_SECRET);

    // Set the user in the request object
    req.user = decoded;
    console.log('User authenticated successfully for unlike action:', req.user);
  } catch (err) {
    console.error('Token verification error:', err);
    return res.status(401).json({
      success: false,
      error: 'Authentication failed. Please log in again.',
      code: 'AUTH_FAILED'
    });
  }
  try {
    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      try {
        await mongooseConnection.initializeMongoose();
      } catch (connError) {
        return res.status(503).json({
          success: false,
          error: 'Database connection unavailable. Please try again later.'
        });
      }
    }

    const comment = await Comment.findById(req.params.commentId);

    if (!comment) {
      return res.status(404).json({ success: false, error: 'Comment not found' });
    }

    const userId = req.user.id || req.user._id;

    // Check if user has liked the comment
    const likedIndex = comment.likes.users.findIndex(id => id.toString() === userId.toString());

    if (likedIndex === -1) {
      return res.json({
        success: true,
        message: 'You have not liked this comment',
        liked: false,
        likes: comment.likes.count
      });
    }

    // Remove user from likes array
    comment.likes.users.splice(likedIndex, 1);
    comment.likes.count = comment.likes.users.length;

    await comment.save();

    // Update the like status in Redis
    try {
      await commentRedisService.cacheLikeStatus(req.params.commentId, userId, false);
      // Also invalidate the comments list cache so it's updated
      await commentRedisService.invalidateCommentsCache(comment.tokenSymbol);
    } catch (redisError) {
      console.error('Error updating Redis cache for unlike:', redisError);
      // Continue even if Redis update fails
    }

    return res.json({
      success: true,
      message: 'Comment unliked successfully',
      liked: false,
      likes: comment.likes.count
    });

  } catch (error) {
    console.error('Error unliking comment:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to unlike comment. Please try again later.'
    });
  }
});

// Check if user has liked a comment
router.get('/:commentId/liked', ensureMongooseConnected, async (req, res) => {
  // Set CORS headers explicitly for this route
  res.header('Access-Control-Allow-Origin', req.headers.origin || 'http://localhost:3000');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');

  // Manual authentication check
  const accessToken = req.cookies?.access_token;
  const headerToken = req.header('Authorization')?.replace('Bearer ', '');
  const token = accessToken || headerToken;

  if (!token) {
    console.error('No authentication token provided for checking like status');
    return res.status(401).json({
      success: false,
      error: 'You must be logged in to check like status',
      code: 'AUTH_REQUIRED'
    });
  }

  // Verify token manually
  try {
    const JWT_SECRET = process.env.JWT_SECRET || 'swap_secure_jwt_secret_key_2024';
    const decoded = jwt.verify(token, JWT_SECRET);

    // Set the user in the request object
    req.user = decoded;
    console.log('User authenticated successfully for checking like status:', req.user);
  } catch (err) {
    console.error('Token verification error:', err);
    return res.status(401).json({
      success: false,
      error: 'Authentication failed. Please log in again.',
      code: 'AUTH_FAILED'
    });
  }
  try {
    const commentId = req.params.commentId;
    const userId = req.user.id || req.user._id;

    // First check Redis cache
    try {
      const cachedLikeStatus = await commentRedisService.getCachedLikeStatus(commentId, userId);
      if (cachedLikeStatus !== null) {
        // Use data from cache
        return res.json({
          success: true,
          liked: cachedLikeStatus
        });
      }
    } catch (redisError) {
      console.error('Error checking like status in Redis:', redisError);
      // Continue to MongoDB if Redis fails
    }

    // If not in cache or Redis is unavailable, check MongoDB
    const comment = await Comment.findById(commentId);

    if (!comment) {
      return res.status(404).json({ success: false, error: 'Comment not found' });
    }

    // Check if user has liked the comment
    const hasLiked = comment.likes.users.some(id => id.toString() === userId.toString());

    // Cache the like status in Redis
    try {
      await commentRedisService.cacheLikeStatus(commentId, userId, hasLiked);
    } catch (redisError) {
      console.error('Error caching like status in Redis:', redisError);
      // Continue even if Redis caching fails
    }

    return res.json({
      success: true,
      liked: hasLiked,
      likes: comment.likes.count
    });

  } catch (error) {
    console.error('Error checking like status:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to check like status. Please try again later.'
    });
  }
});

module.exports = router;
