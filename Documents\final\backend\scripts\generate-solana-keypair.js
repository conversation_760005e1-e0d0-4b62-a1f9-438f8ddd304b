/**
 * <PERSON><PERSON><PERSON> to generate a Solana keypair for development purposes
 * Run with: node scripts/generate-solana-keypair.js
 */

const { Keypair } = require('@solana/web3.js');
const fs = require('fs');
const path = require('path');

// Generate a new keypair
const keypair = Keypair.generate();

// Get the keypair as JSON
const privateKey = Array.from(keypair.secretKey);
const publicKey = keypair.publicKey.toString();

// Create the output
const output = {
  publicKey,
  privateKey
};

// Save to a file
const outputPath = path.join(__dirname, 'solana-keypair.json');
fs.writeFileSync(outputPath, JSON.stringify(output, null, 2));

console.log(`Generated Solana keypair:`);
console.log(`Public Key: ${publicKey}`);
console.log(`Private Key: [${privateKey}]`);
console.log(`\nSaved to: ${outputPath}`);
console.log(`\nTo use this keypair, update your .env file with:`);
console.log(`SOLANA_PRIVATE_KEY=[${privateKey}]`);
console.log(`\nWARNING: This is for development only. Never commit private keys to version control.`);
