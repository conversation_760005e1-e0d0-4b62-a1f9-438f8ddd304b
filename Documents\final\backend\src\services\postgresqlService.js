/**
 * PostgreSQL Service
 *
 * Provides methods for interacting with PostgreSQL database
 * for trading and transaction related data.
 */

const { sequelize, testConnection, initializeDatabase } = require('../config/postgresql');
const Transaction = require('../models/postgresql/transaction');
const Order = require('../models/postgresql/order');
const Trade = require('../models/postgresql/trade');
const MarketData = require('../models/postgresql/marketData');
const UserBalance = require('../models/postgresql/userBalance');
const BalanceTransaction = require('../models/postgresql/balanceTransaction');
const logger = require('../utils/logger');
const { Op } = require('sequelize');

class PostgresqlService {
  constructor() {
    this.isInitialized = false;
    this.models = {
      Transaction,
      Order,
      Trade,
      MarketData,
      UserBalance,
      BalanceTransaction
    };
  }

  /**
   * Initialize the PostgreSQL service
   * @returns {Promise<boolean>} True if initialized successfully
   */
  async initialize() {
    try {
      logger.info('Initializing PostgreSQL service...');

      // Test the connection
      const connectionSuccessful = await testConnection();
      if (!connectionSuccessful) {
        logger.error('Failed to connect to PostgreSQL');
        return false;
      }

      // Initialize the database
      const initSuccessful = await initializeDatabase();
      if (!initSuccessful) {
        logger.error('Failed to initialize PostgreSQL database');
        return false;
      }

      this.isInitialized = true;
      logger.info('PostgreSQL service initialized successfully');
      return true;
    } catch (error) {
      logger.error(`Error initializing PostgreSQL service: ${error.message}`);
      return false;
    }
  }

  /**
   * Create a new transaction
   * @param {Object} transactionData Transaction data
   * @returns {Promise<Object>} Created transaction
   */
  async createTransaction(transactionData) {
    try {
      return await Transaction.create(transactionData);
    } catch (error) {
      logger.error(`Error creating transaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get transaction by ID
   * @param {string} id Transaction ID
   * @returns {Promise<Object>} Transaction
   */
  async getTransactionById(id) {
    try {
      return await Transaction.findByPk(id);
    } catch (error) {
      logger.error(`Error getting transaction by ID: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get transactions by wallet address
   * @param {string} walletAddress Wallet address
   * @param {Object} options Query options (limit, offset, etc.)
   * @returns {Promise<Array>} Transactions
   */
  async getTransactionsByWallet(walletAddress, options = {}) {
    try {
      const { limit = 20, offset = 0, type, status, startDate, endDate } = options;

      const query = {
        where: { walletAddress },
        order: [['createdAt', 'DESC']],
        limit,
        offset
      };

      // Add filters if provided
      if (type) query.where.type = type;
      if (status) query.where.status = status;

      // Add date range if provided
      if (startDate || endDate) {
        query.where.createdAt = {};
        if (startDate) query.where.createdAt[Op.gte] = new Date(startDate);
        if (endDate) query.where.createdAt[Op.lte] = new Date(endDate);
      }

      return await Transaction.findAndCountAll(query);
    } catch (error) {
      logger.error(`Error getting transactions by wallet: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get transactions by token mint
   * @param {string} tokenMint Token mint address
   * @param {Object} options Query options (limit, offset, etc.)
   * @returns {Promise<Array>} Transactions
   */
  async getTransactionsByToken(tokenMint, options = {}) {
    try {
      const { limit = 20, offset = 0, type, status, startDate, endDate } = options;

      const query = {
        where: { tokenMint },
        order: [['createdAt', 'DESC']],
        limit,
        offset
      };

      // Add filters if provided
      if (type) query.where.type = type;
      if (status) query.where.status = status;

      // Add date range if provided
      if (startDate || endDate) {
        query.where.createdAt = {};
        if (startDate) query.where.createdAt[Op.gte] = new Date(startDate);
        if (endDate) query.where.createdAt[Op.lte] = new Date(endDate);
      }

      return await Transaction.findAndCountAll(query);
    } catch (error) {
      logger.error(`Error getting transactions by token: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update transaction status
   * @param {string} id Transaction ID
   * @param {string} status New status
   * @param {Object} additionalData Additional data to update
   * @returns {Promise<Object>} Updated transaction
   */
  async updateTransactionStatus(id, status, additionalData = {}) {
    try {
      const transaction = await Transaction.findByPk(id);
      if (!transaction) {
        throw new Error(`Transaction not found: ${id}`);
      }

      const updateData = { status, ...additionalData };
      await transaction.update(updateData);

      return transaction;
    } catch (error) {
      logger.error(`Error updating transaction status: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a new order
   * @param {Object} orderData Order data
   * @returns {Promise<Object>} Created order
   */
  async createOrder(orderData) {
    try {
      return await Order.create(orderData);
    } catch (error) {
      logger.error(`Error creating order: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get order by ID
   * @param {string} id Order ID
   * @returns {Promise<Object>} Order
   */
  async getOrderById(id) {
    try {
      return await Order.findByPk(id);
    } catch (error) {
      logger.error(`Error getting order by ID: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get orders by wallet address
   * @param {string} walletAddress Wallet address
   * @param {Object} options Query options (limit, offset, etc.)
   * @returns {Promise<Array>} Orders
   */
  async getOrdersByWallet(walletAddress, options = {}) {
    try {
      const { limit = 20, offset = 0, type, status } = options;

      const query = {
        where: { walletAddress },
        order: [['createdAt', 'DESC']],
        limit,
        offset
      };

      // Add filters if provided
      if (type) query.where.type = type;
      if (status) query.where.status = status;

      return await Order.findAndCountAll(query);
    } catch (error) {
      logger.error(`Error getting orders by wallet: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get open orders for a token
   * @param {string} tokenMint Token mint address
   * @param {string} type Order type (buy, sell)
   * @returns {Promise<Array>} Open orders
   */
  async getOpenOrders(tokenMint, type) {
    try {
      const query = {
        where: {
          tokenMint,
          status: {
            [Op.in]: ['open', 'partial']
          }
        },
        order: type === 'buy' ? [['price', 'DESC']] : [['price', 'ASC']]
      };

      if (type) {
        query.where.type = type;
      }

      return await Order.findAll(query);
    } catch (error) {
      logger.error(`Error getting open orders: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update order status
   * @param {string} id Order ID
   * @param {string} status New status
   * @param {Object} additionalData Additional data to update
   * @returns {Promise<Object>} Updated order
   */
  async updateOrderStatus(id, status, additionalData = {}) {
    try {
      const order = await Order.findByPk(id);
      if (!order) {
        throw new Error(`Order not found: ${id}`);
      }

      const updateData = { status, ...additionalData };
      await order.update(updateData);

      return order;
    } catch (error) {
      logger.error(`Error updating order status: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a new trade
   * @param {Object} tradeData Trade data
   * @returns {Promise<Object>} Created trade
   */
  async createTrade(tradeData) {
    try {
      return await Trade.create(tradeData);
    } catch (error) {
      logger.error(`Error creating trade: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get trade by ID
   * @param {string} id Trade ID
   * @returns {Promise<Object>} Trade
   */
  async getTradeById(id) {
    try {
      return await Trade.findByPk(id);
    } catch (error) {
      logger.error(`Error getting trade by ID: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get trades by wallet address (as buyer or seller)
   * @param {string} walletAddress Wallet address
   * @param {Object} options Query options (limit, offset, etc.)
   * @returns {Promise<Array>} Trades
   */
  async getTradesByWallet(walletAddress, options = {}) {
    try {
      const { limit = 20, offset = 0, status } = options;

      const query = {
        where: {
          [Op.or]: [
            { buyerAddress: walletAddress },
            { sellerAddress: walletAddress }
          ]
        },
        order: [['createdAt', 'DESC']],
        limit,
        offset
      };

      // Add status filter if provided
      if (status) query.where.status = status;

      return await Trade.findAndCountAll(query);
    } catch (error) {
      logger.error(`Error getting trades by wallet: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get trades by token mint
   * @param {string} tokenMint Token mint address
   * @param {Object} options Query options (limit, offset, etc.)
   * @returns {Promise<Array>} Trades
   */
  async getTradesByToken(tokenMint, options = {}) {
    try {
      const { limit = 20, offset = 0, status } = options;

      const query = {
        where: { tokenMint },
        order: [['createdAt', 'DESC']],
        limit,
        offset
      };

      // Add status filter if provided
      if (status) query.where.status = status;

      return await Trade.findAndCountAll(query);
    } catch (error) {
      logger.error(`Error getting trades by token: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update trade status
   * @param {string} id Trade ID
   * @param {string} status New status
   * @param {Object} additionalData Additional data to update
   * @returns {Promise<Object>} Updated trade
   */
  async updateTradeStatus(id, status, additionalData = {}) {
    try {
      const trade = await Trade.findByPk(id);
      if (!trade) {
        throw new Error(`Trade not found: ${id}`);
      }

      const updateData = { status, ...additionalData };
      await trade.update(updateData);

      return trade;
    } catch (error) {
      logger.error(`Error updating trade status: ${error.message}`);
      throw error;
    }
  }

  /**
   * Add market data point
   * @param {Object} dataPoint Market data point
   * @returns {Promise<Object>} Created market data point
   */
  async addMarketDataPoint(dataPoint) {
    try {
      // Check if data point already exists
      const existingPoint = await MarketData.findOne({
        where: {
          tokenMint: dataPoint.tokenMint,
          dataType: dataPoint.dataType,
          interval: dataPoint.interval,
          timestamp: dataPoint.timestamp
        }
      });

      // Update if exists, create if not
      if (existingPoint) {
        await existingPoint.update(dataPoint);
        return existingPoint;
      } else {
        return await MarketData.create(dataPoint);
      }
    } catch (error) {
      logger.error(`Error adding market data point: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get market data for a token
   * @param {string} tokenMint Token mint address
   * @param {string} dataType Data type (price, volume, etc.)
   * @param {string} interval Time interval (1m, 5m, etc.)
   * @param {Object} options Query options (limit, startDate, endDate)
   * @returns {Promise<Array>} Market data points
   */
  async getMarketData(tokenMint, dataType, interval, options = {}) {
    try {
      const { limit = 100, startDate, endDate } = options;

      const query = {
        where: {
          tokenMint,
          dataType,
          interval
        },
        order: [['timestamp', 'ASC']],
        limit
      };

      // Add date range if provided
      if (startDate || endDate) {
        query.where.timestamp = {};
        if (startDate) query.where.timestamp[Op.gte] = new Date(startDate);
        if (endDate) query.where.timestamp[Op.lte] = new Date(endDate);
      }

      return await MarketData.findAll(query);
    } catch (error) {
      logger.error(`Error getting market data: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get latest market data for a token
   * @param {string} tokenMint Token mint address
   * @param {string} dataType Data type (price, volume, etc.)
   * @param {string} interval Time interval (1m, 5m, etc.)
   * @returns {Promise<Object>} Latest market data point
   */
  async getLatestMarketData(tokenMint, dataType, interval) {
    try {
      return await MarketData.findOne({
        where: {
          tokenMint,
          dataType,
          interval
        },
        order: [['timestamp', 'DESC']]
      });
    } catch (error) {
      logger.error(`Error getting latest market data: ${error.message}`);
      throw error;
    }
  }

  /**
   * Check if a blockchain transaction has already been processed
   * @param {string} txId Transaction ID (signature)
   * @returns {Promise<boolean>} True if transaction has been processed
   */
  async isTransactionProcessed(txId) {
    try {
      // Check if transaction exists in our processed transactions table
      const transaction = await Transaction.findOne({
        where: { txId }
      });

      return !!transaction;
    } catch (error) {
      logger.error(`Error checking if transaction is processed: ${error.message}`);
      return false; // Assume not processed in case of error
    }
  }

  /**
   * Mark a blockchain transaction as processed
   * @param {string} txId Transaction ID (signature)
   * @param {Object} metadata Transaction metadata
   * @returns {Promise<Object>} Created transaction record
   */
  async markTransactionProcessed(txId, metadata = {}) {
    try {
      // Create a transaction record
      return await Transaction.create({
        id: metadata.id || txId, // Use provided ID or txId as ID
        txId,
        type: metadata.type || 'unknown',
        status: 'completed',
        amount: metadata.amount || 0,
        tokenMint: metadata.tokenMint || null,
        token: metadata.token || 'SOL',
        walletAddress: metadata.sender || null,
        userId: metadata.userId || null,
        metadata: metadata
      });
    } catch (error) {
      logger.error(`Error marking transaction as processed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a user balance record
   * @param {string} userId User ID
   * @returns {Promise<Object>} Created user balance
   */
  async createUserBalance(userId) {
    try {
      // Check if balance already exists
      let balance = await UserBalance.findOne({
        where: { userId }
      });

      // If balance already exists, return it
      if (balance) {
        logger.info(`User balance already exists for user ${userId}`);
        return balance;
      }

      // Create new balance record
      balance = await UserBalance.create({
        userId,
        solBalance: '0',
        tokenBalances: {},
        pendingDeposits: [],
        pendingWithdrawals: []
      });

      logger.info(`Created user balance record for user ${userId}`);

      // Add initial balance transaction for record-keeping
      await this.addBalanceTransaction({
        userId,
        type: 'system',
        token: 'SOL',
        amount: '0',
        txId: `init-${userId}`,
        status: 'completed',
        description: 'Account initialized'
      });

      return balance;
    } catch (error) {
      logger.error(`Error creating user balance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get user balance by user ID
   * @param {string} userId User ID
   * @returns {Promise<Object>} User balance
   */
  async getUserBalance(userId) {
    try {
      let balance = await UserBalance.findOne({
        where: { userId }
      });

      // Create balance if it doesn't exist
      if (!balance) {
        balance = await UserBalance.create({
          userId,
          solBalance: '0',
          tokenBalances: {},
          pendingDeposits: [],
          pendingWithdrawals: []
        });

        logger.info(`Auto-created user balance record for user ${userId}`);

        // Add initial balance transaction for record-keeping
        await this.addBalanceTransaction({
          userId,
          type: 'system',
          token: 'SOL',
          amount: '0',
          txId: `auto-init-${userId}`,
          status: 'completed',
          description: 'Account auto-initialized'
        });
      }

      return balance;
    } catch (error) {
      logger.error(`Error getting user balance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update user SOL balance
   * @param {string} userId User ID
   * @param {string|number} amount Amount to add (positive) or subtract (negative)
   * @returns {Promise<Object>} Updated user balance
   */
  async updateSolBalance(userId, amount) {
    try {
      const balance = await this.getUserBalance(userId);

      // Convert current balance to number
      const currentBalance = parseFloat(balance.solBalance);

      // Convert amount to number
      const amountNum = typeof amount === 'string' ? parseFloat(amount) : amount;

      // Calculate new balance
      const newBalance = currentBalance + amountNum;

      // Ensure balance doesn't go negative
      if (newBalance < 0) {
        throw new Error('Insufficient SOL balance');
      }

      // Update balance
      balance.solBalance = newBalance.toString();
      balance.lastUpdated = new Date();

      await balance.save();

      return balance;
    } catch (error) {
      logger.error(`Error updating SOL balance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update user token balance
   * @param {string} userId User ID
   * @param {string} token Token symbol or mint address
   * @param {string|number} amount Amount to add (positive) or subtract (negative)
   * @returns {Promise<Object>} Updated user balance
   */
  async updateTokenBalance(userId, token, amount) {
    try {
      const balance = await this.getUserBalance(userId);

      // Get current token balances
      const tokenBalances = balance.tokenBalances || {};

      // Get current token balance
      const currentBalance = parseFloat(tokenBalances[token] || '0');

      // Convert amount to number
      const amountNum = typeof amount === 'string' ? parseFloat(amount) : amount;

      // Calculate new balance
      const newBalance = currentBalance + amountNum;

      // Ensure balance doesn't go negative
      if (newBalance < 0) {
        throw new Error(`Insufficient ${token} balance`);
      }

      // Update token balance
      tokenBalances[token] = newBalance.toString();
      balance.tokenBalances = tokenBalances;
      balance.lastUpdated = new Date();

      await balance.save();

      return balance;
    } catch (error) {
      logger.error(`Error updating token balance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Add balance transaction
   * @param {Object} transactionData Transaction data
   * @returns {Promise<Object>} Created transaction
   */
  async addBalanceTransaction(transactionData) {
    try {
      return await BalanceTransaction.create(transactionData);
    } catch (error) {
      logger.error(`Error adding balance transaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get balance transactions by user ID
   * @param {string} userId User ID
   * @param {Object} options Query options (limit, offset, type, token, etc.)
   * @returns {Promise<Object>} Transactions with count
   */
  async getBalanceTransactions(userId, options = {}) {
    try {
      const { limit = 20, offset = 0, type, token, startDate, endDate, status } = options;

      const query = {
        where: { userId },
        order: [['createdAt', 'DESC']],
        limit,
        offset
      };

      // Add filters if provided
      if (type) query.where.type = type;
      if (token) query.where.token = token;
      if (status) query.where.status = status;

      // Add date range if provided
      if (startDate || endDate) {
        query.where.createdAt = {};
        if (startDate) query.where.createdAt[Op.gte] = new Date(startDate);
        if (endDate) query.where.createdAt[Op.lte] = new Date(endDate);
      }

      return await BalanceTransaction.findAndCountAll(query);
    } catch (error) {
      logger.error(`Error getting balance transactions: ${error.message}`);
      throw error;
    }
  }

  /**
   * Add pending deposit
   * @param {string} userId User ID
   * @param {Object} depositData Deposit data
   * @returns {Promise<Object>} Updated user balance
   */
  async addPendingDeposit(userId, depositData) {
    try {
      const balance = await this.getUserBalance(userId);

      // Get current pending deposits
      const pendingDeposits = balance.pendingDeposits || [];

      // Add new pending deposit
      pendingDeposits.push({
        ...depositData,
        timestamp: new Date(),
        status: 'pending'
      });

      // Update balance
      balance.pendingDeposits = pendingDeposits;
      balance.lastUpdated = new Date();

      await balance.save();

      return balance;
    } catch (error) {
      logger.error(`Error adding pending deposit: ${error.message}`);
      throw error;
    }
  }

  /**
   * Confirm pending deposit
   * @param {string} userId User ID
   * @param {string} txId Transaction ID
   * @returns {Promise<Object>} Updated user balance
   */
  async confirmDeposit(userId, txId) {
    try {
      const balance = await this.getUserBalance(userId);

      // Get current pending deposits
      const pendingDeposits = balance.pendingDeposits || [];

      // Find the pending deposit
      const depositIndex = pendingDeposits.findIndex(d => d.txId === txId);

      if (depositIndex === -1) {
        throw new Error('Pending deposit not found');
      }

      const deposit = pendingDeposits[depositIndex];

      // Update deposit status
      deposit.status = 'confirmed';
      pendingDeposits[depositIndex] = deposit;

      // Update balance
      balance.pendingDeposits = pendingDeposits;

      // Add to balance
      if (deposit.token === 'SOL') {
        const currentBalance = parseFloat(balance.solBalance);
        balance.solBalance = (currentBalance + parseFloat(deposit.amount)).toString();
      } else {
        const tokenBalances = balance.tokenBalances || {};
        const currentBalance = parseFloat(tokenBalances[deposit.token] || '0');
        tokenBalances[deposit.token] = (currentBalance + parseFloat(deposit.amount)).toString();
        balance.tokenBalances = tokenBalances;
      }

      balance.lastUpdated = new Date();

      await balance.save();

      // Add balance transaction
      await this.addBalanceTransaction({
        userId,
        type: 'deposit',
        token: deposit.token,
        amount: deposit.amount,
        txId,
        status: 'completed',
        description: 'Deposit confirmed'
      });

      return balance;
    } catch (error) {
      logger.error(`Error confirming deposit: ${error.message}`);
      throw error;
    }
  }

  /**
   * Add pending withdrawal
   * @param {string} userId User ID
   * @param {Object} withdrawalData Withdrawal data
   * @returns {Promise<Object>} Updated user balance
   */
  async addPendingWithdrawal(userId, withdrawalData) {
    try {
      const balance = await this.getUserBalance(userId);

      // Check if user has enough balance
      if (withdrawalData.token === 'SOL') {
        const currentBalance = parseFloat(balance.solBalance);
        if (currentBalance < parseFloat(withdrawalData.amount)) {
          throw new Error('Insufficient SOL balance');
        }

        // Reduce balance
        balance.solBalance = (currentBalance - parseFloat(withdrawalData.amount)).toString();
      } else {
        const tokenBalances = balance.tokenBalances || {};
        const currentBalance = parseFloat(tokenBalances[withdrawalData.token] || '0');

        if (currentBalance < parseFloat(withdrawalData.amount)) {
          throw new Error(`Insufficient ${withdrawalData.token} balance`);
        }

        // Reduce balance
        tokenBalances[withdrawalData.token] = (currentBalance - parseFloat(withdrawalData.amount)).toString();
        balance.tokenBalances = tokenBalances;
      }

      // Get current pending withdrawals
      const pendingWithdrawals = balance.pendingWithdrawals || [];

      // Add new pending withdrawal
      pendingWithdrawals.push({
        ...withdrawalData,
        timestamp: new Date(),
        status: 'pending'
      });

      // Update balance
      balance.pendingWithdrawals = pendingWithdrawals;
      balance.lastUpdated = new Date();

      await balance.save();

      // Add balance transaction
      await this.addBalanceTransaction({
        userId,
        type: 'withdrawal',
        token: withdrawalData.token,
        amount: `-${withdrawalData.amount}`,
        txId: withdrawalData.txId,
        status: 'pending',
        description: 'Withdrawal requested',
        metadata: {
          destinationAddress: withdrawalData.destinationAddress
        }
      });

      return balance;
    } catch (error) {
      logger.error(`Error adding pending withdrawal: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update withdrawal status
   * @param {string} userId User ID
   * @param {string} txId Transaction ID
   * @param {string} status New status (completed, failed)
   * @param {Object} additionalData Additional data to store with the withdrawal
   * @returns {Promise<Object>} Updated user balance
   */
  async updateWithdrawalStatus(userId, txId, status, additionalData = {}) {
    try {
      const balance = await this.getUserBalance(userId);

      // Get current pending withdrawals
      const pendingWithdrawals = balance.pendingWithdrawals || [];

      // Find the pending withdrawal
      const withdrawalIndex = pendingWithdrawals.findIndex(w => w.txId === txId);

      if (withdrawalIndex === -1) {
        throw new Error('Pending withdrawal not found');
      }

      const withdrawal = pendingWithdrawals[withdrawalIndex];

      // Update withdrawal status
      withdrawal.status = status;

      // Add additional data
      if (additionalData.blockchainTxId) {
        withdrawal.blockchainTxId = additionalData.blockchainTxId;
      }

      // Add any other additional data
      Object.keys(additionalData).forEach(key => {
        if (key !== 'blockchainTxId') {
          withdrawal[key] = additionalData[key];
        }
      });

      withdrawal.updatedAt = new Date();
      pendingWithdrawals[withdrawalIndex] = withdrawal;

      // Update balance
      balance.pendingWithdrawals = pendingWithdrawals;

      // If failed, refund the amount
      if (status === 'failed') {
        if (withdrawal.token === 'SOL') {
          const currentBalance = parseFloat(balance.solBalance);
          balance.solBalance = (currentBalance + parseFloat(withdrawal.amount)).toString();
        } else {
          const tokenBalances = balance.tokenBalances || {};
          const currentBalance = parseFloat(tokenBalances[withdrawal.token] || '0');
          tokenBalances[withdrawal.token] = (currentBalance + parseFloat(withdrawal.amount)).toString();
          balance.tokenBalances = tokenBalances;
        }

        // Add refund transaction
        await this.addBalanceTransaction({
          userId,
          type: 'refund',
          token: withdrawal.token,
          amount: withdrawal.amount,
          txId: `refund-${txId}`,
          relatedTxId: txId,
          status: 'completed',
          description: 'Withdrawal failed - amount refunded'
        });
      }

      // Update withdrawal transaction status with additional data
      const updateData = { status };

      if (additionalData.blockchainTxId) {
        updateData.metadata = sequelize.fn(
          'jsonb_set',
          sequelize.col('metadata'),
          '{blockchainTxId}',
          sequelize.literal(`'"${additionalData.blockchainTxId}"'`)
        );
      }

      await BalanceTransaction.update(
        updateData,
        { where: { userId, txId, type: 'withdrawal' } }
      );

      balance.lastUpdated = new Date();

      await balance.save();

      return balance;
    } catch (error) {
      logger.error(`Error updating withdrawal status: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update balance transaction status
   * @param {string} txId Transaction ID
   * @param {string} status New status
   * @param {Object} additionalData Additional data to update
   * @returns {Promise<boolean>} Success status
   */
  async updateBalanceTransactionStatus(txId, status, additionalData = {}) {
    try {
      const updateData = { status };

      // Add additional data to metadata
      if (Object.keys(additionalData).length > 0) {
        // For each key in additionalData, update the metadata
        for (const [key, value] of Object.entries(additionalData)) {
          updateData.metadata = sequelize.fn(
            'jsonb_set',
            sequelize.col('metadata'),
            `{${key}}`,
            sequelize.literal(`'${JSON.stringify(value)}'`)
          );

          // Execute the update for each key
          await BalanceTransaction.update(
            { metadata: updateData.metadata },
            { where: { txId } }
          );
        }
      }

      // Update the status
      await BalanceTransaction.update(
        { status },
        { where: { txId } }
      );

      return true;
    } catch (error) {
      logger.error(`Error updating balance transaction status: ${error.message}`);
      return false;
    }
  }
}

// Create a singleton instance
const postgresqlService = new PostgresqlService();

module.exports = postgresqlService;
