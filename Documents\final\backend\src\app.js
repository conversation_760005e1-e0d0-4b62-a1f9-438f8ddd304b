require('dotenv').config();
const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');
const session = require('express-session');
const cookieParser = require('cookie-parser');
const RedisStore = require('connect-redis').default;
const { database } = require('./config/database');
const { redis } = require('./config/redis');
const tradingController = require('./controllers/tradingController');
const solanaService = require('./services/solanaService');
const depositMonitorService = require('./services/depositMonitorService');
const logger = require('./utils/logger');

// Import routes
const authRoutes = require('./routes/authRoutes');
const otpRoutes = require('./routes/otpRoutes');
const tokenRoutes = require('./routes/tokenRoutes');
const imageRoutes = require('./routes/imageRoutes');
const tradingRoutes = require('./routes/tradingRoutes');
const statsRoutes = require('./routes/statsRoutes');
const commentRoutes = require('./routes/commentRoutes');
const userRoutes = require('./routes/userRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const assetRoutes = require('./routes/assetRoutes');
const transactionRoutes = require('./routes/transactionRoutes');
const tokenHoldingsRoutes = require('./routes/tokenHoldingsRoutes');
const sessionRoutes = require('./routes/sessionRoutes');
const bondingCurveRoutes = require('./routes/bondingCurveRoutes');
const feeRoutes = require('./routes/feeRoutes');
const walletRoutes = require('./routes/walletRoutes');
const balanceRoutes = require('./routes/balanceRoutes');
const healthRoutes = require('./routes/healthRoutes');
const graduationRoutes = require('./routes/graduationRoutes');

// Initialize express app
const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: process.env.NODE_ENV === 'production' ? undefined : false,
}));

// Compress responses
app.use(compression());

// CORS configuration
const corsOptions = {
  origin: process.env.NODE_ENV === 'production'
    ? process.env.FRONTEND_URL
    : ['http://localhost:3000', 'http://localhost:8080'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'X-Requested-With'],
  exposedHeaders: ['Set-Cookie', 'Date', 'ETag'],
  credentials: true, // Important: This allows cookies to be sent cross-origin
  maxAge: 86400 // 24 hours
};
app.use(cors(corsOptions));

// Rate limit middleware - 100 requests per 15 minutes
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  skip: (req) => process.env.NODE_ENV !== 'production' // Skip rate limiting in development
});

// Apply rate limiting to API routes
app.use('/api', apiLimiter);

// Session configuration with Redis
app.use(session({
  store: new RedisStore({ client: redis }),
  secret: process.env.SESSION_SECRET || 'developmentsecret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Parse cookies
app.use(cookieParser(process.env.COOKIE_SECRET || 'developmentsecret'));

// Parse JSON request body
app.use(express.json());

// Parse URL-encoded request body
app.use(express.urlencoded({ extended: true }));

// Initialize databases and services on startup
(async () => {
  try {
    // Initialize databases
    await database.initializeDatabases();
    logger.info('All databases initialized successfully');

    // Initialize Solana service
    try {
      await solanaService.initialize();
      logger.info('Solana service initialized successfully');

      // Start deposit monitor service (check every 2 minutes)
      depositMonitorService.start(120000);
      logger.info('Deposit monitor service started');
    } catch (solanaError) {
      logger.error(`Solana service initialization error: ${solanaError.message}`);
      // Continue running the server even if Solana service failed to initialize
    }
  } catch (error) {
    logger.error(`Database initialization error: ${error.message}`);
    // Continue running the server even if some databases failed to initialize
  }
})();

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Mount routes
app.use('/api/auth', authRoutes);
app.use('/api/otp', otpRoutes);
app.use('/api/tokens', tokenRoutes);
app.use('/api/images', imageRoutes);
app.use('/api/trading', tradingRoutes);
app.use('/api/stats', statsRoutes);
app.use('/api/comments', commentRoutes);
app.use('/api/user', userRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/assets', assetRoutes);
app.use('/api/transactions', transactionRoutes);
app.use('/api/holdings', tokenHoldingsRoutes);
app.use('/api/sessions', sessionRoutes);
app.use('/api/bonding-curve', bondingCurveRoutes);
app.use('/api/fees', feeRoutes);
app.use('/api/wallets', walletRoutes);
app.use('/api/balance', balanceRoutes);
app.use('/api/graduation', graduationRoutes);

// Mount health check routes
app.use('/health', healthRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'An unexpected error occurred',
    error: process.env.NODE_ENV === 'development' ? err : {}
  });
});

// Create HTTP server
const server = http.createServer(app);

// Create WebSocket server attached to the HTTP server
const wss = new WebSocket.Server({ server });

// Handle WebSocket connections
wss.on('connection', tradingController.handleConnection);

// Store WebSocket server for use in other modules
app.set('wss', wss);

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM signal received: closing HTTP server and shutting down services');

  // Stop the deposit monitor service
  if (depositMonitorService.isRunning) {
    depositMonitorService.stop();
    logger.info('Deposit monitor service stopped');
  }

  // Stop accepting new connections
  server.close(async () => {
    logger.info('HTTP server closed');
    // Close database connections
    await database.shutdownDatabases();
    logger.info('Databases shut down');
    process.exit(0);
  });
});

module.exports = server;