/**
 * Server Monitor Script
 * 
 * This script monitors the backend server and automatically restarts it if it crashes.
 * It also logs server output and errors to help diagnose issues.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const MAX_RESTARTS = 10;
const RESTART_DELAY = 5000; // 5 seconds
const LOG_DIR = path.join(__dirname, 'logs');
const SERVER_SCRIPT = path.join(__dirname, 'src', 'index.js');

// Ensure log directory exists
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Create log files
const date = new Date().toISOString().replace(/:/g, '-').split('.')[0];
const stdoutLog = fs.createWriteStream(path.join(LOG_DIR, `server-stdout-${date}.log`), { flags: 'a' });
const stderrLog = fs.createWriteStream(path.join(LOG_DIR, `server-stderr-${date}.log`), { flags: 'a' });
const monitorLog = fs.createWriteStream(path.join(LOG_DIR, `monitor-${date}.log`), { flags: 'a' });

// Log function for the monitor
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  console.log(logMessage.trim());
  monitorLog.write(logMessage);
}

// Start the server
let serverProcess = null;
let restartCount = 0;
let intentionalExit = false;

function startServer() {
  log(`Starting server (attempt ${restartCount + 1}/${MAX_RESTARTS})...`);
  
  // Start the server process with TLS options
  serverProcess = spawn('node', ['--tls-min-v1.0', '--tls-max-v1.2', SERVER_SCRIPT], {
    stdio: 'pipe',
    env: process.env
  });
  
  const pid = serverProcess.pid;
  log(`Server started with PID: ${pid}`);
  
  // Handle server output
  serverProcess.stdout.on('data', (data) => {
    const output = data.toString();
    process.stdout.write(output);
    stdoutLog.write(output);
    
    // Check for specific error messages that might indicate a problem
    if (output.includes('Error:') || output.includes('error:') || output.includes('ECONNREFUSED')) {
      log(`Detected error in server output: ${output.trim()}`);
    }
  });
  
  serverProcess.stderr.on('data', (data) => {
    const output = data.toString();
    process.stderr.write(output);
    stderrLog.write(output);
    log(`Server error: ${output.trim()}`);
  });
  
  // Handle server exit
  serverProcess.on('exit', (code, signal) => {
    const exitReason = signal ? `signal ${signal}` : `exit code ${code}`;
    log(`Server process exited with ${exitReason}`);
    
    serverProcess = null;
    
    if (intentionalExit) {
      log('Server was intentionally stopped. Not restarting.');
      process.exit(0);
    } else if (restartCount < MAX_RESTARTS) {
      restartCount++;
      log(`Restarting server in ${RESTART_DELAY / 1000} seconds...`);
      
      setTimeout(startServer, RESTART_DELAY);
    } else {
      log(`Maximum restart attempts (${MAX_RESTARTS}) reached. Giving up.`);
      process.exit(1);
    }
  });
  
  // Handle server errors
  serverProcess.on('error', (err) => {
    log(`Failed to start server: ${err.message}`);
    
    if (restartCount < MAX_RESTARTS) {
      restartCount++;
      log(`Restarting server in ${RESTART_DELAY / 1000} seconds...`);
      
      setTimeout(startServer, RESTART_DELAY);
    } else {
      log(`Maximum restart attempts (${MAX_RESTARTS}) reached. Giving up.`);
      process.exit(1);
    }
  });
}

// Handle process signals
process.on('SIGINT', () => {
  log('Received SIGINT signal. Shutting down server...');
  intentionalExit = true;
  
  if (serverProcess) {
    serverProcess.kill('SIGINT');
  } else {
    process.exit(0);
  }
});

process.on('SIGTERM', () => {
  log('Received SIGTERM signal. Shutting down server...');
  intentionalExit = true;
  
  if (serverProcess) {
    serverProcess.kill('SIGTERM');
  } else {
    process.exit(0);
  }
});

// Handle uncaught exceptions in the monitor
process.on('uncaughtException', (err) => {
  log(`Monitor uncaught exception: ${err.message}`);
  log(err.stack);
  
  if (serverProcess) {
    intentionalExit = true;
    serverProcess.kill('SIGTERM');
  }
  
  process.exit(1);
});

// Start the server for the first time
startServer();

log('Server monitor started. Press Ctrl+C to stop.');
