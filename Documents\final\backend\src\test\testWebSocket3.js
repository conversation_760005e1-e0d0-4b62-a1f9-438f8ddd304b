const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid');

// Create a WebSocket connection to the C++ Order Matching Engine
const ws = new WebSocket('ws://localhost:9002');

// Handle connection to the C++ Order Matching Engine
ws.on('open', () => {
  console.log('Connected to C++ Order Matching Engine on port 9002');
  
  // Send a properly formatted JSON message
  setTimeout(() => {
    const message = JSON.stringify({
      type: 'getOrderBook',
      requestId: uuidv4(),
      symbol: 'SOL/USDC',
      timestamp: Date.now()
    });
    ws.send(message);
    console.log('Sent JSON message to C++ Order Matching Engine:', message);
  }, 1000);
});

// Handle messages from the C++ Order Matching Engine
ws.on('message', (data) => {
  console.log('Received message from C++ Order Matching Engine:', data.toString());
});

// Handle C++ Order Matching Engine disconnection
ws.on('close', (code, reason) => {
  console.log(`Disconnected from C++ Order Matching Engine: Code ${code}, Reason: ${reason || 'No reason provided'}`);
});

// Handle C++ Order Matching Engine errors
ws.on('error', (err) => {
  console.error('C++ Order Matching Engine error:', err.message);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('Shutting down test client');
  ws.close();
  process.exit(0);
});
