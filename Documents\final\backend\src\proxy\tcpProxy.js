const net = require('net');
const { v4: uuidv4 } = require('uuid');

// Create a TCP server
const server = net.createServer((client) => {
  console.log('Client connected');
  
  // Handle client data
  client.on('data', (data) => {
    console.log('Received data from client:', data.toString());
    
    try {
      // Try to parse the data as JSON
      const message = JSON.parse(data.toString());
      
      // Handle different message types
      switch (message.type) {
        case 'getOrderBook':
          handleGetOrderBook(client, message);
          break;
          
        case 'getMarketData':
          handleGetMarketData(client, message);
          break;
          
        case 'submitOrder':
          handleSubmitOrder(client, message);
          break;
          
        case 'cancelOrder':
          handleCancelOrder(client, message);
          break;
          
        case 'getUserOrders':
          handleGetUserOrders(client, message);
          break;
          
        case 'getAvailableTokens':
          handleGetAvailableTokens(client, message);
          break;
          
        case 'ping':
          handlePing(client, message);
          break;
          
        default:
          sendErrorResponse(client, message.requestId, `Unsupported message type: ${message.type}`);
      }
    } catch (error) {
      console.error('Error handling client data:', error.message);
      // Send an error response
      sendErrorResponse(client, uuidv4(), 'Invalid JSON message');
    }
  });
  
  // Handle client disconnection
  client.on('close', () => {
    console.log('Client disconnected');
  });
  
  // Handle client errors
  client.on('error', (error) => {
    console.error('Client error:', error.message);
  });
});

// Handle getOrderBook message
function handleGetOrderBook(client, message) {
  console.log(`Handling getOrderBook for ${message.symbol}`);
  
  // Get the mock order book for the symbol
  const orderBook = getMockOrderBook(message.symbol);
  
  // Send the response
  sendResponse(client, message.requestId, {
    success: true,
    orderBook
  });
}

// Handle getMarketData message
function handleGetMarketData(client, message) {
  console.log(`Handling getMarketData for ${message.symbol}`);
  
  // Get the mock market data for the symbol
  const marketData = getMockMarketData(message.symbol);
  
  // Send the response
  sendResponse(client, message.requestId, {
    success: true,
    marketData
  });
}

// Handle submitOrder message
function handleSubmitOrder(client, message) {
  console.log(`Handling submitOrder for ${message.order.symbol}`);
  
  // Generate a unique order ID
  const orderId = uuidv4();
  
  // Send the response
  sendResponse(client, message.requestId, {
    success: true,
    orderId,
    trades: []
  });
}

// Handle cancelOrder message
function handleCancelOrder(client, message) {
  console.log(`Handling cancelOrder for ${message.orderId}`);
  
  // Send the response
  sendResponse(client, message.requestId, {
    success: true
  });
}

// Handle getUserOrders message
function handleGetUserOrders(client, message) {
  console.log(`Handling getUserOrders for ${message.walletAddress}`);
  
  // Send the response
  sendResponse(client, message.requestId, {
    success: true,
    orders: []
  });
}

// Handle getAvailableTokens message
function handleGetAvailableTokens(client, message) {
  console.log('Handling getAvailableTokens');
  
  // Send the response
  sendResponse(client, message.requestId, {
    success: true,
    tokens: getMockTokens()
  });
}

// Handle ping message
function handlePing(client, message) {
  console.log('Handling ping');
  
  // Send the response
  sendResponse(client, message.requestId, {
    type: 'pong',
    timestamp: Date.now()
  });
}

// Send a response to the client
function sendResponse(client, requestId, response) {
  // Add the requestId to the response
  response.requestId = requestId;
  
  // Convert the response to JSON
  const jsonStr = JSON.stringify(response);
  
  // Send the response
  client.write(jsonStr);
  console.log(`Sent response for request ${requestId}`);
}

// Send an error response to the client
function sendErrorResponse(client, requestId, errorMessage) {
  sendResponse(client, requestId, {
    success: false,
    error: errorMessage
  });
}

// Get mock order book data
function getMockOrderBook(symbol) {
  if (symbol === 'SOL/USDC') {
    return {
      bids: [
        { price: 99.5, size: 10.5 },
        { price: 99.0, size: 20.3 },
        { price: 98.5, size: 15.7 },
        { price: 98.0, size: 30.2 },
        { price: 97.5, size: 25.8 }
      ],
      asks: [
        { price: 100.5, size: 15.2 },
        { price: 101.0, size: 25.4 },
        { price: 101.5, size: 10.8 },
        { price: 102.0, size: 20.1 },
        { price: 102.5, size: 30.5 }
      ]
    };
  } else if (symbol === 'ETH/USDC') {
    return {
      bids: [
        { price: 3295.5, size: 1.5 },
        { price: 3290.0, size: 2.3 },
        { price: 3285.5, size: 3.7 },
        { price: 3280.0, size: 2.2 },
        { price: 3275.5, size: 1.8 }
      ],
      asks: [
        { price: 3305.5, size: 1.2 },
        { price: 3310.0, size: 2.4 },
        { price: 3315.5, size: 1.8 },
        { price: 3320.0, size: 3.1 },
        { price: 3325.5, size: 2.5 }
      ]
    };
  } else {
    return {
      bids: [],
      asks: []
    };
  }
}

// Get mock market data
function getMockMarketData(symbol) {
  if (symbol === 'SOL/USDC') {
    return {
      symbol: 'SOL/USDC',
      lastPrice: 100.0,
      volume24h: 15000,
      high24h: 105.5,
      low24h: 95.2,
      change24h: 2.5,
      timestamp: Date.now()
    };
  } else if (symbol === 'ETH/USDC') {
    return {
      symbol: 'ETH/USDC',
      lastPrice: 3300.0,
      volume24h: 5000,
      high24h: 3350.5,
      low24h: 3250.2,
      change24h: -1.2,
      timestamp: Date.now()
    };
  } else {
    return {
      symbol,
      lastPrice: 0,
      volume24h: 0,
      high24h: 0,
      low24h: 0,
      change24h: 0,
      timestamp: Date.now()
    };
  }
}

// Get mock tokens
function getMockTokens() {
  return [
    {
      symbol: 'SOL',
      name: 'Solana',
      decimals: 9,
      minOrderSize: 0.1,
      status: 'active',
      price: 100.0,
      priceChange24h: 2.5,
      volume24h: 15000
    },
    {
      symbol: 'ETH',
      name: 'Ethereum',
      decimals: 18,
      minOrderSize: 0.01,
      status: 'active',
      price: 3300.0,
      priceChange24h: -1.2,
      volume24h: 5000
    }
  ];
}

// Start the server
const port = 9002;
server.listen(port, () => {
  console.log(`TCP server running on port ${port}`);
});
