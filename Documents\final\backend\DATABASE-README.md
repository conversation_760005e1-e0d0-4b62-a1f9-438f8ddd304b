# WB-Swap Database Architecture

This document explains the simplified database architecture for the WB-Swap platform.

## Overview

The platform uses three main databases:

1. **MongoDB** - For token metadata, user data, and flexible schema content
2. **PostgreSQL** - For trading, transaction data, and user balances
3. **Redis** - For sessions, caching, and real-time features

## Database Roles

### MongoDB
- User accounts and profiles
- Token metadata and information
- Comments and social interactions
- Notifications
- Any data with flexible schema requirements

### PostgreSQL
- Financial transactions
- Order book entries
- Trade history
- User balances
- Market data (price history, volume)

### Redis
- Session management
- Real-time data caching
- Order matching queue
- Rate limiting

## Connection Configuration

Configure your database connections in the `.env` file:

```
# MongoDB
MONGODB_URI=mongodb+srv://username:<EMAIL>/database

# PostgreSQL
POSTGRESQL_URI=postgres://username:<EMAIL>:5432/database

# Redis
UPSTASH_REDIS_REST_URL=https://your-redis-instance.upstash.io
UPSTASH_REDIS_REST_TOKEN=your_redis_token
```

## Database Initialization

The database connections are initialized in `src/config/database.js`. This file:

1. Loads the database configuration
2. Establishes connections to MongoDB, PostgreSQL, and Redis
3. Provides health check functions
4. Handles graceful shutdown

## Best Practices

1. **Data Access**:
   - Use MongoDB for user and token data
   - Use PostgreSQL for trading and transaction data
   - Use Redis for real-time and cached data

2. **Transactions**:
   - Use PostgreSQL transactions for financial operations
   - Ensure ACID compliance for critical operations

3. **Caching**:
   - Cache frequently accessed data in Redis
   - Implement cache invalidation strategies

## Troubleshooting

If you encounter database connection issues:

1. Check your `.env` file for correct connection strings
2. Ensure the database servers are running and accessible
3. Check firewall settings and network connectivity
4. Look for connection errors in the application logs

## Development vs. Production

In development mode, the application can fall back to mock data if database connections fail. In production mode, database failures will cause the application to exit.

To enable mock fallbacks in development:

```
# In .env.development
USE_MOCK_DATABASE=true
```
