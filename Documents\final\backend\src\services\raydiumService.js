const { Connection, PublicKey } = require('@solana/web3.js');
const { Market } = require('@project-serum/serum');
const axios = require('axios');

class RaydiumService {
  constructor() {
    this.connection = new Connection(process.env.SOLANA_RPC_URL || 'https://api.devnet.solana.com');
    this.raydiumApiUrl = 'https://api.raydium.io/v2';
  }

  async getHotTokens(limit = 10) {
    try {
      const response = await axios.get(`${this.raydiumApiUrl}/main/pairs`);
      const pairs = response.data.data;

      // Sort by volume and get top tokens
      const hotTokens = pairs
        .sort((a, b) => b.volume24h - a.volume24h)
        .slice(0, limit)
        .map(pair => ({
          name: pair.name,
          symbol: pair.symbol,
          mintAddress: pair.mint,
          price: pair.price,
          volume24h: pair.volume24h,
          priceChange24h: pair.priceChange24h,
          liquidity: pair.liquidity
        }));

      return hotTokens;
    } catch (error) {
      console.error('Error fetching hot tokens from Raydium:', error);
      throw error;
    }
  }

  async getTokenPrice(mintAddress) {
    try {
      const response = await axios.get(`${this.raydiumApiUrl}/main/price?tokens=${mintAddress}`);
      const price = response.data.data[mintAddress];

      if (!price) {
        throw new Error('Token price not found');
      }

      return {
        price,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Error fetching token price from Raydium:', error);
      throw error;
    }
  }

  async getTokenInfo(mintAddress) {
    try {
      const response = await axios.get(`${this.raydiumApiUrl}/main/token/${mintAddress}`);
      const tokenInfo = response.data.data;

      return {
        name: tokenInfo.name,
        symbol: tokenInfo.symbol,
        decimals: tokenInfo.decimals,
        totalSupply: tokenInfo.totalSupply,
        holders: tokenInfo.holders,
        marketCap: tokenInfo.marketCap,
        volume24h: tokenInfo.volume24h,
        priceChange24h: tokenInfo.priceChange24h
      };
    } catch (error) {
      console.error('Error fetching token info from Raydium:', error);
      throw error;
    }
  }

  async getTokenChartData(mintAddress, timeframe = '24h') {
    try {
      const response = await axios.get(
        `${this.raydiumApiUrl}/main/chart?token=${mintAddress}&timeframe=${timeframe}`
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching token chart data from Raydium:', error);
      throw error;
    }
  }

  async getTokenLiquidity(mintAddress) {
    try {
      const response = await axios.get(`${this.raydiumApiUrl}/main/liquidity/${mintAddress}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching token liquidity from Raydium:', error);
      throw error;
    }
  }
}

module.exports = new RaydiumService();