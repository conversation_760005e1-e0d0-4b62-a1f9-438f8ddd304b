{"version": 3, "file": "pairing.d.ts", "sourceRoot": "", "sources": ["../../../src/core/pairing.ts"], "names": [], "mappings": ";AAAA,OAAO,EACL,aAAa,EACb,cAAc,EACd,eAAe,EACf,aAAa,EACb,YAAY,EACb,MAAM,8BAA8B,CAAC;AACtC,OAAO,YAAY,MAAM,QAAQ,CAAC;AAElC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,SAAS,CAAC;AAEjC,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAE/C,MAAM,CAAC,OAAO,WAAW,YAAY,CAAC;IACpC,UAAU,MAAM;QACd,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;QACf,KAAK,EAAE,YAAY,CAAC,eAAe,CAAC;QACpC,MAAM,EAAE,OAAO,CAAC;QAChB,YAAY,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC;QAClC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;KACpB;CACF;AAED,MAAM,CAAC,OAAO,WAAW,mBAAmB,CAAC;IAE3C,KAAK,eAAe,GAAG,IAAI,GAAG,aAAa,CAAC;IAE5C,KAAK,QAAQ,GAAG,kBAAkB,GAAG,gBAAgB,CAAC;IAEtD,KAAK,KAAK,GAAG,aAAa,CAAC;IAI3B,UAAU,aAAa;QACrB,gBAAgB,EAAE;YAChB,IAAI,EAAE,MAAM,CAAC;YACb,OAAO,EAAE,MAAM,CAAC;SACjB,CAAC;QACF,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KACzC;IAGD,UAAU,OAAO;QACf,gBAAgB,EAAE,IAAI,CAAC;QACvB,cAAc,EAAE,IAAI,CAAC;KACtB;IAGD,UAAU,aAAa,CAAC,CAAC,SAAS,cAAc,GAAG,eAAe;QAChE,KAAK,EAAE,MAAM,CAAC;QACd,OAAO,EAAE,CAAC,CAAC;KACZ;CACF;AAED,oBAAY,aAAa,GAAG,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;AAEhE,8BAAsB,QAAQ;IAMT,MAAM,EAAE,MAAM;IAAS,IAAI,EAAE,KAAK;IALrD,SAAgB,IAAI,EAAE,MAAM,CAAC;IAC7B,kBAAyB,OAAO,EAAE,MAAM,CAAC;IACzC,SAAgB,MAAM,EAAE,YAAY,CAAC;IACrC,SAAgB,QAAQ,EAAE,aAAa,CAAC;gBAErB,MAAM,EAAE,MAAM,EAAS,IAAI,EAAE,KAAK;aAErC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;aAErB,IAAI,CAAC,MAAM,EAAE;QAC3B,GAAG,EAAE,MAAM,CAAC;QACZ,eAAe,CAAC,EAAE,OAAO,CAAC;KAC3B,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;aAGhB,MAAM,CAAC,MAAM,CAAC,EAAE;QAC9B,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;QACnB,aAAa,CAAC,EAAE,YAAY,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;KAChE,GAAG,OAAO,CAAC;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,GAAG,EAAE,MAAM,CAAA;KAAE,CAAC;aAG3B,QAAQ,CAAC,MAAM,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,GAAG,OAAO,CAAC,IAAI,CAAC;aAGlD,QAAQ,CAAC,MAAM,EAAE;QAAE,OAAO,EAAE,MAAM,EAAE,CAAA;KAAE,GAAG,IAAI;aAG7C,YAAY,CAAC,MAAM,EAAE;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE,GAAG,OAAO,CAAC,IAAI,CAAC;aAGtE,cAAc,CAAC,MAAM,EAAE;QACrC,KAAK,EAAE,MAAM,CAAC;QACd,QAAQ,EAAE,SAAS,CAAC,QAAQ,CAAC;KAC9B,GAAG,OAAO,CAAC,IAAI,CAAC;aAGD,WAAW,IAAI,YAAY,CAAC,MAAM,EAAE;aAGpC,IAAI,CAAC,MAAM,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,GAAG,OAAO,CAAC,IAAI,CAAC;aAG9C,UAAU,CAAC,MAAM,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,GAAG,OAAO,CAAC,IAAI,CAAC;aAEpD,oBAAoB,CAAC,OAAO,EAAE,YAAY,CAAC,MAAM,GAAG,MAAM;CAC3E;AAED,MAAM,WAAW,eAAe;IAC9B,WAAW,CAAC,CAAC,SAAS,mBAAmB,CAAC,QAAQ,EAChD,KAAK,EAAE,MAAM,EACb,MAAM,EAAE,CAAC,EACT,MAAM,EAAE,mBAAmB,CAAC,aAAa,CAAC,CAAC,CAAC,GAC3C,OAAO,CAAC,MAAM,CAAC,CAAC;IAEnB,UAAU,CAAC,CAAC,SAAS,mBAAmB,CAAC,QAAQ,EAC/C,EAAE,EAAE,MAAM,EACV,KAAK,EAAE,MAAM,EACb,MAAM,EAAE,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,GACrC,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,mBAAmB,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEtF,mBAAmB,CAAC,KAAK,EAAE,mBAAmB,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;IAEpF,oBAAoB,CAAC,KAAK,EAAE,mBAAmB,CAAC,aAAa,CAAC,eAAe,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAE/F,oBAAoB,CAClB,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,cAAc,CAAC,mBAAmB,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,GAC3E,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB,qBAAqB,CACnB,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,aAAa,CAAC,mBAAmB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,GAAG,YAAY,GACnF,IAAI,CAAC;IAER,sBAAsB,CACpB,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,cAAc,CAAC,mBAAmB,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,GAC7E,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB,yBAAyB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjF,0BAA0B,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAEjD,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CAC1E"}