/* Create Token Page Styles - Modal Design */
.create-token-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  color: #ffffff;
}

/* Modal Background Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal Container */
.modal-container {
  background: linear-gradient(145deg, #1a1a1a, #0f0f0f);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
  animation: slideIn 0.4s ease-out;
  position: relative;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  background: linear-gradient(145deg, #1a1a1a, #0f0f0f);
  backdrop-filter: blur(10px);
  z-index: 10;
}

.modal-header h1 {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
}

.close-button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #aaa;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-decoration: none;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: scale(1.1);
}

/* Modal Content */
.modal-content {
  padding: 30px;
}

/* Form Styles */
.token-form {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: #ccc;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: #FF6B00;
  outline: none;
  background: rgba(255, 107, 0, 0.1);
  box-shadow: 0 0 20px rgba(255, 107, 0, 0.2);
  transform: translateY(-2px);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #666;
}

.form-group select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12' fill='none'%3E%3Cpath d='M2 4L6 8L10 4' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 15px center;
  padding-right: 40px;
}

.form-help-text {
  margin-top: 5px;
  font-size: 0.8rem;
  color: #777;
}



/* Image Upload Styles */
.image-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.image-preview {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.image-preview:hover {
  border-color: #FF6B00;
  transform: scale(1.05);
  box-shadow: 0 10px 30px rgba(255, 107, 0, 0.3);
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
  font-size: 0.9rem;
  text-align: center;
  padding: 10px;
}

.image-preview:hover .upload-overlay {
  opacity: 1;
}

.token-image-preview {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  border: 2px solid transparent;
  background: linear-gradient(#1e1e1e, #1e1e1e) padding-box,
              linear-gradient(90deg, #FF6B00, #FF2D78) border-box;
}

.token-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.token-image-preview:hover .image-overlay {
  opacity: 1;
}

.upload-button {
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
}

.upload-button:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

/* Create Button */
.create-button {
  background: linear-gradient(135deg, #FF6B00, #FF2D78);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  width: 100%;
  margin-top: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(255, 107, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.create-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.create-button:hover::before {
  left: 100%;
}

.create-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(255, 107, 0, 0.4);
}

.create-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Token Preview Section */
.token-preview-section {
  margin-top: 30px;
  padding-top: 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 20px 0;
  text-align: center;
  background: linear-gradient(135deg, #FF6B00, #FF2D78);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.preview-card-container {
  display: flex;
  justify-content: center;
  max-width: 320px;
  margin: 0 auto;
}

/* Responsive styles */
@media (max-width: 768px) {
  .modal-container {
    margin: 10px;
    max-height: 95vh;
  }

  .modal-header {
    padding: 20px 25px;
  }

  .modal-header h1 {
    font-size: 1.5rem;
  }

  .modal-content {
    padding: 20px;
  }

  .token-form {
    padding: 20px;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .image-preview {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-container {
    border-radius: 15px;
  }

  .modal-header {
    padding: 15px 20px;
  }

  .modal-content {
    padding: 15px;
  }
}

/* Gradient text utility class */
.gradient-text {
  background: linear-gradient(135deg, #FF6B00, #FF2D78);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Custom scrollbar for modal */
.modal-container::-webkit-scrollbar {
  width: 8px;
}

.modal-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.modal-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #FF6B00, #FF2D78);
  border-radius: 4px;
}

.modal-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #FF8533, #FF4D8F);
}

/* Loading state for create button */
.create-button.loading {
  position: relative;
  color: transparent;
}

.create-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
