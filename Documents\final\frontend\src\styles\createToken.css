/* Create Token Page Styles */
.create-token-page {
  padding-top: 70px;
  min-height: 100vh;
  background-color: #000000;
  color: #ffffff;
}

.create-token-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  border-bottom: 1px solid #333;
  padding-bottom: 15px;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
}

.back-button {
  background-color: transparent;
  border: none;
  color: #aaa;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.create-token-content {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 40px;
}

/* Form Styles */
.token-form {
  background-color: #111;
  border-radius: 10px;
  padding: 25px;
  border: 1px solid #333;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: #ccc;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 15px;
  background-color: #1e1e1e;
  border: 1px solid #333;
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: #FF6B00;
  outline: none;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #666;
}

.form-group select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12' fill='none'%3E%3Cpath d='M2 4L6 8L10 4' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 15px center;
  padding-right: 40px;
}

.form-help-text {
  margin-top: 5px;
  font-size: 0.8rem;
  color: #777;
}



/* Image Upload Styles */
.image-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.token-image-preview {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  border: 2px solid transparent;
  background: linear-gradient(#1e1e1e, #1e1e1e) padding-box,
              linear-gradient(90deg, #FF6B00, #FF2D78) border-box;
}

.token-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.token-image-preview:hover .image-overlay {
  opacity: 1;
}

.upload-button {
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
}

.upload-button:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

/* Create Button */
.create-button {
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
  color: white;
  border: none;
  padding: 15px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  width: 100%;
  margin-top: 20px;
  transition: all 0.3s;
}

.create-button:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

/* Token Preview Styles */
.token-preview {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.token-preview h3 {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #333;
}

.preview-card {
  background-color: #1e1e1e;
  border-radius: 15px;
  overflow: hidden;
  border: 1px solid #333;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.preview-card-header {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.preview-image-container {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid transparent;
  background: linear-gradient(#1e1e1e, #1e1e1e) padding-box,
              linear-gradient(90deg, #FF6B00, #FF2D78) border-box;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-card-content {
  padding: 20px;
  text-align: center;
}

.preview-token-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 5px;
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.preview-token-symbol {
  font-size: 0.9rem;
  color: #777;
  background-color: #333;
  padding: 3px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 15px;
}

.preview-token-price {
  font-size: 1.8rem;
  font-weight: 700;
  color: #00C853;
  margin-bottom: 15px;
  font-family: 'Lalezar', cursive;
}

.preview-token-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-detail {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
}

.detail-label {
  color: #aaa;
}

.detail-value {
  color: white;
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 900px) {
  .create-token-content {
    grid-template-columns: 1fr;
  }

  .token-preview {
    order: -1;
    margin-bottom: 30px;
  }
}

/* Gradient text utility class */
.gradient-text {
  background: linear-gradient(90deg, #FF6B00, #FF2D78);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
