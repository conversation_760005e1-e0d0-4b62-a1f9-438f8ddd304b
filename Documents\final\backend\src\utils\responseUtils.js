/**
 * Response Utilities
 * Helper functions for API responses
 */

/**
 * Generate a standardized error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {Error} error - Error object
 * @param {number} statusCode - HTTP status code
 * @returns {Object} - JSON response
 */
function getErrorResponse(res, message, error, statusCode = 500) {
  console.error(`${message}:`, error);
  
  // Determine if this is a client error or server error
  const isClientError = statusCode >= 400 && statusCode < 500;
  
  // For client errors, we can be more specific
  if (isClientError) {
    return res.status(statusCode).json({
      success: false,
      message: message,
      error: error.message
    });
  }
  
  // For server errors, don't expose internal details in production
  return res.status(statusCode).json({
    success: false,
    message: message,
    error: process.env.NODE_ENV === 'production' 
      ? 'An internal server error occurred' 
      : error.message
  });
}

/**
 * Generate a standardized success response
 * @param {Object} res - Express response object
 * @param {Object} data - Response data
 * @param {string} message - Success message
 * @param {number} statusCode - HTTP status code
 * @returns {Object} - JSON response
 */
function getSuccessResponse(res, data, message = 'Success', statusCode = 200) {
  return res.status(statusCode).json({
    success: true,
    message: message,
    ...data
  });
}

module.exports = {
  getErrorResponse,
  getSuccessResponse
};
