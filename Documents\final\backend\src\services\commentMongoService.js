// MongoDB-only comment service (Redis completely removed)
const Comment = require('../models/Comment');
const mongoose = require('mongoose');

console.log('Using MongoDB Atlas directly for comments (Redis removed)');

// Comment service using MongoDB Atlas directly (no caching)
const commentMongoService = {
  /**
   * Get comments for a token directly from MongoDB
   * @param {string} tokenSymbol - The token symbol
   * @returns {Promise<Array>} - Array of comments
   */
  async getCachedComments(tokenSymbol) {
    try {
      const comments = await Comment.find({ tokenSymbol })
        .populate('userId', 'username email')
        .sort({ createdAt: -1 })
        .lean();
      
      console.log(`Retrieved ${comments.length} comments for ${tokenSymbol} from MongoDB`);
      return comments;
    } catch (error) {
      console.error('Error retrieving comments from MongoDB:', error);
      throw new Error('Database operation failed: ' + error.message);
    }
  },

  /**
   * Cache comments (now just returns success since we're using MongoDB directly)
   * @param {string} tokenSymbol - The token symbol
   * @param {Array} comments - Array of comment objects
   * @returns {Promise<boolean>} - Success status
   */
  async cacheComments(tokenSymbol, comments) {
    // No caching needed - comments are stored directly in MongoDB
    console.log(`Comments for ${tokenSymbol} are stored directly in MongoDB (no caching)`);
    return true;
  },

  /**
   * Invalidate comments cache (no-op since we're using MongoDB directly)
   * @param {string} tokenSymbol - The token symbol
   * @returns {Promise<boolean>} - Success status
   */
  async invalidateCommentsCache(tokenSymbol) {
    // No cache to invalidate - using MongoDB directly
    console.log(`No cache to invalidate for ${tokenSymbol} (using MongoDB directly)`);
    return true;
  },

  /**
   * Get a comment directly from MongoDB
   * @param {string} commentId - Comment ID
   * @returns {Promise<Object|null>} - Comment object or null if not found
   */
  async getCachedComment(commentId) {
    try {
      const comment = await Comment.findById(commentId)
        .populate('userId', 'username email')
        .lean();
      
      if (!comment) {
        console.log(`Comment ${commentId} not found in MongoDB`);
        return null;
      }

      console.log(`Retrieved comment ${commentId} from MongoDB`);
      return comment;
    } catch (error) {
      console.error('Error retrieving comment from MongoDB:', error);
      throw new Error('Database operation failed: ' + error.message);
    }
  },

  /**
   * Cache a comment (no-op since we're using MongoDB directly)
   * @param {Object} comment - Comment object
   * @param {boolean} skipInvalidation - Whether to skip cache invalidation
   * @returns {Promise<boolean>} - Success status
   */
  async cacheComment(comment, skipInvalidation = false) {
    // No caching needed - comments are stored directly in MongoDB
    console.log(`Comment ${comment._id} is stored directly in MongoDB (no caching)`);
    return true;
  },

  /**
   * Get like status directly from MongoDB
   * @param {string} commentId - Comment ID
   * @param {string} userId - User ID
   * @returns {Promise<boolean|null>} - Like status or null if not found
   */
  async getCachedLikeStatus(commentId, userId) {
    try {
      const comment = await Comment.findById(commentId).lean();
      
      if (!comment) {
        console.log(`Comment ${commentId} not found for like status check`);
        return null;
      }

      const liked = comment.likes && comment.likes.includes(userId);
      console.log(`Retrieved like status for comment ${commentId} by user ${userId}: ${liked}`);
      return liked;
    } catch (error) {
      console.error('Error retrieving like status from MongoDB:', error);
      throw new Error('Database operation failed: ' + error.message);
    }
  },

  /**
   * Cache like status (no-op since we're using MongoDB directly)
   * @param {string} commentId - Comment ID
   * @param {string} userId - User ID
   * @param {boolean} liked - Like status
   * @returns {Promise<boolean>} - Success status
   */
  async cacheLikeStatus(commentId, userId, liked) {
    // No caching needed - like status is stored directly in MongoDB
    console.log(`Like status for comment ${commentId} by user ${userId} is stored directly in MongoDB (no caching)`);
    return true;
  }
};

module.exports = commentMongoService;
