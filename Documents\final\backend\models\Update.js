const mongoose = require('mongoose');

const UpdateSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['token_created', 'trending_token', 'user_registered'],
    required: true
  },
  tokenId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Token',
    required: function() {
      return this.type === 'token_created' || this.type === 'trending_token';
    }
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: function() {
      return this.type === 'user_registered' || this.type === 'token_created';
    }
  },
  message: {
    type: String,
    required: true
  },
  details: {
    type: Object,
    default: {}
  }
}, {
  timestamps: true
});

// Index for faster queries
UpdateSchema.index({ type: 1 });
UpdateSchema.index({ createdAt: -1 });

module.exports = mongoose.model('Update', UpdateSchema);
