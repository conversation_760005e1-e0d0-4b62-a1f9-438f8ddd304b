/**
 * Graduation Controller
 * 
 * Handles API endpoints for token graduation
 */

const graduationService = require('../services/graduationService');
const logger = require('../utils/logger');

/**
 * Check if a token is eligible for graduation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.checkEligibility = async (req, res) => {
  try {
    const { tokenSymbol } = req.params;
    
    if (!tokenSymbol) {
      return res.status(400).json({
        success: false,
        message: 'Token symbol is required'
      });
    }
    
    const eligibility = await graduationService.checkEligibility(tokenSymbol);
    
    res.status(200).json({
      success: true,
      data: eligibility
    });
  } catch (error) {
    logger.error(`Error checking graduation eligibility: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to check graduation eligibility',
      error: error.message
    });
  }
};

/**
 * Graduate a token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.graduateToken = async (req, res) => {
  try {
    const { tokenSymbol } = req.params;
    const userId = req.user.id;
    
    if (!tokenSymbol) {
      return res.status(400).json({
        success: false,
        message: 'Token symbol is required'
      });
    }
    
    const result = await graduationService.graduateToken(tokenSymbol, userId);
    
    res.status(200).json({
      success: true,
      message: `Token ${tokenSymbol} graduated successfully`,
      data: result
    });
  } catch (error) {
    logger.error(`Error graduating token: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to graduate token',
      error: error.message
    });
  }
};
