import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const juneoBCH1Chain = /*#__PURE__*/ define<PERSON>hain({
    id: 45_013,
    name: 'Juneo BCH1-Chain',
    nativeCurrency: {
        decimals: 18,
        name: 'Juneo BCH1-Chain',
        symbol: 'BCH1',
    },
    rpcUrls: {
        default: { http: ['https://rpc.juneo-mainnet.network/ext/bc/BCH1/rpc'] },
    },
    blockExplorers: {
        default: {
            name: '<PERSON><PERSON> Scan',
            url: 'https://juneoscan.io/chain/12',
            apiUrl: 'https://juneoscan.io/chain/12/api',
        },
    },
});
//# sourceMappingURL=juneoBCH1Chain.js.map