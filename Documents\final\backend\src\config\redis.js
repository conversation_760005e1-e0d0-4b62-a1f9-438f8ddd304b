/**
 * Redis Configuration - DISABLED
 *
 * This file previously provided Redis connection functionality for the application.
 * Redis has been removed and replaced with in-memory storage and MongoDB.
 */

// const { Redis } = require('@upstash/redis'); // REMOVED - Redis no longer used
const logger = require('../utils/logger');

// Redis client instances - REMOVED
// let upstashClient = null;
// let standardClient = null;

/**
 * Initialize Redis connections - DISABLED (Redis removed)
 */
async function initializeRedis() {
  try {
    // Redis removed - using in-memory storage and MongoDB instead
    logger.info('Redis disabled - using in-memory storage and MongoDB for caching');
    return true;

    // // Initialize Upstash Redis REST client
    // const upstashUrl = process.env.UPSTASH_REDIS_REST_URL;
    // const upstashToken = process.env.UPSTASH_REDIS_REST_TOKEN;

    // if (upstashUrl && upstashToken) {
    //   upstashClient = new Redis({
    //     url: upstashUrl,
    //     token: upstashToken,
    //   });

    //   // Test the connection
    //   const pingResult = await upstashClient.ping();
    //   logger.info(`Upstash Redis connection established: ${pingResult}`);
    // } else {
    //   logger.warn('Upstash Redis credentials not provided. Upstash Redis client not initialized.');
    // }

    // // Initialize standard Redis client if needed
    // // This is commented out as we're primarily using Upstash
    // // If you need a standard Redis client, uncomment this section
    // /*
    // const redisUrl = process.env.REDIS_URL;
    // if (redisUrl) {
    //   const { createClient } = require('redis');
    //   standardClient = createClient({
    //     url: redisUrl
    //   });
    //
    //   await standardClient.connect();
    //   logger.info('Standard Redis connection established');
    // } else {
    //   logger.info('Standard Redis URL not provided. Standard Redis client not initialized.');
    // }
    // */

    // // Return success if at least one client is initialized
    // if (upstashClient /* || standardClient */) {
    //   return true;
    // } else {
    //   throw new Error('No Redis clients could be initialized');
    // }
  } catch (error) {
    logger.error(`Redis initialization error (Redis disabled): ${error.message}`);

    // Redis is disabled, always return true
    return true;

    // // In production, Redis is required
    // if (process.env.NODE_ENV === 'production') {
    //   throw error;
    // } else {
    //   // In development, we can continue without Redis
    //   logger.warn('Continuing without Redis in development mode');
    //   return false;
    // }
  }
}

/**
 * Get the Upstash Redis client - DISABLED (returns null)
 */
function getUpstashClient() {
  return null; // Redis disabled
}

/**
 * Get the standard Redis client - DISABLED (returns null)
 */
function getStandardClient() {
  return null; // Redis disabled
}

/**
 * Ping Redis to check connection - DISABLED (returns mock response)
 */
async function ping() {
  // Redis disabled - return mock successful ping
  return 'PONG';

  // try {
  //   if (upstashClient) {
  //     return await upstashClient.ping();
  //   }
  //   return false;
  // } catch (error) {
  //   logger.error(`Redis ping error: ${error.message}`);
  //   return false;
  // }
}

module.exports = {
  initializeRedis,
  getUpstashClient,
  getStandardClient,
  ping,
  upstashClient: null, // Redis disabled
  standardClient: null // Redis disabled
};
