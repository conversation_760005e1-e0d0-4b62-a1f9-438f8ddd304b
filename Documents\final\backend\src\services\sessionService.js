const Session = require('../models/Session');
const LoginHistory = require('../models/LoginHistory');
// const { Redis } = require('@upstash/redis'); // REMOVED - Redis no longer used
const mongoose = require('mongoose');
const UAParser = require('ua-parser-js');
const geoip = require('geoip-lite');

// Initialize Redis client - REMOVED, using MongoDB only
// let redisClient;

// In-memory session store as fallback (now primary storage)
const inMemorySessionStore = new Map();

// Redis removed - using in-memory storage and MongoDB for sessions
// try {
//   const REDIS_URL = process.env.UPSTASH_REDIS_REST_URL;
//   const REDIS_TOKEN = process.env.UPSTASH_REDIS_REST_TOKEN;

//   if (!REDIS_URL || !REDIS_TOKEN) {
//     throw new Error('Redis credentials not configured');
//   }

//   // Use Upstash Redis client
//   redisClient = new Redis({
//     url: REDIS_URL,
//     token: REDIS_TOKEN
//   });
//   console.log('Upstash Redis client initialized for session management');

// Redis removed - using in-memory storage and MongoDB for sessions
console.log('Using in-memory session storage and MongoDB for persistence');

// Create in-memory session management (replaces Redis)
const sessionClient = {
  get: async (key) => inMemorySessionStore.get(key) || null,
  set: async (key, value, options) => {
    inMemorySessionStore.set(key, value);
    // Handle expiration if specified
    if (options && options.ex) {
      setTimeout(() => inMemorySessionStore.delete(key), options.ex * 1000);
    }
    return true;
  },
  del: async (key) => {
    inMemorySessionStore.delete(key);
    return true;
  },
  ttl: async (key) => 3600, // Default 1 hour TTL
  pipeline: () => ({
    del: () => {},
    exec: async () => []
  }),
  ping: async () => 'PONG'
};

//   // Test Redis connection
//   redisClient.set('session:test', 'test-connection')
//     .then(() => console.log('Redis connection test successful'))
//     .catch(err => {
//       console.error('Redis auth connection test failed:', err);
//       console.warn('Falling back to in-memory session storage (not recommended for production)');

//       // Create in-memory fallback for Redis
//       redisClient = {
//         get: async (key) => inMemorySessionStore.get(key) || null,
//         set: async (key, value, options) => {
//           inMemorySessionStore.set(key, value);
//           // Handle expiration if specified
//           if (options && options.ex) {
//             setTimeout(() => inMemorySessionStore.delete(key), options.ex * 1000);
//           }
//           return true;
//         },
//         del: async (key) => {
//           inMemorySessionStore.delete(key);
//           return true;
//         },
//         ttl: async (key) => 3600, // Default 1 hour TTL
//         pipeline: () => ({
//           del: () => {},
//           exec: async () => []
//         }),
//         ping: async () => 'PONG'
//       };
//     });
// } catch (error) {
//   console.error('Failed to initialize Redis client:', error);
//   console.warn('Falling back to in-memory session storage');

//   // Create in-memory fallback for Redis
//   redisClient = {
//     get: async (key) => inMemorySessionStore.get(key) || null,
//     set: async (key, value, options) => {
//       inMemorySessionStore.set(key, value);
//       // Handle expiration if specified
//       if (options && options.ex) {
//         setTimeout(() => inMemorySessionStore.delete(key), options.ex * 1000);
//       }
//       return true;
//     },
//     del: async (key) => {
//       inMemorySessionStore.delete(key);
//       return true;
//     },
//     ttl: async (key) => 3600, // Default 1 hour TTL
//     pipeline: () => ({
//       del: () => {},
//       exec: async () => []
//     }),
//     ping: async () => 'PONG'
//   };
//   console.log('Redis connection test failed: Using in-memory session storage');
// }

class SessionService {
  /**
   * Create a new session when a user logs in
   * @param {Object} userData - User data
   * @param {Object} req - Express request object
   * @param {string} sessionId - Session ID
   * @param {number} expiryInSeconds - Session expiry in seconds
   * @returns {Promise<Object>} Created session
   */
  async createSession(userData, req, sessionId, expiryInSeconds = 86400) {
    try {
      const userAgent = req.headers['user-agent'];
      const ipAddress = req.ip || req.headers['x-forwarded-for'] || 'Unknown';

      // Parse user agent
      const parser = new UAParser(userAgent);
      const browser = parser.getBrowser();
      const os = parser.getOS();
      const device = parser.getDevice();

      // Get location from IP
      const geo = geoip.lookup(ipAddress);
      const location = geo ? `${geo.city || ''}, ${geo.country || 'Unknown'}`.trim() : 'Unknown';

      // Calculate expiry date
      const expiresAt = new Date(Date.now() + (expiryInSeconds * 1000));

      // Create session in MongoDB
      const session = new Session({
        userId: userData.id,
        sessionId,
        device: device.vendor ? `${device.vendor} ${device.model}` : (device.type || 'Desktop'),
        browser: `${browser.name} - ${browser.version}`,
        os: `${os.name} ${os.version}`,
        location,
        ipAddress,
        lastActive: new Date(),
        expiresAt
      });

      await session.save();

      // Store in session client (in-memory) for faster access
      if (sessionClient) {
        const sessionData = {
          id: session._id.toString(),
          userId: userData.id,
          device: session.device,
          browser: session.browser,
          os: session.os,
          location: session.location,
          ipAddress: session.ipAddress,
          lastActive: session.lastActive,
          expiresAt: session.expiresAt
        };

        await sessionClient.set(`session:${sessionId}`, JSON.stringify(sessionData), { ex: expiryInSeconds });
      }

      // Record login history
      await this.recordLogin(userData.id, req, 'Success');

      return session;
    } catch (error) {
      console.error('Error creating session:', error);
      throw error;
    }
  }

  /**
   * Get active sessions for a user
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Active sessions
   */
  async getActiveSessions(userId) {
    try {
      return await Session.find({
        userId,
        isActive: true,
        expiresAt: { $gt: new Date() }
      }).sort({ lastActive: -1 });
    } catch (error) {
      console.error('Error getting active sessions:', error);
      throw error;
    }
  }

  /**
   * Update session last active time
   * @param {string} sessionId - Session ID
   * @returns {Promise<boolean>} Success status
   */
  async updateSessionActivity(sessionId) {
    try {
      const now = new Date();

      // Update in MongoDB
      const session = await Session.findOneAndUpdate(
        { sessionId },
        { lastActive: now },
        { new: true }
      );

      if (!session) return false;

      // Update in session client (in-memory)
      if (sessionClient) {
        const sessionData = await sessionClient.get(`session:${sessionId}`);
        if (sessionData) {
          const data = JSON.parse(sessionData);
          data.lastActive = now;

          // Get TTL of existing key
          const ttl = await sessionClient.ttl(`session:${sessionId}`);
          if (ttl > 0) {
            await sessionClient.set(`session:${sessionId}`, JSON.stringify(data), { ex: ttl });
          }
        }
      }

      return true;
    } catch (error) {
      console.error('Error updating session activity:', error);
      return false;
    }
  }

  /**
   * Terminate a session (sign out)
   * @param {string} sessionId - Session ID
   * @returns {Promise<boolean>} Success status
   */
  async terminateSession(sessionId) {
    try {
      // Update in MongoDB
      const session = await Session.findOneAndUpdate(
        { sessionId },
        { isActive: false },
        { new: true }
      );

      if (!session) return false;

      // Remove from session client (in-memory)
      if (sessionClient) {
        await sessionClient.del(`session:${sessionId}`);
      }

      return true;
    } catch (error) {
      console.error('Error terminating session:', error);
      return false;
    }
  }

  /**
   * Terminate all sessions for a user except the current one
   * @param {string} userId - User ID
   * @param {string} currentSessionId - Current session ID to keep active
   * @returns {Promise<number>} Number of terminated sessions
   */
  async terminateAllOtherSessions(userId, currentSessionId) {
    try {
      // Update in MongoDB
      const result = await Session.updateMany(
        {
          userId,
          sessionId: { $ne: currentSessionId },
          isActive: true
        },
        { isActive: false }
      );

      // Remove from session client (in-memory)
      if (sessionClient) {
        const sessions = await Session.find({
          userId,
          sessionId: { $ne: currentSessionId },
          isActive: false
        });

        const pipeline = sessionClient.pipeline();
        sessions.forEach(session => {
          pipeline.del(`session:${session.sessionId}`);
        });

        await pipeline.exec();
      }

      return result.modifiedCount;
    } catch (error) {
      console.error('Error terminating all other sessions:', error);
      throw error;
    }
  }

  /**
   * Record login attempt in history
   * @param {string} userId - User ID
   * @param {Object} req - Express request object
   * @param {string} status - Login status (Success/Failed)
   * @param {string} failureReason - Reason for failure (if applicable)
   * @returns {Promise<Object>} Created login history entry
   */
  async recordLogin(userId, req, status, failureReason = null) {
    try {
      const userAgent = req.headers['user-agent'];
      const ipAddress = req.ip || req.headers['x-forwarded-for'] || 'Unknown';

      // Parse user agent
      const parser = new UAParser(userAgent);
      const browser = parser.getBrowser();
      const os = parser.getOS();
      const device = parser.getDevice();

      // Get location from IP
      const geo = geoip.lookup(ipAddress);
      const location = geo ? `${geo.city || ''}, ${geo.country || 'Unknown'}`.trim() : 'Unknown';

      // Create login history entry
      const loginHistory = new LoginHistory({
        userId,
        device: device.vendor ? `${device.vendor} ${device.model}` : (device.type || 'Desktop'),
        browser: `${browser.name} - ${browser.version}`,
        os: `${os.name} ${os.version}`,
        location,
        ipAddress,
        status,
        failureReason
      });

      return await loginHistory.save();
    } catch (error) {
      console.error('Error recording login history:', error);
      // Don't throw error to prevent blocking the login process
      return null;
    }
  }

  /**
   * Get login history for a user
   * @param {string} userId - User ID
   * @param {number} limit - Number of entries to return
   * @param {number} skip - Number of entries to skip
   * @returns {Promise<Array>} Login history entries
   */
  async getLoginHistory(userId, limit = 10, skip = 0) {
    try {
      return await LoginHistory.find({ userId })
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      console.error('Error getting login history:', error);
      throw error;
    }
  }

  /**
   * Get login history count for a user
   * @param {string} userId - User ID
   * @returns {Promise<number>} Count of login history entries
   */
  async getLoginHistoryCount(userId) {
    try {
      return await LoginHistory.countDocuments({ userId });
    } catch (error) {
      console.error('Error getting login history count:', error);
      throw error;
    }
  }
}

module.exports = new SessionService();
