import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
const sourceId = 1; // ethereum
export const plume = /*#__PURE__*/ define<PERSON>hain({
    id: 98_865,
    name: '<PERSON><PERSON> (Legacy)',
    nativeCurrency: {
        name: 'Plume Ether',
        symbol: 'ETH',
        decimals: 18,
    },
    rpcUrls: {
        default: {
            http: ['https://rpc.plumenetwork.xyz'],
            webSocket: ['wss://rpc.plumenetwork.xyz'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Blockscout',
            url: 'https://explorer.plumenetwork.xyz',
            apiUrl: 'https://explorer.plumenetwork.xyz/api',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 48_577,
        },
    },
    sourceId,
});
//# sourceMappingURL=plume.js.map