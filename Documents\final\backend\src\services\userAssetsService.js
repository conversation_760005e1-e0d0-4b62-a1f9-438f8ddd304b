/**
 * User Assets Service
 * Handles fetching and managing user assets data
 */

const mongoose = require('mongoose');
const User = require('../models/User');
const Transaction = require('../models/Transaction');
const { getTokenPrice } = require('./priceService');

class UserAssetsService {
  /**
   * Get user assets with current prices and 24h change
   * @param {string} userId - User ID
   * @returns {Promise<Array>} - Array of user assets with balances and values
   */
  async getUserAssets(userId) {
    try {
      // Get user transactions to calculate balances
      const transactions = await Transaction.find({ userId });
      
      // Calculate balances for each token
      const balances = {};
      
      for (const tx of transactions) {
        const { tokenSymbol, amount, type } = tx;
        
        if (!balances[tokenSymbol]) {
          balances[tokenSymbol] = 0;
        }
        
        // Add or subtract based on transaction type
        if (type === 'buy' || type === 'deposit') {
          balances[tokenSymbol] += amount;
        } else if (type === 'sell' || type === 'withdraw') {
          balances[tokenSymbol] -= amount;
        }
      }
      
      // Get current prices and 24h changes for each token
      const assets = [];
      
      for (const [symbol, balance] of Object.entries(balances)) {
        if (balance <= 0) continue; // Skip tokens with zero or negative balance
        
        // Get current price and 24h change
        const { price, change24h } = await getTokenPrice(symbol);
        
        assets.push({
          symbol,
          balance,
          value: balance * price,
          price,
          change24h
        });
      }
      
      return assets;
    } catch (error) {
      console.error('Error fetching user assets:', error);
      throw error;
    }
  }
}

module.exports = new UserAssetsService();
