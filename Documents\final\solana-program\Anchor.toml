[toolchain]
anchor_version = "0.31.1"

[features]
seeds = false
skip-lint = false

[programs.localnet]
meme_coin_platform = "5bupaxUMC3Rz2RK1zgrmBGxaqAz9hsrcAZLnZUSnHYvw"

[programs.devnet]
meme_coin_platform = "5bupaxUMC3Rz2RK1zgrmBGxaqAz9hsrcAZLnZUSnHYvw"

[programs.mainnet]
meme_coin_platform = "5bupaxUMC3Rz2RK1zgrmBGxaqAz9hsrcAZLnZUSnHYvw"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "devnet"
wallet = "~/.config/solana/id.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"
