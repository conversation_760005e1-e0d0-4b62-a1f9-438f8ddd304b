{"version": 3, "file": "index.umd.js", "sources": ["../../safe-json/dist/index.es.js", "../src/node-js/lib/db.ts", "../src/node-js/lib/nodeMigration.ts", "../src/node-js/index.ts", "../src/shared/types.ts", "../src/shared/utils.ts"], "sourcesContent": ["const s=n=>JSON.stringify(n,(r,t)=>typeof t==\"bigint\"?t.toString()+\"n\":t),o=n=>{const r=/([\\[:])?(\\d{17,}|(?:[9](?:[1-9]07199254740991|0[1-9]7199254740991|00[8-9]199254740991|007[2-9]99254740991|007199[3-9]54740991|0071992[6-9]4740991|00719925[5-9]740991|007199254[8-9]40991|0071992547[5-9]0991|00719925474[1-9]991|00719925474099[2-9])))([,\\}\\]])/g,t=n.replace(r,'$1\"$2n\"$3');return JSON.parse(t,(g,e)=>typeof e==\"string\"&&e.match(/^\\d+n$/)?BigInt(e.substring(0,e.length-1)):e)};function i(n){if(typeof n!=\"string\")throw new Error(`Cannot safe json parse value of type ${typeof n}`);try{return o(n)}catch{return n}}function f(n){return typeof n==\"string\"?n:s(n)||\"\"}export{i as safeJsonParse,f as safeJsonStringify};\n//# sourceMappingURL=index.es.js.map\n", "import { safeJsonStringify } from \"@walletconnect/safe-json\";\n\nfunction importLib() {\n  try {\n    const db = require(\"unstorage\");\n    const driver = require(\"unstorage/drivers/fs-lite\");\n    return {\n      db,\n      driver,\n    };\n  } catch (e) {\n    // User didn't install db dependency, show detailed error\n    throw new Error(\n      `To use WalletConnect server side, you'll need to install the \"unstorage\" dependency. If you are seeing this error during a build / in an SSR environment, you can add \"unstorage\" as a devDependency to make this error go away.`,\n    );\n  }\n}\n\ninterface DbKeyValueStorageOptions {\n  dbName: string;\n}\n\nlet StorageLib: any;\ntype ActionType = \"setItem\" | \"removeItem\";\ntype UpdateType = {\n  state: \"active\" | \"idle\";\n  actions: {\n    key: string;\n    value: unknown;\n    action: ActionType;\n    callback: (value: void | PromiseLike<void>) => void;\n  }[];\n};\nexport const MEMORY_DB = \":memory:\";\n\nexport default class Db {\n  private static instances: Record<string, Db> = {};\n  public database;\n  private writeActionsQueue: UpdateType = {\n    state: \"idle\",\n    actions: [],\n  };\n\n  private constructor(opts: DbKeyValueStorageOptions) {\n    if (!StorageLib) {\n      StorageLib = importLib();\n    }\n\n    if (opts?.dbName === MEMORY_DB) {\n      this.database = StorageLib.db.createStorage();\n    } else {\n      this.database = StorageLib.db.createStorage({\n        driver: StorageLib.driver({\n          base: opts?.dbName,\n        }),\n      });\n    }\n  }\n\n  public static create(opts: DbKeyValueStorageOptions): Db {\n    const dbName = opts.dbName;\n    if (dbName === MEMORY_DB) {\n      return new Db(opts);\n    }\n\n    if (!Db.instances[dbName]) {\n      Db.instances[dbName] = new Db(opts);\n    }\n    return Db.instances[dbName];\n  }\n\n  public async getKeys(): Promise<string[]> {\n    return this.database.getKeys();\n  }\n\n  public async getEntries<T = any>(): Promise<[string, T][]> {\n    const entries = await this.database.getItems(await this.database.getKeys());\n    return entries.map((item: any) => [item.key, item.value] as [string, T]);\n  }\n\n  private async onWriteAction(params: {\n    key: string;\n    value?: unknown;\n    action: ActionType;\n  }): Promise<void> {\n    const { key, value, action } = params;\n    let resolveLock: (value: void | PromiseLike<void>) => void = () => ({});\n    const lock = new Promise<void>((resolve) => (resolveLock = resolve));\n    this.writeActionsQueue.actions.push({ key, value, action, callback: resolveLock });\n    if (this.writeActionsQueue.state === \"idle\") {\n      this.startWriteActions();\n    }\n    await lock;\n  }\n\n  private async startWriteActions(): Promise<void> {\n    if (this.writeActionsQueue.actions.length === 0) {\n      this.writeActionsQueue.state = \"idle\";\n      return;\n    }\n    this.writeActionsQueue.state = \"active\";\n    while (this.writeActionsQueue.actions.length > 0) {\n      const writeAction = this.writeActionsQueue.actions.shift();\n      if (!writeAction) continue;\n      const { key, value, action, callback } = writeAction;\n      switch (action) {\n        case \"setItem\":\n          await this.database.setItem(key);\n          await this.database.setItem(key, safeJsonStringify(value));\n          break;\n        case \"removeItem\":\n          await this.database.removeItem(key);\n          break;\n      }\n      callback();\n    }\n    this.writeActionsQueue.state = \"idle\";\n  }\n\n  public async getItem<T = any>(key: string): Promise<T | undefined> {\n    const item = await this.database.getItem(key);\n    if (item === null) {\n      return undefined;\n    }\n    return item as T;\n  }\n\n  public async setItem<_T = any>(key: string, value: any): Promise<void> {\n    await this.onWriteAction({ key, value, action: \"setItem\" });\n  }\n\n  public async removeItem(key: string): Promise<void> {\n    await this.onWriteAction({ key, action: \"removeItem\" });\n  }\n}\n", "import { safeJsonParse } from \"@walletconnect/safe-json\";\nimport { IKeyValueStorage } from \"../../shared\";\nimport { MEMORY_DB } from \"./db\";\nimport fs from \"fs\";\nconst VERSION_KEY = \"wc_storage_version\";\nconst TO_MIGRATE_SUFFIX = \".to_migrate\";\nconst MIGRATED_SUFFIX = \".migrated\";\nconst DB_VERSION = 1;\n\nexport const migrate = async (\n  fromStore: string,\n  toStore: IKeyValueStorage,\n  onCompleteCallback: () => void,\n) => {\n  if (fromStore === MEMORY_DB) {\n    onCompleteCallback();\n    return;\n  }\n  const versionKey = VERSION_KEY;\n  const currentVersion = await toStore.getItem<number>(versionKey);\n  if (currentVersion && currentVersion >= DB_VERSION) {\n    onCompleteCallback();\n    return;\n  }\n  const rawContents = await readFile(`${fromStore}${TO_MIGRATE_SUFFIX}`);\n  if (!rawContents) {\n    onCompleteCallback();\n    return;\n  }\n  const contents = safeJsonParse(rawContents);\n  if (!contents) {\n    onCompleteCallback();\n    return;\n  }\n  const collection = contents?.collections?.[0];\n\n  const items = collection?.data;\n  if (!items || !items.length) {\n    onCompleteCallback();\n    return;\n  }\n\n  while (items.length) {\n    const item = items.shift();\n    if (!item) {\n      continue;\n    }\n    const { id, value } = item;\n    await toStore.setItem(id, safeJsonParse(value));\n  }\n\n  await toStore.setItem(versionKey, DB_VERSION);\n  renameFile(`${fromStore}${TO_MIGRATE_SUFFIX}`, `${fromStore}${MIGRATED_SUFFIX}`);\n  onCompleteCallback();\n};\n\nconst readFile = async (path: string) => {\n  return await new Promise<string | undefined>((resolve) => {\n    fs.readFile(path, { encoding: \"utf8\" }, (err, data) => {\n      if (err) {\n        resolve(undefined);\n      }\n      resolve(data);\n    });\n  });\n};\n\nexport const beforeMigrate = (fromStore: string) => {\n  if (fromStore === MEMORY_DB) return;\n  if (!fs.existsSync(fromStore)) return;\n  if (fs.lstatSync(fromStore).isDirectory()) return;\n  renameFile(fromStore, `${fromStore}${TO_MIGRATE_SUFFIX}`);\n};\n\nconst renameFile = (from: string, to: string) => {\n  try {\n    fs.renameSync(from, to);\n  } catch (e) {}\n};\n", "import { IKeyValueStorage, KeyValueStorageOptions } from \"../shared\";\n\nimport Db from \"./lib/db\";\nimport { beforeMigrate, migrate } from \"./lib/nodeMigration\";\nconst DB_NAME = \"walletconnect.db\";\nexport class KeyValueStorage implements IKeyValueStorage {\n  private database: typeof Db.prototype;\n  private initialized = false;\n\n  constructor(opts?: KeyValueStorageOptions) {\n    const dbName = opts?.database || opts?.table || DB_NAME;\n    beforeMigrate(dbName);\n    this.database = Db.create({\n      dbName,\n    });\n    migrate(dbName, this.database, this.setInitialized);\n  }\n\n  private setInitialized = () => {\n    this.initialized = true;\n  };\n\n  public async getKeys(): Promise<string[]> {\n    await this.initialize();\n    return this.database.getKeys();\n  }\n\n  public async getEntries<T = any>(): Promise<[string, T][]> {\n    await this.initialize();\n    return this.database.getEntries();\n  }\n\n  public async getItem<T = any>(key: string): Promise<T | undefined> {\n    await this.initialize();\n    return this.database.getItem(key);\n  }\n\n  public async setItem<_T = any>(key: string, value: any): Promise<void> {\n    await this.initialize();\n    await this.database.setItem(key, value);\n  }\n\n  public async removeItem(key: string): Promise<void> {\n    await this.initialize();\n    await this.database.removeItem(key);\n  }\n\n  private async initialize() {\n    if (this.initialized) {\n      return;\n    }\n    await new Promise<void>((resolve) => {\n      const interval = setInterval(() => {\n        if (this.initialized) {\n          clearInterval(interval);\n          resolve();\n        }\n      }, 20);\n    });\n  }\n}\n\nexport default KeyValueStorage;\n", "export interface KeyValueStorageOptions {\n  database?: string;\n  table?: string;\n}\n\nexport abstract class IKeyValueStorage {\n  public abstract getKeys(): Promise<string[]>;\n  public abstract getEntries<T = any>(): Promise<[string, T][]>;\n  public abstract getItem<T = any>(key: string): Promise<T | undefined>;\n  public abstract setItem<T = any>(key: string, value: T): Promise<void>;\n  public abstract removeItem(key: string): Promise<void>;\n}\n", "import { safeJsonParse } from \"@walletconnect/safe-json\";\n\nexport function parseEntry(entry: [string, string | null]): [string, any] {\n  return [entry[0], safeJsonParse(entry[1] ?? \"\")];\n}\n"], "names": ["i", "l", "y", "I", "a", "t", "r", "s"], "mappings": ";;;;;;;;;;EAAA,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,oQAAoQ,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAASA,GAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,qCAAqC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;;ECA/lB,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC,OAAM,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,gOAAgO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAQ,MAAM,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,OAAO,EAAE,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,UAAU,EAAE,CAAC,OAAM,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,GAAG,MAAM,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,MAAM,EAAC,CAAC,MAAM,iBAAiB,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,OAAM,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAC,CAAC,MAAM,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE;;ECAn1D,MAAM,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAQ,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAGC,SAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAACC,GAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAACA,GAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,IAAI,OAAO,CAAC,CAAC,EAAE,CAACC,qBAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAQ,MAAM,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGF,SAAC,EAAEE,qBAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAGA,qBAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAACA,qBAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;;ECAnxB,MAAM,CAAC,CAAC,kBAAkB,CAAQ,MAAM,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAACA,aAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAACC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,OAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAC,CAAC,MAAM,OAAO,EAAE,CAAC,OAAO,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,UAAU,EAAE,CAAC,OAAO,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC,MAAM,UAAU,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;;ECAt2B,MAAM,gBAAgB;;ECAmC,SAAS,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;;;;;;;;;;;;"}