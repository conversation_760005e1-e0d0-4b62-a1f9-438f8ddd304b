const express = require('express');
const router = express.Router();
const sessionController = require('../controllers/sessionController');
const { secureAuth } = require('../middleware/auth');

// All routes require authentication
router.use(secureAuth);

// Get active sessions for the authenticated user
router.get('/active', sessionController.getActiveSessions);

// Terminate a specific session
router.delete('/:sessionId', sessionController.terminateSession);

// Terminate all sessions except the current one
router.delete('/', sessionController.terminateAllOtherSessions);

// Get login history for the authenticated user
router.get('/history', sessionController.getLoginHistory);

// Update the last active time for the current session
router.put('/activity', sessionController.updateSessionActivity);

module.exports = router;
