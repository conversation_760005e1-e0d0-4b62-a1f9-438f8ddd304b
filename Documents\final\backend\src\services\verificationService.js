// Simple in-memory store for verification codes
const verificationCodes = new Map();

/**
 * Generate and store a verification code for an email
 * @param {string} email - The email to generate a code for
 * @param {string} username - The username associated with the registration
 * @param {string} password - The password to store temporarily
 * @returns {string} - The generated verification code
 */
const generateCode = (email, username, password) => {
  const code = Math.floor(100000 + Math.random() * 900000).toString();
  
  // Store the verification data with a timestamp
  verificationCodes.set(email, {
    code,
    username,
    password,
    timestamp: Date.now()
  });
  
  // Set a timeout to automatically remove the code after 5 minutes
  setTimeout(() => {
    if (verificationCodes.has(email)) {
      verificationCodes.delete(email);
    }
  }, 5 * 60 * 1000);
  
  return code;
};

/**
 * Verify a code for a given email
 * @param {string} email - The email to verify the code for
 * @param {string} code - The verification code to check
 * @returns {Object|null} - The stored data if valid, null otherwise
 */
const verifyCode = (email, code) => {
  if (!verificationCodes.has(email)) {
    return null;
  }
  
  const data = verificationCodes.get(email);
  
  // Check if the code is expired (5 minutes)
  if (Date.now() - data.timestamp > 5 * 60 * 1000) {
    verificationCodes.delete(email);
    return null;
  }
  
  // Check if the code matches
  if (data.code !== code) {
    return null;
  }
  
  // Return the data and remove from storage
  verificationCodes.delete(email);
  return data;
};

/**
 * Clear verification data for an email
 * @param {string} email - The email to clear data for
 */
const clearVerificationData = (email) => {
  if (verificationCodes.has(email)) {
    verificationCodes.delete(email);
  }
};

module.exports = {
  generateCode,
  verifyCode,
  clearVerificationData
}; 